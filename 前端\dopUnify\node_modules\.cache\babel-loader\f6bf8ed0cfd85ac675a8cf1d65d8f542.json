{"remainingRequest": "D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\node_modules\\babel-loader\\lib\\index.js!D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\src\\views\\system\\notice\\release\\index.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\src\\views\\system\\notice\\release\\index.vue", "mtime": 1686019808685}, {"path": "D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\babel.config.js", "mtime": 1686019818359}, {"path": "D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1655715099000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;AA0BA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;EACAA;EACAC;IAAAC;EAAA;EACAC;IACA;MACAC;MACAC;QACAC;QACAC;QACAC;MACA;MACAC;QACA;QACAC;QACAC;QACAC;QACAC;QACAC;QACAC;QACAC;QACAC;QACAC;QACAC;MACA;MACAC;IACA;EACA;EACAC;IACA;EACA;EACAC;IAAA;IACA;MACA;QACA;QACA;MACA;IACA;EACA;EACAC;IACA;AACA;IACAC;MACA;IACA;IACA;AACA;AACA;IACAC;MACA;QACA;MACA;QACA;MACA;IACA;IACA;AACA;IACAC;MACA;IACA;IACA;AACA;AACA;IACAC;MAAA;MACA;MACA;QACAC;QACAC;MACA;MACAC;QACA;QACA;MACA;IACA;IACA;AACA;AACA;AACA;AACA;IACAC;MACA;MACA;QACA;QAAA,2CACAC;UAAA;QAAA;UAAA;YAAA;YAAA,4CACA;cAAA;YAAA;cAAA;gBAAA;gBACA;kBACAC;gBACA;cACA;YAAA;cAAA;YAAA;cAAA;YAAA;UACA;QAAA;UAAA;QAAA;UAAA;QAAA;MACA;MACAC;IACA;EACA;AACA", "names": ["name", "components", "TableList", "data", "config", "defaultForm", "notice_keyword", "publish_time", "publish_user", "btnAll", "btnQuery", "btnPublish", "btnEdit", "btnRetract", "btnApprovalStatus", "btnReadStatus", "btnDelete", "btnCancle", "btnSaveDraft", "btnSurePublish", "rolelist", "created", "mounted", "methods", "btnPermissions", "validateForm", "queryList", "roleNo", "oper_type", "role_level", "roleNoTree", "roleNoFormat", "role_nos", "retValue", "callback"], "sourceRoot": "src/views/system/notice/release", "sources": ["index.vue"], "sourcesContent": ["<!--\r\n* 发布公告\r\n-->\r\n<template>\r\n  <div class=\"app-container\">\r\n    <div class=\"sun-content\">\r\n      <div class=\"filter-container\">\r\n        <sun-form\r\n          ref=\"formRef\"\r\n          :config=\"config\"\r\n          :default-form=\"defaultForm\"\r\n          :query=\"btnAll.btnQuery\"\r\n          @query=\"queryList\"\r\n          @validateForm=\"validateForm\"\r\n        />\r\n      </div>\r\n    </div>\r\n    <table-list\r\n      ref=\"tableListRef\"\r\n      :default-form=\"defaultForm\"\r\n      :btn-all=\"btnAll\"\r\n      @roleNoFormat=\"roleNoFormat\"\r\n    />\r\n  </div>\r\n</template>\r\n<script>\r\nimport { config } from './info' // 表单配置\r\nimport TableList from './component/table' // 表格\r\nimport { permissionsBtn } from '@/utils/permissions' // 权限配置\r\n// import { treeDataTranslate } from '@/utils/dictionary' // 字典配置\r\n\r\nimport { system } from '@/api'\r\nimport { commonBlank } from 'sunui/srcobf/utils/common'\r\nconst { roleNoTree } = system.SysUser\r\n\r\n// import { SysSystem } from '@/api'\r\n// const { systemController } = SysSystem\r\nexport default {\r\n  name: 'Release',\r\n  components: { TableList },\r\n  data() {\r\n    return {\r\n      config: config(this),\r\n      defaultForm: {\r\n        notice_keyword: '',\r\n        publish_time: [],\r\n        publish_user: ''\r\n      },\r\n      btnAll: {\r\n        // 当前页需要配置权限的按钮  权限获取\r\n        btnQuery: false,\r\n        btnPublish: true,\r\n        btnEdit: true,\r\n        btnRetract: true,\r\n        btnApprovalStatus: true,\r\n        btnReadStatus: true,\r\n        btnDelete: true,\r\n        btnCancle: true,\r\n        btnSaveDraft: true,\r\n        btnSurePublish: true\r\n      },\r\n      rolelist: [] // 角色列表\r\n    }\r\n  },\r\n  created() {\r\n    this.btnPermissions()\r\n  },\r\n  mounted() {\r\n    this.$nextTick().then(() => {\r\n      if (this.btnAll.btnQuery) {\r\n        this.queryList()\r\n        this.roleNo()\r\n      }\r\n    })\r\n  },\r\n  methods: {\r\n    /**\r\n     * 按钮权限配置*/\r\n    btnPermissions() {\r\n      this.btnAll = permissionsBtn(this.$attrs.button_id, this.btnAll)\r\n    },\r\n    /**\r\n     * 表单校验\r\n     * @param {Boolean}valid 校验返回值*/\r\n    validateForm(valid) {\r\n      if (valid) {\r\n        this.$refs.tableListRef.queryList(1)\r\n      } else {\r\n        return false\r\n      }\r\n    },\r\n    /**\r\n     * 按钮：查询*/\r\n    queryList() {\r\n      this.$refs['formRef'].validateForm()\r\n    },\r\n    /**\r\n     * 角色获取\r\n     */\r\n    roleNo() {\r\n      const role_level = this.$store.getters.organLevel + '00'\r\n      const msg = {\r\n        oper_type: 'rolelist',\r\n        role_level: role_level\r\n      }\r\n      roleNoTree(msg).then((response) => {\r\n        const { rolelist } = response.retMap\r\n        this.rolelist = rolelist\r\n      })\r\n    },\r\n    /**\r\n     * 格式化role_no\r\n     * @param {String}role_no 角色号\r\n     * @param {Function}callback 返回方法\r\n     */\r\n    roleNoFormat(role_no, callback) {\r\n      let retValue = ''\r\n      if (!commonBlank(role_no)) {\r\n        const role_nos = role_no.split(',')\r\n        for (const key of role_nos) {\r\n          for (const key1 of this.rolelist) {\r\n            if (key === key1.role_no) {\r\n              retValue += ',' + (key1.role_no + '-' + key1.role_name)\r\n            }\r\n          }\r\n        }\r\n      }\r\n      callback(retValue.substring(1))\r\n    }\r\n  }\r\n}\r\n</script>\r\n<style scoped>\r\n.app-container {\r\n  width: 100%;\r\n  height: 100%;\r\n}\r\n</style>\r\n"]}]}