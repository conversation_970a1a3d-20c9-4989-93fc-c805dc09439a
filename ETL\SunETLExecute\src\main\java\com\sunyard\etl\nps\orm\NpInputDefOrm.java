package com.sunyard.etl.nps.orm;

import java.sql.ResultSet;
import java.sql.SQLException;

import com.sunyard.etl.nps.model.NpInputDef;
import com.sunyard.etl.system.orm.Orm;

public class NpInputDefOrm implements Orm<NpInputDef>{

	public NpInputDef orm(ResultSet rs) {
		NpInputDef npInputDef = new NpInputDef();
		try {
			npInputDef.setNopaperId(rs.getString("NOPAPER_ID"));
			npInputDef.setSource(rs.getString("SOURCE"));
			npInputDef.setECMLogonParameter(rs.getString("ECM_LOGON_PARAMETER"));
			npInputDef.setECMQueryParameter(rs.getString("ECM_QUERY_PARAMETER"));
			npInputDef.setECMUploadParameter(rs.getString("ECM_UPLOAD_PARAMETER"));
			npInputDef.setFTPLogonParameter(rs.getString("FTP_LOGON_PARAMETER"));
			npInputDef.setFTPOtherParameter(rs.getString("FTP_OTHER_PARAMETER"));
			npInputDef.setTMSJobId(rs.getString("TMS_JOB_ID"));
			npInputDef.setStartDate(rs.getString("START_DATE"));
			npInputDef.setNopaperDesc(rs.getString("NOPAPER_DESC"));
			npInputDef.setPsLevel(rs.getString("PS_LEVEL"));
		} catch (SQLException e) {
			e.printStackTrace();
		}
		return npInputDef;
	}


}
