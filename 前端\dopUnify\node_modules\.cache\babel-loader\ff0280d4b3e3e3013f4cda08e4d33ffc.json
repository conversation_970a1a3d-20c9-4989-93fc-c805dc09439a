{"remainingRequest": "E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\node_modules\\babel-loader\\lib\\index.js!E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\node_modules\\eslint-loader\\index.js??ref--13-0!E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\src\\api\\views\\system\\config\\timingService\\index.js", "dependencies": [{"path": "E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\src\\api\\views\\system\\config\\timingService\\index.js", "mtime": 1686019810529}, {"path": "E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\babel.config.js", "mtime": 1686019818359}, {"path": "E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\.babelrc", "mtime": 1705285078668}, {"path": "E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\node_modules\\eslint-loader\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["request", "defaultSettings", "prefix", "service", "system", "SysTimingSer", "query", "data", "url", "method", "params", "message", "pause", "resume", "execute", "deleteSchedule", "add", "modify"], "sources": ["E:/1_Project/XYD_Project/dop-4.0/dop-4.1-qianduan-unify/dopUnify/src/api/views/system/config/timingService/index.js"], "sourcesContent": ["import request from '@/utils/request'\r\nimport defaultSettings from '@/settings'\r\nconst prefix = defaultSettings.service.system\r\n\r\n// 定时服务配置相关接口\r\nexport const SysTimingSer = {\r\n  // 查询\r\n  query(data) {\r\n    return request({\r\n      url: prefix + '/scheduleManage/query.do',\r\n      method: 'get',\r\n      params: { message: data }\r\n    })\r\n  },\r\n  pause(data) {\r\n    // 暂停一个定时服务\r\n    return request({\r\n      url: prefix + '/scheduleManage/pauseOperation.do',\r\n      method: 'post',\r\n      data\r\n    })\r\n  },\r\n  resume(data) {\r\n    // 恢复一个定时服务\r\n    return request({\r\n      url: prefix + '/scheduleManage/resumeOperation.do',\r\n      method: 'post',\r\n      data\r\n    })\r\n  },\r\n  execute(data) {\r\n    // 立刻执行一个定时服务\r\n    return request({\r\n      url: prefix + '/scheduleManage/executeOperation.do',\r\n      method: 'post',\r\n      data\r\n    })\r\n  },\r\n  deleteSchedule(data) {\r\n    // 删除\r\n    return request({\r\n      url: prefix + '/scheduleManage/delete.do',\r\n      method: 'delete',\r\n      data\r\n    })\r\n  },\r\n  add(data) {\r\n    // 新增定时服务\r\n    return request({\r\n      url: prefix + '/scheduleManage/add.do',\r\n      method: 'post',\r\n      data\r\n    })\r\n  },\r\n  modify(data) {\r\n    // 修改定时服务\r\n    return request({\r\n      url: prefix + '/scheduleManage/modify.do',\r\n      method: 'post',\r\n      data\r\n    })\r\n  }\r\n\r\n}\r\n"], "mappings": "AAAA,OAAOA,OAAO,MAAM,iBAAiB;AACrC,OAAOC,eAAe,MAAM,YAAY;AACxC,IAAMC,MAAM,GAAGD,eAAe,CAACE,OAAO,CAACC,MAAM;;AAE7C;AACA,OAAO,IAAMC,YAAY,GAAG;EAC1B;EACAC,KAAK,iBAACC,IAAI,EAAE;IACV,OAAOP,OAAO,CAAC;MACbQ,GAAG,EAAEN,MAAM,GAAG,0BAA0B;MACxCO,MAAM,EAAE,KAAK;MACbC,MAAM,EAAE;QAAEC,OAAO,EAAEJ;MAAK;IAC1B,CAAC,CAAC;EACJ,CAAC;EACDK,KAAK,iBAACL,IAAI,EAAE;IACV;IACA,OAAOP,OAAO,CAAC;MACbQ,GAAG,EAAEN,MAAM,GAAG,mCAAmC;MACjDO,MAAM,EAAE,MAAM;MACdF,IAAI,EAAJA;IACF,CAAC,CAAC;EACJ,CAAC;EACDM,MAAM,kBAACN,IAAI,EAAE;IACX;IACA,OAAOP,OAAO,CAAC;MACbQ,GAAG,EAAEN,MAAM,GAAG,oCAAoC;MAClDO,MAAM,EAAE,MAAM;MACdF,IAAI,EAAJA;IACF,CAAC,CAAC;EACJ,CAAC;EACDO,OAAO,mBAACP,IAAI,EAAE;IACZ;IACA,OAAOP,OAAO,CAAC;MACbQ,GAAG,EAAEN,MAAM,GAAG,qCAAqC;MACnDO,MAAM,EAAE,MAAM;MACdF,IAAI,EAAJA;IACF,CAAC,CAAC;EACJ,CAAC;EACDQ,cAAc,0BAACR,IAAI,EAAE;IACnB;IACA,OAAOP,OAAO,CAAC;MACbQ,GAAG,EAAEN,MAAM,GAAG,2BAA2B;MACzCO,MAAM,EAAE,QAAQ;MAChBF,IAAI,EAAJA;IACF,CAAC,CAAC;EACJ,CAAC;EACDS,GAAG,eAACT,IAAI,EAAE;IACR;IACA,OAAOP,OAAO,CAAC;MACbQ,GAAG,EAAEN,MAAM,GAAG,wBAAwB;MACtCO,MAAM,EAAE,MAAM;MACdF,IAAI,EAAJA;IACF,CAAC,CAAC;EACJ,CAAC;EACDU,MAAM,kBAACV,IAAI,EAAE;IACX;IACA,OAAOP,OAAO,CAAC;MACbQ,GAAG,EAAEN,MAAM,GAAG,2BAA2B;MACzCO,MAAM,EAAE,MAAM;MACdF,IAAI,EAAJA;IACF,CAAC,CAAC;EACJ;AAEF,CAAC"}]}