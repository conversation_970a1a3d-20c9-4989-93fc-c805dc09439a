{"remainingRequest": "E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\node_modules\\babel-loader\\lib\\index.js!E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\src\\components\\Dialog\\SunAuditDialog\\index.vue?vue&type=script&lang=js&", "dependencies": [{"path": "E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\src\\components\\Dialog\\SunAuditDialog\\index.vue", "mtime": 1686019809904}, {"path": "E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\babel.config.js", "mtime": 1686019818359}, {"path": "E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\.babelrc", "mtime": 1705285078668}, {"path": "E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1655715099000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;AAqBA;AACA;AACA;AACA,SACAA,eACAC,eACAC,wBACA;AACA;AACA;AACA;AACA;EAAAC;EAAAC;EAAAC;AACA;AAEA;EACAC;EACAC;IACAC;MACAC;MACAC;QACA;UACAC;UAAA;UACAC;UAAA;UACAC;UAAA;UACAC;UAAA;UACAC;UAAA;UACAC;UAAA;UACAC;UAAA;UACAC;UAAA;UACAC;QACA;MACA;IACA;EACA;EACAC;IACA;MACAC;MACAC;QACA;QACAC;QAAA;QACAC;QAAA;QACAC;MACA;;MACAC;QACA;QACAC;QAAA;QACAC;QAAA;QACAC;MACA;;MACAC;MAAA;MACAC;MAAA;MACAC;IACA;EACA;EACAC;EACAC;EACAC;EACAC;EAEAC;IACAC;MAAA;MACA;MACA;QACA;QACA;QACA;UACA;UACA;UACA;QACA;QACA;MACA;QACA;QACA;QACA;UACA;UACA;QACA;MACA;IACA;IAEA;AACA;IACAC;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA,sBAUA,qBARA3B,2CACAC,2DACAC,+BACAE,+BACAD,2CACAE,yCACAC,qCACAC;gBAAA,MAEAP;kBAAA;kBAAA;gBAAA;gBACA;gBACAT;kBACA;kBACA;gBACA;gBAAA;gBAAA;cAAA;gBAAA,MACAS;kBAAA;kBAAA;gBAAA;gBAAA;gBAAA,OAGA;cAAA;gBAAA4B;gBACA;kBACA;kBACArC;oBACA;kBACA;gBACA;gBAAA;gBAAA;cAAA;gBAEAsC;gBACA;kBACAA;gBACA;;gBACAC;kBACA;oBACA;oBACA;oBACA;oBAAA,2CACAC;sBAAA;oBAAA;sBAAA;wBAAA;wBACAC;sBACA;oBAAA;sBAAA;oBAAA;sBAAA;oBAAA;oBACA;oBACA1C;sBACA;wBACA;wBACA;wBACA;wBACA;wBACA;0BACA2C;0BACAC;4BACAC;4BAAA;4BACAC;4BAAA;4BACAC;4BAAA;4BACAC;4BAAA;4BACAC;4BAAA;4BACAtC;4BAAA;4BACAuC;4BAAA;4BACAC;8BACAC;8BAAA;8BACAC;4BACA;;4BACAC;0BACA;wBACA;;wBACApD;0BACA;0BACAJ;wBACA;sBACA;oBACA;kBACA;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IACA;AACA;AACA;AACA;AACA;AACA;AACA;IACAyD;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA;gBAAA,MAIA;kBAAA;kBAAA;gBAAA;gBACAzD;gBAAA,kCACA;cAAA;gBAEA;gBACAc;kBACA+B;kBACAC;oBACAY;oBACAC;oBACAC;oBACAC;oBACAC;oBACAC,wBACA,uDACA,4BACA;oBACAC;oBAAA;oBACAC;kBACA;gBACA;gBAAA;gBAAA,OACA5D;cAAA;gBAAA6D;gBAAA,kCACAA;cAAA;gBAAA;gBAAA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAIA;IACA;AACA;AACA;IACAC;MAAA;MACA;QACA;UACA;UACA;QACA;QACA;MACA;IACA;IACA;AACA;AACA;IACAC;MACA;IACA;IACA;AACA;AACA;IACAC;MACA;IACA;IACA;AACA;AACA;IACAC;MACA;QACArE;MACA;IACA;EACA;AACA", "names": ["commonMsgInfo", "commonMsgWarn", "commonMsgConfirm", "commonReq", "createFlow", "commonAuth", "name", "props", "dialogConfig", "type", "default", "visible", "auditType", "show_content_sort", "msg", "applyType", "url", "operType", "menuId", "buttonId", "data", "btn", "componentProps", "title", "width", "destroyOnClose", "form", "config", "defaultForm", "labelWidth", "fileList", "delFile", "showDialog", "computed", "watch", "created", "mounted", "methods", "dialogShow", "dialogSubmit", "checkData", "formData", "upload", "uploadFileList", "arr", "parameterList", "sysMap", "operation_value", "operation_type", "operation_desc", "oper_type_flag", "oper_do", "module_id", "plug_in", "attachment", "user_comment", "flowParameter", "auth<PERSON><PERSON><PERSON>", "authType", "authRole", "auth<PERSON><PERSON>", "userNo", "organNo", "password", "fingerValue", "oper_type", "res", "dialogClose", "delFileList", "changeFileList", "onExceed"], "sourceRoot": "src/components/Dialog/SunAuditDialog", "sources": ["index.vue"], "sourcesContent": ["<template>\n  <div class=\"audit\">\n    <el-dialog\n      ref=\"refDialog\"\n      :visible.sync=\"dialogConfig.visible\"\n      :before-close=\"dialogClose\"\n      :destroy-on-close=\"true\"\n      :append-to-body=\"true\"\n      :close-on-click-modal=\"false\"\n      v-bind=\"componentProps\"\n    >\n      <sun-form ref=\"refAudit\" :query=\"btn\" :reset=\"btn\" v-bind=\"form\" />\n      <div slot=\"footer\" class=\"dialog-footer\">\n        <el-button @click=\"dialogClose\">取 消</el-button>\n        <el-button type=\"primary\" @click=\"dialogSubmit\">确 定</el-button>\n      </div>\n    </el-dialog>\n  </div>\n</template>\n\n<script>\nimport { commonBlank } from '@/utils/common'\nimport { dictionaryGet } from '@/utils/dictionary'\nimport { encryptResult } from '@/utils/crypto' // 加密\nimport {\n  commonMsgInfo,\n  commonMsgWarn,\n  commonMsgConfirm\n} from '@/utils/message.js' // 提示信息\nimport { config, config2 } from './info.js'\nimport { btnDatas, getDataValue } from '@/utils/dictionary' // 字典配置\nimport { Common } from '@/api'\nconst { commonReq, createFlow, commonAuth } = Common.SysAudit\nconst { upload } = Common.DataAuditing\n\nexport default {\n  name: 'SunAuditDialog',\n  props: {\n    dialogConfig: {\n      type: Object,\n      default: () => {\n        return {\n          visible: false, // 显示隐藏配置\n          auditType: '', // 页面按钮审核方式\n          show_content_sort: '', // 远程审核所需携带的参数  本地审核和不审核参数为''\n          msg: '', // 请求参数\n          applyType: '', // 请求方式， post get delete\n          url: '', // 请求的服务地址(/system/serve/add.do)\n          operType: '', // 操作类型 增删改 远程审核需要 例：删除 'OPERATE_DELETE'\n          menuId: '', // 菜单id\n          buttonId: '' // 按钮id\n        }\n      }\n    }\n  },\n  data() {\n    return {\n      btn: false,\n      componentProps: {\n        // 弹出框属性\n        title: '审核提交', // 弹出框标题 1-本地授权确认  2-远程审核提交\n        width: '38%', // 当前弹出框宽度 默认80%\n        destroyOnClose: true // 关闭时销毁 Dialog 中的元素\n      },\n      form: {\n        // 表单属性\n        config: {}, // 表单项配置  点击确认弹框前进行赋值\n        defaultForm: {}, // 默认值配置 targetUserno:'',psd:'',author_type:'' 或者file_url:'',postscript:''\n        labelWidth: '15rem' // 当前表单标签宽度配置\n      },\n      fileList: [], // 上传文件列表\n      delFile: [], // 删除文件后的文件列表\n      showDialog: true\n    }\n  },\n  computed: {},\n  watch: {},\n  created() {},\n  mounted() {},\n\n  methods: {\n    dialogShow() {\n      const { auditType } = this.dialogConfig\n      if (auditType.check_flag === '1') {\n        // 审核方式 1-本地审核\n        this.dialogConfig.visible = true\n        this.$nextTick(() => {\n          this.componentProps.title = '授权确认'\n          this.form.config = config() //\n          this.form.defaultForm.author_type = '1'\n        })\n        // 授权判断是否通过  执行用户号密码校验方法\n      } else if (auditType.check_flag === '2') {\n        // 审核方式 2-远程审核\n        this.dialogConfig.visible = true\n        this.$nextTick(() => {\n          this.componentProps.title = '审核提交'\n          this.form.config = config2(this)\n        })\n      }\n    },\n\n    /**\n     * 确定*/\n    async dialogSubmit() {\n      const {\n        auditType,\n        show_content_sort,\n        msg,\n        url,\n        applyType,\n        operType,\n        menuId,\n        buttonId\n      } = this.dialogConfig\n      if (auditType.check_flag === '0' || commonBlank(auditType.check_flag)) {\n        // 审核方式 0-不审核\n        commonReq(msg, url, applyType).then((res) => {\n          this.$emit('dialogAuditSubmit', res) // 将返回值  传递给父组件  执行父组件的方法\n          this.dialogClose() // 审核授权弹窗关闭\n        })\n      } else if (auditType.check_flag === '1') {\n        // 审核方式 1-本地审核\n        // 授权判断是否通过  执行用户号密码校验方法\n        const checkData = await this.authCheck('1', auditType.check_rule)\n        if (checkData?.retCode === '200') {\n          this.dialogClose() // 验证成功 审核授权弹窗关闭\n          commonReq(msg, url, applyType).then((res) => {\n            this.$emit('dialogAuditSubmit', res) // 将返回值  传递给父组件  执行父组件的方法\n          })\n        }\n      } else {\n        const formData = new FormData() //  用FormData存放上传文件\n        this.fileList.forEach((file) => {\n          formData.append('file', file.raw) // file.raw\n        })\n        upload(formData).then((response) => {\n          if (response.retCode === '200') {\n            // 处理上传文件url\n            const { uploadFileList } = response.retMap\n            const arr = []\n            for (const File of uploadFileList) {\n              arr.push(File.saveFileName)\n            }\n            this.form.defaultForm.attachment = arr.join()\n            commonMsgConfirm('是否确认提交审核？', this, (param) => {\n              if (param) {\n                this.dialogClose() // 审核授权弹窗关闭\n                // 父组件新增修改弹窗loading\n                this.$parent.dialogLoading()\n                // 将增删改等操做的请求参数，及创建流程的请求参数一起随同流程传给后台  等审批同意后在执行增删改等操作请求\n                const flowMsg = {\n                  parameterList: [],\n                  sysMap: {\n                    operation_value: msg, // 增删改等操作的请求参数\n                    operation_type: dictionaryGet(operType), // 操作类型 (\"OP001\"  新增)\n                    operation_desc: menuId, // 菜单id\n                    oper_type_flag: getDataValue(btnDatas, buttonId).label, // 操作按钮名称---'新增' '删除'  '修改'\n                    oper_do: url, // 请求地址 system/serve/add.do\n                    show_content_sort: show_content_sort, // 审核-操作详情-表单参数\n                    module_id: auditType.check_rule, // 远程审核时表示审核模板id\n                    plug_in: {\n                      attachment: this.form.defaultForm.attachment, // 文件\n                      user_comment: this.form.defaultForm.user_comment // 附言\n                    },\n                    flowParameter: {} // 参数待定--流程变量数据（不知道哪个页面会用到这个参数 目前传空不影响）\n                  }\n                }\n                createFlow(flowMsg).then((res) => {\n                  this.$emit('dialogAuditSubmit', res) // 调用父组件增删改等请求成功后的操作 - 远程审核不传递res\n                  commonMsgInfo('操作申请已提交！', this)\n                })\n              }\n            })\n          }\n        })\n      }\n    },\n    /**\n     * 通用授权密码校验\n     * @param    authType    授权类型，默认 1-密码授权，后续扩展支持 2-指纹授权 ... 等\n     * @param    authRole    授权角色，支持逗号隔开的多个，默认为空时，以系统参数中的授权角色 DEFAULT_AUTH_ROLE 为准\n     * @param    authUser    指定授权用户\n     * @param    authOrgan    指定授权机构\n     */\n    async authCheck(authType, authRole, authUser, authOrgan) {\n      // 先满足基本功能，后续要加上用户框密码框的和确定的回车功能\n      // 不能为自己授权 校验用户号\n      try {\n        if (this.form.defaultForm.targetUserno === this.$store.getters.userNo) {\n          commonMsgInfo('不能为自己授权', this)\n          return false\n        }\n        // 发请求校验密码\n        const msg = {\n          parameterList: [''],\n          sysMap: {\n            authType: authType,\n            authRole: authRole,\n            authOrgan: commonBlank(authOrgan) ? '' : authOrgan,\n            userNo: this.form.defaultForm.targetUserno,\n            organNo: this.$store.getters.organNo,\n            password: encryptResult(\n              this.$store.getters.initParams.enSecMap.encryptType,\n              this.form.defaultForm.psd\n            ),\n            fingerValue: '', // 指纹登录方法请求返回数据，目前门户没有指纹，先传空值\n            oper_type: 'commonAuth'\n          }\n        }\n        const res = await commonAuth(msg)\n        return res\n      } catch (e) {\n        // console.log(e)\n      }\n    },\n    /**\n     * 授权审核弹出框：关闭\n     */\n    dialogClose() {\n      this.$nextTick(() => {\n        if (this.dialogConfig.auditType.check_flag !== '0') {\n          // 本地审核或远程审核\n          this.$refs.refAudit.resetForm()\n        }\n        this.$emit('changeAuditVisible', false)\n      })\n    },\n    /**\n     * 删除上传文件列表时的回调函数\n     * @param {Array} fileList 文件列表*/\n    delFileList(fileList) {\n      this.delFile = fileList\n    },\n    /**\n     * 上传文件列表状态改变时的回调函数\n     * @param {Array} fileList 文件列表*/\n    changeFileList(fileList) {\n      this.fileList = fileList\n    },\n    /**\n     * 上传文件个数超出时的回调函数\n     * @param {Array} fileList 文件列表*/\n    onExceed(fileList) {\n      if (fileList.length === 3) {\n        commonMsgWarn('最大上传附件个数为3！', this)\n      }\n    }\n  }\n}\n</script>\n<style lang=\"scss\" scoped>\n.dialog-footer {\n  display: flex;\n  justify-content: flex-end;\n}\n</style>\n"]}]}