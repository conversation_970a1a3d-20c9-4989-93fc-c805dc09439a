{"remainingRequest": "D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\node_modules\\thread-loader\\dist\\cjs.js!D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\node_modules\\babel-loader\\lib\\index.js!D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\src\\views\\system\\config\\message\\msgCard\\index.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\src\\views\\system\\config\\message\\msgCard\\index.vue", "mtime": 1703583638610}, {"path": "D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\babel.config.js", "mtime": 1686019818359}, {"path": "D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\.babelrc", "mtime": 1705285078668}, {"path": "D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\node_modules\\thread-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1655715099000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:aW1wb3J0IF9kZWZpbmVQcm9wZXJ0eSBmcm9tICJEOi8xX1Byb2plY3QvWFlEX1Byb2plY3QvZG9wLTQuMC9kb3AtNC4xLXFpYW5kdWFuLXVuaWZ5L2RvcFVuaWZ5L25vZGVfbW9kdWxlcy9AYmFiZWwvcnVudGltZS9oZWxwZXJzL2VzbS9kZWZpbmVQcm9wZXJ0eS5qcyI7CmltcG9ydCBfcmVnZW5lcmF0b3JSdW50aW1lIGZyb20gIkQ6LzFfUHJvamVjdC9YWURfUHJvamVjdC9kb3AtNC4wL2RvcC00LjEtcWlhbmR1YW4tdW5pZnkvZG9wVW5pZnkvbm9kZV9tb2R1bGVzL0BiYWJlbC9ydW50aW1lL2hlbHBlcnMvZXNtL3JlZ2VuZXJhdG9yUnVudGltZS5qcyI7CmltcG9ydCBfb2JqZWN0U3ByZWFkIGZyb20gIkQ6LzFfUHJvamVjdC9YWURfUHJvamVjdC9kb3AtNC4wL2RvcC00LjEtcWlhbmR1YW4tdW5pZnkvZG9wVW5pZnkvbm9kZV9tb2R1bGVzL0BiYWJlbC9ydW50aW1lL2hlbHBlcnMvZXNtL29iamVjdFNwcmVhZDIuanMiOwppbXBvcnQgX2FzeW5jVG9HZW5lcmF0b3IgZnJvbSAiRDovMV9Qcm9qZWN0L1hZRF9Qcm9qZWN0L2RvcC00LjAvZG9wLTQuMS1xaWFuZHVhbi11bmlmeS9kb3BVbmlmeS9ub2RlX21vZHVsZXMvQGJhYmVsL3J1bnRpbWUvaGVscGVycy9lc20vYXN5bmNUb0dlbmVyYXRvci5qcyI7CmltcG9ydCAiY29yZS1qcy9tb2R1bGVzL2VzLm9iamVjdC50by1zdHJpbmcuanMiOwppbXBvcnQgImNvcmUtanMvbW9kdWxlcy93ZWIuZG9tLWNvbGxlY3Rpb25zLmZvci1lYWNoLmpzIjsKaW1wb3J0ICJjb3JlLWpzL21vZHVsZXMvZXMuYXJyYXkuc3BsaWNlLmpzIjsKaW1wb3J0ICJjb3JlLWpzL21vZHVsZXMvZXMuYXJyYXkuZmluZC5qcyI7CmltcG9ydCAiY29yZS1qcy9tb2R1bGVzL2VzLmZ1bmN0aW9uLm5hbWUuanMiOwppbXBvcnQgImNvcmUtanMvbW9kdWxlcy9lcy5qc29uLnN0cmluZ2lmeS5qcyI7Ci8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCgppbXBvcnQgeyBTdW5Ob3RpY2VEaWFsb2csIFByaW50LCBTdW5GbG93RGlhbG9nLCBTdW5HZW5lcmFsRGlhbG9nIH0gZnJvbSAnQC9jb21wb25lbnRzJzsgLy8g6KGo5Y2VCmltcG9ydCB7IGZvcm1Db25maWcsIGRhdGVDb25maWcgfSBmcm9tICcuLi9zeXN0ZW1Nc2cvaW5mbyc7IC8vIOihqOWNleihqOagvOmFjee9rgppbXBvcnQgeyByaXNrQWNjb3VudENvbmZpZywgcmlza0NsaWVudENvbmZpZywgcmlza0RldGFpbENvbmZpZywgcmlza1RhYmxlQ29uZmlnLCByaXNrRm9ybUNvbmZpZywgcmlza0Zvcm1Db25maWcyLCByaXNrRm9ybUNvbmNsdXNpb25Db25maWcsIHJpc2tGb3JtQ29uZmlnMywgcmlza0Zvcm1Db25maWc0LCByaXNrRm9ybUhvQ29uY2x1c2lvbkNvbmZpZyB9IGZyb20gJy4uL3N5c3RlbU1zZy9TdW5EZXNjcmliZVRhYmxlRm9ybURpYWxvZy9pbmZvJzsgLy8g6YWN572uCmltcG9ydCBTdW5PcGVyRGV0YWlsRGlhbG9nIGZyb20gJy4uL3N5c3RlbU1zZy9TdW5PcGVyRGV0YWlsRGlhbG9nJzsKaW1wb3J0IFN1bkRlc2NyaWJlVGFibGVGb3JtRGlhbG9nIGZyb20gJy4uL3N5c3RlbU1zZy9TdW5EZXNjcmliZVRhYmxlRm9ybURpYWxvZyc7Ci8vIGltcG9ydCBGbG93RGlhbG9nIGZyb20gJy4uLy4uLy4uLy4uL2V4dGVuZC9mbG93RGlhbG9nLnZ1ZScKCmltcG9ydCB7IG9wZW5UYXNrUGFnZUJ5Tm9kZVBhdGgsIGdldFRhc2tOb2RlUGF0aCB9IGZyb20gJ0AvdXRpbHMvZmxvd1BhdGgnOwppbXBvcnQgeyB1cGxvYWRGaWxlLCBjb21tb25CbGFuayB9IGZyb20gJ0AvdXRpbHMvY29tbW9uJzsgLy8g5YWs5YWx5pa55rOVCmltcG9ydCB7IGNvbW1vbkZvcm1hdFN1YnN5c3RlbVZhbHVlIH0gZnJvbSAnQC9maWx0ZXJzJzsgLy8g5a2Q5bel56iL5qC85byP5YyW5pa55rOVCmltcG9ydCB7IGNvbW1vbk1zZ0luZm8sIGNvbW1vbk1zZ0NvbmZpcm0sIGNvbW1vbk1zZ1N1Y2Nlc3MsIGNvbW1vbk1zZ1dhcm4gfSBmcm9tICdAL3V0aWxzL21lc3NhZ2UuanMnOyAvLyDmj5DnpLrkv6Hmga8KaW1wb3J0IHsgZGF0ZU5vd0Zvcm1hdDEwIH0gZnJvbSAnQC91dGlscy9kYXRlLmpzJzsgLy8g5pel5pyf5qC85byP5YyWCmltcG9ydCB7IGRpY3Rpb25hcnlHZXQgfSBmcm9tICdAL3V0aWxzL2RpY3Rpb25hcnknOwppbXBvcnQgeyBDb21tb24sIHN5c3RlbSB9IGZyb20gJ0AvYXBpJzsKdmFyIGZpZWxkQ29udHJvbGxlciA9IENvbW1vbi5maWVsZENvbnRyb2xsZXIsCiAgZ2V0U3lzUGFyYW0gPSBDb21tb24uZ2V0U3lzUGFyYW07CnZhciByZWFkTnVtID0gc3lzdGVtLlN5c1F1ZXJ5LnJlYWROdW07CnZhciBfc3lzdGVtJFN5c01lcyA9IHN5c3RlbS5TeXNNZXMsCiAgdW5SZWFkTnVtID0gX3N5c3RlbSRTeXNNZXMudW5SZWFkTnVtLAogIGlnbm9yZU1zZyA9IF9zeXN0ZW0kU3lzTWVzLmlnbm9yZU1zZywKICBub3RpY2VNb2RpZnkgPSBfc3lzdGVtJFN5c01lcy5ub3RpY2VNb2RpZnk7CnZhciBfQ29tbW9uJERhdGFBdWRpdGluZyA9IENvbW1vbi5EYXRhQXVkaXRpbmcsCiAgY2hlY2tUYXNrID0gX0NvbW1vbiREYXRhQXVkaXRpbmcuY2hlY2tUYXNrLAogIHJlbGVhc2VUYXNrID0gX0NvbW1vbiREYXRhQXVkaXRpbmcucmVsZWFzZVRhc2ssCiAgYXVkaXRUYXNrID0gX0NvbW1vbiREYXRhQXVkaXRpbmcuYXVkaXRUYXNrLAogIGhvQ2hlY2tUYXNrID0gX0NvbW1vbiREYXRhQXVkaXRpbmcuaG9DaGVja1Rhc2ssCiAgaG9QYXNzQXVkaXRUYXNrID0gX0NvbW1vbiREYXRhQXVkaXRpbmcuaG9QYXNzQXVkaXRUYXNrLAogIHVwZGF0ZVN5c01lc3NhZ2UgPSBfQ29tbW9uJERhdGFBdWRpdGluZy51cGRhdGVTeXNNZXNzYWdlOwp2YXIgQXBwcm92ZSA9IENvbW1vbi5TeXNBcHByb3ZhbC5BcHByb3ZlOwp2YXIgdGltb3V0MTsKZXhwb3J0IGRlZmF1bHQgewogIG5hbWU6ICdNc2dDYXJkJywKICBjb21wb25lbnRzOiB7CiAgICBTdW5Ob3RpY2VEaWFsb2c6IFN1bk5vdGljZURpYWxvZywKICAgIFByaW50OiBQcmludCwKICAgIFN1bk9wZXJEZXRhaWxEaWFsb2c6IFN1bk9wZXJEZXRhaWxEaWFsb2csCiAgICBTdW5GbG93RGlhbG9nOiBTdW5GbG93RGlhbG9nLAogICAgU3VuRGVzY3JpYmVUYWJsZUZvcm1EaWFsb2c6IFN1bkRlc2NyaWJlVGFibGVGb3JtRGlhbG9nLAogICAgU3VuR2VuZXJhbERpYWxvZzogU3VuR2VuZXJhbERpYWxvZwogIH0sCiAgZGF0YTogZnVuY3Rpb24gZGF0YSgpIHsKICAgIHJldHVybiB7CiAgICAgIGNhcmRWaXNpYmxlOiB0cnVlLAogICAgICAvLyDlvLnmoYbmmL7npLrpmpDol48gIHRydWXmmL7npLoKICAgICAgbW9kYWw6IGZhbHNlLAogICAgICBtc2dOdW06ICcnLAogICAgICAvLyDns7vnu5/mtojmga/mnKrlpITnkIbmnaHmlbAKICAgICAgbXNnTGlzdDogW10sCiAgICAgIC8vIOezu+e7n+a2iOaBrwogICAgICBkaWFsb2dEYXRlOiB7CiAgICAgICAgbm90ZUxpc3Q6IFtdLAogICAgICAgIGFwcGVuZDogdHJ1ZSwKICAgICAgICB2aXNpYmxlOiBmYWxzZSwKICAgICAgICBiYWNrZ3JvdW5kQ29sb3I6IFsnI0VGRjFGRScsICcjRTVGNkYwJywgJyNGRUYwRUYnLCAnI0Y3RUNGQScsICcjRjBFRkZGJ10sCiAgICAgICAgYm9yZGVyQ29sb3I6IFsnIzM3NjRGQycsICcjM0ZCQjk3JywgJyNFQjYyNjInLCAnI0FEM0ZCQicsICcjODA0MEZBJ10sCiAgICAgICAgY29uZmlnOiBkYXRlQ29uZmlnKHRoaXMpCiAgICAgIH0sCiAgICAgIGRpYWxvZzogewogICAgICAgIC8vIOWFrOWRiuivpuaDheW8ueWHuuahhgogICAgICAgIHZpc2libGU6IGZhbHNlLAogICAgICAgIC8vIOW8gOWQry/lhbPpl63lvLnnqpcKICAgICAgICBidG5DYW5jbGU6IGZhbHNlLAogICAgICAgIC8vIOWPlua2iOaMiemSrgogICAgICAgIGJ0blN1Ym1pdDogZmFsc2UsCiAgICAgICAgLy8g56Gu5a6a5oyJ6ZKuCiAgICAgICAgY29tcG9uZW50UHJvcHM6IHsKICAgICAgICAgIC8vIOW8ueWHuuahhumFjee9ruWxnuaApwogICAgICAgICAgdG9wOiAnMTB2aCcsCiAgICAgICAgICB0aXRsZTogJ+afpeeci+WFrOWRiicsCiAgICAgICAgICB3aWR0aDogJzc0LjhyZW0nIC8vIOW9k+WJjeW8ueWHuuahhuWuveW6pgogICAgICAgIH0sCgogICAgICAgIGJ0blByaW50VmlzOiB0cnVlLAogICAgICAgIC8vIOaJk+WNsOaMiemSriAg6buY6K6k5pi+56S6CiAgICAgICAgYnRuQWdyZWVWaXM6IGZhbHNlLAogICAgICAgIC8vIOWQjOaEj+OAgeaLkue7neaMiemSrumakOiXjwogICAgICAgIG5vdGljZUNvbmZpZzogewogICAgICAgICAgaW1nU3JjOiByZXF1aXJlKCdAL2Fzc2V0cy9pbWcvb3RoZXIvbm90aWNlX2ltYWdlcy9ub3RpY2VCYWNrZ3JvdW5kLnBuZycpLAogICAgICAgICAgLy8g6IOM5pmv5Zu+54mH5Yqg6L29CiAgICAgICAgICB0aXRsZTogJ+agh+mimCcsCiAgICAgICAgICAvLyDlhazlkYrmoIfpopgKICAgICAgICAgIGluZm86IFtdLAogICAgICAgICAgLy8g5Y+R5biD5py65p6E44CB5Y+R5biD6ICF5ZCN56ew44CB5Y+R5biD5pe26Ze0CiAgICAgICAgICByZWFkTnVtOiAnJywKICAgICAgICAgIC8vIOmYheivu+mHjwogICAgICAgICAgY29udGVudDogJycsCiAgICAgICAgICAvLyDlhazlkYrmraPmlocKICAgICAgICAgIGZpbGVzOiBbXQogICAgICAgIH0KICAgICAgfSwKICAgICAgZGlhbG9nRm9ybTogewogICAgICAgIC8vIOWFrOWRiuW8ueeql+aLkue7nemAmui/h+ivtOaYjgogICAgICAgIHZpc2libGU6IGZhbHNlLAogICAgICAgIC8vIG9wcmF0ZTogJ3JlZnVzZScsIC8vIOW8ueeql+agh+ivhgogICAgICAgIGNvbXBvbmVudFByb3BzOiB7CiAgICAgICAgICAvLyDlvLnlh7rmoYblsZ7mgKcKICAgICAgICAgIHRpdGxlOiAn5ouS57ud6K+05piOJywKICAgICAgICAgIC8vIOW8ueWHuuahhuagh+mimAogICAgICAgICAgd2lkdGg6ICc1MHJlbScsCiAgICAgICAgICAvLyDlvZPliY3lvLnlh7rmoYblrr3luqYg6buY6K6kODAlCiAgICAgICAgICBhcHBlbmRUb0JvZHk6IHRydWUKICAgICAgICB9LAogICAgICAgIGZvcm06IHsKICAgICAgICAgIC8vIOihqOWNleWxnuaApwogICAgICAgICAgY29uZmlnOiBmb3JtQ29uZmlnKHRoaXMpLAogICAgICAgICAgLy8g6KGo5Y2V6aG56YWN572uCiAgICAgICAgICBkZWZhdWx0Rm9ybTogewogICAgICAgICAgICByZW1hcms6ICcnCiAgICAgICAgICB9LAogICAgICAgICAgLy8g6buY6K6k5YC86YWN572uCiAgICAgICAgICBsYWJlbFdpZHRoOiAnMTByZW0nIC8vIOW9k+WJjeihqOWNleagh+etvuWuveW6pumFjee9rgogICAgICAgIH0KICAgICAgfSwKCiAgICAgIGRpYWxvZ1Jpc2s6IHsKICAgICAgICByZWY6ICdyaXNrUmVmJywKICAgICAgICBsb2FkaW5nOiBmYWxzZSwKICAgICAgICAvLyDnrYnlvoXliqDovb3kuK0KICAgICAgICB2aXNpYmxlOiBmYWxzZSwKICAgICAgICBidG5TdWJtaXQ6IHRydWUsCiAgICAgICAgYnRuQ2FuY2xlOiB0cnVlLAogICAgICAgIGJ0blBhc3M6IGZhbHNlLAogICAgICAgIGJ0bkZhaWw6IGZhbHNlLAogICAgICAgIGJ0bkhvUGFzczogZmFsc2UsCiAgICAgICAgYnRuSG9GYWlsOiBmYWxzZSwKICAgICAgICBidG5Ib0Nsb3NlOiBmYWxzZSwKICAgICAgICBidG5Ib1N1Ym1pdDogZmFsc2UsCiAgICAgICAgYnRuSG9DYW5jbGU6IGZhbHNlLAogICAgICAgIGNvbXBvbmVudFByb3BzOiB7CiAgICAgICAgICAvLyDlvLnlh7rmoYblsZ7mgKcKICAgICAgICAgIHRpdGxlOiAn6aOO6Zmp5o6S5p+lJywKICAgICAgICAgIC8vIOW8ueWHuuahhuagh+mimAogICAgICAgICAgd2lkdGg6ICc4MCUnIC8vIOW9k+WJjeW8ueWHuuahhuWuveW6piDpu5jorqQ4MCUKICAgICAgICB9LAoKICAgICAgICBkZXNNZXNzYWdlOiBbewogICAgICAgICAgdGl0bGU6ICfotKbmiLfln7rmnKzkv6Hmga8nLAogICAgICAgICAgY29sdW1uOiAzLAogICAgICAgICAgYXJyOiByaXNrQWNjb3VudENvbmZpZygpLAogICAgICAgICAgZGVzRGF0YToge30KICAgICAgICB9LCB7CiAgICAgICAgICB0aXRsZTogJ+WuouaIt+WfuuacrOS/oeaBrycsCiAgICAgICAgICBjb2x1bW46IDMsCiAgICAgICAgICBhcnI6IHJpc2tDbGllbnRDb25maWcoKSwKICAgICAgICAgIGRlc0RhdGE6IHt9CiAgICAgICAgfSwgewogICAgICAgICAgdGl0bGU6ICflrqLmiLflsL3osIPor6bmg4UnLAogICAgICAgICAgY29sdW1uOiAzLAogICAgICAgICAgYXJyOiByaXNrRGV0YWlsQ29uZmlnKCksCiAgICAgICAgICBkZXNEYXRhOiB7fQogICAgICAgIH1dLAogICAgICAgIC8vIOaPj+i/sOWIl+ihqOaVsOaNrgogICAgICAgIHRhYmxlQ29uZmlnOiB7CiAgICAgICAgICAvLyDooajmoLzlsZ7mgKcKICAgICAgICAgIHJlZjogJ3RhYmxlUmVmJywKICAgICAgICAgIGNvbHVtbnM6IHJpc2tUYWJsZUNvbmZpZyh0aGlzKSwKICAgICAgICAgIC8vIOihqOWktAogICAgICAgICAgc2VsZWN0aW9uOiBmYWxzZSwKICAgICAgICAgIC8vIOWkjemAiQogICAgICAgICAgaW5kZXhOdW1iZXI6IHRydWUsCiAgICAgICAgICAvLyDluo/lj7cKICAgICAgICAgIGxvYWRpbmc6IGZhbHNlLAogICAgICAgICAgLy8g562J5b6F5Yqg6L295LitCiAgICAgICAgICBwYWdlTGlzdDogewogICAgICAgICAgICAvLyDpobXnoIEKICAgICAgICAgICAgdG90YWxOdW06IDAsCiAgICAgICAgICAgIC8vIOaAu+mhteaVsAogICAgICAgICAgICBjdXJyZW50UGFnZTogMSwKICAgICAgICAgICAgLy8g5b2T5YmN6aG1CiAgICAgICAgICAgIHBhZ2VTaXplOiAxMCAvLyDlvZPliY3pobXmmL7npLrmnaHmlbAKICAgICAgICAgIH0sCgogICAgICAgICAgY29tcG9uZW50UHJvcHM6IHsKICAgICAgICAgICAgLy8g6KGo5qC85bGe5oCn6YWN572uCiAgICAgICAgICAgIGRhdGE6IFtdLAogICAgICAgICAgICAvLyDooajmlbDmja4KICAgICAgICAgICAgaGVpZ2h0OiAnMjByZW0nCiAgICAgICAgICB9CiAgICAgICAgfSwKICAgICAgICBmb3JtOiB7CiAgICAgICAgICAvLyDooajljZXlsZ7mgKcKICAgICAgICAgIC8vIGNvbmZpZzogcmlza0Zvcm1Db25maWcodGhpcyksIC8vIOihqOWNlemhuemFjee9rgogICAgICAgICAgY29uZmlnOiAnJywKICAgICAgICAgIGRlZmF1bHRGb3JtOiB7CiAgICAgICAgICAgIHJlcG9ydFBhcmFtc19hY2NubzogJycsCiAgICAgICAgICAgIHJlcG9ydFBhcmFtc19iZWdpbmRhdGU6ICcnCiAgICAgICAgICB9LAogICAgICAgICAgLy8g6buY6K6k5YC86YWN572uCiAgICAgICAgICBsYWJlbFdpZHRoOiAnMTByZW0nIC8vIOW9k+WJjeihqOWNleagh+etvuWuveW6pumFjee9rgogICAgICAgIH0sCgogICAgICAgIGZvcm0yOiB7CiAgICAgICAgICAvLyDooajljZXlsZ7mgKcKICAgICAgICAgIGNvbmZpZzogcmlza0Zvcm1Db25jbHVzaW9uQ29uZmlnKHRoaXMpLAogICAgICAgICAgLy8g6KGo5Y2V6aG56YWN572uCiAgICAgICAgICBkZWZhdWx0Rm9ybTogewogICAgICAgICAgICBjYmJfY2hlY2tfZGVhbF9yZXN1bHQ6ICcnCiAgICAgICAgICB9LAogICAgICAgICAgLy8g6buY6K6k5YC86YWN572uCiAgICAgICAgICBsYWJlbFdpZHRoOiAnMTByZW0nIC8vIOW9k+WJjeihqOWNleagh+etvuWuveW6pumFjee9rgogICAgICAgIH0sCgogICAgICAgIHJpc2tEaWFsb2dEYXRhOiB7CiAgICAgICAgICBkdWVfbGlzdDogW10sCiAgICAgICAgICB0YXNrX25vX2xpc3Q6IFtdLAogICAgICAgICAgdHlwZTogJycgLy8g5by556qX57G75Z6LCiAgICAgICAgfQogICAgICB9LAoKICAgICAgZGlhbG9nT3BlcjogewogICAgICAgIC8vIOa1geeoi+S6i+mhuS3mk43kvZznlLPor7flvLnmoYbmlbDmja4KICAgICAgICB2aXNpYmxlOiBmYWxzZSwKICAgICAgICBjb21wb25lbnRQcm9wczogewogICAgICAgICAgLy8g5by55Ye65qGG5bGe5oCnCiAgICAgICAgICB0aXRsZTogJycsCiAgICAgICAgICAvLyDlvLnlh7rmoYbmoIfpopgKICAgICAgICAgIHdpZHRoOiAnODAlJyAvLyDlvZPliY3lvLnlh7rmoYblrr3luqYg6buY6K6kODAlCiAgICAgICAgfSwKCiAgICAgICAgcXVlcnlUeXBlOiAnJywKICAgICAgICBjdXJyZW50Um93OiBbXSwKICAgICAgICAvLyDpgInkuK3ooYwKICAgICAgICB0YWJsZURhdGE6IHsKICAgICAgICAgIC8vIOaTjeS9nOivpuaDheW8ueeqly0tLeWIoOmZpC3ooajmoLwKICAgICAgICAgIHRhYmxlQ29sdW1uczogW10sCiAgICAgICAgICAvLyDooajlpLTphY3nva4KICAgICAgICAgIHNlbGVjdGlvbjogZmFsc2UsCiAgICAgICAgICAvLyDlpI3pgIkKICAgICAgICAgIGluZGV4TnVtYmVyOiB0cnVlLAogICAgICAgICAgLy8g5bqP5Y+3CiAgICAgICAgICBsb2FkaW5nOiBmYWxzZSwKICAgICAgICAgIGNvbXBvbmVudFByb3BzOiB7CiAgICAgICAgICAgIGRhdGE6IFtdLAogICAgICAgICAgICAvLyDooajmoLzmlbDmja4KICAgICAgICAgICAgaGVpZ2h0OiAnMTAwcHgnLAogICAgICAgICAgICBmb3JtUm93OiAwIC8vIOihqOWNleihjOaVsAogICAgICAgICAgfSwKCiAgICAgICAgICBwYWdlTGlzdDogewogICAgICAgICAgICB0b3RhbE51bTogMCwKICAgICAgICAgICAgY3VycmVudFBhZ2U6IDEsCiAgICAgICAgICAgIC8vIOW9k+WJjemhtQogICAgICAgICAgICBwYWdlU2l6ZTogdGhpcy4kc3RvcmUuZ2V0dGVycy5wYWdlU2l6ZSAvLyDlvZPliY3pobXmmL7npLrmnaHmlbAKICAgICAgICAgIH0KICAgICAgICB9LAoKICAgICAgICBkZXNEYXRhOiB7fSwKICAgICAgICAvLyDmk43kvZzor6bmg4XooajljZV2YWx1ZQogICAgICAgIGRlZmF1bHRGb3JtMjogW10sCiAgICAgICAgLy8g5pON5L2c6K+m5oOF6KGo5Y2Va2V5CiAgICAgICAgdGFibGVEZXRhaWxEYXRhOiBbXSwKICAgICAgICAvLyDooajmoLzlpITnkIbmmI7nu4bmlbDmja4KICAgICAgICBidG5JZDogJycgLy8g5LiK5Lyg5paH5Lu25o+Q5Lqk5pe25Yik5pat5piv5ZCM5oSP5pON5L2c5oiW5LiN5ZCM5oSP5pON5L2c5qCH5b+XCiAgICAgIH0sCgogICAgICBmbG93Q29uZmlnOiB7CiAgICAgICAgLy8g5rWB56iL5qih5p2/6LCD55So5by556qXCiAgICAgICAgdmlzaWJsZTogZmFsc2UsCiAgICAgICAgY29tcG9uZW50UHJvcHM6IHsKICAgICAgICAgIC8vIOW8ueWHuuahhuWxnuaApwogICAgICAgICAgdGl0bGU6ICcnLAogICAgICAgICAgLy8g5by55Ye65qGG5qCH6aKYCiAgICAgICAgICB0b3A6ICcwcHgnLAogICAgICAgICAgd2lkdGg6ICcxMDAlJyAvLyDlvZPliY3lvLnlh7rmoYblrr3luqYg6buY6K6kODAlCiAgICAgICAgfSwKCiAgICAgICAgbW9kdWxlX2lkOiAnJywKICAgICAgICAvLyDmqKHmnb9pZAogICAgICAgIGNoaWxkRmxvd1VybDogJ3N0YXRpYy9odG1sL2Zsb3cvd29ya2Zsb3cuaHRtbCcsCiAgICAgICAgLy8g5a2Q5bel56iL5rWB56iL6aG16Z2idXJsCiAgICAgICAgdGFza19pZDogJycgLy8g5rWB56iLaWQKICAgICAgfSwKCiAgICAgIC8vIOWfuue6v+mAmueUqOW8ueeqlyBiZWdpbgogICAgICBnZW5lcmFsQ29uZmlnOiB7CiAgICAgICAgdmlzaWJsZTogZmFsc2UsCiAgICAgICAgY29tcG9uZW50UHJvcHM6IHsKICAgICAgICAgIC8vIOW8ueWHuuahhuWxnuaApwogICAgICAgICAgdGl0bGU6ICcnLAogICAgICAgICAgLy8g5by55Ye65qGG5qCH6aKYCiAgICAgICAgICB0b3A6ICcwcHgnLAogICAgICAgICAgd2lkdGg6ICcxMDAlJywKICAgICAgICAgIC8vIOW9k+WJjeW8ueWHuuahhuWuveW6piDpu5jorqQ4MCUKICAgICAgICAgIGhlaWdodDogJzEwMCUnCiAgICAgICAgfSwKICAgICAgICBkYXRhOiB7fSwKICAgICAgICBjaGlsZEZsb3dVcmw6ICdzdGF0aWMvaHRtbC9hc3Nlc3MvZGlhbG9nL3Rhc2tDb25maXJtRGlhbG9nLmh0bWwnLAogICAgICAgIC8vIOWtkOW3peeoi+mhtemdonVybAogICAgICAgIHVybFR5cGU6ICdhY3RpdmUnLAogICAgICAgIC8vIHVybOexu+WeiwogICAgICAgIGhhbmRsZVN0YXRlOiBmYWxzZQogICAgICB9LAogICAgICBjdXJSb3c6IHt9LAogICAgICAvLyDlvZPliY3ngrnlh7vmtojmga8KICAgICAgZGlhbG9nQnRuRGF0YXM6IHsKICAgICAgICBidG5QYXNzOiB7CiAgICAgICAgICBzaG93OiB0cnVlCiAgICAgICAgfSwKICAgICAgICBidG5SZWZ1c2U6IHsKICAgICAgICAgIHNob3c6IHRydWUKICAgICAgICB9CiAgICAgIH0sCiAgICAgIGxvYWRpbmc6IHRydWUKICAgIH07CiAgfSwKICBjb21wdXRlZDogewogICAgLy8g6K6h566X5bGe5oCn6LCD55Soc3RvcmXkuK3nmoTkuLvpopjoibIKICAgIHZhckNvbG9yOiBmdW5jdGlvbiB2YXJDb2xvcigpIHsKICAgICAgcmV0dXJuIHsKICAgICAgICAnLS1jb2xvcic6IHRoaXMuJHN0b3JlLnN0YXRlLnNldHRpbmdzLnRoZW1lCiAgICAgIH07CiAgICB9LAogICAgbXNnTnVtTG9hZGluZzogZnVuY3Rpb24gbXNnTnVtTG9hZGluZygpIHsKICAgICAgcmV0dXJuIHRoaXMubXNnTnVtID09PSAnJzsKICAgIH0KICB9LAogIHdhdGNoOiB7fSwKICBjcmVhdGVkOiBmdW5jdGlvbiBjcmVhdGVkKCkge30sCiAgbW91bnRlZDogZnVuY3Rpb24gbW91bnRlZCgpIHsKICAgIHZhciBfdGhpcyA9IHRoaXM7CiAgICB0aGlzLmxvYWRpbmcgPSB0cnVlOwogICAgdGhpcy5tc2dUb3RhbE51bSgpOwogICAgLy8g5pON5L2c5rWB56iL5aSE55CG5a6M5q+VIOabtOaWsOacquWkhOeQhuWIl+ihqAogICAgdGhpcy4kYnVzLiRvbignb3BlckFwcHJvdmVVcGRhdGUnLCBmdW5jdGlvbiAoZGF0YSkgewogICAgICBpZiAoZGF0YSkgewogICAgICAgIF90aGlzLm1zZ1RvdGFsTnVtKCk7CiAgICAgIH0KICAgIH0pOwogICAgLy8g6L2s5o6I5p2D5rWB56iL44CB5Z+657q/5rWB56iL5qih5p2/6LCD55So5aSE55CG5a6M5q+VIOabtOaWsOacquWkhOeQhuWIl+ihqAogICAgdGhpcy4kYnVzLiRvbignbW91ZGxlU3ViVXBkYXRlJywgZnVuY3Rpb24gKGRhdGEpIHsKICAgICAgaWYgKGRhdGEpIHsKICAgICAgICBfdGhpcy5tc2dUb3RhbE51bSgpOwogICAgICB9CiAgICB9KTsKICAgIC8vIOafpeeci+WFrOWRigogICAgdGhpcy4kYnVzLiRvbignbm90aWNlVXBkYXRlJywgZnVuY3Rpb24gKGRhdGEpIHsKICAgICAgaWYgKGRhdGEpIHsKICAgICAgICBfdGhpcy5tc2dUb3RhbE51bSgpOwogICAgICB9CiAgICB9KTsKICAgIC8vIOWFrOWRiua1geeoi+WuoeaJuSDph43mlrDmn6Xor6Lns7vnu5/mtojmga/pobXpnaLlhazlkYoKICAgIHRoaXMuJGJ1cy4kb24oJ25vdGljZUFwcm92ZVVwZGF0ZScsIGZ1bmN0aW9uIChkYXRhKSB7CiAgICAgIGlmIChkYXRhKSB7CiAgICAgICAgX3RoaXMubXNnVG90YWxOdW0oKTsKICAgICAgfQogICAgfSk7CiAgICAvLyB3ZWJTb2NrZXTmtojmga/mjqjpgIEKICAgIHRoaXMuJGJ1cy4kb24oJ3NvY2tldE1zZycsIGZ1bmN0aW9uIChkYXRhKSB7CiAgICAgIGlmICghY29tbW9uQmxhbmsoZGF0YSkpIHsKICAgICAgICAvLyDlpoLmnpzmtojmga/lvLnmoYbml7bmmL7npLrnirbmgIEKICAgICAgICBpZiAoX3RoaXMuY2FyZFZpc2libGUgPT09IHRydWUpIHsKICAgICAgICAgIF90aGlzLm1zZ0xpc3QudW5zaGlmdChkYXRhKTsKICAgICAgICB9IGVsc2UgewogICAgICAgICAgLy8g5raI5oGv5by556qX5piv5YWz6Zet54q25oCBCiAgICAgICAgICBfdGhpcy5jYXJkVmlzaWJsZSA9IHRydWU7CiAgICAgICAgICBfdGhpcy5tc2dOdW0gPSAnJzsKICAgICAgICAgIF90aGlzLm1zZ1RvdGFsTnVtKCk7CiAgICAgICAgfQogICAgICAgIF90aGlzLm1zZ051bSsrOwogICAgICAgIF90aGlzLiRidXMuJGVtaXQoJ3VuZGVhbE1zZ051bScsIF90aGlzLm1zZ051bSk7CiAgICAgIH0KICAgIH0pOwogICAgLy8g6aaW6aG1LeW+heWKnuS6i+mhueiwg+eUqOezu+e7n+a2iOaBr+W8ueeqlwogICAgdGhpcy4kYnVzLiRvbignb3BlblN5c3RlbU1zZycsIGZ1bmN0aW9uIChkYXRhKSB7CiAgICAgIGlmIChkYXRhKSB7CiAgICAgICAgX3RoaXMubXNnRGV0YWlsKGRhdGEpOwogICAgICB9CiAgICB9KTsKICB9LAogIC8vIOmUgOavgeWvueW6lOiHquWumuS5ieS6i+S7tgogIGJlZm9yZURlc3Ryb3k6IGZ1bmN0aW9uIGJlZm9yZURlc3Ryb3koKSB7CiAgICB0aGlzLiRidXMuJG9mZignb3BlckFwcHJvdmVVcGRhdGUnKTsKICAgIHRoaXMuJGJ1cy4kb2ZmKCdub3RpY2VVcGRhdGUnKTsKICAgIHRoaXMuJGJ1cy4kb2ZmKCdub3RpY2VBcHJvdmVVcGRhdGUnKTsKICAgIHRoaXMuJGJ1cy4kb2ZmKCdtb3VkbGVTdWJVcGRhdGUnKTsKICAgIHRoaXMuJGJ1cy4kb2ZmKCdzb2NrZXRNc2cnKTsKICAgIHRoaXMuJGJ1cy4kb2ZmKCdvcGVuU3lzdGVtTXNnJyk7CiAgICBjbGVhclRpbWVvdXQodGltb3V0MSk7CiAgfSwKICBtZXRob2RzOiB7CiAgICAvLyAvKioKICAgIC8vICAqIOezu+e7n+a2iOaBr+W8ueeqlyBMb2FkaW5nKi8KICAgIC8vIGRpYWxvZ0xvYWRpbmcoKSB7CiAgICAvLyAgIHRoaXMubG9hZGluZyA9IHRoaXMuJGxvYWRpbmcoewogICAgLy8gICAgIC8vIOWjsOaYjuS4gOS4qmxvYWRpbmflr7nosaEKICAgIC8vICAgICB0ZXh0OiAn5q2j5Zyo5Yqg6L29Li4uJywgLy8g5Yqg6L295Yqo55S755qE5paH5a2XCiAgICAvLyAgICAgc3Bpbm5lcjogJ2VsLWljb24tbG9hZGluZycsIC8vIOW8leWFpeeahGxvYWRpbmflm77moIcKICAgIC8vICAgICBiYWNrZ3JvdW5kOiAncmdiYSgwLCAwLCAwLCAwLjMpJywgLy8g6IOM5pmv6aKc6ImyCiAgICAvLyAgICAgdGFyZ2V0OiBkb2N1bWVudC5xdWVyeVNlbGVjdG9yKCcuZGlhbG9nTG9hZGluZycpLCAvLyDpnIDopoHpga7nvannmoTljLrln58KICAgIC8vICAgICBib2R5OiB0cnVlLAogICAgLy8gICAgIGN1c3RvbUNsYXNzOiAnbWFzaycgLy8g6YGu572p5bGC5paw5aKe57G75ZCNCiAgICAvLyAgIH0pCiAgICAvLyAgIHJldHVybiB0aGlzLmxvYWRpbmcKICAgIC8vIH0sCiAgICAvKioNCiAgICAgKuezu+e7n+a2iOaBr+aAu+adoeaVsOWPiua2iOaBr+WIl+ihqCAgKi8KICAgIG1zZ1RvdGFsTnVtOiBmdW5jdGlvbiBtc2dUb3RhbE51bSgpIHsKICAgICAgdmFyIF90aGlzMiA9IHRoaXM7CiAgICAgIHZhciBtc2cgPSB7CiAgICAgICAgcGFyYW1ldGVyTGlzdDogW3t9XSwKICAgICAgICBjdXJyZW50UGFnZTogMSwKICAgICAgICBwYWdlU2l6ZTogdGhpcy4kc3RvcmUuZ2V0dGVycy5wYWdlU2l6ZSwKICAgICAgICB1c2VyX25vOiB0aGlzLiRzdG9yZS5nZXR0ZXJzLnVzZXJObywKICAgICAgICBvcmdhbl9ubzogdGhpcy4kc3RvcmUuZ2V0dGVycy5vcmdhbk5vLAogICAgICAgIG9yZ2FuX2xldmVsOiB0aGlzLiRzdG9yZS5nZXR0ZXJzLm9yZ2FuTGV2ZWwsCiAgICAgICAgcm9sZV9ubzogdGhpcy4kc3RvcmUuZ2V0dGVycy5yb2xlTm8KICAgICAgfTsKICAgICAgdW5SZWFkTnVtKG1zZykudGhlbihmdW5jdGlvbiAocmVzKSB7CiAgICAgICAgdmFyIF9yZXMkcmV0TWFwID0gcmVzLnJldE1hcCwKICAgICAgICAgIHRvdGFsTnVtID0gX3JlcyRyZXRNYXAudG90YWxOdW0sCiAgICAgICAgICBhbGxMaXN0ID0gX3JlcyRyZXRNYXAuYWxsTGlzdDsKICAgICAgICBfdGhpczIubXNnTGlzdCA9IGFsbExpc3Q7IC8vIOa2iOaBr+WIl+ihqAogICAgICAgIF90aGlzMi5tc2dOdW0gPSB0b3RhbE51bTsgLy8g5pyq5aSE55CG5raI5oGv5pWwCgogICAgICAgIF90aGlzMi5sb2FkaW5nID0gZmFsc2U7CiAgICAgICAgLy8gdGhpcy5sb2FkaW5nLmNsb3NlKCkgLy8g5YWz6ZetbG9hZGluZwogICAgICAgIC8vIOWwhuacgOaWsOacquWkhOeQhua2iOaBr+adoeaVsOabtOaWsOWIsCDorr7nva4t57O757uf5raI5oGvCiAgICAgICAgX3RoaXMyLiRidXMuJGVtaXQoJ3VuZGVhbE1zZ051bScsIHRvdGFsTnVtKTsKICAgICAgfSk7CiAgICB9LAogICAgLy8g5by556qX5YWz6ZetCiAgICBjYXJkQ2xvc2U6IGZ1bmN0aW9uIGNhcmRDbG9zZSgpIHsKICAgICAgdGhpcy5jYXJkVmlzaWJsZSA9IGZhbHNlOwogICAgfSwKICAgIC8vIOabtOWkmgogICAgZ2V0TW9yZTogZnVuY3Rpb24gZ2V0TW9yZSgpIHsKICAgICAgLy8gdGhpcy4kcm91dGVyLnB1c2goJy9zeXN0ZW1zJykKICAgICAgdGhpcy4kcm91dGVyLnB1c2goIi9zeXN0ZW1zP3JlZGlyZWN0PSIuY29uY2F0KHRoaXMuJHJvdXRlLmZ1bGxQYXRoKSk7CiAgICB9LAogICAgLy8g5b+955Wl5YWo6YOoCiAgICBpZ25vcmVNc2dBbGw6IGZ1bmN0aW9uIGlnbm9yZU1zZ0FsbCgpIHsKICAgICAgdmFyIF90aGlzMyA9IHRoaXM7CiAgICAgIHZhciBtc2dOb0xpc3QgPSBbXTsKICAgICAgdGhpcy5tc2dMaXN0LmZvckVhY2goZnVuY3Rpb24gKGl0ZW0pIHsKICAgICAgICBtc2dOb0xpc3QucHVzaChpdGVtLm1zZ19ubyk7CiAgICAgIH0pOwogICAgICB2YXIgbXNnID0gewogICAgICAgIHBhcmFtZXRlckxpc3Q6IFtdLAogICAgICAgIC8vIG1zZ19ubzogbXNnTm9MaXN0LmpvaW4oKSwKICAgICAgICB1c2VyX25vOiB0aGlzLiRzdG9yZS5nZXR0ZXJzLnVzZXJObywKICAgICAgICBvcmdhbl9ubzogdGhpcy4kc3RvcmUuZ2V0dGVycy5vcmdhbk5vLAogICAgICAgIG9yZ2FuX2xldmVsOiB0aGlzLiRzdG9yZS5nZXR0ZXJzLm9yZ2FuTGV2ZWwsCiAgICAgICAgcm9sZV9ubzogdGhpcy4kc3RvcmUuZ2V0dGVycy5yb2xlTm8KICAgICAgfTsKICAgICAgaWdub3JlTXNnKG1zZykudGhlbihmdW5jdGlvbiAocmVzKSB7CiAgICAgICAgX3RoaXMzLm1zZ0xpc3Quc3BsaWNlKDAsIF90aGlzMy5tc2dOdW0pOwogICAgICAgIF90aGlzMy4kYnVzLiRlbWl0KCd1bmRlYWxNc2dOdW0nLCAwKTsKICAgICAgICBfdGhpczMuY2FyZFZpc2libGUgPSBmYWxzZTsgLy8g5by556qX5YWz6ZetCiAgICAgIH0pOwogICAgfSwKICAgIC8vIOW/veeVpeS4gOadoQogICAgaWdub3JlTXNnT25lOiBmdW5jdGlvbiBpZ25vcmVNc2dPbmUobXNnTm8sIGluZGV4KSB7CiAgICAgIHZhciBfdGhpczQgPSB0aGlzOwogICAgICB2YXIgbXNnID0gewogICAgICAgIHBhcmFtZXRlckxpc3Q6IFtdLAogICAgICAgIG1zZ19ubzogbXNnTm8sCiAgICAgICAgdXNlcl9ubzogdGhpcy4kc3RvcmUuZ2V0dGVycy51c2VyTm8KICAgICAgfTsKICAgICAgaWdub3JlTXNnKG1zZykudGhlbihmdW5jdGlvbiAocmVzKSB7CiAgICAgICAgX3RoaXM0Lm1zZ0xpc3Quc3BsaWNlKGluZGV4LCAxKTsKICAgICAgICBfdGhpczQubXNnTnVtLS07IC8vIOacquWkhOeQhua2iOaBr+adoeaVsAogICAgICAgIGlmIChfdGhpczQubXNnTGlzdC5sZW5ndGggPT09IDApIHsKICAgICAgICAgIF90aGlzNC5jYXJkVmlzaWJsZSA9IGZhbHNlOyAvLyDlv73nlaXmnIDlkI7kuIDmnaHmtojmga/ml7blvLnnqpflhbPpl60KICAgICAgICB9CiAgICAgIH0pOwogICAgfSwKICAgIC8vIOa2iOaBr+ivpuaDhQogICAgbXNnRGV0YWlsOiBmdW5jdGlvbiBtc2dEZXRhaWwocm93KSB7CiAgICAgIHZhciBfdGhpczUgPSB0aGlzOwogICAgICByZXR1cm4gX2FzeW5jVG9HZW5lcmF0b3IoIC8qI19fUFVSRV9fKi9fcmVnZW5lcmF0b3JSdW50aW1lKCkubWFyayhmdW5jdGlvbiBfY2FsbGVlKCkgewogICAgICAgIHZhciBmbG93VmFsdWUsIGtleSwgcm93RGF0YSwgZmxvd0RhdGEsIGRldGFpbERhdGEsIHJvdXRlciwga3NoTWVudUlkLCB5ak1lbnVJZCwgdW5pZnlSLCBub3RpY2VSLCBsYXN0X21vZGlfZGF0ZSwgX3JvdyRtc2dfcGFyYW1ldGVyLCBub3RlX2luZm8sIG5vdGVfbmFtZSwgd2Fybl90aW1lLCB3YXJuX25vdGUsIHdhcm5fcGhvbmUsIG5vdGU7CiAgICAgICAgcmV0dXJuIF9yZWdlbmVyYXRvclJ1bnRpbWUoKS53cmFwKGZ1bmN0aW9uIF9jYWxsZWUkKF9jb250ZXh0KSB7CiAgICAgICAgICB3aGlsZSAoMSkgewogICAgICAgICAgICBzd2l0Y2ggKF9jb250ZXh0LnByZXYgPSBfY29udGV4dC5uZXh0KSB7CiAgICAgICAgICAgICAgY2FzZSAwOgogICAgICAgICAgICAgICAgY29uc29sZS5sb2coJ21hbycsIHJvdyk7CiAgICAgICAgICAgICAgICAvLyB0aGlzLnRhYmxlLmN1cnJlbnRSb3cgPSByb3cKICAgICAgICAgICAgICAgIC8vIOWIpOaWrea2iOaBr+exu+WeiwogICAgICAgICAgICAgICAgaWYgKCEocm93Lm1zZ190eXBlID09PSAnMDEyJykpIHsKICAgICAgICAgICAgICAgICAgX2NvbnRleHQubmV4dCA9IDc7CiAgICAgICAgICAgICAgICAgIGJyZWFrOwogICAgICAgICAgICAgICAgfQogICAgICAgICAgICAgICAgLy8g5raI5oGv57G75Z6L5Li65YWs5ZGKCiAgICAgICAgICAgICAgICBfdGhpczUuZGlhbG9nLm5vdGljZUNvbmZpZy5pbmZvID0gW107IC8vIOa4heepuuavj+asoeW8ueeql+aVsOaNrgogICAgICAgICAgICAgICAgX3RoaXM1LmRpYWxvZy52aXNpYmxlID0gdHJ1ZTsKICAgICAgICAgICAgICAgIF90aGlzNS4kbmV4dFRpY2soZnVuY3Rpb24gKCkgewogICAgICAgICAgICAgICAgICAvLyDlvLnnqpfliqDovb3lrozmr5XlkI7otYvlgLwg5YWs5ZGK5YaF5a65562JCiAgICAgICAgICAgICAgICAgIF90aGlzNS5kaWFsb2cubm90aWNlQ29uZmlnLnRpdGxlID0gcm93Lm1zZ19wYXJhbWV0ZXIubm90aWNlX3RpdGxlOyAvLyDmoIfpopgKICAgICAgICAgICAgICAgICAgdmFyIGluZm8gPSBbXTsKICAgICAgICAgICAgICAgICAgdmFyIGluZm9PYmogPSB7CiAgICAgICAgICAgICAgICAgICAgcHVibGlzaF9vcmdhbjogcm93LmNyZWF0ZV9vcmdhbiwKICAgICAgICAgICAgICAgICAgICBwdWJsaXNoX3VzZXI6IHJvdy5jcmVhdGVfdXNlciwKICAgICAgICAgICAgICAgICAgICBwdWJsaXNoX3RpbWU6IHJvdy5jcmVhdGVfdGltZQogICAgICAgICAgICAgICAgICB9OwogICAgICAgICAgICAgICAgICBpbmZvLnB1c2goaW5mb09iaik7CiAgICAgICAgICAgICAgICAgIF90aGlzNS5kaWFsb2cubm90aWNlQ29uZmlnLmluZm8gPSBpbmZvOwogICAgICAgICAgICAgICAgICBfdGhpczUuZGlhbG9nLm5vdGljZUNvbmZpZy5jb250ZW50ID0gcm93Lm1zZ19wYXJhbWV0ZXIubm90aWNlX2NvbnRlbnQ7IC8vIOWFrOWRiuWGheWuuQogICAgICAgICAgICAgICAgICBpZiAocm93Lm1zZ19wYXJhbWV0ZXIuZmlsZV91cmwpIHsKICAgICAgICAgICAgICAgICAgICBfdGhpczUuZGlhbG9nLm5vdGljZUNvbmZpZy5maWxlcyA9IHVwbG9hZEZpbGUocm93Lm1zZ19wYXJhbWV0ZXIuZmlsZV91cmwpOwogICAgICAgICAgICAgICAgICB9IGVsc2UgewogICAgICAgICAgICAgICAgICAgIF90aGlzNS5kaWFsb2cubm90aWNlQ29uZmlnLmZpbGVzID0gW107CiAgICAgICAgICAgICAgICAgIH0KICAgICAgICAgICAgICAgICAgLy8g5p+l6K+i6ZiF6K+76YePCiAgICAgICAgICAgICAgICAgIHZhciByZWFkTXNnID0gewogICAgICAgICAgICAgICAgICAgIHBhcmFtZXRlckxpc3Q6IFtdLAogICAgICAgICAgICAgICAgICAgIG5vdGljZV9pZDogcm93Lm1zZ19wYXJhbWV0ZXIubm90aWNlX2lkLAogICAgICAgICAgICAgICAgICAgIHVzZXJfbm86IF90aGlzNS4kc3RvcmUuZ2V0dGVycy51c2VyTm8sCiAgICAgICAgICAgICAgICAgICAgb3JnYW5fbm86IF90aGlzNS4kc3RvcmUuZ2V0dGVycy5vcmdhbk5vCiAgICAgICAgICAgICAgICAgIH07CiAgICAgICAgICAgICAgICAgIHJlYWROdW0ocmVhZE1zZykudGhlbihmdW5jdGlvbiAocmVzcG9uc2UpIHsKICAgICAgICAgICAgICAgICAgICB2YXIgcmVhZF9udW0gPSByZXNwb25zZS5yZXRNYXAucmVhZF9udW07CiAgICAgICAgICAgICAgICAgICAgX3RoaXM1LmRpYWxvZy5ub3RpY2VDb25maWcucmVhZE51bSA9IHJlYWRfbnVtOwogICAgICAgICAgICAgICAgICAgIF90aGlzNS5kaWFsb2cuY29tcG9uZW50UHJvcHMudGl0bGUgPSAn5p+l55yL5YWs5ZGKJzsKICAgICAgICAgICAgICAgICAgICAvLyDmlLnlj5jlpITnkIbnirbmgIEgMC3mnKrlpITnkIYtLS0+MS3lt7LlpITnkIblnKgKICAgICAgICAgICAgICAgICAgICB2YXIgbm90aWNlX2lkID0gcm93Lm1zZ19wYXJhbWV0ZXIubm90aWNlX2lkOwogICAgICAgICAgICAgICAgICAgIHZhciBwYXJhbUpzb24gPSB7fTsKICAgICAgICAgICAgICAgICAgICBpZiAoY29tbW9uQmxhbmsocm93Lm1zZ19ubykpIHsKICAgICAgICAgICAgICAgICAgICAgIHBhcmFtSnNvbiA9IHsKICAgICAgICAgICAgICAgICAgICAgICAgbm90aWNlX2lkOiByb3cubm90aWNlX2lkLAogICAgICAgICAgICAgICAgICAgICAgICBub3RpY2VfdGl0bGU6IHJvdy5ub3RpY2VfdGl0bGUsCiAgICAgICAgICAgICAgICAgICAgICAgIG5vdGljZV9jb250ZW50OiByb3cubm90aWNlX2NvbnRlbnQsCiAgICAgICAgICAgICAgICAgICAgICAgIGZpbGVfdXJsOiByb3cuZmlsZV91cmwKICAgICAgICAgICAgICAgICAgICAgIH07CiAgICAgICAgICAgICAgICAgICAgfQogICAgICAgICAgICAgICAgICAgIGlmIChyb3cuZGVhbF9zdGF0ZSA9PT0gJzAnKSB7CiAgICAgICAgICAgICAgICAgICAgICAvLyDlpoLmnpzmmK/mnKrlpITnkIbnirbmgIEgIOeCueWHu+WQjuabtOaWsOmYheivu+eKtuaAge+8jOWQpuWImeS4jemcgOimgeabtOaUuemYheivu+eKtuaAgQogICAgICAgICAgICAgICAgICAgICAgX3RoaXM1LmZpcnN0UGFnZShub3RpY2VfaWQsIHJvdy5tc2dfbm8sIHBhcmFtSnNvbik7CiAgICAgICAgICAgICAgICAgICAgfQogICAgICAgICAgICAgICAgICB9KTsKICAgICAgICAgICAgICAgIH0pOwogICAgICAgICAgICAgICAgX2NvbnRleHQubmV4dCA9IDExNTsKICAgICAgICAgICAgICAgIGJyZWFrOwogICAgICAgICAgICAgIGNhc2UgNzoKICAgICAgICAgICAgICAgIGlmICghKHJvdy5tc2dfdHlwZSA9PT0gJzAxMScpKSB7CiAgICAgICAgICAgICAgICAgIF9jb250ZXh0Lm5leHQgPSAxMTQ7CiAgICAgICAgICAgICAgICAgIGJyZWFrOwogICAgICAgICAgICAgICAgfQogICAgICAgICAgICAgICAgLy8g5raI5oGv57G75Z6L5Li65rWB56iL5LqL6aG5CiAgICAgICAgICAgICAgICBpZiAocm93Lmhhc093blByb3BlcnR5LmNhbGwoJ3ZhbHVlJykpIHsKICAgICAgICAgICAgICAgICAgLy8g5Y+z5LiL6KeS5omT5byACiAgICAgICAgICAgICAgICAgIGZsb3dWYWx1ZSA9IEpTT04ucGFyc2Uocm93LnZhbHVlKTsKICAgICAgICAgICAgICAgICAgZm9yIChrZXkgaW4gZmxvd1ZhbHVlKSB7CiAgICAgICAgICAgICAgICAgICAgcm93W2tleV0gPSBmbG93VmFsdWVba2V5XTsKICAgICAgICAgICAgICAgICAgfQogICAgICAgICAgICAgICAgICByb3cudmFsdWUgPSAnJzsKICAgICAgICAgICAgICAgIH0KICAgICAgICAgICAgICAgIC8vIOiOt+WPluS7u+WKoeiKgueCuei3r+W+hAogICAgICAgICAgICAgICAgX2NvbnRleHQubmV4dCA9IDExOwogICAgICAgICAgICAgICAgcmV0dXJuIGdldFRhc2tOb2RlUGF0aChyb3cpOwogICAgICAgICAgICAgIGNhc2UgMTE6CiAgICAgICAgICAgICAgICByb3dEYXRhID0gX2NvbnRleHQuc2VudDsKICAgICAgICAgICAgICAgIGlmICghY29tbW9uQmxhbmsocm93RGF0YSkpIHsKICAgICAgICAgICAgICAgICAgX2NvbnRleHQubmV4dCA9IDE0OwogICAgICAgICAgICAgICAgICBicmVhazsKICAgICAgICAgICAgICAgIH0KICAgICAgICAgICAgICAgIHJldHVybiBfY29udGV4dC5hYnJ1cHQoInJldHVybiIpOwogICAgICAgICAgICAgIGNhc2UgMTQ6CiAgICAgICAgICAgICAgICBpZiAoIShyb3dEYXRhLm1zZ19wYXJhbWV0ZXIuZ2VuZXJhbF9mbGFnID09PSAnMScpKSB7CiAgICAgICAgICAgICAgICAgIF9jb250ZXh0Lm5leHQgPSAyMDsKICAgICAgICAgICAgICAgICAgYnJlYWs7CiAgICAgICAgICAgICAgICB9CiAgICAgICAgICAgICAgICBmbG93RGF0YSA9IF9vYmplY3RTcHJlYWQoX29iamVjdFNwcmVhZCh7fSwgcm93Lm1zZ19wYXJhbWV0ZXIpLCB7fSwgewogICAgICAgICAgICAgICAgICAvLyDmt7vliqDmtYHnqIvnsbvlnovlsZ7mgKcgIC8vIOacquWuoeaJue+8iOato+W4uOWKoOi9ve+8iQogICAgICAgICAgICAgICAgICBmbG93X3R5cGU6IHJvd0RhdGEuZGVhbF9zdGF0ZSA9PT0gJzAnID8gJ2RlYWxGbG93TXNnJyA6ICdzaG93Rmxvd01zZycgLy8gZmxvd190eXBlID0gJ2RlYWxGbG93TXNnJyAvLyDlpITnkIbmtYHnqIvns7vnu5/mtojmga8gZmxvd190eXBlID0gJ3Nob3dGbG93TXNnJyAvLyDmn6XnnIvmtYHnqIvns7vnu5/mtojmga8KICAgICAgICAgICAgICAgIH0pOyAvLyDngrnlh7vml7blsIbmlbDmja7lrZjlgqjliLB2dWV46YeMCgogICAgICAgICAgICAgICAgX3RoaXM1LiRzdG9yZS5jb21taXQoJ2NvbW1vbi9TRVRfRkxPV19EQVRBJywgZmxvd0RhdGEpOwogICAgICAgICAgICAgICAgLy8g5omT5byA5rWB56iL55WM6Z2iCiAgICAgICAgICAgICAgICBfdGhpczUubW91ZGxlT3Blbihyb3cpOwogICAgICAgICAgICAgICAgX2NvbnRleHQubmV4dCA9IDExMjsKICAgICAgICAgICAgICAgIGJyZWFrOwogICAgICAgICAgICAgIGNhc2UgMjA6CiAgICAgICAgICAgICAgICAvLyDpnZ7mtYHnqIvmj5Lku7blrqHmibkv5p+l55yLCiAgICAgICAgICAgICAgICAvLyDlpoLmnpzmmK/lhazlkYrmtYHnqIvlrqHmibnkuJTmmK/mnKrlpITnkIbnirbmgIHvvIzlhazlkYrlvLnnqpfpgJrov4fjgIHmi5Lnu53mjInpkq7mmL7npLoKICAgICAgICAgICAgICAgIGlmIChyb3cuZGVhbF9zdGF0ZSA9PT0gJzAnKSB7CiAgICAgICAgICAgICAgICAgIF90aGlzNS5kaWFsb2cuYnRuVmlzaWJsZSA9IHRydWU7CiAgICAgICAgICAgICAgICB9CiAgICAgICAgICAgICAgICBfY29udGV4dC5uZXh0ID0gMjM7CiAgICAgICAgICAgICAgICByZXR1cm4gb3BlblRhc2tQYWdlQnlOb2RlUGF0aChyb3dEYXRhLCByb3dEYXRhLmRlYWxfc3RhdGUpOwogICAgICAgICAgICAgIGNhc2UgMjM6CiAgICAgICAgICAgICAgICBkZXRhaWxEYXRhID0gX2NvbnRleHQuc2VudDsKICAgICAgICAgICAgICAgIGNvbnNvbGUubG9nKHJvd0RhdGEpOwogICAgICAgICAgICAgICAgLy8g5YWs5ZGK5rWB56iL5LqL6aG55aSE55CGCiAgICAgICAgICAgICAgICBpZiAoIShyb3cubm9kZV9tb2R1bGVfcGF0aCA9PT0gJ3N0YXRpYy9qcy9hY3Rpb24vc3lzdGVtL25vdGljZU1hbmFnZS9ub3RpY2VEaWFsb2dBcHByb3ZlLmpzJykpIHsKICAgICAgICAgICAgICAgICAgX2NvbnRleHQubmV4dCA9IDMyOwogICAgICAgICAgICAgICAgICBicmVhazsKICAgICAgICAgICAgICAgIH0KICAgICAgICAgICAgICAgIF90aGlzNS5jdXJSb3cgPSBkZXRhaWxEYXRhWzBdOyAvLyDlhazlkYrmtYHnqIvlrqHmibnmlbDmja7ov5vooYzlpIfku70KICAgICAgICAgICAgICAgIGlmIChyb3cuZGVhbF9zdGF0ZSA9PT0gJzAnKSB7CiAgICAgICAgICAgICAgICAgIC8vIOacquWkhOeQhuaYvuekuuWQjOaEj+S4jeWQjOaEj+aMiemSriAgIOS4jeaYvuekuuaJk+WNsOaMiemSrgogICAgICAgICAgICAgICAgICBfdGhpczUuZGlhbG9nLmJ0blByaW50VmlzID0gZmFsc2U7CiAgICAgICAgICAgICAgICAgIF90aGlzNS5kaWFsb2cuYnRuQWdyZWVWaXMgPSB0cnVlOwogICAgICAgICAgICAgICAgfSBlbHNlIHsKICAgICAgICAgICAgICAgICAgX3RoaXM1LmRpYWxvZy5idG5QcmludFZpcyA9IHRydWU7CiAgICAgICAgICAgICAgICAgIF90aGlzNS5kaWFsb2cuYnRuQWdyZWVWaXMgPSBmYWxzZTsKICAgICAgICAgICAgICAgIH0KICAgICAgICAgICAgICAgIF90aGlzNS5kaWFsb2cudmlzaWJsZSA9IHRydWU7IC8vIOWFrOWRiua1geeoi+WkhOeQhuW8ueeqlwogICAgICAgICAgICAgICAgX3RoaXM1LiRuZXh0VGljayhmdW5jdGlvbiAoKSB7CiAgICAgICAgICAgICAgICAgIF90aGlzNS5kaWFsb2cuY29tcG9uZW50UHJvcHMudGl0bGUgPSAn5YWs5ZGK5a6h5om5JzsKICAgICAgICAgICAgICAgICAgX3RoaXM1LmRpYWxvZy5ub3RpY2VDb25maWcudGl0bGUgPSBkZXRhaWxEYXRhWzBdLm5vdGljZV90aXRsZTsgLy8g5qCH6aKYCiAgICAgICAgICAgICAgICAgIF90aGlzNS5kaWFsb2cubm90aWNlQ29uZmlnLmluZm8gPSBkZXRhaWxEYXRhOwogICAgICAgICAgICAgICAgICBfdGhpczUuZGlhbG9nLm5vdGljZUNvbmZpZy5jb250ZW50ID0gZGV0YWlsRGF0YVswXS5ub3RpY2VfY29udGVudDsgLy8g5YWs5ZGK5YaF5a65CiAgICAgICAgICAgICAgICAgIGlmIChkZXRhaWxEYXRhWzBdLmZpbGVfdXJsKSB7CiAgICAgICAgICAgICAgICAgICAgX3RoaXM1LmRpYWxvZy5ub3RpY2VDb25maWcuZmlsZXMgPSB1cGxvYWRGaWxlKGRldGFpbERhdGFbMF0uZmlsZV91cmwpOwogICAgICAgICAgICAgICAgICB9IGVsc2UgewogICAgICAgICAgICAgICAgICAgIF90aGlzNS5kaWFsb2cubm90aWNlQ29uZmlnLmZpbGVzID0gW107CiAgICAgICAgICAgICAgICAgIH0KICAgICAgICAgICAgICAgIH0pOwogICAgICAgICAgICAgICAgX2NvbnRleHQubmV4dCA9IDExMjsKICAgICAgICAgICAgICAgIGJyZWFrOwogICAgICAgICAgICAgIGNhc2UgMzI6CiAgICAgICAgICAgICAgICBpZiAoIShyb3cubm9kZV9tb2R1bGVfcGF0aCA9PT0gJ3N0YXRpYy9qcy9hY3Rpb24vaW5kZXgvYXV0aG9yL2F1dGhvclJlcXVlc3REaWFsb2cuanMnKSkgewogICAgICAgICAgICAgICAgICBfY29udGV4dC5uZXh0ID0gMzY7CiAgICAgICAgICAgICAgICAgIGJyZWFrOwogICAgICAgICAgICAgICAgfQogICAgICAgICAgICAgICAgLy8g6L2s5o6I5p2D5a6h5om5CiAgICAgICAgICAgICAgICBpZiAoY29tbW9uQmxhbmsoZGV0YWlsRGF0YSkpIHsKICAgICAgICAgICAgICAgICAgY29tbW9uTXNnSW5mbygn5p+l6K+i6K+m5oOF5pWw5o2u5Li656m677yBJywgX3RoaXM1KTsKICAgICAgICAgICAgICAgIH0gZWxzZSB7CiAgICAgICAgICAgICAgICAgIC8vIOi9rOaOiOadg+aooeadvwogICAgICAgICAgICAgICAgICBfdGhpczUuY2hhbmdlT3BlclZpc2libGUodHJ1ZSk7CiAgICAgICAgICAgICAgICAgIF90aGlzNS5kaWFsb2dPcGVyLmN1cnJlbnRSb3cgPSBkZXRhaWxEYXRhWzBdOwogICAgICAgICAgICAgICAgICBfdGhpczUuZGlhbG9nT3Blci5xdWVyeVR5cGUgPSBkZXRhaWxEYXRhWzFdOyAvLyDmn6Xor6LnsbvlnoswLeaIkeeahOeUs+ivtyAxLeaIkeeahOS7o+WKniAyLeaIkeeahOW3suWKngogICAgICAgICAgICAgICAgICBfdGhpczUuZGlhbG9nT3Blci50YWJsZURldGFpbERhdGEgPSBkZXRhaWxEYXRhWzNdOwogICAgICAgICAgICAgICAgICBfdGhpczUuZGlhbG9nT3Blci5pc1N1YmxpY2Vuc2UgPSBkZXRhaWxEYXRhWzRdOwogICAgICAgICAgICAgICAgfQogICAgICAgICAgICAgICAgX2NvbnRleHQubmV4dCA9IDExMjsKICAgICAgICAgICAgICAgIGJyZWFrOwogICAgICAgICAgICAgIGNhc2UgMzY6CiAgICAgICAgICAgICAgICBpZiAoIShyb3cubm9kZV9tb2R1bGVfcGF0aCA9PT0gJ3N0YXRpYy9qcy9hY3Rpb24vYWNjb3VudC9yaXNrL3Jpc2tQcmVjbHVkZURpYWxvZy5qcycpKSB7CiAgICAgICAgICAgICAgICAgIF9jb250ZXh0Lm5leHQgPSA0OTsKICAgICAgICAgICAgICAgICAgYnJlYWs7CiAgICAgICAgICAgICAgICB9CiAgICAgICAgICAgICAgICBfY29udGV4dC5uZXh0ID0gMzk7CiAgICAgICAgICAgICAgICByZXR1cm4gX3RoaXM1LmRpY3Rpb25hcnlTdWJzeXN0ZW1HZXQoJ0FPUycpOwogICAgICAgICAgICAgIGNhc2UgMzk6CiAgICAgICAgICAgICAgICBfdGhpczUuZGlhbG9nUmlzay5yaXNrRGlhbG9nRGF0YS50eXBlID0gJ3Jpc2tQcmVjbHVkZUNoZWNrRm9yT3V0bGV0cyc7IC8vIOW8ueeql+exu+WeiwogICAgICAgICAgICAgICAgX3RoaXM1LmRpYWxvZ1Jpc2sucmlza0RpYWxvZ0RhdGEudGFza19ub19saXN0ID0gZGV0YWlsRGF0YVsxXTsKICAgICAgICAgICAgICAgIF90aGlzNS5kaWFsb2dSaXNrLmRlc01lc3NhZ2VbMF0uZGVzRGF0YSA9IGRldGFpbERhdGFbMF0uYWNjSW5mb0xpc3RbMF07IC8vIOesrOS4gOS4quihqOWNleaVsOaNrgogICAgICAgICAgICAgICAgX3RoaXM1LmRpYWxvZ1Jpc2suZGVzTWVzc2FnZVsxXS5kZXNEYXRhID0gZGV0YWlsRGF0YVswXS5jdXNJbmZvTGlzdFswXTsgLy8g56ys5LqM5Liq6KGo5Y2V5pWw5o2uCiAgICAgICAgICAgICAgICBfdGhpczUuZGlhbG9nUmlzay5kZXNNZXNzYWdlWzJdLmRlc0RhdGEgPSBkZXRhaWxEYXRhWzBdLmR1ZV9saXN0WzBdOyAvLyDnrKzkuInkuKrooajljZXmlbDmja4KICAgICAgICAgICAgICAgIF90aGlzNS5kaWFsb2dSaXNrLnRhYmxlQ29uZmlnLmNvbXBvbmVudFByb3BzLmRhdGEgPSBkZXRhaWxEYXRhWzBdLmZsb3dJbmZvTGlzdDsgLy8g56ys5LiJ5Liq6KGo5qC85pWw5o2uCiAgICAgICAgICAgICAgICAvLyB0aGlzLmRpYWxvZ1Jpc2sucmlza0RpYWxvZ0RhdGEuZHVlX2xpc3QgPSBkZXRhaWxEYXRhWzBdLmR1ZV9saXN0CiAgICAgICAgICAgICAgICBfdGhpczUuZGlhbG9nUmlzay5mb3JtID0gT2JqZWN0LmFzc2lnbih7fSwgX3RoaXM1LmRpYWxvZ1Jpc2suZm9ybSwgewogICAgICAgICAgICAgICAgICBjb25maWc6IHJpc2tGb3JtQ29uZmlnKF90aGlzNSksCiAgICAgICAgICAgICAgICAgIGRlZmF1bHRGb3JtOiB7CiAgICAgICAgICAgICAgICAgICAgcmVwb3J0UGFyYW1zX2FjY25vOiAnJywKICAgICAgICAgICAgICAgICAgICByZXBvcnRQYXJhbXNfYmVnaW5kYXRlOiAnJwogICAgICAgICAgICAgICAgICB9CiAgICAgICAgICAgICAgICB9KTsKICAgICAgICAgICAgICAgIF90aGlzNS5jaGFuZ2VSaXNrVmlzaWJsZSh0cnVlKTsKICAgICAgICAgICAgICAgIF9jb250ZXh0Lm5leHQgPSAxMTI7CiAgICAgICAgICAgICAgICBicmVhazsKICAgICAgICAgICAgICBjYXNlIDQ5OgogICAgICAgICAgICAgICAgaWYgKCEocm93Lm5vZGVfbW9kdWxlX3BhdGggPT09ICdzdGF0aWMvanMvYWN0aW9uL2FjY291bnQvcmlzay9yaXNrVGFza2xpc3RBcHByb3ZhbERpYWxvZy5qcycpKSB7CiAgICAgICAgICAgICAgICAgIF9jb250ZXh0Lm5leHQgPSA2MzsKICAgICAgICAgICAgICAgICAgYnJlYWs7CiAgICAgICAgICAgICAgICB9CiAgICAgICAgICAgICAgICBfY29udGV4dC5uZXh0ID0gNTI7CiAgICAgICAgICAgICAgICByZXR1cm4gX3RoaXM1LmRpY3Rpb25hcnlTdWJzeXN0ZW1HZXQoJ0FPUycpOwogICAgICAgICAgICAgIGNhc2UgNTI6CiAgICAgICAgICAgICAgICBfdGhpczUuZGlhbG9nUmlzay5yaXNrRGlhbG9nRGF0YS50eXBlID0gJ3Jpc2tUYXNrbGlzdEFwcHJvdmFsJzsgLy8g5by556qX57G75Z6LCiAgICAgICAgICAgICAgICBfdGhpczUuZGlhbG9nUmlzay5yaXNrRGlhbG9nRGF0YS50YXNrX25vX2xpc3QgPSBkZXRhaWxEYXRhWzFdOyAvLyDlvZPliY1jYXJk54K55Ye755qE6KGMCiAgICAgICAgICAgICAgICBfdGhpczUuZGlhbG9nUmlzay5kZXNNZXNzYWdlWzBdLmRlc0RhdGEgPSBkZXRhaWxEYXRhWzBdLmFjY19saXN0WzBdOyAvLyDnrKzkuIDkuKrooajljZXmlbDmja4KICAgICAgICAgICAgICAgIF90aGlzNS5kaWFsb2dSaXNrLmRlc01lc3NhZ2VbMV0uZGVzRGF0YSA9IGRldGFpbERhdGFbMF0uY3VzX2xpc3RbMF07IC8vIOesrOS6jOS4quihqOWNleaVsOaNrgogICAgICAgICAgICAgICAgX3RoaXM1LmRpYWxvZ1Jpc2sudGFibGVDb25maWcuY29tcG9uZW50UHJvcHMuZGF0YSA9IGRldGFpbERhdGFbMF0uZmxvd19saXN0OyAvLyDnrKzkuInkuKrooajmoLzmlbDmja4KICAgICAgICAgICAgICAgIF90aGlzNS5kaWFsb2dSaXNrLmRlc01lc3NhZ2VbMl0uZGVzRGF0YSA9IGRldGFpbERhdGFbMF0uZHVlX2xpc3RbMF07IC8vIOesrOS4ieS4quihqOWNleaVsOaNrgogICAgICAgICAgICAgICAgLy8gdGhpcy5kaWFsb2dSaXNrLnJpc2tEaWFsb2dEYXRhLmR1ZV9saXN0ID0gZGV0YWlsRGF0YVswXS5kdWVfbGlzdAogICAgICAgICAgICAgICAgX3RoaXM1LmRpYWxvZ1Jpc2suZm9ybTIgPSBPYmplY3QuYXNzaWduKHt9LCBfdGhpczUuZGlhbG9nUmlzay5mb3JtMiwgewogICAgICAgICAgICAgICAgICBjb25maWc6IHJpc2tGb3JtQ29uY2x1c2lvbkNvbmZpZyhfdGhpczUpLAogICAgICAgICAgICAgICAgICBkZWZhdWx0Rm9ybTogewogICAgICAgICAgICAgICAgICAgIGNiYl9jaGVja19kZWFsX3Jlc3VsdDogY29tbW9uRm9ybWF0U3Vic3lzdGVtVmFsdWUoZGV0YWlsRGF0YVswXS5saXN0WzBdLmNiYl9jaGVja19kZWFsX3Jlc3VsdCwgJ0FNX1JJU0tfQ0hFQ0tfQ0JCX0FVRElUX0RFQUxfUkVTVUxUJywgJ0FPUycpCiAgICAgICAgICAgICAgICAgIH0KICAgICAgICAgICAgICAgIH0pOwogICAgICAgICAgICAgICAgX3RoaXM1LmRpYWxvZ1Jpc2suZm9ybSA9IE9iamVjdC5hc3NpZ24oe30sIF90aGlzNS5kaWFsb2dSaXNrLmZvcm0sIHsKICAgICAgICAgICAgICAgICAgY29uZmlnOiByaXNrRm9ybUNvbmZpZzIoX3RoaXM1KSwKICAgICAgICAgICAgICAgICAgZGVmYXVsdEZvcm06IHsKICAgICAgICAgICAgICAgICAgICBjYmJfYXVkaXRfZGVhbF9kZXM6ICcnCiAgICAgICAgICAgICAgICAgIH0KICAgICAgICAgICAgICAgIH0pOwogICAgICAgICAgICAgICAgX3RoaXM1LmNoYW5nZVJpc2tWaXNpYmxlKHRydWUpOwogICAgICAgICAgICAgICAgX2NvbnRleHQubmV4dCA9IDExMjsKICAgICAgICAgICAgICAgIGJyZWFrOwogICAgICAgICAgICAgIGNhc2UgNjM6CiAgICAgICAgICAgICAgICBpZiAoIShyb3cubm9kZV9tb2R1bGVfcGF0aCA9PT0gJ3N0YXRpYy9qcy9hY3Rpb24vYWNjb3VudC9yaXNrL3Jpc2tQcmVjbHVkZUhvQ2hlY2tEaWFsb2cuanMnKSkgewogICAgICAgICAgICAgICAgICBfY29udGV4dC5uZXh0ID0gNzc7CiAgICAgICAgICAgICAgICAgIGJyZWFrOwogICAgICAgICAgICAgICAgfQogICAgICAgICAgICAgICAgX2NvbnRleHQubmV4dCA9IDY2OwogICAgICAgICAgICAgICAgcmV0dXJuIF90aGlzNS5kaWN0aW9uYXJ5U3Vic3lzdGVtR2V0KCdBT1MnKTsKICAgICAgICAgICAgICBjYXNlIDY2OgogICAgICAgICAgICAgICAgX3RoaXM1LmRpYWxvZ1Jpc2sucmlza0RpYWxvZ0RhdGEudHlwZSA9ICdyaXNrUHJlY2x1ZGVIb0NoZWNrJzsgLy8g5by556qX57G75Z6LCiAgICAgICAgICAgICAgICBfdGhpczUuZGlhbG9nUmlzay5yaXNrRGlhbG9nRGF0YS50YXNrX25vX2xpc3QgPSBkZXRhaWxEYXRhWzFdOyAvLyDlvZPliY1jYXJk54K55Ye755qE6KGMCiAgICAgICAgICAgICAgICBfdGhpczUuZGlhbG9nUmlzay5kZXNNZXNzYWdlWzBdLmRlc0RhdGEgPSBkZXRhaWxEYXRhWzBdLmFjY0luZm9MaXN0WzBdOyAvLyDnrKzkuIDkuKrooajljZXmlbDmja4KICAgICAgICAgICAgICAgIF90aGlzNS5kaWFsb2dSaXNrLmRlc01lc3NhZ2VbMV0uZGVzRGF0YSA9IGRldGFpbERhdGFbMF0uY3VzSW5mb0xpc3RbMF07IC8vIOesrOS6jOS4quihqOWNleaVsOaNrgogICAgICAgICAgICAgICAgX3RoaXM1LmRpYWxvZ1Jpc2suZGVzTWVzc2FnZVsyXS5kZXNEYXRhID0gZGV0YWlsRGF0YVswXS5kdWVfbGlzdFswXTsgLy8g56ys5LiJ5Liq6KGo5Y2V5pWw5o2uCiAgICAgICAgICAgICAgICBfdGhpczUuZGlhbG9nUmlzay50YWJsZUNvbmZpZy5jb21wb25lbnRQcm9wcy5kYXRhID0gZGV0YWlsRGF0YVswXS5mbG93SW5mb0xpc3Q7IC8vIOesrOS4ieS4quihqOagvOaVsOaNrgogICAgICAgICAgICAgICAgLy8gdGhpcy5kaWFsb2dSaXNrLnJpc2tEaWFsb2dEYXRhLmR1ZV9saXN0ID0gZGV0YWlsRGF0YVswXS5kdWVfbGlzdAogICAgICAgICAgICAgICAgX3RoaXM1LmRpYWxvZ1Jpc2suZm9ybSA9IE9iamVjdC5hc3NpZ24oe30sIF90aGlzNS5kaWFsb2dSaXNrLmZvcm0sIHsKICAgICAgICAgICAgICAgICAgY29uZmlnOiByaXNrRm9ybUNvbmZpZzMoX3RoaXM1KSwKICAgICAgICAgICAgICAgICAgZGVmYXVsdEZvcm06IHsKICAgICAgICAgICAgICAgICAgICByZXBvcnRQYXJhbXNfY2hlY2tTdWdnZXN0OiAnJwogICAgICAgICAgICAgICAgICB9CiAgICAgICAgICAgICAgICB9KTsKICAgICAgICAgICAgICAgIF90aGlzNS5kaWFsb2dSaXNrLmZvcm0yID0gT2JqZWN0LmFzc2lnbih7fSwgX3RoaXM1LmRpYWxvZ1Jpc2suZm9ybTIpOwogICAgICAgICAgICAgICAgX3RoaXM1LmNoYW5nZVJpc2tWaXNpYmxlKHRydWUpOwogICAgICAgICAgICAgICAgX2NvbnRleHQubmV4dCA9IDExMjsKICAgICAgICAgICAgICAgIGJyZWFrOwogICAgICAgICAgICAgIGNhc2UgNzc6CiAgICAgICAgICAgICAgICBpZiAoIShyb3cubm9kZV9tb2R1bGVfcGF0aCA9PT0gJ3N0YXRpYy9qcy9hY3Rpb24vYWNjb3VudC9yaXNrL3Jpc2tBcHByb3ZhbEhvRGlhbG9nLmpzJykpIHsKICAgICAgICAgICAgICAgICAgX2NvbnRleHQubmV4dCA9IDkxOwogICAgICAgICAgICAgICAgICBicmVhazsKICAgICAgICAgICAgICAgIH0KICAgICAgICAgICAgICAgIF9jb250ZXh0Lm5leHQgPSA4MDsKICAgICAgICAgICAgICAgIHJldHVybiBfdGhpczUuZGljdGlvbmFyeVN1YnN5c3RlbUdldCgnQU9TJyk7CiAgICAgICAgICAgICAgY2FzZSA4MDoKICAgICAgICAgICAgICAgIF90aGlzNS5kaWFsb2dSaXNrLnJpc2tEaWFsb2dEYXRhLnR5cGUgPSAncmlza0FwcHJvdmFsSG8nOyAvLyDlvLnnqpfnsbvlnosKICAgICAgICAgICAgICAgIF90aGlzNS5kaWFsb2dSaXNrLnJpc2tEaWFsb2dEYXRhLnRhc2tfbm9fbGlzdCA9IGRldGFpbERhdGFbMV07IC8vIOW9k+WJjWNhcmTngrnlh7vnmoTooYwKICAgICAgICAgICAgICAgIF90aGlzNS5kaWFsb2dSaXNrLmRlc01lc3NhZ2VbMF0uZGVzRGF0YSA9IGRldGFpbERhdGFbMF0uYWNjX2xpc3RbMF07IC8vIOesrOS4gOS4quihqOWNleaVsOaNrgogICAgICAgICAgICAgICAgX3RoaXM1LmRpYWxvZ1Jpc2suZGVzTWVzc2FnZVsxXS5kZXNEYXRhID0gZGV0YWlsRGF0YVswXS5jdXNfbGlzdFswXTsgLy8g56ys5LqM5Liq6KGo5Y2V5pWw5o2uCiAgICAgICAgICAgICAgICBfdGhpczUuZGlhbG9nUmlzay5kZXNNZXNzYWdlWzJdLmRlc0RhdGEgPSBkZXRhaWxEYXRhWzBdLmR1ZV9saXN0WzBdOyAvLyDnrKzkuInkuKrooajljZXmlbDmja4KICAgICAgICAgICAgICAgIF90aGlzNS5kaWFsb2dSaXNrLnRhYmxlQ29uZmlnLmNvbXBvbmVudFByb3BzLmRhdGEgPSBkZXRhaWxEYXRhWzBdLmZsb3dfbGlzdDsgLy8g56ys5LiJ5Liq6KGo5qC85pWw5o2uCiAgICAgICAgICAgICAgICAvLyB0aGlzLmRpYWxvZ1Jpc2sucmlza0RpYWxvZ0RhdGEuZHVlX2xpc3QgPSBkZXRhaWxEYXRhWzBdLmR1ZV9saXN0CiAgICAgICAgICAgICAgICBfdGhpczUuZGlhbG9nUmlzay5mb3JtMiA9IE9iamVjdC5hc3NpZ24oe30sIF90aGlzNS5kaWFsb2dSaXNrLmZvcm0yLCB7CiAgICAgICAgICAgICAgICAgIGNvbmZpZzogcmlza0Zvcm1Db25maWc0KF90aGlzNSksCiAgICAgICAgICAgICAgICAgIGRlZmF1bHRGb3JtOiBkZXRhaWxEYXRhWzBdLmxpc3RbMF0KICAgICAgICAgICAgICAgIH0pOwogICAgICAgICAgICAgICAgX3RoaXM1LmRpYWxvZ1Jpc2suZm9ybSA9IE9iamVjdC5hc3NpZ24oe30sIF90aGlzNS5kaWFsb2dSaXNrLmZvcm0sIHsKICAgICAgICAgICAgICAgICAgY29uZmlnOiByaXNrRm9ybUhvQ29uY2x1c2lvbkNvbmZpZyhfdGhpczUpLAogICAgICAgICAgICAgICAgICBkZWZhdWx0Rm9ybTogewogICAgICAgICAgICAgICAgICAgIGhvX2F1ZGl0X2RlYWxfZGVzOiAnJwogICAgICAgICAgICAgICAgICB9CiAgICAgICAgICAgICAgICB9KTsKICAgICAgICAgICAgICAgIF90aGlzNS5jaGFuZ2VSaXNrVmlzaWJsZSh0cnVlKTsKICAgICAgICAgICAgICAgIF9jb250ZXh0Lm5leHQgPSAxMTI7CiAgICAgICAgICAgICAgICBicmVhazsKICAgICAgICAgICAgICBjYXNlIDkxOgogICAgICAgICAgICAgICAgaWYgKCEocm93Lm5vZGVfbW9kdWxlX3BhdGggPT09ICdzdGF0aWMvaHRtbC9hY2NvdW50L29wZW4vZGlhbG9nL2RpYWxvZ0FjY0F1ZGl0Lmh0bWwnIHx8IHJvdy5ub2RlX21vZHVsZV9wYXRoID09PSAnc3RhdGljL2h0bWwvYWNjb3VudC9vcGVuL2RpYWxvZy9kaWFsb2dBY2NSZXZpZXcuaHRtbCcgfHwgcm93Lm5vZGVfbW9kdWxlX3BhdGggPT09ICdzdGF0aWMvaHRtbC9hY2NvdW50L29wZW4vZGlhbG9nL2RpYWxvZ0FjY1JlY29yZC5odG1sJykpIHsKICAgICAgICAgICAgICAgICAgX2NvbnRleHQubmV4dCA9IDk5OwogICAgICAgICAgICAgICAgICBicmVhazsKICAgICAgICAgICAgICAgIH0KICAgICAgICAgICAgICAgIF90aGlzNS5nZW5lcmFsQ29uZmlnLmRhdGEgPSByb3dEYXRhOwogICAgICAgICAgICAgICAgX3RoaXM1LmdlbmVyYWxDb25maWcuY29tcG9uZW50UHJvcHMudGl0bGUgPSByb3cubXNnX2NvbnRlbnQ7IC8vIOaooeadv+agh+mimOWSjOW8ueeql+agh+mimAogICAgICAgICAgICAgICAgX3RoaXM1LmdlbmVyYWxDb25maWcuY2hpbGRGbG93VXJsID0gJ3N0YXRpYy9odG1sL2FjY291bnQvb3Blbi9kaWFsb2cvZGlhbG9nQWNjQXVkaXQuaHRtbCc7CiAgICAgICAgICAgICAgICBfdGhpczUuZ2VuZXJhbENvbmZpZy51cmxUeXBlID0gJ3N0YXRpYyc7CiAgICAgICAgICAgICAgICBfdGhpczUuZ2VuZXJhbENvbmZpZy52aXNpYmxlID0gdHJ1ZTsKICAgICAgICAgICAgICAgIF9jb250ZXh0Lm5leHQgPSAxMTI7CiAgICAgICAgICAgICAgICBicmVhazsKICAgICAgICAgICAgICBjYXNlIDk5OgogICAgICAgICAgICAgICAgaWYgKCEoIWNvbW1vbkJsYW5rKGRldGFpbERhdGEpICYmIGRldGFpbERhdGFbMl0gPT09IHRydWUpKSB7CiAgICAgICAgICAgICAgICAgIF9jb250ZXh0Lm5leHQgPSAxMDY7CiAgICAgICAgICAgICAgICAgIGJyZWFrOwogICAgICAgICAgICAgICAgfQogICAgICAgICAgICAgICAgLy8gaGF2ZVJpZ2h0IOS4unRydWUKICAgICAgICAgICAgICAgIF90aGlzNS5jaGFuZ2VPcGVyVmlzaWJsZSh0cnVlKTsKICAgICAgICAgICAgICAgIF90aGlzNS5kaWFsb2dPcGVyLmN1cnJlbnRSb3cgPSBkZXRhaWxEYXRhWzBdOwogICAgICAgICAgICAgICAgX3RoaXM1LmRpYWxvZ09wZXIucXVlcnlUeXBlID0gZGV0YWlsRGF0YVsxXTsgLy8g5p+l6K+i57G75Z6LMC3miJHnmoTnlLPor7cgMS3miJHnmoTku6Plip4gMi3miJHnmoTlt7Llip4KICAgICAgICAgICAgICAgIF90aGlzNS5kaWFsb2dPcGVyLnRhYmxlRGV0YWlsRGF0YSA9IGRldGFpbERhdGFbM107CiAgICAgICAgICAgICAgICBfY29udGV4dC5uZXh0ID0gMTEyOwogICAgICAgICAgICAgICAgYnJlYWs7CiAgICAgICAgICAgICAgY2FzZSAxMDY6CiAgICAgICAgICAgICAgICBpZiAoIWNvbW1vbkJsYW5rKGRldGFpbERhdGEpKSB7CiAgICAgICAgICAgICAgICAgIF9jb250ZXh0Lm5leHQgPSAxMTA7CiAgICAgICAgICAgICAgICAgIGJyZWFrOwogICAgICAgICAgICAgICAgfQogICAgICAgICAgICAgICAgY29tbW9uTXNnSW5mbygn5p+l6K+i6K+m5oOF5pWw5o2u5Li656m677yBMTExMScsIF90aGlzNSk7CiAgICAgICAgICAgICAgICBfY29udGV4dC5uZXh0ID0gMTEyOwogICAgICAgICAgICAgICAgYnJlYWs7CiAgICAgICAgICAgICAgY2FzZSAxMTA6CiAgICAgICAgICAgICAgICBpZiAoISghY29tbW9uQmxhbmsoZGV0YWlsRGF0YSkgJiYgIWRldGFpbERhdGFbMl0pKSB7CiAgICAgICAgICAgICAgICAgIF9jb250ZXh0Lm5leHQgPSAxMTI7CiAgICAgICAgICAgICAgICAgIGJyZWFrOwogICAgICAgICAgICAgICAgfQogICAgICAgICAgICAgICAgcmV0dXJuIF9jb250ZXh0LmFicnVwdCgicmV0dXJuIik7CiAgICAgICAgICAgICAgY2FzZSAxMTI6CiAgICAgICAgICAgICAgICBfY29udGV4dC5uZXh0ID0gMTE1OwogICAgICAgICAgICAgICAgYnJlYWs7CiAgICAgICAgICAgICAgY2FzZSAxMTQ6CiAgICAgICAgICAgICAgICBpZiAocm93Lm1zZ190eXBlID09PSAnMTY5JykgewogICAgICAgICAgICAgICAgICBfdGhpczUuZGlhbG9nLmNvbXBvbmVudFByb3BzLnRpdGxlID0gJ+afpeeciyc7CiAgICAgICAgICAgICAgICAgIC8vIOa4heepuuavj+asoeW8ueeql+aVsOaNrgogICAgICAgICAgICAgICAgICBfdGhpczUuZGlhbG9nLm5vdGljZUNvbmZpZy5pbmZvID0gW107CiAgICAgICAgICAgICAgICAgIF90aGlzNS5kaWFsb2cudmlzaWJsZSA9IHRydWU7CiAgICAgICAgICAgICAgICAgIF90aGlzNS4kbmV4dFRpY2soZnVuY3Rpb24gKCkgewogICAgICAgICAgICAgICAgICAgIC8vIOW8ueeql+WKoOi9veWujOavleWQjui1i+WAvAogICAgICAgICAgICAgICAgICAgIF90aGlzNS5kaWFsb2cubm90aWNlQ29uZmlnLnRpdGxlID0gJ+S6uuWKm+mihOitpuS/oeaBryc7IC8vIOagh+mimAogICAgICAgICAgICAgICAgICAgIF90aGlzNS5kaWFsb2cubm90aWNlQ29uZmlnLmltZ1NyYyA9ICcnOwogICAgICAgICAgICAgICAgICAgIF90aGlzNS5kaWFsb2cubm90aWNlQ29uZmlnLnJlYWROdW0gPSAwOwogICAgICAgICAgICAgICAgICAgIHZhciBpbmZvID0gW107CiAgICAgICAgICAgICAgICAgICAgdmFyIGluZm9PYmogPSB7CiAgICAgICAgICAgICAgICAgICAgICBwdWJsaXNoX29yZ2FuOiByb3cuY3JlYXRlX29yZ2FuLAogICAgICAgICAgICAgICAgICAgICAgcHVibGlzaF91c2VyOiByb3cuY3JlYXRlX3VzZXIsCiAgICAgICAgICAgICAgICAgICAgICBwdWJsaXNoX3RpbWU6IHJvdy5jcmVhdGVfdGltZQogICAgICAgICAgICAgICAgICAgIH07CiAgICAgICAgICAgICAgICAgICAgaW5mby5wdXNoKGluZm9PYmopOwogICAgICAgICAgICAgICAgICAgIF90aGlzNS5kaWFsb2cubm90aWNlQ29uZmlnLmluZm8gPSBpbmZvOwogICAgICAgICAgICAgICAgICAgIF90aGlzNS5kaWFsb2cubm90aWNlQ29uZmlnLmNvbnRlbnQgPSByb3cubXNnX3BhcmFtZXRlci5tc2c7IC8vIOWFrOWRiuWGheWuuQogICAgICAgICAgICAgICAgICAgIGlmIChyb3cubXNnX3BhcmFtZXRlci5maWxlX3VybCkgewogICAgICAgICAgICAgICAgICAgICAgX3RoaXM1LmRpYWxvZy5ub3RpY2VDb25maWcuZmlsZXMgPSB1cGxvYWRGaWxlKHJvdy5tc2dfcGFyYW1ldGVyLmZpbGVfdXJsKTsKICAgICAgICAgICAgICAgICAgICB9IGVsc2UgewogICAgICAgICAgICAgICAgICAgICAgX3RoaXM1LmRpYWxvZy5ub3RpY2VDb25maWcuZmlsZXMgPSBbXTsKICAgICAgICAgICAgICAgICAgICB9CiAgICAgICAgICAgICAgICAgIH0pOwogICAgICAgICAgICAgICAgfSBlbHNlIGlmIChyb3cubXNnX3R5cGUgPT09ICcxNjgnKSB7CiAgICAgICAgICAgICAgICAgIC8vIOWPr+inhuWMlumihOitpui3s+i9rOmihOitpuiusOW9lemhtemdogogICAgICAgICAgICAgICAgICByb3V0ZXIgPSBfdGhpczUuJHN0b3JlLmdldHRlcnMucGVybWlzc2lvbl9yb3V0ZXM7IC8vIOaJgOacieiPnOWNlQogICAgICAgICAgICAgICAgICBrc2hNZW51SWQgPSBfdGhpczUuJHN0b3JlLmdldHRlcnMubWVudUFyci5maW5kKGZ1bmN0aW9uIChpdGVtKSB7CiAgICAgICAgICAgICAgICAgICAgLy8g6I635Y+W5Y+v6KeG5YyW6I+c5Y2VaWQKICAgICAgICAgICAgICAgICAgICByZXR1cm4gaXRlbS5tZW51X25hbWUuaW5kZXhPZign5Y+v6KeG5YyW5bmz5Y+wJykgPiAtMTsKICAgICAgICAgICAgICAgICAgfSk7CiAgICAgICAgICAgICAgICAgIHlqTWVudUlkID0gX3RoaXM1LiRzdG9yZS5nZXR0ZXJzLm1lbnVBcnIuZmluZChmdW5jdGlvbiAoaXRlbSkgewogICAgICAgICAgICAgICAgICAgIC8vIOiOt+WPlumihOitpuiusOW9leiPnOWNlWlkCiAgICAgICAgICAgICAgICAgICAgcmV0dXJuIGl0ZW0ubWVudV9uYW1lLmluZGV4T2YoJ+mihOitpuiusOW9lScpID4gLTE7CiAgICAgICAgICAgICAgICAgIH0pOwogICAgICAgICAgICAgICAgICB1bmlmeVIgPSByb3V0ZXIuZmluZChmdW5jdGlvbiAoaXRlbSkgewogICAgICAgICAgICAgICAgICAgIC8vIOWPr+inhuWMluWKqOaAgeiPnOWNlei3r+eUsQogICAgICAgICAgICAgICAgICAgIHJldHVybiBpdGVtLm1lbnVfaWQgPT09IGtzaE1lbnVJZC5tZW51X2lkOyAvLyDlj6/op4bljJboj5zljZVpZCAg77yI54i2aWTvvIkKICAgICAgICAgICAgICAgICAgfSk7CiAgICAgICAgICAgICAgICAgIG5vdGljZVIgPSB1bmlmeVIuY2hpbGRyZW4uZmluZChmdW5jdGlvbiAoaXRlbSkgewogICAgICAgICAgICAgICAgICAgIC8vIOmihOitpuiusOW9leWKqOaAgeiPnOWNlei3r+eUsQogICAgICAgICAgICAgICAgICAgIHJldHVybiBpdGVtLm1lbnVfaWQgPT09IHlqTWVudUlkLm1lbnVfaWQ7IC8vIOmihOitpuiusOW9lWlkCiAgICAgICAgICAgICAgICAgIH0pOwoKICAgICAgICAgICAgICAgICAgX3RoaXM1LiRyb3V0ZXIucHVzaCh1bmlmeVIucGF0aCArICcvJyArIG5vdGljZVIucGF0aCk7CiAgICAgICAgICAgICAgICB9IGVsc2UgaWYgKHJvdy5tc2dfdHlwZSA9PT0gJzE2NicpIHsKICAgICAgICAgICAgICAgICAgLy8g5qOA5p+l566h55CGLeS7u+WKoeWNleS4i+WPkQogICAgICAgICAgICAgICAgICBfdGhpczUucm91dGVKdW1wKHsKICAgICAgICAgICAgICAgICAgICBuYW1lOiAn5qOA5p+l566h55CGJywKICAgICAgICAgICAgICAgICAgICBhdHRyOiAnMScKICAgICAgICAgICAgICAgICAgfSwgewogICAgICAgICAgICAgICAgICAgIG5hbWU6ICfpl67popjljZXnoa7orqQnLAogICAgICAgICAgICAgICAgICAgIGF0dHI6ICcyJwogICAgICAgICAgICAgICAgICB9KTsKICAgICAgICAgICAgICAgICAgX3RoaXM1LnRyYW5zZm9ybVN0YXR1cyhyb3cpOwogICAgICAgICAgICAgICAgfSBlbHNlIGlmIChyb3cubXNnX3R5cGUgPT09ICcxNjMnKSB7CiAgICAgICAgICAgICAgICAgIC8vIOajgOafpeeuoeeQhi3ku7vliqHlrp7kvovnlJ/miJAKICAgICAgICAgICAgICAgICAgX3RoaXM1LnJvdXRlSnVtcCh7CiAgICAgICAgICAgICAgICAgICAgbmFtZTogJ+ajgOafpeeuoeeQhicsCiAgICAgICAgICAgICAgICAgICAgYXR0cjogJzEnCiAgICAgICAgICAgICAgICAgIH0sIHsKICAgICAgICAgICAgICAgICAgICBuYW1lOiAn5Lu75Yqh5omn6KGM566h55CGJywKICAgICAgICAgICAgICAgICAgICBhdHRyOiAnMicKICAgICAgICAgICAgICAgICAgfSk7CiAgICAgICAgICAgICAgICAgIF90aGlzNS50cmFuc2Zvcm1TdGF0dXMocm93KTsKICAgICAgICAgICAgICAgIH0gZWxzZSBpZiAocm93Lm1zZ190eXBlID09PSAnMDIxJykgewogICAgICAgICAgICAgICAgICAvLyDogIPor5XnrqHnkIYt5oiR55qE6ICD6K+VCiAgICAgICAgICAgICAgICAgIF90aGlzNS5yb3V0ZUp1bXAoewogICAgICAgICAgICAgICAgICAgIG5hbWU6ICfogIPor5XnrqHnkIYnLAogICAgICAgICAgICAgICAgICAgIGF0dHI6ICcxJwogICAgICAgICAgICAgICAgICB9LCB7CiAgICAgICAgICAgICAgICAgICAgbmFtZTogJ+aIkeeahOiAg+ivlScsCiAgICAgICAgICAgICAgICAgICAgYXR0cjogJzInCiAgICAgICAgICAgICAgICAgIH0pOwogICAgICAgICAgICAgICAgICBfdGhpczUudHJhbnNmb3JtU3RhdHVzKHJvdyk7CiAgICAgICAgICAgICAgICB9IGVsc2UgaWYgKHJvdy5tc2dfdHlwZSA9PT0gJzAyMicpIHsKICAgICAgICAgICAgICAgICAgLy8g6ICD6K+V566h55CGLeiAg+ivlemFjee9rgogICAgICAgICAgICAgICAgICBfdGhpczUucm91dGVKdW1wKHsKICAgICAgICAgICAgICAgICAgICBuYW1lOiAn6ICD6K+V566h55CGJywKICAgICAgICAgICAgICAgICAgICBhdHRyOiAnMScKICAgICAgICAgICAgICAgICAgfSwgewogICAgICAgICAgICAgICAgICAgIG5hbWU6ICfmiJHnmoTogIPor5UnLAogICAgICAgICAgICAgICAgICAgIGF0dHI6ICcyJwogICAgICAgICAgICAgICAgICB9KTsKICAgICAgICAgICAgICAgICAgX3RoaXM1LnRyYW5zZm9ybVN0YXR1cyhyb3cpOwogICAgICAgICAgICAgICAgfSBlbHNlIGlmIChyb3cubXNnX3R5cGUgPT09ICcwMzUnKSB7CiAgICAgICAgICAgICAgICAgIC8vIOiAg+ivleeuoeeQhi3miYvliqjpmIXljbcKICAgICAgICAgICAgICAgICAgX3RoaXM1LnJvdXRlSnVtcCh7CiAgICAgICAgICAgICAgICAgICAgbmFtZTogJ+iAg+ivleeuoeeQhicsCiAgICAgICAgICAgICAgICAgICAgYXR0cjogJzEnCiAgICAgICAgICAgICAgICAgIH0sIHsKICAgICAgICAgICAgICAgICAgICBuYW1lOiAn5omL5Yqo6ZiF5Y23JywKICAgICAgICAgICAgICAgICAgICBhdHRyOiAnMicKICAgICAgICAgICAgICAgICAgfSk7CiAgICAgICAgICAgICAgICAgIF90aGlzNS5nZW5lcmFsT3Blbihyb3cpOwogICAgICAgICAgICAgICAgfSBlbHNlIGlmIChyb3cubXNnX3R5cGUgPT09ICcwMTUnKSB7CiAgICAgICAgICAgICAgICAgIC8vIOS6uuWRmOeuoeeQhi3kurrlkZjlspfkvY3osIPmlbQKICAgICAgICAgICAgICAgICAgX3RoaXM1LnJvdXRlSnVtcCh7CiAgICAgICAgICAgICAgICAgICAgbmFtZTogJ+S6uuWRmOeuoeeQhicsCiAgICAgICAgICAgICAgICAgICAgYXR0cjogJzEnCiAgICAgICAgICAgICAgICAgIH0sIHsKICAgICAgICAgICAgICAgICAgICBuYW1lOiAn5Lq65ZGY5bKX5L2N6LCD5pW0JywKICAgICAgICAgICAgICAgICAgICBhdHRyOiAnMicKICAgICAgICAgICAgICAgICAgfSk7CiAgICAgICAgICAgICAgICAgIF90aGlzNS50cmFuc2Zvcm1TdGF0dXMocm93KTsKICAgICAgICAgICAgICAgIH0gZWxzZSBpZiAocm93Lm1zZ190eXBlID09PSAnMTY5JykgewogICAgICAgICAgICAgICAgICAvLyDnmbvorrDnsL/nrqHnkIYt55m76K6w57C/5o+Q6YaSCiAgICAgICAgICAgICAgICAgIF90aGlzNS5yb3V0ZUp1bXAoewogICAgICAgICAgICAgICAgICAgIG5hbWU6ICfnlLXlrZDnmbvorrDnsL8nLAogICAgICAgICAgICAgICAgICAgIGF0dHI6ICcxJwogICAgICAgICAgICAgICAgICB9LCB7CiAgICAgICAgICAgICAgICAgICAgbmFtZTogJ+eZu+iusOewvycsCiAgICAgICAgICAgICAgICAgICAgYXR0cjogJzInCiAgICAgICAgICAgICAgICAgIH0pOwogICAgICAgICAgICAgICAgICBfdGhpczUudHJhbnNmb3JtU3RhdHVzKHJvdyk7CiAgICAgICAgICAgICAgICB9IGVsc2UgaWYgKHJvdy5tc2dfdHlwZSA9PT0gJzE2MScpIHsKICAgICAgICAgICAgICAgICAgLy8g5qOA5p+l566h55CGLemXrumimOeUs+iviQogICAgICAgICAgICAgICAgICBfdGhpczUucm91dGVKdW1wKHsKICAgICAgICAgICAgICAgICAgICBuYW1lOiAn5qOA5p+l566h55CGJywKICAgICAgICAgICAgICAgICAgICBhdHRyOiAnMScKICAgICAgICAgICAgICAgICAgfSwgewogICAgICAgICAgICAgICAgICAgIG5hbWU6ICfpl67popjljZXnoa7orqQnLAogICAgICAgICAgICAgICAgICAgIGF0dHI6ICcyJwogICAgICAgICAgICAgICAgICB9KTsKICAgICAgICAgICAgICAgICAgX3RoaXM1LnRyYW5zZm9ybVN0YXR1cyhyb3cpOwogICAgICAgICAgICAgICAgfSBlbHNlIGlmIChyb3cubXNnX3R5cGUgPT09ICcyODEnKSB7CiAgICAgICAgICAgICAgICAgIC8vIOiHquWumuS5ieaooeWdly3pl67ljbfosIPmn6UKICAgICAgICAgICAgICAgICAgX3RoaXM1LnJvdXRlSnVtcCh7CiAgICAgICAgICAgICAgICAgICAgbmFtZTogJ+mXruWNt+euoeeQhicsCiAgICAgICAgICAgICAgICAgICAgYXR0cjogJzEnCiAgICAgICAgICAgICAgICAgIH0sIHsKICAgICAgICAgICAgICAgICAgICBuYW1lOiAn6Zeu5Y236LCD5p+l6YWN572uJywKICAgICAgICAgICAgICAgICAgICBhdHRyOiAnMicKICAgICAgICAgICAgICAgICAgfSk7CiAgICAgICAgICAgICAgICAgIF90aGlzNS50cmFuc2Zvcm1TdGF0dXMocm93KTsKICAgICAgICAgICAgICAgIH0gZWxzZSBpZiAocm93Lm1zZ190eXBlID09PSAnMDFvJykgewogICAgICAgICAgICAgICAgICAvLyDoh6rlrprkuYnmqKHlnZct6Zeu5Y236LCD5p+lCiAgICAgICAgICAgICAgICAgIF90aGlzNS5kaWFsb2dEYXRlLnZpc2libGUgPSB0cnVlOwogICAgICAgICAgICAgICAgICAvLyB0aGlzLmRpYWxvZ0RhdGUubm90ZUxpc3RbMF0gPSByb3cubXNnX3BhcmFtZXRlcgogICAgICAgICAgICAgICAgICBsYXN0X21vZGlfZGF0ZSA9IHJvdy5sYXN0X21vZGlfZGF0ZTsKICAgICAgICAgICAgICAgICAgX3JvdyRtc2dfcGFyYW1ldGVyID0gcm93Lm1zZ19wYXJhbWV0ZXIsIG5vdGVfaW5mbyA9IF9yb3ckbXNnX3BhcmFtZXRlci5ub3RlX2luZm8sIG5vdGVfbmFtZSA9IF9yb3ckbXNnX3BhcmFtZXRlci5ub3RlX25hbWUsIHdhcm5fdGltZSA9IF9yb3ckbXNnX3BhcmFtZXRlci53YXJuX3RpbWUsIHdhcm5fbm90ZSA9IF9yb3ckbXNnX3BhcmFtZXRlci53YXJuX25vdGUsIHdhcm5fcGhvbmUgPSBfcm93JG1zZ19wYXJhbWV0ZXIud2Fybl9waG9uZTsKICAgICAgICAgICAgICAgICAgbm90ZSA9IHsKICAgICAgICAgICAgICAgICAgICBsYXN0X21vZGlfZGF0ZTogbGFzdF9tb2RpX2RhdGUsCiAgICAgICAgICAgICAgICAgICAgbm90ZV9pbmZvOiBub3RlX2luZm8sCiAgICAgICAgICAgICAgICAgICAgbm90ZV9uYW1lOiBub3RlX25hbWUsCiAgICAgICAgICAgICAgICAgICAgd2Fybl90aW1lOiB3YXJuX3RpbWUsCiAgICAgICAgICAgICAgICAgICAgd2Fybl9ub3RlOiB3YXJuX25vdGUsCiAgICAgICAgICAgICAgICAgICAgd2Fybl9waG9uZTogd2Fybl9waG9uZQogICAgICAgICAgICAgICAgICB9OwogICAgICAgICAgICAgICAgICBfdGhpczUuZGlhbG9nRGF0ZS5ub3RlTGlzdFswXSA9IG5vdGU7CiAgICAgICAgICAgICAgICAgIF90aGlzNS50cmFuc2Zvcm1TdGF0dXMocm93KTsKICAgICAgICAgICAgICAgIH0gZWxzZSBpZiAocm93Lm1zZ190eXBlID09PSAnMDFtJyB8fCByb3cubXNnX3R5cGUgPT09ICcwMWwnIHx8IHJvdy5tc2dfdHlwZSA9PT0gJzAxZicgfHwgcm93Lm1zZ190eXBlID09PSAnMjcxJyB8fCByb3cubXNnX3R5cGUgPT09ICcyOTInIHx8IHJvdy5tc2dfdHlwZSA9PT0gJzAxMycgfHwgcm93Lm1zZ190eXBlID09PSAnMTUxJyB8fCByb3cubXNnX3R5cGUgPT09ICcxNTMnIHx8IHJvdy5tc2dfdHlwZSA9PT0gJzE1MicgfHwgcm93Lm1zZ190eXBlID09PSAnMTU0JyB8fCByb3cubXNnX3R5cGUgPT09ICcxNjcnIHx8IHJvdy5tc2dfdHlwZSA9PT0gJzE2NCcgfHwgcm93Lm1zZ190eXBlID09PSAnMTYyJyB8fCByb3cubXNnX3R5cGUgPT09ICcyNzInIHx8IHJvdy5tc2dfdHlwZSA9PT0gJzE2MCcgfHwgcm93Lm1zZ190eXBlID09PSAnMDE2JykgewogICAgICAgICAgICAgICAgICAvLyDosIPnlKjln7rnur/lvLnnqpcKICAgICAgICAgICAgICAgICAgX3RoaXM1LmdlbmVyYWxPcGVuKHJvdyk7CiAgICAgICAgICAgICAgICB9CiAgICAgICAgICAgICAgY2FzZSAxMTU6CiAgICAgICAgICAgICAgY2FzZSAiZW5kIjoKICAgICAgICAgICAgICAgIHJldHVybiBfY29udGV4dC5zdG9wKCk7CiAgICAgICAgICAgIH0KICAgICAgICAgIH0KICAgICAgICB9LCBfY2FsbGVlKTsKICAgICAgfSkpKCk7CiAgICB9LAogICAgZ2VuZXJhbE9wZW46IGZ1bmN0aW9uIGdlbmVyYWxPcGVuKHJvdykgewogICAgICB2YXIgX3RoaXM2ID0gdGhpczsKICAgICAgdGhpcy5nZW5lcmFsQ29uZmlnLmRhdGEgPSByb3c7CiAgICAgIHRoaXMuZ2VuZXJhbENvbmZpZy5jb21wb25lbnRQcm9wcy50aXRsZSA9IHJvdy5tc2dfY29udGVudDsgLy8g5qih5p2/5qCH6aKY5ZKM5by556qX5qCH6aKYCiAgICAgIHRoaXMuZ2VuZXJhbENvbmZpZy51cmxUeXBlID0gJ3N0YXRpYyc7CiAgICAgIHRoaXMuZ2VuZXJhbENvbmZpZy5oYW5kbGVTdGF0ZSA9IGZhbHNlOwogICAgICBpZiAocm93Lm1zZ190eXBlID09PSAnMTUyJykgewogICAgICAgIC8vIOiAg+ivhOehruiupOS7u+WKoQogICAgICAgIHRoaXMuZ2VuZXJhbENvbmZpZy5jaGlsZEZsb3dVcmwgPSAnc3RhdGljL2h0bWwvYXNzZXNzL2RpYWxvZy90YXNrQ29uZmlybURpYWxvZy5odG1sJzsKICAgICAgfSBlbHNlIGlmIChyb3cubXNnX3R5cGUgPT09ICcxNTEnKSB7CiAgICAgICAgLy8g6ICD5qC456Gu6K6k5Lu75YqhCiAgICAgICAgdGhpcy5nZW5lcmFsQ29uZmlnLmNoaWxkRmxvd1VybCA9ICdzdGF0aWMvaHRtbC9hc3Nlc3MvZGlhbG9nL3Rhc2tDb25maXJtRGlhbG9nLmh0bWwnOwogICAgICB9IGVsc2UgaWYgKHJvdy5tc2dfdHlwZSA9PT0gJzE1MycpIHsKICAgICAgICAvLyDogIPmoLjnoa7orqTlrqHmoLjku7vliqEKICAgICAgICB0aGlzLmdlbmVyYWxDb25maWcuY2hpbGRGbG93VXJsID0gJ3N0YXRpYy9odG1sL2Fzc2Vzcy9kaWFsb2cvdGFza0F1ZGl0RGlhbG9nLmh0bWwnOwogICAgICB9IGVsc2UgaWYgKHJvdy5tc2dfdHlwZSA9PT0gJzE1NCcpIHsKICAgICAgICAvLyDogIPor4Tnoa7orqTlrqHmoLjku7vliqEKICAgICAgICB0aGlzLmdlbmVyYWxDb25maWcuY2hpbGRGbG93VXJsID0gJ3N0YXRpYy9odG1sL2Fzc2Vzcy9kaWFsb2cvdGFza0F1ZGl0RGlhbG9nLmh0bWwnOwogICAgICB9IGVsc2UgaWYgKHJvdy5tc2dfdHlwZSA9PT0gJzI5MicpIHsKICAgICAgICAvLyDln7norq3pobnnm67pgJrnn6UKICAgICAgICB0aGlzLmdlbmVyYWxDb25maWcuY2hpbGRGbG93VXJsID0gJ3N0YXRpYy9odG1sL3RyYWluL3RyYWluTWVzc2FnZS9kaWFsb2dGb3JtVHJhaW4uaHRtbCc7CiAgICAgICAgdGhpcy5nZW5lcmFsQ29uZmlnLmhhbmRsZVN0YXRlID0gdHJ1ZTsKICAgICAgfSBlbHNlIGlmIChyb3cubXNnX3R5cGUgPT09ICcyNzEnKSB7CiAgICAgICAgLy8g55+l6K+G5a2m5LmgCiAgICAgICAgdGhpcy5nZW5lcmFsQ29uZmlnLmNoaWxkRmxvd1VybCA9ICdzdGF0aWMvaHRtbC9rbm93bGVkZ2Uva25vd2xlZGdlTGVhcm4uaHRtbCc7CiAgICAgIH0gZWxzZSBpZiAocm93Lm1zZ190eXBlID09PSAnOTk5JykgewogICAgICAgIC8vIOa2iOaBr+aOqOmAgQogICAgICAgIHRoaXMuZ2VuZXJhbENvbmZpZy5jaGlsZEZsb3dVcmwgPSAnbW9kdWxlT3BlckZ1bic7CiAgICAgIH0gZWxzZSBpZiAocm93Lm1zZ190eXBlID09PSAnMDE2JykgewogICAgICAgIC8vIOaVsOaNruS4iuaKpQogICAgICAgIHRoaXMuZ2VuZXJhbENvbmZpZy5jaGlsZEZsb3dVcmwgPSAnc3RhdGljL2h0bWwvc3lzdGVtL2RhdGFNYW5hZ2VyL2RhdGFSZXBvcnREaWcuaHRtbCc7CiAgICAgICAgdGhpcy5nZW5lcmFsQ29uZmlnLmNvbXBvbmVudFByb3BzLnRpdGxlID0gJ+aVsOaNruS/oeaBr+S4iuaKpSc7CiAgICAgICAgdGhpcy5nZW5lcmFsQ29uZmlnLmhhbmRsZVN0YXRlID0gdHJ1ZTsKICAgICAgfSBlbHNlIGlmIChyb3cubXNnX3R5cGUgPT09ICcwMTMnKSB7CiAgICAgICAgLy8g6LWE5paZ5YWx5LqrCiAgICAgICAgdGhpcy5nZW5lcmFsQ29uZmlnLmNoaWxkRmxvd1VybCA9ICdzdGF0aWMvaHRtbC9jb21tb24vZGlhbG9nL2RpYWxvZ05vdGljZURldGFpbHMuaHRtbCc7CiAgICAgICAgdGhpcy5nZW5lcmFsQ29uZmlnLmhhbmRsZVN0YXRlID0gdHJ1ZTsKICAgICAgfSBlbHNlIGlmIChyb3cubXNnX3R5cGUgPT09ICcxNjAnKSB7CiAgICAgICAgLy8g5qOA5p+l566h55CGLeS7u+WKoeaJp+ihjOmAmuefpQogICAgICAgIHZhciBtc2cgPSB7CiAgICAgICAgICBwYXJhbWV0ZXJMaXN0OiBbXSwKICAgICAgICAgIHN5c01hcDogewogICAgICAgICAgICBwYXJhbV9pdGVtOiAnVEFTS19DSEVDS19TRU5EJwogICAgICAgICAgfQogICAgICAgIH07CiAgICAgICAgZ2V0U3lzUGFyYW0obXNnKS50aGVuKGZ1bmN0aW9uIChyZXMpIHsKICAgICAgICAgIHZhciByZXRNYXAgPSByZXMucmV0TWFwOwogICAgICAgICAgX3RoaXM2LmdlbmVyYWxDb25maWcuY2hpbGRGbG93VXJsID0gcmV0TWFwLnN5c1BhcmFtOwogICAgICAgICAgX3RoaXM2LmdlbmVyYWxDb25maWcudXJsVHlwZSA9ICdhY3RpdmUnOwogICAgICAgICAgX3RoaXM2LmdlbmVyYWxDb25maWcuaGFuZGxlU3RhdGUgPSB0cnVlOwogICAgICAgIH0pOwogICAgICB9IGVsc2UgaWYgKHJvdy5tc2dfdHlwZSA9PT0gJzE2NCcpIHsKICAgICAgICAvLyDmo4Dmn6XnrqHnkIYt5pW05pS55a6h5om55LiL5Y+RCiAgICAgICAgdGhpcy5nZW5lcmFsQ29uZmlnLmNoaWxkRmxvd1VybCA9ICdzdGF0aWMvanMvYWN0aW9uL3Rhc2svZGlhbG9nL3Rhc2tDb21tb25NaXhJbi5qcyc7CiAgICAgIH0gZWxzZSBpZiAocm93Lm1zZ190eXBlID09PSAnMTY3JykgewogICAgICAgIC8vIOajgOafpeeuoeeQhi3lrqHliY3osIPmn6XpgJrnn6XkuabkuIvlj5HpgJrnn6UKICAgICAgICB0aGlzLmdlbmVyYWxDb25maWcuY2hpbGRGbG93VXJsID0gJ3N0YXRpYy9odG1sL3Rhc2svZGlhbG9nL2RpYWxvZ1Rhc2tNZXNzYWdlLmh0bWwnOwogICAgICB9IGVsc2UgaWYgKHJvdy5tc2dfdHlwZSA9PT0gJzE2MicpIHsKICAgICAgICAvLyDmo4Dmn6XnrqHnkIYt5pW05pS55LiL5Y+RCiAgICAgICAgdGhpcy5nZW5lcmFsQ29uZmlnLmNoaWxkRmxvd1VybCA9ICdzaG93UHViQ2hhbmdlUHJvYmxlbSc7CiAgICAgIH0KICAgICAgLy8gIGVsc2UgaWYgKHJvdy5tc2dfdHlwZSA9PT0gJzI4MScpIHsgLy8g6Ieq5a6a5LmJ5qih5Z2XLemXruWNt+iwg+afpQogICAgICAvLyAgIHRoaXMucm91dGVKdW1wKAogICAgICAvLyAgICAgeyBuYW1lOiAn6Zeu5Y23566h55CGJywgYXR0cjogJzEnIH0sCiAgICAgIC8vICAgICB7IG5hbWU6ICfpl67ljbfosIPmn6XphY3nva4nLCBhdHRyOiAnMicgfQogICAgICAvLyAgICkKICAgICAgLy8gICB0aGlzLmdlbmVyYWxDb25maWcuY2hpbGRGbG93VXJsID0gJ29wZW5RdWVRdWVzdFBhZ2UnCiAgICAgIC8vIH0KICAgICAgdGhpcy5nZW5lcmFsQ29uZmlnLnZpc2libGUgPSB0cnVlOwogICAgICB0aGlzLm1zZ1RvdGFsTnVtKCk7IC8vIOWIt+aWsOWPs+S4i+inkuezu+e7n+a2iOaBrwogICAgfSwKICAgIC8qKg0KICAgICAqIOWFrOWFseaWueazle+8muiOt+WPluWvueW6lOiPnOWNlWlk77yM5bm26L+b6KGM6Lev55Sx6Lez6L2sDQogICAgICogQHBhcmFtIHBhcmVudFI654i257qn6I+c5Y2V5a+56LGhIG5hbWXvvJroj5zljZXlkI3np7DvvJthdHRy77ya6I+c5Y2V57G75Z6LDQogICAgICogQHBhcmFtIGNoaWxkUjrlrZDnuqfoj5zljZXlr7nosaENCiAgICAgKiAqLwogICAgcm91dGVKdW1wOiBmdW5jdGlvbiByb3V0ZUp1bXAocGFyZW50UiwgY2hpbGRSKSB7CiAgICAgIHZhciByb3V0ZXIgPSB0aGlzLiRzdG9yZS5nZXR0ZXJzLnBlcm1pc3Npb25fcm91dGVzOwogICAgICB2YXIgbWhNZW51SWQgPSB0aGlzLiRzdG9yZS5nZXR0ZXJzLm1lbnVBcnIuZmluZChmdW5jdGlvbiAoaXRlbSkgewogICAgICAgIHJldHVybiBpdGVtLm1lbnVfbmFtZS5pbmRleE9mKHBhcmVudFIubmFtZSkgPiAtMSAmJiBpdGVtLm1lbnVfYXR0ciA9PT0gcGFyZW50Ui5hdHRyOwogICAgICB9KTsKICAgICAgdmFyIGdnTWVudUlkID0gdGhpcy4kc3RvcmUuZ2V0dGVycy5tZW51QXJyLmZpbmQoZnVuY3Rpb24gKGl0ZW0pIHsKICAgICAgICByZXR1cm4gaXRlbS5tZW51X25hbWUuaW5kZXhPZihjaGlsZFIubmFtZSkgPiAtMSAmJiBpdGVtLm1lbnVfYXR0ciA9PT0gY2hpbGRSLmF0dHI7CiAgICAgIH0pOwogICAgICBpZiAoY29tbW9uQmxhbmsobWhNZW51SWQpIHx8IGNvbW1vbkJsYW5rKGdnTWVudUlkKSkgewogICAgICAgIGNvbW1vbk1zZ1dhcm4oJ+aXoOebruagh+iPnOWNleadg+mZkCcsIHRoaXMpOwogICAgICAgIHJldHVybjsKICAgICAgfQogICAgICB2YXIgdW5pZnlSID0gcm91dGVyLmZpbmQoZnVuY3Rpb24gKGl0ZW0pIHsKICAgICAgICByZXR1cm4gaXRlbS5tZW51X2lkID09PSBtaE1lbnVJZC5tZW51X2lkOwogICAgICB9KTsKICAgICAgdmFyIG5vdGljZVIgPSB1bmlmeVIuY2hpbGRyZW4uZmluZChmdW5jdGlvbiAoaXRlbSkgewogICAgICAgIHJldHVybiBpdGVtLm1lbnVfaWQgPT09IGdnTWVudUlkLm1lbnVfaWQ7CiAgICAgIH0pOwogICAgICB0aGlzLiRyb3V0ZXIucHVzaCh1bmlmeVIucGF0aCArICcvJyArIG5vdGljZVIucGF0aCk7CiAgICB9LAogICAgLyoqDQogICAgICog5YWs5YWx5pa55rOV77ya5pyq5aSE55CGLT7lt7LlpITnkIYNCiAgICAgKiBAcGFyYW0gcm93IOW9k+WJjeWkhOeQhuihjA0KICAgICAqICovCiAgICB0cmFuc2Zvcm1TdGF0dXM6IGZ1bmN0aW9uIHRyYW5zZm9ybVN0YXR1cyhyb3cpIHsKICAgICAgdmFyIF90aGlzNyA9IHRoaXM7CiAgICAgIHZhciBtc2cgPSB7CiAgICAgICAgcGFyYW1ldGVyTGlzdDogW3t9XSwKICAgICAgICBtc2dfbm86IHJvdy5tc2dfbm8sCiAgICAgICAgbXNnX3R5cGU6IHJvdy5tc2dfdHlwZQogICAgICB9OwogICAgICB1cGRhdGVTeXNNZXNzYWdlKG1zZykudGhlbihmdW5jdGlvbiAocmVzcG9uc2UpIHsKICAgICAgICAvLyBjb21tb25Nc2dTdWNjZXNzKCflpITnkIbmiJDlip8nLCB0aGlzKQogICAgICAgIF90aGlzNy5tc2dUb3RhbE51bSgpOwogICAgICB9KTsKICAgIH0sCiAgICAvKioNCiAgICAgKiDlhazlkYrkv6Hmga86IOabtOaUuemYheivu+eKtuaAgQ0KICAgICAqIEBwYXJhbSBub3RpY2VfaWQ65YWs5ZGKaWQNCiAgICAgKiBAcGFyYW0gbXNnX25vOuezu+e7n+a2iOaBr+e8luWPtw0KICAgICAqIEBwYXJhbSBwYXJhbUpzb27vvJrns7vnu5/mtojmga/lj4LmlbANCiAgICAgKiAqLwogICAgZmlyc3RQYWdlOiBmdW5jdGlvbiBmaXJzdFBhZ2Uobm90aWNlX2lkLCBtc2dfbm8sIHBhcmFtSnNvbikgewogICAgICB2YXIgX3RoaXM4ID0gdGhpczsKICAgICAgdmFyIHJlYWRfdGltZSA9IGRhdGVOb3dGb3JtYXQxMCgpOyAvLyDlvZPliY3ml7bpl7TnmoTljYHkvY3mlbDmoLzlvI8KICAgICAgdmFyIG1zZyA9IHsKICAgICAgICBub3RpY2VfaWQ6IG5vdGljZV9pZCwKICAgICAgICB1c2VyX25vOiB0aGlzLiRzdG9yZS5nZXR0ZXJzLnVzZXJObywKICAgICAgICBvcmdhbl9ubzogdGhpcy4kc3RvcmUuZ2V0dGVycy5vcmdhbk5vLAogICAgICAgIHJlYWRfdGltZTogcmVhZF90aW1lLAogICAgICAgIG1zZ19ubzogbXNnX25vLAogICAgICAgIHBhcnBhbURhdGE6IHBhcmFtSnNvbgogICAgICB9OwogICAgICBub3RpY2VNb2RpZnkobXNnKS50aGVuKGZ1bmN0aW9uIChyZXNwb25zZSkgewogICAgICAgIF90aGlzOC5tc2dUb3RhbE51bSgpOwogICAgICAgIF90aGlzOC4kbmV4dFRpY2soZnVuY3Rpb24gKCkgewogICAgICAgICAgX3RoaXM4LiRidXMuJGVtaXQoJ25vdGljZVVwZGF0ZScsIHRydWUpOwogICAgICAgIH0pOwogICAgICB9KTsKICAgIH0sCiAgICAvKioNCiAgICAgKiBidG4gLSDpgJrov4cNCiAgICAgKiBAcGFyYW0gcm93IOW9k+WJjeihjOWFrOWRiua1geeoi+aVsOaNriovCiAgICBoYW5kbGVQYXNzOiBmdW5jdGlvbiBoYW5kbGVQYXNzKCkgewogICAgICB2YXIgX3RoaXM5ID0gdGhpczsKICAgICAgY29tbW9uTXNnQ29uZmlybSgn56Gu6K6k6YCa6L+H5a6h5om577yfJywgdGhpcywgZnVuY3Rpb24gKHBhcmFtKSB7CiAgICAgICAgaWYgKHBhcmFtKSB7CiAgICAgICAgICB2YXIgcm93ID0gX3RoaXM5LmN1clJvdzsKICAgICAgICAgIHZhciBqc29uID0gewogICAgICAgICAgICBub3RpY2VfaWQ6IHJvdy5ub3RpY2VfaWQsCiAgICAgICAgICAgIG5vdGljZV90aXRsZTogcm93Lm5vdGljZV90aXRsZSwKICAgICAgICAgICAgbm90aWNlX2NvbnRlbnQ6IHJvdy5ub3RpY2VfY29udGVudCwKICAgICAgICAgICAgbXNnX3R5cGU6IHJvdy5tc2dfdHlwZSwKICAgICAgICAgICAgbm90aWNlX2xldmVsOiByb3cubm90aWNlX2xldmVsLAogICAgICAgICAgICBvcmdhbnM6IHJvdy5vcmdhbnMsCiAgICAgICAgICAgIHJvbGVzOiByb3cucm9sZXMsCiAgICAgICAgICAgIHVzZXJzOiByb3cudXNlcnMsCiAgICAgICAgICAgIGZpbGVfdXJsOiByb3cuZmlsZV91cmwsCiAgICAgICAgICAgIHB1Ymxpc2hfb3JnYW46IHJvdy5wdWJsaXNoX29yZ2FuLAogICAgICAgICAgICBwdWJsaXNoX3VzZXI6IHJvdy5wdWJsaXNoX3VzZXIsCiAgICAgICAgICAgIHB1Ymxpc2hfdGltZTogcm93LnB1Ymxpc2hfdGltZSwKICAgICAgICAgICAgaXRlbV9pZDogcm93Lml0ZW1faWQsCiAgICAgICAgICAgIGluc3RfaWQ6IHJvdy5pbnN0X2lkCiAgICAgICAgICB9OwogICAgICAgICAgdmFyIG1zZyA9IHsKICAgICAgICAgICAgcGFyYW1ldGVyTGlzdDogW2pzb25dLAogICAgICAgICAgICB1c2VyX25vOiBfdGhpczkuJHN0b3JlLmdldHRlcnMudXNlck5vLAogICAgICAgICAgICByZW1hcms6ICcnLAogICAgICAgICAgICB0eXBlOiAnMScsCiAgICAgICAgICAgIC8vIOmAmui/h+eahOagh+ivhgogICAgICAgICAgICBvcGVyX3R5cGU6IGRpY3Rpb25hcnlHZXQoJ09QRVJBVEVfT1RIRVInKQogICAgICAgICAgfTsKICAgICAgICAgIEFwcHJvdmUobXNnKS50aGVuKGZ1bmN0aW9uIChyZXNwb25zZSkgewogICAgICAgICAgICBjb21tb25Nc2dTdWNjZXNzKHJlc3BvbnNlLnJldE1zZywgX3RoaXM5KTsKICAgICAgICAgICAgX3RoaXM5Lm1zZ1RvdGFsTnVtKCk7CiAgICAgICAgICAgIF90aGlzOS4kYnVzLiRlbWl0KCdub3RpY2VBcHJvdmVVcGRhdGUnLCB0cnVlKTsKICAgICAgICAgICAgX3RoaXM5LmNoYW5nZVZpc2libGUoZmFsc2UpOyAvLyDlhazlkYrlrqHmibnlvLnmoYblhbPpl60KICAgICAgICAgIH0pOwogICAgICAgIH0KICAgICAgfSk7CiAgICB9LAogICAgLyoqDQogICAgICogYnRuIC0g5ouS57udKi8KICAgIGhhbmRsZVJlZnVzZTogZnVuY3Rpb24gaGFuZGxlUmVmdXNlKCkgewogICAgICB0aGlzLmNoYW5nZVZpc2libGVGb3JtKHRydWUpOwogICAgfSwKICAgIC8qKg0KICAgICAqIGJ0biAtIOaLkue7nQ0KICAgICAqIEBwYXJhbSByb3cg5b2T5YmN6KGM5YWs5ZGK5rWB56iL5pWw5o2uDQogICAgICogKi8KICAgIGRpYWxvZ1JlZnVzZVN1Ym1pdDogZnVuY3Rpb24gZGlhbG9nUmVmdXNlU3VibWl0KCkgewogICAgICB2YXIgX3RoaXMxMCA9IHRoaXM7CiAgICAgIC8vIOWPkemAgea1geeoi+e7iOatouivt+axggogICAgICBjb21tb25Nc2dDb25maXJtKCfnoa7orqTmi5Lnu53lrqHmibnvvJ8nLCB0aGlzLCBmdW5jdGlvbiAoZmxhZykgewogICAgICAgIGlmIChmbGFnKSB7CiAgICAgICAgICB2YXIgcm93ID0gX3RoaXMxMC5jdXJSb3c7CiAgICAgICAgICB2YXIganNvbiA9IHsKICAgICAgICAgICAgbm90aWNlX2lkOiByb3cubm90aWNlX2lkLAogICAgICAgICAgICBub3RpY2VfdGl0bGU6IHJvdy5ub3RpY2VfdGl0bGUsCiAgICAgICAgICAgIG5vdGljZV9jb250ZW50OiByb3cubm90aWNlX2NvbnRlbnQsCiAgICAgICAgICAgIG1zZ190eXBlOiByb3cubXNnX3R5cGUsCiAgICAgICAgICAgIG5vdGljZV9sZXZlbDogcm93Lm5vdGljZV9sZXZlbCwKICAgICAgICAgICAgb3JnYW5zOiByb3cub3JnYW5zLAogICAgICAgICAgICByb2xlczogcm93LnJvbGVzLAogICAgICAgICAgICB1c2Vyczogcm93LnVzZXJzLAogICAgICAgICAgICBmaWxlX3VybDogcm93LmZpbGVfdXJsLAogICAgICAgICAgICBwdWJsaXNoX29yZ2FuOiByb3cucHVibGlzaF9vcmdhbiwKICAgICAgICAgICAgcHVibGlzaF91c2VyOiByb3cucHVibGlzaF91c2VyLAogICAgICAgICAgICBwdWJsaXNoX3RpbWU6IHJvdy5wdWJsaXNoX3RpbWUsCiAgICAgICAgICAgIGl0ZW1faWQ6IHJvdy5pdGVtX2lkLAogICAgICAgICAgICBpbnN0X2lkOiByb3cuaW5zdF9pZAogICAgICAgICAgfTsKICAgICAgICAgIHZhciBtc2cgPSB7CiAgICAgICAgICAgIHBhcmFtZXRlckxpc3Q6IFtqc29uXSwKICAgICAgICAgICAgdXNlcl9ubzogX3RoaXMxMC4kc3RvcmUuZ2V0dGVycy51c2VyTm8sCiAgICAgICAgICAgIHJlbWFyazogX3RoaXMxMC5kaWFsb2dGb3JtLmZvcm0uZGVmYXVsdEZvcm0ucmVtYXJrLAogICAgICAgICAgICAvLyDooajljZXmlbDmja7vvJrmi5Lnu53or7TmmI4KICAgICAgICAgICAgdHlwZTogJzAnLAogICAgICAgICAgICAvLyDmi5Lnu53nmoTmoIfor4YKICAgICAgICAgICAgb3Blcl90eXBlOiBkaWN0aW9uYXJ5R2V0KCdPUEVSQVRFX09USEVSJykKICAgICAgICAgIH07CiAgICAgICAgICBBcHByb3ZlKG1zZykudGhlbihmdW5jdGlvbiAocmVzcG9uc2UpIHsKICAgICAgICAgICAgY29tbW9uTXNnU3VjY2VzcyhyZXNwb25zZS5yZXRNc2csIF90aGlzMTApOwogICAgICAgICAgICBfdGhpczEwLm1zZ1RvdGFsTnVtKCk7CiAgICAgICAgICAgIF90aGlzMTAuJGJ1cy4kZW1pdCgnbm90aWNlQXByb3ZlVXBkYXRlJywgdHJ1ZSk7CiAgICAgICAgICAgIF90aGlzMTAuY2hhbmdlVmlzaWJsZUZvcm0oZmFsc2UpOyAvLyDmi5Lnu53or7TmmI7lvLnmoYblhbPpl60KICAgICAgICAgICAgX3RoaXMxMC5jaGFuZ2VWaXNpYmxlKGZhbHNlKTsgLy8g5YWs5ZGK5a6h5om55by55qGG5YWz6ZetCiAgICAgICAgICB9KTsKICAgICAgICB9CiAgICAgIH0pOwogICAgfSwKICAgIC8qKg0KICAgICAqIOa1geeoi+aooeadv+iwg+eUqOaWueazlQ0KICAgICAqLwogICAgbW91ZGxlT3BlbjogZnVuY3Rpb24gbW91ZGxlT3Blbihyb3cpIHsKICAgICAgdGhpcy5mbG93Q29uZmlnLnZpc2libGUgPSB0cnVlOwogICAgICB0aGlzLmZsb3dDb25maWcuY29tcG9uZW50UHJvcHMudGl0bGUgPSByb3cubXNnX2NvbnRlbnQ7IC8vIOaooeadv+agh+mimOWSjOW8ueeql+agh+mimAogICAgICB0aGlzLmZsb3dDb25maWcubW9kdWxlX2lkID0gcm93Lm1zZ19wYXJhbWV0ZXIubW9kdWxlX2lkOyAvLyDmqKHmnb9JRAogICAgICB0aGlzLmZsb3dDb25maWcudGFza19pZCA9IHJvdy5tc2dfcGFyYW1ldGVyLml0ZW1faWQ7IC8vIOa1geeoi0lECiAgICB9LAogICAgLy8g5rWB56iL5by556qX5YWz6ZetCiAgICBkaWFsb2dGbG93Q2xvc2U6IGZ1bmN0aW9uIGRpYWxvZ0Zsb3dDbG9zZShwYXJhbSkgewogICAgICB2YXIgX3RoaXMxMSA9IHRoaXM7CiAgICAgIC8vIOmcgOimgemHiuaUvuS7u+WKoQogICAgICBpZiAocGFyYW0pIHsKICAgICAgICB2YXIgbWFwRnJhbWUgPSB0aGlzLiRyZWZzWydmbG93RGlhbG9nJ10uJHJlZnNbcGFyYW1dOwogICAgICAgIHZhciBpZnJhbWVXaW4gPSBtYXBGcmFtZS5jb250ZW50V2luZG93OwogICAgICAgIGlmcmFtZVdpbi5wb3N0TWVzc2FnZSh7CiAgICAgICAgICB0eXBlOiAnZG9wVW5pZnknLAogICAgICAgICAgcHJvamVjdE5hbWU6ICdyZXNldEZsb3cnLAogICAgICAgICAgdGFza19pZDogdGhpcy5mbG93Q29uZmlnLnRhc2tfaWQKICAgICAgICB9LCBwcm9jZXNzLmVudi5OT0RFX0VOViA9PT0gJ2RldmVsb3BtZW50JyA/ICcqJyA6IHdpbmRvdy5sb2NhdGlvbi5vcmlnaW4pOwogICAgICB9CiAgICAgIHRpbW91dDEgPSBzZXRUaW1lb3V0KGZ1bmN0aW9uICgpIHsKICAgICAgICBfdGhpczExLiRzdG9yZS5zdGF0ZS5jb21tb24uZmxvdy5tb2R1bGVfaWQgPSAnJzsgLy8g572u56m65rWB56iLaWQKICAgICAgICBfdGhpczExLiRzdG9yZS5zdGF0ZS5jb21tb24uZmxvdy53b3JrZmxvd19kYXRhU3RyLmZsb3dfdHlwZSA9ICdhZGQnOyAvLyDph43mtYHnqIvnsbvlnovkuLrmlrDlop4KICAgICAgICBfdGhpczExLmZsb3dDb25maWcudmlzaWJsZSA9IGZhbHNlOwogICAgICB9LCAzMDApOwogICAgfSwKICAgIC8vIOWfuue6v+mAmueUqOW8ueeql+WFs+mXrQogICAgZGlhbG9nR2VuZXJhbENsb3NlOiBmdW5jdGlvbiBkaWFsb2dHZW5lcmFsQ2xvc2UocGFyYW0pIHsKICAgICAgLy8gaWYgKHBhcmFtKSB7CiAgICAgIC8vICAgY29uc3QgbWFwRnJhbWUgPSB0aGlzLiRyZWZzWydnZW5lcmFsRGlhbG9nJ10uJHJlZnNbcGFyYW1dCiAgICAgIC8vICAgY29uc3QgaWZyYW1lV2luID0gbWFwRnJhbWUuY29udGVudFdpbmRvdwogICAgICAvLyAgIGlmcmFtZVdpbi5wb3N0TWVzc2FnZSgKICAgICAgLy8gICAgIHsKICAgICAgLy8gICAgICAgdHlwZTogJ2RvcFVuaWZ5JywKICAgICAgLy8gICAgICAgcHJvamVjdE5hbWU6ICdyZXNldEdlbmVyYWwnLAogICAgICAvLyAgICAgICB0YXNrX2lkOiB0aGlzLmZsb3dDb25maWcudGFza19pZAogICAgICAvLyAgICAgfSwKICAgICAgLy8gICAgICcqJwogICAgICAvLyAgICkKICAgICAgLy8gfQogICAgICB0aGlzLmdlbmVyYWxDb25maWcudmlzaWJsZSA9IGZhbHNlOwogICAgICBpZiAodGhpcy5nZW5lcmFsQ29uZmlnLmhhbmRsZVN0YXRlKSB7CiAgICAgICAgdmFyIHJvd0RhdGEgPSB0aGlzLmdlbmVyYWxDb25maWcuZGF0YTsKICAgICAgICB0aGlzLnRyYW5zZm9ybVN0YXR1cyhyb3dEYXRhKTsKICAgICAgfQogICAgfSwKICAgIGNoYW5nZUdlbmVyYWw6IGZ1bmN0aW9uIGNoYW5nZUdlbmVyYWwocGFyYW0pIHsKICAgICAgdGhpcy50cmFuc2Zvcm1TdGF0dXMocGFyYW0pOwogICAgfSwKICAgIC8qKg0KICAgICAqIOa1geeoi+S6i+mhuS3mk43kvZznlLPor7ct5pON5L2c6K+m5oOFIC0g5by556qX5YWz6ZetDQogICAgICogQHBhcmFtIHtCb29sZWFufSBwYXJhbSDlvLnlh7rmoYbmmL7npLrpmpDol4/phY3nva4qLwogICAgY2hhbmdlT3BlclZpc2libGU6IGZ1bmN0aW9uIGNoYW5nZU9wZXJWaXNpYmxlKHBhcmFtKSB7CiAgICAgIHRoaXMuZGlhbG9nT3Blci52aXNpYmxlID0gcGFyYW07CiAgICB9LAogICAgLyoqDQogICAgICog6aOO6Zmp5o6S5p+l572R54K55qC45p+l77yM5by55qGG6aG16Z2iDQogICAgICogQHBhcmFtIHtCb29sZWFufSBwYXJhbSDlvLnlh7rmoYbmmL7npLrpmpDol4/phY3nva4qLwogICAgY2hhbmdlUmlza1Zpc2libGU6IGZ1bmN0aW9uIGNoYW5nZVJpc2tWaXNpYmxlKHBhcmFtLCBmbGFnKSB7CiAgICAgIHZhciBfdGhpczEyID0gdGhpczsKICAgICAgdGhpcy5kaWFsb2dSaXNrLnZpc2libGUgPSBwYXJhbTsKICAgICAgaWYgKCFwYXJhbSkgewogICAgICAgIGlmICghZmxhZykgewogICAgICAgICAgdmFyIG1zZyA9IHsKICAgICAgICAgICAgcGFyYW1ldGVyTGlzdDogW3t9XSwKICAgICAgICAgICAgdGFza19ubzogdGhpcy5kaWFsb2dSaXNrLnJpc2tEaWFsb2dEYXRhLnRhc2tfbm9fbGlzdAogICAgICAgICAgfTsKICAgICAgICAgIHJlbGVhc2VUYXNrKG1zZykudGhlbihmdW5jdGlvbiAocmVzcG9uc2UpIHsKICAgICAgICAgICAgLy8KICAgICAgICAgIH0pLmNhdGNoKGZ1bmN0aW9uICgpIHsKICAgICAgICAgICAgY29tbW9uTXNnSW5mbygn5Lu75Yqh6YeK5pS+5aSx6LSl77yBJywgX3RoaXMxMik7CiAgICAgICAgICB9KTsKICAgICAgICB9CiAgICAgIH0KICAgIH0sCiAgICAvKioNCiAgICAgKiDpo47pmanmjpLmn6XnvZHngrnmoLjmn6XvvIzlvLnmoYbpobXpnaINCiAgICAgKiBAcGFyYW0ge0Jvb2xlYW59IHBhcmFtIOehruWumuaguOafpSovCiAgICBkaWFsb2dSaXNrU3VibWl0OiBmdW5jdGlvbiBkaWFsb2dSaXNrU3VibWl0KCkgewogICAgICB2YXIgX3RoaXMxMyA9IHRoaXM7CiAgICAgIHZhciByZXBvcnRQYXJhbXNfYWNjbm8gPSB0aGlzLmRpYWxvZ1Jpc2suZm9ybS5kZWZhdWx0Rm9ybS5yZXBvcnRQYXJhbXNfYWNjbm87IC8vIOWkhOe9rue7k+iuuuWAvAogICAgICB2YXIgbXNnID0gewogICAgICAgIC8vIOafpeivoui+k+WFpeWPguaVsOWIl+ihqAogICAgICAgIHBhcmFtZXRlckxpc3Q6IFt7fV0sCiAgICAgICAgLy8g57O757uf5Y+C5pWw5YiX6KGoCiAgICAgICAgY2JiQ2hlY2tEZWFsUmVzdWx0OiByZXBvcnRQYXJhbXNfYWNjbm8sCiAgICAgICAgLy8g5aSE572u57uT6K665YC8CiAgICAgICAgdGFza05vOiB0aGlzLmRpYWxvZ1Jpc2sucmlza0RpYWxvZ0RhdGEudGFza19ub19saXN0LAogICAgICAgIHVzZXJfY29tbWVudDogY29tbW9uRm9ybWF0U3Vic3lzdGVtVmFsdWUodGhpcy5kaWFsb2dSaXNrLmZvcm0uZGVmYXVsdEZvcm0ucmVwb3J0UGFyYW1zX2FjY25vLCAnQU1fUklTS19DSEVDS19DQkJfQVVESVRfREVBTF9SRVNVTFQnLCAnQU9TJykKICAgICAgfTsKICAgICAgaWYgKHJlcG9ydFBhcmFtc19hY2NubyA9PT0gJzMnKSB7CiAgICAgICAgbXNnLmNiYkF1ZGl0Q29udHJvbCA9IHRoaXMuZGlhbG9nUmlzay5mb3JtLmRlZmF1bHRGb3JtLnJlcG9ydFBhcmFtc19iZWdpbmRhdGU7CiAgICAgIH0KICAgICAgY2hlY2tUYXNrKG1zZykudGhlbihmdW5jdGlvbiAocmVzcG9uc2UpIHsKICAgICAgICBjb21tb25Nc2dTdWNjZXNzKCfmoLjmn6XmtYHnqIvmj5DkuqTmiJDlip/vvIEnLCBfdGhpczEzKTsKICAgICAgfSkuY2F0Y2goZnVuY3Rpb24gKCkgewogICAgICAgIGNvbW1vbk1zZ0luZm8oJ+aguOafpea1geeoi+aPkOS6pOWksei0pe+8gScsIF90aGlzMTMpOwogICAgICB9KTsKICAgICAgdGhpcy5jaGFuZ2VSaXNrVmlzaWJsZShmYWxzZSwgdHJ1ZSk7CiAgICB9LAogICAgLyoqDQogICAgICog6aOO6Zmp5o6S5p+l572R54K55a6h5qC477yM5by55qGG6aG16Z2iDQogICAgICogQHBhcmFtIHtCb29sZWFufSBwYXJhbSDlrqHmoLjpgJrov4cqLwogICAgZGlhbG9nUGFzc1N1Ym1pdDogZnVuY3Rpb24gZGlhbG9nUGFzc1N1Ym1pdCgpIHsKICAgICAgdmFyIF90aGlzMTQgPSB0aGlzOwogICAgICB2YXIgbXNnID0gewogICAgICAgIHBhcmFtZXRlckxpc3Q6IFt7fV0sCiAgICAgICAgdXNlcl9jb21tZW50OiAn5aSE572u6K+05piOOicgKyB0aGlzLmRpYWxvZ1Jpc2suZm9ybS5kZWZhdWx0Rm9ybS5jYmJfYXVkaXRfZGVhbF9kZXMsCiAgICAgICAgdGFza19ubzogdGhpcy5kaWFsb2dSaXNrLnJpc2tEaWFsb2dEYXRhLnRhc2tfbm9fbGlzdCwKICAgICAgICBjYmJfYXVkaXRfZGVhbF9kZXM6IHRoaXMuZGlhbG9nUmlzay5mb3JtLmRlZmF1bHRGb3JtLmNiYl9hdWRpdF9kZWFsX2RlcwogICAgICB9OwogICAgICBhdWRpdFRhc2sobXNnKS50aGVuKGZ1bmN0aW9uIChyZXNwb25zZSkgewogICAgICAgIGNvbW1vbk1zZ1N1Y2Nlc3MocmVzcG9uc2UucmV0TXNnLCBfdGhpczE0KTsKICAgICAgICBfdGhpczE0LmNoYW5nZVJpc2tWaXNpYmxlKGZhbHNlLCB0cnVlKTsKICAgICAgfSk7CiAgICB9LAogICAgLyoqDQogICAgICog6aOO6Zmp5o6S5p+l5oC76KGM5qC45p+l77yM5by55qGG6aG16Z2iDQogICAgICogQHBhcmFtIHtCb29sZWFufSBwYXJhbSDmoLjmn6XpgJrov4cqLwogICAgZGlhbG9nSG9QYXNzU3VibWl0OiBmdW5jdGlvbiBkaWFsb2dIb1Bhc3NTdWJtaXQoKSB7CiAgICAgIHZhciBfdGhpczE1ID0gdGhpczsKICAgICAgdmFyIG1zZyA9IHsKICAgICAgICBwYXJhbWV0ZXJMaXN0OiBbe31dLAogICAgICAgIC8vIOWkhOe9rue7k+iuugogICAgICAgIGhvQ2hlY2tEZWFsUmVzdWx0OiB0aGlzLmRpYWxvZ1Jpc2suZm9ybS5kZWZhdWx0Rm9ybS5yZXBvcnRQYXJhbXNfY2hlY2tTdWdnZXN0LAogICAgICAgIHRhc2tfbm86IHRoaXMuZGlhbG9nUmlzay5yaXNrRGlhbG9nRGF0YS50YXNrX25vX2xpc3QKICAgICAgfTsKICAgICAgaG9DaGVja1Rhc2sobXNnKS50aGVuKGZ1bmN0aW9uIChyZXNwb25zZSkgewogICAgICAgIGNvbW1vbk1zZ1N1Y2Nlc3MocmVzcG9uc2UucmV0TXNnLCBfdGhpczE1KTsKICAgICAgICBfdGhpczE1LmNoYW5nZVJpc2tWaXNpYmxlKGZhbHNlLCB0cnVlKTsKICAgICAgfSk7CiAgICB9LAogICAgLyoqDQogICAgICog6aOO6Zmp5o6S5p+l5oC76KGM5a6h5qC477yM5by55qGG6aG16Z2iDQogICAgICogQHBhcmFtIHtCb29sZWFufSBwYXJhbSDmoLjmn6XpgJrov4cqLwogICAgZGlhbG9nSG9BcHBQYXNzU3VibWl0OiBmdW5jdGlvbiBkaWFsb2dIb0FwcFBhc3NTdWJtaXQoKSB7CiAgICAgIHZhciBfdGhpczE2ID0gdGhpczsKICAgICAgdmFyIG1zZyA9IHsKICAgICAgICBwYXJhbWV0ZXJMaXN0OiBbe31dLAogICAgICAgIHRhc2tfbm86IHRoaXMuZGlhbG9nUmlzay5yaXNrRGlhbG9nRGF0YS50YXNrX25vX2xpc3QsCiAgICAgICAgaG9fYXVkaXRfZGVhbF9kZXM6IHRoaXMuZGlhbG9nUmlzay5mb3JtLmRlZmF1bHRGb3JtLmhvX2F1ZGl0X2RlYWxfZGVzCiAgICAgIH07CiAgICAgIGhvUGFzc0F1ZGl0VGFzayhtc2cpLnRoZW4oZnVuY3Rpb24gKHJlc3BvbnNlKSB7CiAgICAgICAgY29tbW9uTXNnU3VjY2VzcyhyZXNwb25zZS5yZXRNc2csIF90aGlzMTYpOwogICAgICAgIF90aGlzMTYuY2hhbmdlUmlza1Zpc2libGUoZmFsc2UsIHRydWUpOwogICAgICB9KTsKICAgIH0sCiAgICAvKioNCiAgICAgKiDmi5Lnu53or7TmmI7lvLnlh7rmoYYgLSDlhbPpl60NCiAgICAgKiBAcGFyYW0ge0Jvb2xlYW59IHBhcmFtIOW8ueWHuuahhuaYvuekuumakOiXj+mFjee9riovCiAgICBjaGFuZ2VWaXNpYmxlRm9ybTogZnVuY3Rpb24gY2hhbmdlVmlzaWJsZUZvcm0ocGFyYW0pIHsKICAgICAgdGhpcy5kaWFsb2dGb3JtLnZpc2libGUgPSBwYXJhbTsKICAgIH0sCiAgICAvKiDlhazlkYrlvLnlh7rmoYYNCiAgICAgKuaJk+WNsCAqLwogICAgaGFuZGxlUHJpbnQ6IGZ1bmN0aW9uIGhhbmRsZVByaW50KCkgewogICAgICB0aGlzLiRwcmludCh0aGlzLiRyZWZzLnByaW50UmVmKTsKICAgIH0sCiAgICAvKioNCiAgICAgKiDlhazlkYrlvLnlh7rmoYYgLSDlhbPpl60NCiAgICAgKiBAcGFyYW0ge0Jvb2xlYW59IHBhcmFtIOW8ueWHuuahhuaYvuekuumakOiXj+mFjee9riovCiAgICBjaGFuZ2VWaXNpYmxlOiBmdW5jdGlvbiBjaGFuZ2VWaXNpYmxlKHBhcmFtKSB7CiAgICAgIHRoaXMuZGlhbG9nLnZpc2libGUgPSBwYXJhbTsKICAgIH0sCiAgICAvKioNCiAgICAgKiDlhazlkYrlvLnlh7rmoYYgLSDnoa7orqQqLwogICAgZGlhbG9nU3VtYml0OiBmdW5jdGlvbiBkaWFsb2dTdW1iaXQoKSB7CiAgICAgIHRoaXMuY2hhbmdlVmlzaWJsZShmYWxzZSk7CiAgICB9LAogICAgZGljdGlvbmFyeVN1YnN5c3RlbUdldDogZnVuY3Rpb24gZGljdGlvbmFyeVN1YnN5c3RlbUdldChzeXN0ZW1ObykgewogICAgICB2YXIgX3RoaXMxNyA9IHRoaXM7CiAgICAgIGlmICghKHN5c3RlbU5vIGluIHRoaXMuJHN0b3JlLnN0YXRlLmNvbW1vbi5kaWN0aW9uYXJ5TGV0KSkgewogICAgICAgIHZhciBzeXN0ZW1EaWMgPSBKU09OLnBhcnNlKGxvY2FsU3RvcmFnZS5nZXRJdGVtKHN5c3RlbU5vKSk7CiAgICAgICAgaWYgKGNvbW1vbkJsYW5rKHN5c3RlbURpYykpIHsKICAgICAgICAgIHZhciBtc2cgPSB7CiAgICAgICAgICAgIGZpZWxkVGltZTogJycsCiAgICAgICAgICAgIG9wZXJUeXBlOiAnMScsCiAgICAgICAgICAgIC8vIOmXqOaIt+aTjeS9nOagh+ivhgogICAgICAgICAgICBzeXN0ZW1Obzogc3lzdGVtTm8KICAgICAgICAgIH07CiAgICAgICAgICBmaWVsZENvbnRyb2xsZXIobXNnKS50aGVuKGZ1bmN0aW9uIChyZXMpIHsKICAgICAgICAgICAgbG9jYWxTdG9yYWdlLnNldEl0ZW0oc3lzdGVtTm8sIEpTT04uc3RyaW5naWZ5KHJlcy5yZXRNYXBbc3lzdGVtTm9dKSk7CiAgICAgICAgICAgIGxvY2FsU3RvcmFnZS5zZXRJdGVtKHN5c3RlbU5vICsgJ2RpY3Rpb25hcnknLCBKU09OLnN0cmluZ2lmeShfZGVmaW5lUHJvcGVydHkoe30sIHN5c3RlbU5vLCByZXMucmV0TWFwLmRpY3Rpb25hcnlbc3lzdGVtTm9dKSkpOwogICAgICAgICAgICBfdGhpczE3LiRzdG9yZS5jb21taXQoJ2NvbW1vbi9BRERfRElDVElPTkFSWUxFVCcsIHsKICAgICAgICAgICAgICBzeXN0ZW1Obzogc3lzdGVtTm8sCiAgICAgICAgICAgICAgdmFsdWU6IHJlcy5yZXRNYXBbc3lzdGVtTm9dLAogICAgICAgICAgICAgIGRpY3Rpb25hcnk6IF9kZWZpbmVQcm9wZXJ0eSh7fSwgc3lzdGVtTm8sIHJlcy5yZXRNYXAuZGljdGlvbmFyeVtzeXN0ZW1Ob10pCiAgICAgICAgICAgIH0pOwogICAgICAgICAgfSk7CiAgICAgICAgfSBlbHNlIHsKICAgICAgICAgIHRoaXMuJHN0b3JlLmNvbW1pdCgnY29tbW9uL0FERF9ESUNUSU9OQVJZTEVUJywgewogICAgICAgICAgICBzeXN0ZW1Obzogc3lzdGVtTm8sCiAgICAgICAgICAgIHZhbHVlOiBzeXN0ZW1EaWMsCiAgICAgICAgICAgIGRpY3Rpb25hcnk6IEpTT04ucGFyc2UobG9jYWxTdG9yYWdlLmdldEl0ZW0oc3lzdGVtTm8gKyAnZGljdGlvbmFyeScpKQogICAgICAgICAgfSk7CiAgICAgICAgfQogICAgICB9CiAgICB9CiAgfQp9Ow=="}, null]}