$(function() {
	// init date tables
	var jobTable = $("#job_list").dataTable({
		"deferRender": true,
		"processing" : true,
	    "serverSide": true,
		"ajax": {
			url: base_url + "/jobrelation/pageList",
			type:"post",
	        data : function ( d ) {
	        	var obj = {};
	        	obj.jobGroup = $('#jobGroup').val();
	        	obj.jobId = $('#jobId').val();
	        	obj.executorHandler = $('#executorHandler').val();
	        	obj.start = d.start;
	        	obj.length = d.length;
                return obj;
            }
	    },
	    "searching": false,
	    "ordering": false,
	    //"scrollX": true,	// X轴滚动条，取消自适应
	    "columns": [
	                { "data": 'id', "bSortable": false, "visible" : false},
	                { "data": 'jobId', "visible" : true,"width":'10%'},
	                { "data": 'jobInfo.jobDesc',"width":'20%', "visible" : true},
	                { "data": 'childrenJob',"width":'10%', "visible" : true},
	                { "data": 'childrenJobInfo.jobDesc',"width":'20%', "visible" : true},
	                {
						"data": '操作' ,
						"width":'20%',
	                	"render": function ( data, type, row ) {
	                		return function(){
                                tableData['key'+row.id] = row;
                                var html = '<p id="'+ row.id +'" >'+
                                    '<button class="btn btn-warning btn-xs update" type="button">编辑</button>  '+
                                    '<button class="btn btn-warning btn-xs job_operate" _type="job_del" type="button">删除</button>  '+
                                    '<button class="btn btn-danger btn-xs jobQueryList" type="button">查看依赖链</button>  '+
                                    '</p>';

	                			return html;
							};
	                	}
	                }
	            ],
		"language" : {
			"sProcessing" : "处理中...",
			"sLengthMenu" : "每页 _MENU_ 条记录",
			"sZeroRecords" : "没有匹配结果",
			"sInfo" : "第 _PAGE_ 页 ( 总共 _PAGES_ 页，_TOTAL_ 条记录 )",
			"sInfoEmpty" : "无记录",
			"sInfoFiltered" : "(由 _MAX_ 项结果过滤)",
			"sInfoPostFix" : "",
			"sSearch" : "搜索:",
			"sUrl" : "",
			"sEmptyTable" : "表中数据为空",
			"sLoadingRecords" : "载入中...",
			"sInfoThousands" : ",",
			"oPaginate" : {
				"sFirst" : "首页",
				"sPrevious" : "上页",
				"sNext" : "下页",
				"sLast" : "末页"
			},
			"oAria" : {
				"sSortAscending" : ": 以升序排列此列",
				"sSortDescending" : ": 以降序排列此列"
			}
		}
	});

    // table data
    var tableData = {};

	// 搜索按钮
	$('#searchBtn').on('click', function(){
		jobTable.fnDraw();
	});

	// jobGroup change
	$('#jobGroup').on('change', function(){
        //reload
        var jobGroup = $('#jobGroup').val();
        window.location.href = base_url + "/jobinfo?jobGroup=" + jobGroup;
    });

	// job operate
	$("#job_list").on('click', '.job_operate',function() {
		var typeName;
		var url;
		var needFresh = false;

		var type = $(this).attr("_type");

		if ("job_del" == type) {
            typeName = "删除";
            url = base_url + "/jobrelation/remove";
            needFresh = true;
        } else {
			return;
		}

		var id = $(this).parent('p').attr("id");

		layer.confirm('确认' + typeName + '?', {icon: 3, title:'系统提示'}, function(index){
			layer.close(index);

			$.ajax({
				type : 'POST',
				url : url,
				data : {
					"id" : id
				},
				dataType : "json",
				success : function(data){
					if (data.code == 200) {

						layer.open({
							title: '系统提示',
							content: typeName + "成功",
							icon: '1',
							end: function(layero, index){
								if (needFresh) {
									//window.location.reload();
									jobTable.fnDraw();
								}
							}
						});
					} else {
						layer.open({
							title: '系统提示',
							content: (data.msg || typeName + "失败"),
							icon: '2'
						});
					}
				},
			});
		});
	});

	// jquery.validate 自定义校验 “英文字母开头，只含有英文字母、数字和下划线”
	jQuery.validator.addMethod("myValid01", function(value, element) {
		var jobId = $("#addModal .form select[name=jobId]").val();
		var childrenJob = $("#addModal .form select[name=childrenJob]").val();
		if(jobId == childrenJob){
            return false;
		}else {
            return true;
		}
	}, "不能选择任务编号自己");
	jQuery.validator.addMethod("myValid02", function(value, element) {
		var length = value.length;
		var valid = /^[a-zA-Z\u4e00-\u9fa5]+$/;
		return this.optional(element) || valid.test(value);
	}, "必须输入中文");

	// 新增
	$(".add").click(function(){
		$('#addModal').modal({backdrop: false, keyboard: false}).modal('show');
    	$("#addModal_childrenJob").selectpicker('refresh');//对addModal_childrenJob这个下拉框进行重置刷新
	});
	var addModalValidate = $("#addModal .form").validate({
		errorElement : 'span',
        errorClass : 'help-block',
        focusInvalid : true,
        rules : {
            childrenJob : {
            	required : true,
                myValid01:"不能包含任务自己"
			}
        },
        messages : {
            childrenJob : {
            	required :"请选择子任务”."
            }
        },
		highlight : function(element) {
            $(element).closest('.form-group').addClass('has-error');
        },
        success : function(label) {
            label.closest('.form-group').removeClass('has-error');
            label.remove();
        },
        errorPlacement : function(error, element) {
            element.parent('div').append(error);
        },
        submitHandler : function(form) {
            var jobId = $("#addModal .form select[name=jobId]").val();
            var childrenJob = $("#addModal .form select[name=childrenJob]").val();
        	$.post(base_url + "/jobrelation/add?childrenJob="+childrenJob+"&jobId="+jobId,function(data, status) {
    			if (data.code == "200") {
					$('#addModal').modal('hide');
					layer.open({
						title: '系统提示',
						content: '新增任务成功',
						icon: '1',
						end: function(layero, index){
							jobTable.fnDraw();
							//window.location.reload();
						}
					});
    			} else {
					layer.open({
						title: '系统提示',
						content: (data.msg || "新增失败"),
						icon: '2'
					});
    			}
    		});
		}
	});

	function clean_childrenJob(){
		$('#addModal_childrenJob')
	}
	$("#addModal").on('hide.bs.modal', function () {
		$("#addModal .form")[0].reset();
		addModalValidate.resetForm();
		$("#addModal .form .form-group").removeClass("has-error");
		$(".remote_panel").show();	// remote

		$("#addModal .form input[name='executorHandler']").removeAttr("readonly");
	});


    // 运行模式
    $(".glueType").change(function(){
		// executorHandler
        var $executorHandler = $(this).parents("form").find("input[name='executorHandler']");
        var glueType = $(this).val();
        if ('BEAN' != glueType) {
            $executorHandler.val("");
            $executorHandler.attr("readonly","readonly");
        } else {
            $executorHandler.removeAttr("readonly");
        }
    });

	$("#addModal .glueType").change(function(){
		// glueSource
		var glueType = $(this).val();
		if ('GLUE_GROOVY'==glueType){
			$("#addModal .form textarea[name='glueSource']").val( $("#addModal .form .glueSource_java").val() );
		} else if ('GLUE_SHELL'==glueType){
			$("#addModal .form textarea[name='glueSource']").val( $("#addModal .form .glueSource_shell").val() );
		} else if ('GLUE_PYTHON'==glueType){
			$("#addModal .form textarea[name='glueSource']").val( $("#addModal .form .glueSource_python").val() );
		}
	});

	// 更新
	$("#job_list").on('click', '.update',function() {

        var id = $(this).parent('p').attr("id");
        var row = tableData['key'+id];
        if (!row) {
            layer.open({
                title: '系统提示',
                content: ("任务信息加载失败，请刷新页面"),
                icon: '2'
            });
            return;
        }

		// base data
		$("#updateModal .form input[name='id']").val( row.id );
		$('#updateModal .form select[name=jobId] option[value='+ row.jobId +']').prop('selected', true);
		$('#updateModal .form select[name=childrenJob] option[value='+ row.childrenJob +']').prop('selected', true);


		// show
		$('#updateModal').modal({backdrop: false, keyboard: false}).modal('show');
	});
	var updateModalValidate = $("#updateModal .form").validate({
		errorElement : 'span',
        errorClass : 'help-block',
        focusInvalid : true,
        rules : {
            childrenJob : {
                required : true,
                myValid01:"不能包含任务自己"
            }
        },
        messages : {
            childrenJob : {
                required :"请选择子任务”."
            }
        },
		highlight : function(element) {
            $(element).closest('.form-group').addClass('has-error');
        },
        success : function(label) {
            label.closest('.form-group').removeClass('has-error');
            label.remove();
        },
        errorPlacement : function(error, element) {
            element.parent('div').append(error);
        },
        submitHandler : function(form) {
			// post
    		$.post(base_url + "/jobrelation/update", $("#updateModal .form").serialize(), function(data, status) {
    			if (data.code == "200") {
					$('#updateModal').modal('hide');
					layer.open({
						title: '系统提示',
						content: '更新成功',
						icon: '1',
						end: function(layero, index){
							//window.location.reload();
							jobTable.fnDraw();
						}
					});
    			} else {
					layer.open({
						title: '系统提示',
						content: (data.msg || "更新失败"),
						icon: '2'
					});
    			}
    		});
		}
	});
	$("#updateModal").on('hide.bs.modal', function () {
		$("#updateModal .form")[0].reset()
	});






    // 更新
    $("#job_list").on('click', '.jobQueryList',function() {
        url = base_url + "/jobrelation/jobQueryList";
        var id = $(this).parent('p').attr("id");
        $.ajax({
            type : 'POST',
            url : url,
            data : {
                "id" : id
            },
            success : function(data){
				$('#checkDepChain').modal({
					backdrop: false, //指定一个静态的背景，当用户点击模态框外部时不会关闭模态框。
					keyboard: false//当按下 escape 键时关闭模态框，设置为 false 时则按键无效。
				}).modal('show');//当初始化时显示模态框。


                $("#joblist").text(data);
            },

        });
    });

});
