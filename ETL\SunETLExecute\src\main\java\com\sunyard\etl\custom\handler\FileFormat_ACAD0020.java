package com.sunyard.etl.custom.handler;

import java.io.BufferedReader;
import java.io.File;
import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.InputStreamReader;
import java.io.OutputStreamWriter;
import java.io.PrintWriter;
import java.text.SimpleDateFormat;
import com.sunyard.etl.system.common.Constants;
import org.springframework.stereotype.Service;

import com.sunyard.etl.system.dao.DataDateDAO;
import com.sunyard.etl.system.dao.impl.DataDateDAOImpl;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.enums.JobStartEnum;
import com.xxl.job.core.handler.IJobHandler;
import com.xxl.job.core.handler.annotation.JobHandler;
import com.xxl.job.core.log.XxlJobLogger;


@JobHandler(value = "FileFormat_ACAD0020", name = "ACAD0020文件格式化")
@Service
public class FileFormat_ACAD0020 extends IJobHandler {

	private static final long serialVersionUID = 1L;

	private static DataDateDAO dateDao = new DataDateDAOImpl();

	@Override
	public ReturnT<String> execute(String jobId, String... arg1) throws Exception {
		XxlJobLogger.log("开始ACAD0020文件格式化...");
		String jobDate = dateDao.getDataDate();
		if (null != arg1[0]) {
			String dirPath = arg1[0].toString().replace("@", jobDate);
			File dir = new File(dirPath);
			if (!dir.isDirectory()) {
				XxlJobLogger.log("INFO: 资源不足，目录不存在：" + dir, jobId + "");
				return new ReturnT<String>(ReturnT.FAIL_CODE, JobStartEnum.TASK_STATE_NO_RESOURCE.getCode(),
						"文件目录" + dir.getPath() + "不存在");
			}
			File preFile = new File(dir, "ACAD0020_"+jobDate+".txt");
			File file = new File(dir, "ACAD0020_"+jobDate+"_proc.txt");
			if (file.exists()) {
				XxlJobLogger.log("INFO: 文件已存在，先删除后再转换：" + file.getPath(), jobId + "");
				file.delete();
			}
			PrintWriter pw;
			pw = new PrintWriter(new OutputStreamWriter(new FileOutputStream(file), "GBK"));
			if (preFile.length()==0) {
				pw.print("|||");
				pw.flush();
				pw.close();
				XxlJobLogger.log("INFO: 源文件不存在，生成转换文件：" + preFile.getPath() + " >> " + file.getPath(), jobId + "");
				return ReturnT.SUCCESS;
			}
			BufferedReader br = new BufferedReader(new InputStreamReader(new FileInputStream(preFile), "GBK"));
			
			String line = null;
			String siteNo = "";
			String siteName = "";
			int hang = 0 ;
			try {
				while ((line = br.readLine()) != null) {
					hang++ ;
					if(line.contains("1                 ") && line.contains("ATM现金长短款自动挂销账")){
						continue;
					} 
					if("".equals(line.trim())||(line.contains("核心流水号") && line.contains("实际柜员号"))){
						continue;
					}     
					if (line.length() <91 ) {
						continue;
					}
					
					String newLine = null ;
					try {//核心流水号   实际柜员号 实际交易机构 挂销账标识          金额 销账码          机具号   机具机构
						newLine =line.substring(1,13).trim() +" | "	//核心流水号
								+line.substring(13,24).trim() +" | "	//实际柜员号
								+line.substring(24,35).trim() +" | "	//实际交易机构
								+line.substring(35,47).trim() +" | "	//挂销账标识
								+line.substring(47,61).trim() +" | "	// 金额
								+line.substring(61,76).trim() +" | "	//销账码   
								+line.substring(76,86).trim() +" | "	//机具号
								
								+line.substring(86).trim() +" | "	//机具机构
								+ jobDate;	//交易日期
								
						pw.print(newLine+System.getProperty("line.separator"));
					} catch (Exception e) {
						XxlJobLogger.log("INFO: 第"+hang+"出错，原因是："+e, jobId );
						continue ;
					}
				}
			} catch (IOException e) {
				e.printStackTrace();
			}finally{
				pw.flush();
				pw.close();
			}
			Thread.sleep(8000);
			XxlJobLogger.log("INFO: 文件转换成功：" + file.getPath()+ ",休眠完8秒钟", jobId );
			return ReturnT.SUCCESS;
		}
		return ReturnT.FAIL;
	}

	
	


	public static void main(String[] args) {
		FileFormat_ACAD0020 ff = new FileFormat_ACAD0020();
		try {
			ff.execute("244", "D:\\data\\@");
		} catch (Exception e) {
			// TODO Auto-generated catch block
			e.printStackTrace();
		}
	}
}
