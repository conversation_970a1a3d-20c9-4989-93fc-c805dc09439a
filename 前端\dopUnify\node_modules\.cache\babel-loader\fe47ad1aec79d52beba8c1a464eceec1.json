{"remainingRequest": "D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\node_modules\\babel-loader\\lib\\index.js!D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\node_modules\\sunui\\src\\components\\SunTable\\SunPagination\\index.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\node_modules\\sunui\\src\\components\\SunTable\\SunPagination\\index.vue", "mtime": 499162500000}, {"path": "D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\babel.config.js", "mtime": 1686019818359}, {"path": "D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1655715099000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "mappings": ";;;;;;;;;;;;;;;;;;;;;;;AAsBA;AAEA;EACAA;EACAC;IACAC;MACAC;MACAC;IACA;IACAC;MACAD;MACAE;IACA;IACAC;MACAH;MACAE;IACA;IACAE;MACAJ;MACAE;QACA;MACA;IACA;IACAG;MACAL;MACAE;IACA;IACAI;MACAN;MACAE;IACA;IACAK;MACAP;MACAE;IACA;IACAM;MACAR;MACAE;IACA;IACAO;MACAT;MACAE;IACA;IACA;IACA;IACA;IACA;EACA;EACAQ;IACA;MACAC;IACA;EACA;;EACAC;IACAC;MACAC;QACA;MACA;MACAC;QACA;MACA;IACA;IACAC;MACAF;QACA;MACA;MACAC;QACA;MACA;IACA;EACA;EACAE;IACAC;MACA;QAAAL;QAAAG;MAAA;MACA;QACAG;MACA;IACA;IACAC;MACA;QAAAP;QAAAG;MAAA;MACA;QACAG;MACA;IACA;EACA;AACA", "names": ["name", "props", "total", "required", "type", "page", "default", "limit", "pageSizes", "layout", "background", "autoScroll", "hidden", "small", "data", "pagerCount", "computed", "currentPage", "get", "set", "pageSize", "methods", "handleSizeChange", "scrollTo", "handleCurrentChange"], "sourceRoot": "node_modules/sunui/src/components/SunTable/SunPagination", "sources": ["index.vue"], "sourcesContent": ["<!--\n* 页码\n-->\n<template>\n  <div :class=\"{ hidden: hidden }\" class=\"pagination-container\">\n    <el-pagination\n      :small=\"small\"\n      :background=\"background\"\n      :current-page.sync=\"currentPage\"\n      :page-size.sync=\"pageSize\"\n      :layout=\"layout\"\n      :pager-count=\"pagerCount\"\n      :page-sizes=\"pageSizes\"\n      :total=\"total\"\n      v-bind=\"$attrs\"\n      @size-change=\"handleSizeChange\"\n      @current-change=\"handleCurrentChange\"\n    />\n  </div>\n</template>\n\n<script>\nimport { scrollTo } from '@/utils/scroll-to'\n\nexport default {\n  name: 'SunPagination',\n  props: {\n    total: {\n      required: true,\n      type: Number\n    },\n    page: {\n      type: Number,\n      default: 1\n    },\n    limit: {\n      type: Number,\n      default: 10\n    },\n    pageSizes: {\n      type: Array,\n      default() {\n        return [10, 20, 30, 50]\n      }\n    },\n    layout: {\n      type: String,\n      default: 'total, sizes, prev, pager, next, jumper'\n    },\n    background: {\n      type: Boolean,\n      default: true\n    },\n    autoScroll: {\n      type: Boolean,\n      default: true\n    },\n    hidden: {\n      type: <PERSON><PERSON>an,\n      default: false\n    },\n    small: {\n      type: <PERSON><PERSON>an,\n      default: false\n    }\n    // pagerCount: {\n    //   type: Number,\n    //   default: 5 // 页码按钮的数量，当总页数超过该值时会折叠, 大于等于 5 且小于等于 21 的奇数\n    // }\n  },\n  data() {\n    return {\n      pagerCount: 5 // 页码按钮的数量，当总页数超过该值时会折叠, 大于等于 5 且小于等于 21 的奇数\n    }\n  },\n  computed: {\n    currentPage: {\n      get() {\n        return this.page\n      },\n      set(val) {\n        this.$emit('update:page', val)\n      }\n    },\n    pageSize: {\n      get() {\n        return this.limit\n      },\n      set(val) {\n        this.$emit('update:limit', val)\n      }\n    }\n  },\n  methods: {\n    handleSizeChange(val) {\n      this.$emit('pagination', { currentPage: this.currentPage, pageSize: val })\n      if (this.autoScroll) {\n        scrollTo(0, 800)\n      }\n    },\n    handleCurrentChange(val) {\n      this.$emit('pagination', { currentPage: val, pageSize: this.pageSize })\n      if (this.autoScroll) {\n        scrollTo(0, 800)\n      }\n    }\n  }\n}\n</script>\n\n<style scoped>\n.pagination-container {\n  background: #fff;\n  padding: 1rem 0;\n  width: fit-content;\n}\n.pagination-container.hidden {\n  display: none;\n}\n</style>\n"]}]}