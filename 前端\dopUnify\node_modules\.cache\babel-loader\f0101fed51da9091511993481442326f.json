{"remainingRequest": "E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-q<PERSON><PERSON>n\\数字运营平台-统一门户工程\\dopUnify\\node_modules\\babel-loader\\lib\\index.js!E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-q<PERSON>duan\\数字运营平台-统一门户工程\\dopUnify\\node_modules\\eslint-loader\\index.js??ref--13-0!E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan\\数字运营平台-统一门户工程\\dopUnify\\src\\utils\\crypto.js", "dependencies": [{"path": "E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-<PERSON><PERSON><PERSON>n\\数字运营平台-统一门户工程\\dopUnify\\src\\utils\\crypto.js", "mtime": 1712036644751}, {"path": "E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-<PERSON><PERSON><PERSON>n\\数字运营平台-统一门户工程\\dopUnify\\babel.config.js", "mtime": 1686019818359}, {"path": "E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-<PERSON><PERSON><PERSON>n\\数字运营平台-统一门户工程\\dopUnify\\.babelrc", "mtime": 1705285078668}, {"path": "E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-<PERSON><PERSON><PERSON>n\\数字运营平台-统一门户工程\\dopUnify\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-<PERSON><PERSON><PERSON>n\\数字运营平台-统一门户工程\\dopUnify\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-<PERSON><PERSON><PERSON>n\\数字运营平台-统一门户工程\\dopUnify\\node_modules\\eslint-loader\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["CryptoJs", "SM2", "SM3", "SM4", "store", "cryptoAES", "encrypt", "word", "key", "enc", "Utf8", "parse", "srcs", "encrypted", "AES", "iv", "mode", "CBC", "toString", "decrypt", "stringify", "cryptoSM2", "data", "public<PERSON>ey", "inputEncoding", "outputEncoding", "privateKey", "cryptoSM3", "digest", "cryptoSM4ECB", "encryptResult", "type", "getters", "initParams", "enSecMap", "decryptResult", "replaceAll", "String", "fromCharCode"], "sources": ["E:/1_Project/XYD_Project/dop-4.0/dop-4.1-q<PERSON><PERSON>n/数字运营平台-统一门户工程/dopUnify/src/utils/crypto.js"], "sourcesContent": ["import CryptoJs from 'crypto-js'\r\nimport { SM2, SM3, SM4 } from 'gm-crypto'\r\nimport store from '@/store'\r\n// import * as Base64 from 'js-base64'\r\n// function createNonceStr() {\r\n//   const chars = ['0', '1', '2', '3', '4', '5', '6', '7', '8', '9']\r\n//   let strs = ''\r\n//   for (let i = 0; i < 16; i++) {\r\n//     const id = parseInt(Math.random() * 10)\r\n//     strs += chars[id]\r\n//   }\r\n//   return strs\r\n// }\r\n// AES\r\nconst cryptoAES = {\r\n  encrypt(word) {\r\n    const key = CryptoJs.enc.Utf8.parse('0102030405060708')\r\n    const srcs = CryptoJs.enc.Utf8.parse(word)\r\n    const encrypted = CryptoJs.AES.encrypt(srcs, key, {\r\n      iv: key,\r\n      mode: CryptoJs.mode.CBC\r\n    })\r\n    return encrypted.toString()\r\n  },\r\n\r\n  /**\r\n   * 解密\r\n   * @param {String} word: 加密信息\r\n   */\r\n  decrypt(word) {\r\n    const key = CryptoJs.enc.Utf8.parse('0102030405060708')\r\n    const decrypt = CryptoJs.AES.decrypt(word, key, {\r\n      // mode: CryptoJs.mode.ECB,\r\n      // padding: CryptoJs.pad.Pkcs7\r\n      iv: key,\r\n      mode: CryptoJs.mode.CBC\r\n    })\r\n    return CryptoJs.enc.Utf8.stringify(decrypt).toString()\r\n  }\r\n  /**\r\n   * 加密\r\n   * @param {String} word: 加密信息\r\n   */\r\n  // encrypt(word) {\r\n  //   const key = CryptoJs.enc.Utf8.parse('0102030405060708')\r\n  //   const srcs = CryptoJs.enc.Utf8.parse(word)\r\n  //   const aaa = createNonceStr()\r\n  //   const renderNum = CryptoJs.enc.Utf8.parse(aaa)\r\n  //   const encrypted = CryptoJs.AES.encrypt(srcs, key, {\r\n  //     iv: renderNum,\r\n  //     mode: CryptoJs.mode.CBC\r\n  //   })\r\n  //   return Base64.encode(aaa) + ';' + encrypted.toString()\r\n  // },\r\n\r\n  // /**\r\n  //  * 解密\r\n  //  * @param {String} word: 加密信息\r\n  //  */\r\n  // decrypt(word) {\r\n  //   const firstWord = word.indexOf(';') // 第一次出线；的位置\r\n  //   const num = word.substring(0, firstWord) // ;符号之前所有的字符串-变动的密钥\r\n  //   const resultWord = word.substring(firstWord + 1, word.length)\r\n  //   const a = CryptoJs.enc.Base64.parse(num)\r\n  //   console.log(a.words)\r\n  //   // const firstKey = Base64.decode(a.words)\r\n  //   console.log(num, 'num')\r\n  //   console.log(resultWord, 'resultWord')\r\n  //   // console.log(firstKey, 'firstKey')\r\n  //   // console.log(CryptoJs.enc.Base64.parse(num))\r\n\r\n  //   const key = CryptoJs.enc.Utf8.parse('0102030405060708')\r\n  //   // const key2 = CryptoJs.enc.Utf8.parse(firstKey)\r\n  //   const decrypt = CryptoJs.AES.decrypt(resultWord, key, {\r\n  //     // mode: CryptoJs.mode.ECB,\r\n  //     // padding: CryptoJs.pad.Pkcs7\r\n  //     iv: a,\r\n  //     mode: CryptoJs.mode.CBC\r\n  //   })\r\n  //   return CryptoJs.enc.Utf8.stringify(decrypt).toString()\r\n  // }\r\n}\r\n\r\n// SM2\r\n// const { publicKey, privateKey } = SM2.generateKeyPair()\r\nconst cryptoSM2 = {\r\n  encrypt: (data, publicKey) => {\r\n    return SM2.encrypt(data, publicKey, {\r\n      inputEncoding: 'utf8',\r\n      outputEncoding: 'base64'\r\n    })\r\n  },\r\n\r\n  decrypt: (data, privateKey) =>\r\n    SM2.decrypt(data, privateKey, {\r\n      inputEncoding: 'base64',\r\n      outputEncoding: 'utf8'\r\n    })\r\n}\r\n\r\n// SM3\r\nconst cryptoSM3 = {\r\n  encrypt: (data) => SM3.digest(data, 'utf8', 'base64')\r\n}\r\n\r\n// SM4\r\n// key是后端的sm4 key转为32位的16进制\r\nconst key = '********************************'\r\nconst cryptoSM4ECB = {\r\n  encrypt: (data) =>\r\n    SM4.encrypt(data, key, {\r\n      inputEncoding: 'utf8',\r\n      outputEncoding: 'base64'\r\n    }),\r\n  decrypt: (data) =>\r\n    SM4.decrypt(data, key, {\r\n      inputEncoding: 'base64',\r\n      outputEncoding: 'utf8'\r\n    })\r\n}\r\n\r\n// 32位十六进制数字的任意字符串\r\n// const iv = '0123456789abcdeffedcba9876543210'\r\n// const cryptoSM4CBC = {\r\n//   encrypt: (data) =>\r\n//     SM4.encrypt(data, key, {\r\n//       iv,\r\n//       mode: SM2.constants.CBC,\r\n//       inputEncoding: 'utf8',\r\n//       outputEncoding: 'hex'\r\n//     }),\r\n//   decrypt: (data) =>\r\n//     SM4.decrypt(data, key, {\r\n//       iv,\r\n//       mode: SM2.constants.CBC,\r\n//       inputEncoding: 'hex',\r\n//       outputEncoding: 'utf8'\r\n//     })\r\n// }\r\n\r\n// 加密\r\nexport const encryptResult = (type, data) => {\r\n  if (type === 'AES') {\r\n    return cryptoAES.encrypt(data)\r\n  } else if (type === 'SM2') {\r\n    return cryptoSM2.encrypt(data, store.getters.initParams.enSecMap.publicKey)\r\n  } else if (type === 'SM3') {\r\n    return cryptoSM3.encrypt(data)\r\n  } else if (type === 'SM4') {\r\n    return cryptoSM4ECB.encrypt(data)\r\n  }\r\n}\r\n\r\n// 解密\r\nexport const decryptResult = (type, data) => {\r\n  if (type === 'AES') {\r\n    return cryptoAES.decrypt(data)\r\n  } else if (type === 'SM2') {\r\n    // console.log('SM2')\r\n  } else if (type === 'SM4') {\r\n    return cryptoSM4ECB\r\n      .decrypt(data)\r\n      .toString()\r\n      .replaceAll(String.fromCharCode(0), '')\r\n  }\r\n}\r\n"], "mappings": ";;;;;AAAA,OAAOA,QAAQ,MAAM,WAAW;AAChC,SAASC,GAAG,EAAEC,GAAG,EAAEC,GAAG,QAAQ,WAAW;AACzC,OAAOC,KAAK,MAAM,SAAS;AAC3B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAMC,SAAS,GAAG;EAChBC,OAAO,mBAACC,IAAI,EAAE;IACZ,IAAMC,GAAG,GAAGR,QAAQ,CAACS,GAAG,CAACC,IAAI,CAACC,KAAK,CAAC,kBAAkB,CAAC;IACvD,IAAMC,IAAI,GAAGZ,QAAQ,CAACS,GAAG,CAACC,IAAI,CAACC,KAAK,CAACJ,IAAI,CAAC;IAC1C,IAAMM,SAAS,GAAGb,QAAQ,CAACc,GAAG,CAACR,OAAO,CAACM,IAAI,EAAEJ,GAAG,EAAE;MAChDO,EAAE,EAAEP,GAAG;MACPQ,IAAI,EAAEhB,QAAQ,CAACgB,IAAI,CAACC;IACtB,CAAC,CAAC;IACF,OAAOJ,SAAS,CAACK,QAAQ,EAAE;EAC7B,CAAC;EAED;AACF;AACA;AACA;EACEC,OAAO,mBAACZ,IAAI,EAAE;IACZ,IAAMC,GAAG,GAAGR,QAAQ,CAACS,GAAG,CAACC,IAAI,CAACC,KAAK,CAAC,kBAAkB,CAAC;IACvD,IAAMQ,OAAO,GAAGnB,QAAQ,CAACc,GAAG,CAACK,OAAO,CAACZ,IAAI,EAAEC,GAAG,EAAE;MAC9C;MACA;MACAO,EAAE,EAAEP,GAAG;MACPQ,IAAI,EAAEhB,QAAQ,CAACgB,IAAI,CAACC;IACtB,CAAC,CAAC;IACF,OAAOjB,QAAQ,CAACS,GAAG,CAACC,IAAI,CAACU,SAAS,CAACD,OAAO,CAAC,CAACD,QAAQ,EAAE;EACxD;EACA;AACF;AACA;AACA;EACE;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EAEA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EAEA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;AACF,CAAC;;AAED;AACA;AACA,IAAMG,SAAS,GAAG;EAChBf,OAAO,EAAE,iBAACgB,IAAI,EAAEC,SAAS,EAAK;IAC5B,OAAOtB,GAAG,CAACK,OAAO,CAACgB,IAAI,EAAEC,SAAS,EAAE;MAClCC,aAAa,EAAE,MAAM;MACrBC,cAAc,EAAE;IAClB,CAAC,CAAC;EACJ,CAAC;EAEDN,OAAO,EAAE,iBAACG,IAAI,EAAEI,UAAU;IAAA,OACxBzB,GAAG,CAACkB,OAAO,CAACG,IAAI,EAAEI,UAAU,EAAE;MAC5BF,aAAa,EAAE,QAAQ;MACvBC,cAAc,EAAE;IAClB,CAAC,CAAC;EAAA;AACN,CAAC;;AAED;AACA,IAAME,SAAS,GAAG;EAChBrB,OAAO,EAAE,iBAACgB,IAAI;IAAA,OAAKpB,GAAG,CAAC0B,MAAM,CAACN,IAAI,EAAE,MAAM,EAAE,QAAQ,CAAC;EAAA;AACvD,CAAC;;AAED;AACA;AACA,IAAMd,GAAG,GAAG,kCAAkC;AAC9C,IAAMqB,YAAY,GAAG;EACnBvB,OAAO,EAAE,iBAACgB,IAAI;IAAA,OACZnB,GAAG,CAACG,OAAO,CAACgB,IAAI,EAAEd,GAAG,EAAE;MACrBgB,aAAa,EAAE,MAAM;MACrBC,cAAc,EAAE;IAClB,CAAC,CAAC;EAAA;EACJN,OAAO,EAAE,iBAACG,IAAI;IAAA,OACZnB,GAAG,CAACgB,OAAO,CAACG,IAAI,EAAEd,GAAG,EAAE;MACrBgB,aAAa,EAAE,QAAQ;MACvBC,cAAc,EAAE;IAClB,CAAC,CAAC;EAAA;AACN,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA,OAAO,IAAMK,aAAa,GAAG,SAAhBA,aAAa,CAAIC,IAAI,EAAET,IAAI,EAAK;EAC3C,IAAIS,IAAI,KAAK,KAAK,EAAE;IAClB,OAAO1B,SAAS,CAACC,OAAO,CAACgB,IAAI,CAAC;EAChC,CAAC,MAAM,IAAIS,IAAI,KAAK,KAAK,EAAE;IACzB,OAAOV,SAAS,CAACf,OAAO,CAACgB,IAAI,EAAElB,KAAK,CAAC4B,OAAO,CAACC,UAAU,CAACC,QAAQ,CAACX,SAAS,CAAC;EAC7E,CAAC,MAAM,IAAIQ,IAAI,KAAK,KAAK,EAAE;IACzB,OAAOJ,SAAS,CAACrB,OAAO,CAACgB,IAAI,CAAC;EAChC,CAAC,MAAM,IAAIS,IAAI,KAAK,KAAK,EAAE;IACzB,OAAOF,YAAY,CAACvB,OAAO,CAACgB,IAAI,CAAC;EACnC;AACF,CAAC;;AAED;AACA,OAAO,IAAMa,aAAa,GAAG,SAAhBA,aAAa,CAAIJ,IAAI,EAAET,IAAI,EAAK;EAC3C,IAAIS,IAAI,KAAK,KAAK,EAAE;IAClB,OAAO1B,SAAS,CAACc,OAAO,CAACG,IAAI,CAAC;EAChC,CAAC,MAAM,IAAIS,IAAI,KAAK,KAAK,EAAE;IACzB;EAAA,CACD,MAAM,IAAIA,IAAI,KAAK,KAAK,EAAE;IACzB,OAAOF,YAAY,CAChBV,OAAO,CAACG,IAAI,CAAC,CACbJ,QAAQ,EAAE,CACVkB,UAAU,CAACC,MAAM,CAACC,YAAY,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC;EAC3C;AACF,CAAC"}]}