{"remainingRequest": "D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\node_modules\\babel-loader\\lib\\index.js!D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\node_modules\\eslint-loader\\index.js??ref--13-0!D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\src\\api\\views\\system\\externalManage\\post\\index.js", "dependencies": [{"path": "D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\src\\api\\views\\system\\externalManage\\post\\index.js", "mtime": 1686019810701}, {"path": "D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\babel.config.js", "mtime": 1686019818359}, {"path": "D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\.babelrc", "mtime": 1705285078668}, {"path": "D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\node_modules\\eslint-loader\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:aW1wb3J0IHJlcXVlc3QgZnJvbSAnQC91dGlscy9yZXF1ZXN0JzsKaW1wb3J0IGRlZmF1bHRTZXR0aW5ncyBmcm9tICdAL3NldHRpbmdzJzsKdmFyIHByZWZpeCA9IGRlZmF1bHRTZXR0aW5ncy5zZXJ2aWNlLnN5c3RlbTsgLy8g5YmN57yA5YWs5YWx6Lev55SxCmV4cG9ydCB2YXIgU3lzRXh0ZXJQb3N0ID0gewogIC8vIOaOpeWPo+WQjeensAogIHF1ZXJ5OiBmdW5jdGlvbiBxdWVyeShkYXRhKSB7CiAgICAvLyDmn6Xor6IKICAgIHJldHVybiByZXF1ZXN0KHsKICAgICAgdXJsOiBwcmVmaXggKyAnL3Bvc3RleHRlcm5hbC9xdWVyeS5kbycsCiAgICAgIG1ldGhvZDogJ2dldCcsCiAgICAgIHBhcmFtczogewogICAgICAgIG1lc3NhZ2U6IGRhdGEKICAgICAgfQogICAgfSk7CiAgfSwKICBxdWVyeVVzZXI6IGZ1bmN0aW9uIHF1ZXJ5VXNlcihkYXRhKSB7CiAgICAvLyDmn6Xor6LlhbPogZTnlKjmiLcKICAgIHJldHVybiByZXF1ZXN0KHsKICAgICAgdXJsOiBwcmVmaXggKyAnL2V4dGVybmFsVXNlckZsb3cvZXh0ZXJuYWxVc2VyRmxvd0RldGFpbEluaXQuZG8nLAogICAgICBtZXRob2Q6ICdnZXQnLAogICAgICBwYXJhbXM6IHsKICAgICAgICBtZXNzYWdlOiBkYXRhCiAgICAgIH0KICAgIH0pOwogIH0sCiAgYWRkOiBmdW5jdGlvbiBhZGQoZGF0YSkgewogICAgLy8g5paw5aKeCiAgICByZXR1cm4gcmVxdWVzdCh7CiAgICAgIHVybDogcHJlZml4ICsgJy9wb3N0ZXh0ZXJuYWwvYWRkLmRvJywKICAgICAgbWV0aG9kOiAncG9zdCcsCiAgICAgIGRhdGE6IGRhdGEKICAgIH0pOwogIH0sCiAgbW9kaWZ5OiBmdW5jdGlvbiBtb2RpZnkoZGF0YSkgewogICAgLy8g5L+u5pS5CiAgICByZXR1cm4gcmVxdWVzdCh7CiAgICAgIHVybDogcHJlZml4ICsgJy9wb3N0ZXh0ZXJuYWwvbW9kaWZ5LmRvJywKICAgICAgbWV0aG9kOiAncG9zdCcsCiAgICAgIGRhdGE6IGRhdGEKICAgIH0pOwogIH0sCiAgZGVsOiBmdW5jdGlvbiBkZWwoZGF0YSkgewogICAgLy8g5Yig6ZmkCiAgICByZXR1cm4gcmVxdWVzdCh7CiAgICAgIHVybDogcHJlZml4ICsgJy9wb3N0ZXh0ZXJuYWwvZGVsZXRlLmRvJywKICAgICAgbWV0aG9kOiAnZGVsZXRlJywKICAgICAgZGF0YTogZGF0YQogICAgfSk7CiAgfQp9Ow=="}, {"version": 3, "names": ["request", "defaultSettings", "prefix", "service", "system", "SysExterPost", "query", "data", "url", "method", "params", "message", "queryUser", "add", "modify", "del"], "sources": ["D:/1_Project/XYD_Project/dop-4.0/dop-4.1-qianduan-unify/dopUnify/src/api/views/system/externalManage/post/index.js"], "sourcesContent": ["import request from '@/utils/request'\r\nimport defaultSettings from '@/settings'\r\nconst prefix = defaultSettings.service.system // 前缀公共路由\r\nexport const SysExterPost = {\r\n  // 接口名称\r\n  query(data) {\r\n    // 查询\r\n    return request({\r\n      url: prefix + '/postexternal/query.do',\r\n      method: 'get',\r\n      params: { message: data }\r\n    })\r\n  },\r\n  queryUser(data) {\r\n    // 查询关联用户\r\n    return request({\r\n      url: prefix + '/externalUserFlow/externalUserFlowDetailInit.do',\r\n      method: 'get',\r\n      params: { message: data }\r\n    })\r\n  },\r\n  add(data) {\r\n    // 新增\r\n    return request({\r\n      url: prefix + '/postexternal/add.do',\r\n      method: 'post',\r\n      data\r\n    })\r\n  },\r\n  modify(data) {\r\n    // 修改\r\n    return request({\r\n      url: prefix + '/postexternal/modify.do',\r\n      method: 'post',\r\n      data\r\n    })\r\n  },\r\n  del(data) {\r\n    // 删除\r\n    return request({\r\n      url: prefix + '/postexternal/delete.do',\r\n      method: 'delete',\r\n      data\r\n    })\r\n  }\r\n}\r\n"], "mappings": "AAAA,OAAOA,OAAO,MAAM,iBAAiB;AACrC,OAAOC,eAAe,MAAM,YAAY;AACxC,IAAMC,MAAM,GAAGD,eAAe,CAACE,OAAO,CAACC,MAAM,EAAC;AAC9C,OAAO,IAAMC,YAAY,GAAG;EAC1B;EACAC,KAAK,iBAACC,IAAI,EAAE;IACV;IACA,OAAOP,OAAO,CAAC;MACbQ,GAAG,EAAEN,MAAM,GAAG,wBAAwB;MACtCO,MAAM,EAAE,KAAK;MACbC,MAAM,EAAE;QAAEC,OAAO,EAAEJ;MAAK;IAC1B,CAAC,CAAC;EACJ,CAAC;EACDK,SAAS,qBAACL,IAAI,EAAE;IACd;IACA,OAAOP,OAAO,CAAC;MACbQ,GAAG,EAAEN,MAAM,GAAG,iDAAiD;MAC/DO,MAAM,EAAE,KAAK;MACbC,MAAM,EAAE;QAAEC,OAAO,EAAEJ;MAAK;IAC1B,CAAC,CAAC;EACJ,CAAC;EACDM,GAAG,eAACN,IAAI,EAAE;IACR;IACA,OAAOP,OAAO,CAAC;MACbQ,GAAG,EAAEN,MAAM,GAAG,sBAAsB;MACpCO,MAAM,EAAE,MAAM;MACdF,IAAI,EAAJA;IACF,CAAC,CAAC;EACJ,CAAC;EACDO,MAAM,kBAACP,IAAI,EAAE;IACX;IACA,OAAOP,OAAO,CAAC;MACbQ,GAAG,EAAEN,MAAM,GAAG,yBAAyB;MACvCO,MAAM,EAAE,MAAM;MACdF,IAAI,EAAJA;IACF,CAAC,CAAC;EACJ,CAAC;EACDQ,GAAG,eAACR,IAAI,EAAE;IACR;IACA,OAAOP,OAAO,CAAC;MACbQ,GAAG,EAAEN,MAAM,GAAG,yBAAyB;MACvCO,MAAM,EAAE,QAAQ;MAChBF,IAAI,EAAJA;IACF,CAAC,CAAC;EACJ;AACF,CAAC"}]}