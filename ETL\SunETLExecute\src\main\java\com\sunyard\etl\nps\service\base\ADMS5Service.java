package com.sunyard.etl.nps.service.base;

import java.sql.SQLException;
import java.util.ArrayList;
import java.util.List;

import org.sunyard.util.date.DateUtil;

import com.sunyard.etl.nps.common.NPSContants;
import com.sunyard.etl.nps.dao.FlMonDataTbDao;
import com.sunyard.etl.nps.dao.NpBusinessDataTbDao;
import com.sunyard.etl.nps.dao.OutputNopaperDao;
import com.sunyard.etl.nps.dao.ADMS5Dao;
import com.sunyard.etl.nps.model.BpTmpbatchTb;
import com.sunyard.etl.nps.model.BpTmpdata1Tb;
import com.sunyard.etl.nps.model.BpVoucherChkTb;
import com.sunyard.etl.nps.model.FlFlowTb;
import com.sunyard.etl.nps.model.NpImageData;
import com.sunyard.etl.system.model.JobParam;
import com.xxl.job.core.log.XxlJobLogger;

public class ADMS5Service {

	private String tableName;
	private OutputNopaperDao OutputNopaperDao = new OutputNopaperDao();
	private ADMS5Dao ADMS5Dao = new ADMS5Dao();
	private FlMonDataTbDao flMonDataTbDao = new FlMonDataTbDao();
	private NpBusinessDataTbDao npBusinessDataTbDao = new NpBusinessDataTbDao("tableName");

	public ADMS5Service(){
		
	}
	
	public ADMS5Service(String tableName) {
		this.tableName = tableName;
	}
	
	public boolean importFlowOver(String occurDate){
		XxlJobLogger.log("检查流水导入情况", tableName);
		return flMonDataTbDao.getByOccurDate(occurDate);
	}
	
	
	/**
	 * 
	 * @Title OutputBatch
	 * @Description 接入一个批次
	 * <AUTHOR>
	 * 2017年7月19日
	 * @param batchId
	 * @return
	 */
	public boolean outputBatch(BpTmpbatchTb bpTmpbatchTb,JobParam jobParam) {
		try {
			String batchId = bpTmpbatchTb.getBatchId();
			List<BpTmpdata1Tb> bpTmpdata1TbList = new ArrayList<BpTmpdata1Tb>();
			List<NpImageData> dataList = OutputNopaperDao.getADMS5DataList(batchId);
			if (dataList.size() == 0 || dataList == null) { 
				XxlJobLogger.log("NP_IMAGE_DATA_TB表中没有批次对应数据", tableName);
				return false;
			}
			
			// 拼装批次表
			String occurDate = dataList.get(0).getOccurDate();
			String siteNo =  dataList.get(0).getSiteNo();
			String operatorNo =  dataList.get(0).getOperator();
			bpTmpbatchTb.setBatchId(batchId);
			bpTmpbatchTb.setBatchTotalPage(dataList.size()+ "");
			bpTmpbatchTb.setBusinessId(Integer.parseInt(jobParam.getBusinessId()));
			bpTmpbatchTb.setOccurDate(occurDate);
			bpTmpbatchTb.setSiteNo(siteNo);
			bpTmpbatchTb.setOperatorNo(operatorNo);
			bpTmpbatchTb.setProgressFlag(NPSContants.ADMS5_PROCESS_FLAG);
			bpTmpbatchTb.setInputTime(DateUtil.getNewDate("HHmmss"));
			bpTmpbatchTb.setInputDate(dataList.get(0).getIndexValue());

			
			// 拼装数据表
			int inccodeinBatch = 1;
			int primaryInccodein = 1;
			for (NpImageData data : dataList) {
				BpTmpdata1Tb bpTmpdata1Tb = new BpTmpdata1Tb();
				if(data.getPsLevel().equals(NPSContants.ADMS5_PS_LEVEL_MAIN)){
					// 主件
					primaryInccodein = inccodeinBatch;
					bpTmpdata1Tb.setPrimaryInccodein(0);
					bpTmpdata1Tb.setCheckFlag("-1");
				} else {
					// 附件
					bpTmpdata1Tb.setPrimaryInccodein(primaryInccodein);
					bpTmpdata1Tb.setCheckFlag("0");
				}
				bpTmpdata1Tb.setDataSourceId(jobParam.getDataSourceId());
				bpTmpdata1Tb.setBatchId(batchId);
				bpTmpdata1Tb.setFlowId(data.getFlowId());
				bpTmpdata1Tb.setFormName(data.getFormName());
				bpTmpdata1Tb.setInccodeinBatch(inccodeinBatch);
				inccodeinBatch ++;
				bpTmpdata1Tb.setProcessState(NPSContants.ADMS5_PROCESS_STATE);
				bpTmpdata1Tb.setPsLevel(data.getPsLevel());
				bpTmpdata1Tb.setFileName(data.getFileName());
				bpTmpdata1Tb.setBackFileName(data.getBackFileName());
				bpTmpdata1Tb.setContentId(data.getContentId());
				bpTmpdata1TbList.add(bpTmpdata1Tb);
			}
			boolean result = false;
			//  插入前清理批次
			ADMS5Dao.cleanTmpBatch(batchId);
			ADMS5Dao.cleanTmpData(batchId);
			// 插入数据表
			result = ADMS5Dao.insertBpDataTb(bpTmpdata1TbList);
			if (!result) {
				XxlJobLogger.log("无纸化批次 " + batchId + " 插入数据表失败", tableName);
				return false;
			}
			/*
			 * 插入批次表
			 */
			result = ADMS5Dao.insertBpBatchTb(bpTmpbatchTb);
			if (!result) {
				XxlJobLogger.log("无纸化批次 " + batchId + "插入批次表失败", tableName);
				ADMS5Dao.cleanTmpBatch(batchId); // 删除数据表的数据
				return false;
			}
			/*
			 * 插入登记表
			 */
			BpVoucherChkTb bpVoucherChkTb = new BpVoucherChkTb();
			bpVoucherChkTb.setBusinessId(bpTmpbatchTb.getBusinessId());
			bpVoucherChkTb.setInputWorker("NPS");
			bpVoucherChkTb.setOccurDate(occurDate);
			bpVoucherChkTb.setOperatorNo(bpTmpbatchTb.getOperatorNo());
			bpVoucherChkTb.setRegDate(DateUtil.getNow());
			bpVoucherChkTb.setRegTime(DateUtil.getNewDate("HHmmss"));
			bpVoucherChkTb.setSiteNo(bpTmpbatchTb.getSiteNo());
			bpVoucherChkTb.setBatchId(batchId);
			bpVoucherChkTb.setScanVoucherNum(bpTmpbatchTb.getBatchTotalPage());
			bpVoucherChkTb.setRegVoucherNum(bpTmpbatchTb.getBatchTotalPage());
			bpVoucherChkTb.setMixedNPFlag(jobParam.getIsMix());
			result = ADMS5Dao.insertBpVoucherChk(bpVoucherChkTb);
			/*
			 * 勾兑流水
			 */
			List<String> flowList = new ArrayList<String>();
			for (NpImageData i : dataList) {
				String  flowId = i.getFlowId();
				if (flowList.contains(flowId)) {
					continue;
				} else {
					flowList.add(flowId);
				}
			}
			for (String flow : flowList) {
				String serialNo = ADMS5Dao.querySerialNo(batchId, flow);				
				List<FlFlowTb> flowTbList = ADMS5Dao.querySeqId(occurDate, siteNo, operatorNo, flow);
				if(flowTbList == null || flowTbList.size() == 0) {
					XxlJobLogger.log("批次号：" + batchId + "serialNo： " + serialNo + "勾兑流水表失败", tableName);
					continue;
				}
				result = ADMS5Dao.checkFlowStep1(occurDate,siteNo, operatorNo, flow, serialNo);
				if (!result) {
					XxlJobLogger.log("批次号：" + batchId + "serialNo： " + serialNo + "勾兑流水表失败", tableName);
					continue;
				}
				result = ADMS5Dao.checkFlowStep2(serialNo);
				if (!result) {
					XxlJobLogger.log("批次号：" + batchId + "serialNo： " + serialNo + "勾兑流水失败", tableName);
					continue;
				}
				
				// 如果流水已经被勾兑,释放之前的勾兑关系
				for(FlFlowTb flowTb : flowTbList){
					if(("1").equals(flowTb.getCheckFlag())){
						XxlJobLogger.log("批次号：" + batchId + "serialNo： " + serialNo + "对应的流水被其它凭证勾兑,开始释放之前的勾兑关系,", tableName);
						result = ADMS5Dao.checkFlowStep3(flowTb.getLserialNo());
						if (!result) {
							XxlJobLogger.log( "图像serialNo： " + flowTb.getLserialNo() + "的流水勾兑释放失败", tableName);
							continue;
						} else {
							XxlJobLogger.log( "图像serialNo： " + flowTb.getLserialNo() + "的流水勾兑释放成功", tableName);
						}
					}
				}
				
			}
		} catch (SQLException e) {
			e.printStackTrace();
			return false;
		}
		return true;
	}
	
	
	
	@SuppressWarnings("finally")
	public boolean AddBatch(BpTmpbatchTb mixedBatch,JobParam jobParam ) {
		List<BpTmpdata1Tb> bpTmpdata1TbList = new ArrayList<BpTmpdata1Tb>();
		List<NpImageData> dataList = null;
		String batchId = mixedBatch.getBatchId();
		String occurDate = mixedBatch.getOccurDate();
		String siteNo = mixedBatch.getSiteNo();
		String operatorNo = mixedBatch.getOperatorNo();
		String batchTotalPage = mixedBatch.getBatchTotalPage();
		try {
			dataList = OutputNopaperDao.getADMS5DataList(mixedBatch.getBatchId());
		} catch (SQLException e) {
			e.printStackTrace();
			return false;
		}
		if (dataList == null || dataList.size() == 0) {
			XxlJobLogger.log("NP_IMAGE_DATA_TB表中没有批次对应数据", tableName);
			return false;
		}
		/*
		 *  拼装数据表
		 */
		int inccodeinBatch = Integer.parseInt(batchTotalPage) + 1;
		for (NpImageData data : dataList) {
			BpTmpdata1Tb bpTmpdata1Tb = new BpTmpdata1Tb();
			if (data.getPsLevel().equals(NPSContants.ADMS5_PS_LEVEL_MAIN)) {
				// 主件
				bpTmpdata1Tb.setPrimaryInccodein(0);
				bpTmpdata1Tb.setCheckFlag("-1");
			} else {
				// 附件
				bpTmpdata1Tb.setPrimaryInccodein(0);
				bpTmpdata1Tb.setCheckFlag("0");
			}
			bpTmpdata1Tb.setDataSourceId(jobParam.getDataSourceId());
			bpTmpdata1Tb.setBatchId(batchId);
			bpTmpdata1Tb.setFlowId(data.getFlowId());
			bpTmpdata1Tb.setFormName(data.getFormName());
			bpTmpdata1Tb.setInccodeinBatch(inccodeinBatch);
			inccodeinBatch++;
			bpTmpdata1Tb.setProcessState(NPSContants.ADMS5_PROCESS_STATE);
			bpTmpdata1Tb.setPsLevel(data.getPsLevel());
			bpTmpdata1Tb.setFileName(data.getFileName());
			bpTmpdata1Tb.setBackFileName(data.getBackFileName());
			bpTmpdata1Tb.setContentId(data.getContentId());
			bpTmpdata1TbList.add(bpTmpdata1Tb);
		}
		boolean result = false;
		/*
		 *  插入数据表
		 */
		result = ADMS5Dao.insertBpDataTb(bpTmpdata1TbList);
		if (!result) {
			XxlJobLogger.log("无纸化批次 " + batchId + " 插入数据表失败", tableName);
			return false;
		}
		/*
		 * 更新批次表
		 */
		batchTotalPage = Integer.parseInt(batchTotalPage)  + dataList.size() + "";
		result = ADMS5Dao.updateBpTmpBatch(batchId,	batchTotalPage);
		if (!result) {
			XxlJobLogger.log("无纸化批次 " + batchId + "插入批次表失败", tableName);
			try {
				ADMS5Dao.cleanTmpBatch(batchId);// 删除数据表的数据
			} catch (SQLException e) {
				e.printStackTrace();
				XxlJobLogger.log("无纸化批次 " + batchId + "插入批次表失败", tableName);
			} finally {
				return false;
			}
		}
		/*
		 * 更新登记表
		 */
		result = ADMS5Dao.updateBpVoucherChk(batchId, batchTotalPage, batchTotalPage);
		if (!result) {
			XxlJobLogger.log("追加混合批次时，更新登记表失败", tableName);
		}
		/*
		 * 勾兑流水
		 */
		List<String> flowList = new ArrayList<String>();
		for (NpImageData i : dataList) {
			String flowId = i.getFlowId();
			if (flowList.contains(flowId)) {
				continue;
			} else {
				flowList.add(flowId);
			}
		}
		for (String flow : flowList) {
			try {
				String serialNo = ADMS5Dao.querySerialNo(batchId, flow);
				List<FlFlowTb> flowTbList = ADMS5Dao.querySeqId(occurDate, siteNo, operatorNo, flow);
				if(flowTbList == null || flowTbList.size() == 0) {
					XxlJobLogger.log("批次号：" + batchId + "serialNo： " + serialNo + "勾兑流水表失败", tableName);
					continue;
				}
				result = ADMS5Dao.checkFlowStep1(occurDate,siteNo, operatorNo, flow, serialNo);
				if (!result) {
					XxlJobLogger.log("批次号：" + batchId + "serialNo： " + serialNo + "勾兑流水表失败", tableName);
					continue;
				}
				result = ADMS5Dao.checkFlowStep2(serialNo);
				if (!result) {
					XxlJobLogger.log("批次号：" + batchId + "serialNo： " + serialNo + "勾兑流水失败", tableName);
					continue;
				}
				
				// 如果流水已经被勾兑,释放之前的勾兑关系
				for(FlFlowTb flowTb : flowTbList){
					if(("1").equals(flowTb.getCheckFlag())){
						XxlJobLogger.log("批次号：" + batchId + "serialNo： " + serialNo + "对应的流水被其它凭证勾兑,开始释放之前的勾兑关系,", tableName);
						result = ADMS5Dao.checkFlowStep3(flowTb.getLserialNo());
						if (!result) {
							XxlJobLogger.log( "图像serialNo： " + flowTb.getLserialNo() + "的流水勾兑释放失败", tableName);
							continue;
						} else {
							XxlJobLogger.log( "图像serialNo： " + flowTb.getLserialNo() + "的流水勾兑释放成功", tableName);
						}
					}
				}
				
				
			} catch (SQLException e) {
				e.printStackTrace();
				XxlJobLogger.log("批次号：" + batchId + "勾兑流水表失败", tableName);
			}					
		}
		return true;
	}
	

	public void generateBatchId(String dataSourceId) throws SQLException {
		List<String> batchList = npBusinessDataTbDao.getBatch(dataSourceId);
		if (batchList.size() < 0) {
			return ;
		}
		for (String i : batchList) {
			String batchId = DateUtil.generateBatchId();
			String occurDate = i.split(",")[0];
			String siteNo = i.split(",")[1];
			String operatorNo = i.split(",")[2];
			
			int leave = npBusinessDataTbDao.checkLeave(occurDate, siteNo, operatorNo);
			if(leave > 0){
				XxlJobLogger.log("日期" + occurDate + "网点" + siteNo +"柜员 "+ operatorNo + "有下图失败业务暂不生成批次", tableName);
				continue;
			}
			npBusinessDataTbDao.updateBatchId(batchId, occurDate,siteNo, operatorNo, dataSourceId);
		}
	}


}
