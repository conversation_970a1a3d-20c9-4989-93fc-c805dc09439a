{"remainingRequest": "E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1\\数字运营平台-统一门户工程\\dopUnify\\node_modules\\babel-loader\\lib\\index.js!E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1\\数字运营平台-统一门户工程\\dopUnify\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1\\数字运营平台-统一门户工程\\dopUnify\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1\\数字运营平台-统一门户工程\\dopUnify\\src\\views\\system\\notice\\approval\\component\\table\\index.vue?vue&type=script&lang=js&", "dependencies": [{"path": "E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1\\数字运营平台-统一门户工程\\dopUnify\\src\\views\\system\\notice\\approval\\component\\table\\index.vue", "mtime": 1716875178192}, {"path": "E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1\\数字运营平台-统一门户工程\\dopUnify\\babel.config.js", "mtime": 1716875191548}, {"path": "E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1\\数字运营平台-统一门户工程\\dopUnify\\.babelrc", "mtime": 1716875191502}, {"path": "E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1\\数字运营平台-统一门户工程\\dopUnify\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1\\数字运营平台-统一门户工程\\dopUnify\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1\\数字运营平台-统一门户工程\\dopUnify\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1\\数字运营平台-统一门户工程\\dopUnify\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1655715099000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA+DA,SACAA,kBACAC,eACAC,wBACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;EAAAC;EAAAC;AACA;AACA;EACAC;EACAC;IAAAC;EAAA;EACAC;IACAC;MACA;MACAC;QACAC;MACA;MACA;IACA;IACAC;MACA;IACA;EACA;EACAC;EACAC;IACAC;MACAC;MACAC;QACA;MACA;IACA;IACAC;MACAF;MACAC;QACA;MACA;IACA;EACA;EACAE;IACA;MACAC;QACAC;QACAC;QACAC;QACAC;QAAA;QACAC;QAAA;QACAC;UACAP;UAAA;UACAQ;UACAC;QACA;;QACAC;QAAA;QACAC;UACAC;UACAC;UAAA;UACAC;QACA;MACA;;MACAC;QACAC;UACAC;QACA;MACA;MACAC;QACAC;UACAF;QACA;QACAG;UACAH;QACA;MACA;MACAI;QACAC;QAAA;QACAC;QAAA;QACAC;QAAA;QACAjB;UACA;UACAkB;UACAC;QACA;;QACAC;UACAC;UAAA;UACAH;UAAA;UACAI;UAAA;UACAC;UAAA;UACAC;QACA;MACA;;MACAC;QACAV;QACAf;UACA;UACAkB;UAAA;UACAC;QACA;;QACAO;UACA;UACAC;UAAA;UACAtC;YACAuC;UACA;UAAA;UACAC;QACA;MACA;IACA;EACA;;EACAC;IACA;IACA;IACA;EAAA,CACA;EACAC;IACA/C;EACA;EACAgD;IACA;EACA;EACAC;IAAA;IACA;IACA;MACA;QACA;MACA;IACA;EACA;EACAC;IACA;IACA;EACA;EACAC;IACA;IACAC;MACA;MACA;QACAjC;UACA;QACA;MACA;;MACA;IACA;IACA;AACA;IACAkC;MAAA;QAAAC;MACAC;IACA;IACA;AACA;IACAC;MACA;IACA;IACA;AACA;IACAC;MAAA;MACA;MACA;QACAC;QACAC;QACAC;QAAA;QACAC;QACAC;QACAC;QAAA;QACA;QACAC,eACA,yCACA,iDACA,KACA;QAAA,EACA;QACAC,eACA,yCACA,iDACA,KACA;QAAA,EACA;QACAC;QAAA;QACA5C;QACAC;MACA;MACA9B,WACA0E;QACA;QACA;QACA;QACA,yDACAC,qBACA;QACA;MACA,GACAC;QACA;MACA;IACA;IACA;AACA;IACAC;MAAA;MACA;MACA;QACA/E;QACA;MACA;MACA;QACAA;QACA;MACA;MACA;MACA;MACA;QACAmE;QACAC;QACAC;QAAA;QACAC;QACAC;QACAS;QACAC;QACAlD;QACA4C;QAAA;QACA3C;MACA;MACA9B;QACA;QACA;QACA;QACA;QACA;UACA;QACA;UACA;QACA;MACA;MACA;IACA;IACA;AACA;IACAgF;MAAA;MACAjF;QACA;UACA;UACA;YACAkF;YACAC;YACAC;YACAC;YACAC;YACAC;YACAC;YACAC;YACAC;YACAC;YACAC;YACAC;YACAb;YACAD;UACA;UACA;YACAb;YACAG;YACAjB;YAAA;YACAtC;YAAA;YACAgF;UACA;UACA5F;YACAJ;YACA;YACA;YACA;YACA;UACA;QACA;MACA;IACA;IACA;AACA;IACAiG;MACA;IACA;IACA;AACA;AACA;IACAC;MACA;IACA;IACA;AACA;AACA;IACAC;MACA;IACA;IACA;AACA;IACAC;MAAA;MACAlG;QACA;UACA;UACA;YACAkF;YACAC;YACAC;YACAC;YACAC;YACAC;YACAC;YACAC;YACAC;YACAC;YACAC;YACAC;YACAb;YACAD;UACA;UACA;YACAb;YACAG;YACAjB;YAAA;YACAtC;YAAA;YACAgF;UACA;UACA5F;YACAJ;YACA;YACA;YACA;YACA;UACA;QACA;MACA;IACA;IACA;AACA;AACA;IACAqG;MACA;IACA;EACA;AACA", "names": ["commonMsgSuccess", "commonMsgWarn", "commonMsgConfirm", "query", "Approve", "name", "components", "SunNoticeDialog", "filters", "roleNoFormat", "that", "itemName", "paperType", "mixins", "props", "defaultForm", "type", "default", "rolelist", "data", "table", "columns", "ref", "loading", "selection", "indexNumber", "componentProps", "height", "formRow", "currentRow", "pageList", "totalNum", "currentPage", "pageSize", "btnDatas", "btnApproval", "show", "dialogBtnDatas", "btnPass", "btnRefuse", "dialog", "visible", "btnCancle", "btnSubmit", "title", "width", "noticeConfig", "imgSrc", "info", "content", "files", "dialogForm", "form", "config", "remark", "labelWidth", "watch", "beforeCreate", "created", "mounted", "<PERSON><PERSON><PERSON><PERSON>", "methods", "handleSelectionChange", "rowClassName", "rowIndex", "row", "getList", "queryList", "parameterList", "organ_no", "organ_type", "user_no", "role_no", "notice_keyword", "publish_time1", "publish_time2", "method", "then", "retMap", "catch", "handleApproval", "inst_id", "item_id", "handlePass", "notice_id", "notice_title", "notice_content", "msg_type", "notice_level", "organs", "roles", "users", "file_url", "publish_organ", "publish_user", "publish_time", "oper_type", "handleRefuse", "changeVisible", "changeVisibleForm", "dialogRefuseSubmit", "showLoading"], "sourceRoot": "src/views/system/notice/approval/component/table", "sources": ["index.vue"], "sourcesContent": ["<template>\r\n  <div class=\"sun-content\">\r\n    <sun-table\r\n      :table-config=\"table\"\r\n      @selection-change=\"handleSelectionChange\"\r\n      @pagination=\"getList\"\r\n    >\r\n      <template slot=\"tableColumn\">\r\n        <el-table-column\r\n          v-for=\"item in table.columns\"\r\n          :key=\"item.id\"\r\n          :prop=\"item.name\"\r\n          :label=\"item.label\"\r\n          :width=\"item.width\"\r\n        >\r\n          <div slot-scope=\"{ row }\">\r\n            <span v-if=\"item.name === 'publish_time'\">{{\r\n              row[item.name] | dateTimeFormat\r\n            }}</span>\r\n            <span v-else-if=\"item.name === 'notice_level'\">{{\r\n              row[item.name] | commonFormatValue('NOTICE_LEVEL')\r\n            }}</span>\r\n            <span v-else-if=\"item.name === 'notice_state'\">\r\n              <el-tag v-if=\"row[item.name] === '0'\" type=\"warning\">\r\n                {{ row[item.name] | commonFormatValue('NOTICE_STATE') }}</el-tag>\r\n              <el-tag v-if=\"row[item.name] === '1'\" type=\"success\">\r\n                {{ row[item.name] | commonFormatValue('NOTICE_STATE') }}</el-tag>\r\n              <el-tag v-if=\"row[item.name] === '2'\">\r\n                {{ row[item.name] | commonFormatValue('NOTICE_STATE') }}</el-tag>\r\n              <el-tag v-if=\"row[item.name] === '3'\">\r\n                {{ row[item.name] | commonFormatValue('NOTICE_STATE') }}</el-tag>\r\n            </span>\r\n            <span v-else-if=\"item.name === 'publish_organ'\">{{\r\n              row[item.name] | organNameFormat\r\n            }}</span>\r\n            <span v-else>{{ row[item.name] }}</span>\r\n          </div>\r\n        </el-table-column>\r\n      </template>\r\n      <template slot=\"customButton\">\r\n        <sun-button :btn-datas=\"btnDatas\" @handleApproval=\"handleApproval\" />\r\n      </template>\r\n    </sun-table>\r\n    <!--公告弹出框begin-->\r\n    <sun-notice-dialog :dialog-config=\"dialog\" @dialogClose=\"changeVisible\">\r\n      <div slot=\"rightBtn\" class=\"dialog-footer\">\r\n        <sun-button\r\n          :btn-datas=\"dialogBtnDatas\"\r\n          @handlePass=\"handlePass\"\r\n          @handleRefuse=\"handleRefuse\"\r\n        />\r\n      </div>\r\n    </sun-notice-dialog>\r\n    <!--公告弹出框end-->\r\n    <sun-form-dialog\r\n      ref=\"refDialog\"\r\n      :dialog-config=\"dialogForm\"\r\n      @dialogClose=\"changeVisibleForm\"\r\n      @dialogSubmit=\"dialogRefuseSubmit\"\r\n    />\r\n  </div>\r\n</template>\r\n<script>\r\nimport {\r\n  commonMsgSuccess,\r\n  commonMsgWarn,\r\n  commonMsgConfirm\r\n} from '@/utils/message.js' // 提示信息\r\n// import { dateNowFormat } from '@/utils/date.js' // 日期格式化\r\nimport { dictionaryGet } from '@/utils/dictionary.js' // 字典常量\r\nimport ResizeMixin from '@/utils/ResizeHandler' // 整体页面是否根据总高配置\r\nimport { uploadFile } from '@/utils/common' // 公共方法\r\n\r\nimport { configTable, formConfig } from './info' // 表头、表单配置\r\nimport { SunNoticeDialog } from '@/components' // 公告弹窗\r\n\r\nimport { Common } from '@/api'\r\nconst { query, Approve } = Common.SysApproval\r\nlet that\r\nexport default {\r\n  name: 'TableList',\r\n  components: { SunNoticeDialog },\r\n  filters: {\r\n    roleNoFormat: (role_no) => {\r\n      let itemName = null\r\n      that.$emit('roleNoFormat', role_no, (val) => {\r\n        itemName = val\r\n      })\r\n      return itemName\r\n    },\r\n    paperType() {\r\n      return\r\n    }\r\n  },\r\n  mixins: [ResizeMixin],\r\n  props: {\r\n    defaultForm: {\r\n      type: Object,\r\n      default: function() {\r\n        return {}\r\n      }\r\n    },\r\n    rolelist: {\r\n      type: Array,\r\n      default: function() {\r\n        return []\r\n      }\r\n    }\r\n  },\r\n  data() {\r\n    return {\r\n      table: {\r\n        columns: configTable(),\r\n        ref: 'tableRef',\r\n        loading: false,\r\n        selection: true, // 复选\r\n        indexNumber: true, // 序号\r\n        componentProps: {\r\n          data: [], // 表格数据\r\n          height: '100px',\r\n          formRow: 0 // 表单行数\r\n        },\r\n        currentRow: [], // 选中行\r\n        pageList: {\r\n          totalNum: 0,\r\n          currentPage: 1, // 当前页\r\n          pageSize: this.$store.getters.pageSize // 当前页显示条数\r\n        }\r\n      },\r\n      btnDatas: {\r\n        btnApproval: {\r\n          show: this.$attrs['btn-all'].btnApproval\r\n        }\r\n      },\r\n      dialogBtnDatas: {\r\n        btnPass: {\r\n          show: true\r\n        },\r\n        btnRefuse: {\r\n          show: true\r\n        }\r\n      },\r\n      dialog: {\r\n        visible: false, // 开启/关闭弹窗\r\n        btnCancle: false, // 取消按钮\r\n        btnSubmit: false, // 确定按钮\r\n        componentProps: {\r\n          // 弹出框配置属性\r\n          title: '公告审批',\r\n          width: '74.8rem' // 当前弹出框宽度\r\n        },\r\n        noticeConfig: {\r\n          imgSrc: require('@/assets/img/other/notice_images/noticeBackground.png'), // 背景图片加载\r\n          title: '标题', // 公告标题\r\n          info: [], // 发布机构、发布者名称、发布时间\r\n          content: '', // 公告正文\r\n          files: [] // 附件\r\n        }\r\n      },\r\n      dialogForm: {\r\n        visible: false,\r\n        componentProps: {\r\n          // 弹出框属性\r\n          title: '拒绝说明', // 弹出框标题\r\n          width: '50rem' // 当前弹出框宽度 默认80%\r\n        },\r\n        form: {\r\n          // 表单属性\r\n          config: formConfig(this), // 表单项配置\r\n          defaultForm: {\r\n            remark: ''\r\n          }, // 默认值配置\r\n          labelWidth: '10rem' // 当前表单标签宽度配置\r\n        }\r\n      }\r\n    }\r\n  },\r\n  watch: {\r\n    // loading(value) {\r\n    //   this.listLoading = this.loading\r\n    // }\r\n  },\r\n  beforeCreate: function() {\r\n    that = this\r\n  },\r\n  created() {\r\n    this.listLoading = this.loading\r\n  },\r\n  mounted() {\r\n    // 公告流程审批\r\n    this.$bus.$on('noticeAproveUpdate', (data) => {\r\n      if (data) {\r\n        this.queryList()\r\n      }\r\n    })\r\n  },\r\n  beforeDestroy() {\r\n    // 组件销毁之前，清除事件总线\r\n    this.$bus.$off('noticeAproveUpdate')\r\n  },\r\n  methods: {\r\n    // 表格选择多行\r\n    handleSelectionChange(val) {\r\n      const currentRow = val\r\n      if (currentRow.length > 1) {\r\n        currentRow.sort(function(a, b) {\r\n          return a.index - b.index\r\n        }) // 选中行排序\r\n      }\r\n      this.table.currentRow = val\r\n    },\r\n    /**\r\n     * 行的 className 的回调方法，也可以使用字符串为所有行设置一个固定的 className*/\r\n    rowClassName({ row, rowIndex }) {\r\n      row.index = rowIndex // 将索引放置到row数据中\r\n    },\r\n    /**\r\n     *页码更新 */\r\n    getList(param) {\r\n      this.queryList(param.currentPage)\r\n    },\r\n    /**\r\n     * 按钮：查询*/\r\n    queryList(currentPage) {\r\n      this.showLoading()\r\n      const msg = {\r\n        parameterList: [],\r\n        organ_no: this.$store.getters.organNo,\r\n        organ_type: this.$store.getters.organLevel, // 机构级别\r\n        user_no: this.$store.getters.userNo,\r\n        role_no: this.$store.getters.roleNo,\r\n        notice_keyword: this.defaultForm.notice_keyword, // 公告标题关键字\r\n        // 发布日期处理\r\n        publish_time1:\r\n          this.defaultForm.publish_time !== null\r\n            ? this.defaultForm.publish_time[0] === undefined\r\n              ? ''\r\n              : this.defaultForm.publish_time[0] + '000000' // 格式化 yyyyMMddHHmmss\r\n            : '',\r\n        publish_time2:\r\n          this.defaultForm.publish_time !== null\r\n            ? this.defaultForm.publish_time[1] === undefined\r\n              ? ''\r\n              : this.defaultForm.publish_time[1] + '240000' // 格式化 yyyyMMddHHmmss\r\n            : '',\r\n        method: 'getTask', // 固定字段值\r\n        currentPage: currentPage || this.table.pageList.currentPage,\r\n        pageSize: this.table.pageList.pageSize\r\n      }\r\n      query(msg)\r\n        .then((response) => {\r\n          const { retMap } = response\r\n          this.table.componentProps.data = retMap.list\r\n          this.table.pageList.totalNum = retMap.totalNum ? retMap.totalNum : 0\r\n          this.table.pageList.currentPage = retMap.currentPage\r\n            ? retMap.currentPage\r\n            : this.table.pageList.currentPage\r\n          this.showLoading()\r\n        })\r\n        .catch(() => {\r\n          this.showLoading()\r\n        })\r\n    },\r\n    /**\r\n     * btn - 审批*/\r\n    handleApproval() {\r\n      const rows = this.table.currentRow\r\n      if (rows.length === 0) {\r\n        commonMsgWarn('请选择审批的公告!', this)\r\n        return\r\n      }\r\n      if (rows.length > 2) {\r\n        commonMsgWarn('不支持多条审批，请重新选择审批的公告!', this)\r\n        return\r\n      }\r\n      this.dialog.visible = true\r\n      // 查询公告明细\r\n      const msg = {\r\n        parameterList: [],\r\n        organ_no: this.$store.getters.organNo,\r\n        organ_type: this.$store.getters.organLevel, // 机构级别\r\n        user_no: this.$store.getters.userNo,\r\n        role_no: this.$store.getters.roleNo,\r\n        inst_id: rows[0].inst_id,\r\n        item_id: rows[0].item_id,\r\n        currentPage: 1,\r\n        method: 'getTask', // 固定字段值\r\n        pageSize: this.table.pageList.pageSize\r\n      }\r\n      query(msg).then((response) => {\r\n        const { list } = response.retMap\r\n        this.dialog.noticeConfig.title = list[0].notice_title // 标题\r\n        this.dialog.noticeConfig.info = list\r\n        this.dialog.noticeConfig.content = list[0].notice_content // 公告内容\r\n        if (list[0].file_url) {\r\n          this.dialog.noticeConfig.files = uploadFile(list[0].file_url)\r\n        } else {\r\n          this.dialog.noticeConfig.files = []\r\n        }\r\n      })\r\n      this.changeVisible(true)\r\n    },\r\n    /**\r\n     * btn - 通过*/\r\n    handlePass() {\r\n      commonMsgConfirm('确认通过审批？', this, (flag) => {\r\n        if (flag) {\r\n          const row = this.table.currentRow[0]\r\n          const json = {\r\n            notice_id: row.notice_id,\r\n            notice_title: row.notice_title,\r\n            notice_content: row.notice_content,\r\n            msg_type: row.msg_type,\r\n            notice_level: row.notice_level,\r\n            organs: row.organs,\r\n            roles: row.roles,\r\n            users: row.users,\r\n            file_url: row.file_url,\r\n            publish_organ: row.publish_organ,\r\n            publish_user: row.publish_user,\r\n            publish_time: row.publish_time,\r\n            item_id: row.item_id,\r\n            inst_id: row.inst_id\r\n          }\r\n          const msg = {\r\n            parameterList: [json],\r\n            user_no: this.$store.getters.userNo,\r\n            remark: this.dialogForm.form.defaultForm.remark, // 表单数据：拒绝说明\r\n            type: '1', // 通过的标识\r\n            oper_type: dictionaryGet('OPERATE_OTHER')\r\n          }\r\n          Approve(msg).then((response) => {\r\n            commonMsgSuccess(response.retMsg, this)\r\n            this.queryList(1)\r\n            this.$bus.$emit('noticeAproveUpdate', true)\r\n            this.changeVisibleForm(false)\r\n            this.changeVisible(false)\r\n          })\r\n        }\r\n      })\r\n    },\r\n    /**\r\n     * btn - 拒绝*/\r\n    handleRefuse() {\r\n      this.dialogForm.visible = true\r\n    },\r\n    /**\r\n     * 弹出框 - 关闭\r\n     * @param {Boolean} param 弹出框显示隐藏配置*/\r\n    changeVisible(param) {\r\n      this.dialog.visible = param\r\n    },\r\n    /**\r\n     * 拒绝说明弹出框 - 关闭\r\n     * @param {Boolean} param 弹出框显示隐藏配置*/\r\n    changeVisibleForm(param) {\r\n      this.dialogForm.visible = param\r\n    },\r\n    /**\r\n     * 拒绝说明弹出框 - 确认*/\r\n    dialogRefuseSubmit() {\r\n      commonMsgConfirm('确认拒绝审批？', this, (flag) => {\r\n        if (flag) {\r\n          const row = this.table.currentRow[0]\r\n          const json = {\r\n            notice_id: row.notice_id,\r\n            notice_title: row.notice_title,\r\n            notice_content: row.notice_content,\r\n            msg_type: row.msg_type,\r\n            notice_level: row.notice_level,\r\n            organs: row.organs,\r\n            roles: row.roles,\r\n            users: row.users,\r\n            file_url: row.file_url,\r\n            publish_organ: row.publish_organ,\r\n            publish_user: row.publish_user,\r\n            publish_time: row.publish_time,\r\n            item_id: row.item_id,\r\n            inst_id: row.inst_id\r\n          }\r\n          const msg = {\r\n            parameterList: [json],\r\n            user_no: this.$store.getters.userNo,\r\n            remark: this.dialogForm.form.defaultForm.remark, // 表单数据：拒绝说明\r\n            type: '0', // 拒绝的标识\r\n            oper_type: dictionaryGet('OPERATE_OTHER')\r\n          }\r\n          Approve(msg).then((response) => {\r\n            commonMsgSuccess(response.retMsg, this)\r\n            this.queryList(1)\r\n            this.$bus.$emit('noticeAproveUpdate', true)\r\n            this.changeVisibleForm(false)\r\n            this.changeVisible(false)\r\n          })\r\n        }\r\n      })\r\n    },\r\n    /**\r\n     * 加载中动画配置\r\n     * @param {Boolean}param 当前加载显示状态*/\r\n    showLoading(param) {\r\n      this.table.loading = param\r\n    }\r\n  }\r\n}\r\n</script>\r\n<style lang=\"scss\" scoped>\r\n// 新增、修改时，“配置提示”input样式修改\r\n::v-deep {\r\n  .el-form {\r\n    .el-row {\r\n      > div {\r\n        &:nth-last-child(1) {\r\n          input {\r\n            border: 0;\r\n            padding-left: 0;\r\n          }\r\n        }\r\n      }\r\n    }\r\n  }\r\n}\r\n</style>\r\n"]}]}