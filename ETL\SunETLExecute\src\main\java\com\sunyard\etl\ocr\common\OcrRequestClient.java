package com.sunyard.etl.ocr.common;

import com.fasterxml.jackson.databind.ObjectMapper;

import java.io.*;
import java.net.HttpURLConnection;
import java.net.URL;
import java.net.URLEncoder;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import com.google.json.JsonSanitizer;
import com.sunyard.etl.nps.common.Parameters;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
/**
 * OCR识别请求消息发送类
 * <AUTHOR>
 *
 */
public class OcrRequestClient {

	private static Logger logger = LoggerFactory.getLogger(OcrRequestClient.class);
	// 连接超时，单位毫秒
	public static int CONNECT_TIME_OUT = 10000;
	// 读取超时，单位毫秒
	public static int READ_TIME_OUT = 10000;
	// 通信编码
	public static String CHAR_SET = OCRConstants.MESSAGE_ENCODING;

	public static String sendPost(String url, String param) throws Exception {
		String result = "";
		PrintWriter out = null;
		BufferedReader in = null;

		InputStream is = null;
		InputStreamReader isr = null;

		OutputStream os = null;
		OutputStreamWriter osw = null;
		try {
			URL httpUrl = new URL(url);
			// 获取URL连接
			HttpURLConnection httpConn = (HttpURLConnection) httpUrl.openConnection();
			// 设置通用的请求属性
			httpConn.setRequestProperty("accept", "*/*");
			httpConn.setRequestProperty("CHAR_SET", CHAR_SET);
			httpConn.setRequestProperty("connection", "Keep-Alive");
			httpConn.setRequestProperty("Content-Type", "application/x-www-form-urlencoded");
			httpConn.setConnectTimeout(CONNECT_TIME_OUT);
			httpConn.setReadTimeout(READ_TIME_OUT);
			httpConn.setRequestMethod("POST");

			// 发送POST请求必须设置如下两行
			httpConn.setDoOutput(true);
			httpConn.setDoInput(true);
			// POST请求不能使用缓存
			httpConn.setUseCaches(false);
			// 建立URL连接
			httpConn.connect();
			// 获取URLConnection对象对应的输出流
			os = httpConn.getOutputStream();
			osw = new OutputStreamWriter(os,CHAR_SET);
			out = new PrintWriter(osw);
			// 发送请求参数
			out.print(param);
			// flush输出流的缓冲
			out.flush();
			// 定义BufferedReader输入流来读取URL的响应，请求异常时读取错误信息
			if (httpConn.getResponseCode() != HttpURLConnection.HTTP_OK) {
				logger.error("http请求异常");
				is = httpConn.getErrorStream();
			} else {
				is = httpConn.getInputStream();
			}
			isr = new InputStreamReader(is , CHAR_SET);
			in = new BufferedReader(isr);
			// 读取返回信息
			String line;
			while ((line = in.readLine()) != null) {
				result += line;
			}
		} finally {
			// 关闭输出流、输入流
			try {
				if (out != null) {
					out.close();
				}
				if (osw != null) {
					osw.close();
				}
				if (os != null) {
					os.close();
				}
				if (in != null) {
					in.close();
				}
				if (isr != null) {
					isr.close();
				}
				if (is != null) {
					is.close();
				}
			} catch (IOException ex) {
				logger.error("关闭http输入、输出流异常", ex);
			}
		}
		return result;
	}


	@SuppressWarnings({ "rawtypes", "unchecked" })
	public static void main(String[] args) throws Exception {
		List parameterList = new ArrayList();
		Map<String, Object> batchInfo = new HashMap<String, Object>();
		//批次信息
		batchInfo.put("batchId", "20180806101302107960");//后督批次号
		batchInfo.put("inputDate", "20180806");//扫描日期
		batchInfo.put("siteNO", "000001");//交易机构
		batchInfo.put("channelId", "001"); //渠道号
		parameterList.add(batchInfo); //建议只放1个map,识别平台只存了第一个。

//		 按序号识别
		Map<String, Object> sysMap = new HashMap<String, Object>();
		sysMap.put("chan_id", "CHAN_202502141705366607"); //渠道号
		sysMap.put("task_type", "1"); //是否回调 0-否 ，1-是
		sysMap.put("doc_id", "20250226175844722801");
		sysMap.put("organ_no", "00023"); //机构号
		sysMap.put("ecmDate", "20250226");
		sysMap.put("taskId", "20250325014604212");

		Map<String, Object> msgMap = new HashMap<String, Object>();
		msgMap.put("parameterList", parameterList);
		msgMap.put("sysMap", sysMap);

//		String url = "http://localhost:8080/SunARS/userRoleSyn.do";
		String url = "http://127.0.0.1:9000/SunFA/api/SunFA/asyncTask/createAsyncTask.do";
//		String url = "http://localhost:8099/etl";
		ObjectMapper objectMapper = new ObjectMapper();
		//String message = objectMapper.writeValueAsString(msgMap);
		String message = objectMapper.writeValueAsString(sysMap);
		message = JsonSanitizer.sanitize(message);
		//String backMessge = sendPostToSunFA(url, "message="+ URLEncoder.encode(message, OCRConstants.MESSAGE_ENCODING));
		String backMessge = sendPostToSunFA(url, message);
		System.out.println(backMessge);
	}
	public static String sendPostToSunFA(String url, String param) throws Exception {
		String result = "";
		PrintWriter out = null;
		BufferedReader in = null;

		InputStream is = null;
		InputStreamReader isr = null;

		OutputStream os = null;
		OutputStreamWriter osw = null;
		try {
			URL httpUrl = new URL(url);
			// 获取URL连接
			HttpURLConnection httpConn = (HttpURLConnection) httpUrl.openConnection();
			// 设置通用的请求属性
			httpConn.setRequestProperty("accept", "*/*");
			httpConn.setRequestProperty("connection", "Keep-Alive");
			httpConn.addRequestProperty("Content-Type", "application/json");
			// 发送POST请求必须设置如下两行
			httpConn.setDoOutput(true);
			httpConn.setDoInput(true);
			// POST请求不能使用缓存
			// 建立URL连接
			httpConn.connect();
			// 获取URLConnection对象对应的输出流
			os = httpConn.getOutputStream();
			osw = new OutputStreamWriter(os,CHAR_SET);
			out = new PrintWriter(osw);
			// 发送请求参数
			out.print(param);
			// flush输出流的缓冲
			out.flush();
			// 定义BufferedReader输入流来读取URL的响应，请求异常时读取错误信息
			if (httpConn.getResponseCode() != HttpURLConnection.HTTP_OK) {
				logger.error("http请求异常");
				is = httpConn.getErrorStream();
			} else {
				is = httpConn.getInputStream();
			}
			isr = new InputStreamReader(is , CHAR_SET);
			in = new BufferedReader(isr);


			// 读取返回信息
			String line;
			while ((line = in.readLine()) != null) {
				result += line;
			}
		} finally {
			// 关闭输出流、输入流
			try {
				if (out != null) {
					out.close();
				}
				if (osw != null) {
					osw.close();
				}
				if (os != null) {
					os.close();
				}
				if (in != null) {
					in.close();
				}
				if (isr != null) {
					isr.close();
				}
				if (is != null) {
					is.close();
				}
			} catch (IOException ex) {
				logger.error("关闭http输入、输出流异常", ex);
			}
		}
		return result;
	}
}
