{"remainingRequest": "D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\node_modules\\babel-loader\\lib\\index.js!D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\node_modules\\eslint-loader\\index.js??ref--13-0!D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\src\\views\\system\\notice\\release\\info.js", "dependencies": [{"path": "D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\src\\views\\system\\notice\\release\\info.js", "mtime": 1686019808701}, {"path": "D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\babel.config.js", "mtime": 1686019818359}, {"path": "D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\.babelrc", "mtime": 1705285078668}, {"path": "D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\node_modules\\eslint-loader\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8g6KGo5Y2VCmV4cG9ydCB2YXIgY29uZmlnID0gZnVuY3Rpb24gY29uZmlnKHRoYXQpIHsKICByZXR1cm4gewogICAgbm90aWNlX2tleXdvcmQ6IHsKICAgICAgY29tcG9uZW50OiAnaW5wdXQnLAogICAgICBsYWJlbDogJ+WFrOWRiuWFs+mUruWtlycsCiAgICAgIGNvbFNwYW46IDgsCiAgICAgIG5hbWU6ICdub3RpY2Vfa2V5d29yZCcsCiAgICAgIGNvbmZpZzogewogICAgICAgIC8vIGZvcm0taXRlbSDphY3nva4KICAgICAgICBydWxlczogW3sKICAgICAgICAgIG1pbjogMCwKICAgICAgICAgIG1heDogNjAsCiAgICAgICAgICBtZXNzYWdlOiAn6K+35pyA5aSa5aGr5YaZNjDkuKrlrZfnrKYnCiAgICAgICAgfV0KICAgICAgfSwKICAgICAgY29tcG9uZW50UHJvcHM6IHsKICAgICAgICAvLyBpbnB1dOe7hOS7tumFjee9rgogICAgICAgIHBsYWNlaG9sZGVyOiAn5pSv5oyB5oyJ5YWs5ZGK5YWz6ZSu5a2X5qih57OK5p+l6K+iJywKICAgICAgICBjbGVhcmFibGU6IHRydWUKICAgICAgfQogICAgfSwKICAgIHB1Ymxpc2hfdGltZTogewogICAgICBjb21wb25lbnQ6ICdkYXRlLXBpY2tlcicsCiAgICAgIGxhYmVsOiAn5Y+R5biD5pe26Ze0JywKICAgICAgY29sU3BhbjogOCwKICAgICAgbmFtZTogJ3B1Ymxpc2hfdGltZScsCiAgICAgIGNvbmZpZzoge30sCiAgICAgIGNvbXBvbmVudFByb3BzOiB7CiAgICAgICAgZmlsdGVyYWJsZTogdHJ1ZSwKICAgICAgICB0eXBlOiAnZGF0ZXJhbmdlJywKICAgICAgICBjbGVhcmFibGU6IHRydWUsCiAgICAgICAgc3RhcnRQbGFjZWhvbGRlcjogJ+W8gOWni+aXpeacnycsCiAgICAgICAgZW5kUGxhY2Vob2xkZXI6ICfnu5PmnZ/ml6XmnJ8nLAogICAgICAgIHZhbHVlRm9ybWF0OiAneXl5eU1NZGQnCiAgICAgIH0KICAgIH0sCiAgICBwdWJsaXNoX3VzZXI6IHsKICAgICAgY29tcG9uZW50OiAnaW5wdXQnLAogICAgICBsYWJlbDogJ+WPkeW4g+eUqOaItycsCiAgICAgIGNvbFNwYW46IDgsCiAgICAgIG5hbWU6ICdwdWJsaXNoX3VzZXInLAogICAgICBjb25maWc6IHsKICAgICAgICAvLyBmb3JtLWl0ZW0g6YWN572uCiAgICAgICAgcnVsZXM6IFt7CiAgICAgICAgICBtaW46IDAsCiAgICAgICAgICBtYXg6IDEwLAogICAgICAgICAgbWVzc2FnZTogJ+ivt+acgOWkmuWhq+WGmTEw5Liq5a2X56ymJwogICAgICAgIH1dCiAgICAgIH0sCiAgICAgIGNvbXBvbmVudFByb3BzOiB7CiAgICAgICAgLy8gaW5wdXTnu4Tku7bphY3nva4KICAgICAgICBwbGFjZWhvbGRlcjogJ+aUr+aMgeaMieWPkeW4g+eUqOaIt+aooeeziuafpeivoicsCiAgICAgICAgY2xlYXJhYmxlOiB0cnVlCiAgICAgIH0KICAgIH0KICB9Owp9Ow=="}, {"version": 3, "names": ["config", "that", "notice_keyword", "component", "label", "colSpan", "name", "rules", "min", "max", "message", "componentProps", "placeholder", "clearable", "publish_time", "filterable", "type", "startPlaceholder", "endPlaceholder", "valueFormat", "publish_user"], "sources": ["D:/1_Project/XYD_Project/dop-4.0/dop-4.1-qian<PERSON>n-unify/dopUnify/src/views/system/notice/release/info.js"], "sourcesContent": ["// 表单\r\nexport const config = (that) => ({\r\n  notice_keyword: {\r\n    component: 'input',\r\n    label: '公告关键字',\r\n    colSpan: 8,\r\n    name: 'notice_keyword',\r\n    config: {\r\n      // form-item 配置\r\n      rules: [\r\n        { min: 0, max: 60, message: '请最多填写60个字符' }\r\n      ]\r\n    },\r\n    componentProps: {\r\n      // input组件配置\r\n      placeholder: '支持按公告关键字模糊查询',\r\n      clearable: true\r\n    }\r\n  },\r\n  publish_time: {\r\n    component: 'date-picker',\r\n    label: '发布时间',\r\n    colSpan: 8,\r\n    name: 'publish_time',\r\n    config: {},\r\n    componentProps: {\r\n      filterable: true,\r\n      type: 'daterange',\r\n      clearable: true,\r\n      startPlaceholder: '开始日期',\r\n      endPlaceholder: '结束日期',\r\n      valueFormat: 'yyyyMMdd'\r\n    }\r\n  },\r\n  publish_user: {\r\n    component: 'input',\r\n    label: '发布用户',\r\n    colSpan: 8,\r\n    name: 'publish_user',\r\n    config: {\r\n      // form-item 配置\r\n      rules: [\r\n        { min: 0, max: 10, message: '请最多填写10个字符' }\r\n      ]\r\n    },\r\n    componentProps: {\r\n      // input组件配置\r\n      placeholder: '支持按发布用户模糊查询',\r\n      clearable: true\r\n    }\r\n  }\r\n})\r\n"], "mappings": "AAAA;AACA,OAAO,IAAMA,MAAM,GAAG,SAATA,MAAM,CAAIC,IAAI;EAAA,OAAM;IAC/BC,cAAc,EAAE;MACdC,SAAS,EAAE,OAAO;MAClBC,KAAK,EAAE,OAAO;MACdC,OAAO,EAAE,CAAC;MACVC,IAAI,EAAE,gBAAgB;MACtBN,MAAM,EAAE;QACN;QACAO,KAAK,EAAE,CACL;UAAEC,GAAG,EAAE,CAAC;UAAEC,GAAG,EAAE,EAAE;UAAEC,OAAO,EAAE;QAAa,CAAC;MAE9C,CAAC;MACDC,cAAc,EAAE;QACd;QACAC,WAAW,EAAE,cAAc;QAC3BC,SAAS,EAAE;MACb;IACF,CAAC;IACDC,YAAY,EAAE;MACZX,SAAS,EAAE,aAAa;MACxBC,KAAK,EAAE,MAAM;MACbC,OAAO,EAAE,CAAC;MACVC,IAAI,EAAE,cAAc;MACpBN,MAAM,EAAE,CAAC,CAAC;MACVW,cAAc,EAAE;QACdI,UAAU,EAAE,IAAI;QAChBC,IAAI,EAAE,WAAW;QACjBH,SAAS,EAAE,IAAI;QACfI,gBAAgB,EAAE,MAAM;QACxBC,cAAc,EAAE,MAAM;QACtBC,WAAW,EAAE;MACf;IACF,CAAC;IACDC,YAAY,EAAE;MACZjB,SAAS,EAAE,OAAO;MAClBC,KAAK,EAAE,MAAM;MACbC,OAAO,EAAE,CAAC;MACVC,IAAI,EAAE,cAAc;MACpBN,MAAM,EAAE;QACN;QACAO,KAAK,EAAE,CACL;UAAEC,GAAG,EAAE,CAAC;UAAEC,GAAG,EAAE,EAAE;UAAEC,OAAO,EAAE;QAAa,CAAC;MAE9C,CAAC;MACDC,cAAc,EAAE;QACd;QACAC,WAAW,EAAE,aAAa;QAC1BC,SAAS,EAAE;MACb;IACF;EACF,CAAC;AAAA,CAAC"}]}