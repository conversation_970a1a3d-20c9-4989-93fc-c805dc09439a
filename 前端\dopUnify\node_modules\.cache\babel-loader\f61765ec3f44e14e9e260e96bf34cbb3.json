{"remainingRequest": "E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\node_modules\\babel-loader\\lib\\index.js!E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\src\\views\\extend\\qiankun.vue?vue&type=script&lang=js&", "dependencies": [{"path": "E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\src\\views\\extend\\qiankun.vue", "mtime": 1686019809591}, {"path": "E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\babel.config.js", "mtime": 1686019818359}, {"path": "E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\.babelrc", "mtime": 1705285078668}, {"path": "E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1655715099000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;AAkBA;AACA;AACA;AACA;AACA;AACA,IACAA,kBACAC,OADAD;AAEA;EACAE;EACAC;IAAAC;EAAA;EACAC;IACA;MACAC;MACAC;MACAC;QACA;QACAC;QACAC;UACA;UACAC;UAAA;UACAC;UACAC;QACA;;QACAC;QAAA;QACAC;MACA;;MACAC;MACAC;IACA;EACA;EACAC;EACAC;IACA;MACA;IACA;;IACA;IACA;EACA;EACAC;IAAA;IACA;MAAA;MAAA;QAAA;UAAA;YAAA;cACA;cACAC;cACAC;cACAC;cACAC;cACA;cACA;cACA;cAAA,IACA;gBAAA;gBAAA;cAAA;cACAC;cACAD;cAAA,KACAE;gBAAA;gBAAA;cAAA;cAAA;cAAA,OACA;YAAA;cAAA;cAAA;YAAA;cAEA;gBACAC;gBACAC;gBACAC,uBACAC;cAEA;YAAA;cAGAC;cACAC;cACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;kBACA;kBACA;oBACA;oBACA;oBACA;oBACAC,oDACA;sBACA;oBACA,EACA;oBACAD;kBACA;oBACAC,qDACA;sBACA;oBACA,EACA;oBACAF;oBACAC;oBACA;kBACA;oBACAD;oBACA;oBACAE,sDACA;sBACA;oBACA,EACA,QACA;oBACA;oBACAT;oBACAQ;oBACA;kBACA;gBACA;kBACA;kBACA;kBACA;kBACAA;kBACAD;gBACA;cACA;;cAEA;cACA;YAAA;YAAA;cAAA;UAAA;QAAA;MAAA;IAAA,CACA;EACA;EACAG;IACA;AACA;AACA;IACAC;MAAA;MACA;QACA;UACAC;UACAC;UACAC;UAAA;UACAX;QACA;QACA3B;UACA;UACA8B;UACAA,qBACAH,yBACAY,+EACA;UACA;YACAZ;YACAC;YACAC;UACA;UACAW;QACA;MACA;IACA;IACAC;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA,KACA;kBAAA;kBAAA;gBAAA;gBAAA;gBAAA,OACA;cAAA;gBAGA;AACA;AACA;gBACAC;kBACAC;kBACAnC;oBACA;oBACAoC;kBACA;gBACA;gBACA;;gBAEA;gBACA;kBACA;kBACA;kBACA;oBACA;kBACA;;kBACA;oBACA;oBACA;kBACA;gBACA;gBAEAX;gBACAjB,0BAEA;gBACA6B;gBACA;kBACAC,yBACAP,yDACA;kBACAQ;kBACA;oBACA;sBACAA,mDACAD,uBACAA,iBACA;oBACA;kBACA;kBACAD;oBACAG;kBACA;kBACAH;oBACAI;kBACA;kBACA;oBACA,IACAC,oBACAA,yBACAA,sBACA;sBACA;oBACA;kBACA;gBACA;gBACA1B;gBACA;;gBAEA,+BACA;kBACA;kBACAtB;kBAAA;kBACA;kBACA;kBACA8B;kBAEAmB;kBAAA;kBACAC;kBAAA;kBACAC;oBACA;oBACAC;sBACAX;oBACA;oBACA;oBACApB;oBAAA;oBACAP;oBACA;kBACA;gBACA,GACA;kBACAuC;oBACAC;oBAAA;oBACAC;kBACA;gBACA,EACA;gBAAA;gBAAA,OAEA;cAAA;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IACA;IACA;IACAC;MACA;MACA;QACA;QACAlD;UAAAoC;QAAA;MACA;IACA;IACA;AACA;AACA;IACAe;MACA;QAAAjD;QAAAK;MACA;MACA;MACA;MACA;IACA;EACA;AACA", "names": ["fieldController", "Common", "name", "components", "SunFlowDialog", "data", "extendRef", "microApp", "flowConfig", "visible", "componentProps", "title", "top", "width", "module_id", "childFlowUrl", "actions", "systemNum", "watch", "<PERSON><PERSON><PERSON><PERSON>", "mounted", "pathIndex", "routeMessage", "path", "console", "systemDic", "commonBlank", "systemNo", "value", "dictionary", "localStorage", "pathN", "entry", "appName", "methods", "getDictionaryData", "parameterList", "fieldTime", "operType", "JSON", "resolve", "loadMicroApps", "initialState", "common", "flag", "commonData", "dictionaryP", "dictionaryPUnfiy", "UNIFY", "dictionaryTree", "item", "container", "activeRule", "props", "parentStore", "sandBox", "strictStyleIsolation", "experimentalStyleIsolation", "dialogFlowClose", "dialogFlow"], "sourceRoot": "src/views/extend", "sources": ["qiankun.vue"], "sourcesContent": ["<!--\n* qiankun 接入外系统代码入口\n-->\n<template>\n  <div>\n    <div :id=\"extendRef\" class=\"qiankun\" />\n    <!-- 流程详情弹框 -->\n    <!-- 调用流程弹框 begin -->\n    <sun-flow-dialog\n      v-if=\"flowConfig.visible\"\n      ref=\"flowDialog\"\n      :dialog-config=\"flowConfig\"\n      @dialogClose=\"dialogFlowClose\"\n    />\n  </div>\n</template>\n\n<script>\nimport { loadMicroApp, initGlobalState } from 'qiankun'\nimport SunFlowDialog from '@/components/Dialog/SunFlowDialog' // 流程详情弹窗\nimport { commonBlank } from '@/utils/common'\nimport { dictionaryFieds } from '@/utils/dictionary'\nimport { Common } from '@/api'\nconst {\n  fieldController // 同步字典\n} = Common\nexport default {\n  name: 'Qiankun',\n  components: { SunFlowDialog },\n  data() {\n    return {\n      extendRef: 'extendRef',\n      microApp: null,\n      flowConfig: {\n        // 流程模板调用弹窗\n        visible: false,\n        componentProps: {\n          // 弹出框属性\n          title: '', // 弹出框标题\n          top: '0px',\n          width: '100%' // 当前弹出框宽度 默认80%\n        },\n        module_id: '', // 模板id\n        childFlowUrl: 'static/html/flow/workflow.html' // 子工程流程页面url\n      },\n      actions: {},\n      systemNum: ''\n    }\n  },\n  watch: {},\n  beforeDestroy() {\n    if (this.microApp) {\n      this.microApp.unmount() // 卸载当前应用\n    }\n    this.microApp = null\n    this.actions.offGlobalStateChange() // 关闭 监听actions全局公共状态数据的变化\n  },\n  mounted() {\n    this.$nextTick().then(async() => {\n      this.extendRef = this.extendRef + new Date().getTime()\n      const pathIndex = this.$route.matched.length - 1\n      const routeMessage = this.$route.matched[pathIndex].props.default\n      const { path, system_no, deplName } = routeMessage\n      console.log(system_no)\n      // const path = routeMessage.path\n      this.systemNum = system_no // 系统号\n      // 获取外系统菜单 数据字典\n      if (!(this.systemNum in this.$store.state.common.dictionaryLet)) {\n        const systemDic = JSON.parse(localStorage.getItem(this.systemNum))\n        console.log(JSON.parse(localStorage.getItem(this.systemNum + 'dictionary')))\n        if (commonBlank(systemDic)) {\n          await this.getDictionaryData(this.systemNum)\n        } else {\n          this.$store.commit('common/ADD_DICTIONARYLET', {\n            systemNo: this.systemNum,\n            value: systemDic,\n            dictionary: JSON.parse(\n              localStorage.getItem(this.systemNum + 'dictionary')\n            )\n          })\n        }\n      }\n      let pathN = ''\n      let entry = '' // 入口地址配置\n      if (!commonBlank(path)) {\n        // 获取当前路由\n        this.$store.commit('user/SET_ROUTE_M', routeMessage) // 存储当前路由信息\n        // const pathUrl = path.split(':') // redirect:/report/report1\n        // redirect:/unify/static/html/flow/flowDef.html\n        // redirect:/report/reportList\n        if (path.indexOf('http') === -1) {\n          // 做了代理\n          if (path.indexOf('static/html') !== -1) {\n            // 兼容老基线sunaos\n            // pathN = path.split(':')[1]\n            // const appName = pathN.split('/')[1]\n            const appName = dictionaryFieds('EXT_SYS_DEPL_NAME').find(\n              (item) => {\n                return item.value === deplName\n              }\n            ).label\n            entry = `${window.location.pathname}redirect/${appName}/qiankun.html`\n          } else if (path.indexOf('SunDS') !== -1) {\n            const appName = dictionaryFieds('EXT_SYS_DEPL_NAME').find(\n              (item) => {\n                return item.value === deplName\n              }\n            ).label\n            pathN = path\n            entry = `${window.location.pathname}redirect/${appName}/html/qiankun.html`\n            // entry = `/dopUnify/redirect/${appName}/html/qiankun.html`\n          } else {\n            pathN = path\n            // const appName = pathN.split('/')[0]\n            const appName = dictionaryFieds('EXT_SYS_DEPL_NAME').find(\n              (item) => {\n                return item.value === deplName\n              }\n            ).label\n            // entry = `${window.location.pathname}redirect/${deplName}/`\n            // this.iframeUrl = `http://************/dopUnify/redirect/${deplName}/iframe.html`\n            console.log('windowwindowwindowwindow', window.location)\n            entry = `${window.location.pathname}redirect/${appName}/`\n            // entry = `http://***********/dopUnify/redirect/${appName}/`\n          }\n        } else {\n          // 本地测试\n          // http://127.0.0.1:8086/?/report/reportList\n          // http://localhost:5000/SunAOS/qiankun.html?/unify/static/html/flow/nodeConfig.html\n          entry = path.split('?')[0]\n          pathN = path.split('?')[1]\n        }\n      }\n\n      // this.extendRef = this.extendRef + new Date().getTime()\n      this.loadMicroApps(entry, pathN)\n    })\n  },\n  methods: {\n    /**\n     * 获取数据字典\n     */\n    getDictionaryData(systemNo) {\n      return new Promise((resolve) => {\n        const msg = {\n          parameterList: [''],\n          fieldTime: '',\n          operType: '1', // 门户操作标识\n          systemNo: systemNo\n        }\n        fieldController(msg).then((res) => {\n          // 新增外系统字典\n          localStorage.setItem(systemNo, JSON.stringify(res.retMap[systemNo]))\n          localStorage.setItem(\n            systemNo + 'dictionary',\n            JSON.stringify({ [systemNo]: res.retMap.dictionary[systemNo] })\n          )\n          this.$store.commit('common/ADD_DICTIONARYLET', {\n            systemNo,\n            value: res.retMap[systemNo],\n            dictionary: res.retMap.dictionary\n          })\n          resolve()\n        })\n      })\n    },\n    async loadMicroApps(entry, path) {\n      if (this.microApp) {\n        await this.microApp.unmount()\n      }\n\n      /**\n       * 监听子应用数据\n       */\n      const initialState = {\n        common: {},\n        flowConfig: {\n          // 流程传参\n          flag: false\n        }\n      } // 这里可以写初始化数据\n      this.actions = initGlobalState(initialState) // 初始化state\n\n      // 监听actions全局公共状态数据的变化\n      this.actions.onGlobalStateChange((state, prevState) => {\n        // console.log('主应用变更前：', prevState)\n        // console.log('主应用变更后：', state)\n        if (state.common !== prevState.common) {\n          this.$store.commit('common/SET_COMMON', state.common) // 将获取的最新的公共状态保存到vuex中\n        }\n        if (state.flowConfig.flag === true) {\n          // 流程配置\n          this.dialogFlow(state.flowConfig)\n        }\n      })\n\n      const appName = path.split('/')[0] // 获取当前系统名称，如：unify、dapVisuality等\n      const actions = this.actions\n\n      // 合并父子工程数据字典 begin\n      let commonData = JSON.parse(JSON.stringify(this.$store.state.common))\n      if (this.systemNum !== 'UNIFY') {\n        const dictionaryP = JSON.parse(\n          JSON.stringify(this.$store.state.common.dictionaryLet)\n        )\n        let dictionaryPUnfiy = {}\n        for (const key in dictionaryP) {\n          if (key === this.systemNum) {\n            dictionaryPUnfiy = {\n              ...dictionaryP['UNIFY'],\n              ...dictionaryP[key]\n            }\n          }\n        }\n        commonData.dictionaryLet = Object.assign({}, commonData.dictionaryLet, {\n          UNIFY: dictionaryPUnfiy\n        })\n        commonData = Object.assign({}, commonData, {\n          dictionaryTree: commonData.dictionaryTree[this.systemNum]\n        })\n        for (const item in commonData.dictionaryLet) {\n          if (\n            item !== 'UNIFY' &&\n            item !== 'dictionary' &&\n            item !== 'fieldTime'\n          ) {\n            delete commonData.dictionaryLet[item]\n          }\n        }\n      }\n      console.log(commonData.dictionaryLet)\n      // 合并父子工程数据字典 end\n\n      this.microApp = loadMicroApp(\n        {\n          // 手动加载微应用\n          name: appName, // 必选，微应用的名称，微应用之间必须确保唯一\n          // entry: 'http://127.0.0.1:1315/', // - 必选，微应用的入口\n          // entry: 'http://172.1.11.245:9909/SunAOS/qiankun.html', // - 必选，微应用的入口\n          entry: entry,\n\n          container: '#' + this.extendRef, // -必选，微应用的容器节点的选择器或者 Element 实例\n          activeRule: `/${appName}`, // - 必选，微应用的激活规则\n          props: {\n            // - 可选，主应用需要传递给微应用的数据。\n            parentStore: Object.assign({}, this.$store.state, {\n              common: commonData\n            }),\n            // parentStore: this.$store.state,\n            path: path, // 子应用页面路由path\n            actions // 主子应用数据通信\n            // msg: '我是父应用传过来的值，传递给B应用'\n          }\n        },\n        {\n          sandBox: {\n            strictStyleIsolation: true, // 开启严格的样式隔离模式\n            experimentalStyleIsolation: true\n          }\n        }\n      )\n\n      await this.microApp.mountPromise\n      this.$forceUpdate() // 解决视图不更新的问题\n    },\n    // 流程弹窗关闭\n    dialogFlowClose() {\n      this.flowConfig.visible = false\n      this.actions.setGlobalState({\n        // 子应用向主应用传递信息\n        flowConfig: { flag: false } // 移除相关信息\n      })\n    },\n    /**\n     * 流程弹出框：flowConfig\n     */\n    dialogFlow(data) {\n      const { module_id, componentProps, childFlowUrl } = data\n      this.flowConfig.componentProps.title = componentProps.title\n      this.flowConfig.module_id = module_id\n      this.flowConfig.visible = true\n      this.flowConfig.childFlowUrl = childFlowUrl\n    }\n  }\n}\n</script>\n\n<style lang=\"scss\" scoped></style>\n"]}]}