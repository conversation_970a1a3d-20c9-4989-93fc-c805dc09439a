{"remainingRequest": "D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\node_modules\\thread-loader\\dist\\cjs.js!D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\node_modules\\babel-loader\\lib\\index.js!D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\src\\api\\common\\audit\\index.js", "dependencies": [{"path": "D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\src\\api\\common\\audit\\index.js", "mtime": 1686019810966}, {"path": "D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\babel.config.js", "mtime": 1686019818359}, {"path": "D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\node_modules\\thread-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, null]}