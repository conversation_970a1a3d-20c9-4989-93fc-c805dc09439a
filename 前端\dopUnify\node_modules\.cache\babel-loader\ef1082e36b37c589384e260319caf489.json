{"remainingRequest": "D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\node_modules\\babel-loader\\lib\\index.js!D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\node_modules\\eslint-loader\\index.js??ref--13-0!D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\src\\views\\system\\organ\\info.js", "dependencies": [{"path": "D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\src\\views\\system\\organ\\info.js", "mtime": 1686019808888}, {"path": "D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\babel.config.js", "mtime": 1686019818359}, {"path": "D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\.babelrc", "mtime": 1705285078668}, {"path": "D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\node_modules\\eslint-loader\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["dictionaryFieds", "config", "that", "organ_no", "component", "label", "colSpan", "name", "componentProps", "clearable", "options", "organ_type", "<PERSON><PERSON><PERSON>", "filterable", "organ_level", "organ_status", "parent_organ"], "sources": ["D:/1_Project/XYD_Project/dop-4.0/dop-4.1-qian<PERSON>n-unify/dopUnify/src/views/system/organ/info.js"], "sourcesContent": ["import { dictionaryFieds } from '@/utils/dictionary'\n\n// 表单\nexport const config = (that) => ({\n  organ_no: {\n    component: 'select-tree',\n    label: '机构号',\n    colSpan: 8,\n    name: 'organ_no',\n    componentProps: {\n      // input组件配置\n      clearable: true\n    },\n    options: []\n  },\n  organ_type: {\n    component: 'select',\n    label: '机构类型',\n    colSpan: 8,\n    name: 'organ_type',\n    config: {},\n    componentProps: {\n      placehodler: '请选择',\n      filterable: true,\n      clearable: true\n    },\n    options: dictionaryFieds('ORGAN_TYPE')\n  },\n  organ_level: {\n    component: 'select',\n    label: '机构级别',\n    colSpan: 8,\n    name: 'organ_level',\n    config: {},\n    componentProps: {\n      placehodler: '请选择',\n      filterable: true,\n      clearable: true\n    },\n    options: dictionaryFieds('ORGAN_LEVEL')\n  },\n  organ_status: {\n    component: 'select',\n    label: '机构状态',\n    colSpan: 8,\n    name: 'organ_status',\n    config: {},\n    componentProps: {\n      placehodler: '请选择',\n      filterable: true,\n      clearable: true\n    },\n    options: dictionaryFieds('ORGAN_STATUS')\n  },\n  parent_organ: {\n    component: 'select-tree',\n    label: '上级机构',\n    colSpan: 8,\n    name: 'parent_organ',\n    config: {},\n    componentProps: {\n      placehodler: '请选择',\n      filterable: true,\n      clearable: true\n    },\n    options: []\n  }\n})\n"], "mappings": "AAAA,SAASA,eAAe,QAAQ,oBAAoB;;AAEpD;AACA,OAAO,IAAMC,MAAM,GAAG,SAATA,MAAM,CAAIC,IAAI;EAAA,OAAM;IAC/BC,QAAQ,EAAE;MACRC,SAAS,EAAE,aAAa;MACxBC,KAAK,EAAE,KAAK;MACZC,OAAO,EAAE,CAAC;MACVC,IAAI,EAAE,UAAU;MAChBC,cAAc,EAAE;QACd;QACAC,SAAS,EAAE;MACb,CAAC;MACDC,OAAO,EAAE;IACX,CAAC;IACDC,UAAU,EAAE;MACVP,SAAS,EAAE,QAAQ;MACnBC,KAAK,EAAE,MAAM;MACbC,OAAO,EAAE,CAAC;MACVC,IAAI,EAAE,YAAY;MAClBN,MAAM,EAAE,CAAC,CAAC;MACVO,cAAc,EAAE;QACdI,WAAW,EAAE,KAAK;QAClBC,UAAU,EAAE,IAAI;QAChBJ,SAAS,EAAE;MACb,CAAC;MACDC,OAAO,EAAEV,eAAe,CAAC,YAAY;IACvC,CAAC;IACDc,WAAW,EAAE;MACXV,SAAS,EAAE,QAAQ;MACnBC,KAAK,EAAE,MAAM;MACbC,OAAO,EAAE,CAAC;MACVC,IAAI,EAAE,aAAa;MACnBN,MAAM,EAAE,CAAC,CAAC;MACVO,cAAc,EAAE;QACdI,WAAW,EAAE,KAAK;QAClBC,UAAU,EAAE,IAAI;QAChBJ,SAAS,EAAE;MACb,CAAC;MACDC,OAAO,EAAEV,eAAe,CAAC,aAAa;IACxC,CAAC;IACDe,YAAY,EAAE;MACZX,SAAS,EAAE,QAAQ;MACnBC,KAAK,EAAE,MAAM;MACbC,OAAO,EAAE,CAAC;MACVC,IAAI,EAAE,cAAc;MACpBN,MAAM,EAAE,CAAC,CAAC;MACVO,cAAc,EAAE;QACdI,WAAW,EAAE,KAAK;QAClBC,UAAU,EAAE,IAAI;QAChBJ,SAAS,EAAE;MACb,CAAC;MACDC,OAAO,EAAEV,eAAe,CAAC,cAAc;IACzC,CAAC;IACDgB,YAAY,EAAE;MACZZ,SAAS,EAAE,aAAa;MACxBC,KAAK,EAAE,MAAM;MACbC,OAAO,EAAE,CAAC;MACVC,IAAI,EAAE,cAAc;MACpBN,MAAM,EAAE,CAAC,CAAC;MACVO,cAAc,EAAE;QACdI,WAAW,EAAE,KAAK;QAClBC,UAAU,EAAE,IAAI;QAChBJ,SAAS,EAAE;MACb,CAAC;MACDC,OAAO,EAAE;IACX;EACF,CAAC;AAAA,CAAC"}]}