{"remainingRequest": "E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-q<PERSON><PERSON>n\\数字运营平台-统一门户工程\\dopUnify\\node_modules\\babel-loader\\lib\\index.js!E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-q<PERSON>duan\\数字运营平台-统一门户工程\\dopUnify\\node_modules\\eslint-loader\\index.js??ref--13-0!E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan\\数字运营平台-统一门户工程\\dopUnify\\src\\utils\\flowPath.js", "dependencies": [{"path": "E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-<PERSON><PERSON><PERSON>n\\数字运营平台-统一门户工程\\dopUnify\\src\\utils\\flowPath.js", "mtime": 1713835568679}, {"path": "E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-<PERSON><PERSON><PERSON>n\\数字运营平台-统一门户工程\\dopUnify\\babel.config.js", "mtime": 1686019818359}, {"path": "E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-<PERSON><PERSON><PERSON>n\\数字运营平台-统一门户工程\\dopUnify\\.babelrc", "mtime": 1705285078668}, {"path": "E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-<PERSON><PERSON><PERSON>n\\数字运营平台-统一门户工程\\dopUnify\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-<PERSON><PERSON><PERSON>n\\数字运营平台-统一门户工程\\dopUnify\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-<PERSON><PERSON><PERSON>n\\数字运营平台-统一门户工程\\dopUnify\\node_modules\\eslint-loader\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, null]}