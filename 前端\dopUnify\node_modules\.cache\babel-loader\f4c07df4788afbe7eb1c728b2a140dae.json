{"remainingRequest": "D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\node_modules\\babel-loader\\lib\\index.js!D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\src\\views\\error-page\\error2.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\src\\views\\error-page\\error2.vue", "mtime": 1686019809638}, {"path": "D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\babel.config.js", "mtime": 1686019818359}, {"path": "D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1655715099000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KCmV4cG9ydCBkZWZhdWx0IHsKICBuYW1lOiAnRXJyb3IyJywKICBkYXRhOiBmdW5jdGlvbiBkYXRhKCkgewogICAgcmV0dXJuIHsKICAgICAgbnVtOiAxMAogICAgfTsKICB9LAogIGNvbXB1dGVkOiB7CiAgICBtZXNzYWdlOiBmdW5jdGlvbiBtZXNzYWdlKCkgewogICAgICByZXR1cm4gJ+WwhuiHquWKqOi/lOWbnueZu+W9lemhtS4uLic7CiAgICB9CiAgfSwKICBtb3VudGVkOiBmdW5jdGlvbiBtb3VudGVkKCkgewogICAgdGhpcy5jb3VudCgpOwogIH0sCiAgbWV0aG9kczogewogICAgY291bnQ6IGZ1bmN0aW9uIGNvdW50KCkgewogICAgICB2YXIgdm0gPSB0aGlzOwogICAgICB2YXIgaW50ZXJ2YWwgPSB3aW5kb3cuc2V0SW50ZXJ2YWwoZnVuY3Rpb24gKCkgewogICAgICAgIHZtLm51bS0tOwogICAgICAgIGlmICh2bS5udW0gPT09IDApIHsKICAgICAgICAgIHZtLiRyb3V0ZXIucHVzaCh7CiAgICAgICAgICAgIHBhdGg6ICcvbG9naW4nCiAgICAgICAgICB9KTsKICAgICAgICAgIHdpbmRvdy5jbGVhckludGVydmFsKGludGVydmFsKTsKICAgICAgICB9CiAgICAgIH0sIDEwMDApOwogICAgfQogIH0KfTs="}, {"version": 3, "mappings": ";;;;;;;;;;;;;;;;;;;;;;;AAuBA;EACAA;EACAC;IACA;MACAC;IACA;EACA;EACAC;IACAC;MACA;IACA;EACA;EACAC;IACA;EACA;EACAC;IACAC;MACA;MACA;QACAC;QACA;UACAA;YAAAC;UAAA;UACAC;QACA;MACA;IACA;EACA;AACA", "names": ["name", "data", "num", "computed", "message", "mounted", "methods", "count", "vm", "path", "window"], "sourceRoot": "src/views/error-page", "sources": ["error2.vue"], "sourcesContent": ["<template>\r\n  <div>\r\n    <div class=\"wscn-http404-container\">\r\n      <div class=\"wscn-http404\">\r\n        <img src=\"@/assets/img/other/404_images/error.jpg\" alt=\"404\">\r\n      </div>\r\n      <div align=\"center\">\r\n        <p>\r\n          您计算机的日期和时间设置不正确，与服务端存在一定差异，因此无法建立安全连接。\r\n        </p>\r\n        <p>\r\n          请尝试修改本机时间后\r\n          <router-link to=\"/login\">\r\n            <a class=\"return-home\">返回首页</a>\r\n          </router-link>\r\n          重试。\r\n        </p>\r\n      </div>\r\n    </div>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nexport default {\r\n  name: 'Error2',\r\n  data() {\r\n    return {\r\n      num: 10\r\n    }\r\n  },\r\n  computed: {\r\n    message() {\r\n      return '将自动返回登录页...'\r\n    }\r\n  },\r\n  mounted() {\r\n    this.count()\r\n  },\r\n  methods: {\r\n    count() {\r\n      const vm = this\r\n      const interval = window.setInterval(function() {\r\n        vm.num--\r\n        if (vm.num === 0) {\r\n          vm.$router.push({ path: '/login' })\r\n          window.clearInterval(interval)\r\n        }\r\n      }, 1000)\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n.wscn-http404-container {\r\n  transform: translate(-50%, -50%);\r\n  position: absolute;\r\n  top: 40%;\r\n  left: 50%;\r\n}\r\n.wscn-http404 {\r\n  position: relative;\r\n  width: 1200px;\r\n  padding: 0 50px;\r\n  overflow: hidden;\r\n}\r\n.return-home {\r\n  color: #1890ff;\r\n}\r\n</style>\r\n"]}]}