{"remainingRequest": "E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1\\数字运营平台-统一门户工程\\dopUnify\\node_modules\\babel-loader\\lib\\index.js!E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1\\数字运营平台-统一门户工程\\dopUnify\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1\\数字运营平台-统一门户工程\\dopUnify\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1\\数字运营平台-统一门户工程\\dopUnify\\src\\views\\home\\component\\shortcutMenu\\index.vue?vue&type=script&lang=js&", "dependencies": [{"path": "E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1\\数字运营平台-统一门户工程\\dopUnify\\src\\views\\home\\component\\shortcutMenu\\index.vue", "mtime": 1716875179059}, {"path": "E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1\\数字运营平台-统一门户工程\\dopUnify\\babel.config.js", "mtime": 1716875191548}, {"path": "E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1\\数字运营平台-统一门户工程\\dopUnify\\.babelrc", "mtime": 1716875191502}, {"path": "E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1\\数字运营平台-统一门户工程\\dopUnify\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1\\数字运营平台-统一门户工程\\dopUnify\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1\\数字运营平台-统一门户工程\\dopUnify\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1\\数字运营平台-统一门户工程\\dopUnify\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1655715099000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA2BA;AACA;AACA;AACA;AACA;AACA;EACAA;EACAC;EACAC;IACA;MACAC;MAAA;MACAC;MACAC;IACA;EACA;EACAC;IAAA;IACA;IACA;IACA;MACA;QACA;MACA;IACA;EACA;EACAC;IACA;EACA;EACAC;IACAC;MAAA;MACA;QACAC;QACAC;MACA;MACAC;QACA;QACA;QACAC;UACA;YACAC;YACAC;YACAC;YACAC;YACAC;UACA;UACAC;QACA;QACA;UACA;QACA;QACA;MACA;IACA;IACAC;MACA;IACA;IACAC;MACA;QAAAC;MAAA;IACA;EACA;AACA", "names": ["name", "components", "data", "colorIconsPath", "title", "selectData", "mounted", "<PERSON><PERSON><PERSON><PERSON>", "methods", "initMenu", "parameterList", "sysMap", "queryPortal", "returnlist", "id", "label", "path", "class", "order", "list", "edit", "getMenu", "to"], "sourceRoot": "src/views/home/<USER>/shortcutMenu", "sources": ["index.vue"], "sourcesContent": ["<!--\n* 首页-快捷菜单\n-->\n<template>\n  <div class=\"homeContent\">\n    <div class=\"homePageTitle\">\n      {{ title }} <span class=\"InfoMore\" @click=\"edit\">进入编辑 ></span>\n    </div>\n    <div class=\"homePageBox\">\n      <div class=\"menuBox\">\n        <router-link\n          v-for=\"item in selectData\"\n          :key=\"item.id\"\n          class=\"menuItem\"\n          v-bind=\"getMenu(item.path)\"\n        >\n          <div class=\"menuItemIcon\">\n            <img class=\"menuColorIcons\" :src=\"colorIconsPath + 'color-'+ item.class + '.svg'\" :alt=\"item.class\">\n          </div>\n          <span class=\"homeTwoTitle\" :title=\"item.label\">{{ item.label }}</span>\n        </router-link>\n      </div>\n    </div>\n  </div>\n</template>\n\n<script>\nimport { Home } from '@/api'\nconst { queryPortal } = Home\n// import { ShortcutMenu } from '@/api'\n// const { query } = ShortcutMenu\nimport defaultSettings from '@/settings'\nexport default {\n  name: 'ShortcutMenu',\n  components: {},\n  data() {\n    return {\n      colorIconsPath: defaultSettings.colorIconsPath, // 菜色图标路径\n      title: '快捷菜单',\n      selectData: []\n    }\n  },\n  mounted() {\n    this.initMenu()\n    // 首页刷新：事件总线\n    this.$bus.$on('shortMenuFlushed', (params) => {\n      if (params) {\n        this.initMenu()\n      }\n    })\n  },\n  beforeDestroy() {\n    this.$bus.$off('shortMenuFlushed')\n  },\n  methods: {\n    initMenu() {\n      const msg = {\n        parameterList: [],\n        sysMap: {}\n      }\n      queryPortal(msg).then((res) => {\n        const { returnlist } = res.retMap\n        const list = []\n        returnlist.map((item) => {\n          const node = {\n            id: item.MENU_ID,\n            label: item.MENU_LABEL,\n            path: item.MENU_URL,\n            class: item.ICON_SRC,\n            order: item.ICON_ORDER\n          }\n          list.push(node)\n        })\n        this.selectData = list.sort(function(a, b) {\n          return b - a\n        })\n        // console.log(this.selectData)\n      })\n    },\n    edit() {\n      this.$router.push('/shortcutMenuEdit')\n    },\n    getMenu(path) {\n      return { to: path }\n    }\n  }\n}\n</script>\n\n<style scoped lang=\"scss\">\n@import '~@/assets/scss/common/variable/variable/size.scss';\n.InfoMore {\n  float: right;\n  color: gray;\n  font-size: $noteFomt;\n  cursor: pointer;\n}\n</style>\n"]}]}