package com.sunyard.etl.nps.dao;

import java.sql.SQLException;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.sunyard.util.dbutil.DBHandler;

import com.sun.rowset.CachedRowSetImpl;
import com.sunyard.etl.nps.model.SmDataSourceSetTb;
import com.sunyard.etl.nps.orm.SmDataSourceSetTbOrm;
import com.sunyard.etl.system.common.Constants;
import com.xxl.job.core.log.XxlJobLogger;

public class SmDataSourceSetTbDao {
	protected final Logger log = LoggerFactory.getLogger(getClass());
    private String tableName;
	
    SmDataSourceSetTbOrm orm = new SmDataSourceSetTbOrm();
    
	public SmDataSourceSetTbDao(){
		
	}
	
	public SmDataSourceSetTbDao(String tableName){
		this.tableName = tableName;
	}
	
	public SmDataSourceSetTb getById(String id){
		SmDataSourceSetTb source = null;
		CachedRowSetImpl rs = null;
		DBHandler dbHandler = new DBHandler(Constants.CONN_POOL_NAME);
		try {
			String sql = "SELECT * FROM SM_DATASOURCE_SET_TB T WHERE T.DATA_SOURCE_ID = ?";
			XxlJobLogger.log("寻找配置:" + sql, tableName);
			rs = dbHandler.queryRs(sql, id);
			while(rs.next()){
				source = orm.orm(rs);
			}
		} catch (SQLException e) {
			e.printStackTrace();
		} 
		return source;
	}
}
