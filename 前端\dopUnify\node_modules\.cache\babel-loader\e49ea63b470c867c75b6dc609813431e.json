{"remainingRequest": "E:\\华南区项目\\中国银行\\开发区\\科技公司运营平台\\前端\\dopUnify\\node_modules\\thread-loader\\dist\\cjs.js!E:\\华南区项目\\中国银行\\开发区\\科技公司运营平台\\前端\\dopUnify\\node_modules\\babel-loader\\lib\\index.js!E:\\华南区项目\\中国银行\\开发区\\科技公司运营平台\\前端\\dopUnify\\src\\permission.js", "dependencies": [{"path": "E:\\华南区项目\\中国银行\\开发区\\科技公司运营平台\\前端\\dopUnify\\src\\permission.js", "mtime": 1721631954662}, {"path": "E:\\华南区项目\\中国银行\\开发区\\科技公司运营平台\\前端\\dopUnify\\babel.config.js", "mtime": 1721631954662}, {"path": "E:\\华南区项目\\中国银行\\开发区\\科技公司运营平台\\前端\\dopUnify\\.babelrc", "mtime": 1721631954662}, {"path": "E:\\华南区项目\\中国银行\\开发区\\科技公司运营平台\\前端\\dopUnify\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "E:\\华南区项目\\中国银行\\开发区\\科技公司运营平台\\前端\\dopUnify\\node_modules\\thread-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "E:\\华南区项目\\中国银行\\开发区\\科技公司运营平台\\前端\\dopUnify\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, null]}