{"remainingRequest": "D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\node_modules\\thread-loader\\dist\\cjs.js!D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\node_modules\\babel-loader\\lib\\index.js!D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\node_modules\\qiankun\\es\\globalState.js", "dependencies": [{"path": "D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\node_modules\\qiankun\\es\\globalState.js", "mtime": 1667130453000}, {"path": "D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\babel.config.js", "mtime": 1686019818359}, {"path": "D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\node_modules\\thread-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, null]}