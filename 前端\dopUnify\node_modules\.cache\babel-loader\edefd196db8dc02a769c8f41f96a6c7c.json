{"remainingRequest": "E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-q<PERSON><PERSON>n\\数字运营平台-统一门户工程\\dopUnify\\node_modules\\babel-loader\\lib\\index.js!E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-q<PERSON>duan\\数字运营平台-统一门户工程\\dopUnify\\node_modules\\eslint-loader\\index.js??ref--13-0!E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan\\数字运营平台-统一门户工程\\dopUnify\\src\\router\\index.js", "dependencies": [{"path": "E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-<PERSON><PERSON><PERSON>n\\数字运营平台-统一门户工程\\dopUnify\\src\\router\\index.js", "mtime": 1686019818031}, {"path": "E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-<PERSON><PERSON><PERSON>n\\数字运营平台-统一门户工程\\dopUnify\\babel.config.js", "mtime": 1686019818359}, {"path": "E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-<PERSON><PERSON><PERSON>n\\数字运营平台-统一门户工程\\dopUnify\\.babelrc", "mtime": 1705285078668}, {"path": "E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-<PERSON><PERSON><PERSON>n\\数字运营平台-统一门户工程\\dopUnify\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-<PERSON><PERSON><PERSON>n\\数字运营平台-统一门户工程\\dopUnify\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-<PERSON><PERSON><PERSON>n\\数字运营平台-统一门户工程\\dopUnify\\node_modules\\eslint-loader\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["<PERSON><PERSON>", "Router", "use", "Layout", "constantRoutes", "path", "component", "hidden", "children", "redirect", "name", "meta", "title", "icon", "affix", "originalPush", "prototype", "push", "location", "onResolve", "onReject", "call", "catch", "err", "createRouter", "scroll<PERSON>eh<PERSON>or", "y", "routes", "router", "resetRouter", "newRouter", "matcher"], "sources": ["E:/1_Project/XYD_Project/dop-4.0/dop-4.1-qianduan/数字运营平台-统一门户工程/dopUnify/src/router/index.js"], "sourcesContent": ["import Vue from 'vue'\nimport Router from 'vue-router'\n\nVue.use(Router)\n\n/* Layout */\nimport Layout from '@/layout'\n\n/* Router Modules */\n// import componentsRouter from './modules/components'\n\n/**\n // 当设置 true 的时候该路由不会在侧边栏出现 如401，login等页面，或者如一些编辑页面/edit/1\n    hidden: true // (默认 false)\n\n//当设置 noRedirect 的时候该路由在面包屑导航中不可被点击\n   redirect: 'noRedirect'\n\n// 当你一个路由下面的 children 声明的路由大于1个时，自动会变成嵌套的模式--如组件页面\n// 只有一个时，会将那个子路由当做根路由显示在侧边栏--如引导页面\n// 若你想不管路由下面的 children 声明的个数都显示你的根路由\n// 你可以设置 alwaysShow: true，这样它就会忽略之前定义的规则，一直显示根路由\n   alwaysShow: true\n\nname: 'router-name' // 设定路由的名字，一定要填写不然使用<keep-alive>时会出现各种问题\nmeta: {\n  roles: ['admin', 'editor'] // 设置该路由进入的权限，支持多个权限叠加\n  title: 'title' // 设置该路由在侧边栏和面包屑中展示的名字\n  icon: 'svg-name' // 设置该路由的图标，支持 svg-class，也支持 el-icon-x element-ui 的 icon\n  noCache: true // 如果设置为true，则不会被 <keep-alive> 缓存(默认 false)\n  breadcrumb: false //  如果设置为false，则不会在breadcrumb面包屑中显示(默认 true)\n  affix: true // 如果设置为true，它则会固定在tags-view中(默认 false)\n\n  // 当路由设置了该属性，则会高亮相对应的侧边栏。\n  // 这在某些场景非常有用，比如：一个文章的列表页路由为：/article/list\n  // 点击文章进入文章详情页，这时候路由为/article/1，但你想在侧边栏高亮文章列表的路由，就可以进行如下设置\n  activeMenu: '/article/list'\n}\n */\n\n/**\n * constantRoutes\n *没有权限要求的基本页面\n *所有角色均可访问\n */\nexport const constantRoutes = [\n  {\n    path: '/redirect', // 字符串，对应当前路由的路径，总是解析为绝对路径\n    component: Layout,\n    hidden: true,\n    children: [\n      {\n        path: '/redirect/:path(.*)',\n        component: () => import('@/views/redirect/index')\n      }\n    ]\n  },\n  {\n    path: '/404',\n    component: () => import(/* webpackChunkName: \"error\" */'@/views/error-page/404'),\n    hidden: true\n  },\n  {\n    path: '/401',\n    component: () => import(/* webpackChunkName: \"error\" */'@/views/error-page/401'),\n    hidden: true\n  },\n  {\n    path: '/error',\n    component: () => import(/* webpackChunkName: \"error\" */'@/views/error-page/error'),\n    hidden: true\n  },\n  {\n    path: '/error2',\n    component: () => import(/* webpackChunkName: \"error\" */'@/views/error-page/error2'),\n    hidden: true\n  },\n  {\n    path: '/login',\n    component: () => import(/* webpackChunkName: \"login\" */'@/views/login/index'),\n    hidden: true\n  },\n  {\n    path: '/',\n    component: Layout,\n    redirect: '/home',\n    hidden: true, // 是否显示默认false显示，true:不显示\n    children: [\n      {\n        path: 'home',\n        component: () => import(/* webpackChunkName: \"home\" */'@/views/home/<USER>'),\n        name: 'Home',\n        meta: { title: '主页', icon: 'dashboard', affix: true } // 路由元信息\n      },\n      {\n        path: '/shortcutMenuEdit',\n        component: () =>\n          import(/* webpackChunkName: \"home\" */'@/views/home/<USER>/shortcutMenu/shortcutMenuEdit'),\n        hidden: true,\n        name: 'ShortcutMenuEdit',\n        meta: { title: '快捷菜单编辑' } // 路由元信息\n      }\n    ]\n  },\n  {\n    path: '/systems',\n    component: Layout,\n    redirect: '/systems/msg',\n    hidden: true, // 是否显示默认false显示，true:不显示\n    children: [\n      {\n        path: 'msg',\n        component: () =>\n          import(/* webpackChunkName: \"systems\" */'@/views/system/config/message/systemMsg/index'),\n        name: 'SystemMsg',\n        meta: { title: '系统消息', icon: 'dashboard', affix: false } // 路由元信息\n      }\n    ]\n  }\n  // componentsRouter\n]\n\n/**\n * 异步路由\n */\n// export const asyncRoutes = [\n//   sysRouter\n// ]\n// 重写push方法, 在调用方法的时候用catch捕获异常, 解决相同路径跳转报错问题\n// const originalPush = Router.prototype.push\n// Router.prototype.push = function push(location, onResolve, onReject) {\n//   if (onResolve || onReject) {\n//     return originalPush.call(this, location, onResolve, onReject)\n//   }\n//   // return originalPush.call(this, location).catch((err) => console.log(122, err))\n// }\nconst originalPush = Router.prototype.push\nRouter.prototype.push = function push(location, onResolve, onReject) {\n  if (onResolve || onReject) {\n    return originalPush.call(this, location, onResolve, onReject)\n  }\n  return originalPush.call(this, location).catch((err) => err)\n}\n\n// 创建路由\nconst createRouter = () =>\n  new Router({\n    // mode: 'history', // require service support\n    scrollBehavior: () => ({ y: 0 }), // 当切换到新路由时，页面滚到顶部\n    routes: constantRoutes\n  })\n\nconst router = createRouter()\n\n// Detail see: https://github.com/vuejs/vue-router/issues/1234#issuecomment-357941465\nexport function resetRouter() {\n  const newRouter = createRouter()\n  router.matcher = newRouter.matcher // 重置路由\n}\n\nexport default router\n"], "mappings": ";;;;AAAA,OAAOA,GAAG,MAAM,KAAK;AACrB,OAAOC,MAAM,MAAM,YAAY;AAE/BD,GAAG,CAACE,GAAG,CAACD,MAAM,CAAC;;AAEf;AACA,OAAOE,MAAM,MAAM,UAAU;;AAE7B;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA,OAAO,IAAMC,cAAc,GAAG,CAC5B;EACEC,IAAI,EAAE,WAAW;EAAE;EACnBC,SAAS,EAAEH,MAAM;EACjBI,MAAM,EAAE,IAAI;EACZC,QAAQ,EAAE,CACR;IACEH,IAAI,EAAE,qBAAqB;IAC3BC,SAAS,EAAE;MAAA;QAAA,uCAAa,wBAAwB;MAAA;IAAA;EAClD,CAAC;AAEL,CAAC,EACD;EACED,IAAI,EAAE,MAAM;EACZC,SAAS,EAAE;IAAA;MAAA,uCAA4C,wBAAwB;IAAA;EAAA,CAAC;EAChFC,MAAM,EAAE;AACV,CAAC,EACD;EACEF,IAAI,EAAE,MAAM;EACZC,SAAS,EAAE;IAAA;MAAA,uCAA4C,wBAAwB;IAAA;EAAA,CAAC;EAChFC,MAAM,EAAE;AACV,CAAC,EACD;EACEF,IAAI,EAAE,QAAQ;EACdC,SAAS,EAAE;IAAA;MAAA,uCAA4C,0BAA0B;IAAA;EAAA,CAAC;EAClFC,MAAM,EAAE;AACV,CAAC,EACD;EACEF,IAAI,EAAE,SAAS;EACfC,SAAS,EAAE;IAAA;MAAA,uCAA4C,2BAA2B;IAAA;EAAA,CAAC;EACnFC,MAAM,EAAE;AACV,CAAC,EACD;EACEF,IAAI,EAAE,QAAQ;EACdC,SAAS,EAAE;IAAA;MAAA,uCAA4C,qBAAqB;IAAA;EAAA,CAAC;EAC7EC,MAAM,EAAE;AACV,CAAC,EACD;EACEF,IAAI,EAAE,GAAG;EACTC,SAAS,EAAEH,MAAM;EACjBM,QAAQ,EAAE,OAAO;EACjBF,MAAM,EAAE,IAAI;EAAE;EACdC,QAAQ,EAAE,CACR;IACEH,IAAI,EAAE,MAAM;IACZC,SAAS,EAAE;MAAA;QAAA,uCAA2C,oBAAoB;MAAA;IAAA,CAAC;IAC3EI,IAAI,EAAE,MAAM;IACZC,IAAI,EAAE;MAAEC,KAAK,EAAE,IAAI;MAAEC,IAAI,EAAE,WAAW;MAAEC,KAAK,EAAE;IAAK,CAAC,CAAC;EACxD,CAAC,EACD;IACET,IAAI,EAAE,mBAAmB;IACzBC,SAAS,EAAE;MAAA;QAAA,uCAC4B,sDAAsD;MAAA;IAAA,CAAC;IAC9FC,MAAM,EAAE,IAAI;IACZG,IAAI,EAAE,kBAAkB;IACxBC,IAAI,EAAE;MAAEC,KAAK,EAAE;IAAS,CAAC,CAAC;EAC5B,CAAC;AAEL,CAAC,EACD;EACEP,IAAI,EAAE,UAAU;EAChBC,SAAS,EAAEH,MAAM;EACjBM,QAAQ,EAAE,cAAc;EACxBF,MAAM,EAAE,IAAI;EAAE;EACdC,QAAQ,EAAE,CACR;IACEH,IAAI,EAAE,KAAK;IACXC,SAAS,EAAE;MAAA;QAAA,uCAC+B,+CAA+C;MAAA;IAAA,CAAC;IAC1FI,IAAI,EAAE,WAAW;IACjBC,IAAI,EAAE;MAAEC,KAAK,EAAE,MAAM;MAAEC,IAAI,EAAE,WAAW;MAAEC,KAAK,EAAE;IAAM,CAAC,CAAC;EAC3D,CAAC;AAEL;AACA;AAAA,CACD;;AAED;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAMC,YAAY,GAAGd,MAAM,CAACe,SAAS,CAACC,IAAI;AAC1ChB,MAAM,CAACe,SAAS,CAACC,IAAI,GAAG,SAASA,IAAI,CAACC,QAAQ,EAAEC,SAAS,EAAEC,QAAQ,EAAE;EACnE,IAAID,SAAS,IAAIC,QAAQ,EAAE;IACzB,OAAOL,YAAY,CAACM,IAAI,CAAC,IAAI,EAAEH,QAAQ,EAAEC,SAAS,EAAEC,QAAQ,CAAC;EAC/D;EACA,OAAOL,YAAY,CAACM,IAAI,CAAC,IAAI,EAAEH,QAAQ,CAAC,CAACI,KAAK,CAAC,UAACC,GAAG;IAAA,OAAKA,GAAG;EAAA,EAAC;AAC9D,CAAC;;AAED;AACA,IAAMC,YAAY,GAAG,SAAfA,YAAY;EAAA,OAChB,IAAIvB,MAAM,CAAC;IACT;IACAwB,cAAc,EAAE;MAAA,OAAO;QAAEC,CAAC,EAAE;MAAE,CAAC;IAAA,CAAC;IAAE;IAClCC,MAAM,EAAEvB;EACV,CAAC,CAAC;AAAA;AAEJ,IAAMwB,MAAM,GAAGJ,YAAY,EAAE;;AAE7B;AACA,OAAO,SAASK,WAAW,GAAG;EAC5B,IAAMC,SAAS,GAAGN,YAAY,EAAE;EAChCI,MAAM,CAACG,OAAO,GAAGD,SAAS,CAACC,OAAO,EAAC;AACrC;;AAEA,eAAeH,MAAM"}]}