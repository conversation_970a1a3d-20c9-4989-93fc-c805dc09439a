{"remainingRequest": "D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\node_modules\\babel-loader\\lib\\index.js!D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\node_modules\\sunui\\src\\utils\\common.js", "dependencies": [{"path": "D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\node_modules\\sunui\\src\\utils\\common.js", "mtime": 499162500000}, {"path": "D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\babel.config.js", "mtime": 1686019818359}, {"path": "D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["commonBlank", "params", "undefined", "type", "Object", "prototype", "toString", "call", "split", "slice", "trim", "length", "key", "parseTime", "time", "cFormat", "arguments", "format", "date", "test", "parseInt", "replace", "RegExp", "Date", "formatObj", "y", "getFullYear", "m", "getMonth", "d", "getDate", "h", "getHours", "i", "getMinutes", "s", "getSeconds", "a", "getDay", "time_str", "result", "value", "padStart"], "sources": ["D:/1_Project/XYD_Project/dop-4.0/dop-4.1-qianduan-unify/dopUnify/node_modules/sunui/src/utils/common.js"], "sourcesContent": ["// 判断是否为 空 空对象 空数组 空字符等\nexport const commonBlank = (params) => {\n  if (params === null || params === undefined) {\n    return true\n  }\n  const type = Object.prototype.toString.call(params).split(' ')[1].slice(0, -1)\n  if (type === 'String') {\n    return params === '' || params.trim().length === 0\n  }\n  if (type === 'Object') {\n    for (const key in params) {\n      return false\n    }\n    return true\n  }\n  if (type === 'Array') {\n    return params.length === 0\n  }\n  if (type === 'Null') {\n    return true\n  }\n  if (type === 'Undefined') {\n    return true\n  }\n  if (type === 'Number') {\n    return params <= 0\n  }\n}\n/**\n * 公共方法*/\n/**\n * 解析时间到字符串\n * @param {(Object|string|number)} time\n * @param {string} cFormat\n * @returns {string | null}\n */\nexport function parseTime(time, cFormat) {\n  if (arguments.length === 0 || !time) {\n    return null\n  }\n  const format = cFormat || '{y}-{m}-{d} {h}:{i}:{s}'\n  let date\n  if (typeof time === 'object') {\n    date = time\n  } else {\n    if (typeof time === 'string') {\n      if (/^[0-9]+$/.test(time)) {\n        // support \"1548221490638\"\n        time = parseInt(time)\n      } else {\n        // support safari\n        // https://stackoverflow.com/questions/4310953/invalid-date-in-safari\n        time = time.replace(new RegExp(/-/gm), '/')\n      }\n    }\n\n    if (typeof time === 'number' && time.toString().length === 10) {\n      time = time * 1000\n    }\n    date = new Date(time)\n  }\n  const formatObj = {\n    y: date.getFullYear(),\n    m: date.getMonth() + 1,\n    d: date.getDate(),\n    h: date.getHours(),\n    i: date.getMinutes(),\n    s: date.getSeconds(),\n    a: date.getDay()\n  }\n  const time_str = format.replace(/{([ymdhisa])+}/g, (result, key) => {\n    const value = formatObj[key]\n    // Note: getDay() returns 0 on Sunday\n    if (key === 'a') {\n      return ['日', '一', '二', '三', '四', '五', '六'][value]\n    }\n    return value.toString().padStart(2, '0')\n  })\n  return time_str\n}\n"], "mappings": ";;;;;;;;;;;AAAA;AACA,OAAO,IAAMA,WAAW,GAAG,SAAdA,WAAW,CAAIC,MAAM,EAAK;EACrC,IAAIA,MAAM,KAAK,IAAI,IAAIA,MAAM,KAAKC,SAAS,EAAE;IAC3C,OAAO,IAAI;EACb;EACA,IAAMC,IAAI,GAAGC,MAAM,CAACC,SAAS,CAACC,QAAQ,CAACC,IAAI,CAACN,MAAM,CAAC,CAACO,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAACC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;EAC9E,IAAIN,IAAI,KAAK,QAAQ,EAAE;IACrB,OAAOF,MAAM,KAAK,EAAE,IAAIA,MAAM,CAACS,IAAI,EAAE,CAACC,MAAM,KAAK,CAAC;EACpD;EACA,IAAIR,IAAI,KAAK,QAAQ,EAAE;IACrB,KAAK,IAAMS,GAAG,IAAIX,MAAM,EAAE;MACxB,OAAO,KAAK;IACd;IACA,OAAO,IAAI;EACb;EACA,IAAIE,IAAI,KAAK,OAAO,EAAE;IACpB,OAAOF,MAAM,CAACU,MAAM,KAAK,CAAC;EAC5B;EACA,IAAIR,IAAI,KAAK,MAAM,EAAE;IACnB,OAAO,IAAI;EACb;EACA,IAAIA,IAAI,KAAK,WAAW,EAAE;IACxB,OAAO,IAAI;EACb;EACA,IAAIA,IAAI,KAAK,QAAQ,EAAE;IACrB,OAAOF,MAAM,IAAI,CAAC;EACpB;AACF,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASY,SAAS,CAACC,IAAI,EAAEC,OAAO,EAAE;EACvC,IAAIC,SAAS,CAACL,MAAM,KAAK,CAAC,IAAI,CAACG,IAAI,EAAE;IACnC,OAAO,IAAI;EACb;EACA,IAAMG,MAAM,GAAGF,OAAO,IAAI,yBAAyB;EACnD,IAAIG,IAAI;EACR,IAAI,QAAOJ,IAAI,MAAK,QAAQ,EAAE;IAC5BI,IAAI,GAAGJ,IAAI;EACb,CAAC,MAAM;IACL,IAAI,OAAOA,IAAI,KAAK,QAAQ,EAAE;MAC5B,IAAI,UAAU,CAACK,IAAI,CAACL,IAAI,CAAC,EAAE;QACzB;QACAA,IAAI,GAAGM,QAAQ,CAACN,IAAI,CAAC;MACvB,CAAC,MAAM;QACL;QACA;QACAA,IAAI,GAAGA,IAAI,CAACO,OAAO,CAAC,IAAIC,MAAM,CAAC,KAAK,CAAC,EAAE,GAAG,CAAC;MAC7C;IACF;IAEA,IAAI,OAAOR,IAAI,KAAK,QAAQ,IAAIA,IAAI,CAACR,QAAQ,EAAE,CAACK,MAAM,KAAK,EAAE,EAAE;MAC7DG,IAAI,GAAGA,IAAI,GAAG,IAAI;IACpB;IACAI,IAAI,GAAG,IAAIK,IAAI,CAACT,IAAI,CAAC;EACvB;EACA,IAAMU,SAAS,GAAG;IAChBC,CAAC,EAAEP,IAAI,CAACQ,WAAW,EAAE;IACrBC,CAAC,EAAET,IAAI,CAACU,QAAQ,EAAE,GAAG,CAAC;IACtBC,CAAC,EAAEX,IAAI,CAACY,OAAO,EAAE;IACjBC,CAAC,EAAEb,IAAI,CAACc,QAAQ,EAAE;IAClBC,CAAC,EAAEf,IAAI,CAACgB,UAAU,EAAE;IACpBC,CAAC,EAAEjB,IAAI,CAACkB,UAAU,EAAE;IACpBC,CAAC,EAAEnB,IAAI,CAACoB,MAAM;EAChB,CAAC;EACD,IAAMC,QAAQ,GAAGtB,MAAM,CAACI,OAAO,CAAC,iBAAiB,EAAE,UAACmB,MAAM,EAAE5B,GAAG,EAAK;IAClE,IAAM6B,KAAK,GAAGjB,SAAS,CAACZ,GAAG,CAAC;IAC5B;IACA,IAAIA,GAAG,KAAK,GAAG,EAAE;MACf,OAAO,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC,CAAC6B,KAAK,CAAC;IACnD;IACA,OAAOA,KAAK,CAACnC,QAAQ,EAAE,CAACoC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC;EAC1C,CAAC,CAAC;EACF,OAAOH,QAAQ;AACjB"}]}