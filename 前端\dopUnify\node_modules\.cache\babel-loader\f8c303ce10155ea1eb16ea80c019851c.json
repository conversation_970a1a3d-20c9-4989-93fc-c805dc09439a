{"remainingRequest": "D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\node_modules\\babel-loader\\lib\\index.js!D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\src\\components\\Dialog\\SunFlowDialog\\index.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\src\\components\\Dialog\\SunFlowDialog\\index.vue", "mtime": 1702370267267}, {"path": "D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\babel.config.js", "mtime": 1686019818359}, {"path": "D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\.babelrc", "mtime": 1705285078668}, {"path": "D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1655715099000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;AAwBA;AACA;EACAA;EACAC;IACAC;MACAC;MACAC;QACA;UACAC;YACA;YACAC;UACA;;UACAC;UAAA;UACAC;QACA;MACA;IACA;EACA;EACAC;IACA;MACAC;MACAC;MACA;MACAC;MAAA;MACAC;MAAA;MACAC;MACAC;IACA;EACA;;EACAC;EACAC;IACAL;MACA;QACA;MACA;IACA;IACAC;MAAA;MACA;QACA;QACA;QACA;UACA;QACA;MACA;IACA;EACA;EACAK;IAAA;IACA;IACA;MACA;QACA;QACA;MACA;QACA;QACA;QACA;MACA;MACA;IACA;EACA;EACAC;IACA;AACA;AACA;IACAC;MACA;MACA;QACA;QACA;QACA;QACAC;QACA;QACA;UACAA;UACAC;UACAnB;UACAY;UACAQ;UAAA;UACAC;QACA;QACAC;QACA;QACAC,iCACAC,sEACA;MACA;IACA;IACA;AACA;AACA;IACAC;MAAA;MACAC;QACA;QACA;QACA;UACA;UACA;QACA;UACA;UACA;YACAC;YACArB;UACA;UACA;UACA;YACA;YACA;YACA;UACA;QACA,WACAsB,uCACAA,mCACA;UACA;QACA;MACA;IACA;IACA;AACA;AACA;IACAC;MACA;MACA;MACA;IACA;EACA;AACA", "names": ["name", "props", "dialogConfig", "type", "default", "componentProps", "title", "visible", "task_id", "data", "iframeUrl", "extendRef", "is<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "flow_flag", "height", "projectName", "computed", "watch", "mounted", "methods", "postMess", "user", "common", "moudle_id", "alldata", "iframeWin", "JSON", "process", "postParam", "window", "key", "evt", "dialogClose"], "sourceRoot": "src/components/Dialog/SunFlowDialog", "sources": ["index.vue"], "sourcesContent": ["<!--\r\n* 外系统代码入口           :destroy-on-close=\"true\"\r\n-->\r\n<template>\r\n  <el-dialog\r\n    ref=\"refDialog\"\r\n    :visible.sync=\"dialogConfig.visible\"\r\n    :before-close=\"dialogClose\"\r\n    :close-on-click-modal=\"false\"\r\n    :append-to-body=\"true\"\r\n    v-bind=\"dialogConfig.componentProps\"\r\n  >\r\n    <iframe\r\n      :ref=\"extendRef\"\r\n      :v-if=\"dialogConfig.visible\"\r\n      class=\"extendIframe\"\r\n      :src=\"iframeUrl\"\r\n      frameborder=\"0\"\r\n      :style=\"{ height }\"\r\n    />\r\n  </el-dialog>\r\n</template>\r\n\r\n<script>\r\nimport { deepClone } from '@/utils/common'\r\nexport default {\r\n  name: 'SunFlowDialog',\r\n  props: {\r\n    dialogConfig: {\r\n      type: Object,\r\n      default: () => {\r\n        return {\r\n          componentProps: {\r\n            // 弹出框属性\r\n            title: '' // 弹出框标题 1-本地授权确认 2-远程审核提交\r\n          },\r\n          visible: false, // 显示隐藏配置\r\n          task_id: '' // 流程id\r\n        }\r\n      }\r\n    }\r\n  },\r\n  data() {\r\n    return {\r\n      iframeUrl: '',\r\n      extendRef: 'extendRef' + new Date().getTime(),\r\n      // extendRef: 'extendRef',\r\n      isChildrenReady: false, // 子工程是否准备好\r\n      flow_flag: false, // 子工程弹窗打开状态\r\n      height: 0,\r\n      projectName: '' // 子项目名称\r\n    }\r\n  },\r\n  computed: {},\r\n  watch: {\r\n    isChildrenReady(val) {\r\n      if (val) {\r\n        this.postMess()\r\n      }\r\n    },\r\n    flow_flag(val) {\r\n      if (val) {\r\n        this.dialogClose() // 流程弹窗关闭\r\n        this.$emit('queryList')\r\n        this.$nextTick(() => {\r\n          this.$bus.$emit('moudleSubUpdate', true) // true  // 更新系统消息弹窗的数据/更新系统消息右下角弹窗数据\r\n        })\r\n      }\r\n    }\r\n  },\r\n  mounted() {\r\n    this.height = document.body.clientHeight - 54 - 64 + 'px'\r\n    this.$nextTick().then(() => {\r\n      if (process.env.NODE_ENV !== 'development') {\r\n        // 生产环境\r\n        this.iframeUrl = `${window.location.origin}${window.location.pathname}redirect/SunAOS/iframe.html`\r\n      } else {\r\n        // 开发环境、 本地测试\r\n        // this.iframeUrl = `http://***********/dopUnify/redirect/unify/iframe.html`\r\n        this.iframeUrl = 'http://localhost:5000/SunAOS/iframe.html'\r\n      }\r\n      this.postParam()\r\n    })\r\n  },\r\n  methods: {\r\n    /**\r\n     *向iframe子页面发送消息\r\n     */\r\n    postMess() {\r\n      const mapFrame = this.$refs[this.extendRef]\r\n      if (mapFrame) {\r\n        const iframeWin = mapFrame.contentWindow\r\n        const user = deepClone(this.$store.state.user)\r\n        // user.routeM.path = `http://***********/dopUnify/redirect/unify/iframe.html?/unify/static/html/flow/workflow.html` // 本地测试\r\n        user.routeM.path = `${this.iframeUrl}?/unify/${this.dialogConfig.childFlowUrl}`\r\n        this.$store.commit('common/SET_FLOW_DATA2', this.dialogConfig.module_id)\r\n        const msg = {\r\n          user: user,\r\n          common: this.$store.state.common,\r\n          type: 'dopUnify',\r\n          projectName: this.projectName,\r\n          moudle_id: this.dialogConfig.module_id, // 模板id\r\n          alldata: this.dialogConfig\r\n        }\r\n        iframeWin.postMessage(\r\n          // 向子工程发送\r\n          JSON.parse(JSON.stringify(msg)),\r\n          process.env.NODE_ENV === 'development' ? '*' : window.location.origin\r\n        )\r\n      }\r\n    },\r\n    /**\r\n     * 接收iframe子页面消息\r\n     */\r\n    postParam() {\r\n      window.addEventListener('message', (evt) => {\r\n        // 接收子工程数据\r\n        // 若子页面已经加载好通知父工程修改了isChildrenReady的状态\r\n        if (evt.data.type === 'dopUnify') {\r\n          this.isChildrenReady = true // true表示子工程已经准备好向父工程发送消息了\r\n          this.projectName = evt.data.projectName\r\n        } else if (evt.data.type === 'saveData') {\r\n          // 子工程的数据存储\r\n          this.$store.commit('common/SET_SAVE_DATA', {\r\n            key: evt.data.projectName,\r\n            data: evt.data.data\r\n          })\r\n          // flow_close_flag 初始值为true  流程提交后为false\r\n          if (evt.data.data.localObject.flow_close_flag) {\r\n            // 菜单审核的流程详情不调用此方法 !evt.data.data.localObject.flow_close_flag===true\r\n            // false表示流程提交完毕  父组件弹窗应关闭\r\n            this.flow_flag = true\r\n          }\r\n        } else if (\r\n          evt.data.type === 'closeFlowDialog' &&\r\n          evt.data.projectName === 'sunaos'\r\n        ) {\r\n          this.dialogClose()\r\n        }\r\n      })\r\n    },\r\n    /**\r\n     * 弹出框：关闭\r\n     */\r\n    dialogClose() {\r\n      this.$store.state.common.flow.module_id = '' // 置空流程ID\r\n      // this.$store.state.common.flow.workflow_dataStr.flow_type = 'add'\r\n      this.$emit('dialogClose', this.extendRef) // this.flowConfig.visible = false父组件流程弹窗关闭\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n:deep(.el-dialog) {\r\n  height: 100%;\r\n  margin-bottom: 0 !important;\r\n}\r\n.extendIframe {\r\n  width: 100%;\r\n  height: 100%;\r\n  top: 5rem;\r\n  bottom: 0;\r\n  overflow: hidden;\r\n}\r\n// ::v-deep {\r\n//   .el-dialog__header {\r\n//     background: #2670f5;\r\n//   }\r\n// }\r\n</style>\r\n"]}]}