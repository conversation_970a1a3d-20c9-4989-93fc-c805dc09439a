{"remainingRequest": "D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\node_modules\\thread-loader\\dist\\cjs.js!D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\node_modules\\babel-loader\\lib\\index.js!D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\node_modules\\qiankun\\es\\sandbox\\patchers\\dynamicAppend\\forLooseSandbox.js", "dependencies": [{"path": "D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\node_modules\\qiankun\\es\\sandbox\\patchers\\dynamicAppend\\forLooseSandbox.js", "mtime": 1667130453000}, {"path": "D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\babel.config.js", "mtime": 1686019818359}, {"path": "D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\node_modules\\thread-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, null]}