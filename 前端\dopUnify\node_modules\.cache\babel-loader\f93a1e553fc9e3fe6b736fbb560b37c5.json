{"remainingRequest": "D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\node_modules\\babel-loader\\lib\\index.js!D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\node_modules\\eslint-loader\\index.js??ref--13-0!D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\src\\store\\getters.js", "dependencies": [{"path": "D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\src\\store\\getters.js", "mtime": 1690773061214}, {"path": "D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\babel.config.js", "mtime": 1686019818359}, {"path": "D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\node_modules\\eslint-loader\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:dmFyIGdldHRlcnMgPSB7CiAgc2lkZWJhcjogZnVuY3Rpb24gc2lkZWJhcihzdGF0ZSkgewogICAgcmV0dXJuIHN0YXRlLmFwcC5zaWRlYmFyOwogIH0sCiAgLy8gc2l6ZTogc3RhdGUgPT4gc3RhdGUuYXBwLnNpemUsCiAgZGV2aWNlOiBmdW5jdGlvbiBkZXZpY2Uoc3RhdGUpIHsKICAgIHJldHVybiBzdGF0ZS5hcHAuZGV2aWNlOwogIH0sCiAgdmlzaXRlZFZpZXdzOiBmdW5jdGlvbiB2aXNpdGVkVmlld3Moc3RhdGUpIHsKICAgIHJldHVybiBzdGF0ZS50YWdzVmlldy52aXNpdGVkVmlld3M7CiAgfSwKICBjYWNoZWRWaWV3czogZnVuY3Rpb24gY2FjaGVkVmlld3Moc3RhdGUpIHsKICAgIHJldHVybiBzdGF0ZS50YWdzVmlldy5jYWNoZWRWaWV3czsKICB9LAogIHRva2VuOiBmdW5jdGlvbiB0b2tlbihzdGF0ZSkgewogICAgcmV0dXJuIHN0YXRlLnVzZXIudG9rZW47CiAgfSwKICBhdmF0YXI6IGZ1bmN0aW9uIGF2YXRhcihzdGF0ZSkgewogICAgcmV0dXJuIHN0YXRlLnVzZXIuYXZhdGFyOwogIH0sCiAgdXNlck5vOiBmdW5jdGlvbiB1c2VyTm8oc3RhdGUpIHsKICAgIHJldHVybiBzdGF0ZS51c2VyLnVzZXJObzsKICB9LAogIC8vIOeUqOaIt+WQjQogIHVzZXJMZXZlbDogZnVuY3Rpb24gdXNlckxldmVsKHN0YXRlKSB7CiAgICByZXR1cm4gc3RhdGUudXNlci51c2VyTGV2ZWw7CiAgfSwKICAvLyDnlKjmiLfnuqfliKsKICB1c2VyTmFtZTogZnVuY3Rpb24gdXNlck5hbWUoc3RhdGUpIHsKICAgIHJldHVybiBzdGF0ZS51c2VyLnVzZXJOYW1lOwogIH0sCiAgLy8g55So5oi35ZCN56ewCiAgb3JnYW5ObzogZnVuY3Rpb24gb3JnYW5ObyhzdGF0ZSkgewogICAgcmV0dXJuIHN0YXRlLnVzZXIub3JnYW5ObzsKICB9LAogIC8vIOacuuaehOWPtwogIHJvbGVObzogZnVuY3Rpb24gcm9sZU5vKHN0YXRlKSB7CiAgICByZXR1cm4gc3RhdGUudXNlci5yb2xlTm87CiAgfSwKICAvLyDop5LoibIKICBpc0ZpcnN0TG9naW46IGZ1bmN0aW9uIGlzRmlyc3RMb2dpbihzdGF0ZSkgewogICAgcmV0dXJuIHN0YXRlLnVzZXIubG9naW5JbmZvLmlzRmlyc3RMb2dpbjsKICB9LAogIC8vIOWIneWni+Wvhuegge<PERSON>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"}, {"version": 3, "names": ["getters", "sidebar", "state", "app", "device", "visitedViews", "tagsView", "cachedViews", "token", "user", "avatar", "userNo", "userLevel", "userName", "organNo", "roleNo", "is<PERSON>irstL<PERSON>in", "loginInfo", "isOverFlag", "customAttrMap", "organLevel", "introduction", "roles", "initParams", "permission_routes", "permission", "routes", "permission_routes_array", "addRoutesArray", "theme", "settings", "themeName", "titles", "common", "organTreeObject", "organTree", "rightOrganTree", "organList", "dictionaryLet", "dictionaryTree", "pageSize", "externalData", "DataSource", "externalDataSource", "homeMoudles", "homeMoudlesChecked", "roleList", "menuArr", "roleLevelData", "userList", "routesParent", "isShowSearch", "releaseTaskFlag", "flowPath", "releaseTaskId", "menuAuditData", "ismClassList", "ismDateList", "ismEmpList", "rightOrganArray", "ismOrganList", "ismPostList", "menuCount", "menuExist", "singleLogin", "loginTag", "loginTerminal", "loginKkIP", "kkIP", "loginKkport", "kkport", "includeModule", "pageName"], "sources": ["D:/1_Project/XYD_Project/dop-4.0/dop-4.1-qianduan-unify/dopUnify/src/store/getters.js"], "sourcesContent": ["const getters = {\n  sidebar: (state) => state.app.sidebar,\n  // size: state => state.app.size,\n  device: (state) => state.app.device,\n  visitedViews: (state) => state.tagsView.visitedViews,\n  cachedViews: (state) => state.tagsView.cachedViews,\n  token: (state) => state.user.token,\n  avatar: (state) => state.user.avatar,\n  userNo: (state) => state.user.userNo, // 用户名\n  userLevel: (state) => state.user.userLevel, // 用户级别\n  userName: (state) => state.user.userName, // 用户名称\n  organNo: (state) => state.user.organNo, // 机构号\n  roleNo: (state) => state.user.roleNo, // 角色\n  isFirstLogin: (state) => state.user.loginInfo.isFirstLogin, // 初始密码标识\n  isOverFlag: (state) => state.user.loginInfo.isOverFlag, // 过期密码标识\n  customAttrMap: (state) => state.user.loginInfo.customAttrMap, // 用户选择的主题色\n  organLevel: (state) => state.user.organLevel, // 机构级别\n  introduction: (state) => state.user.introduction,\n  roles: (state) => state.user.roles,\n  initParams: (state) => state.user.initParams, // 初始化参数\n  permission_routes: (state) => state.permission.routes,\n  permission_routes_array: (state) => state.permission.addRoutesArray, // 菜单数组形式（单个菜单）\n  theme: (state) => state.settings.theme,\n  themeName: (state) => state.settings.themeName,\n  titles: (state) => state.common.titles,\n  organTreeObject: (state) => state.common.organTreeObject, // 机构数据为对象\n  organTree: (state) => state.common.rightOrganTree, // 机构数据为树\n  rightOrganTree: (state) => state.common.rightOrganTree, // 机构数据为树\n  organList: (state) => state.common.organList, // 机构数据为数组\n  dictionaryLet: (state) => state.common.dictionaryLet, // 后台传入字典\n  dictionaryTree: (state) => state.common.dictionaryTree, // 字典树\n  pageSize: (state) => state.common.pageSize, // 当前页默认条数\n  externalData: (state) => state.common.externalData, // 外表字典\n  DataSource: (state) => state.common.dictionaryTree, // 数据字典数组\n  externalDataSource: (state) => state.common.externalDataSource, // 外表字典数组\n  homeMoudles: (state) => state.common.homeMoudles, // 默认显示模块\n  homeMoudlesChecked: (state) => state.common.homeMoudlesChecked, // 选择显示模块\n  roleList: (state) => state.common.roleList, // 角色数据为数组\n  menuArr: (state) => state.common.menuArr, // 菜单数据\n  roleLevelData: (state) => state.common.roleLevelData, // 角色数据为数组\n  userList: (state) => state.common.userList, // 全行用户信息\n  routesParent: (state) => state.common.routesParent, // 父级菜单为数组\n  isShowSearch: (state) => state.common.isShowSearch, //  菜单搜索框是否显示\n  releaseTaskFlag: (state) => state.flowPath.releaseTaskFlag, // 是否需要释放任务标志\n  releaseTaskId: (state) => state.flowPath.releaseTaskId, // 释放任务的id\n  menuAuditData: (state) => state.user.menuAuditData, // 菜单按钮审核方式数组\n  ismClassList: (state) => state.flowPath.ismClassList, // 排班班次\n  ismDateList: (state) => state.flowPath.ismDateList, // 排班日期\n  ismEmpList: (state) => state.flowPath.ismEmpList, // 排班人员\n  rightOrganArray: (state) => state.common.rightOrganArray, // 权限机构数组\n  ismOrganList: (state) => state.flowPath.ismOrganList, // 排班日期\n  ismPostList: (state) => state.flowPath.ismPostList, // 排班岗位\n  menuCount: (state) => state.flowPath.menuCount, // 点击的菜单次数配置\n  menuExist: (state) => state.flowPath.menuExist, // 存菜单名称\n  singleLogin: (state) => state.user.loginInfo.singleLogin, // 是否单点登录\n  loginTag: (state) => state.user.loginInfo.loginTerminal, // 是否是pc,webScoket里ws请求地址会用到，目写死的1-pc\n  loginKkIP: (state) => state.user.loginInfo.kkIP, // kkIp\n  loginKkport: (state) => state.user.loginInfo.kkport, // kkport\n  includeModule: (state) => state.user.includeModule, // 用户所包含的模块\n  pageName: (state) => state.flowPath.pageName // 合同管理-维护/总价页面\n}\nexport default getters\n"], "mappings": "AAAA,IAAMA,OAAO,GAAG;EACdC,OAAO,EAAE,iBAACC,KAAK;IAAA,OAAKA,KAAK,CAACC,GAAG,CAACF,OAAO;EAAA;EACrC;EACAG,MAAM,EAAE,gBAACF,KAAK;IAAA,OAAKA,KAAK,CAACC,GAAG,CAACC,MAAM;EAAA;EACnCC,YAAY,EAAE,sBAACH,KAAK;IAAA,OAAKA,KAAK,CAACI,QAAQ,CAACD,YAAY;EAAA;EACpDE,WAAW,EAAE,qBAACL,KAAK;IAAA,OAAKA,KAAK,CAACI,QAAQ,CAACC,WAAW;EAAA;EAClDC,KAAK,EAAE,eAACN,KAAK;IAAA,OAAKA,KAAK,CAACO,IAAI,CAACD,KAAK;EAAA;EAClCE,MAAM,EAAE,gBAACR,KAAK;IAAA,OAAKA,KAAK,CAACO,IAAI,CAACC,MAAM;EAAA;EACpCC,MAAM,EAAE,gBAACT,KAAK;IAAA,OAAKA,KAAK,CAACO,IAAI,CAACE,MAAM;EAAA;EAAE;EACtCC,SAAS,EAAE,mBAACV,KAAK;IAAA,OAAKA,KAAK,CAACO,IAAI,CAACG,SAAS;EAAA;EAAE;EAC5CC,QAAQ,EAAE,kBAACX,KAAK;IAAA,OAAKA,KAAK,CAACO,IAAI,CAACI,QAAQ;EAAA;EAAE;EAC1CC,OAAO,EAAE,iBAACZ,KAAK;IAAA,OAAKA,KAAK,CAACO,IAAI,CAACK,OAAO;EAAA;EAAE;EACxCC,MAAM,EAAE,gBAACb,KAAK;IAAA,OAAKA,KAAK,CAACO,IAAI,CAACM,MAAM;EAAA;EAAE;EACtCC,YAAY,EAAE,sBAACd,KAAK;IAAA,OAAKA,KAAK,CAACO,IAAI,CAACQ,SAAS,CAACD,YAAY;EAAA;EAAE;EAC5DE,UAAU,EAAE,oBAAChB,KAAK;IAAA,OAAKA,KAAK,CAACO,IAAI,CAACQ,SAAS,CAACC,UAAU;EAAA;EAAE;EACxDC,aAAa,EAAE,uBAACjB,KAAK;IAAA,OAAKA,KAAK,CAACO,IAAI,CAACQ,SAAS,CAACE,aAAa;EAAA;EAAE;EAC9DC,UAAU,EAAE,oBAAClB,KAAK;IAAA,OAAKA,KAAK,CAACO,IAAI,CAACW,UAAU;EAAA;EAAE;EAC9CC,YAAY,EAAE,sBAACnB,KAAK;IAAA,OAAKA,KAAK,CAACO,IAAI,CAACY,YAAY;EAAA;EAChDC,KAAK,EAAE,eAACpB,KAAK;IAAA,OAAKA,KAAK,CAACO,IAAI,CAACa,KAAK;EAAA;EAClCC,UAAU,EAAE,oBAACrB,KAAK;IAAA,OAAKA,KAAK,CAACO,IAAI,CAACc,UAAU;EAAA;EAAE;EAC9CC,iBAAiB,EAAE,2BAACtB,KAAK;IAAA,OAAKA,KAAK,CAACuB,UAAU,CAACC,MAAM;EAAA;EACrDC,uBAAuB,EAAE,iCAACzB,KAAK;IAAA,OAAKA,KAAK,CAACuB,UAAU,CAACG,cAAc;EAAA;EAAE;EACrEC,KAAK,EAAE,eAAC3B,KAAK;IAAA,OAAKA,KAAK,CAAC4B,QAAQ,CAACD,KAAK;EAAA;EACtCE,SAAS,EAAE,mBAAC7B,KAAK;IAAA,OAAKA,KAAK,CAAC4B,QAAQ,CAACC,SAAS;EAAA;EAC9CC,MAAM,EAAE,gBAAC9B,KAAK;IAAA,OAAKA,KAAK,CAAC+B,MAAM,CAACD,MAAM;EAAA;EACtCE,eAAe,EAAE,yBAAChC,KAAK;IAAA,OAAKA,KAAK,CAAC+B,MAAM,CAACC,eAAe;EAAA;EAAE;EAC1DC,SAAS,EAAE,mBAACjC,KAAK;IAAA,OAAKA,KAAK,CAAC+B,MAAM,CAACG,cAAc;EAAA;EAAE;EACnDA,cAAc,EAAE,wBAAClC,KAAK;IAAA,OAAKA,KAAK,CAAC+B,MAAM,CAACG,cAAc;EAAA;EAAE;EACxDC,SAAS,EAAE,mBAACnC,KAAK;IAAA,OAAKA,KAAK,CAAC+B,MAAM,CAACI,SAAS;EAAA;EAAE;EAC9CC,aAAa,EAAE,uBAACpC,KAAK;IAAA,OAAKA,KAAK,CAAC+B,MAAM,CAACK,aAAa;EAAA;EAAE;EACtDC,cAAc,EAAE,wBAACrC,KAAK;IAAA,OAAKA,KAAK,CAAC+B,MAAM,CAACM,cAAc;EAAA;EAAE;EACxDC,QAAQ,EAAE,kBAACtC,KAAK;IAAA,OAAKA,KAAK,CAAC+B,MAAM,CAACO,QAAQ;EAAA;EAAE;EAC5CC,YAAY,EAAE,sBAACvC,KAAK;IAAA,OAAKA,KAAK,CAAC+B,MAAM,CAACQ,YAAY;EAAA;EAAE;EACpDC,UAAU,EAAE,oBAACxC,KAAK;IAAA,OAAKA,KAAK,CAAC+B,MAAM,CAACM,cAAc;EAAA;EAAE;EACpDI,kBAAkB,EAAE,4BAACzC,KAAK;IAAA,OAAKA,KAAK,CAAC+B,MAAM,CAACU,kBAAkB;EAAA;EAAE;EAChEC,WAAW,EAAE,qBAAC1C,KAAK;IAAA,OAAKA,KAAK,CAAC+B,MAAM,CAACW,WAAW;EAAA;EAAE;EAClDC,kBAAkB,EAAE,4BAAC3C,KAAK;IAAA,OAAKA,KAAK,CAAC+B,MAAM,CAACY,kBAAkB;EAAA;EAAE;EAChEC,QAAQ,EAAE,kBAAC5C,KAAK;IAAA,OAAKA,KAAK,CAAC+B,MAAM,CAACa,QAAQ;EAAA;EAAE;EAC5CC,OAAO,EAAE,iBAAC7C,KAAK;IAAA,OAAKA,KAAK,CAAC+B,MAAM,CAACc,OAAO;EAAA;EAAE;EAC1CC,aAAa,EAAE,uBAAC9C,KAAK;IAAA,OAAKA,KAAK,CAAC+B,MAAM,CAACe,aAAa;EAAA;EAAE;EACtDC,QAAQ,EAAE,kBAAC/C,KAAK;IAAA,OAAKA,KAAK,CAAC+B,MAAM,CAACgB,QAAQ;EAAA;EAAE;EAC5CC,YAAY,EAAE,sBAAChD,KAAK;IAAA,OAAKA,KAAK,CAAC+B,MAAM,CAACiB,YAAY;EAAA;EAAE;EACpDC,YAAY,EAAE,sBAACjD,KAAK;IAAA,OAAKA,KAAK,CAAC+B,MAAM,CAACkB,YAAY;EAAA;EAAE;EACpDC,eAAe,EAAE,yBAAClD,KAAK;IAAA,OAAKA,KAAK,CAACmD,QAAQ,CAACD,eAAe;EAAA;EAAE;EAC5DE,aAAa,EAAE,uBAACpD,KAAK;IAAA,OAAKA,KAAK,CAACmD,QAAQ,CAACC,aAAa;EAAA;EAAE;EACxDC,aAAa,EAAE,uBAACrD,KAAK;IAAA,OAAKA,KAAK,CAACO,IAAI,CAAC8C,aAAa;EAAA;EAAE;EACpDC,YAAY,EAAE,sBAACtD,KAAK;IAAA,OAAKA,KAAK,CAACmD,QAAQ,CAACG,YAAY;EAAA;EAAE;EACtDC,WAAW,EAAE,qBAACvD,KAAK;IAAA,OAAKA,KAAK,CAACmD,QAAQ,CAACI,WAAW;EAAA;EAAE;EACpDC,UAAU,EAAE,oBAACxD,KAAK;IAAA,OAAKA,KAAK,CAACmD,QAAQ,CAACK,UAAU;EAAA;EAAE;EAClDC,eAAe,EAAE,yBAACzD,KAAK;IAAA,OAAKA,KAAK,CAAC+B,MAAM,CAAC0B,eAAe;EAAA;EAAE;EAC1DC,YAAY,EAAE,sBAAC1D,KAAK;IAAA,OAAKA,KAAK,CAACmD,QAAQ,CAACO,YAAY;EAAA;EAAE;EACtDC,WAAW,EAAE,qBAAC3D,KAAK;IAAA,OAAKA,KAAK,CAACmD,QAAQ,CAACQ,WAAW;EAAA;EAAE;EACpDC,SAAS,EAAE,mBAAC5D,KAAK;IAAA,OAAKA,KAAK,CAACmD,QAAQ,CAACS,SAAS;EAAA;EAAE;EAChDC,SAAS,EAAE,mBAAC7D,KAAK;IAAA,OAAKA,KAAK,CAACmD,QAAQ,CAACU,SAAS;EAAA;EAAE;EAChDC,WAAW,EAAE,qBAAC9D,KAAK;IAAA,OAAKA,KAAK,CAACO,IAAI,CAACQ,SAAS,CAAC+C,WAAW;EAAA;EAAE;EAC1DC,QAAQ,EAAE,kBAAC/D,KAAK;IAAA,OAAKA,KAAK,CAACO,IAAI,CAACQ,SAAS,CAACiD,aAAa;EAAA;EAAE;EACzDC,SAAS,EAAE,mBAACjE,KAAK;IAAA,OAAKA,KAAK,CAACO,IAAI,CAACQ,SAAS,CAACmD,IAAI;EAAA;EAAE;EACjDC,WAAW,EAAE,qBAACnE,KAAK;IAAA,OAAKA,KAAK,CAACO,IAAI,CAACQ,SAAS,CAACqD,MAAM;EAAA;EAAE;EACrDC,aAAa,EAAE,uBAACrE,KAAK;IAAA,OAAKA,KAAK,CAACO,IAAI,CAAC8D,aAAa;EAAA;EAAE;EACpDC,QAAQ,EAAE,kBAACtE,KAAK;IAAA,OAAKA,KAAK,CAACmD,QAAQ,CAACmB,QAAQ;EAAA,EAAC;AAC/C,CAAC;;AACD,eAAexE,OAAO"}]}