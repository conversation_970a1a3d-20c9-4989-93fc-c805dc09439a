{"remainingRequest": "E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-q<PERSON><PERSON>n\\数字运营平台-统一门户工程\\dopUnify\\node_modules\\thread-loader\\dist\\cjs.js!E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan\\数字运营平台-统一门户工程\\dopUnify\\node_modules\\babel-loader\\lib\\index.js!E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan\\数字运营平台-统一门户工程\\dopUnify\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan\\数字运营平台-统一门户工程\\dopUnify\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-q<PERSON>duan\\数字运营平台-统一门户工程\\dopUnify\\src\\views\\system\\config\\external\\component\\table\\index.vue?vue&type=script&lang=js&", "dependencies": [{"path": "E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-<PERSON><PERSON><PERSON>n\\数字运营平台-统一门户工程\\dopUnify\\src\\views\\system\\config\\external\\component\\table\\index.vue", "mtime": 1686019808029}, {"path": "E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-<PERSON><PERSON><PERSON>n\\数字运营平台-统一门户工程\\dopUnify\\babel.config.js", "mtime": 1686019818359}, {"path": "E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-<PERSON><PERSON><PERSON>n\\数字运营平台-统一门户工程\\dopUnify\\.babelrc", "mtime": 1705285078668}, {"path": "E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-<PERSON><PERSON><PERSON>n\\数字运营平台-统一门户工程\\dopUnify\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-<PERSON><PERSON><PERSON>n\\数字运营平台-统一门户工程\\dopUnify\\node_modules\\thread-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-<PERSON><PERSON><PERSON>n\\数字运营平台-统一门户工程\\dopUnify\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-<PERSON><PERSON><PERSON>n\\数字运营平台-统一门户工程\\dopUnify\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-<PERSON><PERSON><PERSON>n\\数字运营平台-统一门户工程\\dopUnify\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1655715099000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, null]}