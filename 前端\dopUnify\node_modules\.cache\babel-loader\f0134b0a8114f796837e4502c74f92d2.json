{"remainingRequest": "E:\\华南区项目\\中国银行\\开发区\\科技公司运营平台\\前端\\dopUnify\\node_modules\\thread-loader\\dist\\cjs.js!E:\\华南区项目\\中国银行\\开发区\\科技公司运营平台\\前端\\dopUnify\\node_modules\\babel-loader\\lib\\index.js!E:\\华南区项目\\中国银行\\开发区\\科技公司运营平台\\前端\\dopUnify\\src\\components\\Layout\\ThemePicker\\indexColor.js", "dependencies": [{"path": "E:\\华南区项目\\中国银行\\开发区\\科技公司运营平台\\前端\\dopUnify\\src\\components\\Layout\\ThemePicker\\indexColor.js", "mtime": 1721631954662}, {"path": "E:\\华南区项目\\中国银行\\开发区\\科技公司运营平台\\前端\\dopUnify\\babel.config.js", "mtime": 1721631954662}, {"path": "E:\\华南区项目\\中国银行\\开发区\\科技公司运营平台\\前端\\dopUnify\\.babelrc", "mtime": 1721631954662}, {"path": "E:\\华南区项目\\中国银行\\开发区\\科技公司运营平台\\前端\\dopUnify\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "E:\\华南区项目\\中国银行\\开发区\\科技公司运营平台\\前端\\dopUnify\\node_modules\\thread-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "E:\\华南区项目\\中国银行\\开发区\\科技公司运营平台\\前端\\dopUnify\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, null]}