{"remainingRequest": "E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-q<PERSON><PERSON>n\\数字运营平台-统一门户工程\\dopUnify\\node_modules\\babel-loader\\lib\\index.js!E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-q<PERSON>duan\\数字运营平台-统一门户工程\\dopUnify\\node_modules\\eslint-loader\\index.js??ref--13-0!E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan\\数字运营平台-统一门户工程\\dopUnify\\src\\utils\\externalData.js", "dependencies": [{"path": "E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-<PERSON><PERSON><PERSON>n\\数字运营平台-统一门户工程\\dopUnify\\src\\utils\\externalData.js", "mtime": 1686019809810}, {"path": "E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-<PERSON><PERSON><PERSON>n\\数字运营平台-统一门户工程\\dopUnify\\babel.config.js", "mtime": 1686019818359}, {"path": "E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-<PERSON><PERSON><PERSON>n\\数字运营平台-统一门户工程\\dopUnify\\.babelrc", "mtime": 1705285078668}, {"path": "E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-<PERSON><PERSON><PERSON>n\\数字运营平台-统一门户工程\\dopUnify\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-<PERSON><PERSON><PERSON>n\\数字运营平台-统一门户工程\\dopUnify\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-<PERSON><PERSON><PERSON>n\\数字运营平台-统一门户工程\\dopUnify\\node_modules\\eslint-loader\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:aW1wb3J0IF90b0NvbnN1bWFibGVBcnJheSBmcm9tICJFOi8xX1Byb2plY3QvWFlEX1Byb2plY3QvZG9wLTQuMC9kb3AtNC4xLXFpYW5kdWFuL1x1NjU3MFx1NUI1N1x1OEZEMFx1ODQyNVx1NUU3M1x1NTNGMC1cdTdFREZcdTRFMDBcdTk1RThcdTYyMzdcdTVERTVcdTdBMEIvZG9wVW5pZnkvbm9kZV9tb2R1bGVzL0BiYWJlbC9ydW50aW1lL2hlbHBlcnMvZXNtL3RvQ29uc3VtYWJsZUFycmF5LmpzIjsKaW1wb3J0IF9jcmVhdGVGb3JPZkl0ZXJhdG9ySGVscGVyIGZyb20gIkU6LzFfUHJvamVjdC9YWURfUHJvamVjdC9kb3AtNC4wL2RvcC00LjEtcWlhbmR1YW4vXHU2NTcwXHU1QjU3XHU4RkQwXHU4NDI1XHU1RTczXHU1M0YwLVx1N0VERlx1NEUwMFx1OTVFOFx1NjIzN1x1NURFNVx1N0EwQi9kb3BVbmlmeS9ub2RlX21vZHVsZXMvQGJhYmVsL3J1bnRpbWUvaGVscGVycy9lc20vY3JlYXRlRm9yT2ZJdGVyYXRvckhlbHBlci5qcyI7CmltcG9ydCAiY29yZS1qcy9tb2R1bGVzL2VzLmFycmF5Lm1hcC5qcyI7CmltcG9ydCAiY29yZS1qcy9tb2R1bGVzL2VzLnJlZ2V4cC5leGVjLmpzIjsKaW1wb3J0ICJjb3JlLWpzL21vZHVsZXMvZXMuc3RyaW5nLnJlcGxhY2UuanMiOwppbXBvcnQgImNvcmUtanMvbW9kdWxlcy9lcy5mdW5jdGlvbi5uYW1lLmpzIjsKaW1wb3J0ICJjb3JlLWpzL21vZHVsZXMvZXMuYXJyYXkuZmluZC5qcyI7CmltcG9ydCAiY29yZS1qcy9tb2R1bGVzL2VzLm9iamVjdC50by1zdHJpbmcuanMiOwppbXBvcnQgImNvcmUtanMvbW9kdWxlcy9lcy5hcnJheS5jb25jYXQuanMiOwppbXBvcnQgImNvcmUtanMvbW9kdWxlcy9lcy5zdHJpbmcuc3RhcnRzLXdpdGguanMiOwppbXBvcnQgImNvcmUtanMvbW9kdWxlcy93ZWIuZG9tLWNvbGxlY3Rpb25zLmZvci1lYWNoLmpzIjsKaW1wb3J0ICJjb3JlLWpzL21vZHVsZXMvZXMuc3RyaW5nLnRyaW0uanMiOwovLyDlkITmqKHlnZfnmoTlpJbooajmlbDmja7mupAKaW1wb3J0IHN0b3JlIGZyb20gJ0Avc3RvcmUnOwoKLy8g5pm66IO95o6S54+tICAqKioqKioqKioqKiptb2R1bGVOYW1l5b+F6aG75pivYS1i55qE5b2i5byPLG1vZHVsZU1ldGhvZOeahOaWueazleWQjeimgeWSjG1ldGhvZHPkuK3nmoTmlrnms5Xkv53mjIHkuIDoh7QKZXhwb3J0IHZhciB6bnBiX2luZENvbmZpZyA9IHsKICBtb2R1bGVOYW1lOiAnem5wYi3mmbrog73mjpLnj60nLAogIC8vIOaJgOS7o+ihqOaooeWdlwogIG1vZHVsZU1ldGhvZDogJ3pucGJfSVNXQjAwMDEt6I635Y+W5o6S54+t5Lq65ZGYLHpucGJfSVNXQjAwMDIt6I635Y+W5o6S54+t5pel5pyfLHpucGJfSVNXQjAwMDUt6I635Y+W5o6S54+t54+t5qyhLHpucGJfSVNXQjAwMDMt6I635Y+W5o6S54+t5py65p6ELHpucGJfSVNXQjAwMDQt6I635Y+W5o6S54+t5bKX5L2NJywKICAvLyDmjInpkq7mlrnms5Xlj4rlkI3np7DvvIzlrprkuYnkuo5tZXRob2Rz5Lit5bm25LiA5LiA5a+55bqU77yM5pa55rOV5ZCN5Lul5qih5Z2XX+W8gOWktCArIOWKn+iDveagvOW8j+WRveWQjQogIG1ldGhvZHM6IHsKICAgIC8vIOiHquWumuS5ieaWueazlS3ojrflj5bmjpLnj63kurrlkZgKICAgIHpucGJfSVNXQjAwMDE6IGZ1bmN0aW9uIHpucGJfSVNXQjAwMDEoKSB7CiAgICAgIHZhciBlbXBMaXN0ID0gc3RvcmUuZ2V0dGVycy5pc21FbXBMaXN0OyAvLyDmjpLnj63kurrlkZgKICAgICAgcmV0dXJuIGVtcExpc3Q7CiAgICB9LAogICAgLy8g6Ieq5a6a5LmJ5pa55rOVLeiOt+WPluaOkuePreaXpeacnwogICAgem5wYl9JU1dCMDAwMjogZnVuY3Rpb24gem5wYl9JU1dCMDAwMigpIHsKICAgICAgdmFyIGRhdGVBcnIgPSBzdG9yZS5nZXR0ZXJzLmlzbURhdGVMaXN0OyAvLyDmjpLnj63ml6XmnJ8KICAgICAgdmFyIHJlZyA9IC9eKFxkezR9KShcZHsyfSkoXGR7Mn0pJC87IC8vIOaXpeacn+agvOW8j+WMluato+WImQogICAgICB2YXIgZGF0ZUFycjIgPSBkYXRlQXJyLm1hcChmdW5jdGlvbiAoaXRlbSkgewogICAgICAgIGl0ZW0gPSBpdGVtLnJlcGxhY2UocmVnLCAnJDEtJDItJDMnKTsKICAgICAgICByZXR1cm4gaXRlbTsKICAgICAgfSk7CiAgICAgIHZhciBkYXkxID0gZGF0ZUFycjJbMF07IC8vIOi1t+Wni+aXpeacnwogICAgICAvLyDlsIbotbflp4vml6XmnJ/lvoDliY3mjqjkuIDlpKkgYmVnaW4KICAgICAgdmFyIGRhdGVUaW1lID0gbmV3IERhdGUoZGF5MSk7CiAgICAgIGRhdGVUaW1lID0gZGF0ZVRpbWUuc2V0RGF0ZShkYXRlVGltZS5nZXREYXRlKCkpOwogICAgICBkYXRlVGltZSA9IG5ldyBEYXRlKGRhdGVUaW1lKTsKICAgICAgZGF0ZVRpbWUgPSBkYXRlVGltZS5nZXRGdWxsWWVhcigpICsgJy0nICsgKGRhdGVUaW1lLmdldE1vbnRoKCkgKyAxKSArICctJyArIGRhdGVUaW1lLmdldERhdGUoKTsKICAgICAgZGF0ZUFycjJbMF0gPSBkYXRlVGltZTsKICAgICAgLy8g5bCG6LW35aeL5pel5pyf5b6A5YmN5o6o5LiA5aSpIGVuZAogICAgICByZXR1cm4gZGF0ZUFycjI7CiAgICB9LAogICAgLy8g6Ieq5a6a5LmJ5pa55rOVLeiOt+WPluaOkuePreacuuaehAogICAgem5wYl9JU1dCMDAwMzogZnVuY3Rpb24gem5wYl9JU1dCMDAwMygpIHsKICAgICAgdmFyIG9yZ2FuT2JqID0gc3RvcmUuZ2V0dGVycy5pc21Pcmdhbkxpc3Q7IC8vIOaOkuePreacuuaehAogICAgICB2YXIgb3JnYW5BcnIgPSBbXTsgLy8g55So5oi36YCJ5oup55qE5o6S54+t5py65p6ECiAgICAgIHZhciBfaXRlcmF0b3IgPSBfY3JlYXRlRm9yT2ZJdGVyYXRvckhlbHBlcihvcmdhbk9iai5zZWxlY3RlZE9yZ2FuTGlzdCksCiAgICAgICAgX3N0ZXA7CiAgICAgIHRyeSB7CiAgICAgICAgdmFyIF9sb29wID0gZnVuY3Rpb24gX2xvb3AoKSB7CiAgICAgICAgICB2YXIgaXRlbSA9IF9zdGVwLnZhbHVlOwogICAgICAgICAgLy8g6YGN5Y6G55So5oi36YCJ5oup55qE5o6S54+t54+t5qyhCiAgICAgICAgICB2YXIgb3JnYW5TaWduID0gb3JnYW5PYmoub3JnYW5MaXN0LmZpbmQoZnVuY3Rpb24gKGkpIHsKICAgICAgICAgICAgcmV0dXJuIGkuaWQgPT09IGl0ZW07CiAgICAgICAgICB9KTsgLy8gb3JnYW5TaWdu5Li65Y2V5Liq54+t5qyhCiAgICAgICAgICBvcmdhbkFyciA9IFtdLmNvbmNhdChfdG9Db25zdW1hYmxlQXJyYXkob3JnYW5BcnIpLCBbb3JnYW5TaWduXSk7CiAgICAgICAgfTsKICAgICAgICBmb3IgKF9pdGVyYXRvci5zKCk7ICEoX3N0ZXAgPSBfaXRlcmF0b3IubigpKS5kb25lOykgewogICAgICAgICAgX2xvb3AoKTsKICAgICAgICB9CiAgICAgIH0gY2F0Y2ggKGVycikgewogICAgICAgIF9pdGVyYXRvci5lKGVycik7CiAgICAgIH0gZmluYWxseSB7CiAgICAgICAgX2l0ZXJhdG9yLmYoKTsKICAgICAgfQogICAgICB2YXIgb3JnYW5BcnIyID0gb3JnYW5BcnIubWFwKGZ1bmN0aW9uIChpdGVtKSB7CiAgICAgICAgaXRlbVsnbGFiZWwnXSA9IGl0ZW0ubmFtZTsKICAgICAgICBpdGVtWyd2YWx1ZSddID0gaXRlbS5pZDsKICAgICAgICByZXR1cm4gaXRlbTsKICAgICAgfSk7CiAgICAgIHJldHVybiBvcmdhbkFycjI7CiAgICB9LAogICAgLy8g6Ieq5a6a5LmJ5pa55rOVLeiOt+WPluaOkuePreWyl+S9jQogICAgem5wYl9JU1dCMDAwNDogZnVuY3Rpb24gem5wYl9JU1dCMDAwNCgpIHsKICAgICAgdmFyIG9yZ2FuT2JqID0gc3RvcmUuZ2V0dGVycy5pc21Qb3N0TGlzdDsgLy8g5o6S54+t5py65p6ECiAgICAgIHJldHVybiBvcmdhbk9iajsKICAgIH0sCiAgICAvLyDoh6rlrprkuYnmlrnms5Ut6I635Y+W5o6S54+t54+t5qyhCiAgICB6bnBiX0lTV0IwMDA1OiBmdW5jdGlvbiB6bnBiX0lTV0IwMDA1KCkgewogICAgICB2YXIgY2xhc3NPYmogPSBzdG9yZS5nZXR0ZXJzLmlzbUNsYXNzTGlzdDsgLy8g5o6S54+t54+t5qyhCiAgICAgIHZhciBjbGFzc0FyciA9IFtdOyAvLyDnlKjmiLfpgInmi6nnmoTmjpLnj63nj63mrKEKICAgICAgdmFyIF9pdGVyYXRvcjIgPSBfY3JlYXRlRm9yT2ZJdGVyYXRvckhlbHBlcihjbGFzc09iai5zZWxlY3RlZENsYXNzTGlzdCksCiAgICAgICAgX3N0ZXAyOwogICAgICB0cnkgewogICAgICAgIHZhciBfbG9vcDIgPSBmdW5jdGlvbiBfbG9vcDIoKSB7CiAgICAgICAgICB2YXIgaXRlbSA9IF9zdGVwMi52YWx1ZTsKICAgICAgICAgIC8vIOmBjeWOhueUqOaIt+mAieaLqeeahOaOkuePreePreasoQogICAgICAgICAgdmFyIGNsYXNzU2lnbiA9IGNsYXNzT2JqLmNsYXNzTGlzdC5maW5kKGZ1bmN0aW9uIChpKSB7CiAgICAgICAgICAgIHJldHVybiBpLnZhbHVlID09PSBpdGVtOwogICAgICAgICAgfSk7IC8vIGNsYXNzU2lnbuS4uuWNleS4quePreasoQogICAgICAgICAgY2xhc3NBcnIgPSBbXS5jb25jYXQoX3RvQ29uc3VtYWJsZUFycmF5KGNsYXNzQXJyKSwgW2NsYXNzU2lnbl0pOwogICAgICAgIH07CiAgICAgICAgZm9yIChfaXRlcmF0b3IyLnMoKTsgIShfc3RlcDIgPSBfaXRlcmF0b3IyLm4oKSkuZG9uZTspIHsKICAgICAgICAgIF9sb29wMigpOwogICAgICAgIH0KICAgICAgfSBjYXRjaCAoZXJyKSB7CiAgICAgICAgX2l0ZXJhdG9yMi5lKGVycik7CiAgICAgIH0gZmluYWxseSB7CiAgICAgICAgX2l0ZXJhdG9yMi5mKCk7CiAgICAgIH0KICAgICAgcmV0dXJuIGNsYXNzQXJyOwogICAgfQogIH0KfTsKLyoqDQogKiDpgJrov4flkI3np7Dojrflj5blr7nlupTnmoRWdWXlr7nosaHvvIhWdWXlr7nosaHjgIHmlrnms5XvvIkNCiAqLwpleHBvcnQgZnVuY3Rpb24gZ2V0TW9kdWxlVnVlQnlOYW1lKG5hbWUpIHsKICB2YXIgbW9kdWxlVnVlID0gJyc7CiAgaWYgKG5hbWUuc3RhcnRzV2l0aCgnZXhhbV8nKSkgewogICAgLy8gbW9kdWxlVnVlID0gZXhhbV9pbmRDb25maWcgLy8g5ZCE5qih5Z2X5a+55bqU55qE5a+56LGhIGV4YW1faW5kQ29uZmlnCiAgfSBlbHNlIGlmIChuYW1lLnN0YXJ0c1dpdGgoJ3pucGJfJykpIHsKICAgIG1vZHVsZVZ1ZSA9IHpucGJfaW5kQ29uZmlnOwogIH0gLyogZWxzZSBpZiAobmFtZS5zdGFydHNXaXRoKCdpbmRjYXRvcl8nKSkgew0KICAgIG1vZHVsZVZ1ZSA9IGluZGNhdG9yX2luZENvbmZpZw0KICAgIH0gZWxzZSBpZiAobmFtZS5zdGFydHNXaXRoKCdyZWdpc3Rlcl8nKSkgew0KICAgIG1vZHVsZVZ1ZSA9IHJlZ2lzdGVyX2luZENvbmZpZw0KICAgIH0gICovCiAgcmV0dXJuIG1vZHVsZVZ1ZTsKfQoKLy8g5qC85byP5YyW5LiL5ouJ5qCR5pWw5o2uCmV4cG9ydCB2YXIgc2VsZWN0RGF0YSA9IGZ1bmN0aW9uIHNlbGVjdERhdGEoKSB7CiAgLy8g5pWw5o2u5rqQ57G75Z6L5Li66Ieq5a6a5LmJ5pe277yM5oyJ5a6e6ZmF5qih5Z2X6ZyA6KaB6YWN572uYWxsTW9kdWxl77yM5qih5Z2X5ZCNX2luZENvbmZpZyznlKjkuo7mnoTpgKDmlrnms5XpgInmi6nmoJHjgIIKICAvLyDlkIzml7blnKhleHRlcm5hbERhdGEuaHRtbCDkuK3lvJXlhaXlr7nlupRqc+aWh+S7tu+8jOeUqOS6juiwg+eUqOaMh+WumuaWueazleiOt+WPluaVsOaNrua6kOOAggogIHZhciBhbGxNb2R1bGUgPSAnem5wYl9pbmRDb25maWcnOwogIC8vICdzeXN0ZW1faW5kQ29uZmlnLGV4YW1faW5kQ29uZmlnLHRyYWluX2luZENvbmZpZyxpbmRjYXRvcl9pbmRDb25maWcscmVnaXN0ZXJfaW5kQ29uZmlnJwogIHZhciBhbGxNb2R1bGVBcnIgPSBhbGxNb2R1bGUuc3BsaXQoJywnKTsKICB2YXIgdGVtcERhdGEgPSBbXTsKICB2YXIgb2JqID0ge307CiAgYWxsTW9kdWxlQXJyLmZvckVhY2goZnVuY3Rpb24gKGl0ZW0pIHsKICAgIHZhciBtb2R1bGUgPSBnZXRNb2R1bGVWdWVCeU5hbWUoaXRlbSk7CiAgICBpZiAobW9kdWxlLm1vZHVsZU1ldGhvZC5pbmRleE9mKCctJykgIT09IC0xKSB7CiAgICAgIC8vIOaooeWdl+iKgueCuQogICAgICBvYmogPSB7CiAgICAgICAgaWQ6IG1vZHVsZS5tb2R1bGVOYW1lLnNwbGl0KCctJylbMF0udHJpbSgpLAogICAgICAgIHBpZDogbW9kdWxlLm1vZHVsZU5hbWUuc3BsaXQoJy0nKVswXS50cmltKCksCiAgICAgICAgdmFsdWU6IG1vZHVsZS5tb2R1bGVOYW1lLnNwbGl0KCctJylbMF0udHJpbSgpLAogICAgICAgIGxhYmVsOiBtb2R1bGUubW9kdWxlTmFtZS5zcGxpdCgnLScpWzBdICsgJy0nICsgbW9kdWxlLm1vZHVsZU5hbWUuc3BsaXQoJy0nKVsxXQogICAgICB9OwogICAgICB0ZW1wRGF0YS5wdXNoKG9iaik7CiAgICAgIHZhciBtZXRob2QgPSBtb2R1bGUubW9kdWxlTWV0aG9kLnNwbGl0KCcsJyk7CiAgICAgIC8vIOaWueazleiKgueCuQogICAgICBtZXRob2QuZm9yRWFjaChmdW5jdGlvbiAoa2V5KSB7CiAgICAgICAgb2JqID0gewogICAgICAgICAgaWQ6IGtleS5zcGxpdCgnLScpWzBdLnRyaW0oKSwKICAgICAgICAgIHBpZDogbW9kdWxlLm1vZHVsZU5hbWUuc3BsaXQoJy0nKVswXS50cmltKCksCiAgICAgICAgICB2YWx1ZToga2V5LnNwbGl0KCctJylbMF0udHJpbSgpLAogICAgICAgICAgbGFiZWw6IGtleS5zcGxpdCgnLScpWzBdICsgJy0nICsga2V5LnNwbGl0KCctJylbMV0udHJpbSgpCiAgICAgICAgfTsKICAgICAgICB0ZW1wRGF0YS5wdXNoKG9iaik7CiAgICAgIH0pOwogICAgfQogIH0pOwogIHJldHVybiBidWlsZFRyZWUodGVtcERhdGEsICdpZCcsICdwaWQnKTsKfTsKCi8qKg0KICogQHBhcmFtIHthcnJheX0gYXJyYXkg6ZyA6KaB6L2s5YyW5qCR55qE5pWw57uEDQogKiBAcGFyYW0ge3N0cmluZ30gaWQg5L2c5Li6aWTnmoTmoIfor4YNCiAqIEBwYXJhbSB7c3RyaW5nfSBwYXJlbnRfaWQgICDkvZzkuLpwaWTnmoTmoIfor4YNCiAqIHJldHVybiB7YXJyYXl9ICDov5Tlm57moJHnmoTmlbDnu4QNCiAqLwp2YXIgYnVpbGRUcmVlID0gZnVuY3Rpb24gYnVpbGRUcmVlKGFycmF5LCBpZCwgcGFyZW50X2lkKSB7CiAgLy8g5Yib5bu65Li05pe25a+56LGhCiAgdmFyIHRlbXAgPSB7fTsKICAvLyDliJvlu7rpnIDopoHov5Tlm57nmoTmoJHlvaLlr7nosaEKICB2YXIgdHJlZSA9IFtdOwogIC8vIOWFiOmBjeWOhuaVsOe7hO+8jOWwhuaVsOe7hOeahOavj+S4gOmhuea3u+WKoOWIsHRlbXDlr7nosaHkuK0KICBmb3IgKHZhciBrIGluIGFycmF5KSB7CiAgICB0ZW1wW2FycmF5W2tdW2lkXV0gPSBhcnJheVtrXTsKICB9CiAgLy8g6YGN5Y6GdGVtcOWvueixoe+8jOWwhuW9k+WJjeWtkOiKgueCueS4jueItuiKgueCueW7uueri+i/nuaOpQogIGZvciAodmFyIGkgaW4gdGVtcCkgewogICAgLy8g5Yik5pat5piv5ZCm5piv5qC56IqC54K55LiL55qE6aG5CiAgICBpZiAodGVtcFtpXVtwYXJlbnRfaWRdICE9PSB0ZW1wW2ldW2lkXSkgewogICAgICAvLyBpZCE9PXBpZCAg5LiN5piv54i26IqC54K5CiAgICAgIC8vIOmYsuatouiHquW3seeCueayoeacieeItuiKgueCuSAg5paw5aKe5oql6ZSZ5a+86Ie05qCR5raI5aSxCiAgICAgIGlmICh0ZW1wW3RlbXBbaV1bcGFyZW50X2lkXV0pIHsKICAgICAgICBpZiAoIXRlbXBbdGVtcFtpXVtwYXJlbnRfaWRdXS5jaGlsZHJlbikgewogICAgICAgICAgdGVtcFt0ZW1wW2ldW3BhcmVudF9pZF1dLmNoaWxkcmVuID0gW107CiAgICAgICAgfQogICAgICAgIHRlbXBbdGVtcFtpXVtwYXJlbnRfaWRdXS5jaGlsZHJlbi5wdXNoKHRlbXBbaV0pOwogICAgICB9CiAgICB9IGVsc2UgewogICAgICAvLyBpZD1waWQg5piv5qC56IqC54K5CiAgICAgIHRyZWUucHVzaCh0ZW1wW2ldKTsKICAgIH0KICB9CiAgcmV0dXJuIHRyZWU7Cn07"}, {"version": 3, "names": ["store", "znpb_indConfig", "moduleName", "moduleMethod", "methods", "znpb_ISWB0001", "empList", "getters", "ismEmpList", "znpb_ISWB0002", "dateArr", "ismDateList", "reg", "dateArr2", "map", "item", "replace", "day1", "dateTime", "Date", "setDate", "getDate", "getFullYear", "getMonth", "znpb_ISWB0003", "organObj", "ismOrganList", "organArr", "selectedOrganList", "organSign", "organList", "find", "i", "id", "organArr2", "name", "znpb_ISWB0004", "ismPostList", "znpb_ISWB0005", "classObj", "ismClassList", "classArr", "selectedClassList", "classSign", "classList", "value", "getModuleVueByName", "moduleVue", "startsWith", "selectData", "allModule", "allModuleArr", "split", "tempData", "obj", "for<PERSON>ach", "module", "indexOf", "trim", "pid", "label", "push", "method", "key", "buildTree", "array", "parent_id", "temp", "tree", "k", "children"], "sources": ["E:/1_Project/XYD_Project/dop-4.0/dop-4.1-qian<PERSON>n/数字运营平台-统一门户工程/dopUnify/src/utils/externalData.js"], "sourcesContent": ["// 各模块的外表数据源\r\nimport store from '@/store'\r\n\r\n// 智能排班  ************moduleName必须是a-b的形式,moduleMethod的方法名要和methods中的方法保持一致\r\nexport const znpb_indConfig = {\r\n  moduleName: 'znpb-智能排班', // 所代表模块\r\n  moduleMethod:\r\n    'znpb_ISWB0001-获取排班人员,znpb_ISWB0002-获取排班日期,znpb_ISWB0005-获取排班班次,znpb_ISWB0003-获取排班机构,znpb_ISWB0004-获取排班岗位', // 按钮方法及名称，定义于methods中并一一对应，方法名以模块_开头 + 功能格式命名\r\n  methods: { // 自定义方法-获取排班人员\r\n    znpb_ISWB0001: () => {\r\n      const empList = store.getters.ismEmpList // 排班人员\r\n      return empList\r\n    },\r\n    // 自定义方法-获取排班日期\r\n    znpb_ISWB0002: () => {\r\n      const dateArr = store.getters.ismDateList // 排班日期\r\n      const reg = /^(\\d{4})(\\d{2})(\\d{2})$/ // 日期格式化正则\r\n      const dateArr2 = dateArr.map(item => {\r\n        item = item.replace(reg, '$1-$2-$3')\r\n        return item\r\n      })\r\n      const day1 = dateArr2[0] // 起始日期\r\n      // 将起始日期往前推一天 begin\r\n      let dateTime = new Date(day1)\r\n      dateTime = dateTime.setDate(dateTime.getDate())\r\n      dateTime = new Date(dateTime)\r\n      dateTime = dateTime.getFullYear() + '-' + (dateTime.getMonth() + 1) + '-' + dateTime.getDate()\r\n      dateArr2[0] = dateTime\r\n      // 将起始日期往前推一天 end\r\n      return dateArr2\r\n    },\r\n    // 自定义方法-获取排班机构\r\n    znpb_ISWB0003: () => {\r\n      const organObj = store.getters.ismOrganList // 排班机构\r\n      let organArr = [] // 用户选择的排班机构\r\n      for (const item of organObj.selectedOrganList) { // 遍历用户选择的排班班次\r\n        const organSign = organObj.organList.find(i => { return i.id === item }) // organSign为单个班次\r\n        organArr = [...organArr, organSign]\r\n      }\r\n      const organArr2 = organArr.map(item => {\r\n        item['label'] = item.name\r\n        item['value'] = item.id\r\n        return item\r\n      })\r\n      return organArr2\r\n    },\r\n    // 自定义方法-获取排班岗位\r\n    znpb_ISWB0004: () => {\r\n      const organObj = store.getters.ismPostList // 排班机构\r\n      return organObj\r\n    },\r\n    // 自定义方法-获取排班班次\r\n    znpb_ISWB0005: () => {\r\n      const classObj = store.getters.ismClassList // 排班班次\r\n      let classArr = [] // 用户选择的排班班次\r\n      for (const item of classObj.selectedClassList) { // 遍历用户选择的排班班次\r\n        const classSign = classObj.classList.find(i => { return i.value === item }) // classSign为单个班次\r\n        classArr = [...classArr, classSign]\r\n      }\r\n      return classArr\r\n    }\r\n  }\r\n}\r\n/**\r\n * 通过名称获取对应的Vue对象（Vue对象、方法）\r\n */\r\nexport function getModuleVueByName(name) {\r\n  let moduleVue = ''\r\n  if (name.startsWith('exam_')) {\r\n    // moduleVue = exam_indConfig // 各模块对应的对象 exam_indConfig\r\n  } else if (name.startsWith('znpb_')) {\r\n    moduleVue = znpb_indConfig\r\n  } /* else if (name.startsWith('indcator_')) {\r\n    moduleVue = indcator_indConfig\r\n  } else if (name.startsWith('register_')) {\r\n    moduleVue = register_indConfig\r\n  }  */\r\n  return moduleVue\r\n}\r\n\r\n// 格式化下拉树数据\r\nexport const selectData = () => {\r\n  // 数据源类型为自定义时，按实际模块需要配置allModule，模块名_indConfig,用于构造方法选择树。\r\n  // 同时在externalData.html 中引入对应js文件，用于调用指定方法获取数据源。\r\n  const allModule = 'znpb_indConfig'\r\n  // 'system_indConfig,exam_indConfig,train_indConfig,indcator_indConfig,register_indConfig'\r\n  const allModuleArr = allModule.split(',')\r\n  const tempData = []\r\n  let obj = {}\r\n  allModuleArr.forEach((item) => {\r\n    const module = getModuleVueByName(item)\r\n    if (module.moduleMethod.indexOf('-') !== -1) {\r\n      // 模块节点\r\n      obj = {\r\n        id: module.moduleName.split('-')[0].trim(),\r\n        pid: module.moduleName.split('-')[0].trim(),\r\n        value: module.moduleName.split('-')[0].trim(),\r\n        label:\r\n          module.moduleName.split('-')[0] +\r\n          '-' +\r\n          module.moduleName.split('-')[1]\r\n      }\r\n      tempData.push(obj)\r\n      const method = module.moduleMethod.split(',')\r\n      // 方法节点\r\n      method.forEach((key) => {\r\n        obj = {\r\n          id: key.split('-')[0].trim(),\r\n          pid: module.moduleName.split('-')[0].trim(),\r\n          value: key.split('-')[0].trim(),\r\n          label: key.split('-')[0] + '-' + key.split('-')[1].trim()\r\n        }\r\n        tempData.push(obj)\r\n      })\r\n    }\r\n  })\r\n  return buildTree(tempData, 'id', 'pid')\r\n}\r\n\r\n/**\r\n * @param {array} array 需要转化树的数组\r\n * @param {string} id 作为id的标识\r\n * @param {string} parent_id   作为pid的标识\r\n * return {array}  返回树的数组\r\n */\r\nconst buildTree = (array, id, parent_id) => {\r\n  // 创建临时对象\r\n  const temp = {}\r\n  // 创建需要返回的树形对象\r\n  const tree = []\r\n  // 先遍历数组，将数组的每一项添加到temp对象中\r\n  for (const k in array) {\r\n    temp[array[k][id]] = array[k]\r\n  }\r\n  // 遍历temp对象，将当前子节点与父节点建立连接\r\n  for (const i in temp) {\r\n    // 判断是否是根节点下的项\r\n    if (temp[i][parent_id] !== temp[i][id]) {\r\n      // id!==pid  不是父节点\r\n      // 防止自己点没有父节点  新增报错导致树消失\r\n      if (temp[temp[i][parent_id]]) {\r\n        if (!temp[temp[i][parent_id]].children) {\r\n          temp[temp[i][parent_id]].children = []\r\n        }\r\n        temp[temp[i][parent_id]].children.push(temp[i])\r\n      }\r\n    } else {\r\n      // id=pid 是根节点\r\n      tree.push(temp[i])\r\n    }\r\n  }\r\n  return tree\r\n}\r\n"], "mappings": ";;;;;;;;;;;;AAAA;AACA,OAAOA,KAAK,MAAM,SAAS;;AAE3B;AACA,OAAO,IAAMC,cAAc,GAAG;EAC5BC,UAAU,EAAE,WAAW;EAAE;EACzBC,YAAY,EACV,0GAA0G;EAAE;EAC9GC,OAAO,EAAE;IAAE;IACTC,aAAa,EAAE,yBAAM;MACnB,IAAMC,OAAO,GAAGN,KAAK,CAACO,OAAO,CAACC,UAAU,EAAC;MACzC,OAAOF,OAAO;IAChB,CAAC;IACD;IACAG,aAAa,EAAE,yBAAM;MACnB,IAAMC,OAAO,GAAGV,KAAK,CAACO,OAAO,CAACI,WAAW,EAAC;MAC1C,IAAMC,GAAG,GAAG,yBAAyB,EAAC;MACtC,IAAMC,QAAQ,GAAGH,OAAO,CAACI,GAAG,CAAC,UAAAC,IAAI,EAAI;QACnCA,IAAI,GAAGA,IAAI,CAACC,OAAO,CAACJ,GAAG,EAAE,UAAU,CAAC;QACpC,OAAOG,IAAI;MACb,CAAC,CAAC;MACF,IAAME,IAAI,GAAGJ,QAAQ,CAAC,CAAC,CAAC,EAAC;MACzB;MACA,IAAIK,QAAQ,GAAG,IAAIC,IAAI,CAACF,IAAI,CAAC;MAC7BC,QAAQ,GAAGA,QAAQ,CAACE,OAAO,CAACF,QAAQ,CAACG,OAAO,EAAE,CAAC;MAC/CH,QAAQ,GAAG,IAAIC,IAAI,CAACD,QAAQ,CAAC;MAC7BA,QAAQ,GAAGA,QAAQ,CAACI,WAAW,EAAE,GAAG,GAAG,IAAIJ,QAAQ,CAACK,QAAQ,EAAE,GAAG,CAAC,CAAC,GAAG,GAAG,GAAGL,QAAQ,CAACG,OAAO,EAAE;MAC9FR,QAAQ,CAAC,CAAC,CAAC,GAAGK,QAAQ;MACtB;MACA,OAAOL,QAAQ;IACjB,CAAC;IACD;IACAW,aAAa,EAAE,yBAAM;MACnB,IAAMC,QAAQ,GAAGzB,KAAK,CAACO,OAAO,CAACmB,YAAY,EAAC;MAC5C,IAAIC,QAAQ,GAAG,EAAE,EAAC;MAAA,2CACCF,QAAQ,CAACG,iBAAiB;QAAA;MAAA;QAAA;UAAA,IAAlCb,IAAI;UAAkC;UAC/C,IAAMc,SAAS,GAAGJ,QAAQ,CAACK,SAAS,CAACC,IAAI,CAAC,UAAAC,CAAC,EAAI;YAAE,OAAOA,CAAC,CAACC,EAAE,KAAKlB,IAAI;UAAC,CAAC,CAAC,EAAC;UACzEY,QAAQ,gCAAOA,QAAQ,IAAEE,SAAS,EAAC;QAAA;QAFrC,oDAA+C;UAAA;QAG/C;MAAC;QAAA;MAAA;QAAA;MAAA;MACD,IAAMK,SAAS,GAAGP,QAAQ,CAACb,GAAG,CAAC,UAAAC,IAAI,EAAI;QACrCA,IAAI,CAAC,OAAO,CAAC,GAAGA,IAAI,CAACoB,IAAI;QACzBpB,IAAI,CAAC,OAAO,CAAC,GAAGA,IAAI,CAACkB,EAAE;QACvB,OAAOlB,IAAI;MACb,CAAC,CAAC;MACF,OAAOmB,SAAS;IAClB,CAAC;IACD;IACAE,aAAa,EAAE,yBAAM;MACnB,IAAMX,QAAQ,GAAGzB,KAAK,CAACO,OAAO,CAAC8B,WAAW,EAAC;MAC3C,OAAOZ,QAAQ;IACjB,CAAC;IACD;IACAa,aAAa,EAAE,yBAAM;MACnB,IAAMC,QAAQ,GAAGvC,KAAK,CAACO,OAAO,CAACiC,YAAY,EAAC;MAC5C,IAAIC,QAAQ,GAAG,EAAE,EAAC;MAAA,4CACCF,QAAQ,CAACG,iBAAiB;QAAA;MAAA;QAAA;UAAA,IAAlC3B,IAAI;UAAkC;UAC/C,IAAM4B,SAAS,GAAGJ,QAAQ,CAACK,SAAS,CAACb,IAAI,CAAC,UAAAC,CAAC,EAAI;YAAE,OAAOA,CAAC,CAACa,KAAK,KAAK9B,IAAI;UAAC,CAAC,CAAC,EAAC;UAC5E0B,QAAQ,gCAAOA,QAAQ,IAAEE,SAAS,EAAC;QAAA;QAFrC,uDAA+C;UAAA;QAG/C;MAAC;QAAA;MAAA;QAAA;MAAA;MACD,OAAOF,QAAQ;IACjB;EACF;AACF,CAAC;AACD;AACA;AACA;AACA,OAAO,SAASK,kBAAkB,CAACX,IAAI,EAAE;EACvC,IAAIY,SAAS,GAAG,EAAE;EAClB,IAAIZ,IAAI,CAACa,UAAU,CAAC,OAAO,CAAC,EAAE;IAC5B;EAAA,CACD,MAAM,IAAIb,IAAI,CAACa,UAAU,CAAC,OAAO,CAAC,EAAE;IACnCD,SAAS,GAAG9C,cAAc;EAC5B,CAAC,CAAC;AACJ;AACA;AACA;AACA;EACE,OAAO8C,SAAS;AAClB;;AAEA;AACA,OAAO,IAAME,UAAU,GAAG,SAAbA,UAAU,GAAS;EAC9B;EACA;EACA,IAAMC,SAAS,GAAG,gBAAgB;EAClC;EACA,IAAMC,YAAY,GAAGD,SAAS,CAACE,KAAK,CAAC,GAAG,CAAC;EACzC,IAAMC,QAAQ,GAAG,EAAE;EACnB,IAAIC,GAAG,GAAG,CAAC,CAAC;EACZH,YAAY,CAACI,OAAO,CAAC,UAACxC,IAAI,EAAK;IAC7B,IAAMyC,MAAM,GAAGV,kBAAkB,CAAC/B,IAAI,CAAC;IACvC,IAAIyC,MAAM,CAACrD,YAAY,CAACsD,OAAO,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC,EAAE;MAC3C;MACAH,GAAG,GAAG;QACJrB,EAAE,EAAEuB,MAAM,CAACtD,UAAU,CAACkD,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAACM,IAAI,EAAE;QAC1CC,GAAG,EAAEH,MAAM,CAACtD,UAAU,CAACkD,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAACM,IAAI,EAAE;QAC3Cb,KAAK,EAAEW,MAAM,CAACtD,UAAU,CAACkD,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAACM,IAAI,EAAE;QAC7CE,KAAK,EACHJ,MAAM,CAACtD,UAAU,CAACkD,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,GAC/B,GAAG,GACHI,MAAM,CAACtD,UAAU,CAACkD,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC;MAClC,CAAC;MACDC,QAAQ,CAACQ,IAAI,CAACP,GAAG,CAAC;MAClB,IAAMQ,MAAM,GAAGN,MAAM,CAACrD,YAAY,CAACiD,KAAK,CAAC,GAAG,CAAC;MAC7C;MACAU,MAAM,CAACP,OAAO,CAAC,UAACQ,GAAG,EAAK;QACtBT,GAAG,GAAG;UACJrB,EAAE,EAAE8B,GAAG,CAACX,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAACM,IAAI,EAAE;UAC5BC,GAAG,EAAEH,MAAM,CAACtD,UAAU,CAACkD,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAACM,IAAI,EAAE;UAC3Cb,KAAK,EAAEkB,GAAG,CAACX,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAACM,IAAI,EAAE;UAC/BE,KAAK,EAAEG,GAAG,CAACX,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,GAAG,GAAG,GAAGW,GAAG,CAACX,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAACM,IAAI;QACzD,CAAC;QACDL,QAAQ,CAACQ,IAAI,CAACP,GAAG,CAAC;MACpB,CAAC,CAAC;IACJ;EACF,CAAC,CAAC;EACF,OAAOU,SAAS,CAACX,QAAQ,EAAE,IAAI,EAAE,KAAK,CAAC;AACzC,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA;AACA,IAAMW,SAAS,GAAG,SAAZA,SAAS,CAAIC,KAAK,EAAEhC,EAAE,EAAEiC,SAAS,EAAK;EAC1C;EACA,IAAMC,IAAI,GAAG,CAAC,CAAC;EACf;EACA,IAAMC,IAAI,GAAG,EAAE;EACf;EACA,KAAK,IAAMC,CAAC,IAAIJ,KAAK,EAAE;IACrBE,IAAI,CAACF,KAAK,CAACI,CAAC,CAAC,CAACpC,EAAE,CAAC,CAAC,GAAGgC,KAAK,CAACI,CAAC,CAAC;EAC/B;EACA;EACA,KAAK,IAAMrC,CAAC,IAAImC,IAAI,EAAE;IACpB;IACA,IAAIA,IAAI,CAACnC,CAAC,CAAC,CAACkC,SAAS,CAAC,KAAKC,IAAI,CAACnC,CAAC,CAAC,CAACC,EAAE,CAAC,EAAE;MACtC;MACA;MACA,IAAIkC,IAAI,CAACA,IAAI,CAACnC,CAAC,CAAC,CAACkC,SAAS,CAAC,CAAC,EAAE;QAC5B,IAAI,CAACC,IAAI,CAACA,IAAI,CAACnC,CAAC,CAAC,CAACkC,SAAS,CAAC,CAAC,CAACI,QAAQ,EAAE;UACtCH,IAAI,CAACA,IAAI,CAACnC,CAAC,CAAC,CAACkC,SAAS,CAAC,CAAC,CAACI,QAAQ,GAAG,EAAE;QACxC;QACAH,IAAI,CAACA,IAAI,CAACnC,CAAC,CAAC,CAACkC,SAAS,CAAC,CAAC,CAACI,QAAQ,CAACT,IAAI,CAACM,IAAI,CAACnC,CAAC,CAAC,CAAC;MACjD;IACF,CAAC,MAAM;MACL;MACAoC,IAAI,CAACP,IAAI,CAACM,IAAI,CAACnC,CAAC,CAAC,CAAC;IACpB;EACF;EACA,OAAOoC,IAAI;AACb,CAAC"}]}