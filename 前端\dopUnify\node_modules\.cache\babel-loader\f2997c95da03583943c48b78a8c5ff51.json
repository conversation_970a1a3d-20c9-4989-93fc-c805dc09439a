{"remainingRequest": "E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-q<PERSON><PERSON>n\\数字运营平台-统一门户工程\\dopUnify\\node_modules\\babel-loader\\lib\\index.js!E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan\\数字运营平台-统一门户工程\\dopUnify\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan\\数字运营平台-统一门户工程\\dopUnify\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan\\数字运营平台-统一门户工程\\dopUnify\\src\\components\\Charts\\Business.vue?vue&type=script&lang=js&", "dependencies": [{"path": "E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-<PERSON><PERSON><PERSON>n\\数字运营平台-统一门户工程\\dopUnify\\src\\components\\Charts\\Business.vue", "mtime": 1705285078511}, {"path": "E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-<PERSON><PERSON><PERSON>n\\数字运营平台-统一门户工程\\dopUnify\\babel.config.js", "mtime": 1686019818359}, {"path": "E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-<PERSON><PERSON><PERSON>n\\数字运营平台-统一门户工程\\dopUnify\\.babelrc", "mtime": 1705285078668}, {"path": "E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-<PERSON><PERSON><PERSON>n\\数字运营平台-统一门户工程\\dopUnify\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-<PERSON><PERSON><PERSON>n\\数字运营平台-统一门户工程\\dopUnify\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-<PERSON><PERSON><PERSON>n\\数字运营平台-统一门户工程\\dopUnify\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-<PERSON><PERSON><PERSON>n\\数字运营平台-统一门户工程\\dopUnify\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1655715099000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, null]}