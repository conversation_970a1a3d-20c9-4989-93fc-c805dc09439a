{"remainingRequest": "E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-q<PERSON><PERSON>n\\数字运营平台-统一门户工程\\dopUnify\\node_modules\\babel-loader\\lib\\index.js!E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan\\数字运营平台-统一门户工程\\dopUnify\\node_modules\\@babel\\runtime\\helpers\\esm\\possibleConstructorReturn.js", "dependencies": [{"path": "E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-<PERSON><PERSON><PERSON>n\\数字运营平台-统一门户工程\\dopUnify\\node_modules\\@babel\\runtime\\helpers\\esm\\possibleConstructorReturn.js", "mtime": 456789000000}, {"path": "E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-<PERSON><PERSON><PERSON>n\\数字运营平台-统一门户工程\\dopUnify\\babel.config.js", "mtime": 1686019818359}, {"path": "E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-<PERSON><PERSON><PERSON>n\\数字运营平台-统一门户工程\\dopUnify\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-<PERSON><PERSON><PERSON>n\\数字运营平台-统一门户工程\\dopUnify\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:aW1wb3J0IF90eXBlb2YgZnJvbSAiLi90eXBlb2YuanMiOwppbXBvcnQgYXNzZXJ0VGhpc0luaXRpYWxpemVkIGZyb20gIi4vYXNzZXJ0VGhpc0luaXRpYWxpemVkLmpzIjsKZXhwb3J0IGRlZmF1bHQgZnVuY3Rpb24gX3Bvc3NpYmxlQ29uc3RydWN0b3JSZXR1cm4oc2VsZiwgY2FsbCkgewogIGlmIChjYWxsICYmIChfdHlwZW9mKGNhbGwpID09PSAib2JqZWN0IiB8fCB0eXBlb2YgY2FsbCA9PT0gImZ1bmN0aW9uIikpIHsKICAgIHJldHVybiBjYWxsOwogIH0gZWxzZSBpZiAoY2FsbCAhPT0gdm9pZCAwKSB7CiAgICB0aHJvdyBuZXcgVHlwZUVycm9yKCJEZXJpdmVkIGNvbnN0cnVjdG9ycyBtYXkgb25seSByZXR1cm4gb2JqZWN0IG9yIHVuZGVmaW5lZCIpOwogIH0KICByZXR1cm4gYXNzZXJ0VGhpc0luaXRpYWxpemVkKHNlbGYpOwp9"}, {"version": 3, "names": ["_typeof", "assertThisInitialized", "_possibleConstructorReturn", "self", "call", "TypeError"], "sources": ["E:/1_Project/XYD_Project/dop-4.0/dop-4.1-qianduan/数字运营平台-统一门户工程/dopUnify/node_modules/@babel/runtime/helpers/esm/possibleConstructorReturn.js"], "sourcesContent": ["import _typeof from \"./typeof.js\";\nimport assertThisInitialized from \"./assertThisInitialized.js\";\nexport default function _possibleConstructorReturn(self, call) {\n  if (call && (_typeof(call) === \"object\" || typeof call === \"function\")) {\n    return call;\n  } else if (call !== void 0) {\n    throw new TypeError(\"Derived constructors may only return object or undefined\");\n  }\n  return assertThisInitialized(self);\n}"], "mappings": "AAAA,OAAOA,OAAO,MAAM,aAAa;AACjC,OAAOC,qBAAqB,MAAM,4BAA4B;AAC9D,eAAe,SAASC,0BAA0B,CAACC,IAAI,EAAEC,IAAI,EAAE;EAC7D,IAAIA,IAAI,KAAKJ,OAAO,CAACI,IAAI,CAAC,KAAK,QAAQ,IAAI,OAAOA,IAAI,KAAK,UAAU,CAAC,EAAE;IACtE,OAAOA,IAAI;EACb,CAAC,MAAM,IAAIA,IAAI,KAAK,KAAK,CAAC,EAAE;IAC1B,MAAM,IAAIC,SAAS,CAAC,0DAA0D,CAAC;EACjF;EACA,OAAOJ,qBAAqB,CAACE,IAAI,CAAC;AACpC"}]}