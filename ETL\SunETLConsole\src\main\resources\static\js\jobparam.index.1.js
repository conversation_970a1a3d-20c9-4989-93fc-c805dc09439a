$(function() {
	// init date tables
	var jobTable = $("#job_list").dataTable({
		"deferRender": true,
		"processing" : true,
	    "serverSide": true,
		"ajax": {
			url: base_url + "/jobparam/pageList",
			type:"post",
	        data : function ( d ) {
	        	var obj = {};
	        	obj.jobId = $('#jobId').val();
	        	obj.param_field = $('#param_field').val();
	        	obj.start = d.start;
	        	obj.length = d.length;
                return obj;
            }
	    },
	    "searching": false,
	    "ordering": false,
	    //"scrollX": true,	// X轴滚动条，取消自适应
	    "columns": [
	                { "data": 'id', "bSortable": false, "visible" : false},
	                { "data": 'jobId', "visible" : true},
	                { "data": 'param_field', "visible" : true},
					{ "data": 'param_value', "visible" : true},
			        { "data": 'param_field_ch', "visible" : true},
					{
						"data": '操作' ,
	                	"render": function ( data, type, row ) {
	                		return function(){
	                			var codeBtn = "";
								// html
                                tableData['key'+row.id] = row;
								var html = '<p id="'+ row.id +'" >'+
									'<button class="btn btn-warning btn-xs update" type="button">编辑</button>  '+
									codeBtn +
									'<button class="btn btn-danger btn-xs job_operate" _type="job_del" type="button">删除</button>  '+
									'</p>';

	                			return html;
							};
	                	}
	                }
	            ],
		"language" : {
			"sProcessing" : "处理中...",
			"sLengthMenu" : "每页 _MENU_ 条记录",
			"sZeroRecords" : "没有匹配结果",
			"sInfo" : "第 _PAGE_ 页 ( 总共 _PAGES_ 页，_TOTAL_ 条记录 )",
			"sInfoEmpty" : "无记录",
			"sInfoFiltered" : "(由 _MAX_ 项结果过滤)",
			"sInfoPostFix" : "",
			"sSearch" : "搜索:",
			"sUrl" : "",
			"sEmptyTable" : "表中数据为空",
			"sLoadingRecords" : "载入中...",
			"sInfoThousands" : ",",
			"oPaginate" : {
				"sFirst" : "首页",
				"sPrevious" : "上页",
				"sNext" : "下页",
				"sLast" : "末页"
			},
			"oAria" : {
				"sSortAscending" : ": 以升序排列此列",
				"sSortDescending" : ": 以降序排列此列"
			}
		}
	});

    // table data
    var tableData = {};

	// 搜索按钮
	$('#searchBtn').on('click', function(){
		jobTable.fnDraw();
	});


    // 搜索按钮
    var selected_num;
    $('#addJobId').on('change', function(){
        selected_num=$(this).find("option:selected").val();
        $(".add_list").text("");
        add_custom_num = 0;
		$.ajax({
			type : 'POST',
			url : base_url+"/jobinfo/loadById",
			data : {
				"id" : selected_num
			},
			async: false,
			dataType : "json",
			success : function(data){
				var executorHandler = data.jobInfo.executorHandler;
				if(executorHandler=="DataImpService"||executorHandler=="SQLLoadService"){
                    var warp = document.createDocumentFragment();
                    for(var x=0;x < data.mcTableTbs.length ; x++){
                       			 warp+="<option value="+ data.mcTableTbs[x].id+">"+data.mcTableTbs[x].tableDesc+"</option>";
                    }


					$(".add_list").append("<div class='form-group'><div class='col-sm-4'><input type='text' class='form-control' name='param_field_ch'  value='源数据表' >" +
							"</div><input type='hidden' class='form-control' name='param_field'    value= 'SOURCE_TABLE_ID' >" +
							"<div class='col-sm-8'><select class='form-control' name='param_value'     >"+warp+"</select></div>" +
							"<div class='col-sm-4'><input type='text' class='form-control' name='param_field_ch'  value='文本路径' ></div>" +
							"<input type='hidden' class='form-control' name='param_field'    value= 'LOAD_FILE_PATH' >" +
							"<div class='col-sm-8'><input type='text' class='form-control' name='param_value'     ></div>" +
							"<div class='col-sm-4'><input type='text' class='form-control' name='param_field_ch'  value='源文件名称' ></div>" +
							"<input type='hidden' class='form-control' name='param_field'    value= 'LOAD_FILE_NAME' >" +
	                        "<div class='col-sm-8'><input type='text' class='form-control' name='param_value'    ></div>" +
	                        "<div class='col-sm-4'><input type='text' class='form-control' name='param_field_ch'  value='导数文件名称' ></div>" +
	                        "<input type='hidden' class='form-control' name='param_field'    value= 'LOAD_DATA_FILE_NAME' >" +
	                        "<div class='col-sm-8'><input type='text' class='form-control' name='param_value'    ></div>" +
	                        "<div class='col-sm-4'><input type='text' class='form-control' name='param_field_ch'  value='标志文件名称' ></div>" +
	                        "<input type='hidden' class='form-control' name='param_field'    value= 'LOAD_FLAG_FILE_NAME' >" +
	                        "<div class='col-sm-8'><input type='text' class='form-control' name='param_value'    ></div>" +
	                        "<div class='col-sm-4'><input type='text' class='form-control' name='param_field_ch'  value='是否需要解压' ></div>" +
	                        "<input type='hidden' class='form-control' name='param_field'    value= 'IS_UNZIP' >" +
	                        "<div class='col-sm-8'><select class='form-control' name='param_value'     ><option value='0'>不需要解压</option><option value='1'>需要解压</option></select></div>" +
	                        "<div class='col-sm-4'><input type='text' class='form-control' name='param_field_ch'  value='分隔符' ></div>" +
	                        "<input type='hidden' class='form-control' name='param_field'    value= 'SPLIT_MARK' >" +
	                        "<div class='col-sm-8'><input type='text' class='form-control' name='param_value'    ></div>" +
	                        "<div class='col-sm-4'><input type='text' class='form-control' name='param_field_ch'  value='起始行' ></div>" +
	                        "<input type='hidden' class='form-control' name='param_field'    value= 'START_LINE' >" +
	                        "<div class='col-sm-8'><input type='text' class='form-control' name='param_value'     ></div>" +
							"</div>");
				}else if(executorHandler == "EndTask" || executorHandler == "StartTask"){
					alert("该任务类型不需要配置参数");
				}else if(executorHandler == "ftpDownload"){
					$(".add_list").append("<div class='form-group'>" +
							
							

							"<div class='col-sm-4'><input type='text' class='form-control' name='param_field_ch'  value='FTP服务器' ></div>" +
							"<input type='hidden' class='form-control' name='param_field'    value= 'FTP_SERVER_ID' >" +
							"<div class='col-sm-8'><input type='text' class='form-control' name='param_value'      ></div>" +
							"<div class='col-sm-4'><input type='text' class='form-control' name='param_field_ch'  value='文件名称' ></div>" +
							"<input type='hidden' class='form-control' name='param_field'    value= 'FTP_FILE_NAME' >" +
							"<div class='col-sm-8'><input type='text' class='form-control' name='param_value'      ></div>" +
							"<div class='col-sm-4'><input type='text' class='form-control' name='param_field_ch'  value='FTP标志文件名称' ></div>" +
							"<input type='hidden' class='form-control' name='param_field'    value= 'FTP_FLAG_FILE_NAME' >" +
	                        "<div class='col-sm-8'><input type='text' class='form-control' name='param_value'  ></div>" +
	                        "<div class='col-sm-4'><input type='text' class='form-control' name='param_field_ch'  value='FTP下载路径' ></div>" +
	                        "<input type='hidden' class='form-control' name='param_field'    value= 'FTP_PATH' >" +
	                        "<div class='col-sm-8'><input type='text' class='form-control' name='param_value'     ></div>" +
							"</div>");
				}else if(executorHandler == "ModelAnalyzeService"){
							$(".add_list").append("<div class='form-group'>" +
							"<div class='col-sm-4'><input type='text' class='form-control' name='param_field_ch'  value='模型ID' ></div>" +
							"<input type='hidden' class='form-control' name='param_field'    value= 'MODEL_ID' >" +
                                "<div class='col-sm-8'><input type='text' class='form-control' name='param_value'       ></div>" +
							"</div>");
				}else if(executorHandler == "SqlService"){
					$(".add_list").append(
							"<div class='form-group'>" +
							"<div class='col-sm-4'><input type='text' class='form-control' name='param_field_ch'  value='SQL语句ID' ></div>" +
							"<input type='hidden' class='form-control' name='param_field'    value= 'SQL_ID' >" +
							"<div class='col-sm-8'><input type='text' class='form-control' name='param_value'      ></div> " +
							"</div>");
				}else if(executorHandler == "ImageQuery") {
					$(".add_list").append("<div class='form-group'>" +
							
							"<div style = 'margin:0 0 30px 0'><textarea rows = '5' cols = '10' disabled='true' class='form-control' name='param_field_ch'  >" +
							"任务说明：根据数据来源，对无纸化业务数据表中的业务，进行ECM查询或下载\n" +
							"配置说明：查询模式，会在无纸化图像数据表生成对应的影像数据\n" +
							"配置说明：下载模式，会在文件路径下按  目录层级：[日期\机构号\柜员号\图像文件]  生成文件\n" +
							"配置说明：下载模式，图像文件名为流水号-凭证序号，凭证序号1为主件。例如[流水号1-1.jpg][流水号1-2.jpg][流水号2-1.jpg]。\n" +
							"</textarea></div>" +					
							
							"<div class='col-sm-4'><input type='text' class='form-control' name='param_field_ch'  value='影像来源(DATA_SOURCE_ID)' ></div>" +
							"<input type='hidden' class='form-control' name='param_field'    value= 'DATA_SOURCE_ID' >" +
							"<div class='col-sm-8'><input type='text' class='form-control' name='param_value'     ></div>" +

							"<div class='col-sm-4'><input type='text' class='form-control' name='param_field_ch'  value='模式(0-查询，1-下载)' ></div>" +
		                    "<input type='hidden' class='form-control' name='param_field'    value= 'IS_DOWNLOAD' >" +
		                    "<div class='col-sm-8'><input type='text' class='form-control' name='param_value'     ></div>" +

	                    	"<div class='col-sm-4'><input type='text' class='form-control' name='param_field_ch'  value='文件本地路径(下载模式)' ></div>" +
		                    "<input type='hidden' class='form-control' name='param_field'    value= 'LOCAL_PATH' >" +
		                    "<div class='col-sm-8'><input type='text' class='form-control' name='param_value'     ></div>" +
	                        
							"</div>");

					$(".add_list").append("<div class='add_list_custom'></div>");
					$(".add_list").append("<div style='text-align: center;'><button type='button' class='btn btn-default'  onclick='add_form()'>" +
						"<span class='glyphicon glyphicon-plus' style='color: #059b45;font-size: 18px;'></span></button></div>");
				} else if (executorHandler == "ImageUpload"){
					$(".add_list").append("<div class='form-group'>" +
							
							"<div style = 'margin:0 0 30px 0'><textarea rows = '5' cols = '10' disabled='true' class='form-control' name='param_field_ch'  >" +
							"任务说明：将本地文件路径下的批次图片上传或追加到ECM\n" +
							"配置说明：本地图片可通过前置任务[ftp下载]或者[影像下载]获取。目录层级要求为[日期\机构号\柜员号\图像文件]。\n" +
							"配置说明：图像文件名要求为流水号-凭证序号，凭证序号1为主件。例如[流水号1-1.jpg][流水号1-2.jpg][流水号2-1.jpg]。\n" +
							"配置说明：凭证混合模式，如果后督系统已经存在相同三要素的混合批次，则追加影像到该CONTENT_ID\n" +
							"配置说明：凭证不混合模式，将图像上传到ECM\n" +
							"</textarea></div>" +
							
	                        "<div class='col-sm-4'><input type='text' class='form-control' name='param_field_ch'  value='文件本地路径' ></div>" +
	                        "<input type='hidden' class='form-control' name='param_field'    value= 'LOCAL_PATH' >" +
	                        "<div class='col-sm-8'><input type='text' class='form-control' name='param_value'     ></div>" +
	                        
	                        "<div class='col-sm-4'><input type='text' class='form-control' name='param_field_ch'  value='模式(0-凭证不混合,1-凭证混合)' ></div>" +
	                        "<input type='hidden' class='form-control' name='param_field'    value= 'IS_MIX' >" +
	                        "<div class='col-sm-8'><input type='text' class='form-control' name='param_value'     ></div>" +
	                        
	                        "<div class='col-sm-4'><input type='text' class='form-control' name='param_field_ch'  value='统一主附件(可选)(0-附件,1-主件)' ></div>" +
	                        "<input type='hidden' class='form-control' name='param_field'    value= 'PS_LEVEL' >" +
	                        "<div class='col-sm-8'><input type='text' class='form-control' name='param_value'     ></div>" +
	                        
	                        
							"</div>");
					$(".add_list").append("<div class='add_list_custom'></div>");
					$(".add_list").append("<div style='text-align: center;'><button type='button' class='btn btn-default'  onclick='add_form()'>" +
						"<span class='glyphicon glyphicon-plus' style='color: #059b45;font-size: 18px;'></span></button></div>");
				} else if (executorHandler == "OutBatch"){
					$(".add_list").append("<div class='form-group'>" +
							
							"<div style = 'margin:0 0 30px 0'><textarea rows = '5' cols = '10' disabled='true' class='form-control' name='param_field_ch'  >" +
							"任务说明：整合无纸化业务数据表和无纸化图像数据表，在后督数据库生成完整批次信息，勾兑流水。\n" +
							"配置说明：影像来源：处理无纸化业务数据表中对应影像来源的数据\n" +
							"</textarea></div>" +
							
							"<div class='col-sm-4'><input type='text' class='form-control' name='param_field_ch'  value='影像来源(DATA_SOURCE_ID)' ></div>" +
							"<input type='hidden' class='form-control' name='param_field'    value= 'DATA_SOURCE_ID' >" +
							"<div class='col-sm-8'><input type='text' class='form-control' name='param_value'     ></div>" +
							
							"<div class='col-sm-4'><input type='text' class='form-control' name='param_field_ch'  value='后督凭证类型(BUSINESS_ID)' ></div>" +
							"<input type='hidden' class='form-control' name='param_field'    value= 'BUSINESS_ID' >" +
							"<div class='col-sm-8'><input type='text' class='form-control' name='param_value'     ></div>" +
							
							"</div>");
					$(".add_list").append("<div class='add_list_custom'></div>");
					$(".add_list").append("<div style='text-align: center;'><button type='button' class='btn btn-default'  onclick='add_form()'>" +
						"<span class='glyphicon glyphicon-plus' style='color: #059b45;font-size: 18px;'></span></button></div>");

				} else{
					$(".add_list").append("<div class='add_list_custom'></div>");
					$(".add_list").append("<div style='text-align: center;'><button type='button' class='btn btn-default'  onclick='add_form()'>" +
						"<span class='glyphicon glyphicon-plus' style='color: #059b45;font-size: 18px;'></span></button></div>");
				}
				
				var param_field = [];
				$("input[name ='param_field']").each(function (){
					var value = $(this).val(); 
					param_field.push(value);					
				});
				var i = 0;
				$("input[name ='param_value']").each(function (){
					var str = param_field[i];
					if(str in data){
						var value = data[str].param_value;
						$(this).val(value); 
					}
					i++;
				});

			},
		});
    });

	// job operate
	$("#job_list").on('click', '.job_operate',function() {
		var typeName;
		var url;
		var needFresh = false;

		var type = $(this).attr("_type");

		if ("job_del" == type) {
			typeName = "删除";
			url = base_url + "/jobparam/remove";
			needFresh = true;
		} else if ("job_trigger" == type) {
			typeName = "执行";
			url = base_url + "/jobinfo/trigger";
		} else {
			return;
		}

		var id = $(this).parent('p').attr("id");

		layer.confirm('确认' + typeName + '?', {icon: 3, title:'系统提示'}, function(index){
			layer.close(index);

			$.ajax({
				type : 'POST',
				url : url,
				data : {
					"id" : id
				},
				dataType : "json",
				success : function(data){
					if (data.code == 200) {

						layer.open({
							title: '系统提示',
							content: typeName + "成功",
							icon: '1',
							end: function(layero, index){
								if (needFresh) {
									//window.location.reload();
									jobTable.fnDraw();
								}
							}
						});
					} else {
						layer.open({
							title: '系统提示',
							content: (data.msg || typeName + "失败"),
							icon: '2'
						});
					}
				},
			});
		});
	});

	// jquery.validate 自定义校验 “英文字母开头，只含有英文字母、数字和下划线”
	jQuery.validator.addMethod("myValid01", function(value, element) {
		var length = value.length;
		var valid = /^[a-zA-Z][a-zA-Z0-9_]*$/;
		return this.optional(element) || valid.test(value);
	}, "只支持英文字母开头，只含有英文字母、数字和下划线");

	// 新增
	$(".add").click(function(){
		$('#addModal').modal({backdrop: false, keyboard: false}).modal('show');
	});
	var addModalValidate = $("#addModal .form").validate({
		errorElement : 'span',
        errorClass : 'help-block',
        focusInvalid : true,
        rules : {
            jobId : {
				required : true,
				maxlength: 50
			},
            param_value : {
				required : true,
			},
            param_field : {
            	required : true,
                myValid01 : true
            },
            param_field_ch : {
				required : true
			}
        },
        messages : {
            param_value : {
            	required :"参数必须有值"
            },
            param_field : {
            	required :"请输入参数字段"
            },
            param_field_ch : {
            	required : "请输入参数字段说明"
            }
        },
		highlight : function(element) {
            $(element).closest('.form-group').addClass('has-error');
        },
        success : function(label) {
            label.closest('.form-group').removeClass('has-error');
            label.remove();
        },
        errorPlacement : function(error, element) {
            element.parent('div').append(error);
        },
        submitHandler : function(form) {
        	$.post(base_url + "/jobparam/add",  $("#addModal .form").serialize(), function(data, status) {
    			if (data.code == "200") {
					$('#addModal').modal('hide');
					layer.open({
						title: '系统提示',
						content: '新增参数成功',
						icon: '1',
						end: function(layero, index){
							jobTable.fnDraw();
							//window.location.reload();
						}
					});
    			} else {
					layer.open({
						title: '系统提示',
						content: (data.msg || "新增失败"),
						icon: '2'
					});
    			}
    		});
		}
	});
	$("#addModal").on('hide.bs.modal', function () {
		$("#addModal .form")[0].reset();
		addModalValidate.resetForm();
		$("#addModal .form .form-group").removeClass("has-error");
		$(".remote_panel").show();	// remote

		$("#addModal .form input[name='executorHandler']").removeAttr("readonly");
		$("#list_"+selected_num).css("display","none");

	});


	// 更新
	$("#job_list").on('click', '.update',function() {

        var id = $(this).parent('p').attr("id");
        var row = tableData['key'+id];
        if (!row) {
            layer.open({
                title: '系统提示',
                content: ("参数信息加载失败，请刷新页面"),
                icon: '2'
            });
            return;
        }
		// base data
		$("#updateModal .form input[name='id']").val( row.id );
		$('#updateModal .form select[name=jobId] option[value='+ row.jobId +']').prop('selected', true);
		$("#updateModal .form input[name='param_value']").val( row.param_value );
		$("#updateModal .form input[name='param_field_ch']").val( row.param_field_ch );
		$("#updateModal .form input[name='param_field']").val( row.param_field);


		// show
		$('#updateModal').modal({backdrop: false, keyboard: false}).modal('show');
	});
	var updateModalValidate = $("#updateModal .form").validate({
		errorElement : 'span',
        errorClass : 'help-block',
        focusInvalid : true,
        rules : {
            jobId : {
                required : true,
                maxlength: 50
            },
            param_value : {
                required : true,
            },
            param_field : {
                required : true,
                myValid01 : true
            },
            param_field_ch : {
                required : true
            }
        },
        messages : {
            param_value : {
                required :"参数必须有值"
            },
            param_field : {
                required :"请输入参数字段"
            },
            param_field_ch : {
                required : "请输入参数字段说明"
            }
        },
		highlight : function(element) {
            $(element).closest('.form-group').addClass('has-error');
        },
        success : function(label) {
            label.closest('.form-group').removeClass('has-error');
            label.remove();
        },
        errorPlacement : function(error, element) {
            element.parent('div').append(error);
        },
        submitHandler : function(form) {
			// post
    		$.post(base_url + "/jobparam/reschedule", $("#updateModal .form").serialize(), function(data, status) {
    			if (data.code == "200") {
					$('#updateModal').modal('hide');
					layer.open({
						title: '系统提示',
						content: '更新成功',
						icon: '1',
						end: function(layero, index){
							//window.location.reload();
							jobTable.fnDraw();	
						}
					});
    			} else {
					layer.open({
						title: '系统提示',
						content: (data.msg || "更新失败"),
						icon: '2'
					});
    			}
    		});
		}
	});
	$("#updateModal").on('hide.bs.modal', function () {
		$("#updateModal .form")[0].reset()
	});

});
var add_custom_num = 0;
//新增表单
function add_form() {
	if(add_custom_num >= 5){
		alert("一次性最多新增5个参数内容");
		return false;
	}
	add_custom_num++;
	$(".add_list_custom").append("<div class='form-group'><label for='lastname' class='col-sm-2 control-label'>参数字段<font color='black'>*" +
			"</font></label><div class='col-sm-4'><input type='text' class='form-control' name='param_field_ch' placeholder='参数字段说明' maxlength='100' >" +
			"</div><label for='firstname' class='col-sm-2 control-label'>参数值<font color='black'>*</font></label><div class='col-sm-4'>" +
			"<input type='text' class='form-control' name='param_field' placeholder='请输入参数值' maxlength='100' >" +
			"</div><label for='firstname' class='col-sm-2 control-label'>参数说明<font color='black'>*</font>" +
			"</label><div class='col-sm-4'><input type='text' class='form-control' name='param_field' placeholder='请输入参数说明' maxlength='100' ></div></div>")
}
