package com.sunyard.etl.data;



import com.sunyard.etl.data.impl.DaService;
import com.sunyard.etl.system.common.Constants;
import com.sunyard.etl.system.common.GlobVar;
import com.sunyard.etl.system.dao.FlCheckoffDao;
import com.sunyard.etl.system.dao.SmGlobalSetDao;
import com.sunyard.etl.system.dao.impl.FlCheckoffDaoImpl;
import com.sunyard.etl.system.dao.impl.SmGlobalSetDaoImpl;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.IJobHandler;
import com.xxl.job.core.handler.annotation.JobHandler;
import org.springframework.stereotype.Service;
import org.sunyard.util.string.StringUtil;

import java.util.concurrent.Executors;

import static com.sunyard.etl.system.common.GlobVar.daList;

/**
 * 归档程序
 */
@JobHandler(value = "DataACService",name="数据归档")
@Service
public class DataACService extends IJobHandler {

    /**
	 * 
	 */
	private static final long serialVersionUID = 1L;
	private FlCheckoffDao flCheckoffDao = new FlCheckoffDaoImpl();
	private SmGlobalSetDao smGlobalSetDao = new SmGlobalSetDaoImpl();

    @Override
    public ReturnT<String> execute(String jobId, String... params) throws Exception {

        GlobVar.daPool = Executors
                .newFixedThreadPool(GlobVar.maxServices);

             //归档主控程序
            //归档主控
            String DATA_ARCHIVE_DATE = smGlobalSetDao.getParam("SYSTEM","DATA_ARCHIVE_DATE");
            if(StringUtil.checkNull(DATA_ARCHIVE_DATE)){
                DATA_ARCHIVE_DATE = "0";
            }

            daList = flCheckoffDao.getCheckOffByCheckFlagDao(
                    Constants.ALL_CHECK_OFF, Integer.valueOf(DATA_ARCHIVE_DATE));
            for (int i = 0; i < daList.size(); i++) {
                if (GlobVar.daList.get(i).getProcessing() == Constants.NOT_PROCESS) {
                    GlobVar.daList.get(i).setProcessing(Constants.IN_PROCESS);
                    GlobVar.daPool.execute(new DaService(GlobVar.daList.get(i)));
                }
            }


        return  ReturnT.SUCCESS;

    }
}
