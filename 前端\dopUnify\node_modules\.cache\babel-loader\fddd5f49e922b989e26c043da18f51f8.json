{"remainingRequest": "E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-q<PERSON><PERSON>n\\数字运营平台-统一门户工程\\dopUnify\\node_modules\\babel-loader\\lib\\index.js!E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan\\数字运营平台-统一门户工程\\dopUnify\\node_modules\\eslint-loader\\index.js??ref--13-0!E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan\\数字运营平台-统一门户工程\\dopUnify\\src\\views\\system\\externalManage\\post\\component\\table\\info.js", "dependencies": [{"path": "E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-<PERSON><PERSON><PERSON><PERSON>\\数字运营平台-统一门户工程\\dopUnify\\src\\views\\system\\externalManage\\post\\component\\table\\info.js", "mtime": 1686019808919}, {"path": "E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-<PERSON><PERSON><PERSON>n\\数字运营平台-统一门户工程\\dopUnify\\babel.config.js", "mtime": 1686019818359}, {"path": "E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-<PERSON><PERSON><PERSON>n\\数字运营平台-统一门户工程\\dopUnify\\.babelrc", "mtime": 1705285078668}, {"path": "E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-<PERSON><PERSON><PERSON>n\\数字运营平台-统一门户工程\\dopUnify\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-<PERSON><PERSON><PERSON>n\\数字运营平台-统一门户工程\\dopUnify\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-<PERSON><PERSON><PERSON>n\\数字运营平台-统一门户工程\\dopUnify\\node_modules\\eslint-loader\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["v1", "uuidv1", "configTable", "that", "name", "label", "id", "config", "post_no", "component", "colSpan", "rules", "required", "message", "componentProps", "<PERSON><PERSON><PERSON>", "clearable", "options", "external_system_no", "filterable", "role_no"], "sources": ["E:/1_Project/XYD_Project/dop-4.0/dop-4.1-qianduan/数字运营平台-统一门户工程/dopUnify/src/views/system/externalManage/post/component/table/info.js"], "sourcesContent": ["import { v1 as uuidv1 } from 'uuid'\r\n// 表头\r\nexport const configTable = (that) => [\r\n  {\r\n    name: 'post_no',\r\n    label: '运营岗位',\r\n    id: uuidv1()\r\n  },\r\n  {\r\n    name: 'external_system_no',\r\n    label: '系统编号',\r\n    id: uuidv1()\r\n  },\r\n  {\r\n    name: 'role_no',\r\n    label: '角色编号',\r\n    id: uuidv1()\r\n  }\r\n]\r\n\r\n// 新增、修改弹出框表单\r\nexport const config = (that) => ({\r\n  post_no: {\r\n    component: 'select',\r\n    label: '运营岗位',\r\n    colSpan: 24,\r\n    name: 'post_no',\r\n    config: {\r\n      // form-item 配置\r\n      rules: [{ required: true, message: '运营岗位为必选' }]\r\n    },\r\n    componentProps: {\r\n      // input组件配置\r\n      placehodler: '支持运营岗位模糊查询',\r\n      clearable: true\r\n    },\r\n    options: []\r\n  },\r\n  external_system_no: {\r\n    component: 'select',\r\n    label: '系统号',\r\n    colSpan: 24,\r\n    name: 'external_system_no',\r\n    config: {\r\n      rules: [{ required: true, message: '系统号为必选' }]\r\n    },\r\n    componentProps: {\r\n      placehodler: '请选择',\r\n      filterable: true\r\n    },\r\n    options: []\r\n  },\r\n  role_no: {\r\n    component: 'select',\r\n    label: '角色编号',\r\n    colSpan: 24,\r\n    name: 'role_no',\r\n    config: {\r\n      // form-item 配置\r\n      rules: [{ required: true, message: '角色编号为必选' }]\r\n    },\r\n    componentProps: {\r\n      // input组件配置\r\n      placehodler: '支持角色编号模糊查询',\r\n      clearable: true\r\n    },\r\n    options: []\r\n    // methods: {\r\n    //   change(param) {\r\n    //     that.roleNoChange(param)\r\n    //   }\r\n    // }\r\n  }\r\n})\r\n"], "mappings": "AAAA,SAASA,EAAE,IAAIC,MAAM,QAAQ,MAAM;AACnC;AACA,OAAO,IAAMC,WAAW,GAAG,SAAdA,WAAW,CAAIC,IAAI;EAAA,OAAK,CACnC;IACEC,IAAI,EAAE,SAAS;IACfC,KAAK,EAAE,MAAM;IACbC,EAAE,EAAEL,MAAM;EACZ,CAAC,EACD;IACEG,IAAI,EAAE,oBAAoB;IAC1BC,KAAK,EAAE,MAAM;IACbC,EAAE,EAAEL,MAAM;EACZ,CAAC,EACD;IACEG,IAAI,EAAE,SAAS;IACfC,KAAK,EAAE,MAAM;IACbC,EAAE,EAAEL,MAAM;EACZ,CAAC,CACF;AAAA;;AAED;AACA,OAAO,IAAMM,MAAM,GAAG,SAATA,MAAM,CAAIJ,IAAI;EAAA,OAAM;IAC/BK,OAAO,EAAE;MACPC,SAAS,EAAE,QAAQ;MACnBJ,KAAK,EAAE,MAAM;MACbK,OAAO,EAAE,EAAE;MACXN,IAAI,EAAE,SAAS;MACfG,MAAM,EAAE;QACN;QACAI,KAAK,EAAE,CAAC;UAAEC,QAAQ,EAAE,IAAI;UAAEC,OAAO,EAAE;QAAU,CAAC;MAChD,CAAC;MACDC,cAAc,EAAE;QACd;QACAC,WAAW,EAAE,YAAY;QACzBC,SAAS,EAAE;MACb,CAAC;MACDC,OAAO,EAAE;IACX,CAAC;IACDC,kBAAkB,EAAE;MAClBT,SAAS,EAAE,QAAQ;MACnBJ,KAAK,EAAE,KAAK;MACZK,OAAO,EAAE,EAAE;MACXN,IAAI,EAAE,oBAAoB;MAC1BG,MAAM,EAAE;QACNI,KAAK,EAAE,CAAC;UAAEC,QAAQ,EAAE,IAAI;UAAEC,OAAO,EAAE;QAAS,CAAC;MAC/C,CAAC;MACDC,cAAc,EAAE;QACdC,WAAW,EAAE,KAAK;QAClBI,UAAU,EAAE;MACd,CAAC;MACDF,OAAO,EAAE;IACX,CAAC;IACDG,OAAO,EAAE;MACPX,SAAS,EAAE,QAAQ;MACnBJ,KAAK,EAAE,MAAM;MACbK,OAAO,EAAE,EAAE;MACXN,IAAI,EAAE,SAAS;MACfG,MAAM,EAAE;QACN;QACAI,KAAK,EAAE,CAAC;UAAEC,QAAQ,EAAE,IAAI;UAAEC,OAAO,EAAE;QAAU,CAAC;MAChD,CAAC;MACDC,cAAc,EAAE;QACd;QACAC,WAAW,EAAE,YAAY;QACzBC,SAAS,EAAE;MACb,CAAC;MACDC,OAAO,EAAE;MACT;MACA;MACA;MACA;MACA;IACF;EACF,CAAC;AAAA,CAAC"}]}