{"remainingRequest": "E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\node_modules\\babel-loader\\lib\\index.js!E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\src\\views\\redirect\\index.vue?vue&type=script&lang=js&", "dependencies": [{"path": "E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\src\\views\\redirect\\index.vue", "mtime": 1686019809623}, {"path": "E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\babel.config.js", "mtime": 1686019818359}, {"path": "E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\.babelrc", "mtime": 1705285078668}, {"path": "E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1655715099000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:aW1wb3J0ICJjb3JlLWpzL21vZHVsZXMvZXMucmVnZXhwLmV4ZWMuanMiOwppbXBvcnQgImNvcmUtanMvbW9kdWxlcy9lcy5zdHJpbmcucmVwbGFjZS5qcyI7Ci8qIOmAiemhueWNoSDpobXpnaLliLfmlrDph43lrprlkJEqLwpleHBvcnQgZGVmYXVsdCB7CiAgY3JlYXRlZDogZnVuY3Rpb24gY3JlYXRlZCgpIHsKICAgIHZhciBfdGhpcyQkcm91dGUgPSB0aGlzLiRyb3V0ZSwKICAgICAgcGFyYW1zID0gX3RoaXMkJHJvdXRlLnBhcmFtcywKICAgICAgcXVlcnkgPSBfdGhpcyQkcm91dGUucXVlcnk7CiAgICB2YXIgcGF0aCA9IHBhcmFtcy5wYXRoOwogICAgdGhpcy4kcm91dGVyLnJlcGxhY2UoewogICAgICBwYXRoOiAnLycgKyBwYXRoLAogICAgICBxdWVyeTogcXVlcnkKICAgIH0pOwogIH0sCiAgcmVuZGVyOiBmdW5jdGlvbiByZW5kZXIoaCkgewogICAgcmV0dXJuIGgoKTsgLy8g6YG/5YWN6K2m5ZGK5raI5oGvCiAgfQp9Ow=="}, {"version": 3, "mappings": ";;AACA;AACA;EACAA;IACA;MAAAC;MAAAC;IACA;IACA;MAAAC;MAAAD;IAAA;EACA;EACAE;IACA;EACA;AACA", "names": ["created", "params", "query", "path", "render"], "sourceRoot": "src/views/redirect", "sources": ["index.vue"], "sourcesContent": ["<script>\n/* 选项卡 页面刷新重定向*/\nexport default {\n  created() {\n    const { params, query } = this.$route\n    const { path } = params\n    this.$router.replace({ path: '/' + path, query })\n  },\n  render: function(h) {\n    return h() // 避免警告消息\n  }\n}\n</script>\n"]}]}