{"remainingRequest": "D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\node_modules\\babel-loader\\lib\\index.js!D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\src\\views\\system\\config\\external\\component\\table\\index.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\src\\views\\system\\config\\external\\component\\table\\index.vue", "mtime": 1686019808029}, {"path": "D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\babel.config.js", "mtime": 1686019818359}, {"path": "D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\.babelrc", "mtime": 1705285078668}, {"path": "D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1655715099000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA+EA,SACAA,kBACAC,eACAC,wBACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;EAAAC;EAAAC;EAAAC;EAAAC;EAAAC;EAAAC;AACA;EACAC;EACAC;EACAC;IACAC;MACAC;MACAC;QACA;MACA;IACA;IACAC;MACAF;MACAC;QACA;MACA;IACA;EACA;EACAE;IACA;MACAC;QACA;QACAC;QAAA;QACAC;QACAC;QAAA;QACAC;QAAA;QACAC;QACAC;UACAP;UAAA;UACAQ;UACAC;QACA;;QACAC;UACAC;UACAC;UAAA;UACAC;QACA;;QACAC;MACA;;MACAC;QACA;QACAC;UACAC;QACA;QACAC;UACAD;QACA;QACAE;UACAF;QACA;MACA;MACAG;QACAb;UACA;UACAc;QACA;QACAC;QACAC;QACAC;UACAC;UACAC;UACA9B;YACA+B;YACAC;YACAC;YACAC;YACAC;YACAC;YACAC;YACAC;UACA;QACA;MACA;MACAC;MAAA;MACAC;QACA;QACAd;QAAA;QACAe;QAAA;QACAC;QAAA;QACA/B;UACA;UACAgC;UAAA;UACAlB;QACA;;QACAmB;UACArC;UACAsC;UAAA;UACApC;UAAA;UACAC;UACAI;YACA;YACAC;YAAA;YACAC;YAAA;YACAC;UACA;;UACAN;YACA;YACAP;UACA;QACA;;QACA0C;MACA;IACA;EACA;;EACAC;IACAR;MACA;IAAA,CACA;IACA7B;MACA;IACA;IACA;MACAsC;QACA;UACA;UACA;QACA;UACA;UACA;QACA;MACA;MACA;MACAC;IACA;IACA;MACAD;QACA;UACA;UACA;QACA;UACA;QACA;MACA;MACA;MACAC;IACA;IACA;MACAD;QACA;QACA;UACA;QACA;MACA;MACA;MACAC;IACA;EACA;EACAC;IACA;IACA;IACA;EACA;;EACAC;IACA;IACAC;MACA;MACA;QACAlC;UACA;QACA;MACA;;MACA;IACA;IACA;AACA;IACAmC;MAAA;MACA;MACA;QACAC,gBACA;UACAvB;UACAwB;UACArB;UACAC;QACA,EACA;QACAqB,0CACA,8BACA;QACAC;QACAzC;QACAC;MACA;MACA1B;QACA;UAAAmE;UAAA3C;UAAAC;QACA;QACA;QACA;QACA;QACA0C;UACA;YACA;cACAC;gBACA;kBACAC;gBACA;cACA;YACA;UACA;QACA;QACA;MACA;IACA;IACA;AACA;IACAC;MACA,6DACA;QACAlB;QACAmB;MAAA,EACA;MACA;IACA;IACA;AACA;IACAC;MAAA;MACA;MACA;QACA1E;QACA;MACA;QACAA;QACA;MACA;MACA,6DACA;QACAsD;QACAmB;MAAA,EACA;MACA;MACA;QACA;QACA;QACA;QACA;QACA;QACAE;UACAC;QACA;QACA;QACA;QACA;UACA;YACA;YACA,8CACA;UACA;YACA;YACA,iDACA;UACA;QACA;MACA;IACA;IACA,oBACAC;MACA;MACA;MACA;QACA;QACA;UACAC;UACA;QACA;MACA;MACA;IACA;IACA;AACA;IACA3E;MAAA;MACA;QACA;QACA;UACA8D;UACAc;UACAlC;UACAC;QACA;QACA3C,YACA6E;UACA;UACA;UACA;YACA;YACA;cACA;cACA;gBACA;cACA;YACA;cACA;cACAhF,cACA,mCACA,OACA;YACA;UACA;QACA,GACAiF;UACA;UACA;QACA;MACA;IACA;IACA;AACA;IACAC;MACA;IACA;IACA;AACA;AACA;IACAC;MACA;IACA;IAAA;IACA;AACA;IACAC;MACA;MACA;QACA;MACA;QACA;MACA;IACA;IACA;AACA;IACAC;MAAA;MACA,IACA,sDACA,sBACA;QACArF;MACA;QACA;QACA;UACAiE;UACAqB;UACA5C;QACA;QACAtC,aACA4E;UACA,4BAMA;YALAtC;YACAC;YACAC;YACAC;YACAC;UAEA;YACAmB,gBACA;cACAvB;cACAC;cACAC;cACAC;cACAC;cACAE,cACA,uDACA,8CACA;cACAuC;YACA,EACA;YACA7C;YACAC;YACAE;YACAC;YACAF;YACAI,cACA,uDACA,8CACA;YACAuC;YACAC;cACAC;cACAC;YACA;UACA;UACArF,UACA2E;YACA;YACAjF;YACA;UACA,GACAkF;YACA;UAAA,CACA;QACA,GACAA;UACA;QAAA,CACA;MACA;IACA;IACA;AACA;IACAU;MAAA;MACA,IACA,sDACA,sBACA;QACA3F;MACA;QACAC;UACA;YACA;YACA,4BAOA;cANAyC;cACAC;cACAE;cACAC;cACAE;cACAJ;YAEA;cACAqB;gBAEA;gBACA2B;gBACAC;gBACAC;gBACAC;gBACAC;gBACAC;cAAA,GACA;gBAAA;gBACAjD,cACA,uDACA,8CACA;gBACAuC;cAAA;YAEA,GACA;cAAA;cACAvC,cACA,uDACA,8CACA;cACA;cACA4C;cACAC;cACAC;cACAC;cACAR;YAAA,EACA;YACAjF,YACA0E;cACAjF;cACA;cACA;YACA,GACAkF;cACA;YACA;YACA;UACA;QACA;MACA;IACA;IACA;AACA;IACAiB;MACA;IACA;IACA;AACA;AACA;AACA;AACA;IACAC;MACA;MACA;QACA;QACA;UACA;UACAC;QACA;QACAC;MACA;MACA;IACA;IACA;AACA;IACAC;MAAA;MACA;MACA;QACAtG;QACA;MACA;MACAC;QACA;UACA,uCACA,gBACA,iBACA,WACA,eACA,cACA,gBACA,iBACA;UACA;YACAgE;YACAsC;UACA;UACAhG,SACAyE;YACAjF;YACA;UACA,GACAkF;YACA;UAAA,CACA;QACA;MACA;IACA;IACA;AACA;IACAuB;MACA;QAAA5E;MACA;MACA;MACA;IACA;IACA;AACA;IACA6E;MACA;IACA;EACA;AACA", "names": ["commonMsgSuccess", "commonMsgWarn", "commonMsgConfirm", "query", "check", "id<PERSON><PERSON><PERSON>", "add", "modify", "del", "name", "mixins", "props", "defaultForm", "type", "default", "btnAll", "data", "table", "tableColumns", "ref", "selection", "indexNumber", "loading", "componentProps", "height", "formRow", "pageList", "totalNum", "currentPage", "pageSize", "currentRow", "btnDatas", "btnAdd", "show", "btnModify", "btnDelete", "dialog", "width", "visible", "top", "form", "config", "labelWidth", "external_key", "external_desc", "is_open", "trans_modul", "impl_class", "external_type", "external_sql", "external_method", "ischeck", "tableDialog", "btnSubmit", "btnCancle", "title", "tableConfig", "columns", "hiddenPage", "watch", "handler", "deep", "created", "methods", "handleSelectionChange", "queryList", "parameterList", "external_value", "start_date", "end_date", "list", "i", "item", "handleAdd", "oprate", "handleModify", "keys", "obbb", "checkColm", "retFlag", "sql", "then", "catch", "testDialogClose", "changeVisible", "dialogSumbit", "dialogAddSubmit", "oper_type", "last_modi_date", "operation_user", "organ_no", "user_no", "dialogEditSubmit", "old_external_key", "old_external_desc", "old_external_sql", "old_is_open", "old_trans_modul", "old_impl_class", "changeVisibleTD", "commonChoices", "jsonObj", "jsonArr", "handleDelete", "operation_value", "getList", "showLoading"], "sourceRoot": "src/views/system/config/external/component/table", "sources": ["index.vue"], "sourcesContent": ["<template>\r\n  <div class=\"sun-content\">\r\n    <sun-table\r\n      :table-config=\"table\"\r\n      @selection-change=\"handleSelectionChange\"\r\n      @pagination=\"getList\"\r\n    >\r\n      <template slot=\"tableColumn\">\r\n        <el-table-column\r\n          v-for=\"item in table.tableColumns\"\r\n          :key=\"item.id\"\r\n          :prop=\"item.name\"\r\n          :label=\"item.label\"\r\n          :width=\"item.width\"\r\n        >\r\n          <div slot-scope=\"{ row }\">\r\n            <span v-if=\"item.name === 'is_open'\">{{\r\n              row[item.name] | commonFormatValue('IS_OPEN')\r\n            }}</span>\r\n            <span v-else-if=\"item.name === 'trans_modul'\">{{\r\n              row[item.name] | commonFormatValue('SERVICE_MODULE')\r\n            }}</span>\r\n            <span\r\n              v-else-if=\"item.name === 'external_sql'\"\r\n              class=\"textOverflow\"\r\n              :title=\"row[item.name]\"\r\n            >{{ row[item.name] }}</span>\r\n            <span\r\n              v-else-if=\"item.name === 'external_desc'\"\r\n              class=\"textOverflow\"\r\n              :title=\"row[item.name]\"\r\n            >{{ row[item.name] }}</span>\r\n            <span\r\n              v-else-if=\"item.name === 'impl_class'\"\r\n              class=\"textOverflow\"\r\n              :title=\"row[item.name]\"\r\n            >{{ row[item.name] }}</span>\r\n            <span v-else-if=\"item.name === 'last_modi_date'\">{{\r\n              row[item.name] | dateTimeFormat\r\n            }}</span>\r\n            <span v-else>{{ row[item.name] }}</span>\r\n          </div>\r\n        </el-table-column>\r\n      </template>\r\n      <template slot=\"customButton\">\r\n        <sun-button\r\n          :btn-datas=\"btnDatas\"\r\n          @handleAdd=\"handleAdd\"\r\n          @handleModify=\"handleModify\"\r\n          @handleDelete=\"handleDelete\"\r\n        />\r\n        <!--按钮配置-->\r\n      </template></sun-table>\r\n    <sun-form-dialog\r\n      :dialog-config=\"dialog\"\r\n      top=\"6vh\"\r\n      @dialogClose=\"changeVisible\"\r\n      @dialogSubmit=\"dialogSumbit\"\r\n    >\r\n      <div slot=\"rightBtn\" class=\"rightBtn\">\r\n        <el-button\r\n          v-if=\"dialog.form.defaultForm.external_type !== '1'\"\r\n          type=\"success\"\r\n          @click=\"check\"\r\n        >检 测</el-button>\r\n      </div></sun-form-dialog><!--新增、修改弹出框-->\r\n    <sun-table-dialog\r\n      top=\"1rem\"\r\n      :show-page=\"tableDialog.hiddenPage\"\r\n      :dialog-config=\"tableDialog\"\r\n      @dialogClose=\"changeVisibleTD\"\r\n    >\r\n      <template slot-scope=\"{ item, row }\">\r\n        <span v-if=\"item.name === 'dvalue'\">{{ row[item.name] }}</span>\r\n        <span v-else>{{ row[item.name] }}</span>\r\n      </template> </sun-table-dialog><!-- 检测表单 -->\r\n  </div>\r\n</template>\r\n<script>\r\nimport {\r\n  commonMsgSuccess,\r\n  commonMsgWarn,\r\n  commonMsgConfirm\r\n} from '@/utils/message.js' // 提示信息\r\nimport { commonBlank } from '@/utils/common'\r\nimport { dateNowFormat } from '@/utils/date.js'\r\nimport { config, configTable, testConfigTable } from './info' // 表头、表单配置\r\nimport { selectData } from '@/utils/externalData.js'\r\nimport ResizeMixin from '@/utils/ResizeHandler' // 整体页面是否根据总高配置\r\n\r\nimport { system } from '@/api'\r\nconst { query, check, idCheck, add, modify, del } = system.SysExt\r\nexport default {\r\n  name: 'TableList',\r\n  mixins: [ResizeMixin],\r\n  props: {\r\n    defaultForm: {\r\n      type: Object,\r\n      default: function() {\r\n        return {}\r\n      }\r\n    },\r\n    btnAll: {\r\n      type: Object,\r\n      default: function() {\r\n        return {}\r\n      }\r\n    }\r\n  },\r\n  data() {\r\n    return {\r\n      table: {\r\n        // 表格配置\r\n        tableColumns: configTable(), // 表头配置\r\n        ref: 'tableRef',\r\n        selection: true, // 复选\r\n        indexNumber: true, // 序号\r\n        loading: false,\r\n        componentProps: {\r\n          data: [], // 表格数据\r\n          height: '325px',\r\n          formRow: 2 // 表单行数\r\n        },\r\n        pageList: {\r\n          totalNum: 0,\r\n          currentPage: 1, // 当前页\r\n          pageSize: this.$store.getters.pageSize // 当前页显示条数\r\n        },\r\n        currentRow: [] // 选中行\r\n      },\r\n      btnDatas: {\r\n        // 按钮配置\r\n        btnAdd: {\r\n          show: this.btnAll.btnAdd\r\n        },\r\n        btnModify: {\r\n          show: this.btnAll.btnModify\r\n        },\r\n        btnDelete: {\r\n          show: this.btnAll.btnDelete\r\n        }\r\n      },\r\n      dialog: {\r\n        componentProps: {\r\n          // 弹出框配置属性\r\n          width: '90rem'\r\n        },\r\n        visible: false,\r\n        top: '6vh',\r\n        form: {\r\n          config: config(this),\r\n          labelWidth: '13rem',\r\n          defaultForm: {\r\n            external_key: '',\r\n            external_desc: '',\r\n            is_open: '',\r\n            trans_modul: '',\r\n            impl_class: '',\r\n            external_type: '',\r\n            external_sql: '',\r\n            external_method: ''\r\n          }\r\n        }\r\n      },\r\n      ischeck: '0', // 是否进行检测\r\n      tableDialog: {\r\n        // 检测表格弹框\r\n        visible: false, // 显示隐藏配置\r\n        btnSubmit: false, // 确定按钮\r\n        btnCancle: true, // 取消按钮\r\n        componentProps: {\r\n          // 弹出框属性\r\n          title: '查询结果', // 弹出框标题\r\n          width: '50%' // 当前弹出框宽度 默认80%\r\n        },\r\n        tableConfig: {\r\n          ref: 'tableRef',\r\n          columns: testConfigTable(), // 表头配置\r\n          indexNumber: true, // 序号\r\n          loading: false,\r\n          pageList: {\r\n            // 页码\r\n            totalNum: 0, // 总页数\r\n            currentPage: 1, // 当前页\r\n            pageSize: this.$store.getters.pageSize // 当前页显示条数\r\n          },\r\n          componentProps: {\r\n            // 表格属性配置\r\n            data: [] // 表数据\r\n          }\r\n        },\r\n        hiddenPage: true // 页码不显示\r\n      }\r\n    }\r\n  },\r\n  watch: {\r\n    ischeck(val) {\r\n      // console.log('检测值', val)\r\n    },\r\n    loading(value) {\r\n      this.listLoading = this.loading\r\n    },\r\n    'dialog.form.defaultForm.external_type': {\r\n      handler(newName, oldName) {\r\n        if (newName === '0') {\r\n          this.dialog.form.config.external_sql.hidden = false\r\n          this.dialog.form.config.external_method.hidden = true\r\n        } else {\r\n          this.dialog.form.config.external_sql.hidden = true\r\n          this.dialog.form.config.external_method.hidden = false\r\n        }\r\n      },\r\n      // immediate: true\r\n      deep: true\r\n    },\r\n    'dialog.componentProps.title': {\r\n      handler(newName, oldName) {\r\n        if (newName === '编辑') {\r\n          // 编辑状态数据源ID框禁用\r\n          this.dialog.form.config.external_key.componentProps.disabled = true\r\n        } else {\r\n          this.dialog.form.config.external_key.componentProps.disabled = false\r\n        }\r\n      },\r\n      // immediate: true\r\n      deep: true\r\n    },\r\n    'dialog.visible': {\r\n      handler(val) {\r\n        // 每次新增和编辑弹窗打开时重置监测状态0\r\n        if (val) {\r\n          this.ischeck = '0'\r\n        }\r\n      },\r\n      // immediate: true\r\n      deep: true\r\n    }\r\n  },\r\n  created() {\r\n    this.listLoading = this.loading\r\n    this.queryList()\r\n    this.dialog.form.config.external_method.options = selectData() // 修改活新增 数据源类型为自定义时 数据源方法的值\r\n  },\r\n  methods: {\r\n    // 表格选择单行、多行\r\n    handleSelectionChange(val) {\r\n      const currentRow = val\r\n      if (currentRow.length > 1) {\r\n        currentRow.sort(function(a, b) {\r\n          return a.index - b.index\r\n        }) // 选中行排序:升序\r\n      }\r\n      this.table.currentRow = val\r\n    },\r\n    /**\r\n     * 按钮：查询*/\r\n    queryList(currentPage) {\r\n      this.showLoading()\r\n      const msg = {\r\n        parameterList: [\r\n          {\r\n            external_key: this.defaultForm.source_id,\r\n            external_value: this.defaultForm.source_name,\r\n            trans_modul: this.defaultForm.trans_modul,\r\n            impl_class: this.defaultForm.impl_class\r\n          }\r\n        ],\r\n        start_date: this.defaultForm.start_date\r\n          ? this.defaultForm.start_date\r\n          : '',\r\n        end_date: this.defaultForm.end_date ? this.defaultForm.end_date : '',\r\n        currentPage: currentPage || this.table.pageList.currentPage,\r\n        pageSize: this.table.pageList.pageSize\r\n      }\r\n      query(msg).then((res) => {\r\n        const { list, totalNum, currentPage } = res.retMap\r\n        this.table.componentProps.data = list\r\n        this.table.pageList.totalNum = totalNum\r\n        this.table.pageList.currentPage = currentPage\r\n        // 如果是自定义  查询SQL/METHOD格式化\r\n        list.forEach((item) => {\r\n          if (!item.external_sql.startsWith('select')) {\r\n            this.dialog.form.config.external_method.options.forEach((i) => {\r\n              i.children.forEach((val) => {\r\n                if (val.value === item.external_sql) {\r\n                  item.external_sql = val.label\r\n                }\r\n              })\r\n            })\r\n          }\r\n        })\r\n        this.showLoading()\r\n      })\r\n    },\r\n    /**\r\n     * btn - 新增*/\r\n    handleAdd() {\r\n      this.dialog.componentProps = {\r\n        ...this.dialog.componentProps,\r\n        title: '新增',\r\n        oprate: 'add'\r\n      }\r\n      this.changeVisible(true)\r\n    },\r\n    /**\r\n     * btn - 编辑*/\r\n    handleModify() {\r\n      const rows = this.table.currentRow.length\r\n      if (rows === 0) {\r\n        commonMsgWarn('请选择要修改的数据', this)\r\n        return\r\n      } else if (rows > 1) {\r\n        commonMsgWarn('多条数据无法进行修改操作!', this)\r\n        return\r\n      }\r\n      this.dialog.componentProps = {\r\n        ...this.dialog.componentProps,\r\n        title: '编辑',\r\n        oprate: 'edit'\r\n      } // 添加属性\r\n      this.changeVisible(true)\r\n      this.$nextTick(() => {\r\n        // 弹出框加载完成后赋值\r\n        // this.dialog.form.defaultForm = Object.assign({}, this.table.currentRow[0])\r\n        // 弹出框加载完成后赋值、\r\n        const obbb = {}\r\n        const keys = Object.keys(this.dialog.form.defaultForm)\r\n        keys.forEach((item) => {\r\n          obbb[item] = this.table.currentRow[0][item]\r\n        })\r\n        this.dialog.form.defaultForm = obbb\r\n        // 以select开头说明数据源类型是SQl  其他开头数据源类型是自定义\r\n        if (this.table.currentRow[0]) {\r\n          if (this.table.currentRow[0].external_sql.startsWith('select')) {\r\n            this.dialog.form.defaultForm.external_type = '0'\r\n            this.dialog.form.defaultForm.external_sql =\r\n              this.table.currentRow[0].external_sql\r\n          } else {\r\n            this.dialog.form.defaultForm.external_type = '1'\r\n            this.dialog.form.defaultForm.external_method =\r\n              this.table.currentRow[0].external_sql.split('-')[0]\r\n          }\r\n        }\r\n      })\r\n    },\r\n    /** 校验查询sql字段别名规范 */\r\n    checkColm(list) {\r\n      let retFlag = true\r\n      // 遍历list，根据dvalue,dname获取对应值，如果值为null则返回false\r\n      for (let i = 0; i < list.length; i++) {\r\n        const obj = list[i]\r\n        if (commonBlank(obj.dvalue) || commonBlank(obj.dname)) {\r\n          retFlag = false\r\n          break\r\n        }\r\n      }\r\n      return retFlag\r\n    },\r\n    /**\r\n     *btn- 检测*/\r\n    check() {\r\n      if (this.dialog.form.defaultForm.external_type === '0') {\r\n        // SQL检测 检测通过\r\n        const msg = {\r\n          parameterList: [{}],\r\n          sql: this.dialog.form.defaultForm.external_sql,\r\n          trans_modul: this.dialog.form.defaultForm.trans_modul,\r\n          impl_class: this.dialog.form.defaultForm.impl_class\r\n        }\r\n        check(msg)\r\n          .then((res) => {\r\n            this.tableDialog.visible = true\r\n            // 弹框加载完成后赋值\r\n            this.$nextTick(() => {\r\n              const { list } = res.retMap\r\n              if (this.checkColm(list)) {\r\n                this.tableDialog.tableConfig.componentProps.data = list\r\n                if (!commonBlank(list)) {\r\n                  this.ischeck = '1'\r\n                }\r\n              } else {\r\n                this.ischeck = '0'\r\n                commonMsgWarn(\r\n                  '检测失败:SQL查询字段需定义为别名dvalue,dname!',\r\n                  this\r\n                )\r\n              }\r\n            })\r\n          })\r\n          .catch(() => {\r\n            // 检测未通过\r\n            this.ischeck = '0'\r\n          })\r\n      }\r\n    },\r\n    /**\r\n     * 检测结果弹出框 - 关闭*/\r\n    testDialogClose() {\r\n      this.dialogTable.visible = false\r\n    },\r\n    /**\r\n     * 新增修改弹出框 - 关闭\r\n     * @param {Boolean} param 弹出框显示隐藏配置*/\r\n    changeVisible(param) {\r\n      this.dialog.visible = param\r\n    }, // handleAdd\r\n    /**\r\n     * 弹出框 - 确认*/\r\n    dialogSumbit() {\r\n      const param = this.dialog.componentProps.oprate\r\n      if (param === 'add') {\r\n        this.dialogAddSubmit()\r\n      } else {\r\n        this.dialogEditSubmit()\r\n      }\r\n    },\r\n    /**\r\n     * 弹出框 - 确认 - 新增*/\r\n    dialogAddSubmit() {\r\n      if (\r\n        this.dialog.form.defaultForm.external_type === '0' &&\r\n        this.ischeck === '0'\r\n      ) {\r\n        commonMsgWarn('sql检测成功后才可提交', this)\r\n      } else {\r\n        // 先进行id判断是否重复请求   在进行新增请求\r\n        const msg = {\r\n          parameterList: [],\r\n          oper_type: 'idCheck',\r\n          external_key: this.dialog.form.defaultForm.external_key\r\n        }\r\n        idCheck(msg)\r\n          .then(() => {\r\n            const {\r\n              external_key,\r\n              external_desc,\r\n              is_open,\r\n              trans_modul,\r\n              impl_class\r\n            } = this.dialog.form.defaultForm\r\n            const msg2 = {\r\n              parameterList: [\r\n                {\r\n                  external_key: external_key,\r\n                  external_desc: external_desc,\r\n                  is_open: is_open,\r\n                  trans_modul: trans_modul,\r\n                  impl_class: impl_class,\r\n                  external_sql:\r\n                    this.dialog.form.defaultForm.external_type === '0'\r\n                      ? this.dialog.form.defaultForm.external_sql\r\n                      : this.dialog.form.defaultForm.external_method,\r\n                  last_modi_date: dateNowFormat()\r\n                }\r\n              ],\r\n              external_key: external_key,\r\n              external_desc: external_desc,\r\n              trans_modul: trans_modul,\r\n              impl_class: impl_class,\r\n              is_open: is_open,\r\n              external_sql:\r\n                this.dialog.form.defaultForm.external_type === '0'\r\n                  ? this.dialog.form.defaultForm.external_sql\r\n                  : this.dialog.form.defaultForm.external_method,\r\n              last_modi_date: dateNowFormat(),\r\n              operation_user: {\r\n                organ_no: this.$store.getters.organNo,\r\n                user_no: this.$store.getters.userNo\r\n              }\r\n            }\r\n            add(msg2)\r\n              .then((res) => {\r\n                this.queryList()\r\n                commonMsgSuccess(res.retMsg, this)\r\n                this.changeVisible(false) // 弹出框关闭\r\n              })\r\n              .catch((e) => {\r\n                // console.log(e)\r\n              })\r\n          })\r\n          .catch((e) => {\r\n            // console.log(e)\r\n          })\r\n      }\r\n    },\r\n    /**\r\n     * 弹出框 - 确认 - 编辑*/\r\n    dialogEditSubmit() {\r\n      if (\r\n        this.dialog.form.defaultForm.external_type === '0' &&\r\n        this.ischeck === '0'\r\n      ) {\r\n        commonMsgWarn('sql检测成功后才可提交', this)\r\n      } else {\r\n        commonMsgConfirm('是否确认提交当前数据？', this, (param) => {\r\n          if (param) {\r\n            this.showLoading()\r\n            const {\r\n              external_key,\r\n              external_desc,\r\n              trans_modul,\r\n              impl_class,\r\n              external_sql,\r\n              is_open\r\n            } = this.table.currentRow[0]\r\n            const msg = {\r\n              parameterList: [\r\n                {\r\n                  // ...this.table.currentRow[0], // 改动前的服务器信息\r\n                  old_external_key: external_key,\r\n                  old_external_desc: external_desc,\r\n                  old_external_sql: external_sql.split('-')[0],\r\n                  old_is_open: is_open,\r\n                  old_trans_modul: trans_modul,\r\n                  old_impl_class: impl_class,\r\n                  ...this.dialog.form.defaultForm, // 修改后的值\r\n                  external_sql:\r\n                    this.dialog.form.defaultForm.external_type === '0'\r\n                      ? this.dialog.form.defaultForm.external_sql\r\n                      : this.dialog.form.defaultForm.external_method,\r\n                  last_modi_date: dateNowFormat()\r\n                }\r\n              ],\r\n              ...this.dialog.form.defaultForm, // 修改后的值\r\n              external_sql:\r\n                this.dialog.form.defaultForm.external_type === '0'\r\n                  ? this.dialog.form.defaultForm.external_sql\r\n                  : this.dialog.form.defaultForm.external_method,\r\n              // ...this.table.currentRow[0], // 改动前的服务器信息\r\n              old_external_key: external_key,\r\n              old_external_desc: external_desc,\r\n              old_external_sql: external_sql.split('-')[0],\r\n              old_is_open: is_open,\r\n              last_modi_date: dateNowFormat()\r\n            }\r\n            modify(msg)\r\n              .then((res) => {\r\n                commonMsgSuccess(res.retMsg, this)\r\n                this.showLoading()\r\n                this.queryList()\r\n              })\r\n              .catch(() => {\r\n                this.showLoading()\r\n              })\r\n            this.changeVisible(false) // 弹出框关闭\r\n          }\r\n        })\r\n      }\r\n    },\r\n    /**\r\n     * 弹出框 - 检测 - 关闭*/\r\n    changeVisibleTD() {\r\n      this.tableDialog.visible = false\r\n    },\r\n    /**\r\n     * 多行数据拼写报文的方法\r\n     * @param dataArr\t选择行的数组\r\n     * @param attrArr  放置的参数数组\r\n     */\r\n    commonChoices(dataArr, attrArr) {\r\n      const jsonArr = []\r\n      for (let i = 0; i < dataArr.length; i++) {\r\n        const jsonObj = {}\r\n        for (let j = 0; j < attrArr.length; j++) {\r\n          const name = attrArr[j]\r\n          jsonObj[name] = dataArr[i][name]\r\n        }\r\n        jsonArr.push(jsonObj)\r\n      }\r\n      return jsonArr\r\n    },\r\n    /**\r\n     * btn - 删除*/\r\n    handleDelete() {\r\n      const rows = this.table.currentRow\r\n      if (rows.length === 0) {\r\n        commonMsgWarn('请选择要删除的行', this)\r\n        return\r\n      }\r\n      commonMsgConfirm('是否确认删除当前选中行？', this, (param) => {\r\n        if (param) {\r\n          const dels = this.commonChoices(rows, [\r\n            'external_key',\r\n            'external_desc',\r\n            'is_open',\r\n            'trans_modul',\r\n            'impl_class',\r\n            'external_sql',\r\n            'last_modi_date'\r\n          ])\r\n          const msg = {\r\n            parameterList: [],\r\n            operation_value: dels\r\n          }\r\n          del(msg)\r\n            .then((res) => {\r\n              commonMsgSuccess(res.retMsg, this)\r\n              this.queryList()\r\n            })\r\n            .catch((e) => {\r\n              // console.log(e)\r\n            })\r\n        }\r\n      })\r\n    },\r\n    /**\r\n     *页码更新 */\r\n    getList(pageParam) {\r\n      const { currentPage, pageSize } = pageParam\r\n      this.table.pageList.pageSize = pageSize\r\n      this.table.pageList.currentPage = currentPage\r\n      this.queryList()\r\n    },\r\n    /**\r\n     * 加载中动画配置*/\r\n    showLoading() {\r\n      this.listLoading = !this.listLoading\r\n    }\r\n  }\r\n}\r\n</script>\r\n<style lang=\"scss\" scoped>\r\n.jobServer {\r\n  color: #1890ff;\r\n  cursor: pointer;\r\n}\r\n.operator {\r\n  span {\r\n    display: inline-block;\r\n    margin-left: 1rem;\r\n    color: #1890ff;\r\n    cursor: pointer;\r\n  }\r\n}\r\n::v-deep {\r\n  .el-dialog__body {\r\n    .el-table {\r\n      height: 40rem;\r\n      overflow: auto;\r\n    }\r\n  }\r\n}\r\n</style>\r\n"]}]}