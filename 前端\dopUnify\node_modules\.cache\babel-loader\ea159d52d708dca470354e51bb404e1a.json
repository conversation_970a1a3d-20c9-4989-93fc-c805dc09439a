{"remainingRequest": "E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-q<PERSON><PERSON>n\\数字运营平台-统一门户工程\\dopUnify\\node_modules\\babel-loader\\lib\\index.js!E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan\\数字运营平台-统一门户工程\\dopUnify\\node_modules\\qiankun\\es\\sandbox\\index.js", "dependencies": [{"path": "E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan\\数字运营平台-统一门户工程\\dopUnify\\node_modules\\qiankun\\es\\sandbox\\index.js", "mtime": 1667130453000}, {"path": "E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-<PERSON><PERSON><PERSON>n\\数字运营平台-统一门户工程\\dopUnify\\babel.config.js", "mtime": 1686019818359}, {"path": "E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-<PERSON><PERSON><PERSON>n\\数字运营平台-统一门户工程\\dopUnify\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-<PERSON><PERSON><PERSON>n\\数字运营平台-统一门户工程\\dopUnify\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:aW1wb3J0ICJjb3JlLWpzL21vZHVsZXMvZXMuYXJyYXkuc2xpY2UuanMiOwppbXBvcnQgImNvcmUtanMvbW9kdWxlcy9lcy5vYmplY3QudG8tc3RyaW5nLmpzIjsKaW1wb3J0ICJjb3JlLWpzL21vZHVsZXMvd2ViLmRvbS1jb2xsZWN0aW9ucy5mb3ItZWFjaC5qcyI7CmltcG9ydCAiY29yZS1qcy9tb2R1bGVzL2VzLmFycmF5Lm1hcC5qcyI7CmltcG9ydCAiY29yZS1qcy9tb2R1bGVzL2VzLmFycmF5LmNvbmNhdC5qcyI7CmltcG9ydCBfdG9Db25zdW1hYmxlQXJyYXkgZnJvbSAiQGJhYmVsL3J1bnRpbWUvaGVscGVycy9lc20vdG9Db25zdW1hYmxlQXJyYXkiOwppbXBvcnQgX2FzeW5jVG9HZW5lcmF0b3IgZnJvbSAiQGJhYmVsL3J1bnRpbWUvaGVscGVycy9lc20vYXN5bmNUb0dlbmVyYXRvciI7CmltcG9ydCBfcmVnZW5lcmF0b3JSdW50aW1lIGZyb20gIkBiYWJlbC9ydW50aW1lL3JlZ2VuZXJhdG9yIjsKaW1wb3J0IExlZ2FjeVNhbmRib3ggZnJvbSAnLi9sZWdhY3kvc2FuZGJveCc7CmltcG9ydCB7IHBhdGNoQXRCb290c3RyYXBwaW5nLCBwYXRjaEF0TW91bnRpbmcgfSBmcm9tICcuL3BhdGNoZXJzJzsKaW1wb3J0IFByb3h5U2FuZGJveCBmcm9tICcuL3Byb3h5U2FuZGJveCc7CmltcG9ydCBTbmFwc2hvdFNhbmRib3ggZnJvbSAnLi9zbmFwc2hvdFNhbmRib3gnOwpleHBvcnQgeyBnZXRDdXJyZW50UnVubmluZ0FwcCB9IGZyb20gJy4vY29tbW9uJzsKZXhwb3J0IHsgY3NzIH0gZnJvbSAnLi9wYXRjaGVycyc7Ci8qKgogKiDnlJ/miJDlupTnlKjov5DooYzml7bmspnnrrEKICoKICog5rKZ566x5YiG5Lik5Liq57G75Z6L77yaCiAqIDEuIGFwcCDnjq/looPmspnnrrEKICogIGFwcCDnjq/looPmspnnrrHmmK/mjIflupTnlKjliJ3lp4vljJbov4fkuYvlkI7vvIzlupTnlKjkvJrlnKjku4DkuYjmoLfnmoTkuIrkuIvmlofnjq/looPov5DooYzjgILmr4/kuKrlupTnlKjnmoTnjq/looPmspnnrrHlj6rkvJrliJ3lp4vljJbkuIDmrKHvvIzlm6DkuLrlrZDlupTnlKjlj6rkvJrop6blj5HkuIDmrKEgYm9vdHN0cmFwIOOAggogKiAg5a2Q5bqU55So5Zyo5YiH5o2i5pe277yM5a6e6ZmF5LiK5YiH5o2i55qE5pivIGFwcCDnjq/looPmspnnrrHjgIIKICogMi4gcmVuZGVyIOaymeeusQogKiAg5a2Q5bqU55So5ZyoIGFwcCBtb3VudCDlvIDlp4vliY3nlJ/miJDlpb3nmoTnmoTmspnnrrHjgILmr4/mrKHlrZDlupTnlKjliIfmjaLov4flkI7vvIxyZW5kZXIg5rKZ566x6YO95Lya6YeN546w5Yid5aeL5YyW44CCCiAqCiAqIOi/meS5iOiuvuiuoeeahOebrueahOaYr+S4uuS6huS/neivgeavj+S4quWtkOW6lOeUqOWIh+aNouWbnuadpeS5i+WQju+8jOi/mOiDvei/kOihjOWcqOW6lOeUqCBib290c3RyYXAg5LmL5ZCO55qE546v5aKD5LiL44CCCiAqCiAqIEBwYXJhbSBhcHBOYW1lCiAqIEBwYXJhbSBlbGVtZW50R2V0dGVyCiAqIEBwYXJhbSBzY29wZWRDU1MKICogQHBhcmFtIHVzZUxvb3NlU2FuZGJveAogKiBAcGFyYW0gZXhjbHVkZUFzc2V0RmlsdGVyCiAqIEBwYXJhbSBnbG9iYWxDb250ZXh0CiAqIEBwYXJhbSBzcGVlZHlTYW5kQm94CiAqLwpleHBvcnQgZnVuY3Rpb24gY3JlYXRlU2FuZGJveENvbnRhaW5lcihhcHBOYW1lLCBlbGVtZW50R2V0dGVyLCBzY29wZWRDU1MsIHVzZUxvb3NlU2FuZGJveCwgZXhjbHVkZUFzc2V0RmlsdGVyLCBnbG9iYWxDb250ZXh0LCBzcGVlZHlTYW5kQm94KSB7CiAgdmFyIHNhbmRib3g7CiAgaWYgKHdpbmRvdy5Qcm94eSkgewogICAgc2FuZGJveCA9IHVzZUxvb3NlU2FuZGJveCA/IG5ldyBMZWdhY3lTYW5kYm94KGFwcE5hbWUsIGdsb2JhbENvbnRleHQpIDogbmV3IFByb3h5U2FuZGJveChhcHBOYW1lLCBnbG9iYWxDb250ZXh0KTsKICB9IGVsc2UgewogICAgc2FuZGJveCA9IG5ldyBTbmFwc2hvdFNhbmRib3goYXBwTmFtZSk7CiAgfQogIC8vIHNvbWUgc2lkZSBlZmZlY3QgY291bGQgYmUgYmUgaW52b2tlZCB3aGlsZSBib290c3RyYXBwaW5nLCBzdWNoIGFzIGR5bmFtaWMgc3R5bGVzaGVldCBpbmplY3Rpb24gd2l0aCBzdHlsZS1sb2FkZXIsIGVzcGVjaWFsbHkgZHVyaW5nIHRoZSBkZXZlbG9wbWVudCBwaGFzZQogIHZhciBib290c3RyYXBwaW5nRnJlZXJzID0gcGF0Y2hBdEJvb3RzdHJhcHBpbmcoYXBwTmFtZSwgZWxlbWVudEdldHRlciwgc2FuZGJveCwgc2NvcGVkQ1NTLCBleGNsdWRlQXNzZXRGaWx0ZXIsIHNwZWVkeVNhbmRCb3gpOwogIC8vIG1vdW50aW5nIGZyZWVycyBhcmUgb25lLW9mZiBhbmQgc2hvdWxkIGJlIHJlLWluaXQgYXQgZXZlcnkgbW91bnRpbmcgdGltZQogIHZhciBtb3VudGluZ0ZyZWVycyA9IFtdOwogIHZhciBzaWRlRWZmZWN0c1JlYnVpbGRlcnMgPSBbXTsKICByZXR1cm4gewogICAgaW5zdGFuY2U6IHNhbmRib3gsCiAgICAvKioKICAgICAqIOaymeeuseiiqyBtb3VudAogICAgICog5Y+v6IO95piv5LuOIGJvb3RzdHJhcCDnirbmgIHov5vlhaXnmoQgbW91bnQKICAgICAqIOS5n+WPr+iDveaYr+S7jiB1bm1vdW50IOS5i+WQjuWGjeasoeWUpOmGkui/m+WFpSBtb3VudAogICAgICovCiAgICBtb3VudDogZnVuY3Rpb24gbW91bnQoKSB7CiAgICAgIHJldHVybiBfYXN5bmNUb0dlbmVyYXRvciggLyojX19QVVJFX18qL19yZWdlbmVyYXRvclJ1bnRpbWUubWFyayhmdW5jdGlvbiBfY2FsbGVlKCkgewogICAgICAgIHZhciBzaWRlRWZmZWN0c1JlYnVpbGRlcnNBdEJvb3RzdHJhcHBpbmcsIHNpZGVFZmZlY3RzUmVidWlsZGVyc0F0TW91bnRpbmc7CiAgICAgICAgcmV0dXJuIF9yZWdlbmVyYXRvclJ1bnRpbWUud3JhcChmdW5jdGlvbiBfY2FsbGVlJChfY29udGV4dCkgewogICAgICAgICAgd2hpbGUgKDEpIHsKICAgICAgICAgICAgc3dpdGNoIChfY29udGV4dC5wcmV2ID0gX2NvbnRleHQubmV4dCkgewogICAgICAgICAgICAgIGNhc2UgMDoKICAgICAgICAgICAgICAgIC8qIC0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLSDlm6DkuLrmnInkuIrkuIvmlofkvp3otZbvvIh3aW5kb3fvvInvvIzku6XkuIvku6PnoIHmiafooYzpobrluo/kuI3og73lj5ggLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tICovCiAgICAgICAgICAgICAgICAvKiAtLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0gMS4g5ZCv5YqoL+aBouWkjSDmspnnrrEtLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0gKi8KICAgICAgICAgICAgICAgIHNhbmRib3guYWN0aXZlKCk7CiAgICAgICAgICAgICAgICBzaWRlRWZmZWN0c1JlYnVpbGRlcnNBdEJvb3RzdHJhcHBpbmcgPSBzaWRlRWZmZWN0c1JlYnVpbGRlcnMuc2xpY2UoMCwgYm9vdHN0cmFwcGluZ0ZyZWVycy5sZW5ndGgpOwogICAgICAgICAgICAgICAgc2lkZUVmZmVjdHNSZWJ1aWxkZXJzQXRNb3VudGluZyA9IHNpZGVFZmZlY3RzUmVidWlsZGVycy5zbGljZShib290c3RyYXBwaW5nRnJlZXJzLmxlbmd0aCk7IC8vIG11c3QgcmVidWlsZCB0aGUgc2lkZSBlZmZlY3RzIHdoaWNoIGFkZGVkIGF0IGJvb3RzdHJhcHBpbmcgZmlyc3RseSB0byByZWNvdmVyeSB0byBuYXR1cmUgc3RhdGUKICAgICAgICAgICAgICAgIGlmIChzaWRlRWZmZWN0c1JlYnVpbGRlcnNBdEJvb3RzdHJhcHBpbmcubGVuZ3RoKSB7CiAgICAgICAgICAgICAgICAgIHNpZGVFZmZlY3RzUmVidWlsZGVyc0F0Qm9vdHN0cmFwcGluZy5mb3JFYWNoKGZ1bmN0aW9uIChyZWJ1aWxkKSB7CiAgICAgICAgICAgICAgICAgICAgcmV0dXJuIHJlYnVpbGQoKTsKICAgICAgICAgICAgICAgICAgfSk7CiAgICAgICAgICAgICAgICB9CiAgICAgICAgICAgICAgICAvKiAtLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0gMi4g5byA5ZCv5YWo5bGA5Y+Y6YeP6KGl5LiBIC0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLSovCiAgICAgICAgICAgICAgICAvLyByZW5kZXIg5rKZ566x5ZCv5Yqo5pe25byA5aeL5Yqr5oyB5ZCE57G75YWo5bGA55uR5ZCs77yM5bC96YeP5LiN6KaB5Zyo5bqU55So5Yid5aeL5YyW6Zi25q615pyJIOS6i+S7tuebkeWQrC/lrprml7blmagg562J5Ymv5L2c55SoCiAgICAgICAgICAgICAgICBtb3VudGluZ0ZyZWVycyA9IHBhdGNoQXRNb3VudGluZyhhcHBOYW1lLCBlbGVtZW50R2V0dGVyLCBzYW5kYm94LCBzY29wZWRDU1MsIGV4Y2x1ZGVBc3NldEZpbHRlciwgc3BlZWR5U2FuZEJveCk7CiAgICAgICAgICAgICAgICAvKiAtLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0gMy4g6YeN572u5LiA5Lqb5Yid5aeL5YyW5pe255qE5Ymv5L2c55SoIC0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLSovCiAgICAgICAgICAgICAgICAvLyDlrZjlnKggcmVidWlsZGVyIOWImeihqOaYjuacieS6m+WJr+S9nOeUqOmcgOimgemHjeW7ugogICAgICAgICAgICAgICAgaWYgKHNpZGVFZmZlY3RzUmVidWlsZGVyc0F0TW91bnRpbmcubGVuZ3RoKSB7CiAgICAgICAgICAgICAgICAgIHNpZGVFZmZlY3RzUmVidWlsZGVyc0F0TW91bnRpbmcuZm9yRWFjaChmdW5jdGlvbiAocmVidWlsZCkgewogICAgICAgICAgICAgICAgICAgIHJldHVybiByZWJ1aWxkKCk7CiAgICAgICAgICAgICAgICAgIH0pOwogICAgICAgICAgICAgICAgfQogICAgICAgICAgICAgICAgLy8gY2xlYW4gdXAgcmVidWlsZGVycwogICAgICAgICAgICAgICAgc2lkZUVmZmVjdHNSZWJ1aWxkZXJzID0gW107CiAgICAgICAgICAgICAgY2FzZSA3OgogICAgICAgICAgICAgIGNhc2UgImVuZCI6CiAgICAgICAgICAgICAgICByZXR1cm4gX2NvbnRleHQuc3RvcCgpOwogICAgICAgICAgICB9CiAgICAgICAgICB9CiAgICAgICAgfSwgX2NhbGxlZSk7CiAgICAgIH0pKSgpOwogICAgfSwKICAgIC8qKgogICAgICog5oGi5aSNIGdsb2JhbCDnirbmgIHvvIzkvb/lhbbog73lm57liLDlupTnlKjliqDovb3kuYvliY3nmoTnirbmgIEKICAgICAqLwogICAgdW5tb3VudDogZnVuY3Rpb24gdW5tb3VudCgpIHsKICAgICAgcmV0dXJuIF9hc3luY1RvR2VuZXJhdG9yKCAvKiNfX1BVUkVfXyovX3JlZ2VuZXJhdG9yUnVudGltZS5tYXJrKGZ1bmN0aW9uIF9jYWxsZWUyKCkgewogICAgICAgIHJldHVybiBfcmVnZW5lcmF0b3JSdW50aW1lLndyYXAoZnVuY3Rpb24gX2NhbGxlZTIkKF9jb250ZXh0MikgewogICAgICAgICAgd2hpbGUgKDEpIHsKICAgICAgICAgICAgc3dpdGNoIChfY29udGV4dDIucHJldiA9IF9jb250ZXh0Mi5uZXh0KSB7CiAgICAgICAgICAgICAgY2FzZSAwOgogICAgICAgICAgICAgICAgLy8gcmVjb3JkIHRoZSByZWJ1aWxkZXJzIG9mIHdpbmRvdyBzaWRlIGVmZmVjdHMgKGV2ZW50IGxpc3RlbmVycyBvciB0aW1lcnMpCiAgICAgICAgICAgICAgICAvLyBub3RlIHRoYXQgdGhlIGZyZWVzIG9mIG1vdW50aW5nIHBoYXNlIGFyZSBvbmUtb2ZmIGFzIGl0IHdpbGwgYmUgcmUtaW5pdCBhdCBuZXh0IG1vdW50aW5nCiAgICAgICAgICAgICAgICBzaWRlRWZmZWN0c1JlYnVpbGRlcnMgPSBbXS5jb25jYXQoX3RvQ29uc3VtYWJsZUFycmF5KGJvb3RzdHJhcHBpbmdGcmVlcnMpLCBfdG9Db25zdW1hYmxlQXJyYXkobW91bnRpbmdGcmVlcnMpKS5tYXAoZnVuY3Rpb24gKGZyZWUpIHsKICAgICAgICAgICAgICAgICAgcmV0dXJuIGZyZWUoKTsKICAgICAgICAgICAgICAgIH0pOwogICAgICAgICAgICAgICAgc2FuZGJveC5pbmFjdGl2ZSgpOwogICAgICAgICAgICAgIGNhc2UgMjoKICAgICAgICAgICAgICBjYXNlICJlbmQiOgogICAgICAgICAgICAgICAgcmV0dXJuIF9jb250ZXh0Mi5zdG9wKCk7CiAgICAgICAgICAgIH0KICAgICAgICAgIH0KICAgICAgICB9LCBfY2FsbGVlMik7CiAgICAgIH0pKSgpOwogICAgfQogIH07Cn0="}, null]}