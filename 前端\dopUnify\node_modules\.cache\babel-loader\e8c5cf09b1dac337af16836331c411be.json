{"remainingRequest": "D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\node_modules\\babel-loader\\lib\\index.js!D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\node_modules\\sunui\\srcobf\\components\\Dialog\\SunDictionaryDialog\\index.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\node_modules\\sunui\\srcobf\\components\\Dialog\\SunDictionaryDialog\\index.vue", "mtime": 499162500000}, {"path": "D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\babel.config.js", "mtime": 1686019818359}, {"path": "D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1655715099000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA4BA;AACA;AACA;EACAA;EACAC;IACAC;EACA;EACAC;IACAC;MACAC;MACAC;IACA;IACAC;MACAF;MACAC;IACA;EACA;EACAE;IACA;MACAC;MACAC;MACAC;MACAC;IACA;EACA;EACAC;IACAT;MACAU;QACA;UACA;QACA;UACA;UACA;UACA;QACA;MACA;IACA;EACA;EACAC;IACA;IACAC;MAAA;MACA;MACA;QACA;QACA;QACA;QACA;QACA;QACA;MACA;IACA;IACA;IACAC;MAAA;MACA;MACA,mGACAC;QACAC;QACAC;QACAC;QACArB;QACAS;UACAa;YAAAC;YAAAC;UAAA;QACA;QACAC;UACAC;QACA;MACA,oCACAR;QACAC;QACAC;QACAC;QACArB;QACAS;UACAa;YAAAC;YAAAC;UAAA;QACA;QACAC;UACAC;QACA;MACA,oCACAR;QACAC;QACAC;QACAC;QACArB;QACA2B,OACAT;QACAU;UACAC;UACAC;UACAC;UACAC;UACAC;QACA;QACAlB;UACAmB;YAAA;YACA;YACA;YACA;YACA;cACA;cACAC;cACA;cACA;cACA;cACAA;cACAA;YACA;cACA;cACA;cACA;cACA,+BACA;gBAAA;cAAA,EACA;cACAC;gBACAD;gBACAA;cACA;YACA;UACA;QACA;MACA,oBACA;IACA;IACAE;MAAA;MACA;MACA,+GACAnB,6EACAA,6EACAA,iCACA;IACA;IACA;IACAoB;MACA;IACA;IACA;IACAC;MACA;IACA;IACA;IACAC;MACA;QACA;QACA;QACAC,QACAC;UAAA;QAAA,GACAC;UACA;YACAC;UACA;YACAA;UACA;QACA;QACA;MACA;IACA;EACA;AACA", "names": ["name", "components", "SunForm", "props", "visible", "type", "default", "value", "data", "config", "defaultForm", "initValue", "recordNum", "watch", "handler", "methods", "initCompoment", "addForm", "num", "component", "label", "colSpan", "rules", "required", "message", "componentProps", "<PERSON><PERSON><PERSON>", "class", "styleObj", "color", "fontSize", "marginTop", "marginLeft", "cursor", "click", "that", "keys", "addDefaultForm", "beforeClose", "submit", "validateForm", "arrData", "filter", "for<PERSON>ach", "str"], "sourceRoot": "node_modules/sunui/srcobf/components/Dialog/SunDictionaryDialog", "sources": ["index.vue"], "sourcesContent": ["<!--下拉菜单源数据配置  -->\r\n<template>\r\n  <el-dialog\r\n    :visible=\"visible\"\r\n    v-bind=\"$attrs\"\r\n    :before-close=\"beforeClose\"\r\n    :destroy-on-close=\"true\"\r\n    :close-on-click-modal=\"false\"\r\n    :append-to-body=\"true\"\r\n  >\r\n    <div>\r\n      <sun-form\r\n        ref=\"pointref\"\r\n        :config=\"config\"\r\n        :default-form=\"defaultForm\"\r\n        :reset=\"false\"\r\n        :query=\"false\"\r\n        @validateForm=\"validateForm\"\r\n      />\r\n    </div>\r\n    <span slot=\"footer\" class=\"dialog-footer\">\r\n      <el-button @click=\"beforeClose\">取 消</el-button>\r\n      <el-button type=\"primary\" @click=\"submit\">提交</el-button>\r\n    </span>\r\n  </el-dialog>\r\n</template>\r\n\r\n<script>\r\nimport SunForm from '../../SunForm'\r\n// import { commonBlank } from '@/utils/common'\r\nexport default {\r\n  name: 'SunDictionaryDialog',\r\n  components: {\r\n    SunForm\r\n  },\r\n  props: {\r\n    visible: {\r\n      type: Boolean,\r\n      default: false\r\n    },\r\n    value: {\r\n      type: String,\r\n      default: ''\r\n    }\r\n  },\r\n  data() {\r\n    return {\r\n      config: {},\r\n      defaultForm: {},\r\n      initValue: 'aa0',\r\n      recordNum: 0\r\n    }\r\n  },\r\n  watch: {\r\n    visible: {\r\n      handler(newVal, oldVal) {\r\n        if (newVal) {\r\n          this.initCompoment()\r\n        } else {\r\n          this.config = {}\r\n          this.defaultForm = {}\r\n          this.recordNum = 0\r\n        }\r\n      }\r\n    }\r\n  },\r\n  methods: {\r\n    // 初始化规则得分配置对象\r\n    initCompoment() {\r\n      const ss = ['a', 'b', 'c', 'd', 'e', 'f', 'g', 'h', 'i', 'j']\r\n      this.value.split('||').forEach((item, index) => {\r\n        const zz = parseInt(index / 10) // 十位数\r\n        const yy = parseInt(index % 10) // 个位数\r\n        const str = ss[zz] + ss[yy] + index\r\n        this.addForm(str)\r\n        this.addDefaultForm(str, item)\r\n        this.recordNum = index + 1\r\n      })\r\n    },\r\n    // 构造连接表单\r\n    addForm(num) {\r\n      const that = this\r\n      this.config = Object.assign({}, this.config, {\r\n        [num + 'a']: {\r\n          component: 'input',\r\n          label: '值',\r\n          colSpan: 10,\r\n          name: num + 'a',\r\n          config: {\r\n            rules: [{ required: true, message: '值为必输' }]\r\n          },\r\n          componentProps: {\r\n            placehodler: '请输入'\r\n          }\r\n        },\r\n        [num + 'b']: {\r\n          component: 'input',\r\n          label: '描述',\r\n          colSpan: 10,\r\n          name: num + 'b',\r\n          config: {\r\n            rules: [{ required: true, message: '描述为必输' }]\r\n          },\r\n          componentProps: {\r\n            placehodler: '请输入'\r\n          }\r\n        },\r\n        [num + 'c']: {\r\n          component: 'icon',\r\n          label: '',\r\n          colSpan: 3,\r\n          name: num + 'c',\r\n          class:\r\n            num === this.initValue ? 'el-icon-circle-plus' : 'el-icon-remove',\r\n          styleObj: {\r\n            color: num === this.initValue ? '#67c23a' : '#f56c6c',\r\n            fontSize: '2.6rem',\r\n            marginTop: '0.5rem',\r\n            marginLeft: '-9rem',\r\n            cursor: 'pointer'\r\n          },\r\n          methods: {\r\n            click({ name }) {\r\n              // 获取点击的行数\r\n              const ss = ['a', 'b', 'c', 'd', 'e', 'f', 'g', 'h', 'i', 'j']\r\n              const num = name.match(/\\d+/g)[0]\r\n              if (num === '0') {\r\n                // add\r\n                that.recordNum = that.recordNum + 1\r\n                const zz = parseInt(that.recordNum / 10) // 十位数\r\n                const yy = parseInt(that.recordNum % 10) // 个位数\r\n                const str = ss[zz] + ss[yy] + that.recordNum\r\n                that.addForm(str)\r\n                that.addDefaultForm(str, '')\r\n              } else {\r\n                // delete\r\n                const zz = parseInt(num / 10)\r\n                const yy = parseInt(num % 10)\r\n                const keys = ['a', 'b', 'c'].map(\r\n                  (item) => ss[zz] + ss[yy] + num + item\r\n                )\r\n                keys.forEach((item) => {\r\n                  that.$delete(that.config, item)\r\n                  that.$delete(that.defaultForm, item)\r\n                })\r\n              }\r\n            }\r\n          }\r\n        }\r\n      })\r\n    },\r\n    addDefaultForm(num, value) {\r\n      const arrData = value.split('-')\r\n      this.defaultForm = Object.assign({}, this.defaultForm, {\r\n        [num + 'a']: value !== '' ? arrData[0] : '',\r\n        [num + 'b']: value !== '' ? arrData[1] : '',\r\n        [num + 'c']: ''\r\n      })\r\n    },\r\n    // 关闭弹框\r\n    beforeClose() {\r\n      this.$emit('update:visible', false)\r\n    },\r\n    // 提交数据\r\n    submit() {\r\n      this.$refs.pointref.validateForm()\r\n    },\r\n    // 表单校验\r\n    validateForm(valid) {\r\n      if (valid) {\r\n        const arrData = Object.values(this.defaultForm)\r\n        let str = ''\r\n        arrData\r\n          .filter((item) => item !== '')\r\n          .forEach((item2, index) => {\r\n            if (index % 2 === 0) {\r\n              str += item2 + '-'\r\n            } else {\r\n              str += item2 + '||'\r\n            }\r\n          })\r\n        this.$emit('submit', [this.defaultForm, str.slice(0, -2)])\r\n      }\r\n    }\r\n  }\r\n}\r\n</script>\r\n<style scoped lang=\"scss\"></style>\r\n"]}]}