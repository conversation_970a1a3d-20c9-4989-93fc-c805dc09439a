package com.sunyard.etl.nps.dao;

import java.sql.SQLException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;












import java.util.Map;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.sunyard.util.dbutil.DBHandler;

import com.sun.rowset.CachedRowSetImpl;
import com.sunyard.etl.nps.model.BpTmpbatchTb;
import com.sunyard.etl.nps.model.NpBusinessData;
import com.sunyard.etl.nps.orm.NpBusinessDataOrm;
import com.sunyard.etl.system.common.Constants;
import com.sunyard.etl.system.orm.Orm;
import com.xxl.job.core.log.XxlJobLogger;

public class NpBusinessDataTbDao {
	
	private Orm<NpBusinessData> npBusinessDataOrm = new NpBusinessDataOrm();
	private String tableName;
	
	protected final Logger log = LoggerFactory.getLogger(getClass());

	public NpBusinessDataTbDao (){
		
	}
	
	public NpBusinessDataTbDao (String tableName){
		this.tableName = tableName;
	}
	
	/**
	 * 根据批次信息从无纸化业务表中取得业务信息（重构：根据nopaperId获取待处理的业务数据）
	 * 
	 * <AUTHOR> 2017年7月6日
	 * @param batch
	 * @return
	 * @throws SQLException
	 */
	public List<NpBusinessData> getPendingBusinessData(String dataSourceId) {
		DBHandler dbHandler = new DBHandler(Constants.CONN_POOL_NAME);
		CachedRowSetImpl rs = null;
		List<NpBusinessData> npBusinessDataList = new ArrayList<NpBusinessData>();
		String sql = "SELECT * FROM NP_BUSINESS_DATA_TB  T WHERE T.DATA_SOURCE_ID = ? AND T.FLAG = '0' AND T.ERROR_FLAG = '0' ORDER BY  BUSI_DATA_NO ";
		XxlJobLogger.log("获取业务集合:" + sql, tableName);
		try {
			rs = dbHandler.queryRs(sql, dataSourceId);
			while (rs.next()) {
				NpBusinessData busi = npBusinessDataOrm.orm(rs);
				npBusinessDataList.add(busi);
			}
		} catch (SQLException e) {
			e.printStackTrace();
			return null;
		}
		return npBusinessDataList;
	}
	
	
	/**
	 * 
	 * @Title getBatch
	 * @Description 提取批次
	 * <AUTHOR> 2017年8月8日
	 * @param nopaperId
	 * @return
	 * @throws SQLException
	 */
	public List<String> getBatch(String dataSourceId) throws SQLException {
		List<String> list = new ArrayList<String>();
		DBHandler dbHandler = new DBHandler(Constants.CONN_POOL_NAME);
		CachedRowSetImpl rs = null;
		String sql = "SELECT DISTINCT T.OCCUR_DATE,T.SITE_NO,T.OPERATOR_NO FROM NP_BUSINESS_DATA_TB  T "
				+ "WHERE T.DATA_SOURCE_ID = ? AND T.ERROR_FLAG = '0' AND T.FLAG = '1' AND T.ADMS5 = '0'";
		rs = dbHandler.queryRs(sql, dataSourceId);
		while (rs.next()) {
			list.add(rs.getString(1) + "," + rs.getString(2) + "," + rs.getString(3));
		}
		return list;
	}
	
	/**
	 * 
	 * @Title updateBatchId
	 * @Description 跟新批次号
	 * <AUTHOR>
	 * 2017年10月30日
	 * @return
	 * @throws SQLException
	 */
	public boolean updateBatchId(String batchId, String occurDate,
			String siteNo, String operatorNo, String dataSourceId)
			throws SQLException {
		int i = 0;
		DBHandler dbHandler = new DBHandler(Constants.CONN_POOL_NAME);
		String sql = "UPDATE NP_BUSINESS_DATA_TB T SET T.BATCH_ID = ? "
				+ "WHERE T.OCCUR_DATE = ? AND  T.SITE_NO = ? "
				+ "AND T.OPERATOR_NO = ? AND T.DATA_SOURCE_ID = ?"
				+ "AND T.ERROR_FLAG = '0' AND T.FLAG = '1' AND T.ADMS5 = '0'";
		i = dbHandler.execute(sql, batchId, occurDate, siteNo, operatorNo, dataSourceId);
		if (i == 0) {
			return false;
		} else {
			return true;
		}
	}
	
	/**
	 * 
	 * @Title getBatch
	 * @Description 获取可接入的批次号列表
	 * <AUTHOR> 2017年7月12日
	 * @param nopaperId
	 * @return
	 * @throws SQLException
	 */
	public List<String> getBatchId(String dataSourceId) throws SQLException {
		List<String> list = new ArrayList<String>();
		DBHandler dbHandler = new DBHandler(Constants.CONN_POOL_NAME);
		CachedRowSetImpl rs = null;
		String sql = "SELECT DISTINCT BATCH_ID  FROM NP_BUSINESS_DATA_TB T WHERE T.DATA_SOURCE_ID = ? "
				+ "AND T.FLAG = '1' AND T.ADMS5 = '0' ORDER BY BATCH_ID";
		rs = dbHandler.queryRs(sql, dataSourceId);
		while (rs.next()) {
			list.add(rs.getString(1));
		}
		return list;
	}
	
	
	/**
	 * 
	 * @Title updateFlag
	 * @Description 更新处理状态
	 * <AUTHOR> 2017年7月20日
	 * @return
	 * @throws SQLException
	 */
	public boolean updateTarget(String batchId, String target) throws SQLException {
		DBHandler dbHandler = new DBHandler(Constants.CONN_POOL_NAME);
		String sql = "UPDATE NP_BUSINESS_DATA_TB T SET T." + target	+ " = '1'  WHERE T.BATCH_ID = ?";
		int i = dbHandler.execute(sql, batchId);
		if (i < 0)
			return false;
		return true;

	}
	
	
	

	/**
	 * 根据无纸化ID获取差错业务
	 * 
	 * <AUTHOR> 2017年7月6日
	 * @param batch
	 * @return
	 * @throws SQLException
	 */

	public List<NpBusinessData> getPendingErrorBusinessData(String nopaperId)
			throws SQLException {
		DBHandler dbHandler = new DBHandler(Constants.CONN_POOL_NAME);
		CachedRowSetImpl rs = null;
		List<NpBusinessData> npBusinessDataList = new ArrayList<NpBusinessData>();
		String sql = "SELECT * FROM NP_BUSINESS_DATA_TB T WHERE T.NOPAPER_ID IN ('"+nopaperId +"') AND T.ERROR_FLAG = '1' AND T.FLAG = '1' AND T.ADMS5 = '0'";
		log.info("根据批次信息从无纸化业务表中取得业务信息:" + sql);
		rs = dbHandler.queryRs(sql);
		while (rs.next()) {
			NpBusinessData npBusinessData = npBusinessDataOrm.orm(rs);
			npBusinessDataList.add(npBusinessData);
		}
		return npBusinessDataList;
	}
	
	
	
	
	/**
	 * 根据无纸化ID从无纸化业务数据表中提取需要处理的日期
	 * 
	 * <AUTHOR> 2017年7月6日
	 * @param nopaperId
	 * @return
	 * @throws SQLException
	 */

	public List<String> getPendingDateList(String dataSourceId) {
		DBHandler dbHandler = new DBHandler(Constants.CONN_POOL_NAME);
		CachedRowSetImpl rs = null;
		List<String> pendingDateList = new ArrayList<String>();
		String sql = "SELECT DISTINCT  OCCUR_DATE  FROM NP_BUSINESS_DATA_TB T  WHERE T.NOPAPER_ID = ? AND T.FLAG = '0' AND T.ERROR_FLAG = '0'  ORDER BY OCCUR_DATE";
		log.info("获取dataSourceId = " + dataSourceId + "的无纸化业务待处理日期:" + sql);
		try {
			rs = dbHandler.queryRs(sql);
			while (rs.next()) {
				pendingDateList.add(rs.getString(1));
			}
		} catch (SQLException e) {
			e.printStackTrace();
			return null;
		}
		return pendingDateList;
	}
	
	

	/**
	 * 根据日期+无纸化ID从无纸化业务数据表中提取批次
	 * 
	 * <AUTHOR> 2017年7月6日
	 * @param nopaperId
	 * @return
	 * @throws SQLException
	 */

	public List<BpTmpbatchTb> getPendingBatchList(String occurDate,
			String nopaperId) {
		DBHandler dbHandler = new DBHandler(Constants.CONN_POOL_NAME);
		CachedRowSetImpl rs = null;
		List<BpTmpbatchTb> pendingBatchList = new ArrayList<BpTmpbatchTb>();
		String sql = "SELECT DISTINCT OCCUR_DATE,T.SITE_NO,T.OPERATOR_NO "
				+ "FROM NP_BUSINESS_DATA_TB T  " + "WHERE T.NOPAPER_ID IN ('"+nopaperId+"')  AND OCCUR_DATE = ? AND T.FLAG = '0' AND T.ERROR_FLAG = '0'  ORDER BY OCCUR_DATE";

		log.info("提取nopaperId = " + nopaperId + "当前待处理的批次:" + sql);
		try {
			rs = dbHandler.queryRs(sql,occurDate);
			while (rs.next()) {
				BpTmpbatchTb batch = new BpTmpbatchTb();
				batch.setOccurDate(rs.getString("OCCUR_DATE"));
				batch.setSiteNo(rs.getString("SITE_NO"));
				batch.setOperatorNo(rs.getString("OPERATOR_NO"));
				pendingBatchList.add(batch);
			}
		} catch (SQLException e) {
			e.printStackTrace();
			return null;
		}
		return pendingBatchList;
	}
	
	
	

	/**
	 * 根据批次信息从无纸化业务表中取得业务信息（重构：根据nopaperId获取待处理的业务数据）
	 * 
	 * <AUTHOR> 2017年7月6日
	 * @param batch
	 * @return
	 * @throws SQLException
	 */
	public List<NpBusinessData> getPendingBusinessData(String nopaperId,
			String occurDate, String siteNo, String operatorNo) {
		DBHandler dbHandler = new DBHandler(Constants.CONN_POOL_NAME);
		CachedRowSetImpl rs = null;
		List<NpBusinessData> npBusinessDataList = new ArrayList<NpBusinessData>();
		String sql = "SELECT * FROM NP_BUSINESS_DATA_TB  T WHERE T.NOPAPER_ID  IN ('"+nopaperId+"') AND T.FLAG = '0'  AND T.OCCUR_DATE = ? "
				+ "AND T.SITE_NO = ? AND T.OPERATOR_NO = ? ";
		log.info("根据批次信息从无纸化业务表中取得业务信息:" + sql);
		try {
			rs = dbHandler.queryRs(sql, occurDate, siteNo,
					operatorNo);
			while (rs.next()) {
				NpBusinessData busi = npBusinessDataOrm.orm(rs);
				npBusinessDataList.add(busi);
			}
		} catch (SQLException e) {
			e.printStackTrace();
			return null;
		}
		return npBusinessDataList;
	}


	public boolean updateBusiData(String batchId, String contentId,
			String indexValue, String busiDataNo) {
		DBHandler dbHandler = new DBHandler(Constants.CONN_POOL_NAME);
		String sql = "UPDATE NP_BUSINESS_DATA_TB T SET T.FLAG = '1',T.BATCH_ID = '"
				+ batchId
				+ "',T.CONTENT_ID_NEW ='"
				+ contentId
				+ "', T.INDEX_VALUE_NEW = '"
				+ indexValue
				+ "' WHERE T.BUSI_DATA_NO IN ('" + busiDataNo + "')";
		try {
			int i = dbHandler.execute(sql);
			if (i == 0)
				return false;
		} catch (SQLException e) {
			e.printStackTrace();
			return false;
		}
		return true;
	}
	
	
	
	public int updateOne(String flag ,String batchId, String contentIdNew, String indexValueNew, String busiDataNo) throws SQLException {
		DBHandler dbHandler = new DBHandler(Constants.CONN_POOL_NAME);
		String sql = "UPDATE NP_BUSINESS_DATA_TB T SET T.FLAG = ? ";
		String params = flag;
		if (null != batchId) {
			sql = sql + " ,T.BATCH_ID = ?";
			params = params + "," + batchId;
		}
		if (null != contentIdNew) {
			sql = sql + " ,T.CONTENT_ID_NEW = ?";
			params = params + "," + contentIdNew;
		}
		if (null != indexValueNew) {
			sql = sql + " ,T.INDEX_VALUE_NEW = ?";
			params = params + "," + indexValueNew;
		}
		sql = sql + "WHERE T.BUSI_DATA_NO IN ('" + busiDataNo + "')";
		return dbHandler.execute(sql, params);
	}
	
	
	public int updateFlag(String flag, String busiDataNo) throws SQLException {
		DBHandler dbHandler = new DBHandler(Constants.CONN_POOL_NAME);
		String sql = "UPDATE NP_BUSINESS_DATA_TB T SET T.FLAG = ?  WHERE T.BUSI_DATA_NO = ?";		 
		int i =  dbHandler.execute(sql, flag, busiDataNo);
		return i;
	}
	
	

	/**
	 * 根据日期和任务ID从无纸化业务表中获取流水号和BUSINESSNO的对应关系
	 * 
	 * <AUTHOR> 2017年7月10日
	 * @param occurDate
	 * @param nopaperId
	 * @return
	 * @throws SQLException
	 */
	public Map<String, String> getBusiDataNoByDataList(List<NpBusinessData> list)
			throws SQLException {
		Map<String, String> map = new HashMap<String, String>();
		if (list.size() == 0 || list == null) {
			return map;
		}
		String occurDate = list.get(0).getOccurDate();
		String siteNo = list.get(0).getSiteNo();
		String operatorNo = list.get(0).getOperatorNo();
		String flowId = list.get(0).getFlowId();
		String dataSourceId = list.get(0).getDataSourceId();
		for (int i = 1; i < list.size(); i++) {
			NpBusinessData data = list.get(i);
			flowId = flowId + "','" + data.getFlowId();
		}
		DBHandler dbHandler = new DBHandler(Constants.CONN_POOL_NAME);
		CachedRowSetImpl rs = null;
		String sql = "SELECT T.FLOW_ID,T.BUSI_DATA_NO FROM NP_BUSINESS_DATA_TB  T WHERE "
				+ " T.OCCUR_DATE = '" + occurDate
				+ "' AND T.SITE_NO = '" + siteNo
				+ "' AND T.OPERATOR_NO = '" + operatorNo
				+ "' AND T.DATA_SOURCE_ID = '" + dataSourceId
				+ "' AND T.FLOW_ID IN ('" + flowId + "')"
				+ " AND T.FLAG >= '0' ";
		log.info("查询BUSI_DATA_NO:" + sql);
		rs = dbHandler.queryRs(sql);
		while (rs.next()) {
			map.put(rs.getString(1), rs.getString(2));
		}
		return map;
	}
	

	/**
	 * 从无纸化业务信息表中删除数据
	 * 
	 * <AUTHOR> 2017年7月7日
	 * @param cleanByBusiDataNo
	 * @return
	 */
	public boolean cleanByBusiDataNo(String busiDataNo) {
		DBHandler dbHandler = new DBHandler(Constants.CONN_POOL_NAME);
		try {
			String sql = "DELETE FROM NP_BUSINESS_DATA_TB T WHERE T.BUSI_DATA_NO IN ('"
					+ busiDataNo + "') ";
			String sql2 = "DELETE FROM NP_IMAGE_DATA_TB T WHERE T.BUSI_DATA_NO IN ('"
					+ busiDataNo + "') ";
			int[] ii = dbHandler.executeAsBatch(sql, sql2);
			for (int i : ii) {
				if (i < 0 && i != -2)
					return false;
			}
		} catch (SQLException e) {
			e.printStackTrace();
			return false;
		}
		return true;
	}
	
	
	/**
	 * 把FTP业务信息插入到无纸化业务信息表中
	 * 
	 * <AUTHOR> 2017年7月7日
	 * @param npBusinessDataList
	 * @return
	 */
	public boolean insertNpBusinessData(List<NpBusinessData> npBusinessDataList) {
		DBHandler dbHandler = new DBHandler(Constants.CONN_POOL_NAME);
		List<Object[]> paramsList = new ArrayList<Object[]>();
		for (NpBusinessData i : npBusinessDataList) {
			String[] params = { i.getOccurDate(), i.getSiteNo(),
					i.getOperatorNo(), i.getFlowId(), i.getFormName(),
					i.getContentId(), i.getDataSourceId(), i.getBatchId(), i.getIndexValue(),i.getMix() };
			paramsList.add(params);
		}
		try {
			String sql = "INSERT INTO NP_BUSINESS_DATA_TB(OCCUR_DATE,SITE_NO,OPERATOR_NO,FLOW_ID,FORM_NAME,CONTENT_ID,DATA_SOURCE_ID,BATCH_ID,INDEX_VALUE,MIX) VALUES (?,?,?,?,?,?,?,?,?,?)";
			int[] ii = dbHandler.executeAsBatch(sql, paramsList);
			for (int i : ii) {
				if (i < 0 && i != -2)
					return false;
			}
		} catch (SQLException e) {
			e.printStackTrace();
			return false;
		}
		return true;
	}
	
	
	public int checkLeave(String occurDate, String siteNo, String operatorNo) throws SQLException{
		DBHandler dbHandler = new DBHandler(Constants.CONN_POOL_NAME);
		CachedRowSetImpl rs = null;
		String sql = "SELECT COUNT(*) FROM NP_BUSINESS_DATA_TB WHERE OCCUR_DATE = ? AND SITE_NO = ? AND OPERATOR_NO = ? AND FLAG = '0'";
		rs = dbHandler.queryRs(sql, occurDate, siteNo, operatorNo);
		while (rs.next()){
			return rs.getInt(1);
		}
		return -1;
	}
	
	

	public List<String> checkMix(String batchId) throws SQLException{
		DBHandler dbHandler = new DBHandler(Constants.CONN_POOL_NAME);
		CachedRowSetImpl rs = null;
		List<String> list = new ArrayList<String>();
		String sql = "SELECT DISTINCT MIX  FROM NP_BUSINESS_DATA_TB WHERE BATCH_ID = ? AND  MIX IS NOT NULL";
		rs = dbHandler.queryRs(sql, batchId);
		while (rs.next()){
			list.add(rs.getString("MIX"));
		}
		return list;
	}
	
	
//	
//	public static void main(String[] args) throws SQLException {
//		NpBusinessDataTbDao a = new NpBusinessDataTbDao();
//		int i = a.updateFlag("-1", "22481");
//		System.out.println(i);
//		System.out.println("11");
//	}
}
