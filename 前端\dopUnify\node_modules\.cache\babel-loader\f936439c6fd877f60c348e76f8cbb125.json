{"remainingRequest": "D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\node_modules\\babel-loader\\lib\\index.js!D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\node_modules\\qiankun\\es\\utils.js", "dependencies": [{"path": "D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\node_modules\\qiankun\\es\\utils.js", "mtime": 1667130453000}, {"path": "D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\babel.config.js", "mtime": 1686019818359}, {"path": "D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["_typeof", "_createClass", "_classCallCheck", "_isFunction", "_once", "_snakeCase", "version", "toArray", "array", "Array", "isArray", "sleep", "ms", "Promise", "resolve", "setTimeout", "nextTick", "window", "Zone", "cb", "then", "globalTaskPending", "nextTask", "fnRegexCheckCacheMap", "WeakMap", "isConstructable", "fn", "hasPrototypeMethods", "prototype", "constructor", "Object", "getOwnPropertyNames", "length", "has", "get", "constructable", "fnString", "toString", "constructableFunctionRegex", "classRegex", "test", "set", "naughty<PERSON><PERSON><PERSON>", "document", "all", "callableFnCacheMap", "isCallable", "callable", "frozenPropertyCacheMap", "isPropertyFrozen", "target", "p", "targetPropertiesFromCache", "propertyDescriptor", "getOwnPropertyDescriptor", "frozen", "Boolean", "configurable", "writable", "boundedMap", "isBoundedFunction", "bounded", "name", "indexOf", "hasOwnProperty", "qiankunHeadTagName", "getDefaultTplWrapper", "tpl", "tplWithSimulatedHead", "replace", "concat", "getWrapperId", "nativeGlobal", "Function", "getGlobalAppInstanceMap", "defineProperty", "enumerable", "value", "__app_instance_name_map__", "genAppInstanceIdByName", "appName", "globalAppInstanceMap", "validateExportLifecycle", "exports", "_ref", "bootstrap", "mount", "unmount", "Deferred", "_this", "promise", "reject", "supportsUserTiming", "performance", "mark", "clearMarks", "measure", "clearMeasures", "getEntriesByName", "performanceGetEntriesByName", "<PERSON><PERSON><PERSON>", "type", "marks", "performanceMark", "performanceMeasure", "measureName", "isEnableScopedCSS", "sandbox", "strictStyleIsolation", "experimentalStyleIsolation", "getXPathForElement", "el", "body", "contains", "undefined", "xpath", "pos", "tmpEle", "element", "documentElement", "nodeType", "nodeName", "previousSibling", "parentNode", "getContainer", "container", "querySelector", "getContainerXPath", "containerElement"], "sources": ["D:/1_Project/XYD_Project/dop-4.0/dop-4.1-qianduan-unify/dopUnify/node_modules/qiankun/es/utils.js"], "sourcesContent": ["import _typeof from \"@babel/runtime/helpers/esm/typeof\";\nimport _createClass from \"@babel/runtime/helpers/esm/createClass\";\nimport _classCallCheck from \"@babel/runtime/helpers/esm/classCallCheck\";\nimport _isFunction from \"lodash/isFunction\";\nimport _once from \"lodash/once\";\nimport _snakeCase from \"lodash/snakeCase\";\nimport { version } from './version';\nexport function toArray(array) {\n  return Array.isArray(array) ? array : [array];\n}\nexport function sleep(ms) {\n  return new Promise(function (resolve) {\n    return setTimeout(resolve, ms);\n  });\n}\n// Promise.then might be synchronized in Zone.js context, we need to use setTimeout instead to mock next tick.\nvar nextTick = typeof window.Zone === 'function' ? setTimeout : function (cb) {\n  return Promise.resolve().then(cb);\n};\nvar globalTaskPending = false;\n/**\n * Run a callback before next task executing, and the invocation is idempotent in every singular task\n * That means even we called nextTask multi times in one task, only the first callback will be pushed to nextTick to be invoked.\n * @param cb\n */\nexport function nextTask(cb) {\n  if (!globalTaskPending) {\n    globalTaskPending = true;\n    nextTick(function () {\n      cb();\n      globalTaskPending = false;\n    });\n  }\n}\nvar fnRegexCheckCacheMap = new WeakMap();\nexport function isConstructable(fn) {\n  // prototype methods might be changed while code running, so we need check it every time\n  var hasPrototypeMethods = fn.prototype && fn.prototype.constructor === fn && Object.getOwnPropertyNames(fn.prototype).length > 1;\n  if (hasPrototypeMethods) return true;\n  if (fnRegexCheckCacheMap.has(fn)) {\n    return fnRegexCheckCacheMap.get(fn);\n  }\n  /*\n    1. 有 prototype 并且 prototype 上有定义一系列非 constructor 属性\n    2. 函数名大写开头\n    3. class 函数\n    满足其一则可认定为构造函数\n   */\n  var constructable = hasPrototypeMethods;\n  if (!constructable) {\n    // fn.toString has a significant performance overhead, if hasPrototypeMethods check not passed, we will check the function string with regex\n    var fnString = fn.toString();\n    var constructableFunctionRegex = /^function\\b\\s[A-Z].*/;\n    var classRegex = /^class\\b/;\n    constructable = constructableFunctionRegex.test(fnString) || classRegex.test(fnString);\n  }\n  fnRegexCheckCacheMap.set(fn, constructable);\n  return constructable;\n}\n/**\n * in safari\n * typeof document.all === 'undefined' // true\n * typeof document.all === 'function' // true\n * We need to discriminate safari for better performance\n */\nvar naughtySafari = typeof document.all === 'function' && typeof document.all === 'undefined';\nvar callableFnCacheMap = new WeakMap();\nexport function isCallable(fn) {\n  if (callableFnCacheMap.has(fn)) {\n    return true;\n  }\n  var callable = naughtySafari ? typeof fn === 'function' && typeof fn !== 'undefined' : typeof fn === 'function';\n  if (callable) {\n    callableFnCacheMap.set(fn, callable);\n  }\n  return callable;\n}\nvar frozenPropertyCacheMap = new WeakMap();\nexport function isPropertyFrozen(target, p) {\n  if (!target || !p) {\n    return false;\n  }\n  var targetPropertiesFromCache = frozenPropertyCacheMap.get(target) || {};\n  if (targetPropertiesFromCache[p]) {\n    return targetPropertiesFromCache[p];\n  }\n  var propertyDescriptor = Object.getOwnPropertyDescriptor(target, p);\n  var frozen = Boolean(propertyDescriptor && propertyDescriptor.configurable === false && (propertyDescriptor.writable === false || propertyDescriptor.get && !propertyDescriptor.set));\n  targetPropertiesFromCache[p] = frozen;\n  frozenPropertyCacheMap.set(target, targetPropertiesFromCache);\n  return frozen;\n}\nvar boundedMap = new WeakMap();\nexport function isBoundedFunction(fn) {\n  if (boundedMap.has(fn)) {\n    return boundedMap.get(fn);\n  }\n  /*\n   indexOf is faster than startsWith\n   see https://jsperf.com/string-startswith/72\n   */\n  var bounded = fn.name.indexOf('bound ') === 0 && !fn.hasOwnProperty('prototype');\n  boundedMap.set(fn, bounded);\n  return bounded;\n}\nexport var qiankunHeadTagName = 'qiankun-head';\nexport function getDefaultTplWrapper(name) {\n  return function (tpl) {\n    var tplWithSimulatedHead;\n    if (tpl.indexOf('<head>') !== -1) {\n      // We need to mock a head placeholder as native head element will be erased by browser in micro app\n      tplWithSimulatedHead = tpl.replace('<head>', \"<\".concat(qiankunHeadTagName, \">\")).replace('</head>', \"</\".concat(qiankunHeadTagName, \">\"));\n    } else {\n      // Some template might not be a standard html document, thus we need to add a simulated head tag for them\n      tplWithSimulatedHead = \"<\".concat(qiankunHeadTagName, \"></\").concat(qiankunHeadTagName, \">\").concat(tpl);\n    }\n    return \"<div id=\\\"\".concat(getWrapperId(name), \"\\\" data-name=\\\"\").concat(name, \"\\\" data-version=\\\"\").concat(version, \"\\\">\").concat(tplWithSimulatedHead, \"</div>\");\n  };\n}\nexport function getWrapperId(name) {\n  return \"__qiankun_microapp_wrapper_for_\".concat(_snakeCase(name), \"__\");\n}\nexport var nativeGlobal = new Function('return this')();\nvar getGlobalAppInstanceMap = _once(function () {\n  if (!nativeGlobal.hasOwnProperty('__app_instance_name_map__')) {\n    Object.defineProperty(nativeGlobal, '__app_instance_name_map__', {\n      enumerable: false,\n      configurable: true,\n      writable: true,\n      value: {}\n    });\n  }\n  return nativeGlobal.__app_instance_name_map__;\n});\n/**\n * Get app instance name with the auto-increment approach\n * @param appName\n */\nexport var genAppInstanceIdByName = function genAppInstanceIdByName(appName) {\n  var globalAppInstanceMap = getGlobalAppInstanceMap();\n  if (!(appName in globalAppInstanceMap)) {\n    nativeGlobal.__app_instance_name_map__[appName] = 0;\n    return appName;\n  }\n  globalAppInstanceMap[appName]++;\n  return \"\".concat(appName, \"_\").concat(globalAppInstanceMap[appName]);\n};\n/** 校验子应用导出的 生命周期 对象是否正确 */\nexport function validateExportLifecycle(exports) {\n  var _ref = exports !== null && exports !== void 0 ? exports : {},\n    bootstrap = _ref.bootstrap,\n    mount = _ref.mount,\n    unmount = _ref.unmount;\n  return _isFunction(bootstrap) && _isFunction(mount) && _isFunction(unmount);\n}\nexport var Deferred = /*#__PURE__*/_createClass(function Deferred() {\n  var _this = this;\n  _classCallCheck(this, Deferred);\n  this.promise = void 0;\n  this.resolve = void 0;\n  this.reject = void 0;\n  this.promise = new Promise(function (resolve, reject) {\n    _this.resolve = resolve;\n    _this.reject = reject;\n  });\n});\nvar supportsUserTiming = typeof performance !== 'undefined' && typeof performance.mark === 'function' && typeof performance.clearMarks === 'function' && typeof performance.measure === 'function' && typeof performance.clearMeasures === 'function' && typeof performance.getEntriesByName === 'function';\nexport function performanceGetEntriesByName(markName, type) {\n  var marks = null;\n  if (supportsUserTiming) {\n    marks = performance.getEntriesByName(markName, type);\n  }\n  return marks;\n}\nexport function performanceMark(markName) {\n  if (supportsUserTiming) {\n    performance.mark(markName);\n  }\n}\nexport function performanceMeasure(measureName, markName) {\n  if (supportsUserTiming && performance.getEntriesByName(markName, 'mark').length) {\n    performance.measure(measureName, markName);\n    performance.clearMarks(markName);\n    performance.clearMeasures(measureName);\n  }\n}\nexport function isEnableScopedCSS(sandbox) {\n  if (_typeof(sandbox) !== 'object') {\n    return false;\n  }\n  if (sandbox.strictStyleIsolation) {\n    return false;\n  }\n  return !!sandbox.experimentalStyleIsolation;\n}\n/**\n * copy from https://developer.mozilla.org/zh-CN/docs/Using_XPath\n * @param el\n * @param document\n */\nexport function getXPathForElement(el, document) {\n  // not support that if el not existed in document yet(such as it not append to document before it mounted)\n  if (!document.body.contains(el)) {\n    return undefined;\n  }\n  var xpath = '';\n  var pos;\n  var tmpEle;\n  var element = el;\n  while (element !== document.documentElement) {\n    pos = 0;\n    tmpEle = element;\n    while (tmpEle) {\n      if (tmpEle.nodeType === 1 && tmpEle.nodeName === element.nodeName) {\n        // If it is ELEMENT_NODE of the same name\n        pos += 1;\n      }\n      tmpEle = tmpEle.previousSibling;\n    }\n    xpath = \"*[name()='\".concat(element.nodeName, \"'][\").concat(pos, \"]/\").concat(xpath);\n    element = element.parentNode;\n  }\n  xpath = \"/*[name()='\".concat(document.documentElement.nodeName, \"']/\").concat(xpath);\n  xpath = xpath.replace(/\\/$/, '');\n  return xpath;\n}\nexport function getContainer(container) {\n  return typeof container === 'string' ? document.querySelector(container) : container;\n}\nexport function getContainerXPath(container) {\n  if (container) {\n    var containerElement = getContainer(container);\n    if (containerElement) {\n      return getXPathForElement(containerElement, document);\n    }\n  }\n  return undefined;\n}"], "mappings": ";;;;;;;;;;;;AAAA,OAAOA,OAAO,MAAM,mCAAmC;AACvD,OAAOC,YAAY,MAAM,wCAAwC;AACjE,OAAOC,eAAe,MAAM,2CAA2C;AACvE,OAAOC,WAAW,MAAM,mBAAmB;AAC3C,OAAOC,KAAK,MAAM,aAAa;AAC/B,OAAOC,UAAU,MAAM,kBAAkB;AACzC,SAASC,OAAO,QAAQ,WAAW;AACnC,OAAO,SAASC,OAAO,CAACC,KAAK,EAAE;EAC7B,OAAOC,KAAK,CAACC,OAAO,CAACF,KAAK,CAAC,GAAGA,KAAK,GAAG,CAACA,KAAK,CAAC;AAC/C;AACA,OAAO,SAASG,KAAK,CAACC,EAAE,EAAE;EACxB,OAAO,IAAIC,OAAO,CAAC,UAAUC,OAAO,EAAE;IACpC,OAAOC,UAAU,CAACD,OAAO,EAAEF,EAAE,CAAC;EAChC,CAAC,CAAC;AACJ;AACA;AACA,IAAII,QAAQ,GAAG,OAAOC,MAAM,CAACC,IAAI,KAAK,UAAU,GAAGH,UAAU,GAAG,UAAUI,EAAE,EAAE;EAC5E,OAAON,OAAO,CAACC,OAAO,EAAE,CAACM,IAAI,CAACD,EAAE,CAAC;AACnC,CAAC;AACD,IAAIE,iBAAiB,GAAG,KAAK;AAC7B;AACA;AACA;AACA;AACA;AACA,OAAO,SAASC,QAAQ,CAACH,EAAE,EAAE;EAC3B,IAAI,CAACE,iBAAiB,EAAE;IACtBA,iBAAiB,GAAG,IAAI;IACxBL,QAAQ,CAAC,YAAY;MACnBG,EAAE,EAAE;MACJE,iBAAiB,GAAG,KAAK;IAC3B,CAAC,CAAC;EACJ;AACF;AACA,IAAIE,oBAAoB,GAAG,IAAIC,OAAO,EAAE;AACxC,OAAO,SAASC,eAAe,CAACC,EAAE,EAAE;EAClC;EACA,IAAIC,mBAAmB,GAAGD,EAAE,CAACE,SAAS,IAAIF,EAAE,CAACE,SAAS,CAACC,WAAW,KAAKH,EAAE,IAAII,MAAM,CAACC,mBAAmB,CAACL,EAAE,CAACE,SAAS,CAAC,CAACI,MAAM,GAAG,CAAC;EAChI,IAAIL,mBAAmB,EAAE,OAAO,IAAI;EACpC,IAAIJ,oBAAoB,CAACU,GAAG,CAACP,EAAE,CAAC,EAAE;IAChC,OAAOH,oBAAoB,CAACW,GAAG,CAACR,EAAE,CAAC;EACrC;EACA;AACF;AACA;AACA;AACA;AACA;EACE,IAAIS,aAAa,GAAGR,mBAAmB;EACvC,IAAI,CAACQ,aAAa,EAAE;IAClB;IACA,IAAIC,QAAQ,GAAGV,EAAE,CAACW,QAAQ,EAAE;IAC5B,IAAIC,0BAA0B,GAAG,sBAAsB;IACvD,IAAIC,UAAU,GAAG,UAAU;IAC3BJ,aAAa,GAAGG,0BAA0B,CAACE,IAAI,CAACJ,QAAQ,CAAC,IAAIG,UAAU,CAACC,IAAI,CAACJ,QAAQ,CAAC;EACxF;EACAb,oBAAoB,CAACkB,GAAG,CAACf,EAAE,EAAES,aAAa,CAAC;EAC3C,OAAOA,aAAa;AACtB;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAIO,aAAa,GAAG,OAAOC,QAAQ,CAACC,GAAG,KAAK,UAAU,IAAI,OAAOD,QAAQ,CAACC,GAAG,KAAK,WAAW;AAC7F,IAAIC,kBAAkB,GAAG,IAAIrB,OAAO,EAAE;AACtC,OAAO,SAASsB,UAAU,CAACpB,EAAE,EAAE;EAC7B,IAAImB,kBAAkB,CAACZ,GAAG,CAACP,EAAE,CAAC,EAAE;IAC9B,OAAO,IAAI;EACb;EACA,IAAIqB,QAAQ,GAAGL,aAAa,GAAG,OAAOhB,EAAE,KAAK,UAAU,IAAI,OAAOA,EAAE,KAAK,WAAW,GAAG,OAAOA,EAAE,KAAK,UAAU;EAC/G,IAAIqB,QAAQ,EAAE;IACZF,kBAAkB,CAACJ,GAAG,CAACf,EAAE,EAAEqB,QAAQ,CAAC;EACtC;EACA,OAAOA,QAAQ;AACjB;AACA,IAAIC,sBAAsB,GAAG,IAAIxB,OAAO,EAAE;AAC1C,OAAO,SAASyB,gBAAgB,CAACC,MAAM,EAAEC,CAAC,EAAE;EAC1C,IAAI,CAACD,MAAM,IAAI,CAACC,CAAC,EAAE;IACjB,OAAO,KAAK;EACd;EACA,IAAIC,yBAAyB,GAAGJ,sBAAsB,CAACd,GAAG,CAACgB,MAAM,CAAC,IAAI,CAAC,CAAC;EACxE,IAAIE,yBAAyB,CAACD,CAAC,CAAC,EAAE;IAChC,OAAOC,yBAAyB,CAACD,CAAC,CAAC;EACrC;EACA,IAAIE,kBAAkB,GAAGvB,MAAM,CAACwB,wBAAwB,CAACJ,MAAM,EAAEC,CAAC,CAAC;EACnE,IAAII,MAAM,GAAGC,OAAO,CAACH,kBAAkB,IAAIA,kBAAkB,CAACI,YAAY,KAAK,KAAK,KAAKJ,kBAAkB,CAACK,QAAQ,KAAK,KAAK,IAAIL,kBAAkB,CAACnB,GAAG,IAAI,CAACmB,kBAAkB,CAACZ,GAAG,CAAC,CAAC;EACrLW,yBAAyB,CAACD,CAAC,CAAC,GAAGI,MAAM;EACrCP,sBAAsB,CAACP,GAAG,CAACS,MAAM,EAAEE,yBAAyB,CAAC;EAC7D,OAAOG,MAAM;AACf;AACA,IAAII,UAAU,GAAG,IAAInC,OAAO,EAAE;AAC9B,OAAO,SAASoC,iBAAiB,CAAClC,EAAE,EAAE;EACpC,IAAIiC,UAAU,CAAC1B,GAAG,CAACP,EAAE,CAAC,EAAE;IACtB,OAAOiC,UAAU,CAACzB,GAAG,CAACR,EAAE,CAAC;EAC3B;EACA;AACF;AACA;AACA;EACE,IAAImC,OAAO,GAAGnC,EAAE,CAACoC,IAAI,CAACC,OAAO,CAAC,QAAQ,CAAC,KAAK,CAAC,IAAI,CAACrC,EAAE,CAACsC,cAAc,CAAC,WAAW,CAAC;EAChFL,UAAU,CAAClB,GAAG,CAACf,EAAE,EAAEmC,OAAO,CAAC;EAC3B,OAAOA,OAAO;AAChB;AACA,OAAO,IAAII,kBAAkB,GAAG,cAAc;AAC9C,OAAO,SAASC,oBAAoB,CAACJ,IAAI,EAAE;EACzC,OAAO,UAAUK,GAAG,EAAE;IACpB,IAAIC,oBAAoB;IACxB,IAAID,GAAG,CAACJ,OAAO,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC,EAAE;MAChC;MACAK,oBAAoB,GAAGD,GAAG,CAACE,OAAO,CAAC,QAAQ,EAAE,GAAG,CAACC,MAAM,CAACL,kBAAkB,EAAE,GAAG,CAAC,CAAC,CAACI,OAAO,CAAC,SAAS,EAAE,IAAI,CAACC,MAAM,CAACL,kBAAkB,EAAE,GAAG,CAAC,CAAC;IAC5I,CAAC,MAAM;MACL;MACAG,oBAAoB,GAAG,GAAG,CAACE,MAAM,CAACL,kBAAkB,EAAE,KAAK,CAAC,CAACK,MAAM,CAACL,kBAAkB,EAAE,GAAG,CAAC,CAACK,MAAM,CAACH,GAAG,CAAC;IAC1G;IACA,OAAO,YAAY,CAACG,MAAM,CAACC,YAAY,CAACT,IAAI,CAAC,EAAE,iBAAiB,CAAC,CAACQ,MAAM,CAACR,IAAI,EAAE,oBAAoB,CAAC,CAACQ,MAAM,CAAChE,OAAO,EAAE,KAAK,CAAC,CAACgE,MAAM,CAACF,oBAAoB,EAAE,QAAQ,CAAC;EACpK,CAAC;AACH;AACA,OAAO,SAASG,YAAY,CAACT,IAAI,EAAE;EACjC,OAAO,iCAAiC,CAACQ,MAAM,CAACjE,UAAU,CAACyD,IAAI,CAAC,EAAE,IAAI,CAAC;AACzE;AACA,OAAO,IAAIU,YAAY,GAAG,IAAIC,QAAQ,CAAC,aAAa,CAAC,EAAE;AACvD,IAAIC,uBAAuB,GAAGtE,KAAK,CAAC,YAAY;EAC9C,IAAI,CAACoE,YAAY,CAACR,cAAc,CAAC,2BAA2B,CAAC,EAAE;IAC7DlC,MAAM,CAAC6C,cAAc,CAACH,YAAY,EAAE,2BAA2B,EAAE;MAC/DI,UAAU,EAAE,KAAK;MACjBnB,YAAY,EAAE,IAAI;MAClBC,QAAQ,EAAE,IAAI;MACdmB,KAAK,EAAE,CAAC;IACV,CAAC,CAAC;EACJ;EACA,OAAOL,YAAY,CAACM,yBAAyB;AAC/C,CAAC,CAAC;AACF;AACA;AACA;AACA;AACA,OAAO,IAAIC,sBAAsB,GAAG,SAASA,sBAAsB,CAACC,OAAO,EAAE;EAC3E,IAAIC,oBAAoB,GAAGP,uBAAuB,EAAE;EACpD,IAAI,EAAEM,OAAO,IAAIC,oBAAoB,CAAC,EAAE;IACtCT,YAAY,CAACM,yBAAyB,CAACE,OAAO,CAAC,GAAG,CAAC;IACnD,OAAOA,OAAO;EAChB;EACAC,oBAAoB,CAACD,OAAO,CAAC,EAAE;EAC/B,OAAO,EAAE,CAACV,MAAM,CAACU,OAAO,EAAE,GAAG,CAAC,CAACV,MAAM,CAACW,oBAAoB,CAACD,OAAO,CAAC,CAAC;AACtE,CAAC;AACD;AACA,OAAO,SAASE,uBAAuB,CAACC,OAAO,EAAE;EAC/C,IAAIC,IAAI,GAAGD,OAAO,KAAK,IAAI,IAAIA,OAAO,KAAK,KAAK,CAAC,GAAGA,OAAO,GAAG,CAAC,CAAC;IAC9DE,SAAS,GAAGD,IAAI,CAACC,SAAS;IAC1BC,KAAK,GAAGF,IAAI,CAACE,KAAK;IAClBC,OAAO,GAAGH,IAAI,CAACG,OAAO;EACxB,OAAOpF,WAAW,CAACkF,SAAS,CAAC,IAAIlF,WAAW,CAACmF,KAAK,CAAC,IAAInF,WAAW,CAACoF,OAAO,CAAC;AAC7E;AACA,OAAO,IAAIC,QAAQ,GAAG,aAAavF,YAAY,CAAC,SAASuF,QAAQ,GAAG;EAClE,IAAIC,KAAK,GAAG,IAAI;EAChBvF,eAAe,CAAC,IAAI,EAAEsF,QAAQ,CAAC;EAC/B,IAAI,CAACE,OAAO,GAAG,KAAK,CAAC;EACrB,IAAI,CAAC5E,OAAO,GAAG,KAAK,CAAC;EACrB,IAAI,CAAC6E,MAAM,GAAG,KAAK,CAAC;EACpB,IAAI,CAACD,OAAO,GAAG,IAAI7E,OAAO,CAAC,UAAUC,OAAO,EAAE6E,MAAM,EAAE;IACpDF,KAAK,CAAC3E,OAAO,GAAGA,OAAO;IACvB2E,KAAK,CAACE,MAAM,GAAGA,MAAM;EACvB,CAAC,CAAC;AACJ,CAAC,CAAC;AACF,IAAIC,kBAAkB,GAAG,OAAOC,WAAW,KAAK,WAAW,IAAI,OAAOA,WAAW,CAACC,IAAI,KAAK,UAAU,IAAI,OAAOD,WAAW,CAACE,UAAU,KAAK,UAAU,IAAI,OAAOF,WAAW,CAACG,OAAO,KAAK,UAAU,IAAI,OAAOH,WAAW,CAACI,aAAa,KAAK,UAAU,IAAI,OAAOJ,WAAW,CAACK,gBAAgB,KAAK,UAAU;AAC3S,OAAO,SAASC,2BAA2B,CAACC,QAAQ,EAAEC,IAAI,EAAE;EAC1D,IAAIC,KAAK,GAAG,IAAI;EAChB,IAAIV,kBAAkB,EAAE;IACtBU,KAAK,GAAGT,WAAW,CAACK,gBAAgB,CAACE,QAAQ,EAAEC,IAAI,CAAC;EACtD;EACA,OAAOC,KAAK;AACd;AACA,OAAO,SAASC,eAAe,CAACH,QAAQ,EAAE;EACxC,IAAIR,kBAAkB,EAAE;IACtBC,WAAW,CAACC,IAAI,CAACM,QAAQ,CAAC;EAC5B;AACF;AACA,OAAO,SAASI,kBAAkB,CAACC,WAAW,EAAEL,QAAQ,EAAE;EACxD,IAAIR,kBAAkB,IAAIC,WAAW,CAACK,gBAAgB,CAACE,QAAQ,EAAE,MAAM,CAAC,CAACpE,MAAM,EAAE;IAC/E6D,WAAW,CAACG,OAAO,CAACS,WAAW,EAAEL,QAAQ,CAAC;IAC1CP,WAAW,CAACE,UAAU,CAACK,QAAQ,CAAC;IAChCP,WAAW,CAACI,aAAa,CAACQ,WAAW,CAAC;EACxC;AACF;AACA,OAAO,SAASC,iBAAiB,CAACC,OAAO,EAAE;EACzC,IAAI3G,OAAO,CAAC2G,OAAO,CAAC,KAAK,QAAQ,EAAE;IACjC,OAAO,KAAK;EACd;EACA,IAAIA,OAAO,CAACC,oBAAoB,EAAE;IAChC,OAAO,KAAK;EACd;EACA,OAAO,CAAC,CAACD,OAAO,CAACE,0BAA0B;AAC7C;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASC,kBAAkB,CAACC,EAAE,EAAEpE,QAAQ,EAAE;EAC/C;EACA,IAAI,CAACA,QAAQ,CAACqE,IAAI,CAACC,QAAQ,CAACF,EAAE,CAAC,EAAE;IAC/B,OAAOG,SAAS;EAClB;EACA,IAAIC,KAAK,GAAG,EAAE;EACd,IAAIC,GAAG;EACP,IAAIC,MAAM;EACV,IAAIC,OAAO,GAAGP,EAAE;EAChB,OAAOO,OAAO,KAAK3E,QAAQ,CAAC4E,eAAe,EAAE;IAC3CH,GAAG,GAAG,CAAC;IACPC,MAAM,GAAGC,OAAO;IAChB,OAAOD,MAAM,EAAE;MACb,IAAIA,MAAM,CAACG,QAAQ,KAAK,CAAC,IAAIH,MAAM,CAACI,QAAQ,KAAKH,OAAO,CAACG,QAAQ,EAAE;QACjE;QACAL,GAAG,IAAI,CAAC;MACV;MACAC,MAAM,GAAGA,MAAM,CAACK,eAAe;IACjC;IACAP,KAAK,GAAG,YAAY,CAAC7C,MAAM,CAACgD,OAAO,CAACG,QAAQ,EAAE,KAAK,CAAC,CAACnD,MAAM,CAAC8C,GAAG,EAAE,IAAI,CAAC,CAAC9C,MAAM,CAAC6C,KAAK,CAAC;IACpFG,OAAO,GAAGA,OAAO,CAACK,UAAU;EAC9B;EACAR,KAAK,GAAG,aAAa,CAAC7C,MAAM,CAAC3B,QAAQ,CAAC4E,eAAe,CAACE,QAAQ,EAAE,KAAK,CAAC,CAACnD,MAAM,CAAC6C,KAAK,CAAC;EACpFA,KAAK,GAAGA,KAAK,CAAC9C,OAAO,CAAC,KAAK,EAAE,EAAE,CAAC;EAChC,OAAO8C,KAAK;AACd;AACA,OAAO,SAASS,YAAY,CAACC,SAAS,EAAE;EACtC,OAAO,OAAOA,SAAS,KAAK,QAAQ,GAAGlF,QAAQ,CAACmF,aAAa,CAACD,SAAS,CAAC,GAAGA,SAAS;AACtF;AACA,OAAO,SAASE,iBAAiB,CAACF,SAAS,EAAE;EAC3C,IAAIA,SAAS,EAAE;IACb,IAAIG,gBAAgB,GAAGJ,YAAY,CAACC,SAAS,CAAC;IAC9C,IAAIG,gBAAgB,EAAE;MACpB,OAAOlB,kBAAkB,CAACkB,gBAAgB,EAAErF,QAAQ,CAAC;IACvD;EACF;EACA,OAAOuE,SAAS;AAClB"}]}