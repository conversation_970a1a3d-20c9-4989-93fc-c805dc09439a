{"remainingRequest": "D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\node_modules\\thread-loader\\dist\\cjs.js!D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\node_modules\\babel-loader\\lib\\index.js!D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\src\\components\\Dialog\\SunAuditDialog\\index.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\src\\components\\Dialog\\SunAuditDialog\\index.vue", "mtime": 1686019809904}, {"path": "D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\babel.config.js", "mtime": 1686019818359}, {"path": "D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\node_modules\\thread-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1655715099000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, null]}