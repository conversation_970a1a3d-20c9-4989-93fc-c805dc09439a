{"remainingRequest": "D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\node_modules\\thread-loader\\dist\\cjs.js!D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\node_modules\\babel-loader\\lib\\index.js!D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\src\\utils\\calendar.js", "dependencies": [{"path": "D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\src\\utils\\calendar.js", "mtime": 1686019809826}, {"path": "D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\babel.config.js", "mtime": 1686019818359}, {"path": "D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\node_modules\\thread-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, null]}