{"remainingRequest": "D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\node_modules\\babel-loader\\lib\\index.js!D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\node_modules\\eslint-loader\\index.js??ref--13-0!D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\src\\router\\routers\\extend\\index.js", "dependencies": [{"path": "D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\src\\router\\routers\\extend\\index.js", "mtime": 1686019818031}, {"path": "D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\babel.config.js", "mtime": 1686019818359}, {"path": "D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\.babelrc", "mtime": 1705285078668}, {"path": "D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\node_modules\\eslint-loader\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:LyoqDQogKiDlpJbpg6jns7vnu5/mjqXlhaUNCiAqLwoKZXhwb3J0IHZhciBleHRlbmQgPSB7CiAgJy9wYXJlbnRzJzogewogICAgLy8g5LiA57qn6I+c5Y2V5YmN5YqgIOKAmC/igJkKICAgIHRpdGxlOiAn5LiA57qn6I+c5Y2VJywKICAgIG5hbWU6ICdQYXJlbnQnLAogICAgY29tcG9uZW50OiAnL0xheW91dCcKICB9LAogICdwYXJlbnRNZW51JzogewogICAgdGl0bGU6ICfkuoznuqflj4rlhbblroPniLboioLngrknLAogICAgbmFtZTogJ1BhcmVudE1lbnUnLAogICAgY29tcG9uZW50OiAnZXh0ZW5kL3BhcmVudE1lbnUnCiAgfSwKICAnZXh0ZW5kJzogewogICAgdGl0bGU6ICflpJbpg6jmjqXlhaUnLAogICAgbmFtZTogJ0V4dGVuZCcsCiAgICBjb21wb25lbnQ6ICdleHRlbmQnCiAgfSwKICAncWlhbmt1bic6IHsKICAgIHRpdGxlOiAncWlhbmt1buWklumDqOaOpeWFpScsCiAgICBuYW1lOiAnUWlhbmt1bicsCiAgICBjb21wb25lbnQ6ICdleHRlbmQvcWlhbmt1bicKICB9Cn07"}, {"version": 3, "names": ["extend", "title", "name", "component"], "sources": ["D:/1_Project/XYD_Project/dop-4.0/dop-4.1-qianduan-unify/dopUnify/src/router/routers/extend/index.js"], "sourcesContent": ["\r\n/**\r\n * 外部系统接入\r\n */\r\n\r\nexport const extend = {\r\n  '/parents': {\r\n    // 一级菜单前加 ‘/’\r\n    title: '一级菜单',\r\n    name: 'Parent',\r\n    component: '/Layout'\r\n  },\r\n  'parentMenu': {\r\n    title: '二级及其它父节点',\r\n    name: 'ParentMenu',\r\n    component: 'extend/parentMenu'\r\n  },\r\n  'extend': {\r\n    title: '外部接入',\r\n    name: 'Extend',\r\n    component: 'extend'\r\n  },\r\n  'qiankun': {\r\n    title: 'qiankun外部接入',\r\n    name: 'Qianku<PERSON>',\r\n    component: 'extend/qiankun'\r\n  }\r\n}\r\n"], "mappings": "AACA;AACA;AACA;;AAEA,OAAO,IAAMA,MAAM,GAAG;EACpB,UAAU,EAAE;IACV;IACAC,KAAK,EAAE,MAAM;IACbC,IAAI,EAAE,QAAQ;IACdC,SAAS,EAAE;EACb,CAAC;EACD,YAAY,EAAE;IACZF,KAAK,EAAE,UAAU;IACjBC,IAAI,EAAE,YAAY;IAClBC,SAAS,EAAE;EACb,CAAC;EACD,QAAQ,EAAE;IACRF,KAAK,EAAE,MAAM;IACbC,IAAI,EAAE,QAAQ;IACdC,SAAS,EAAE;EACb,CAAC;EACD,SAAS,EAAE;IACTF,KAAK,EAAE,aAAa;IACpBC,IAAI,EAAE,SAAS;IACfC,SAAS,EAAE;EACb;AACF,CAAC"}]}