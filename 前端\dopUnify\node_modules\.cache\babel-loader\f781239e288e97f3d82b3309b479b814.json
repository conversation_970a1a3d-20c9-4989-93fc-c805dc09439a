{"remainingRequest": "D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\node_modules\\thread-loader\\dist\\cjs.js!D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\node_modules\\babel-loader\\lib\\index.js!D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\node_modules\\screenfull\\index.js", "dependencies": [{"path": "D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\node_modules\\screenfull\\index.js", "mtime": 499162500000}, {"path": "D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\babel.config.js", "mtime": 1686019818359}, {"path": "D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\node_modules\\thread-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:aW1wb3J0IF9zbGljZWRUb0FycmF5IGZyb20gIkQ6LzFfUHJvamVjdC9YWURfUHJvamVjdC9kb3AtNC4wL2RvcC00LjEtcWlhbmR1YW4tdW5pZnkvZG9wVW5pZnkvbm9kZV9tb2R1bGVzL0BiYWJlbC9ydW50aW1lL2hlbHBlcnMvZXNtL3NsaWNlZFRvQXJyYXkuanMiOwppbXBvcnQgX2NyZWF0ZUZvck9mSXRlcmF0b3JIZWxwZXIgZnJvbSAiRDovMV9Qcm9qZWN0L1hZRF9Qcm9qZWN0L2RvcC00LjAvZG9wLTQuMS1xaWFuZHVhbi11bmlmeS9kb3BVbmlmeS9ub2RlX21vZHVsZXMvQGJhYmVsL3J1bnRpbWUvaGVscGVycy9lc20vY3JlYXRlRm9yT2ZJdGVyYXRvckhlbHBlci5qcyI7CmltcG9ydCAiY29yZS1qcy9tb2R1bGVzL2VzLm9iamVjdC50by1zdHJpbmcuanMiOwppbXBvcnQgImNvcmUtanMvbW9kdWxlcy93ZWIuZG9tLWNvbGxlY3Rpb25zLml0ZXJhdG9yLmpzIjsKLyogZXNsaW50LWRpc2FibGUgcHJvbWlzZS9wcmVmZXItYXdhaXQtdG8tdGhlbiAqLwoKdmFyIG1ldGhvZE1hcCA9IFtbJ3JlcXVlc3RGdWxsc2NyZWVuJywgJ2V4aXRGdWxsc2NyZWVuJywgJ2Z1bGxzY3JlZW5FbGVtZW50JywgJ2Z1bGxzY3JlZW5FbmFibGVkJywgJ2Z1bGxzY3JlZW5jaGFuZ2UnLCAnZnVsbHNjcmVlbmVycm9yJ10sCi8vIE5ldyBXZWJLaXQKWyd3ZWJraXRSZXF1ZXN0RnVsbHNjcmVlbicsICd3ZWJraXRFeGl0RnVsbHNjcmVlbicsICd3ZWJraXRGdWxsc2NyZWVuRWxlbWVudCcsICd3ZWJraXRGdWxsc2NyZWVuRW5hYmxlZCcsICd3ZWJraXRmdWxsc2NyZWVuY2hhbmdlJywgJ3dlYmtpdGZ1bGxzY3JlZW5lcnJvciddLAovLyBPbGQgV2ViS2l0Clsnd2Via2l0UmVxdWVzdEZ1bGxTY3JlZW4nLCAnd2Via2l0Q2FuY2VsRnVsbFNjcmVlbicsICd3ZWJraXRDdXJyZW50RnVsbFNjcmVlbkVsZW1lbnQnLCAnd2Via2l0Q2FuY2VsRnVsbFNjcmVlbicsICd3ZWJraXRmdWxsc2NyZWVuY2hhbmdlJywgJ3dlYmtpdGZ1bGxzY3JlZW5lcnJvciddLCBbJ21velJlcXVlc3RGdWxsU2NyZWVuJywgJ21vekNhbmNlbEZ1bGxTY3JlZW4nLCAnbW96RnVsbFNjcmVlbkVsZW1lbnQnLCAnbW96RnVsbFNjcmVlbkVuYWJsZWQnLCAnbW96ZnVsbHNjcmVlbmNoYW5nZScsICdtb3pmdWxsc2NyZWVuZXJyb3InXSwgWydtc1JlcXVlc3RGdWxsc2NyZWVuJywgJ21zRXhpdEZ1bGxzY3JlZW4nLCAnbXNGdWxsc2NyZWVuRWxlbWVudCcsICdtc0Z1bGxzY3JlZW5FbmFibGVkJywgJ01TRnVsbHNjcmVlbkNoYW5nZScsICdNU0Z1bGxzY3JlZW5FcnJvciddXTsKdmFyIG5hdGl2ZUFQSSA9IGZ1bmN0aW9uICgpIHsKICBpZiAodHlwZW9mIGRvY3VtZW50ID09PSAndW5kZWZpbmVkJykgewogICAgcmV0dXJuIGZhbHNlOwogIH0KICB2YXIgdW5wcmVmaXhlZE1ldGhvZHMgPSBtZXRob2RNYXBbMF07CiAgdmFyIHJldHVyblZhbHVlID0ge307CiAgdmFyIF9pdGVyYXRvciA9IF9jcmVhdGVGb3JPZkl0ZXJhdG9ySGVscGVyKG1ldGhvZE1hcCksCiAgICBfc3RlcDsKICB0cnkgewogICAgZm9yIChfaXRlcmF0b3IucygpOyAhKF9zdGVwID0gX2l0ZXJhdG9yLm4oKSkuZG9uZTspIHsKICAgICAgdmFyIG1ldGhvZExpc3QgPSBfc3RlcC52YWx1ZTsKICAgICAgdmFyIGV4aXRGdWxsc2NyZWVuTWV0aG9kID0gbWV0aG9kTGlzdCA9PT0gbnVsbCB8fCBtZXRob2RMaXN0ID09PSB2b2lkIDAgPyB2b2lkIDAgOiBtZXRob2RMaXN0WzFdOwogICAgICBpZiAoZXhpdEZ1bGxzY3JlZW5NZXRob2QgaW4gZG9jdW1lbnQpIHsKICAgICAgICB2YXIgX2l0ZXJhdG9yMiA9IF9jcmVhdGVGb3JPZkl0ZXJhdG9ySGVscGVyKG1ldGhvZExpc3QuZW50cmllcygpKSwKICAgICAgICAgIF9zdGVwMjsKICAgICAgICB0cnkgewogICAgICAgICAgZm9yIChfaXRlcmF0b3IyLnMoKTsgIShfc3RlcDIgPSBfaXRlcmF0b3IyLm4oKSkuZG9uZTspIHsKICAgICAgICAgICAgdmFyIF9zdGVwMiR2YWx1ZSA9IF9zbGljZWRUb0FycmF5KF9zdGVwMi52YWx1ZSwgMiksCiAgICAgICAgICAgICAgaW5kZXggPSBfc3RlcDIkdmFsdWVbMF0sCiAgICAgICAgICAgICAgbWV0aG9kID0gX3N0ZXAyJHZhbHVlWzFdOwogICAgICAgICAgICByZXR1cm5WYWx1ZVt1bnByZWZpeGVkTWV0aG9kc1tpbmRleF1dID0gbWV0aG9kOwogICAgICAgICAgfQogICAgICAgIH0gY2F0Y2ggKGVycikgewogICAgICAgICAgX2l0ZXJhdG9yMi5lKGVycik7CiAgICAgICAgfSBmaW5hbGx5IHsKICAgICAgICAgIF9pdGVyYXRvcjIuZigpOwogICAgICAgIH0KICAgICAgICByZXR1cm4gcmV0dXJuVmFsdWU7CiAgICAgIH0KICAgIH0KICB9IGNhdGNoIChlcnIpIHsKICAgIF9pdGVyYXRvci5lKGVycik7CiAgfSBmaW5hbGx5IHsKICAgIF9pdGVyYXRvci5mKCk7CiAgfQogIHJldHVybiBmYWxzZTsKfSgpOwp2YXIgZXZlbnROYW1lTWFwID0gewogIGNoYW5nZTogbmF0aXZlQVBJLmZ1bGxzY3JlZW5jaGFuZ2UsCiAgZXJyb3I6IG5hdGl2ZUFQSS5mdWxsc2NyZWVuZXJyb3IKfTsKCi8vIGVzbGludC1kaXNhYmxlLW5leHQtbGluZSBpbXBvcnQvbm8tbXV0YWJsZS1leHBvcnRzCnZhciBzY3JlZW5mdWxsID0gewogIC8vIGVzbGludC1kaXNhYmxlLW5leHQtbGluZSBkZWZhdWx0LXBhcmFtLWxhc3QKICByZXF1ZXN0OiBmdW5jdGlvbiByZXF1ZXN0KCkgewogICAgdmFyIGVsZW1lbnQgPSBhcmd1bWVudHMubGVuZ3RoID4gMCAmJiBhcmd1bWVudHNbMF0gIT09IHVuZGVmaW5lZCA/IGFyZ3VtZW50c1swXSA6IGRvY3VtZW50LmRvY3VtZW50RWxlbWVudDsKICAgIHZhciBvcHRpb25zID0gYXJndW1lbnRzLmxlbmd0aCA+IDEgPyBhcmd1bWVudHNbMV0gOiB1bmRlZmluZWQ7CiAgICByZXR1cm4gbmV3IFByb21pc2UoZnVuY3Rpb24gKHJlc29sdmUsIHJlamVjdCkgewogICAgICB2YXIgb25GdWxsU2NyZWVuRW50ZXJlZCA9IGZ1bmN0aW9uIG9uRnVsbFNjcmVlbkVudGVyZWQoKSB7CiAgICAgICAgc2NyZWVuZnVsbC5vZmYoJ2NoYW5nZScsIG9uRnVsbFNjcmVlbkVudGVyZWQpOwogICAgICAgIHJlc29sdmUoKTsKICAgICAgfTsKICAgICAgc2NyZWVuZnVsbC5vbignY2hhbmdlJywgb25GdWxsU2NyZWVuRW50ZXJlZCk7CiAgICAgIHZhciByZXR1cm5Qcm9taXNlID0gZWxlbWVudFtuYXRpdmVBUEkucmVxdWVzdEZ1bGxzY3JlZW5dKG9wdGlvbnMpOwogICAgICBpZiAocmV0dXJuUHJvbWlzZSBpbnN0YW5jZW9mIFByb21pc2UpIHsKICAgICAgICByZXR1cm5Qcm9taXNlLnRoZW4ob25GdWxsU2NyZWVuRW50ZXJlZCkuY2F0Y2gocmVqZWN0KTsKICAgICAgfQogICAgfSk7CiAgfSwKICBleGl0OiBmdW5jdGlvbiBleGl0KCkgewogICAgcmV0dXJuIG5ldyBQcm9taXNlKGZ1bmN0aW9uIChyZXNvbHZlLCByZWplY3QpIHsKICAgICAgaWYgKCFzY3JlZW5mdWxsLmlzRnVsbHNjcmVlbikgewogICAgICAgIHJlc29sdmUoKTsKICAgICAgICByZXR1cm47CiAgICAgIH0KICAgICAgdmFyIG9uRnVsbFNjcmVlbkV4aXQgPSBmdW5jdGlvbiBvbkZ1bGxTY3JlZW5FeGl0KCkgewogICAgICAgIHNjcmVlbmZ1bGwub2ZmKCdjaGFuZ2UnLCBvbkZ1bGxTY3JlZW5FeGl0KTsKICAgICAgICByZXNvbHZlKCk7CiAgICAgIH07CiAgICAgIHNjcmVlbmZ1bGwub24oJ2NoYW5nZScsIG9uRnVsbFNjcmVlbkV4aXQpOwogICAgICB2YXIgcmV0dXJuUHJvbWlzZSA9IGRvY3VtZW50W25hdGl2ZUFQSS5leGl0RnVsbHNjcmVlbl0oKTsKICAgICAgaWYgKHJldHVyblByb21pc2UgaW5zdGFuY2VvZiBQcm9taXNlKSB7CiAgICAgICAgcmV0dXJuUHJvbWlzZS50aGVuKG9uRnVsbFNjcmVlbkV4aXQpLmNhdGNoKHJlamVjdCk7CiAgICAgIH0KICAgIH0pOwogIH0sCiAgdG9nZ2xlOiBmdW5jdGlvbiB0b2dnbGUoZWxlbWVudCwgb3B0aW9ucykgewogICAgcmV0dXJuIHNjcmVlbmZ1bGwuaXNGdWxsc2NyZWVuID8gc2NyZWVuZnVsbC5leGl0KCkgOiBzY3JlZW5mdWxsLnJlcXVlc3QoZWxlbWVudCwgb3B0aW9ucyk7CiAgfSwKICBvbmNoYW5nZTogZnVuY3Rpb24gb25jaGFuZ2UoY2FsbGJhY2spIHsKICAgIHNjcmVlbmZ1bGwub24oJ2NoYW5nZScsIGNhbGxiYWNrKTsKICB9LAogIG9uZXJyb3I6IGZ1bmN0aW9uIG9uZXJyb3IoY2FsbGJhY2spIHsKICAgIHNjcmVlbmZ1bGwub24oJ2Vycm9yJywgY2FsbGJhY2spOwogIH0sCiAgb246IGZ1bmN0aW9uIG9uKGV2ZW50LCBjYWxsYmFjaykgewogICAgdmFyIGV2ZW50TmFtZSA9IGV2ZW50TmFtZU1hcFtldmVudF07CiAgICBpZiAoZXZlbnROYW1lKSB7CiAgICAgIGRvY3VtZW50LmFkZEV2ZW50TGlzdGVuZXIoZXZlbnROYW1lLCBjYWxsYmFjaywgZmFsc2UpOwogICAgfQogIH0sCiAgb2ZmOiBmdW5jdGlvbiBvZmYoZXZlbnQsIGNhbGxiYWNrKSB7CiAgICB2YXIgZXZlbnROYW1lID0gZXZlbnROYW1lTWFwW2V2ZW50XTsKICAgIGlmIChldmVudE5hbWUpIHsKICAgICAgZG9jdW1lbnQucmVtb3ZlRXZlbnRMaXN0ZW5lcihldmVudE5hbWUsIGNhbGxiYWNrLCBmYWxzZSk7CiAgICB9CiAgfSwKICByYXc6IG5hdGl2ZUFQSQp9OwpPYmplY3QuZGVmaW5lUHJvcGVydGllcyhzY3JlZW5mdWxsLCB7CiAgaXNGdWxsc2NyZWVuOiB7CiAgICBnZXQ6IGZ1bmN0aW9uIGdldCgpIHsKICAgICAgcmV0dXJuIEJvb2xlYW4oZG9jdW1lbnRbbmF0aXZlQVBJLmZ1bGxzY3JlZW5FbGVtZW50XSk7CiAgICB9CiAgfSwKICBlbGVtZW50OiB7CiAgICBlbnVtZXJhYmxlOiB0cnVlLAogICAgZ2V0OiBmdW5jdGlvbiBnZXQoKSB7CiAgICAgIHZhciBfZG9jdW1lbnQkbmF0aXZlQVBJJGY7CiAgICAgIHJldHVybiAoX2RvY3VtZW50JG5hdGl2ZUFQSSRmID0gZG9jdW1lbnRbbmF0aXZlQVBJLmZ1bGxzY3JlZW5FbGVtZW50XSkgIT09IG51bGwgJiYgX2RvY3VtZW50JG5hdGl2ZUFQSSRmICE9PSB2b2lkIDAgPyBfZG9jdW1lbnQkbmF0aXZlQVBJJGYgOiB1bmRlZmluZWQ7CiAgICB9CiAgfSwKICBpc0VuYWJsZWQ6IHsKICAgIGVudW1lcmFibGU6IHRydWUsCiAgICAvLyBDb2VyY2UgdG8gYm9vbGVhbiBpbiBjYXNlIG9mIG9sZCBXZWJLaXQuCiAgICBnZXQ6IGZ1bmN0aW9uIGdldCgpIHsKICAgICAgcmV0dXJuIEJvb2xlYW4oZG9jdW1lbnRbbmF0aXZlQVBJLmZ1bGxzY3JlZW5FbmFibGVkXSk7CiAgICB9CiAgfQp9KTsKaWYgKCFuYXRpdmVBUEkpIHsKICBzY3JlZW5mdWxsID0gewogICAgaXNFbmFibGVkOiBmYWxzZQogIH07Cn0KZXhwb3J0IGRlZmF1bHQgc2NyZWVuZnVsbDs="}, null]}