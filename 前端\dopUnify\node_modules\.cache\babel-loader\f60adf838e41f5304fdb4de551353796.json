{"remainingRequest": "D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\node_modules\\thread-loader\\dist\\cjs.js!D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\node_modules\\babel-loader\\lib\\index.js!D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\node_modules\\sunui\\srcobf\\components\\SunForm\\selectTree.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\node_modules\\sunui\\srcobf\\components\\SunForm\\selectTree.vue", "mtime": 499162500000}, {"path": "D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\babel.config.js", "mtime": 1686019818359}, {"path": "D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\node_modules\\thread-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1655715099000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, null]}