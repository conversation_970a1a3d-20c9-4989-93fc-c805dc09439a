{"remainingRequest": "E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-q<PERSON><PERSON>n\\数字运营平台-统一门户工程\\dopUnify\\node_modules\\babel-loader\\lib\\index.js!E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan\\数字运营平台-统一门户工程\\dopUnify\\node_modules\\eslint-loader\\index.js??ref--13-0!E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan\\数字运营平台-统一门户工程\\dopUnify\\src\\api\\views\\home\\component\\workbench.js", "dependencies": [{"path": "E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-<PERSON><PERSON><PERSON>n\\数字运营平台-统一门户工程\\dopUnify\\src\\api\\views\\home\\component\\workbench.js", "mtime": 1686019810888}, {"path": "E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-<PERSON><PERSON><PERSON>n\\数字运营平台-统一门户工程\\dopUnify\\babel.config.js", "mtime": 1686019818359}, {"path": "E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-<PERSON><PERSON><PERSON>n\\数字运营平台-统一门户工程\\dopUnify\\.babelrc", "mtime": 1705285078668}, {"path": "E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-<PERSON><PERSON><PERSON>n\\数字运营平台-统一门户工程\\dopUnify\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-<PERSON><PERSON><PERSON>n\\数字运营平台-统一门户工程\\dopUnify\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-<PERSON><PERSON><PERSON>n\\数字运营平台-统一门户工程\\dopUnify\\node_modules\\eslint-loader\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:aW1wb3J0IHJlcXVlc3QgZnJvbSAnQC91dGlscy9yZXF1ZXN0JzsKaW1wb3J0IGRlZmF1bHRTZXR0aW5ncyBmcm9tICdAL3NldHRpbmdzJzsKdmFyIHByZWZpeCA9IGRlZmF1bHRTZXR0aW5ncy5zZXJ2aWNlLnN5c3RlbTsgLy8g5YmN57yA5YWs5YWx6Lev55SxCgpleHBvcnQgdmFyIHdvcmtiZW5jaCA9IHsKICAvKioNCiAgICog5Li76aG1OiDmiJHnmoTlt6XkvZzlj7Dmn6Xor6INCiAgICovCiAgcXVlcnlXb3JrTnVtOiBmdW5jdGlvbiBxdWVyeVdvcmtOdW0oZGF0YSkgewogICAgcmV0dXJuIHJlcXVlc3QoewogICAgICB1cmw6IHByZWZpeCArICcvc3lzdGVtTXNnL2ludGlSZW1pbmQuZG8nLAogICAgICBtZXRob2Q6ICdnZXQnLAogICAgICBwYXJhbXM6IHsKICAgICAgICBtZXNzYWdlOiBkYXRhCiAgICAgIH0KICAgIH0pOwogIH0sCiAgcXVlcnlEYXRlV29yazogZnVuY3Rpb24gcXVlcnlEYXRlV29yayhkYXRhKSB7CiAgICByZXR1cm4gcmVxdWVzdCh7CiAgICAgIHVybDogcHJlZml4ICsgJy9zeXN0ZW1Nc2cvZ2V0Tm90ZUxpc3QuZG8nLAogICAgICBtZXRob2Q6ICdnZXQnLAogICAgICBwYXJhbXM6IHsKICAgICAgICBtZXNzYWdlOiBkYXRhCiAgICAgIH0KICAgIH0pOwogIH0KfTs="}, {"version": 3, "names": ["request", "defaultSettings", "prefix", "service", "system", "workbench", "queryWorkNum", "data", "url", "method", "params", "message", "queryDateWork"], "sources": ["E:/1_Project/XYD_Project/dop-4.0/dop-4.1-q<PERSON><PERSON>n/数字运营平台-统一门户工程/dopUnify/src/api/views/home/<USER>/workbench.js"], "sourcesContent": ["import request from '@/utils/request'\r\nimport defaultSettings from '@/settings'\r\nconst prefix = defaultSettings.service.system // 前缀公共路由\r\n\r\nexport const workbench = {\r\n  /**\r\n   * 主页: 我的工作台查询\r\n   */\r\n  queryWorkNum(data) {\r\n    return request({\r\n      url: prefix + '/systemMsg/intiRemind.do',\r\n      method: 'get',\r\n      params: { message: data }\r\n    })\r\n  },\r\n  queryDateWork(data) {\r\n    return request({\r\n      url: prefix + '/systemMsg/getNoteList.do',\r\n      method: 'get',\r\n      params: { message: data }\r\n    })\r\n  }\r\n}\r\n"], "mappings": "AAAA,OAAOA,OAAO,MAAM,iBAAiB;AACrC,OAAOC,eAAe,MAAM,YAAY;AACxC,IAAMC,MAAM,GAAGD,eAAe,CAACE,OAAO,CAACC,MAAM,EAAC;;AAE9C,OAAO,IAAMC,SAAS,GAAG;EACvB;AACF;AACA;EACEC,YAAY,wBAACC,IAAI,EAAE;IACjB,OAAOP,OAAO,CAAC;MACbQ,GAAG,EAAEN,MAAM,GAAG,0BAA0B;MACxCO,MAAM,EAAE,KAAK;MACbC,MAAM,EAAE;QAAEC,OAAO,EAAEJ;MAAK;IAC1B,CAAC,CAAC;EACJ,CAAC;EACDK,aAAa,yBAACL,IAAI,EAAE;IAClB,OAAOP,OAAO,CAAC;MACbQ,GAAG,EAAEN,MAAM,GAAG,2BAA2B;MACzCO,MAAM,EAAE,KAAK;MACbC,MAAM,EAAE;QAAEC,OAAO,EAAEJ;MAAK;IAC1B,CAAC,CAAC;EACJ;AACF,CAAC"}]}