{"remainingRequest": "E:\\华南区项目\\中国银行\\开发区\\科技公司运营平台\\前端\\dopUnify\\node_modules\\babel-loader\\lib\\index.js!E:\\华南区项目\\中国银行\\开发区\\科技公司运营平台\\前端\\dopUnify\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!E:\\华南区项目\\中国银行\\开发区\\科技公司运营平台\\前端\\dopUnify\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\华南区项目\\中国银行\\开发区\\科技公司运营平台\\前端\\dopUnify\\node_modules\\vue-lottie\\src\\lottie.vue?vue&type=script&lang=js&", "dependencies": [{"path": "E:\\华南区项目\\中国银行\\开发区\\科技公司运营平台\\前端\\dopUnify\\node_modules\\vue-lottie\\src\\lottie.vue", "mtime": 499162500000}, {"path": "E:\\华南区项目\\中国银行\\开发区\\科技公司运营平台\\前端\\dopUnify\\babel.config.js", "mtime": 1721631954662}, {"path": "E:\\华南区项目\\中国银行\\开发区\\科技公司运营平台\\前端\\dopUnify\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "E:\\华南区项目\\中国银行\\开发区\\科技公司运营平台\\前端\\dopUnify\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "E:\\华南区项目\\中国银行\\开发区\\科技公司运营平台\\前端\\dopUnify\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "E:\\华南区项目\\中国银行\\开发区\\科技公司运营平台\\前端\\dopUnify\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1655715099000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:aW1wb3J0ICJjb3JlLWpzL21vZHVsZXMvZXMubnVtYmVyLmNvbnN0cnVjdG9yLmpzIjsKLy8KLy8KLy8KLy8KCmltcG9ydCBsb3R0aWUgZnJvbSAnbG90dGllLXdlYic7CmV4cG9ydCBkZWZhdWx0IHsKICBwcm9wczogewogICAgb3B0aW9uczogewogICAgICB0eXBlOiBPYmplY3QsCiAgICAgIHJlcXVpcmVkOiB0cnVlCiAgICB9LAogICAgaGVpZ2h0OiBOdW1iZXIsCiAgICB3aWR0aDogTnVtYmVyCiAgfSwKICBkYXRhOiBmdW5jdGlvbiBkYXRhKCkgewogICAgcmV0dXJuIHsKICAgICAgc3R5bGU6IHsKICAgICAgICB3aWR0aDogdGhpcy53aWR0aCA/ICIiLmNvbmNhdCh0aGlzLndpZHRoLCAicHgiKSA6ICcxMDAlJywKICAgICAgICBoZWlnaHQ6IHRoaXMuaGVpZ2h0ID8gIiIuY29uY2F0KHRoaXMuaGVpZ2h0LCAicHgiKSA6ICcxMDAlJywKICAgICAgICBvdmVyZmxvdzogJ2hpZGRlbicsCiAgICAgICAgbWFyZ2luOiAnMCBhdXRvJwogICAgICB9CiAgICB9OwogIH0sCiAgbW91bnRlZDogZnVuY3Rpb24gbW91bnRlZCgpIHsKICAgIHRoaXMuYW5pbSA9IGxvdHRpZS5sb2FkQW5pbWF0aW9uKHsKICAgICAgY29udGFpbmVyOiB0aGlzLiRyZWZzLmxhdkNvbnRhaW5lciwKICAgICAgcmVuZGVyZXI6ICdzdmcnLAogICAgICBsb29wOiB0aGlzLm9wdGlvbnMubG9vcCAhPT0gZmFsc2UsCiAgICAgIGF1dG9wbGF5OiB0aGlzLm9wdGlvbnMuYXV0b3BsYXkgIT09IGZhbHNlLAogICAgICBhbmltYXRpb25EYXRhOiB0aGlzLm9wdGlvbnMuYW5pbWF0aW9uRGF0YSwKICAgICAgcmVuZGVyZXJTZXR0aW5nczogdGhpcy5vcHRpb25zLnJlbmRlcmVyU2V0dGluZ3MKICAgIH0pOwogICAgdGhpcy4kZW1pdCgnYW5pbUNyZWF0ZWQnLCB0aGlzLmFuaW0pOwogIH0KfTs="}, {"version": 3, "mappings": ";;;;;;AAKA;AAEA;EACAA;IACAC;MACAC;MACAC;IACA;IACAC;IACAC;EACA;EAEAC;IACA;MACAC;QACAF;QACAD;QACAI;QACAC;MACA;IACA;EACA;EAEAC;IACA;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;IACA,EACA;IACA;EACA;AACA", "names": ["props", "options", "type", "required", "height", "width", "data", "style", "overflow", "margin", "mounted", "container", "renderer", "loop", "autoplay", "animationData", "rendererSettings"], "sourceRoot": "node_modules/vue-lottie/src", "sources": ["lottie.vue"], "sourcesContent": ["<template>\n    <div :style=\"style\" ref=\"lavContainer\"></div>\n</template>\n\n<script>\n  import lottie from 'lottie-web';\n\n  export default {\n    props: {\n      options: {\n        type: Object,\n        required: true\n      },\n      height: Number,\n      width: Number,\n    },\n\n    data () {\n      return {\n        style: {\n          width: this.width ? `${this.width}px` : '100%',\n          height: this.height ? `${this.height}px` : '100%',\n          overflow: 'hidden',\n          margin: '0 auto'\n        }\n      }\n    },\n\n    mounted () {\n      this.anim = lottie.loadAnimation({\n          container: this.$refs.lavContainer,\n          renderer: 'svg',\n          loop: this.options.loop !== false,\n          autoplay: this.options.autoplay !== false,\n          animationData: this.options.animationData,\n          rendererSettings: this.options.rendererSettings\n        }\n      );\n      this.$emit('animCreated', this.anim)\n    }\n  }\n</script>\n"]}]}