{"remainingRequest": "D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\node_modules\\babel-loader\\lib\\index.js!D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\src\\views\\system\\config\\application\\component\\table\\index.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\src\\views\\system\\config\\application\\component\\table\\index.vue", "mtime": 1686019808201}, {"path": "D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\babel.config.js", "mtime": 1686019818359}, {"path": "D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\.babelrc", "mtime": 1705285078668}, {"path": "D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1655715099000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA8DA,SACAA,kBACAC,eACAC,wBACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACAC;EACAC;IAAAC;EAAA;EACAC;EACAC;EACAC;IACAC;MACAC;MACAC;QACA;MACA;IACA;IACAC;MACAF;MACAC;QACA;MACA;IACA;EACA;EACAE;IACA;MACAC;MACAC;QACA;QACAC;QAAA;QACAC;QACAC;QAAA;QACAC;QAAA;QACAC;QACAC;UACAR;UAAA;UACAS;UACAC;QACA;;QACAC;UACAC;UACAC;UAAA;UACAC;QACA;;QACAC;MACA;;MACAC;QACA;QACAC;UACAC;QACA;QACAC;UACAD;QACA;QACAE;UACAF;QACA;MACA;MACAG;QACAb;UACA;UACAc;UACAC;QACA;QACAC;QACAC;UACAC;UACAC;UAAA;UACA/B;YACAgC;YACAC;YACAC;YACAC;YACAC;YACAC;YACAC;YACAC;YACAC;YACAC;UACA;QACA;QACA9B;MACA;;MACA+B;QACAd;QACAe;QAAA;QACAC;QAAA;QACAC;QAAA;QACAC;QAAA;QACAC;QAAA;QACAC;QAAA;QACAC;QAAA;QACAC;MACA;IACA;EACA;;EACAC;IACAxC;MACA;IACA;IACA;MACA;MACAyC;QACA;QACA;UACA;UACA;QACA;UACA;UACA;QACA;MACA;MACAC;IACA;IACA;MACAD;QACA;UACA;UACA;UACA;QACA;UACA;UACA;QACA;MACA;MACA;MACAC;IACA;IACA;MACAD;QACA;UACA;UACA;UACA;QACA;MACA;MACAC;IACA;EACA;EACAC;IACA;EACA;EACAC;IACA,0BACA;EACA;EACAC;IACA;AACA;AACA;AACA;AACA;IACAC;MACA;MACA;QACA;QACA;UACA;UACAC;QACA;QACAC;MACA;MACA;IACA;IACA;IACAC;MACA;MACA;QACAzC;UACA;QACA;MACA;;MACA;IACA;IACA;AACA;IACA0C;MAAA;MACA;MACA;QACAC;QACAC;QACA9C;QACAC;MACA;MACA;MACA8C;QACA;UAAAC;UAAAjD;UAAAC;QACA;QACA;QACA;QACA;MACA;IACA;IAEA;AACA;IACAiD;MACA,6DACA;QACAvC;QACAwC;MAAA,EACA;MACA;IACA;IACA;AACA;IACAC;MAAA;MACA;MACA;QACA5E;QACA;MACA;QACAA;QACA;MACA;MACA,6DACA;QACAmC;QACAwC;MAAA,EACA;MACA;MACA;QACA;QACA,+CACA,IACA,2BACA;MACA;IACA;IACA;AACA;IACAE;MACA;MACA;QACA;MACA;QACA;MACA;IACA;IACA;AACA;IACAC;MAAA;MACA7E;QACA;UACA;UACA,4CACA,2BACA,SACA;UACA;UACA;YACA;YACA;cACAyC;cACAC;cACAC;cACAC;cACAC;cACAE;cACAC;cACAF;cACAP;cACA;cACAuC,eACA,YACA,UACA,YACA,eACA,YACA,YACA,WACA,WACA;cACA;cACAC;gBACAxC;gBACAO;gBACAC;gBACAF;cACA;YACA;YACA;YACA;cACAE;cAAA;cACAC;YAAA,GACA;cACAgC;cACAC;cACAX;YAAA,EACA;UACA;YACA;YACA;YACA;cACAD;gBAEAtB;gBAAA;gBACAC;cAAA,GACA,gCAEA;cACAgC;cACAC;cACAX;YACA;UACA;UACA;UACA,yBACAY;UACA;UACA;UACA;UACA;YACA;UACA;YACA;UACA;QACA;MACA;IACA;IACA;AACA;IACAC;MAAA;MACAnF;QACA;UACA;UACA,4CACA,2BACA,YACA;UACA;UACA;YACA;YACA;cACAyC;cACAC;cACAC;cACAC;cACAC;cACAE;cACAC;cACAF;cACAP;cACA;cACAuC,eACA,YACA,UACA,YACA,eACA,YACA,YACA,WACA,WACA;cACA;cACAC;gBACAxC;gBACAO;gBACAC;gBACAF;cACA;YACA;YACA;YACA;cACAE;cACAC;YAAA,GACA;cACAgC;cACAC;cACAX;YAAA,EACA;UACA;YACA;YACA;YACA;cACAD;gBAEAtB;gBACAC;cAAA,GACA,gCAEA;cACAgC;cACAC;cACAX;YACA;UACA;UAEA;UACA,yBACAY;UACA;UACA;UACA;UACA;YACA;UACA;YACA;UACA;QACA;MACA;IACA;IACA;AACA;IACAE;MAAA;MACA;MACA;QACArF;QACA;MACA;MACAC;QACA;UACA,uCACA,UACA,YACA,YACA,UACA,YACA,aACA;UACA;UACA,4CACA,2BACA,YACA;UACA;UACA;YACA;YACA;cACAyC;cACA;cACAqC;cACA;cACAC;YACA;YACA;cACAM;YACA;UACA;YACA;YACA;YACA;cACAhB;cACAiB;cACAhB;YACA;UACA;UAEA;UACA,yBACAY;UACA;UACA;UACA;UACA;YACA;UACA;YACA;UACA;QACA;MACA;IACA;IACA;IACAK;MACA;IACA;IACA;AACA;AACA;AACA;IACAC;MACA;MACA;MACA;MACA;MACA;QACA;QACA;QACA1F;QACA;MACA;MACA;IACA;IACA;AACA;AACA;IACA2F;MACA;IACA;IACA;AACA;AACA;IACAC;MACA;IACA;IACA;AACA;IACAC;MACA;QAAAlE;MACA;MACA;MACA;IACA;IACA;AACA;IACAmE;MACA;IACA;EACA;AACA", "names": ["commonMsgSuccess", "commonMsgWarn", "commonMsgConfirm", "name", "components", "SunAuditDialog", "filters", "mixins", "props", "defaultForm", "type", "default", "btnAll", "data", "listLoading", "table", "tableColumns", "ref", "selection", "indexNumber", "loading", "componentProps", "height", "formRow", "pageList", "totalNum", "currentPage", "pageSize", "currentRow", "btnDatas", "btnAdd", "show", "btnModify", "btnDelete", "dialog", "width", "title", "visible", "form", "config", "labelWidth", "organ_no", "ser_id", "ser_name", "ser_ip", "ser_port", "ser_content", "ser_type", "is_open", "irp_type", "thread_num", "dialogAudit", "auditType", "show_content_sort", "msg", "applyType", "url", "operType", "menuId", "buttonId", "watch", "handler", "deep", "created", "mounted", "methods", "commonChoices", "jsonObj", "jsonArr", "handleSelectionChange", "queryList", "parameterList", "oper_type", "query", "returnList", "handleAdd", "oprate", "handleModify", "dialogSubmit", "dialogAddSubmit", "content_sort", "data_translation", "oldMsg", "user_no", "defaultSettings", "dialogEditSubmit", "handleDelete", "deleteData_list", "operation_value", "dialogLoading", "dialogAuditSubmit", "changeVisible", "changeAuditVisible", "getList", "showLoading"], "sourceRoot": "src/views/system/config/application/component/table", "sources": ["index.vue"], "sourcesContent": ["<template>\n  <div class=\"sun-content\">\n    <sun-table\n      :table-config=\"table\"\n      @selection-change=\"handleSelectionChange\"\n      @pagination=\"getList\"\n    >\n      <template slot=\"tableColumn\">\n        <el-table-column\n          v-for=\"item in table.tableColumns\"\n          :key=\"item.id\"\n          :prop=\"item.name\"\n          :label=\"item.label\"\n          :width=\"item.width\"\n        >\n          <div slot-scope=\"{ row }\">\n            <span v-if=\"item.name === 'ser_type'\">{{\n              row[item.name] | commonFormatValue('SERVER_TYPE')\n            }}</span>\n            <span v-else-if=\"item.name === 'irp_type'\">{{\n              row[item.name] | commonFormatValue('IRP_TYPE')\n            }}</span>\n            <span v-else-if=\"item.name === 'is_open'\">{{\n              row[item.name] | commonFormatValue('IS_OPEN')\n            }}</span>\n            <span v-else-if=\"item.name === 'last_modi_date'\">{{\n              row[item.name] | dateTimeFormat\n            }}</span>\n            <span v-else-if=\"item.name === 'organ_no'\">{{\n              row[item.name] | organNameFormat\n            }}</span>\n            <span v-else>{{ row[item.name] }}</span>\n          </div>\n        </el-table-column>\n      </template>\n      <template slot=\"customButton\">\n        <sun-button\n          :btn-datas=\"btnDatas\"\n          @handleAdd=\"handleAdd\"\n          @handleModify=\"handleModify\"\n          @handleDelete=\"handleDelete\"\n        />\n        <!--按钮配置-->\n      </template>\n    </sun-table>\n    <div class=\"111\">\n      <sun-form-dialog\n        :dialog-config=\"dialog\"\n        @dialogClose=\"changeVisible\"\n        @dialogSubmit=\"dialogSubmit\"\n      /><!--新增、修改弹出框-->\n    </div>\n\n    <sun-audit-dialog\n      ref=\"refAudit\"\n      :dialog-config=\"dialogAudit\"\n      @changeAuditVisible=\"changeAuditVisible\"\n      @dialogAuditSubmit=\"dialogAuditSubmit\"\n    /><!-- 本地授权或远程审核弹框 -->\n  </div>\n</template>\n<script>\nimport {\n  commonMsgSuccess,\n  commonMsgWarn,\n  commonMsgConfirm\n} from '@/utils/message.js' // 提示信息\nimport { dictionaryGet } from '@/utils/dictionary.js' // 字典常量\nimport { config, configTable } from './info' // 表头、表单配置\nimport { SunAuditDialog } from '@/components'\nimport ResizeMixin from '@/utils/ResizeHandler' // 整体页面是否根据总高配置\nimport { btnAuditType } from '@/utils/flowPath' // 获取按钮审核方式\nimport defaultSettings from '@/settings'\nimport { system } from '@/api'\nconst { query } = system.SysApplication\nexport default {\n  name: 'TableList',\n  components: { SunAuditDialog },\n  filters: {},\n  mixins: [ResizeMixin],\n  props: {\n    defaultForm: {\n      type: Object,\n      default: function() {\n        return {}\n      }\n    },\n    btnAll: {\n      type: Object,\n      default: function() {\n        return {}\n      }\n    }\n  },\n  data() {\n    return {\n      listLoading: false,\n      table: {\n        // 表格配置\n        tableColumns: configTable(), // 表头配置\n        ref: 'tableRef',\n        selection: true, // 复选\n        indexNumber: true, // 序号\n        loading: false,\n        componentProps: {\n          data: [], // 表格数据\n          height: '100px',\n          formRow: 0 // 表单行数\n        },\n        pageList: {\n          totalNum: 0,\n          currentPage: 1, // 当前页\n          pageSize: this.$store.getters.pageSize // 当前页显示条数\n        },\n        currentRow: [] // 选中行\n      },\n      btnDatas: {\n        // 按钮配置\n        btnAdd: {\n          show: this.btnAll.btnAdd\n        },\n        btnModify: {\n          show: this.btnAll.btnModify\n        },\n        btnDelete: {\n          show: this.btnAll.btnDelete\n        }\n      },\n      dialog: {\n        componentProps: {\n          // 弹出框配置属性\n          width: '90rem',\n          title: ''\n        },\n        visible: false,\n        form: {\n          config: config(this),\n          labelWidth: '15rem', // 当前表单标签宽度配置\n          defaultForm: {\n            organ_no: '',\n            ser_id: '',\n            ser_name: '',\n            ser_ip: '',\n            ser_port: null,\n            ser_content: '',\n            ser_type: '',\n            is_open: '',\n            irp_type: '',\n            thread_num: ''\n          }\n        },\n        loading: false // 弹框懒加载\n      },\n      dialogAudit: {\n        visible: false,\n        auditType: '', // 审核方式\n        show_content_sort: '', // 远程审核所需携带的参数  本地审核和不审核参数为''\n        msg: '', // 请求参数\n        applyType: '', // 请求方式， post get delete\n        url: '', // 请求的服务地址(/system/serve/add.do)\n        operType: '', // 操作类型 增删改 远程审核需要 例：删除 'OPERATE_DELETE'\n        menuId: '', // 菜单id\n        buttonId: '' // 按钮id\n      }\n    }\n  },\n  watch: {\n    loading(value) {\n      this.listLoading = this.loading\n    },\n    'dialog.form.defaultForm.ser_type': {\n      // 打开、关闭弹出框\n      handler(val) {\n        // 服务类型为影像服务时   识别方式和识别线程数框出现\n        if (val === '2') {\n          this.dialog.form.config.irp_type.hidden = false\n          this.dialog.form.config.thread_num.hidden = false\n        } else {\n          this.dialog.form.config.irp_type.hidden = true\n          this.dialog.form.config.thread_num.hidden = true\n        }\n      },\n      deep: true\n    },\n    'dialog.componentProps.title': {\n      handler(newName, oldName) {\n        if (newName === '编辑') {\n          // 编辑状态服务ip、服务端口框禁用\n          this.dialog.form.config.ser_ip.componentProps.disabled = true\n          this.dialog.form.config.ser_port.componentProps.disabled = true\n        } else {\n          this.dialog.form.config.ser_ip.componentProps.disabled = false\n          this.dialog.form.config.ser_port.componentProps.disabled = false\n        }\n      },\n      // immediate: true\n      deep: true\n    },\n    'dialog.visible': {\n      handler(val) {\n        if (!val) {\n          // 每次关闭弹出框清空服务类型和影像服务数值\n          this.dialog.form.defaultForm.irp_type = ''\n          this.dialog.form.defaultForm.thread_num = ''\n        }\n      },\n      deep: true\n    }\n  },\n  created() {\n    this.queryList()\n  },\n  mounted() {\n    this.dialogAudit.menuId =\n      this.$route.matched.slice(-1)[0].props.default.menu_id\n  },\n  methods: {\n    /**\n     * 多行数据拼写报文的方法\n     * @param dataArr\t选择行的数组\n     * @param attrArr  放置的参数数组\n     */\n    commonChoices(dataArr, attrArr) {\n      const jsonArr = []\n      for (let i = 0; i < dataArr.length; i++) {\n        const jsonObj = {}\n        for (let j = 0; j < attrArr.length; j++) {\n          const name = attrArr[j]\n          jsonObj[name] = dataArr[i][name]\n        }\n        jsonArr.push(jsonObj)\n      }\n      return jsonArr\n    },\n    // 表格选择多行\n    handleSelectionChange(val) {\n      const currentRow = val\n      if (currentRow.length > 1) {\n        currentRow.sort(function(a, b) {\n          return a.rn - b.rn\n        }) // 选中行排序\n      }\n      this.table.currentRow = val\n    },\n    /**\n     * 按钮：查询*/\n    queryList(currentPage) {\n      this.showLoading(true)\n      const msg = {\n        parameterList: [{ ...this.defaultForm }],\n        oper_type: dictionaryGet('OPERATE_QUERY'),\n        currentPage: currentPage || this.table.pageList.currentPage,\n        pageSize: this.table.pageList.pageSize\n      }\n      // 查询\n      query(msg).then((res) => {\n        const { returnList, totalNum, currentPage } = res.retMap\n        this.table.componentProps.data = returnList\n        this.table.pageList.totalNum = totalNum\n        this.table.pageList.currentPage = currentPage\n        this.showLoading(false)\n      })\n    },\n\n    /**\n     * btn - 新增*/\n    handleAdd() {\n      this.dialog.componentProps = {\n        ...this.dialog.componentProps,\n        title: '新增',\n        oprate: 'add'\n      }\n      this.changeVisible(true)\n    },\n    /**\n     * btn - 编辑*/\n    handleModify() {\n      const rows = this.table.currentRow.length\n      if (rows === 0) {\n        commonMsgWarn('请选择要修改的行', this)\n        return\n      } else if (rows > 1) {\n        commonMsgWarn('请选择一行', this)\n        return\n      }\n      this.dialog.componentProps = {\n        ...this.dialog.componentProps,\n        title: '编辑',\n        oprate: 'edit'\n      } // 添加属性\n      this.changeVisible(true)\n      this.$nextTick(() => {\n        // 弹出框加载完成后赋值、\n        this.dialog.form.defaultForm = Object.assign(\n          {},\n          this.table.currentRow[0]\n        )\n      })\n    },\n    /**\n     * 弹出框 - 确认弹框类型*/\n    dialogSubmit() {\n      const param = this.dialog.componentProps.oprate\n      if (param === 'add') {\n        this.dialogAddSubmit()\n      } else {\n        this.dialogEditSubmit()\n      }\n    },\n    /**\n     * 弹出框 - 确认 - 新增*/\n    dialogAddSubmit() {\n      commonMsgConfirm('是否确认提交当前数据？', this, (param) => {\n        if (param) {\n          // 先从本地获取菜单按钮审核方式  传递子组件\n          this.dialogAudit.auditType = btnAuditType(\n            this.dialogAudit.menuId,\n            'btnAdd'\n          )\n          // 如果是远程审核 需携带以下参数(体现在审核操作详情里)   不审核和本地审核不需要携带\n          if (this.dialogAudit.auditType.check_flag === '2') {\n            // 页面显示排序\n            this.dialogAudit.show_content_sort = {\n              ser_name: '服务名称',\n              ser_ip: '服务ip',\n              ser_port: '服务端口',\n              ser_content: '访问目录',\n              ser_type: '服务类型',\n              irp_type: '识别方式',\n              thread_num: '识别线程数',\n              is_open: '启用标志',\n              organ_no: '所属机构',\n              // 排序标识content_sort  //目前门户没有用到排序\n              content_sort: [\n                'ser_name',\n                'ser_ip',\n                'ser_port',\n                'ser_content',\n                'ser_type',\n                'irp_type',\n                'is_open',\n                'organ_no'\n              ],\n              // 数据转译data_translation\n              data_translation: {\n                organ_no: 'ORGAN_NO',\n                is_open: 'IS_OPEN',\n                irp_type: 'IRP_TYPE',\n                ser_type: 'SERVER_TYPE'\n              }\n            }\n            // 请求参数\n            this.dialogAudit.msg = {\n              irp_type: '', // 识别方式  服务类型为影像识别时有值\n              thread_num: '', // 识别线程数  服务类型为影像识别时有值\n              ...this.dialog.form.defaultForm,\n              oldMsg: {},\n              user_no: this.$store.getters.userNo,\n              oper_type: dictionaryGet('OPERATE_ADD')\n            }\n          } else {\n            this.dialogAudit.show_content_sort = {}\n            // 请求参数\n            this.dialogAudit.msg = {\n              parameterList: [\n                {\n                  irp_type: '', // 识别方式  服务类型为影像识别时有值\n                  thread_num: '', // 识别线程数  服务类型为影像识别时有值\n                  ...this.dialog.form.defaultForm\n                }\n              ],\n              oldMsg: {},\n              user_no: this.$store.getters.userNo,\n              oper_type: dictionaryGet('OPERATE_ADD')\n            }\n          }\n          this.dialogAudit.applyType = 'post' // 请求方式\n          this.dialogAudit.url =\n            defaultSettings.service.system + '/server/add.do' // 请求地址\n          this.dialogAudit.operType = 'OPERATE_ADD' // 必传参数\n          this.dialogAudit.buttonId = 'btnAdd'\n          // 调用子组件的判断审核方式具体操作的方法  如果是不审核直接发请求进行增删改操作，否则审核方式弹框显示\n          if (this.dialogAudit.auditType.check_flag === '0') {\n            this.$refs.refAudit.dialogSubmit()\n          } else {\n            this.$refs.refAudit.dialogShow()\n          }\n        }\n      })\n    },\n    /**\n     * 弹出框 - 确认 - 编辑*/\n    dialogEditSubmit() {\n      commonMsgConfirm('是否确认提交当前数据？', this, (param) => {\n        if (param) {\n          // 先从本地获取菜单按钮审核方式  传递子组件\n          this.dialogAudit.auditType = btnAuditType(\n            this.dialogAudit.menuId,\n            'btnModify'\n          )\n          // 如果是远程审核 需携带以下参数(体现在审核操作详情里)   不审核和本地审核不需要携带\n          if (this.dialogAudit.auditType.check_flag === '2') {\n            // 页面显示排序\n            this.dialogAudit.show_content_sort = {\n              ser_name: '服务名称',\n              ser_ip: '服务ip',\n              ser_port: '服务端口',\n              ser_content: '访问目录',\n              ser_type: '服务类型',\n              irp_type: '识别方式',\n              thread_num: '识别线程数',\n              is_open: '启用标志',\n              organ_no: '所属机构',\n              // 排序标识content_sort  //目前门户没有用到排序\n              content_sort: [\n                'ser_name',\n                'ser_ip',\n                'ser_port',\n                'ser_content',\n                'ser_type',\n                'irp_type',\n                'is_open',\n                'organ_no'\n              ],\n              // 数据转译data_translation\n              data_translation: {\n                organ_no: 'ORGAN_NO',\n                is_open: 'IS_OPEN',\n                irp_type: 'IRP_TYPE',\n                ser_type: 'SERVER_TYPE'\n              }\n            }\n            // 请求参数\n            this.dialogAudit.msg = {\n              irp_type: '',\n              thread_num: '',\n              ...this.dialog.form.defaultForm,\n              oldMsg: { ...this.table.currentRow[0] },\n              user_no: this.$store.getters.userNo,\n              oper_type: dictionaryGet('OPERATE_MODIFY')\n            }\n          } else {\n            this.dialogAudit.show_content_sort = {}\n            // 请求参数\n            this.dialogAudit.msg = {\n              parameterList: [\n                {\n                  irp_type: '',\n                  thread_num: '',\n                  ...this.dialog.form.defaultForm\n                }\n              ],\n              oldMsg: { ...this.table.currentRow[0] },\n              user_no: this.$store.getters.userNo,\n              oper_type: dictionaryGet('OPERATE_MODIFY')\n            }\n          }\n\n          this.dialogAudit.applyType = 'post' // 请求方式\n          this.dialogAudit.url =\n            defaultSettings.service.system + '/server/modify.do' // 请求地址\n          this.dialogAudit.operType = 'OPERATE_MODIFY'\n          this.dialogAudit.buttonId = 'btnModify'\n          // 调用子组件的判断审核方式具体操作的方法  如果是不审核直接发请求进行增删改操作，否则审核方式弹框显示\n          if (this.dialogAudit.auditType.check_flag === '0') {\n            this.$refs.refAudit.dialogSubmit()\n          } else {\n            this.$refs.refAudit.dialogShow()\n          }\n        }\n      })\n    },\n    /**\n     * btn - 删除*/\n    handleDelete() {\n      const rows = this.table.currentRow\n      if (rows.length === 0) {\n        commonMsgWarn('请选择要删除的行', this)\n        return\n      }\n      commonMsgConfirm('是否确认删除当前选中的记录？', this, (param) => {\n        if (param) {\n          const dels = this.commonChoices(rows, [\n            'ser_id',\n            'ser_name',\n            'ser_type',\n            'ser_ip',\n            'irp_type',\n            'thread_num'\n          ])\n          // 先从本地获取菜单按钮审核方式  传递子组件\n          this.dialogAudit.auditType = btnAuditType(\n            this.dialogAudit.menuId,\n            'btnDelete'\n          )\n          // 如果是远程审核 需携带以下参数(体现在审核操作详情里)   不审核和本地审核不需要携带\n          if (this.dialogAudit.auditType.check_flag === '2') {\n            // 页面显示排序\n            this.dialogAudit.show_content_sort = {\n              ser_name: '服务名称',\n              // 排序标识content_sort\n              content_sort: ['ser_name'],\n              // 数据转译data_translation\n              data_translation: {}\n            }\n            this.dialogAudit.msg = {\n              deleteData_list: dels\n            }\n          } else {\n            this.dialogAudit.show_content_sort = {}\n            // 请求参数\n            this.dialogAudit.msg = {\n              parameterList: [],\n              operation_value: dels,\n              oper_type: dictionaryGet('OPERATE_DELETE')\n            }\n          }\n\n          this.dialogAudit.applyType = 'delete' // 请求方式\n          this.dialogAudit.url =\n            defaultSettings.service.system + '/server/delete.do' // 请求地址\n          this.dialogAudit.operType = 'OPERATE_DELETE'\n          this.dialogAudit.buttonId = 'btnDelete'\n          // 调用子组件的判断审核方式具体操作的方法  如果是不审核直接发请求进行增删改操作，否则审核方式弹框显示\n          if (this.dialogAudit.auditType.check_flag === '0') {\n            this.$refs.refAudit.dialogSubmit()\n          } else {\n            this.$refs.refAudit.dialogShow()\n          }\n        }\n      })\n    },\n    // 新增修改弹框loading\n    dialogLoading() {\n      this.dialog.loading = true\n    },\n    /**\n     * 不审核或本地审核 - 确定 子组件调用该方法传递res\n     * @param res-增删改等操作成功的返回结果\n     */\n    dialogAuditSubmit(res) {\n      // 拿到子组件修改请求返回的res,进行接下来的操作  例如调用this.queryList()   showing()等方法\n      // 新增/修改（未增加菜单审核前请求成功后的操作放到这里）\n      this.dialog.loading = false // 新增修改弹框关闭loading\n      this.changeVisible(false) // 修改弹出框关闭\n      if (this.dialogAudit.auditType.check_flag !== '2') {\n        // s是本地审核或者不审核\n        this.showLoading(false)\n        commonMsgSuccess(res.retMsg, this)\n        this.queryList(1)\n      }\n      // 删除请求 或者其他操作请求 if(){}else{}\n    },\n    /**\n     * 新增、修改弹出框 - 关闭\n     * @param {Boolean} param 弹出框显示隐藏配置*/\n    changeVisible(param) {\n      this.dialog.visible = param\n    },\n    /**\n     * 审核弹出框 - 显示、关闭\n     * @param {Boolean} param 弹出框显示隐藏配置*/\n    changeAuditVisible(param) {\n      this.dialogAudit.visible = param\n    },\n    /**\n     *页码更新 */\n    getList(pageParam) {\n      const { currentPage, pageSize } = pageParam\n      this.table.pageList.pageSize = pageSize\n      this.table.pageList.currentPage = currentPage\n      this.queryList()\n    },\n    /**\n     * 加载中动画配置*/\n    showLoading() {\n      this.listLoading = !this.listLoading\n    }\n  }\n}\n</script>\n<style lang=\"scss\" scoped></style>\n"]}]}