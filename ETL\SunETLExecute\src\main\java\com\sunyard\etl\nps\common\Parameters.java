package com.sunyard.etl.nps.common;

import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.io.InputStreamReader;
import java.net.InetAddress;
import java.util.Enumeration;
import java.util.HashMap;
import java.util.Map;
import java.util.Properties;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;





public class Parameters {

	private static Logger logger = LoggerFactory.getLogger(Parameters.class);

	private static final String PARAM_PATH = "config/parameters.properties";

	private static final String TRADE_TYPE_PATH = "config/trade_type.properties";

	public static Map<String, String> tradeMap = new HashMap<String, String>();

	private static final String FILE_ROOT_PATH_PARAM = "fileRootPath";
	private static final String DS_ROOT_PARAM = "dsRoot";
	private static final String FLAG_FILE_SUFFIX_PARAM = "flagFileSuffix";
	private static final String FLOW_CHECK_TIME = "flowCheckTime";
	private static final String FILE_CHECK_TIME = "fileCheckTime";
	private static final String SPLIT_CHAR = "splitChar";
	private static final String LAST_NUM = "lastNum";
	private static final String IS_AUDIT = "isAudit";
	private static final String FS_MACHINE_IP = "fsMachineIp";
	private static final String FS_MACHINE_PORT = "fsMachinePort";
	private static final String IS_DELETE = "isDelete";
	private static final String DELETE_DAYS = "deleteDays";
	private static final String VTM_IMPORT_PERIOD = "vtmImportPeriod";
	private static final String START_DATE = "startDate";
	private static final String ENCODE = "GBK";
	private static final String IMG_BASE_PATH = "IMGBasePath";
	private static final String TEST_IMG_PATH = "testIMGPath";
	private static final String TEST_JPG_PATH = "testJPGPath";
	private static final String ECM_IP = "ECMIP";
	private static final String ECM_PORT = "ECMPort";

	public static String fileRootPath;
	public static String dsRoot;
	public static String flagFileSuffix;
	public static long flowCheckTime;
	public static long fileCheckTime;
	public static String splitChar;
	public static String lastNum;
	public static String isAudit;
	public static String fsMachineIp;
	public static int fsMachinePort;
	public static int isDelete;
	public static int deleteDays;
	public static long vtmImportPeriod;
	public static String startDate;
	public static String rootPath;
	public static String IMGBasePath;
	public static String testIMGPath;
	public static String testJPGPath;
	public static String ECMIP;
	public static int ECMPort;
	public static String ECMUsername;
	public static String ECMpassword;
	public static String simpleNopaperID;
	

	public static final String AUDIT = "1";
	public static final String PROGRESS_FLAG_NEED_PROCESS = "2";
	public static final String PROGRESS_FLAG_NOT_NEED_PROCESS = "11";
	public static final String NEED_PROCESS = "1";
	public static final String NOT_NEED_PROCESS = "0";
	public static final int BATCH_CHECK_FLAG_COMPLETE = 1;
	public static final int BATCH_CHECK_FLAG_INIT = -1;
	public static final String PS_LEVEL_Z = "0";
	public static final int PROCESS_STATE_PSUB = 14;
	public static final int PROCESS_STATE_LAST = 766;

	public static final String VTM_FLAG_INIT = "0";
	public static final String VTM_FLAG_SUCCESS = "1";
	public static final String VTM_FLAG_FAIL = "2";

	public static final String VTM_STATUS_INIT = "0";
	public static final String VTM_STATUS_SUCCESS = "1";
	public static final String VTM_STATUS_FAIL = "2";

	public static final int BUSINESS_ID = 6;
	
	//混合业务增加
	public static final int MIXED_PROCESS_STATE_MIXED = 179;
	public static final String MIXED_FORM_NAME = "VTM一般附件";
	public static final int VTM_INFO_STATUS = 3;//VTM_INFO表中混合业务的标记

	
	// IP
	public static String IP_ADDRESS = null;

	// 后督5.x的批次状态
	// 98-结束
	// 10-等待自动识别
	// 20-等待人工补录
	// 30-等待业务审核
	// 40-等待核实（中心应用不读取）
	public static String ADMS_PROCESS_FLAG = "98";
	public static String ADMS_PROCESS_STATE = "100000";
	public static String IMAGE_FORMAT = "JPG";

	
	
	private static void initParameter(Properties prop) {
		fileRootPath = prop.getProperty(FILE_ROOT_PATH_PARAM);
		dsRoot = prop.getProperty(DS_ROOT_PARAM);
		flagFileSuffix = prop.getProperty(FLAG_FILE_SUFFIX_PARAM);
		fileCheckTime = Long.valueOf(prop.getProperty(FILE_CHECK_TIME));
		flowCheckTime = Long.valueOf(prop.getProperty(FLOW_CHECK_TIME));
		splitChar = prop.getProperty(SPLIT_CHAR);
		lastNum = prop.getProperty(LAST_NUM);
		isAudit = prop.getProperty(IS_AUDIT);
		fsMachineIp = prop.getProperty(FS_MACHINE_IP);
		fsMachinePort = Integer.valueOf(prop.getProperty(FS_MACHINE_PORT));
		isDelete = Integer.valueOf(prop.getProperty(IS_DELETE));
		deleteDays = Integer.valueOf(prop.getProperty(DELETE_DAYS));
		vtmImportPeriod = Long.valueOf(prop.getProperty(VTM_IMPORT_PERIOD));
		startDate = prop.getProperty(START_DATE);
		IMGBasePath = prop.getProperty(IMG_BASE_PATH);
		testIMGPath = prop.getProperty(TEST_IMG_PATH);
		testJPGPath = prop.getProperty(TEST_JPG_PATH);
		ECMIP = prop.getProperty(ECM_IP);
		ECMPort = Integer.valueOf(prop.getProperty(ECM_PORT));
		ECMUsername = prop.getProperty("ECMUsername");
		ECMpassword = prop.getProperty("ECMpassword");
		simpleNopaperID = prop.getProperty("simpleNopaperID");
	}

	private static void initTradeType(Properties prop) {
		Enumeration<?> propertyNames = prop.propertyNames();
		String propertyName;
		String propertyValue;
		while (propertyNames.hasMoreElements()) {
			propertyName = (String) propertyNames.nextElement();
			propertyValue = prop.getProperty(propertyName);
			tradeMap.put(propertyName, propertyValue);
		}
	}

	static {
		Properties properties = new Properties();
		InputStreamReader inputStreamReader = null;
		try {
			rootPath = System.getProperty("root.path");
			if (rootPath == null) {rootPath = System.getProperty("user.dir");}
			inputStreamReader = new InputStreamReader(new FileInputStream(new File(rootPath, PARAM_PATH)), ENCODE);
			properties.load(inputStreamReader);
			initParameter(properties);
			properties.clear();
			
			// 获取IP
			IP_ADDRESS = InetAddress.getLocalHost().getHostAddress().toString();
		} catch (IOException e) {
			logger.error("加载properties文件出错", e);
			throw new RuntimeException("加载properties文件出错", e);
		} finally {
			if (inputStreamReader != null) {
				try {
					inputStreamReader.close();
				} catch (IOException e) {
					logger.error("关闭流失败", e);
				}
			}
		}
	}

}
