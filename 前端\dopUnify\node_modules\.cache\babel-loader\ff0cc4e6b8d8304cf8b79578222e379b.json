{"remainingRequest": "E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-q<PERSON><PERSON>n\\数字运营平台-统一门户工程\\dopUnify\\node_modules\\babel-loader\\lib\\index.js!E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan\\数字运营平台-统一门户工程\\dopUnify\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan\\数字运营平台-统一门户工程\\dopUnify\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan\\数字运营平台-统一门户工程\\dopUnify\\src\\views\\system\\outManage\\permissions\\component\\table\\index.vue?vue&type=script&lang=js&", "dependencies": [{"path": "E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-<PERSON><PERSON><PERSON>n\\数字运营平台-统一门户工程\\dopUnify\\src\\views\\system\\outManage\\permissions\\component\\table\\index.vue", "mtime": 1703583638790}, {"path": "E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-<PERSON><PERSON><PERSON>n\\数字运营平台-统一门户工程\\dopUnify\\babel.config.js", "mtime": 1686019818359}, {"path": "E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-<PERSON><PERSON><PERSON>n\\数字运营平台-统一门户工程\\dopUnify\\.babelrc", "mtime": 1705285078668}, {"path": "E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-<PERSON><PERSON><PERSON>n\\数字运营平台-统一门户工程\\dopUnify\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-<PERSON><PERSON><PERSON>n\\数字运营平台-统一门户工程\\dopUnify\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-<PERSON><PERSON><PERSON>n\\数字运营平台-统一门户工程\\dopUnify\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-<PERSON><PERSON><PERSON>n\\数字运营平台-统一门户工程\\dopUnify\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1655715099000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:aW1wb3J0IF9vYmplY3RTcHJlYWQgZnJvbSAiRTovMV9Qcm9qZWN0L1hZRF9Qcm9qZWN0L2RvcC00LjAvZG9wLTQuMS1xaWFuZHVhbi9cdTY1NzBcdTVCNTdcdThGRDBcdTg0MjVcdTVFNzNcdTUzRjAtXHU3RURGXHU0RTAwXHU5NUU4XHU2MjM3XHU1REU1XHU3QTBCL2RvcFVuaWZ5L25vZGVfbW9kdWxlcy9AYmFiZWwvcnVudGltZS9oZWxwZXJzL2VzbS9vYmplY3RTcHJlYWQyLmpzIjsKaW1wb3J0ICJjb3JlLWpzL21vZHVsZXMvZXMuYXJyYXkuc29ydC5qcyI7CmltcG9ydCAiY29yZS1qcy9tb2R1bGVzL2VzLm9iamVjdC50by1zdHJpbmcuanMiOwppbXBvcnQgImNvcmUtanMvbW9kdWxlcy93ZWIuZG9tLWNvbGxlY3Rpb25zLmZvci1lYWNoLmpzIjsKaW1wb3J0ICJjb3JlLWpzL21vZHVsZXMvZXMuYXJyYXkuY29uY2F0LmpzIjsKaW1wb3J0ICJjb3JlLWpzL21vZHVsZXMvZXMuYXJyYXkubWFwLmpzIjsKLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KCmltcG9ydCB7IGNvbW1vbk1zZ1N1Y2Nlc3MsIGNvbW1vbk1zZ1dhcm4sIGNvbW1vbk1zZ0NvbmZpcm0gfSBmcm9tICdAL3V0aWxzL21lc3NhZ2UuanMnOyAvLyDmj5DnpLrkv6Hmga8KCmltcG9ydCB7IGNvbmZpZywgY29uZmlnVGFibGUgfSBmcm9tICcuL2luZm8nOyAvLyDooajlpLTjgIHooajljZXphY3nva4KaW1wb3J0IFJlc2l6ZU1peGluIGZyb20gJ0AvdXRpbHMvUmVzaXplSGFuZGxlcic7IC8vIOaVtOS9k+mhtemdouaYr+WQpuagueaNruaAu+mrmOmFjee9rgoKaW1wb3J0IHsgc3lzdGVtIH0gZnJvbSAnQC9hcGknOwp2YXIgX3N5c3RlbSRTeXNPdXRQZXJtID0gc3lzdGVtLlN5c091dFBlcm0sCiAgcXVlcnkgPSBfc3lzdGVtJFN5c091dFBlcm0ucXVlcnksCiAgYWRkID0gX3N5c3RlbSRTeXNPdXRQZXJtLmFkZCwKICBtb2RpZnkgPSBfc3lzdGVtJFN5c091dFBlcm0ubW9kaWZ5LAogIGRlbCA9IF9zeXN0ZW0kU3lzT3V0UGVybS5kZWw7CnZhciB0aW1vdXQxOwpleHBvcnQgZGVmYXVsdCB7CiAgbmFtZTogJ1RhYmxlTGlzdCcsCiAgbWl4aW5zOiBbUmVzaXplTWl4aW5dLAogIHByb3BzOiB7CiAgICBkZWZhdWx0Rm9ybTogewogICAgICB0eXBlOiBPYmplY3QsCiAgICAgIGRlZmF1bHQ6IGZ1bmN0aW9uIF9kZWZhdWx0KCkgewogICAgICAgIHJldHVybiB7fTsKICAgICAgfQogICAgfSwKICAgIGJ0bkFsbDogewogICAgICB0eXBlOiBPYmplY3QsCiAgICAgIGRlZmF1bHQ6IGZ1bmN0aW9uIF9kZWZhdWx0KCkgewogICAgICAgIHJldHVybiB7fTsKICAgICAgfQogICAgfQogIH0sCiAgZGF0YTogZnVuY3Rpb24gZGF0YSgpIHsKICAgIHJldHVybiB7CiAgICAgIGxpc3RMb2FkaW5nOiBmYWxzZSwKICAgICAgdGFibGU6IHsKICAgICAgICAvLyDooajmoLzphY3nva4KICAgICAgICB0YWJsZUNvbHVtbnM6IGNvbmZpZ1RhYmxlKCksCiAgICAgICAgLy8g6KGo5aS06YWN572uCiAgICAgICAgcmVmOiAndGFibGVSZWYnLAogICAgICAgIHNlbGVjdGlvbjogdHJ1ZSwKICAgICAgICAvLyDlpI3pgIkKICAgICAgICBpbmRleE51bWJlcjogdHJ1ZSwKICAgICAgICAvLyDluo/lj7cKICAgICAgICBsb2FkaW5nOiBmYWxzZSwKICAgICAgICBjb21wb25lbnRQcm9wczogewogICAgICAgICAgZGF0YTogW10sCiAgICAgICAgICAvLyDooajmoLzmlbDmja4KICAgICAgICAgIGhlaWdodDogJzM3MHB4JywKICAgICAgICAgIGZvcm1Sb3c6IDEgLy8g6KGo5Y2V6KGM5pWwCiAgICAgICAgfSwKCiAgICAgICAgcGFnZUxpc3Q6IHsKICAgICAgICAgIHRvdGFsTnVtOiAwLAogICAgICAgICAgY3VycmVudFBhZ2U6IDEsCiAgICAgICAgICAvLyDlvZPliY3pobUKICAgICAgICAgIHBhZ2VTaXplOiB0aGlzLiRzdG9yZS5nZXR0ZXJzLnBhZ2VTaXplIC8vIOW9k+WJjemhteaYvuekuuadoeaVsAogICAgICAgIH0sCgogICAgICAgIGN1cnJlbnRSb3c6IFtdIC8vIOmAieS4reihjAogICAgICB9LAoKICAgICAgYnRuRGF0YXM6IHsKICAgICAgICAvLyDmjInpkq7phY3nva4KICAgICAgICBidG5BZGQ6IHsKICAgICAgICAgIHNob3c6IHRoaXMuYnRuQWxsLmJ0bkFkZAogICAgICAgIH0sCiAgICAgICAgYnRuTW9kaWZ5OiB7CiAgICAgICAgICBzaG93OiB0aGlzLmJ0bkFsbC5idG5Nb2RpZnkKICAgICAgICB9LAogICAgICAgIGJ0bkRlbGV0ZTogewogICAgICAgICAgc2hvdzogdGhpcy5idG5BbGwuYnRuRGVsZXRlCiAgICAgICAgfQogICAgICB9LAogICAgICBkaWFsb2c6IHsKICAgICAgICBjb21wb25lbnRQcm9wczogewogICAgICAgICAgLy8g5by55Ye65qGG6YWN572u5bGe5oCnCiAgICAgICAgICB3aWR0aDogJzgwcmVtJywKICAgICAgICAgIHRpdGxlOiAnJwogICAgICAgIH0sCiAgICAgICAgdmlzaWJsZTogZmFsc2UsCiAgICAgICAgZm9ybTogewogICAgICAgICAgY29uZmlnOiBjb25maWcodGhpcyksCiAgICAgICAgICBsYWJlbFdpZHRoOiAnMTVyZW0nLAogICAgICAgICAgLy8g5b2T5YmN6KGo5Y2V5qCH562+5a695bqm6YWN572uCiAgICAgICAgICBkZWZhdWx0Rm9ybTogewogICAgICAgICAgICBzeXNfaWQ6ICcnLAogICAgICAgICAgICB0ZF9ubzogJycsCiAgICAgICAgICAgIGlzX29wZW46ICcnCiAgICAgICAgICB9CiAgICAgICAgfQogICAgICB9CiAgICB9OwogIH0sCiAgd2F0Y2g6IHsKICAgIGxvYWRpbmc6IGZ1bmN0aW9uIGxvYWRpbmcodmFsdWUpIHsKICAgICAgdGhpcy5saXN0TG9hZGluZyA9IHRoaXMubG9hZGluZzsKICAgIH0sCiAgICAnZGlhbG9nLmNvbXBvbmVudFByb3BzLnRpdGxlJzogewogICAgICBoYW5kbGVyOiBmdW5jdGlvbiBoYW5kbGVyKG5ld05hbWUsIG9sZE5hbWUpIHsKICAgICAgICBpZiAobmV3TmFtZSA9PT0gJ+e8lui+kScpIHsKICAgICAgICAgIC8vIOe8lui+keeKtuaAgeaVsOaNrua6kElE5qGG56aB55SoCiAgICAgICAgICB0aGlzLmRpYWxvZy5mb3JtLmNvbmZpZy5zeXNfaWQuY29tcG9uZW50UHJvcHMuZGlzYWJsZWQgPSB0cnVlOwogICAgICAgICAgdGhpcy5kaWFsb2cuZm9ybS5jb25maWcudGRfbm8uY29tcG9uZW50UHJvcHMuZGlzYWJsZWQgPSB0cnVlOwogICAgICAgIH0gZWxzZSB7CiAgICAgICAgICB0aGlzLmRpYWxvZy5mb3JtLmNvbmZpZy5zeXNfaWQuY29tcG9uZW50UHJvcHMuZGlzYWJsZWQgPSBmYWxzZTsKICAgICAgICAgIHRoaXMuZGlhbG9nLmZvcm0uY29uZmlnLnRkX25vLmNvbXBvbmVudFByb3BzLmRpc2FibGVkID0gZmFsc2U7CiAgICAgICAgfQogICAgICB9LAogICAgICAvLyBpbW1lZGlhdGU6IHRydWUKICAgICAgZGVlcDogdHJ1ZQogICAgfQogIH0sCiAgY3JlYXRlZDogZnVuY3Rpb24gY3JlYXRlZCgpIHt9LAogIGJlZm9yZURlc3Ryb3k6IGZ1bmN0aW9uIGJlZm9yZURlc3Ryb3koKSB7CiAgICBjbGVhclRpbWVvdXQodGltb3V0MSk7CiAgfSwKICBtb3VudGVkOiBmdW5jdGlvbiBtb3VudGVkKCkgewogICAgdmFyIF90aGlzID0gdGhpczsKICAgIHRoaXMuJG5leHRUaWNrKGZ1bmN0aW9uICgpIHsKICAgICAgX3RoaXMuc3lzT3B0aW9ucygpOyAvLyDojrflj5blpJbooajmlbDmja7mupDmlbDmja4g57O757uf5qCH6K+GICDmjqXlj6PmoIfor4YKICAgICAgLy8gdGhpcy5xdWVyeUxpc3QoKQogICAgfSk7CiAgfSwKCiAgbWV0aG9kczogewogICAgLyoqDQogICAgICog5aSa6KGM5pWw5o2u5ou85YaZ5oql5paH55qE5pa55rOVDQogICAgICogQHBhcmFtIGRhdGFBcnIJ6YCJ5oup6KGM55qE5pWw57uEDQogICAgICogQHBhcmFtIGF0dHJBcnIgIOaUvue9rueahOWPguaVsOaVsOe7hA0KICAgICAqLwogICAgY29tbW9uQ2hvaWNlczogZnVuY3Rpb24gY29tbW9uQ2hvaWNlcyhkYXRhQXJyLCBhdHRyQXJyKSB7CiAgICAgIHZhciBqc29uQXJyID0gW107CiAgICAgIGZvciAodmFyIGkgPSAwOyBpIDwgZGF0YUFyci5sZW5ndGg7IGkrKykgewogICAgICAgIHZhciBqc29uT2JqID0ge307CiAgICAgICAgZm9yICh2YXIgaiA9IDA7IGogPCBhdHRyQXJyLmxlbmd0aDsgaisrKSB7CiAgICAgICAgICB2YXIgbmFtZSA9IGF0dHJBcnJbal07CiAgICAgICAgICBqc29uT2JqW25hbWVdID0gZGF0YUFycltpXVtuYW1lXTsKICAgICAgICB9CiAgICAgICAganNvbkFyci5wdXNoKGpzb25PYmopOwogICAgICB9CiAgICAgIHJldHVybiBqc29uQXJyOwogICAgfSwKICAgIC8vIOihqOagvOmAieaLqeWkmuihjAogICAgaGFuZGxlU2VsZWN0aW9uQ2hhbmdlOiBmdW5jdGlvbiBoYW5kbGVTZWxlY3Rpb25DaGFuZ2UodmFsKSB7CiAgICAgIHZhciBjdXJyZW50Um93ID0gdmFsOwogICAgICBpZiAoY3VycmVudFJvdy5sZW5ndGggPiAxKSB7CiAgICAgICAgY3VycmVudFJvdy5zb3J0KGZ1bmN0aW9uIChhLCBiKSB7CiAgICAgICAgICByZXR1cm4gYS5ybiAtIGIucm47CiAgICAgICAgfSk7IC8vIOmAieS4reihjOaOkuW6jwogICAgICB9CgogICAgICB0aGlzLnRhYmxlLmN1cnJlbnRSb3cgPSB2YWw7CiAgICB9LAogICAgLyoqDQogICAgICog5oyJ6ZKu77ya5p+l6K+iKi8KICAgIHF1ZXJ5TGlzdDogZnVuY3Rpb24gcXVlcnlMaXN0KGN1cnJlbnRQYWdlKSB7CiAgICAgIHZhciBfdGhpczIgPSB0aGlzOwogICAgICB0aGlzLnNob3dMb2FkaW5nKHRydWUpOwogICAgICB2YXIgbXNnID0gewogICAgICAgIHBhcmFtZXRlckxpc3Q6IFtfb2JqZWN0U3ByZWFkKHt9LCB0aGlzLmRlZmF1bHRGb3JtKV0sCiAgICAgICAgY3VycmVudFBhZ2U6IGN1cnJlbnRQYWdlIHx8IHRoaXMudGFibGUucGFnZUxpc3QuY3VycmVudFBhZ2UsCiAgICAgICAgcGFnZVNpemU6IHRoaXMudGFibGUucGFnZUxpc3QucGFnZVNpemUKICAgICAgfTsKICAgICAgLy8g5p+l6K+iCiAgICAgIHF1ZXJ5KG1zZykudGhlbihmdW5jdGlvbiAocmVzKSB7CiAgICAgICAgdmFyIF9yZXMkcmV0TWFwID0gcmVzLnJldE1hcCwKICAgICAgICAgIHJldHVybkxpc3QgPSBfcmVzJHJldE1hcC5yZXR1cm5MaXN0LAogICAgICAgICAgdG90YWxOdW0gPSBfcmVzJHJldE1hcC50b3RhbE51bSwKICAgICAgICAgIGN1cnJlbnRQYWdlID0gX3JlcyRyZXRNYXAuY3VycmVudFBhZ2U7CiAgICAgICAgX3RoaXMyLnRhYmxlLmNvbXBvbmVudFByb3BzLmRhdGEgPSByZXR1cm5MaXN0OwogICAgICAgIF90aGlzMi50YWJsZS5wYWdlTGlzdC50b3RhbE51bSA9IHRvdGFsTnVtOwogICAgICAgIF90aGlzMi50YWJsZS5wYWdlTGlzdC5jdXJyZW50UGFnZSA9IGN1cnJlbnRQYWdlOwogICAgICAgIC8vIOagvOW8j+WMluezu+e7n+agh+ivhuWSjOaOpeWPo+agh+ivhgogICAgICAgIF90aGlzMi50YWJsZS5jb21wb25lbnRQcm9wcy5kYXRhLmZvckVhY2goZnVuY3Rpb24gKGl0ZW0pIHsKICAgICAgICAgIF90aGlzMi4kc3RvcmUuZ2V0dGVycy5leHRlcm5hbERhdGEuQUxMX1NZU19JRC5mb3JFYWNoKGZ1bmN0aW9uIChpdGVtMikgewogICAgICAgICAgICBpZiAoaXRlbTIudmFsdWUgPT09IGl0ZW0uc3lzX2lkKSB7CiAgICAgICAgICAgICAgaXRlbS5zeXNfaWQgPSAiIi5jb25jYXQoaXRlbTIudmFsdWUsICItIikuY29uY2F0KGl0ZW0yLmxhYmVsKTsKICAgICAgICAgICAgfQogICAgICAgICAgfSk7CiAgICAgICAgICBfdGhpczIuJHN0b3JlLmdldHRlcnMuZXh0ZXJuYWxEYXRhLlREX05PLmZvckVhY2goZnVuY3Rpb24gKGl0ZW0yKSB7CiAgICAgICAgICAgIGlmIChpdGVtMi52YWx1ZSA9PT0gaXRlbS50ZF9ubykgewogICAgICAgICAgICAgIGl0ZW0udGRfbm8gPSAiIi5jb25jYXQoaXRlbTIudmFsdWUsICItIikuY29uY2F0KGl0ZW0yLmxhYmVsKTsKICAgICAgICAgICAgfQogICAgICAgICAgfSk7CiAgICAgICAgfSk7CiAgICAgICAgX3RoaXMyLnNob3dMb2FkaW5nKGZhbHNlKTsKICAgICAgfSk7CiAgICB9LAogICAgLyoqDQogICAgICog5by55Ye65qGGIC0g5YWz6ZetDQogICAgICogQHBhcmFtIHtCb29sZWFufSBwYXJhbSDlvLnlh7rmoYbmmL7npLrpmpDol4/phY3nva4qLwogICAgY2hhbmdlVmlzaWJsZTogZnVuY3Rpb24gY2hhbmdlVmlzaWJsZShwYXJhbSkgewogICAgICB0aGlzLmRpYWxvZy52aXNpYmxlID0gcGFyYW07CiAgICB9LAogICAgLyoqDQogICAgICogYnRuIC0g5paw5aKeKi8KICAgIGhhbmRsZUFkZDogZnVuY3Rpb24gaGFuZGxlQWRkKCkgewogICAgICB0aGlzLmRpYWxvZy5jb21wb25lbnRQcm9wcyA9IF9vYmplY3RTcHJlYWQoX29iamVjdFNwcmVhZCh7fSwgdGhpcy5kaWFsb2cuY29tcG9uZW50UHJvcHMpLCB7fSwgewogICAgICAgIHRpdGxlOiAn5paw5aKeJywKICAgICAgICBvcHJhdGU6ICdhZGQnCiAgICAgIH0pOwogICAgICB0aGlzLmNoYW5nZVZpc2libGUodHJ1ZSk7CiAgICB9LAogICAgLyoqDQogICAgICogYnRuIC0g57yW6L6RKi8KICAgIGhhbmRsZU1vZGlmeTogZnVuY3Rpb24gaGFuZGxlTW9kaWZ5KCkgewogICAgICB2YXIgX3RoaXMzID0gdGhpczsKICAgICAgdmFyIHJvd3MgPSB0aGlzLnRhYmxlLmN1cnJlbnRSb3cubGVuZ3RoOwogICAgICBpZiAocm93cyA9PT0gMCkgewogICAgICAgIGNvbW1vbk1zZ1dhcm4oJ+ivt+mAieaLqeimgeS/ruaUueeahOihjCcsIHRoaXMpOwogICAgICAgIHJldHVybjsKICAgICAgfSBlbHNlIGlmIChyb3dzID4gMSkgewogICAgICAgIGNvbW1vbk1zZ1dhcm4oJ+ivt+mAieaLqeS4gOihjCcsIHRoaXMpOwogICAgICAgIHJldHVybjsKICAgICAgfQogICAgICB0aGlzLmRpYWxvZy5jb21wb25lbnRQcm9wcyA9IF9vYmplY3RTcHJlYWQoX29iamVjdFNwcmVhZCh7fSwgdGhpcy5kaWFsb2cuY29tcG9uZW50UHJvcHMpLCB7fSwgewogICAgICAgIHRpdGxlOiAn57yW6L6RJywKICAgICAgICBvcHJhdGU6ICdlZGl0JwogICAgICB9KTsgLy8g5re75Yqg5bGe5oCnCiAgICAgIHRoaXMuY2hhbmdlVmlzaWJsZSh0cnVlKTsKICAgICAgdGhpcy4kbmV4dFRpY2soZnVuY3Rpb24gKCkgewogICAgICAgIC8vIOW8ueWHuuahhuWKoOi9veWujOaIkOWQjui1i+WAvOOAgQogICAgICAgIF90aGlzMy5kaWFsb2cuZm9ybS5kZWZhdWx0Rm9ybSA9IE9iamVjdC5hc3NpZ24oe30sIF90aGlzMy50YWJsZS5jdXJyZW50Um93WzBdKTsKICAgICAgfSk7CiAgICB9LAogICAgLyoqDQogICAgICog5by55Ye65qGGIC0g56Gu6K6k5by55qGG57G75Z6LKi8KICAgIGRpYWxvZ1N1Ym1pdDogZnVuY3Rpb24gZGlhbG9nU3VibWl0KCkgewogICAgICB2YXIgcGFyYW0gPSB0aGlzLmRpYWxvZy5jb21wb25lbnRQcm9wcy5vcHJhdGU7CiAgICAgIGlmIChwYXJhbSA9PT0gJ2FkZCcpIHsKICAgICAgICB0aGlzLmRpYWxvZ0FkZFN1Ym1pdCgpOwogICAgICB9IGVsc2UgewogICAgICAgIHRoaXMuZGlhbG9nRWRpdFN1Ym1pdCgpOwogICAgICB9CiAgICB9LAogICAgLyoqDQogICAgICog5by55Ye65qGGIC0g56Gu6K6kIC0g5paw5aKeKi8KICAgIGRpYWxvZ0FkZFN1Ym1pdDogZnVuY3Rpb24gZGlhbG9nQWRkU3VibWl0KCkgewogICAgICB2YXIgX3RoaXM0ID0gdGhpczsKICAgICAgdGhpcy5zaG93TG9hZGluZyh0cnVlKTsKICAgICAgdmFyIG1zZyA9IHsKICAgICAgICBwYXJhbWV0ZXJMaXN0OiBbX29iamVjdFNwcmVhZChfb2JqZWN0U3ByZWFkKHt9LCB0aGlzLmRpYWxvZy5mb3JtLmRlZmF1bHRGb3JtKSwge30sIHsKICAgICAgICAgIGlzX2xvY2s6IHRoaXMuJHN0b3JlLmdldHRlcnMudXNlck5vLAogICAgICAgICAgYmVmb3JlX2RhdGE6IHt9LAogICAgICAgICAgdXNlcl9ubzogdGhpcy4kc3RvcmUuZ2V0dGVycy51c2VyTm8KICAgICAgICB9KV0sCiAgICAgICAgYmVmb3JlX2RhdGE6IHt9LAogICAgICAgIHVzZXJfbm86IHRoaXMuJHN0b3JlLmdldHRlcnMudXNlck5vLAogICAgICAgIG9wZXJhdGlvbl91c2VyOiB7CiAgICAgICAgICBvcmdhbl9ubzogdGhpcy4kc3RvcmUuZ2V0dGVycy5vcmdhbk5vLAogICAgICAgICAgdXNlcl9ubzogdGhpcy4kc3RvcmUuZ2V0dGVycy51c2VyTm8KICAgICAgICB9CiAgICAgIH07CiAgICAgIGFkZChtc2cpLnRoZW4oZnVuY3Rpb24gKHJlcykgewogICAgICAgIGNvbW1vbk1zZ1N1Y2Nlc3MoJ+aWsOWinuaIkOWKnycsIF90aGlzNCk7CiAgICAgICAgX3RoaXM0LnNob3dMb2FkaW5nKGZhbHNlKTsKICAgICAgICBfdGhpczQucXVlcnlMaXN0KCk7CiAgICAgIH0pOwogICAgICB0aGlzLmNoYW5nZVZpc2libGUoZmFsc2UpOyAvLyDlvLnlh7rmoYblhbPpl60KICAgIH0sCiAgICAvKioNCiAgICAgKiDlvLnlh7rmoYYgLSDnoa7orqQgLSDnvJbovpEqLwogICAgZGlhbG9nRWRpdFN1Ym1pdDogZnVuY3Rpb24gZGlhbG9nRWRpdFN1Ym1pdCgpIHsKICAgICAgdmFyIF90aGlzNSA9IHRoaXM7CiAgICAgIGNvbW1vbk1zZ0NvbmZpcm0oJ+aYr+WQpuehruiupOaPkOS6pOW9k+WJjeaVsOaNru+8nycsIHRoaXMsIGZ1bmN0aW9uIChwYXJhbSkgewogICAgICAgIGlmIChwYXJhbSkgewogICAgICAgICAgX3RoaXM1LnNob3dMb2FkaW5nKHRydWUpOwogICAgICAgICAgdmFyIG1zZyA9IHsKICAgICAgICAgICAgcGFyYW1ldGVyTGlzdDogW3sKICAgICAgICAgICAgICBzeXNfaWQ6IF90aGlzNS5kaWFsb2cuZm9ybS5kZWZhdWx0Rm9ybS5zeXNfaWQuc3BsaXQoJy0nKVswXSwKICAgICAgICAgICAgICB0ZF9ubzogX3RoaXM1LmRpYWxvZy5mb3JtLmRlZmF1bHRGb3JtLnRkX25vLnNwbGl0KCctJylbMF0sCiAgICAgICAgICAgICAgaXNfb3BlbjogX3RoaXM1LmRpYWxvZy5mb3JtLmRlZmF1bHRGb3JtLmlzX29wZW4uc3BsaXQoJy0nKVswXSwKICAgICAgICAgICAgICBpc19sb2NrOiBfdGhpczUuJHN0b3JlLmdldHRlcnMudXNlck5vLAogICAgICAgICAgICAgIGJlZm9yZV9kYXRhOiBfb2JqZWN0U3ByZWFkKF9vYmplY3RTcHJlYWQoe30sIF90aGlzNS50YWJsZS5jdXJyZW50Um93WzBdKSwge30sIHsKICAgICAgICAgICAgICAgIHN5c19pZDogX3RoaXM1LnRhYmxlLmN1cnJlbnRSb3dbMF0uc3lzX2lkLnNwbGl0KCctJylbMF0sCiAgICAgICAgICAgICAgICB0ZF9ubzogX3RoaXM1LnRhYmxlLmN1cnJlbnRSb3dbMF0udGRfbm8uc3BsaXQoJy0nKVswXQogICAgICAgICAgICAgIH0pLAogICAgICAgICAgICAgIHVzZXJfbm86IF90aGlzNS4kc3RvcmUuZ2V0dGVycy51c2VyTm8KICAgICAgICAgICAgfV0sCiAgICAgICAgICAgIGJlZm9yZV9kYXRhOiBfb2JqZWN0U3ByZWFkKF9vYmplY3RTcHJlYWQoe30sIF90aGlzNS50YWJsZS5jdXJyZW50Um93WzBdKSwge30sIHsKICAgICAgICAgICAgICBzeXNfaWQ6IF90aGlzNS50YWJsZS5jdXJyZW50Um93WzBdLnN5c19pZC5zcGxpdCgnLScpWzBdLAogICAgICAgICAgICAgIHRkX25vOiBfdGhpczUudGFibGUuY3VycmVudFJvd1swXS50ZF9uby5zcGxpdCgnLScpWzBdCiAgICAgICAgICAgIH0pLAogICAgICAgICAgICB1c2VyX25vOiBfdGhpczUuJHN0b3JlLmdldHRlcnMudXNlck5vLAogICAgICAgICAgICBvcGVyYXRpb25fdXNlcjogewogICAgICAgICAgICAgIG9yZ2FuX25vOiBfdGhpczUuJHN0b3JlLmdldHRlcnMub3JnYW5ObywKICAgICAgICAgICAgICB1c2VyX25vOiBfdGhpczUuJHN0b3JlLmdldHRlcnMudXNlck5vCiAgICAgICAgICAgIH0KICAgICAgICAgIH07CiAgICAgICAgICAvLyDkv67mlLkKICAgICAgICAgIG1vZGlmeShtc2cpLnRoZW4oZnVuY3Rpb24gKHJlcykgewogICAgICAgICAgICBjb21tb25Nc2dTdWNjZXNzKCfkv67mlLnmiJDlip8nLCBfdGhpczUpOwogICAgICAgICAgICBfdGhpczUuc2hvd0xvYWRpbmcoZmFsc2UpOwogICAgICAgICAgICBfdGhpczUucXVlcnlMaXN0KCk7CiAgICAgICAgICB9KS5jYXRjaChmdW5jdGlvbiAoKSB7CiAgICAgICAgICAgIF90aGlzNS5zaG93TG9hZGluZyhmYWxzZSk7CiAgICAgICAgICB9KTsKICAgICAgICAgIF90aGlzNS5jaGFuZ2VWaXNpYmxlKGZhbHNlKTsgLy8g5by55Ye65qGG5YWz6ZetCiAgICAgICAgfQogICAgICB9KTsKICAgIH0sCiAgICAvKioNCiAgICAgKiBidG4gLSDliKDpmaQqLwogICAgaGFuZGxlRGVsZXRlOiBmdW5jdGlvbiBoYW5kbGVEZWxldGUoKSB7CiAgICAgIHZhciBfdGhpczYgPSB0aGlzOwogICAgICB2YXIgcm93cyA9IHRoaXMudGFibGUuY3VycmVudFJvdzsKICAgICAgaWYgKHJvd3MubGVuZ3RoID09PSAwKSB7CiAgICAgICAgY29tbW9uTXNnV2Fybign6K+36YCJ5oup6KaB5Yig6Zmk55qE6KGMJywgdGhpcyk7CiAgICAgICAgcmV0dXJuOwogICAgICB9CiAgICAgIGNvbW1vbk1zZ0NvbmZpcm0oJ+aYr+WQpuehruiupOWIoOmZpOW9k+WJjemAieS4reeahOiusOW9le+8nycsIHRoaXMsIGZ1bmN0aW9uIChwYXJhbSkgewogICAgICAgIGlmIChwYXJhbSkgewogICAgICAgICAgdmFyIGRlbHMgPSBbXTsgLy8g6YCJ5Lit5Yig6Zmk55qE5pWw57uECiAgICAgICAgICBfdGhpczYudGFibGUuY3VycmVudFJvdy5mb3JFYWNoKGZ1bmN0aW9uIChpdGVtKSB7CiAgICAgICAgICAgIGRlbHMucHVzaCh7CiAgICAgICAgICAgICAgc3lzX2lkOiBpdGVtLnN5c19pZC5zcGxpdCgnLScpWzBdLAogICAgICAgICAgICAgIHRkX25vOiBpdGVtLnRkX25vLnNwbGl0KCctJylbMF0sCiAgICAgICAgICAgICAgaXNfb3BlbjogaXRlbS5pc19vcGVuLAogICAgICAgICAgICAgIGlzX2xvY2s6IGl0ZW0uaXNfbG9jawogICAgICAgICAgICB9KTsKICAgICAgICAgIH0pOwogICAgICAgICAgdmFyIG1zZyA9IHsKICAgICAgICAgICAgb3BlcmF0aW9uX3ZhbHVlOiBkZWxzLAogICAgICAgICAgICBwYXJhbWV0ZXJMaXN0OiBkZWxzLAogICAgICAgICAgICBpc19sb2NrOiBfdGhpczYuJHN0b3JlLmdldHRlcnMudXNlck5vLAogICAgICAgICAgICBvcGVyYXRpb25fdXNlcjogewogICAgICAgICAgICAgIG9yZ2FuX25vOiBfdGhpczYuJHN0b3JlLmdldHRlcnMub3JnYW5ObywKICAgICAgICAgICAgICB1c2VyX25vOiBfdGhpczYuJHN0b3JlLmdldHRlcnMudXNlck5vCiAgICAgICAgICAgIH0KICAgICAgICAgIH07CiAgICAgICAgICBkZWwobXNnKS50aGVuKGZ1bmN0aW9uIChyZXMpIHsKICAgICAgICAgICAgY29tbW9uTXNnU3VjY2Vzcygn5Yig6Zmk5oiQ5YqfJywgX3RoaXM2KTsKICAgICAgICAgICAgX3RoaXM2LnF1ZXJ5TGlzdCgxKTsKICAgICAgICAgIH0pLmNhdGNoKGZ1bmN0aW9uIChlKSB7CiAgICAgICAgICAgIC8vIGNvbnNvbGUubG9nKGUpCiAgICAgICAgICB9KTsKICAgICAgICB9CiAgICAgIH0pOwogICAgfSwKICAgIC8qKg0KICAgICAqIOWkhOeQhuS4i+aLieahhuezu+e7n+agh+ivhuWSjOaOpeWPo+agh+ivhuaVsOaNriovCiAgICBzeXNPcHRpb25zOiBmdW5jdGlvbiBzeXNPcHRpb25zKCkgewogICAgICB2YXIgX3RoaXM3ID0gdGhpczsKICAgICAgdGltb3V0MSA9IHNldFRpbWVvdXQoZnVuY3Rpb24gKCkgewogICAgICAgIHZhciBhcnJheVN5c0lkID0gX3RoaXM3LiRzdG9yZS5nZXR0ZXJzLmV4dGVybmFsRGF0YS5BTExfU1lTX0lEOwogICAgICAgIHZhciBzeXNpZEFycmF5ID0gW107CiAgICAgICAgdmFyIGFycmF5VGRObyA9IF90aGlzNy4kc3RvcmUuZ2V0dGVycy5leHRlcm5hbERhdGEuVERfTk87CiAgICAgICAgdmFyIHRkbm9BcnJheSA9IFtdOwogICAgICAgIGFycmF5U3lzSWQubWFwKGZ1bmN0aW9uIChpdGVtKSB7CiAgICAgICAgICB2YXIgdmFsdWVTID0gT2JqZWN0LmFzc2lnbih7fSwgaXRlbSwgewogICAgICAgICAgICBsYWJlbDogaXRlbS52YWx1ZSArICctJyArIGl0ZW0ubGFiZWwKICAgICAgICAgIH0pOwogICAgICAgICAgc3lzaWRBcnJheS5wdXNoKHZhbHVlUyk7CiAgICAgICAgfSk7CiAgICAgICAgYXJyYXlUZE5vLm1hcChmdW5jdGlvbiAoaXRlbSkgewogICAgICAgICAgdmFyIHZhbHVlUzIgPSBPYmplY3QuYXNzaWduKHt9LCBpdGVtLCB7CiAgICAgICAgICAgIGxhYmVsOiBpdGVtLnZhbHVlICsgJy0nICsgaXRlbS5sYWJlbAogICAgICAgICAgfSk7CiAgICAgICAgICB0ZG5vQXJyYXkucHVzaCh2YWx1ZVMyKTsKICAgICAgICB9KTsKICAgICAgICAvLyDojrflj5blpJbpg6jlrZflhbgKICAgICAgICBfdGhpczcuZGlhbG9nLmZvcm0uY29uZmlnLnN5c19pZC5vcHRpb25zID0gc3lzaWRBcnJheTsKICAgICAgICBfdGhpczcuZGlhbG9nLmZvcm0uY29uZmlnLnRkX25vLm9wdGlvbnMgPSB0ZG5vQXJyYXk7CiAgICAgIH0sIDEwMDApOwogICAgfSwKICAgIC8qKg0KICAgICAq6aG156CB5pu05pawICovCiAgICBnZXRMaXN0OiBmdW5jdGlvbiBnZXRMaXN0KHBhZ2VQYXJhbSkgewogICAgICB2YXIgY3VycmVudFBhZ2UgPSBwYWdlUGFyYW0uY3VycmVudFBhZ2UsCiAgICAgICAgcGFnZVNpemUgPSBwYWdlUGFyYW0ucGFnZVNpemU7CiAgICAgIHRoaXMudGFibGUucGFnZUxpc3QucGFnZVNpemUgPSBwYWdlU2l6ZTsKICAgICAgdGhpcy50YWJsZS5wYWdlTGlzdC5jdXJyZW50UGFnZSA9IGN1cnJlbnRQYWdlOwogICAgICB0aGlzLnF1ZXJ5TGlzdCgpOwogICAgfSwKICAgIC8qKg0KICAgICAqIOWKoOi9veS4reWKqOeUu+mFjee9riovCiAgICBzaG93TG9hZGluZzogZnVuY3Rpb24gc2hvd0xvYWRpbmcoKSB7CiAgICAgIHRoaXMubGlzdExvYWRpbmcgPSAhdGhpcy5saXN0TG9hZGluZzsKICAgIH0KICB9Cn07"}, {"version": 3, "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA4CA,SACAA,kBACAC,eACAC,wBACA;;AAEA;AACA;;AAEA;AACA;EAAAC;EAAAC;EAAAC;EAAAC;AACA;AACA;EACAC;EACAC;EACAC;IACAC;MACAC;MACAC;QACA;MACA;IACA;IACAC;MACAF;MACAC;QACA;MACA;IACA;EACA;EACAE;IACA;MACAC;MACAC;QACA;QACAC;QAAA;QACAC;QACAC;QAAA;QACAC;QAAA;QACAC;QACAC;UACAR;UAAA;UACAS;UACAC;QACA;;QACAC;UACAC;UACAC;UAAA;UACAC;QACA;;QACAC;MACA;;MACAC;QACA;QACAC;UACAC;QACA;QACAC;UACAD;QACA;QACAE;UACAF;QACA;MACA;MACAG;QACAb;UACA;UACAc;UACAC;QACA;QACAC;QACAC;UACAC;UACAC;UAAA;UACA/B;YACAgC;YACAC;YACAC;UACA;QACA;MACA;IACA;EACA;EACAC;IACAxB;MACA;IACA;IACA;MACAyB;QACA;UACA;UACA;UACA;QACA;UACA;UACA;QACA;MACA;MACA;MACAC;IACA;EACA;EACAC;EACAC;IACAC;EACA;EACAC;IAAA;IACA;MACA;MACA;IACA;EACA;;EACAC;IACA;AACA;AACA;AACA;AACA;IACAC;MACA;MACA;QACA;QACA;UACA;UACAC;QACA;QACAC;MACA;MACA;IACA;IACA;IACAC;MACA;MACA;QACA3B;UACA;QACA;MACA;;MACA;IACA;IACA;AACA;IACA4B;MAAA;MACA;MACA;QACAC;QACA/B;QACAC;MACA;MACA;MACAzB;QACA;UAAAwD;UAAAjC;UAAAC;QACA;QACA;QACA;QACA;QACA;UACA;YACA;cACAiC;YACA;UACA;UACA;YACA;cACAA;YACA;UACA;QACA;QAEA;MACA;IACA;IACA;AACA;AACA;IACAC;MACA;IACA;IACA;AACA;IACAC;MACA,6DACA;QACAzB;QACA0B;MAAA,EACA;MACA;IACA;IACA;AACA;IACAC;MAAA;MACA;MACA;QACA/D;QACA;MACA;QACAA;QACA;MACA;MACA,6DACA;QACAoC;QACA0B;MAAA,EACA;MACA;MACA;QACA;QACA,+CACA,IACA,2BACA;MACA;IACA;IAEA;AACA;IACAE;MACA;MACA;QACA;MACA;QACA;MACA;IACA;IACA;AACA;IACAC;MAAA;MACA;MACA;QACAR,gDAEA;UACAS;UACAC;UACAC;QAAA,GAEA;QACAD;QACAC;QACAC;UACAC;UACAF;QACA;MACA;MACAjE;QACAJ;QACA;QACA;MACA;MACA;IACA;IAEA;AACA;IACAwE;MAAA;MACAtE;QACA;UACA;UACA;YACAwD,gBACA;cACAhB;cACAC;cACAC;cACAuB;cACAC,6CACA;gBACA1B;gBACAC;cAAA,EACA;cACA0B;YACA,EACA;YACAD,6CACA;cACA1B;cACAC;YAAA,EACA;YACA0B;YACAC;cACAC;cACAF;YACA;UACA;UACA;UACAhE,YACAoE;YACAzE;YACA;YACA;UACA,GACA0E;YACA;UACA;UACA;QACA;MACA;IACA;IACA;AACA;IACAC;MAAA;MACA;MACA;QACA1E;QACA;MACA;MACAC;QACA;UACA;UACA;YACA0E;cACAlC;cACAC;cACAC;cACAuB;YACA;UACA;UACA;YACAU;YACAnB;YACAS;YACAG;cACAC;cACAF;YACA;UACA;UACA/D,SACAmE;YACAzE;YACA;UACA,GACA0E;YACA;UAAA,CACA;QACA;MACA;IACA;IACA;AACA;IACAI;MAAA;MACAC;QACA;QACA;QACA;QACA;QACAC;UACA;YACAC;UACA;UACAC;QACA;QACAC;UACA;YACAF;UACA;UACAG;QACA;QACA;QACA;QACA;MACA;IACA;IACA;AACA;IACAC;MACA;QAAAzD;MACA;MACA;MACA;IACA;IACA;AACA;IACA0D;MACA;IACA;EACA;AACA", "names": ["commonMsgSuccess", "commonMsgWarn", "commonMsgConfirm", "query", "add", "modify", "del", "name", "mixins", "props", "defaultForm", "type", "default", "btnAll", "data", "listLoading", "table", "tableColumns", "ref", "selection", "indexNumber", "loading", "componentProps", "height", "formRow", "pageList", "totalNum", "currentPage", "pageSize", "currentRow", "btnDatas", "btnAdd", "show", "btnModify", "btnDelete", "dialog", "width", "title", "visible", "form", "config", "labelWidth", "sys_id", "td_no", "is_open", "watch", "handler", "deep", "created", "<PERSON><PERSON><PERSON><PERSON>", "clearTimeout", "mounted", "methods", "commonChoices", "jsonObj", "jsonArr", "handleSelectionChange", "queryList", "parameterList", "returnList", "item", "changeVisible", "handleAdd", "oprate", "handleModify", "dialogSubmit", "dialogAddSubmit", "is_lock", "before_data", "user_no", "operation_user", "organ_no", "dialogEditSubmit", "then", "catch", "handleDelete", "dels", "operation_value", "sysOptions", "timout1", "arraySysId", "label", "sysidArray", "arrayTdNo", "tdnoArray", "getList", "showLoading"], "sourceRoot": "src/views/system/outManage/permissions/component/table", "sources": ["index.vue"], "sourcesContent": ["<template>\r\n  <div class=\"sun-content\">\r\n    <sun-table\r\n      :table-config=\"table\"\r\n      @selection-change=\"handleSelectionChange\"\r\n      @pagination=\"getList\"\r\n    >\r\n      <template slot=\"tableColumn\">\r\n        <el-table-column\r\n          v-for=\"item in table.tableColumns\"\r\n          :key=\"item.id\"\r\n          :prop=\"item.name\"\r\n          :label=\"item.label\"\r\n          :width=\"item.width\"\r\n        >\r\n          <div slot-scope=\"{ row }\">\r\n            <span v-if=\"item.name === 'last_modi_date'\">{{\r\n              row[item.name] | dateTimeFormat\r\n            }}</span>\r\n            <span v-else-if=\"item.name === 'is_open'\">{{\r\n              row[item.name] | commonFormatValue('IS_OPEN')\r\n            }}</span>\r\n            <span v-else>{{ row[item.name] }}</span>\r\n          </div>\r\n        </el-table-column>\r\n      </template>\r\n      <template slot=\"customButton\">\r\n        <sun-button\r\n          :btn-datas=\"btnDatas\"\r\n          @handleAdd=\"handleAdd\"\r\n          @handleModify=\"handleModify\"\r\n          @handleDelete=\"handleDelete\"\r\n        />\r\n        <!--按钮配置-->\r\n      </template>\r\n    </sun-table>\r\n    <sun-form-dialog\r\n      :dialog-config=\"dialog\"\r\n      @dialogClose=\"changeVisible\"\r\n      @dialogSubmit=\"dialogSubmit\"\r\n    /><!--新增、修改弹出框-->\r\n  </div>\r\n</template>\r\n<script>\r\nimport {\r\n  commonMsgSuccess,\r\n  commonMsgWarn,\r\n  commonMsgConfirm\r\n} from '@/utils/message.js' // 提示信息\r\n\r\nimport { config, configTable } from './info' // 表头、表单配置\r\nimport ResizeMixin from '@/utils/ResizeHandler' // 整体页面是否根据总高配置\r\n\r\nimport { system } from '@/api'\r\nconst { query, add, modify, del } = system.SysOutPerm\r\nlet timout1\r\nexport default {\r\n  name: 'TableList',\r\n  mixins: [ResizeMixin],\r\n  props: {\r\n    defaultForm: {\r\n      type: Object,\r\n      default: function() {\r\n        return {}\r\n      }\r\n    },\r\n    btnAll: {\r\n      type: Object,\r\n      default: function() {\r\n        return {}\r\n      }\r\n    }\r\n  },\r\n  data() {\r\n    return {\r\n      listLoading: false,\r\n      table: {\r\n        // 表格配置\r\n        tableColumns: configTable(), // 表头配置\r\n        ref: 'tableRef',\r\n        selection: true, // 复选\r\n        indexNumber: true, // 序号\r\n        loading: false,\r\n        componentProps: {\r\n          data: [], // 表格数据\r\n          height: '370px',\r\n          formRow: 1 // 表单行数\r\n        },\r\n        pageList: {\r\n          totalNum: 0,\r\n          currentPage: 1, // 当前页\r\n          pageSize: this.$store.getters.pageSize // 当前页显示条数\r\n        },\r\n        currentRow: [] // 选中行\r\n      },\r\n      btnDatas: {\r\n        // 按钮配置\r\n        btnAdd: {\r\n          show: this.btnAll.btnAdd\r\n        },\r\n        btnModify: {\r\n          show: this.btnAll.btnModify\r\n        },\r\n        btnDelete: {\r\n          show: this.btnAll.btnDelete\r\n        }\r\n      },\r\n      dialog: {\r\n        componentProps: {\r\n          // 弹出框配置属性\r\n          width: '80rem',\r\n          title: ''\r\n        },\r\n        visible: false,\r\n        form: {\r\n          config: config(this),\r\n          labelWidth: '15rem', // 当前表单标签宽度配置\r\n          defaultForm: {\r\n            sys_id: '',\r\n            td_no: '',\r\n            is_open: ''\r\n          }\r\n        }\r\n      }\r\n    }\r\n  },\r\n  watch: {\r\n    loading(value) {\r\n      this.listLoading = this.loading\r\n    },\r\n    'dialog.componentProps.title': {\r\n      handler(newName, oldName) {\r\n        if (newName === '编辑') {\r\n          // 编辑状态数据源ID框禁用\r\n          this.dialog.form.config.sys_id.componentProps.disabled = true\r\n          this.dialog.form.config.td_no.componentProps.disabled = true\r\n        } else {\r\n          this.dialog.form.config.sys_id.componentProps.disabled = false\r\n          this.dialog.form.config.td_no.componentProps.disabled = false\r\n        }\r\n      },\r\n      // immediate: true\r\n      deep: true\r\n    }\r\n  },\r\n  created() {},\r\n  beforeDestroy() {\r\n    clearTimeout(timout1)\r\n  },\r\n  mounted() {\r\n    this.$nextTick(() => {\r\n      this.sysOptions() // 获取外表数据源数据 系统标识  接口标识\r\n      // this.queryList()\r\n    })\r\n  },\r\n  methods: {\r\n    /**\r\n     * 多行数据拼写报文的方法\r\n     * @param dataArr\t选择行的数组\r\n     * @param attrArr  放置的参数数组\r\n     */\r\n    commonChoices(dataArr, attrArr) {\r\n      const jsonArr = []\r\n      for (let i = 0; i < dataArr.length; i++) {\r\n        const jsonObj = {}\r\n        for (let j = 0; j < attrArr.length; j++) {\r\n          const name = attrArr[j]\r\n          jsonObj[name] = dataArr[i][name]\r\n        }\r\n        jsonArr.push(jsonObj)\r\n      }\r\n      return jsonArr\r\n    },\r\n    // 表格选择多行\r\n    handleSelectionChange(val) {\r\n      const currentRow = val\r\n      if (currentRow.length > 1) {\r\n        currentRow.sort(function(a, b) {\r\n          return a.rn - b.rn\r\n        }) // 选中行排序\r\n      }\r\n      this.table.currentRow = val\r\n    },\r\n    /**\r\n     * 按钮：查询*/\r\n    queryList(currentPage) {\r\n      this.showLoading(true)\r\n      const msg = {\r\n        parameterList: [{ ...this.defaultForm }],\r\n        currentPage: currentPage || this.table.pageList.currentPage,\r\n        pageSize: this.table.pageList.pageSize\r\n      }\r\n      // 查询\r\n      query(msg).then((res) => {\r\n        const { returnList, totalNum, currentPage } = res.retMap\r\n        this.table.componentProps.data = returnList\r\n        this.table.pageList.totalNum = totalNum\r\n        this.table.pageList.currentPage = currentPage\r\n        // 格式化系统标识和接口标识\r\n        this.table.componentProps.data.forEach((item) => {\r\n          this.$store.getters.externalData.ALL_SYS_ID.forEach((item2) => {\r\n            if (item2.value === item.sys_id) {\r\n              item.sys_id = `${item2.value}-${item2.label}`\r\n            }\r\n          })\r\n          this.$store.getters.externalData.TD_NO.forEach((item2) => {\r\n            if (item2.value === item.td_no) {\r\n              item.td_no = `${item2.value}-${item2.label}`\r\n            }\r\n          })\r\n        })\r\n\r\n        this.showLoading(false)\r\n      })\r\n    },\r\n    /**\r\n     * 弹出框 - 关闭\r\n     * @param {Boolean} param 弹出框显示隐藏配置*/\r\n    changeVisible(param) {\r\n      this.dialog.visible = param\r\n    },\r\n    /**\r\n     * btn - 新增*/\r\n    handleAdd() {\r\n      this.dialog.componentProps = {\r\n        ...this.dialog.componentProps,\r\n        title: '新增',\r\n        oprate: 'add'\r\n      }\r\n      this.changeVisible(true)\r\n    },\r\n    /**\r\n     * btn - 编辑*/\r\n    handleModify() {\r\n      const rows = this.table.currentRow.length\r\n      if (rows === 0) {\r\n        commonMsgWarn('请选择要修改的行', this)\r\n        return\r\n      } else if (rows > 1) {\r\n        commonMsgWarn('请选择一行', this)\r\n        return\r\n      }\r\n      this.dialog.componentProps = {\r\n        ...this.dialog.componentProps,\r\n        title: '编辑',\r\n        oprate: 'edit'\r\n      } // 添加属性\r\n      this.changeVisible(true)\r\n      this.$nextTick(() => {\r\n        // 弹出框加载完成后赋值、\r\n        this.dialog.form.defaultForm = Object.assign(\r\n          {},\r\n          this.table.currentRow[0]\r\n        )\r\n      })\r\n    },\r\n\r\n    /**\r\n     * 弹出框 - 确认弹框类型*/\r\n    dialogSubmit() {\r\n      const param = this.dialog.componentProps.oprate\r\n      if (param === 'add') {\r\n        this.dialogAddSubmit()\r\n      } else {\r\n        this.dialogEditSubmit()\r\n      }\r\n    },\r\n    /**\r\n     * 弹出框 - 确认 - 新增*/\r\n    dialogAddSubmit() {\r\n      this.showLoading(true)\r\n      const msg = {\r\n        parameterList: [\r\n          {\r\n            ...this.dialog.form.defaultForm,\r\n            is_lock: this.$store.getters.userNo,\r\n            before_data: {},\r\n            user_no: this.$store.getters.userNo\r\n          }\r\n        ],\r\n        before_data: {},\r\n        user_no: this.$store.getters.userNo,\r\n        operation_user: {\r\n          organ_no: this.$store.getters.organNo,\r\n          user_no: this.$store.getters.userNo\r\n        }\r\n      }\r\n      add(msg).then((res) => {\r\n        commonMsgSuccess('新增成功', this)\r\n        this.showLoading(false)\r\n        this.queryList()\r\n      })\r\n      this.changeVisible(false) // 弹出框关闭\r\n    },\r\n\r\n    /**\r\n     * 弹出框 - 确认 - 编辑*/\r\n    dialogEditSubmit() {\r\n      commonMsgConfirm('是否确认提交当前数据？', this, (param) => {\r\n        if (param) {\r\n          this.showLoading(true)\r\n          const msg = {\r\n            parameterList: [\r\n              {\r\n                sys_id: this.dialog.form.defaultForm.sys_id.split('-')[0],\r\n                td_no: this.dialog.form.defaultForm.td_no.split('-')[0],\r\n                is_open: this.dialog.form.defaultForm.is_open.split('-')[0],\r\n                is_lock: this.$store.getters.userNo,\r\n                before_data: {\r\n                  ...this.table.currentRow[0],\r\n                  sys_id: this.table.currentRow[0].sys_id.split('-')[0],\r\n                  td_no: this.table.currentRow[0].td_no.split('-')[0]\r\n                },\r\n                user_no: this.$store.getters.userNo\r\n              }\r\n            ],\r\n            before_data: {\r\n              ...this.table.currentRow[0],\r\n              sys_id: this.table.currentRow[0].sys_id.split('-')[0],\r\n              td_no: this.table.currentRow[0].td_no.split('-')[0]\r\n            },\r\n            user_no: this.$store.getters.userNo,\r\n            operation_user: {\r\n              organ_no: this.$store.getters.organNo,\r\n              user_no: this.$store.getters.userNo\r\n            }\r\n          }\r\n          // 修改\r\n          modify(msg)\r\n            .then((res) => {\r\n              commonMsgSuccess('修改成功', this)\r\n              this.showLoading(false)\r\n              this.queryList()\r\n            })\r\n            .catch(() => {\r\n              this.showLoading(false)\r\n            })\r\n          this.changeVisible(false) // 弹出框关闭\r\n        }\r\n      })\r\n    },\r\n    /**\r\n     * btn - 删除*/\r\n    handleDelete() {\r\n      const rows = this.table.currentRow\r\n      if (rows.length === 0) {\r\n        commonMsgWarn('请选择要删除的行', this)\r\n        return\r\n      }\r\n      commonMsgConfirm('是否确认删除当前选中的记录？', this, (param) => {\r\n        if (param) {\r\n          const dels = [] // 选中删除的数组\r\n          this.table.currentRow.forEach((item) => {\r\n            dels.push({\r\n              sys_id: item.sys_id.split('-')[0],\r\n              td_no: item.td_no.split('-')[0],\r\n              is_open: item.is_open,\r\n              is_lock: item.is_lock\r\n            })\r\n          })\r\n          const msg = {\r\n            operation_value: dels,\r\n            parameterList: dels,\r\n            is_lock: this.$store.getters.userNo,\r\n            operation_user: {\r\n              organ_no: this.$store.getters.organNo,\r\n              user_no: this.$store.getters.userNo\r\n            }\r\n          }\r\n          del(msg)\r\n            .then((res) => {\r\n              commonMsgSuccess('删除成功', this)\r\n              this.queryList(1)\r\n            })\r\n            .catch((e) => {\r\n              // console.log(e)\r\n            })\r\n        }\r\n      })\r\n    },\r\n    /**\r\n     * 处理下拉框系统标识和接口标识数据*/\r\n    sysOptions() {\r\n      timout1 = setTimeout(() => {\r\n        const arraySysId = this.$store.getters.externalData.ALL_SYS_ID\r\n        const sysidArray = []\r\n        const arrayTdNo = this.$store.getters.externalData.TD_NO\r\n        const tdnoArray = []\r\n        arraySysId.map(function(item) {\r\n          const valueS = Object.assign({}, item, {\r\n            label: item.value + '-' + item.label\r\n          })\r\n          sysidArray.push(valueS)\r\n        })\r\n        arrayTdNo.map(function(item) {\r\n          const valueS2 = Object.assign({}, item, {\r\n            label: item.value + '-' + item.label\r\n          })\r\n          tdnoArray.push(valueS2)\r\n        })\r\n        // 获取外部字典\r\n        this.dialog.form.config.sys_id.options = sysidArray\r\n        this.dialog.form.config.td_no.options = tdnoArray\r\n      }, 1000)\r\n    },\r\n    /**\r\n     *页码更新 */\r\n    getList(pageParam) {\r\n      const { currentPage, pageSize } = pageParam\r\n      this.table.pageList.pageSize = pageSize\r\n      this.table.pageList.currentPage = currentPage\r\n      this.queryList()\r\n    },\r\n    /**\r\n     * 加载中动画配置*/\r\n    showLoading() {\r\n      this.listLoading = !this.listLoading\r\n    }\r\n  }\r\n}\r\n</script>\r\n<style lang=\"scss\" scoped></style>\r\n"]}]}