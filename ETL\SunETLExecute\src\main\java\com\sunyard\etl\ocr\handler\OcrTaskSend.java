package com.sunyard.etl.ocr.handler;

import com.sunyard.etl.ocr.bean.DataSource;
import com.sunyard.etl.ocr.bean.TmpBatch;
import com.sunyard.etl.ocr.common.OCRConstants;
import com.sunyard.etl.common.dao.DataSourceDao;
import com.sunyard.etl.ocr.service.TaskSendService;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.enums.JobStartEnum;
import com.xxl.job.core.handler.IJobHandler;
import com.xxl.job.core.handler.annotation.JobHandler;
import com.xxl.job.core.log.XxlJobLogger;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;
import org.sunyard.util.string.StringUtil;

import java.util.List;
import java.util.Map;

/**
 * OCR识别任务发送
 * <AUTHOR>
 */
@JobHandler(value="ocrTaskSendHandler", name="档案-识别任务发送")
@Component
public class OcrTaskSend extends IJobHandler {

	private static final long serialVersionUID = 1L;
	protected final Logger logger = LoggerFactory.getLogger("ocrTaskSendLogger");
	private String tableName = "OcrTaskSend";
	private static DataSourceDao dataSourceDao = new DataSourceDao();

	@Override
	public ReturnT<String> execute(String jobId, String... strings) throws Exception {
		logger.info("==========================================");
		logger.info("====      档案-识别任务发送任务开始       ====");
		logger.info("==========================================");

		Map<String, String> params = dataSourceDao.getParamsMap(Integer.parseInt(jobId));
		String datasource = (String)params.get("DATASOURCE");
		String channleId = params.get("CHANNLEID");
		String ocrUrl = params.get("OCR_URL");
		String platForm=params.get("PLATFORM");

		if (StringUtil.isEmpty(datasource)) {
			XxlJobLogger.log("【OCR识别任务发送】:" + jobId + "参数缺失,无法执行");
			return new  ReturnT<String>(ReturnT.FAIL_CODE, JobStartEnum.TASK_CONFIG_ERROR.getCode(),"找不到对应的参数表信息,无法执行");
		}

		//获取任务执行数据源
		DataSource ds = dataSourceDao.findById(Integer.parseInt(datasource));
		if (ds == null) {
			XxlJobLogger.log("【OCR识别任务发送】:" + jobId + "数据源未定义");
			return new ReturnT<String>(500, JobStartEnum.TASK_CONFIG_ERROR.getCode(), "参数缺失,无法执行");
		}
		String unifyDatasource = params.get("UNIFY_DATASOURCE");
		DataSource unifyDs = ds;
		if (!StringUtil.isEmpty(unifyDatasource)) {
			//获取任务执行数据源
			unifyDs = dataSourceDao.findById(Integer.parseInt(unifyDatasource));
			if (unifyDs == null) {
				XxlJobLogger.log("【OCR识别任务发送】:数据源定义出错");
				return new ReturnT<String>(ReturnT.FAIL_CODE, JobStartEnum.TASK_CONFIG_ERROR.getCode(), "数据源定义出错,无法执行");
			}
		}
		TaskSendService taskSendService = new TaskSendService(ds,unifyDs);

		List<TmpBatch> batchList = taskSendService.getSendBatch();
		Map<String, String> retMap = taskSendService.sendBatchList(batchList, channleId,ocrUrl,platForm);
		if(OCRConstants.HANDLE_SUCCESS.equals(retMap.get("retCode"))) {
			return new ReturnT<>(ReturnT.SUCCESS_CODE, retMap.get("retMsg"));
		}else {
			return new ReturnT<>(ReturnT.FAIL_CODE, retMap.get("retMsg"));
		}
	}

}
