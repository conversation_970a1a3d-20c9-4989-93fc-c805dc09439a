package com.sunyard.etl.nps.model;

public class BpVoucherChkTb {

	private int businessId;
	private String occurDate;
	private String siteNo;
	private String operatorNo;
	private String regDate;
	private String regTime;
	private String inputWorker;
	private String batchId;
	private String mixedNPFlag = "0";
	private String regVoucherNum;
	private String scanVoucherNum;
	private String batchCommit = "1";
	
	public int getBusinessId() {
		return businessId;
	}
	public void setBusinessId(int businessId) {
		this.businessId = businessId;
	}
	public String getOccurDate() {
		return occurDate;
	}
	public void setOccurDate(String occurDate) {
		this.occurDate = occurDate;
	}
	public String getSiteNo() {
		return siteNo;
	}
	public void setSiteNo(String siteNo) {
		this.siteNo = siteNo;
	}
	public String getOperatorNo() {
		return operatorNo;
	}
	public void setOperatorNo(String operatorNo) {
		this.operatorNo = operatorNo;
	}
	public String getRegDate() {
		return regDate;
	}
	public void setRegDate(String regDate) {
		this.regDate = regDate;
	}
	public String getRegTime() {
		return regTime;
	}
	public void setRegTime(String regTime) {
		this.regTime = regTime;
	}
	public String getInputWorker() {
		return inputWorker;
	}
	public void setInputWorker(String inputWorker) {
		this.inputWorker = inputWorker;
	}
	public String getBatchId() {
		return batchId;
	}
	public void setBatchId(String batchId) {
		this.batchId = batchId;
	}
	public String getMixedNPFlag() {
		return mixedNPFlag;
	}
	public void setMixedNPFlag(String mixedNPFlag) {
		this.mixedNPFlag = mixedNPFlag;
	}
	public String getRegVoucherNum() {
		return regVoucherNum;
	}
	public void setRegVoucherNum(String regVoucherNum) {
		this.regVoucherNum = regVoucherNum;
	}
	public String getScanVoucherNum() {
		return scanVoucherNum;
	}
	public void setScanVoucherNum(String scanVoucherNum) {
		this.scanVoucherNum = scanVoucherNum;
	}
	public String getBatchCommit() {
		return batchCommit;
	}
	public void setBatchCommit(String batchCommit) {
		this.batchCommit = batchCommit;
	}
}
