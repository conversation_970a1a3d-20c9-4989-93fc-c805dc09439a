{"remainingRequest": "E:\\华南区项目\\中国银行\\开发区\\科技公司运营平台\\前端\\dopUnify\\node_modules\\babel-loader\\lib\\index.js!E:\\华南区项目\\中国银行\\开发区\\科技公司运营平台\\前端\\dopUnify\\node_modules\\@babel\\runtime\\helpers\\esm\\isNativeFunction.js", "dependencies": [{"path": "E:\\华南区项目\\中国银行\\开发区\\科技公司运营平台\\前端\\dopUnify\\node_modules\\@babel\\runtime\\helpers\\esm\\isNativeFunction.js", "mtime": 456789000000}, {"path": "E:\\华南区项目\\中国银行\\开发区\\科技公司运营平台\\前端\\dopUnify\\babel.config.js", "mtime": 1721631954662}, {"path": "E:\\华南区项目\\中国银行\\开发区\\科技公司运营平台\\前端\\dopUnify\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "E:\\华南区项目\\中国银行\\开发区\\科技公司运营平台\\前端\\dopUnify\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:aW1wb3J0ICJjb3JlLWpzL21vZHVsZXMvZXMub2JqZWN0LnRvLXN0cmluZy5qcyI7CmltcG9ydCAiY29yZS1qcy9tb2R1bGVzL2VzLnJlZ2V4cC50by1zdHJpbmcuanMiOwpleHBvcnQgZGVmYXVsdCBmdW5jdGlvbiBfaXNOYXRpdmVGdW5jdGlvbihmbikgewogIHJldHVybiBGdW5jdGlvbi50b1N0cmluZy5jYWxsKGZuKS5pbmRleE9mKCJbbmF0aXZlIGNvZGVdIikgIT09IC0xOwp9"}, {"version": 3, "names": ["_isNativeFunction", "fn", "Function", "toString", "call", "indexOf"], "sources": ["E:/华南区项目/中国银行/开发区/科技公司运营平台/前端/dopUnify/node_modules/@babel/runtime/helpers/esm/isNativeFunction.js"], "sourcesContent": ["export default function _isNativeFunction(fn) {\n  return Function.toString.call(fn).indexOf(\"[native code]\") !== -1;\n}"], "mappings": ";;AAAA,eAAe,SAASA,iBAAiB,CAACC,EAAE,EAAE;EAC5C,OAAOC,QAAQ,CAACC,QAAQ,CAACC,IAAI,CAACH,EAAE,CAAC,CAACI,OAAO,CAAC,eAAe,CAAC,KAAK,CAAC,CAAC;AACnE"}]}