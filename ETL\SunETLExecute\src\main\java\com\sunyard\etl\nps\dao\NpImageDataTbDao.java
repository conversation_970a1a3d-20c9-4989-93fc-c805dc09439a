package com.sunyard.etl.nps.dao;

import java.sql.SQLException;
import java.util.ArrayList;
import java.util.List;

import org.sunyard.util.dbutil.DBHandler;

import com.sun.rowset.CachedRowSetImpl;
import com.sunyard.etl.nps.model.NpImageData;
import com.sunyard.etl.nps.orm.NpImageDataOrm;
import com.sunyard.etl.system.common.Constants;
import com.sunyard.etl.system.orm.Orm;
import com.xxl.job.core.log.XxlJobLogger;

public class NpImageDataTbDao {
	
	private String tableName;
	private Orm<NpImageData> npImageDataOrm = new NpImageDataOrm();

	public NpImageDataTbDao(){
		
	}
	
	public NpImageDataTbDao(String tableName){
		this.tableName = tableName;
	}
	
	
	/**
	 * 从无纸化业务信息表中删除数据
	 * 
	 * <AUTHOR> 2017年7月7日
	 * @param busiDataNo
	 * @return
	 */
	public boolean deleteByBusiDataNo(String busiDataNo) {
		DBHandler dbHandler = new DBHandler(Constants.CONN_POOL_NAME);
		try {
			String sql = "DELETE FROM NP_IMAGE_DATA_TB T WHERE T.BUSI_DATA_NO IN ('" + busiDataNo + "') ";
			XxlJobLogger.log("从NP_IMAGE_DATA_TB中删除数据:" + sql, tableName);
			dbHandler.execute(sql);
		} catch (SQLException e) {
			e.printStackTrace();
			return false;
		}
		return true;
	}
	
	
	/**
	 * 把一笔无纸化业务对应的图像的数据集合npImageDataList插入到NpImageData表中，同时更新该笔业务的处理状态
	 * 
	 * 
	 * <AUTHOR> 2017年7月7日
	 * @param npImageDataList
	 * @return
	 */
	public boolean insert(List<NpImageData> npImageDataList) {
		DBHandler dbHandler = new DBHandler(Constants.CONN_POOL_NAME);
		List<String> sqlList = new ArrayList<String>();
		for (NpImageData i : npImageDataList) {
			String sql = "INSERT INTO NP_IMAGE_DATA_TB(BUSI_DATA_NO,FORM_NAME,FILE_NAME,BACK_FILE_NAME,IMAGE_SIZE,BACK_IMAGE_SIZE,ORDER_NUM, PS_LEVEL) "
					+ "VALUES ('"
					+ i.getBusiDataNo()
					+ "','"
					+ i.getFormName()
					+ "','"
					+ i.getFileName()
					+ "','"
					+ i.getBackFileName()
					+ "',"
					+ i.getImageSize()
					+ ","
					+ i.getBackImageSize()
					+ ",'" + i.getOrderNum() + "','" + i.getPsLevel() + "')";
			sqlList.add(sql);
			String updateSql = "UPDATE NP_BUSINESS_DATA_TB T  SET T.FLAG = '1'  WHERE T.BUSI_DATA_NO ="
					+ i.getBusiDataNo();
			sqlList.add(updateSql);
		}

		try {
			int[] i = dbHandler.executeAsBatch(sqlList);
			for (int flag : i) {
				if (flag < 0 && flag != -2)
					return false;
			}
		} catch (SQLException e) {
			e.printStackTrace();
			return false;
		}
		return true;
	}
	
	
	/**
	 * 
	 * @Title getImgInfo
	 * @Description 根据busiDataNo从np_image_data_tb中找到图像信息数据
	 * <AUTHOR> 2017年8月9日
	 * @param busiDataNo
	 * @return
	 * @throws SQLException
	 */
	public List<NpImageData> queryByBusiDataNo(String busiDataNo) throws SQLException {
		List<NpImageData> list = new ArrayList<NpImageData>();
		DBHandler dbHandler = new DBHandler(Constants.CONN_POOL_NAME);
		CachedRowSetImpl rs = null;
		String sql = "SELECT * FROM NP_IMAGE_DATA_TB  T WHERE  T.BUSI_DATA_NO = ?";
		rs = dbHandler.queryRs(sql, busiDataNo);
		while (rs.next()) {
			NpImageData npImageData = npImageDataOrm.orm(rs);
			list.add(npImageData);
		}
		return list;
	}
	
	
	

	/**
	 * 把一笔无纸化业务对应的图像的数据集合npImageDataList插入到NpImageData表中，同时更新该笔业务的处理状态
	 * 
	 * 
	 * <AUTHOR> 2017年7月7日
	 * @param npImageDataList
	 * @return
	 */
	public boolean insertNpImageData(List<NpImageData> npImageDataList) {
		DBHandler dbHandler = new DBHandler(Constants.CONN_POOL_NAME);
		List<String> sqlList = new ArrayList<String>();
		for (NpImageData i : npImageDataList) {
			String sql = "INSERT INTO NP_IMAGE_DATA_TB(BUSI_DATA_NO,FORM_NAME,FILE_NAME,BACK_FILE_NAME,IMAGE_SIZE,BACK_IMAGE_SIZE,ORDER_NUM, PS_LEVEL) "
					+ "VALUES ('"
					+ i.getBusiDataNo()
					+ "','"
					+ i.getFormName()
					+ "','"
					+ i.getFileName()
					+ "','"
					+ i.getBackFileName()
					+ "',"
					+ i.getImageSize()
					+ ","
					+ i.getBackImageSize()
					+ ",'" + i.getOrderNum() + "','" + i.getPsLevel() + "')";
			sqlList.add(sql);
			String updateSql = "UPDATE NP_BUSINESS_DATA_TB T  SET T.FLAG = '1'  WHERE T.BUSI_DATA_NO ="
					+ i.getBusiDataNo();
			sqlList.add(updateSql);
		}

		try {
			int[] i = dbHandler.executeAsBatch(sqlList);
			for (int flag : i) {
				if (flag < 0 && flag != -2)
					return false;
			}
		} catch (SQLException e) {
			e.printStackTrace();
			return false;
		}
		return true;
	}
}
