{"remainingRequest": "E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\node_modules\\babel-loader\\lib\\index.js!E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\node_modules\\eslint-loader\\index.js??ref--13-0!E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\src\\views\\system\\notice\\prac\\info.js", "dependencies": [{"path": "E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\src\\views\\system\\notice\\prac\\info.js", "mtime": 1686019808794}, {"path": "E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\babel.config.js", "mtime": 1686019818359}, {"path": "E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\.babelrc", "mtime": 1705285078668}, {"path": "E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\node_modules\\eslint-loader\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:ZXhwb3J0IHZhciBjb25maWcgPSBmdW5jdGlvbiBjb25maWcodGhhdCkgewogIHJldHVybiB7CiAgICBub3RpY2Vfa2V5d29yZDogewogICAgICBjb21wb25lbnQ6ICdpbnB1dCcsCiAgICAgIGxhYmVsOiAn5YWs5ZGK5qCH6aKYJywKICAgICAgY29sU3BhbjogOCwKICAgICAgbmFtZTogJ25vdGljZV9rZXl3b3JkJywKICAgICAgY29tcG9uZW50UHJvcHM6IHsKICAgICAgICAvLyBpbnB1dOe7hOS7tumFjee9rgogICAgICAgIC8vIGNsZWFyYWJsZTogdHJ1ZSwKICAgICAgICBwbGFjZWhvbGRlcjogJ+aUr+aMgeWFrOWRiuagh+mimOaooeeziuafpeivoicKICAgICAgfQogICAgfSwKICAgIHB1Ymxpc2hfdGltZTogewogICAgICBjb21wb25lbnQ6ICdkYXRlLXBpY2tlcicsCiAgICAgIGxhYmVsOiAn5Y+R5biD5pel5pyfJywKICAgICAgY29sU3BhbjogOCwKICAgICAgbmFtZTogJ3B1Ymxpc2hfdGltZTEnLAogICAgICBjb21wb25lbnRQcm9wczogewogICAgICAgIHR5cGU6ICdkYXRlcmFuZ2UnLAogICAgICAgIHJhbmdlU2VwYXJhdG9yOiAnLScsCiAgICAgICAgc3RhcnRQbGFjZWhvbGRlcjogJ+W8gOWni+aXpeacnycsCiAgICAgICAgZW5kUGxhY2Vob2xkZXI6ICfnu5PmnZ/ml6XmnJ8nLAogICAgICAgIHZhbHVlRm9ybWF0OiAneXl5eU1NZGQnCiAgICAgIH0KICAgIH0KICB9Owp9Ow=="}, {"version": 3, "names": ["config", "that", "notice_keyword", "component", "label", "colSpan", "name", "componentProps", "placeholder", "publish_time", "type", "rangeSeparator", "startPlaceholder", "endPlaceholder", "valueFormat"], "sources": ["E:/1_Project/XYD_Project/dop-4.0/dop-4.1-qian<PERSON>n-unify/dopUnify/src/views/system/notice/prac/info.js"], "sourcesContent": ["export const config = (that) => ({\r\n  notice_keyword: {\r\n    component: 'input',\r\n    label: '公告标题',\r\n    colSpan: 8,\r\n    name: 'notice_keyword',\r\n    componentProps: { // input组件配置\r\n      // clearable: true,\r\n      placeholder: '支持公告标题模糊查询'\r\n    }\r\n  },\r\n  publish_time: {\r\n    component: 'date-picker',\r\n    label: '发布日期',\r\n    colSpan: 8,\r\n    name: 'publish_time1',\r\n    componentProps: {\r\n      type: 'daterange',\r\n      rangeSeparator: '-',\r\n      startPlaceholder: '开始日期',\r\n      endPlaceholder: '结束日期',\r\n      valueFormat: 'yyyyMMdd'\r\n    }\r\n  }\r\n})\r\n"], "mappings": "AAAA,OAAO,IAAMA,MAAM,GAAG,SAATA,MAAM,CAAIC,IAAI;EAAA,OAAM;IAC/BC,cAAc,EAAE;MACdC,SAAS,EAAE,OAAO;MAClBC,KAAK,EAAE,MAAM;MACbC,OAAO,EAAE,CAAC;MACVC,IAAI,EAAE,gBAAgB;MACtBC,cAAc,EAAE;QAAE;QAChB;QACAC,WAAW,EAAE;MACf;IACF,CAAC;IACDC,YAAY,EAAE;MACZN,SAAS,EAAE,aAAa;MACxBC,KAAK,EAAE,MAAM;MACbC,OAAO,EAAE,CAAC;MACVC,IAAI,EAAE,eAAe;MACrBC,cAAc,EAAE;QACdG,IAAI,EAAE,WAAW;QACjBC,cAAc,EAAE,GAAG;QACnBC,gBAAgB,EAAE,MAAM;QACxBC,cAAc,EAAE,MAAM;QACtBC,WAAW,EAAE;MACf;IACF;EACF,CAAC;AAAA,CAAC"}]}