{"remainingRequest": "E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\node_modules\\babel-loader\\lib\\index.js!E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\node_modules\\eslint-loader\\index.js??ref--13-0!E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\src\\api\\common\\flowPath\\index.js", "dependencies": [{"path": "E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\src\\api\\common\\flowPath\\index.js", "mtime": 1689922250028}, {"path": "E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\babel.config.js", "mtime": 1686019818359}, {"path": "E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\.babelrc", "mtime": 1705285078668}, {"path": "E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\node_modules\\eslint-loader\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["request", "defaultSettings", "prefix", "service", "system", "FlowPath", "releaseTask", "data", "url", "method", "selectModule", "params", "message", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "selectSubmitType", "selectFlowDetailData", "approveFlow", "selectAuthorRequestApprovalSystem", "instDetail", "instOther"], "sources": ["E:/1_Project/XYD_Project/dop-4.0/dop-4.1-qianduan-unify/dopUnify/src/api/common/flowPath/index.js"], "sourcesContent": ["import request from '@/utils/request'\r\nimport defaultSettings from '@/settings'\r\nconst prefix = defaultSettings.service.system\r\n\r\n// 流程相关\r\nexport const FlowPath = {\r\n  // 流程相关   释放处理中流程任务，解绑处理人\r\n  releaseTask(data) {\r\n    return request({\r\n      url: '/flow/flowGeneral/releaseTask.do',\r\n      method: 'post',\r\n      data\r\n    })\r\n  },\r\n  // 筛选流程模板\r\n  selectModule(data) {\r\n    return request({\r\n      url: '/flow/flowGeneral/selectModule.do',\r\n      method: 'get',\r\n      params: { message: data }\r\n    })\r\n  },\r\n  // 按钮功能准入禁止判断\r\n  judgeRightForButton(data) {\r\n    return request({\r\n      url: prefix + '/operationRequest/judgeRightForButton.do',\r\n      method: 'get',\r\n      params: { message: data }\r\n    })\r\n  },\r\n  // 获取按钮授权方式（数据存到vuex里）\r\n  selectSubmitType(data) {\r\n    return request({\r\n      url: prefix + '/operationRequest/selectSubmitType.do',\r\n      method: 'get',\r\n      params: { message: data }\r\n    })\r\n  },\r\n  // 转授权流程接口\r\n  selectFlowDetailData(data) {\r\n    return request({\r\n      url: prefix + '/subLicense/selectFlowDetailData.do',\r\n      method: 'get',\r\n      params: { message: data }\r\n    })\r\n  },\r\n  // 转授权流程审批接口同意拒绝操作\r\n  approveFlow(data) {\r\n    return request({\r\n      url: prefix + '/subLicense/approveFlow.do',\r\n      method: 'post',\r\n      data\r\n    })\r\n  },\r\n  // 转授权详情流程接口\r\n  selectAuthorRequestApprovalSystem(data) {\r\n    return request({\r\n      url: prefix + '/subLicense/selectAuthorRequestApprovalSystem.do',\r\n      method: 'get',\r\n      params: { message: data }\r\n    })\r\n  },\r\n  // 指标发布历史，下线历史详情\r\n  instDetail(data) {\r\n    return request({\r\n      url: '/flow/flowWork/instDetail.do',\r\n      method: 'post',\r\n      data\r\n    })\r\n  },\r\n  instOther(url, data) {\r\n    return request({\r\n      url: url,\r\n      method: 'post',\r\n      data\r\n    })\r\n  }\r\n}\r\n"], "mappings": "AAAA,OAAOA,OAAO,MAAM,iBAAiB;AACrC,OAAOC,eAAe,MAAM,YAAY;AACxC,IAAMC,MAAM,GAAGD,eAAe,CAACE,OAAO,CAACC,MAAM;;AAE7C;AACA,OAAO,IAAMC,QAAQ,GAAG;EACtB;EACAC,WAAW,uBAACC,IAAI,EAAE;IAChB,OAAOP,OAAO,CAAC;MACbQ,GAAG,EAAE,kCAAkC;MACvCC,MAAM,EAAE,MAAM;MACdF,IAAI,EAAJA;IACF,CAAC,CAAC;EACJ,CAAC;EACD;EACAG,YAAY,wBAACH,IAAI,EAAE;IACjB,OAAOP,OAAO,CAAC;MACbQ,GAAG,EAAE,mCAAmC;MACxCC,MAAM,EAAE,KAAK;MACbE,MAAM,EAAE;QAAEC,OAAO,EAAEL;MAAK;IAC1B,CAAC,CAAC;EACJ,CAAC;EACD;EACAM,mBAAmB,+BAACN,IAAI,EAAE;IACxB,OAAOP,OAAO,CAAC;MACbQ,GAAG,EAAEN,MAAM,GAAG,0CAA0C;MACxDO,MAAM,EAAE,KAAK;MACbE,MAAM,EAAE;QAAEC,OAAO,EAAEL;MAAK;IAC1B,CAAC,CAAC;EACJ,CAAC;EACD;EACAO,gBAAgB,4BAACP,IAAI,EAAE;IACrB,OAAOP,OAAO,CAAC;MACbQ,GAAG,EAAEN,MAAM,GAAG,uCAAuC;MACrDO,MAAM,EAAE,KAAK;MACbE,MAAM,EAAE;QAAEC,OAAO,EAAEL;MAAK;IAC1B,CAAC,CAAC;EACJ,CAAC;EACD;EACAQ,oBAAoB,gCAACR,IAAI,EAAE;IACzB,OAAOP,OAAO,CAAC;MACbQ,GAAG,EAAEN,MAAM,GAAG,qCAAqC;MACnDO,MAAM,EAAE,KAAK;MACbE,MAAM,EAAE;QAAEC,OAAO,EAAEL;MAAK;IAC1B,CAAC,CAAC;EACJ,CAAC;EACD;EACAS,WAAW,uBAACT,IAAI,EAAE;IAChB,OAAOP,OAAO,CAAC;MACbQ,GAAG,EAAEN,MAAM,GAAG,4BAA4B;MAC1CO,MAAM,EAAE,MAAM;MACdF,IAAI,EAAJA;IACF,CAAC,CAAC;EACJ,CAAC;EACD;EACAU,iCAAiC,6CAACV,IAAI,EAAE;IACtC,OAAOP,OAAO,CAAC;MACbQ,GAAG,EAAEN,MAAM,GAAG,kDAAkD;MAChEO,MAAM,EAAE,KAAK;MACbE,MAAM,EAAE;QAAEC,OAAO,EAAEL;MAAK;IAC1B,CAAC,CAAC;EACJ,CAAC;EACD;EACAW,UAAU,sBAACX,IAAI,EAAE;IACf,OAAOP,OAAO,CAAC;MACbQ,GAAG,EAAE,8BAA8B;MACnCC,MAAM,EAAE,MAAM;MACdF,IAAI,EAAJA;IACF,CAAC,CAAC;EACJ,CAAC;EACDY,SAAS,qBAACX,GAAG,EAAED,IAAI,EAAE;IACnB,OAAOP,OAAO,CAAC;MACbQ,GAAG,EAAEA,GAAG;MACRC,MAAM,EAAE,MAAM;MACdF,IAAI,EAAJA;IACF,CAAC,CAAC;EACJ;AACF,CAAC"}]}