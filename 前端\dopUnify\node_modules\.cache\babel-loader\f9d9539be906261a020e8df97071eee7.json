{"remainingRequest": "D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\node_modules\\thread-loader\\dist\\cjs.js!D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\node_modules\\babel-loader\\lib\\index.js!D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\src\\views\\system\\dailyManage\\log\\component\\table\\index.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\src\\views\\system\\dailyManage\\log\\component\\table\\index.vue", "mtime": 1686019808419}, {"path": "D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\babel.config.js", "mtime": 1686019818359}, {"path": "D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\node_modules\\thread-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1655715099000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, null]}