{"remainingRequest": "E:\\华南区项目\\中国银行\\开发区\\科技公司运营平台\\前端\\dopUnify\\node_modules\\babel-loader\\lib\\index.js!E:\\华南区项目\\中国银行\\开发区\\科技公司运营平台\\前端\\dopUnify\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!E:\\华南区项目\\中国银行\\开发区\\科技公司运营平台\\前端\\dopUnify\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\华南区项目\\中国银行\\开发区\\科技公司运营平台\\前端\\dopUnify\\src\\components\\Layout\\Breadcrumb\\index.vue?vue&type=script&lang=js&", "dependencies": [{"path": "E:\\华南区项目\\中国银行\\开发区\\科技公司运营平台\\前端\\dopUnify\\src\\components\\Layout\\Breadcrumb\\index.vue", "mtime": 1721631954662}, {"path": "E:\\华南区项目\\中国银行\\开发区\\科技公司运营平台\\前端\\dopUnify\\babel.config.js", "mtime": 1721631954662}, {"path": "E:\\华南区项目\\中国银行\\开发区\\科技公司运营平台\\前端\\dopUnify\\.babelrc", "mtime": 1721631954662}, {"path": "E:\\华南区项目\\中国银行\\开发区\\科技公司运营平台\\前端\\dopUnify\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "E:\\华南区项目\\中国银行\\开发区\\科技公司运营平台\\前端\\dopUnify\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "E:\\华南区项目\\中国银行\\开发区\\科技公司运营平台\\前端\\dopUnify\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "E:\\华南区项目\\中国银行\\开发区\\科技公司运营平台\\前端\\dopUnify\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1655715099000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "mappings": ";;;;;;;;;;;;;;;;;;AAYA;AAEA;EACAA;IACA;MACAC;IACA;EACA;EACAC;IACAC;MACA;MACA;QACA;MACA;MACA;IACA;EACA;EACAC;IACA;EACA;EACAC;IACAC;MACA;MACA;QAAA;MAAA;MACA;MAEA;QACAC;UAAAC;UAAAC;YAAAC;UAAA;QAAA;MACA;MAEA;QAAA;MAAA;IACA;IACAC;MACA;MACA;QACA;MACA;MACA;IACA;IACAC;MACA;MACA;MACA;MACA;IACA;IACAC;MACA;QAAAL;MACA;QACA;QACA;MACA;MACA;IACA;EACA;AACA", "names": ["data", "levelList", "watch", "$route", "created", "methods", "getBreadcrumb", "matched", "path", "meta", "title", "isDashboard", "pathCompile", "handleLink"], "sourceRoot": "src/components/Layout/Breadcrumb", "sources": ["index.vue"], "sourcesContent": ["<template>\n  <el-breadcrumb class=\"app-breadcrumb\" separator=\"/\">\n    <transition-group name=\"breadcrumb\">\n      <el-breadcrumb-item v-for=\"(item,index) in levelList\" :key=\"item.path\">\n        <span v-if=\"item.redirect==='noRedirect'||index==levelList.length-1\" class=\"no-redirect\">{{ item.meta.title }}</span>\n        <a v-else @click.prevent=\"handleLink(item)\">{{ item.meta.title }}</a>\n      </el-breadcrumb-item>\n    </transition-group>\n  </el-breadcrumb>\n</template>\n\n<script>\nimport pathToRegexp from 'path-to-regexp'\n\nexport default {\n  data() {\n    return {\n      levelList: null\n    }\n  },\n  watch: {\n    $route(route) {\n      // if you go to the redirect page, do not update the breadcrumbs\n      if (route.path.startsWith('/redirect/')) {\n        return\n      }\n      this.getBreadcrumb()\n    }\n  },\n  created() {\n    this.getBreadcrumb()\n  },\n  methods: {\n    getBreadcrumb() {\n      // only show routes with meta.title\n      let matched = this.$route.matched.filter(item => item.meta && item.meta.title)\n      const first = matched[0]\n\n      if (!this.isDashboard(first)) {\n        matched = [{ path: '/home', meta: { title: '主页' }}].concat(matched)\n      }\n\n      this.levelList = matched.filter(item => item.meta && item.meta.title && item.meta.breadcrumb !== false)\n    },\n    isDashboard(route) {\n      const name = route && route.name\n      if (!name) {\n        return false\n      }\n      return name.trim().toLocaleLowerCase() === 'Dashboard'.toLocaleLowerCase()\n    },\n    pathCompile(path) {\n      // To solve this problem https://github.com/PanJiaChen/vue-element-admin/issues/561\n      const { params } = this.$route\n      var toPath = pathToRegexp.compile(path)\n      return toPath(params)\n    },\n    handleLink(item) {\n      const { redirect, path } = item\n      if (redirect) {\n        this.$router.push(redirect)\n        return\n      }\n      this.$router.push(this.pathCompile(path))\n    }\n  }\n}\n</script>\n\n<style lang=\"scss\" scoped>\n.app-breadcrumb.el-breadcrumb {\n  display: inline-block;\n  font-size: 14px;\n  line-height: 50px;\n  margin-left: 8px;\n\n  .no-redirect {\n    color: #97a8be;\n    cursor: text;\n  }\n}\n</style>\n"]}]}