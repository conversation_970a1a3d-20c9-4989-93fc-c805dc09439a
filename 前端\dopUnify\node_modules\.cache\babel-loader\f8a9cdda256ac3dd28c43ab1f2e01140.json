{"remainingRequest": "D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\node_modules\\babel-loader\\lib\\index.js!D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\src\\layout\\extend\\index.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\src\\layout\\extend\\index.vue", "mtime": 1703583640644}, {"path": "D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\babel.config.js", "mtime": 1686019818359}, {"path": "D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\.babelrc", "mtime": 1705285078668}, {"path": "D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1655715099000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA+CA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IACAA,kBACAC,OADAD;AAEA;EACAE;EACAC;IAAAC;EAAA;EACAC;IACA;MACAC;MACAC;MAAA;MACAC;MACAC;MACAC;MAAA;MACAC;QACA;QACAC;QACAC;UACA;UACAC;UAAA;UACAC;UACAC;QACA;;QACAC;QAAA;QACAC;QAAA;QACAC;MACA;;MACAC;QACA;QACAR;QACAE;QACAO;MACA;MACAC;IACA;EACA;EACAC;IACAd;MACA;QACAe;QACA;MACA;IACA;EACA;EACAC;IAAA;IACA;IACA;IACA;IACA;IACA;MACAX;MACAY;IACA;IACA;MACA;QAAA;QAAA;UAAA;YAAA;cAAA;gBACAC;gBACAC;gBACAC;gBACA;gBACA;gBACA;gBAAA,IACAC;kBAAA;kBAAA;gBAAA;gBACAC;gBAAA,KACAC;kBAAA;kBAAA;gBAAA;gBAAA;gBAAA,OACA;cAAA;gBAAA;gBAAA;cAAA;gBAEA;kBACAC;kBACAC;kBACAC,uBACAC;gBAEA;cAAA;gBAGA;gBACA;gBACA;gBACA;kBACAC;kBACA;oBACA;oBACAC,0DACA;sBACA;oBACA,EACA;oBACAC,iBACA;oBACA;sBACA;sBACAA;oBACA;sBACA;sBACAA,wCACA,GACAC,uDACA;oBACA;oBAEA;sBACA;sBACA;sBACA;sBACA;oBACA;sBACA;sBACA;oBACA;kBACA;oBACA;oBACA;kBACA;kBACA;kBACA;gBACA;;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA,CACA;IACA;EACA;EACA;EACAC;IACAC;IACAA;IACAA;EACA;EACAC;IACA;AACA;AACA;IACAC;MACA;IACA;IACA;AACA;AACA;IACAC;MAAA;MACA;QACA;UACAC;UACAC;UACAC;UAAA;UACAf;QACA;QACAjC;UACA;UACAoC;UACAA,qBACAH,yBACAgB,+EACA;UACA;YACAhB;YACAC;YACAC;UACA;UACAe;QACA;MACA;IACA;IACAC;MACA;MACA;MACA;MACAC;MACA;MACA;MACA;MACA;MACA;;MAEA;MACA;MACA;QACA,6BACAH,uDACA;QACA;QACA;UACA;YACAI,mDACAC,uBACAA,iBACA;UACA;QACA;QACA;QACA;QACA;UACAC;UACAC,yCACA,IACAA,8CACAD,iCACA;QACA;UACAA;UACAC,yCACA,IACAA,8CAEAD,iCAEA;UACAC;YACAC;UACA;QACA;QAEA;UACA;YACA,IACAC,oBACAA,yBACAA,sBACA;cACA;YACA;UACA;QACA;UACA;YACA;cACA,IACAA,4BACAA,0BACAA,uBACA;gBACA;cACA;YACA;UACA;QACA;MACA;MACA;;MAEA;QACA;UACAC;UACAC;UACAC;UACAnD;QACA;QACAc;QAEAE;QACA;QACAuB,iCACAa,sEACA;MACA;MACA;IACA;IACA;AACA;AACA;IACAC;MACA;MACAvB;IACA;IACA;AACA;AACA;AACA;IACAwB;MAAA;MACA;MACA;MACA;MACAxC;MACA;QACA;QACA;MACA;QACA;QACA;UACAyC;UACA5D;QACA;MACA;QACA;QACA;MACA,WACA6D,uCACAA,wCACA;QACA;QACA;QACA;UACA;UACA;YACA;UACA;QACA;MACA,WACAA,uCACAA,mCACA;QACA;MACA;QACA;QACA,WACAA,2CACAA,iCACA,UACA,aACA;QACA;QACA;QACA;QACA;QACA;QAEA;UACA;UACA;UACA;YACA;cACAP;cACAC;cACAO;cACAC;cACAC,SACA7B,yBACAA,2BACA;cACAqB;cACAnD;YACA;YACAgB;YACA;YACAuB,iCACAa,yCACA,MACAtB,uBACA;UACA;QACA;MACA;QACA;QACA,wDACAS,oCACA;QACA;QACA;QACA;QACA;QACA;QACA;;QAEA;QACA;QACA;QACA;UACA;UACA;UACA;YACA;cACAU;cACAC;cACAC;cACAnD;YACA;YACAgB;YACA;YACAuB,iCACAa,yCACA,MACAtB,uBACA;UACA;QACA;MACA;QACA;QACA;UACA;UACA;QACA,WACA8B,2BACAJ,qCACA;UACA;UACAI;QACA;MACA;QACA;QACA;UACAC;QACA;MACA;QACA;QACA;QACA;UACA,OACAb;QAEA;QACA;UACA,OACAA;QAEA;QACA;UACA,OACAA;QAEA;QACA;UACA;QACA;QACA;UACA;QACA;QACA;UACA;QACA;QACA;MACA;IACA;IAEA;AACA;AACA;IACAc;MACA;QACA;MACA;MACA;QAAA3D;QAAAM;MACA;MACA;MACA;MACA;IACA;IACA;AACA;AACA;IACAsD;MACA;MACA;QACA;QACA;UACA;UACA;UACA;YACA;cACAZ;cACAnD;cACAL;YACA;YACAqB;YACA;YACAuB,iCACA,IACA;UACA;QACA;MACA;IACA;IACA;IACAyB;MAAA;MACA;MACA;MAEA;MACA,mBACA,8DACA,SACA;MACAC,yBACA;QACAd;QACAnD;QACAQ;MACA,GACA4C,sEACA;MAEA;QACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACApC,sBACA;YACAmC;UACA,GACAC,yCACA,MACAtB,uBACA;QACA;UACA;UACAd,sBACA;YACAmC;UACA,GACAC,yCACA,MACAtB,uBACA;QACA;MACA;MACA;QACA;QACA;QACA;MACA;MACA;IACA;IACA;AACA;AACA;IACAoC;MACA;IACA;EACA;AACA", "names": ["fieldController", "Common", "name", "components", "SunFlowDialog", "data", "iframeUrl", "pathSrc", "extendRef", "is<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "projectName", "flowConfig", "visible", "componentProps", "title", "top", "width", "module_id", "task_id", "childFlowUrl", "indcDialog", "url", "systemNum", "watch", "console", "mounted", "iframeWin", "pathIndex", "routeMessage", "path", "system_no", "systemDic", "commonBlank", "systemNo", "value", "dictionary", "localStorage", "store", "depl<PERSON><PERSON><PERSON><PERSON>", "before<PERSON>ath", "window", "<PERSON><PERSON><PERSON><PERSON>", "clearTimeout", "methods", "handleClose", "getDictionaryData", "parameterList", "fieldTime", "operType", "JSON", "resolve", "postMess", "that", "dictionaryPUnfiy", "dictionaryP", "childSystemNo", "commonData", "dictionaryTree", "item", "user", "common", "type", "process", "postParam", "getChildData", "key", "evt", "indcNo", "indcType", "indcUrl", "screenfull", "location", "dialogFlow", "resize<PERSON><PERSON>ler", "dialogFlowClose", "aosIframeWin", "changeVisible"], "sourceRoot": "src/layout/extend", "sources": ["index.vue"], "sourcesContent": ["<!--\n* 外系统代码入口\n-->\n<template>\n  <div>\n    <iframe\n      :ref=\"extendRef\"\n      class=\"extendIframe\"\n      :src=\"iframeUrl\"\n      frameborder=\"0\"\n    />\n    <!-- 流程详情弹框 -->\n    <!-- 调用流程弹框 begin -->\n    <sun-flow-dialog\n      v-if=\"flowConfig.visible\"\n      ref=\"flowDialog\"\n      :dialog-config=\"flowConfig\"\n      @dialogClose=\"dialogFlowClose\"\n    />\n    <!-- 查看 指标详情 血缘分析弹框弹框 -->\n    <el-dialog\n      v-if=\"indcDialog.visible\"\n      :title=\"indcDialog.title\"\n      :visible.sync=\"indcDialog.visible\"\n      width=\"100%\"\n      :fullscreen=\"true\"\n      :before-close=\"handleClose\"\n    >\n      <iframe\n        id=\"idc\"\n        :src=\"indcDialog.url\"\n        frameborder=\"0\"\n        width=\"100%\"\n        :height=\"indcDialog.height\"\n      />\n      <span slot=\"footer\" class=\"dialog-footer\">\n        <el-button @click=\"indcDialog.visible = false\">取 消</el-button>\n        <el-button\n          type=\"primary\"\n          @click=\"indcDialog.visible = false\"\n        >确 定</el-button>\n      </span>\n    </el-dialog>\n  </div>\n</template>\n\n<script>\n// import { TagsView } from '@/layout/components'\nimport SunFlowDialog from '@/components/Dialog/SunFlowDialog' // 流程详情弹窗\nimport store from '@/store'\nimport { commonBlank } from '@/utils/common'\nimport { dictionaryFieds } from '@/utils/dictionary'\nimport screenfull from 'screenfull'\nimport { Common } from '@/api'\nconst {\n  fieldController // 同步字典\n} = Common\nexport default {\n  name: 'Extend',\n  components: { SunFlowDialog },\n  data() {\n    return {\n      iframeUrl: '',\n      pathSrc: '', // 子工程地址\n      extendRef: 'extendRef' + new Date().getTime(),\n      isChildrenReady: false,\n      projectName: '', // 子项目名称\n      flowConfig: {\n        // 流程模板调用弹窗\n        visible: false,\n        componentProps: {\n          // 弹出框属性\n          title: '', // 弹出框标题\n          top: '0px',\n          width: '100%' // 当前弹出框宽度 默认80%\n        },\n        module_id: '', // 模板id\n        task_id: '', // 流程id\n        childFlowUrl: 'static/html/flow/workflow.html' // 子工程流程页面url\n      },\n      indcDialog: {\n        // 指标弹框\n        visible: false,\n        title: '',\n        url: ''\n      },\n      systemNum: ''\n    }\n  },\n  watch: {\n    isChildrenReady(val) {\n      if (val) {\n        console.log('isChildrenReady', val)\n        this.postMess()\n      }\n    }\n  },\n  mounted() {\n    const currView = this.$store.getters.cachedViews.pop()\n    const mapFrame = this.$refs[this.extendRef]\n    const iframeWin = mapFrame.contentWindow\n    this.$store.commit('app/CHANGE_LOADING', true)\n    this.$store.commit('flowPath/SET_EXTEND_LIST', {\n      title: currView,\n      iframeWin: iframeWin\n    })\n    this.$nextTick().then(() => {\n      this.$router.onReady(async() => {\n        const pathIndex = this.$route.matched.length - 1\n        const routeMessage = this.$route.matched[pathIndex].props.default\n        const { path, system_no, deplName, query_conditions } = routeMessage\n        this.$store.commit('common/SET_FLOW_CONDITION', query_conditions)\n        this.systemNum = system_no // 系统号\n        // 获取外系统菜单 数据字典\n        if (!(system_no in this.$store.state.common.dictionaryLet)) {\n          const systemDic = JSON.parse(localStorage.getItem(this.systemNum))\n          if (commonBlank(systemDic)) {\n            await this.getDictionaryData(system_no)\n          } else {\n            this.$store.commit('common/ADD_DICTIONARYLET', {\n              systemNo: this.systemNum,\n              value: systemDic,\n              dictionary: JSON.parse(\n                localStorage.getItem(this.systemNum + 'dictionary')\n              )\n            })\n          }\n        }\n        // http://172.1.11.51:9528/redirect/report/#/report/dataSet\n        // http://172.1.1.21/dopUnify/redirect/report/#/report/dataSet\n        this.pathSrc = path\n        if (!commonBlank(path)) {\n          store.commit('user/SET_ROUTE_M', routeMessage)\n          if (!commonBlank(deplName)) {\n            // 有做代理\n            const deplNamelabel = dictionaryFieds('EXT_SYS_DEPL_NAME').find(\n              (item) => {\n                return item.value === deplName\n              }\n            ).label\n            let beforePath = ''\n            // 判断是ie还是chrome\n            if (navigator.userAgent.indexOf('Chrome') !== -1) {\n              // 当前浏览器是 Chrome\n              beforePath = `${window.location.origin}`\n            } else if (navigator.userAgent.indexOf('Trident') !== -1) {\n              // 当前浏览器是 IE\n              beforePath = window.location.href.slice(\n                0,\n                window.location.href.indexOf(window.location.pathname)\n              )\n            }\n\n            if (path.indexOf('static/html') !== -1) {\n              // 兼容老基线sunaos\n              // http://172.1.1.101/dopUnify/redirect/unify/iframe.html\n              this.iframeUrl = `${beforePath}${window.location.pathname}redirect/${deplNamelabel}/iframe.html`\n              // this.iframeUrl = `http://172.1.10.185/dopUnify/redirect/${deplNamelabel}/iframe.html`\n            } else {\n              this.iframeUrl = `${beforePath}${window.location.pathname}redirect/${deplNamelabel}/#/${path}`\n              // this.iframeUrl = `http://172.16.15.8/dopUnify/redirect/${deplNamelabel}/#/${path}`\n            }\n          } else {\n            // 本地测试\n            this.iframeUrl = path\n          }\n          // this.extendRef = this.extendRef + this.$route.path\n          // console.log('this.iframeUrl:', this.iframeUrl)\n        }\n        this.postParam()\n      })\n    })\n  },\n  // 销毁对应自定义事件\n  beforeDestroy() {\n    clearTimeout(this.timeout1)\n    clearTimeout(this.timeout2)\n    clearTimeout(this.timeout3)\n  },\n  methods: {\n    /**\n     * 关闭弹框\n     */\n    handleClose() {\n      this.indcDialog.visible = false\n    },\n    /**\n     * 获取数据字典\n     */\n    getDictionaryData(systemNo) {\n      return new Promise((resolve) => {\n        const msg = {\n          parameterList: [''],\n          fieldTime: '',\n          operType: '1', // 门户操作标识\n          systemNo: systemNo\n        }\n        fieldController(msg).then((res) => {\n          // 新增外系统字典\n          localStorage.setItem(systemNo, JSON.stringify(res.retMap[systemNo]))\n          localStorage.setItem(\n            systemNo + 'dictionary',\n            JSON.stringify({ [systemNo]: res.retMap.dictionary[systemNo] })\n          )\n          this.$store.commit('common/ADD_DICTIONARYLET', {\n            systemNo,\n            value: res.retMap[systemNo],\n            dictionary: res.retMap.dictionary\n          })\n          resolve()\n        })\n      })\n    },\n    postMess() {\n      const that = this\n      // setTimeout(() => {\n      const mapFrame = this.$refs[this.extendRef]\n      that.$store.commit('app/CHANGE_LOADING', false)\n      // mapFrame.onload = function() {\n      //   console.log('mapFramemapFramemapFrame--onload')\n      //   that.$store.commit('app/CHANGE_LOADING', false)\n      // }\n      const iframeWin = mapFrame.contentWindow\n\n      // 合并父子工程数据字典 begin\n      let commonData = JSON.parse(JSON.stringify(this.$store.state.common))\n      if (this.systemNum !== 'UNIFY') {\n        const dictionaryP = JSON.parse(\n          JSON.stringify(this.$store.state.common.dictionaryLet)\n        )\n        let dictionaryPUnfiy = {}\n        for (const key in dictionaryP) {\n          if (key === this.systemNum) {\n            dictionaryPUnfiy = {\n              ...dictionaryP['UNIFY'],\n              ...dictionaryP[key]\n            }\n          }\n        }\n        let childSystemNo = ''\n        // 单独为指标修改传值模式\n        if (this.projectName === 'indicator') {\n          childSystemNo = 'AOS'\n          commonData.dictionaryLet = Object.assign(\n            {},\n            commonData.dictionaryLet,\n            { [childSystemNo]: dictionaryPUnfiy }\n          )\n        } else {\n          childSystemNo = 'UNIFY'\n          commonData.dictionaryLet = Object.assign(\n            {},\n            commonData.dictionaryLet,\n            {\n              [childSystemNo]: dictionaryPUnfiy\n            }\n          )\n          commonData = Object.assign({}, commonData, {\n            dictionaryTree: commonData.dictionaryTree[this.systemNum]\n          })\n        }\n\n        if (childSystemNo === 'UNIFY') {\n          for (const item in commonData.dictionaryLet) {\n            if (\n              item !== 'UNIFY' &&\n              item !== 'dictionary' &&\n              item !== 'fieldTime'\n            ) {\n              delete commonData.dictionaryLet[item]\n            }\n          }\n        } else {\n          if (this.projectName !== 'indicator') {\n            for (const item in commonData.dictionaryLet) {\n              if (\n                item !== this.systemNum &&\n                item !== 'dictionary' &&\n                item !== 'fieldTime'\n              ) {\n                delete commonData.dictionaryLet[item] // 门户工程需要用到门户‘部署工程’的字典码，不能删除\n              }\n            }\n          }\n        }\n      }\n      // 合并父子工程数据字典 end\n\n      if (iframeWin) {\n        const msg = {\n          user: this.$store.state.user,\n          common: commonData,\n          type: 'dopUnify',\n          projectName: this.projectName\n        }\n        console.log('合并父子工程数据字典', window.location.origin)\n\n        iframeWin.postMessage(\n          // 向子工程发送\n          JSON.parse(JSON.stringify(msg)),\n          process.env.NODE_ENV === 'development' ? '*' : window.location.origin\n        )\n      }\n      // }, 1000)\n    },\n    /**\n     * 向iframe子页面发送消息\n     */\n    postParam() {\n      // window.addEventListener('message', this.getChildData)\n      window.onmessage = this.getChildData\n    },\n    /**\n     *\n     * 获取子工程的数据\n     */\n    getChildData: function(evt) {\n      // console.log('接收子工程数据', evt, 6666, evt.data.type)\n      // 接收子工程数据\n      // 若子页面已经加载好通知父工程修改了isChildrenReady的状态\n      console.log('获取子工程的数据', evt)\n      if (evt.data.type === 'dopUnify') {\n        this.isChildrenReady = true\n        this.projectName = evt.data.projectName\n      } else if (evt.data.type === 'saveData') {\n        // 子工程的数据存储\n        this.$store.commit('common/SET_SAVE_DATA', {\n          key: evt.data.projectName,\n          data: evt.data.data\n        })\n      } else if (evt.data.type === 'unifyFlow') {\n        // 流程弹出框\n        this.dialogFlow(evt.data.data)\n      } else if (\n        evt.data.type === 'closeCurrentTab' ||\n        evt.data.type === 'closeCurrentDialog'\n      ) {\n        // 关闭当前页\n        // window.closeCurrentTab()\n        for (const i in this.$store.getters.visitedViews) {\n          const temp = this.$store.getters.visitedViews[i]\n          if (temp.path === this.$route.path) {\n            this.$store.commit('common/Test', temp)\n          }\n        }\n      } else if (\n        evt.data.type === 'closeFlowDialog' &&\n        evt.data.projectName !== 'sunaos'\n      ) {\n        this.dialogFlowClose()\n      } else if (evt.data.type === 'openIndc') {\n        // 查看指标明细\n        const type =\n          evt.data.data.title.indexOf('血缘') !== -1\n            ? evt.data.data.indcType !== '2'\n              ? 'blood'\n              : 'bloodNew'\n            : 'checkDetail'\n        const path = 'indicators/indicators/cycle/indicators/cycle/' + type\n        this.indcDialog.url = `${window.location.origin}${window.location.pathname}redirect/dapIndicator/#/${path}`\n        this.indcDialog.height = window.innerHeight - 55 - 62 - 32 - 6\n        this.indcDialog.title = evt.data.data.title\n        this.indcDialog.visible = true\n\n        this.timeout3 = setTimeout(() => {\n          const mapFrame = document.querySelector('#idc')\n          const iframeWin = mapFrame.contentWindow\n          if (iframeWin) {\n            const msg = {\n              user: this.$store.state.user,\n              common: this.$store.state.common,\n              indcNo: evt.data.data.indcNos,\n              indcType: evt.data.data.indcType,\n              indcUrl:\n                window.location.origin +\n                window.location.pathname +\n                'redirect/dapVisuality',\n              type: 'dopUnify',\n              projectName: 'indicator'\n            }\n            iframeWin.postMessage(\n              // 向子工程发送\n              JSON.parse(JSON.stringify(msg)),\n              process.env.NODE_ENV === 'development'\n                ? '*'\n                : window.location.origin\n            )\n          }\n        }, 1000)\n      } else if (evt.data.type === 'openDap') {\n        // 可视化\n        const path = `report/preview?${encodeURIComponent(\n          JSON.stringify(evt.data.data.query)\n        )}`\n        // const path = `report/dataSet`\n        this.indcDialog.url = `${window.location.origin}${window.location.pathname}redirect/dapVisuality/#/${path}`\n        // this.indcDialog.url = `http://localhost:5003/#/report/preview?${encodeURIComponent(\n        //   JSON.stringify(evt.data.data.query)\n        // )}`\n        // this.indcDialog.url = `http://localhost:5003/#/report/dataSet`\n\n        this.indcDialog.height = window.innerHeight - 55 - 62 - 32 - 6\n        this.indcDialog.title = evt.data.data.title\n        this.indcDialog.visible = true\n        this.timeout1 = setTimeout(() => {\n          const mapFrame = document.querySelector('#idc')\n          const iframeWin = mapFrame.contentWindow\n          if (iframeWin) {\n            const msg = {\n              user: this.$store.state.user,\n              common: this.$store.state.common,\n              type: 'dopUnify',\n              projectName: 'visual'\n            }\n            iframeWin.postMessage(\n              // 向子工程发送\n              JSON.parse(JSON.stringify(msg)),\n              process.env.NODE_ENV === 'development'\n                ? '*'\n                : window.location.origin\n            )\n          }\n        }, 1000)\n      } else if (evt.data.type === 'fullscreen') {\n        // 老基线-考试管理-全屏\n        if (!screenfull.isFullscreen && evt.data.data.operation === 'open') {\n          // 全屏\n          this.resizeHandler()\n        } else if (\n          screenfull.isFullscreen &&\n          evt.data.data.operation === 'close'\n        ) {\n          // 退出全屏\n          screenfull.exit()\n        }\n      } else if (evt.data.type === 'goLogin') {\n        // 如果基线token  门户跳转登录页\n        this.$store.dispatch('user/resetToken').then(() => {\n          location.reload()\n        })\n      } else if (evt.data.type === 'openBaselinesPage') {\n        const datas = evt.data.data\n        const router = this.$store.getters.permission_routes // 所有菜单\n        const mhMenuId = this.$store.getters.menuArr.find((item) => {\n          return (\n            item.menu_name.indexOf(datas.first) > -1 && item.menu_attr === '1'\n          )\n        })\n        const ggMenuId = this.$store.getters.menuArr.find((item) => {\n          return (\n            item.menu_name.indexOf(datas.second) > -1 && item.menu_attr === '2'\n          )\n        })\n        const cqMenuId = this.$store.getters.menuArr.find((item) => {\n          return (\n            item.menu_name.indexOf(datas.third) > -1 && item.menu_attr === '2'\n          )\n        })\n        const unifyR = router.find((item) => {\n          return item.menu_id === mhMenuId.menu_id\n        })\n        const noticeR = unifyR.children.find((item) => {\n          return item.menu_id === ggMenuId.menu_id\n        })\n        const queryR = noticeR.children.find((item) => {\n          return item.menu_id === cqMenuId.menu_id\n        })\n        this.$router.push(unifyR.path + '/' + noticeR.path + '/' + queryR.path)\n      }\n    },\n\n    /**\n     * 流程弹出框：flowConfig\n     */\n    dialogFlow(data) {\n      if (data.flowData) {\n        this.flowConfig.flowData = data.flowData\n      }\n      const { module_id, componentProps, childFlowUrl } = data\n      this.flowConfig.componentProps.title = componentProps.title\n      this.flowConfig.module_id = module_id\n      this.flowConfig.visible = true\n      this.flowConfig.childFlowUrl = childFlowUrl\n    },\n    /**\n     * 判断是否全屏: 基线-考试管理-我的考试\n     */\n    resizeHandler() {\n      // 全屏下监控是否按键了ESC\n      if (!screenfull.isFullscreen) {\n        // 全屏下按键esc后要执行的动作\n        if (this.pathSrc.indexOf('static/html/exam/exam/myExam.html') !== -1) {\n          const mapFrame = this.$refs[this.extendRef]\n          const iframeWin = mapFrame.contentWindow\n          if (iframeWin) {\n            const msg = {\n              type: 'dopUnify',\n              projectName: 'exam',\n              data: ''\n            }\n            iframeWin.postMessage(\n              // 向子工程发送\n              JSON.parse(JSON.stringify(msg)),\n              '*'\n            )\n          }\n        }\n      }\n    },\n    // 流程弹窗关闭\n    dialogFlowClose() {\n      const mapFrame = this.$refs[this.extendRef]\n      const iframeWin = mapFrame.contentWindow\n\n      const iframeInfoData = this.flowConfig\n      const aosIframeWin =\n        this.$refs['flowDialog'].$refs['refDialog'].$el.querySelector(\n          'iframe'\n        ).contentWindow\n      aosIframeWin.postMessage(\n        {\n          type: 'dopUnify',\n          projectName: 'resetFlow',\n          task_id: this.flowConfig.task_id\n        },\n        process.env.NODE_ENV === 'development' ? '*' : window.location.origin\n      )\n\n      if (iframeInfoData.flowData) {\n        if (iframeInfoData.flowData.approveType) {\n          // \t// 指标下线\n          // if (iframeInfoData.alldata.flowData.approveType =='2') {\n          // \tmainCont.flowDealDetailShow(iframeInfoData.alldata.flowData);\n          // }else{\n          // \t\t// 指标发布\n          // \tmainCont.indPublish(iframeInfoData.alldata.flowData);\n          // }\n          iframeWin.postMessage(\n            {\n              type: 'closeDialog-ind'\n            },\n            process.env.NODE_ENV === 'development'\n              ? '*'\n              : window.location.origin\n          )\n        } else {\n          // mainCont.pageApply(iframeInfoData.alldata.flowData)\n          iframeWin.postMessage(\n            {\n              type: 'closeDialog-vis'\n            },\n            process.env.NODE_ENV === 'development'\n              ? '*'\n              : window.location.origin\n          )\n        }\n      }\n      this.timeout2 = setTimeout(() => {\n        this.$store.state.common.flow.module_id = '' // 置空流程ID\n        // this.$store.state.common.flow.workflow_dataStr.flow_type = 'add'\n        this.flowConfig.visible = false\n      }, 1000)\n      // window.removeEventListener('message', this.getChildData)\n    },\n    /**\n     * 指标下线历史，发布历史详情弹框关闭\n     */\n    changeVisible() {\n      this.dialogDetail.visible = false\n    }\n  }\n}\n</script>\n\n<style lang=\"scss\" scoped>\n.extendIframe {\n  width: 100%;\n  height: 100%;\n  position: absolute;\n  top: 0;\n  bottom: 0;\n  overflow: hidden;\n}\n.offline {\n  margin-left: 18rem;\n  ::v-deep {\n    .tableContent {\n      min-height: 50rem;\n      overflow: auto;\n    }\n  }\n}\n</style>\n"]}]}