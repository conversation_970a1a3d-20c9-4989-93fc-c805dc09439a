{"remainingRequest": "D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\node_modules\\babel-loader\\lib\\index.js!D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\node_modules\\eslint-loader\\index.js??ref--13-0!D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\src\\components\\Layout\\ThemePicker\\indexColor.js", "dependencies": [{"path": "D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\src\\components\\Layout\\ThemePicker\\indexColor.js", "mtime": 1686019810044}, {"path": "D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\babel.config.js", "mtime": 1686019818359}, {"path": "D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\.babelrc", "mtime": 1705285078668}, {"path": "D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\node_modules\\eslint-loader\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["getThemeCluster", "theme", "type", "tintColor", "color", "tint", "red", "parseInt", "slice", "green", "blue", "join", "Math", "round", "toString", "shadeColor", "shade", "clusters", "i", "push", "Number", "toFixed", "length", "for<PERSON>ach", "index", "document", "body", "style", "setProperty", "updateStyle", "oldCluster", "newCluster", "newStyle", "replace", "RegExp", "trim", "ElementUiCSS", "updateThemeColor", "val", "ORIGINAL_THEME", "ThemeCode", "themeCluster", "<PERSON><PERSON><PERSON><PERSON>", "id", "originalCluster", "styleTag", "getElementById", "createElement", "setAttribute", "head", "append<PERSON><PERSON><PERSON>", "innerText", "<PERSON><PERSON><PERSON><PERSON>"], "sources": ["D:/1_Project/XYD_Project/dop-4.0/dop-4.1-qianduan-unify/dopUnify/src/components/Layout/ThemePicker/indexColor.js"], "sourcesContent": ["/**\n * 主题色配置\n * 入参：67c23a\n * 结果：['67c23a', '103,194,58', '#76c84e', '#85ce61', '#95d475', '#a4da89', '#b3e19d', '#c2e7b0', '#d1edc4', '#e1f3d8', '#f0f9eb', '#5daf34']\n */\nfunction getThemeCluster(theme, type = '') {\n  const tintColor = (color, tint) => {\n    let red = parseInt(color.slice(0, 2), 16)\n    let green = parseInt(color.slice(2, 4), 16)\n    let blue = parseInt(color.slice(4, 6), 16)\n\n    if (tint === 0) {\n      // when primary color is in its rgb space\n      return [red, green, blue].join(',')\n    } else {\n      red += Math.round(tint * (255 - red))\n      green += Math.round(tint * (255 - green))\n      blue += Math.round(tint * (255 - blue))\n\n      red = red.toString(16)\n      green = green.toString(16)\n      blue = blue.toString(16)\n\n      return `#${red}${green}${blue}`\n    }\n  }\n\n  const shadeColor = (color, shade) => {\n    let red = parseInt(color.slice(0, 2), 16)\n    let green = parseInt(color.slice(2, 4), 16)\n    let blue = parseInt(color.slice(4, 6), 16)\n\n    red = Math.round((1 - shade) * red)\n    green = Math.round((1 - shade) * green)\n    blue = Math.round((1 - shade) * blue)\n\n    red = red.toString(16)\n    green = green.toString(16)\n    blue = blue.toString(16)\n\n    return `#${red}${green}${blue}`\n  }\n\n  const clusters = [theme]\n  for (let i = 0; i <= 9; i++) {\n    clusters.push(tintColor(theme, Number((i / 10).toFixed(2))))\n  }\n  clusters.push(shadeColor(theme, 0.1))\n  // 这行代码是我自己后面的其他优化所需，可删\n  if (type && type === 'new' && clusters.length) {\n    clusters.forEach((theme, index) => {\n      if (index > 1) {\n        document.body.style.setProperty(`--theme-weaken-color-${index}`, clusters[index]) // 逐渐淡化颜色\n      }\n    })\n    // console.log('主题色系-------------------', clusters)\n  }\n  return clusters\n}\n\n/**\n * @param style 老的css样式代码\n * @param oldCluster 老的一些列主题色  待替换\n * @param newCluster 新的一系列主题色  替换成\n *\n * @returns newStyle 新的 css样式代码 替换后的\n */\nfunction updateStyle(style, oldCluster, newCluster) {\n  let newStyle = style\n  oldCluster.forEach((color, index) => {\n    // 将老颜色替换成新颜色\n    newStyle = newStyle.replace(new RegExp(color, 'ig'), newCluster[index].trim()) // 全局替换 不区分大小写 去掉多余空格\n  })\n  return newStyle\n}\n\n/**\n * main\n */\nimport ElementUiCSS from './ElementUiCSS.js' // 就是上文说的默认样式 index.css; (字符串)\nexport const updateThemeColor = function(val) {\n  if (typeof val !== 'string' || val.length === 0) return\n  const ORIGINAL_THEME = '#409EFF' // default color (element ui的默认主题色，所有我们根据这个去改)\n  const ThemeCode = ElementUiCSS\n    .replace(/@font-face{[^}]+}/g, '') // 去掉字体样式\n    .replace(/.el-icon-[a-zA-Z0-9-:^]+before{content:\"[^}]+}/g, '') // 去掉图标样式\n  // require('element-ui/lib/theme-chalk/index.css')\n\n  // try {\n  //   console.log('ThemeCode', ThemeCode)\n  //   console.log('ThemeCode', String(ThemeCode))\n  //   console.log('ThemeCode', ThemeCode.innerText)\n  // } catch (error) {\n  //   console.log(error)\n  // }\n  // 得到一系列 主题色颜色 (我们需要的颜色 '产出')\n  const themeCluster = getThemeCluster(val.replace('#', ''), 'new')\n  /**\n   * 入参：'chalk'(旧css代码), 'chalk-style'(style的id)\n   * 直接 将老的 css 代码里 待改的旧颜色改成 新颜色 然后将新的样式 插入到head标签里\n   */\n  const getHandler = id => {\n    return () => {\n      // 得到一系列 主题色颜色 (原始的一些列颜色  待改)\n      const originalCluster = getThemeCluster(ORIGINAL_THEME.replace('#', ''))\n      const newStyle = updateStyle(ThemeCode, originalCluster, themeCluster)\n      // console.log('newStyle', newStyle)\n\n      let styleTag = document.getElementById(id)\n      if (!styleTag) {\n        styleTag = document.createElement('style')\n        styleTag.setAttribute('id', id)\n        document.head.appendChild(styleTag)\n      }\n      styleTag.innerText = newStyle\n    }\n  }\n  const chalkHandler = getHandler('chalk-style')\n  chalkHandler()\n}\n"], "mappings": ";;;;;;;;;;;;;AAAA;AACA;AACA;AACA;AACA;AACA,SAASA,eAAe,CAACC,KAAK,EAAa;EAAA,IAAXC,IAAI,uEAAG,EAAE;EACvC,IAAMC,SAAS,GAAG,SAAZA,SAAS,CAAIC,KAAK,EAAEC,IAAI,EAAK;IACjC,IAAIC,GAAG,GAAGC,QAAQ,CAACH,KAAK,CAACI,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC;IACzC,IAAIC,KAAK,GAAGF,QAAQ,CAACH,KAAK,CAACI,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC;IAC3C,IAAIE,IAAI,GAAGH,QAAQ,CAACH,KAAK,CAACI,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC;IAE1C,IAAIH,IAAI,KAAK,CAAC,EAAE;MACd;MACA,OAAO,CAACC,GAAG,EAAEG,KAAK,EAAEC,IAAI,CAAC,CAACC,IAAI,CAAC,GAAG,CAAC;IACrC,CAAC,MAAM;MACLL,GAAG,IAAIM,IAAI,CAACC,KAAK,CAACR,IAAI,IAAI,GAAG,GAAGC,GAAG,CAAC,CAAC;MACrCG,KAAK,IAAIG,IAAI,CAACC,KAAK,CAACR,IAAI,IAAI,GAAG,GAAGI,KAAK,CAAC,CAAC;MACzCC,IAAI,IAAIE,IAAI,CAACC,KAAK,CAACR,IAAI,IAAI,GAAG,GAAGK,IAAI,CAAC,CAAC;MAEvCJ,GAAG,GAAGA,GAAG,CAACQ,QAAQ,CAAC,EAAE,CAAC;MACtBL,KAAK,GAAGA,KAAK,CAACK,QAAQ,CAAC,EAAE,CAAC;MAC1BJ,IAAI,GAAGA,IAAI,CAACI,QAAQ,CAAC,EAAE,CAAC;MAExB,kBAAWR,GAAG,SAAGG,KAAK,SAAGC,IAAI;IAC/B;EACF,CAAC;EAED,IAAMK,UAAU,GAAG,SAAbA,UAAU,CAAIX,KAAK,EAAEY,KAAK,EAAK;IACnC,IAAIV,GAAG,GAAGC,QAAQ,CAACH,KAAK,CAACI,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC;IACzC,IAAIC,KAAK,GAAGF,QAAQ,CAACH,KAAK,CAACI,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC;IAC3C,IAAIE,IAAI,GAAGH,QAAQ,CAACH,KAAK,CAACI,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC;IAE1CF,GAAG,GAAGM,IAAI,CAACC,KAAK,CAAC,CAAC,CAAC,GAAGG,KAAK,IAAIV,GAAG,CAAC;IACnCG,KAAK,GAAGG,IAAI,CAACC,KAAK,CAAC,CAAC,CAAC,GAAGG,KAAK,IAAIP,KAAK,CAAC;IACvCC,IAAI,GAAGE,IAAI,CAACC,KAAK,CAAC,CAAC,CAAC,GAAGG,KAAK,IAAIN,IAAI,CAAC;IAErCJ,GAAG,GAAGA,GAAG,CAACQ,QAAQ,CAAC,EAAE,CAAC;IACtBL,KAAK,GAAGA,KAAK,CAACK,QAAQ,CAAC,EAAE,CAAC;IAC1BJ,IAAI,GAAGA,IAAI,CAACI,QAAQ,CAAC,EAAE,CAAC;IAExB,kBAAWR,GAAG,SAAGG,KAAK,SAAGC,IAAI;EAC/B,CAAC;EAED,IAAMO,QAAQ,GAAG,CAAChB,KAAK,CAAC;EACxB,KAAK,IAAIiB,CAAC,GAAG,CAAC,EAAEA,CAAC,IAAI,CAAC,EAAEA,CAAC,EAAE,EAAE;IAC3BD,QAAQ,CAACE,IAAI,CAAChB,SAAS,CAACF,KAAK,EAAEmB,MAAM,CAAC,CAACF,CAAC,GAAG,EAAE,EAAEG,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EAC9D;EACAJ,QAAQ,CAACE,IAAI,CAACJ,UAAU,CAACd,KAAK,EAAE,GAAG,CAAC,CAAC;EACrC;EACA,IAAIC,IAAI,IAAIA,IAAI,KAAK,KAAK,IAAIe,QAAQ,CAACK,MAAM,EAAE;IAC7CL,QAAQ,CAACM,OAAO,CAAC,UAACtB,KAAK,EAAEuB,KAAK,EAAK;MACjC,IAAIA,KAAK,GAAG,CAAC,EAAE;QACbC,QAAQ,CAACC,IAAI,CAACC,KAAK,CAACC,WAAW,gCAAyBJ,KAAK,GAAIP,QAAQ,CAACO,KAAK,CAAC,CAAC,EAAC;MACpF;IACF,CAAC,CAAC;IACF;EACF;;EACA,OAAOP,QAAQ;AACjB;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASY,WAAW,CAACF,KAAK,EAAEG,UAAU,EAAEC,UAAU,EAAE;EAClD,IAAIC,QAAQ,GAAGL,KAAK;EACpBG,UAAU,CAACP,OAAO,CAAC,UAACnB,KAAK,EAAEoB,KAAK,EAAK;IACnC;IACAQ,QAAQ,GAAGA,QAAQ,CAACC,OAAO,CAAC,IAAIC,MAAM,CAAC9B,KAAK,EAAE,IAAI,CAAC,EAAE2B,UAAU,CAACP,KAAK,CAAC,CAACW,IAAI,EAAE,CAAC,EAAC;EACjF,CAAC,CAAC;;EACF,OAAOH,QAAQ;AACjB;;AAEA;AACA;AACA;AACA,OAAOI,YAAY,MAAM,mBAAmB,EAAC;AAC7C,OAAO,IAAMC,gBAAgB,GAAG,SAAnBA,gBAAgB,CAAYC,GAAG,EAAE;EAC5C,IAAI,OAAOA,GAAG,KAAK,QAAQ,IAAIA,GAAG,CAAChB,MAAM,KAAK,CAAC,EAAE;EACjD,IAAMiB,cAAc,GAAG,SAAS,EAAC;EACjC,IAAMC,SAAS,GAAGJ,YAAY,CAC3BH,OAAO,CAAC,oBAAoB,EAAE,EAAE,CAAC,CAAC;EAAA,CAClCA,OAAO,CAAC,iDAAiD,EAAE,EAAE,CAAC,EAAC;EAClE;;EAEA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA,IAAMQ,YAAY,GAAGzC,eAAe,CAACsC,GAAG,CAACL,OAAO,CAAC,GAAG,EAAE,EAAE,CAAC,EAAE,KAAK,CAAC;EACjE;AACF;AACA;AACA;EACE,IAAMS,UAAU,GAAG,SAAbA,UAAU,CAAGC,EAAE,EAAI;IACvB,OAAO,YAAM;MACX;MACA,IAAMC,eAAe,GAAG5C,eAAe,CAACuC,cAAc,CAACN,OAAO,CAAC,GAAG,EAAE,EAAE,CAAC,CAAC;MACxE,IAAMD,QAAQ,GAAGH,WAAW,CAACW,SAAS,EAAEI,eAAe,EAAEH,YAAY,CAAC;MACtE;;MAEA,IAAII,QAAQ,GAAGpB,QAAQ,CAACqB,cAAc,CAACH,EAAE,CAAC;MAC1C,IAAI,CAACE,QAAQ,EAAE;QACbA,QAAQ,GAAGpB,QAAQ,CAACsB,aAAa,CAAC,OAAO,CAAC;QAC1CF,QAAQ,CAACG,YAAY,CAAC,IAAI,EAAEL,EAAE,CAAC;QAC/BlB,QAAQ,CAACwB,IAAI,CAACC,WAAW,CAACL,QAAQ,CAAC;MACrC;MACAA,QAAQ,CAACM,SAAS,GAAGnB,QAAQ;IAC/B,CAAC;EACH,CAAC;EACD,IAAMoB,YAAY,GAAGV,UAAU,CAAC,aAAa,CAAC;EAC9CU,YAAY,EAAE;AAChB,CAAC"}]}