{"remainingRequest": "D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\node_modules\\babel-loader\\lib\\index.js!D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\node_modules\\eslint-loader\\index.js??ref--13-0!D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\src\\views\\system\\externalManage\\system\\info.js", "dependencies": [{"path": "D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\src\\views\\system\\externalManage\\system\\info.js", "mtime": 1686019809076}, {"path": "D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\babel.config.js", "mtime": 1686019818359}, {"path": "D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\.babelrc", "mtime": 1705285078668}, {"path": "D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\node_modules\\eslint-loader\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:aW1wb3J0IHsgZGljdGlvbmFyeUZpZWRzIH0gZnJvbSAnQC91dGlscy9kaWN0aW9uYXJ5JzsgLy8g5a2X5YW4Ci8vIOihqOWNlQpleHBvcnQgdmFyIGNvbmZpZyA9IGZ1bmN0aW9uIGNvbmZpZyh0aGF0KSB7CiAgcmV0dXJuIHsKICAgIHN5c3RlbV9uYW1lOiB7CiAgICAgIGNvbXBvbmVudDogJ2lucHV0JywKICAgICAgbGFiZWw6ICfns7vnu5/lkI3np7AnLAogICAgICBjb2xTcGFuOiA4LAogICAgICBuYW1lOiAnc3lzdGVtX25hbWUnLAogICAgICBjb25maWc6IHsKICAgICAgICAvLyBmb3JtLWl0ZW0g6YWN572uCiAgICAgICAgcnVsZXM6IFt7CiAgICAgICAgICBtaW46IDAsCiAgICAgICAgICBtYXg6IDMwLAogICAgICAgICAgbWVzc2FnZTogJ+ivt+acgOWkmuWhq+WGmTMw5Liq5a2X56ymJwogICAgICAgIH1dCiAgICAgIH0sCiAgICAgIGNvbXBvbmVudFByb3BzOiB7CiAgICAgICAgLy8gaW5wdXTnu4Tku7bphY3nva4KICAgICAgICBwbGFjZWhvbGRlcjogJ+aUr+aMgeaMieezu+e7n+WQjeensOaooeeziuafpeivoicsCiAgICAgICAgY2xlYXJhYmxlOiB0cnVlCiAgICAgIH0KICAgIH0sCiAgICBzeXN0ZW1fdHlwZTogewogICAgICBjb21wb25lbnQ6ICdzZWxlY3QnLAogICAgICBsYWJlbDogJ+ezu+e7n+exu+WeiycsCiAgICAgIGNvbFNwYW46IDgsCiAgICAgIG5hbWU6ICdzeXN0ZW1fdHlwZScsCiAgICAgIGNvbmZpZzoge30sCiAgICAgIGNvbXBvbmVudFByb3BzOiB7CiAgICAgICAgcGxhY2Vob2RsZXI6ICfor7fpgInmi6knLAogICAgICAgIGZpbHRlcmFibGU6IHRydWUsCiAgICAgICAgY2xlYXJhYmxlOiB0cnVlCiAgICAgIH0sCiAgICAgIG9wdGlvbnM6IGRpY3Rpb25hcnlGaWVkcygnRVhURVJOQUxfU1lTVEVNX1RZUEUnKQogICAgfQogIH07Cn07"}, {"version": 3, "names": ["dictionaryFieds", "config", "that", "system_name", "component", "label", "colSpan", "name", "rules", "min", "max", "message", "componentProps", "placeholder", "clearable", "system_type", "<PERSON><PERSON><PERSON>", "filterable", "options"], "sources": ["D:/1_Project/XYD_Project/dop-4.0/dop-4.1-qianduan-unify/dopUnify/src/views/system/externalManage/system/info.js"], "sourcesContent": ["import { dictionaryFieds } from '@/utils/dictionary' // 字典\r\n// 表单\r\nexport const config = (that) => ({\r\n  system_name: {\r\n    component: 'input',\r\n    label: '系统名称',\r\n    colSpan: 8,\r\n    name: 'system_name',\r\n    config: {\r\n      // form-item 配置\r\n      rules: [\r\n        { min: 0, max: 30, message: '请最多填写30个字符' }\r\n      ]\r\n    },\r\n    componentProps: {\r\n      // input组件配置\r\n      placeholder: '支持按系统名称模糊查询',\r\n      clearable: true\r\n    }\r\n  },\r\n  system_type: {\r\n    component: 'select',\r\n    label: '系统类型',\r\n    colSpan: 8,\r\n    name: 'system_type',\r\n    config: {},\r\n    componentProps: {\r\n      placehodler: '请选择',\r\n      filterable: true,\r\n      clearable: true\r\n    },\r\n    options: dictionaryFieds('EXTERNAL_SYSTEM_TYPE')\r\n  }\r\n})\r\n"], "mappings": "AAAA,SAASA,eAAe,QAAQ,oBAAoB,EAAC;AACrD;AACA,OAAO,IAAMC,MAAM,GAAG,SAATA,MAAM,CAAIC,IAAI;EAAA,OAAM;IAC/BC,WAAW,EAAE;MACXC,SAAS,EAAE,OAAO;MAClBC,KAAK,EAAE,MAAM;MACbC,OAAO,EAAE,CAAC;MACVC,IAAI,EAAE,aAAa;MACnBN,MAAM,EAAE;QACN;QACAO,KAAK,EAAE,CACL;UAAEC,GAAG,EAAE,CAAC;UAAEC,GAAG,EAAE,EAAE;UAAEC,OAAO,EAAE;QAAa,CAAC;MAE9C,CAAC;MACDC,cAAc,EAAE;QACd;QACAC,WAAW,EAAE,aAAa;QAC1BC,SAAS,EAAE;MACb;IACF,CAAC;IACDC,WAAW,EAAE;MACXX,SAAS,EAAE,QAAQ;MACnBC,KAAK,EAAE,MAAM;MACbC,OAAO,EAAE,CAAC;MACVC,IAAI,EAAE,aAAa;MACnBN,MAAM,EAAE,CAAC,CAAC;MACVW,cAAc,EAAE;QACdI,WAAW,EAAE,KAAK;QAClBC,UAAU,EAAE,IAAI;QAChBH,SAAS,EAAE;MACb,CAAC;MACDI,OAAO,EAAElB,eAAe,CAAC,sBAAsB;IACjD;EACF,CAAC;AAAA,CAAC"}]}