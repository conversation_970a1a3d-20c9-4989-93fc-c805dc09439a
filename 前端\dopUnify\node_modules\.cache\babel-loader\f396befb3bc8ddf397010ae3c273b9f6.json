{"remainingRequest": "D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\node_modules\\babel-loader\\lib\\index.js!D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\src\\components\\SunTransfer\\index.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\src\\components\\SunTransfer\\index.vue", "mtime": 1686019810185}, {"path": "D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\babel.config.js", "mtime": 1686019818359}, {"path": "D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\.babelrc", "mtime": 1705285078668}, {"path": "D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1655715099000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA0HA;AACA;AACA;EAAAA;AACA;AACA;EACAC;EACAC;IAAAC;EAAA;EACAC;EACAC;IACAC;MACAC;MACAC;IACA;IACAC;MACAF;MACAC;IACA;IACAE;MACAH;MACAC;MACA;IACA;;IACA;IACAG;MACAJ;MACAC;QACA;MACA;IACA;IACA;IACAI;MACAL;MACAC;QACA;MACA;IACA;EACA;EACAK;IACA;MACAC;MACAC;MACAC;MACAC;MAAA;MACAC;MAAA;MACAC;MAAA;MACAC;MAAA;MACAC;MAAA;MACAC;MAAA;MACAC;MAAA;MACAC;MAAA;MACAC;MAAA;MACAC;MAAA;MACAC;MAAA;MACAC;MACA;MACA;MACA;MACA;MACA;MACA;MACA;QACA3B;QACA4B;QACAC;MACA,EACA;MACAC,UACA;QACA9B;QACA4B;QACAC;MACA,EACA;MACAE;MAAA;MACAC;MAAA;MACAC;MACAC;MACAC;QACAC;QACAR;MACA;MACA;MACA;IACA;EACA;;EACAS;IACA;IACAL;MACAM;MACAC;QAAA;QACA;QACA;UACA;YACAC;YACAA;UACA;QACA;QACAC;UACA;YACA;cACAC;cACA;YACA;UACA;QACA;QACA;QACA;QACA;MACA;IACA;IACAX;MACAO;MACAC;QACA;QACAE;UACA;YACAE;UACA;QACA;QACA;MACA;IACA;EACA;EACAC;IACA;IACA;EACA;EACAC;IACAC;MACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACAC;MAAA;MACA;MACA;QACA,IACAC,mCACAA,qCACA;UACA;UACA;QACA;MACA;MACA;QACAA;QACAA;QACA;MACA;IACA;IACAC;MACA;MACA;QACA;UACAP;UACAA;QACA;MACA;IACA;IACA;IACAQ;MAAA;MACA;QACA;QACA;UACA,IACAR,oCACAA,sCACA;YACA;YACA;UACA;QACA;QACA;UACAA;QACA;MACA;IACA;IACA;IACAS;MACA;QACA;UACAX;QACA;MACA;QACAQ;UACAR;QACA;QACA;MACA;IACA;IACA;IACAY;MACA;MACA;IACA;IACA;IACAC;MAAA;MACA;QACA,6BACA;QACA;QACA;UACA,IACAX,oCACAA,sCACA;YACAA;YACAA;UACA;QACA;MACA;IACA;IACA;IACAY;MAAA;MACA;MACA;QACA;UACA;QACA;MACA;MACA;QACA;QACA;UACA,IACAZ,oCACAA,sCACA;YACA;YACA;UACA;QACA;QACA;UACAA;UACA;QACA;MACA;MACA;IACA;IACA;IACAa;MAAA;MACA;MACA;QACA;UACAC;UACAC;YACAC;YACAC;YACAtC;YACAuC;YACAC;UACA;QACA;QACAC;UACAC;YACA;cACAC;cACAH;cACAI;cACAC;cAAA;cACAC;YACA;;YACA;UACA;UACA;QACA;MACA;QACA;UACAX;UACAC;YACAC;YACAC;YACAtC;YACAuC;YACAC;UACA;QACA;QACA9D;UACAgE;YACA;cACAC;cACAH;cACAI;cACAC;cAAA;cACAC;YACA;;YACA;UACA;UACA;QACA;MACA;QACA;UACA,IACA3B,sDACAA,kDACA;YACA;cACAwB;cACAH;cACAI;cACAC;cAAA;cACAC;YACA;UACA;QACA;;QACA;MACA;MACA;IACA;IACA;IACAC;MAAA;MACA;MACA;MACA;QACAZ;QACAC;UACAG;UACAF;UACArC;UACAwC;UACAF;QACA;MACA;MACA;QACAG;UACA;UACAC;YACA;cACAC;cACAH;cACAI;cACAC;cAAA;cACAC;YACA;UACA;;UACA;UACA;QACA;MACA;QACApE;UACA;UACAgE;YACA;cACAC;cACAH;cACAI;cACAC;cAAA;cACAC;YACA;UACA;;UACA;UACA;QACA;MACA;QACA;QACA;UACA;YACAH;YACAH;YACAI;YACAC;YAAA;YACAC;UACA;QACA;;QACA;MACA;MACA;IACA;EACA;AACA", "names": ["queryUserSecondOrgan", "name", "directives", "elDragDialog", "inheritAttrs", "props", "nocheckRule", "type", "default", "nowFunction", "dataType", "allUserList", "organNoUserListMap", "data", "countFlag", "btn", "disabledFlag", "leftSelectedNumber", "leftAllNumber", "rightSelectedNumber", "rightAllNumber", "treeFlag", "org_id", "windowHeight", "dialogHeight", "selectValue", "selectRightValue", "flag", "leftColumns", "label", "id", "columns", "tableLeftData", "tableRightData", "userInput", "treeData", "defaultProps", "children", "watch", "deep", "handler", "element", "newValue", "item", "num", "mounted", "methods", "selectRight", "rowDblclick", "val", "rowRightDblclick", "checkDisabled", "handleSelectionLeftChange", "handleSelectionRightChange", "leftPush", "rightPush", "queryInfo", "parameterList", "sysMap", "oper_type", "org_tp", "role_id", "user_no", "query<PERSON><PERSON><PERSON><PERSON>", "response", "rn", "user_name", "selected", "disabled", "handleNodeClick"], "sourceRoot": "src/components/SunTransfer", "sources": ["index.vue"], "sourcesContent": ["<!--\n* 弹出框：单个表单\n-->\n<template>\n  <div class=\"largeBox\">\n    <el-input\n      v-model=\"userInput\"\n      placeholder=\"支持用户号和用户名模糊查询\"\n      style=\"width: 30%; margin-bottom: 2rem\"\n      @keydown.native.enter=\"queryInfo\"\n    >\n      <i\n        slot=\"suffix\"\n        class=\"el-input__icon el-icon-search\"\n        @click=\"queryInfo\"\n      />\n    </el-input>\n    <div class=\"box\">\n      <div class=\"leftTree\">\n        <el-tree\n          :data=\"treeData\"\n          :props=\"defaultProps\"\n          class=\"treeBody\"\n          @node-click=\"handleNodeClick\"\n        />\n      </div>\n      <!-- 左侧表格--begin -->\n      <div class=\"leftTable\">\n        <span class=\"countNumber\">\n          {{ leftSelectedNumber }}/{{ leftAllNumber }}\n        </span>\n        <el-table\n          ref=\"multipleTable\"\n          style=\"width: 100%\"\n          height=\"100%\"\n          :data=\"tableLeftData\"\n          tooltip-effect=\"dark\"\n          @row-dblclick=\"rowDblclick\"\n          @selection-change=\"handleSelectionLeftChange\"\n        >\n          <el-table-column type=\"selection\" width=\"55\">\n            <div slot-scope=\"{ row }\">\n              <span>\n                <el-checkbox v-model=\"row.selected\" :disabled=\"row.disabled\" />\n              </span>\n            </div>\n          </el-table-column>\n          <el-table-column\n            v-for=\"item in leftColumns\"\n            :key=\"item.id\"\n            :prop=\"item.name\"\n            :label=\"item.label\"\n            :width=\"item.width\"\n          >\n            <template slot=\"header\">\n              <span>可选用户</span>\n            </template>\n            <div slot-scope=\"{ row }\">\n              <span\n                v-if=\"item.name === 'rn'\"\n              >{{ row.user_no }}-{{ row.user_name }}</span>\n              <!-- <span v-else-if=\"item.name === 'selected'\">\n                <el-checkbox v-model=\"row.selected\" :disabled=\"row.disabled\" />\n              </span> -->\n            </div>\n          </el-table-column>\n        </el-table>\n      </div>\n      <!-- 左侧表格--end -->\n      <!-- 中间按钮--begin -->\n      <div class=\"middleButton\">\n        <el-button-group>\n          <el-button\n            type=\"primary\"\n            icon=\"el-icon-arrow-left\"\n            style=\"margin-bottom: 1rem; width: 100%\"\n            @click=\"leftPush\"\n          />\n          <el-button\n            type=\"primary\"\n            icon=\"el-icon-arrow-right\"\n            style=\"width: 100%\"\n            @click=\"rightPush\"\n          />\n        </el-button-group>\n      </div>\n      <!-- 中间按钮--end -->\n      <!-- 右侧表格--begin -->\n      <div class=\"rightTable\">\n        <span class=\"countNumber\">\n          {{ rightSelectedNumber }}/{{ rightAllNumber }}\n        </span>\n        <el-table\n          :data=\"tableRightData\"\n          style=\"width: 100%\"\n          height=\"100%\"\n          tooltip-effect=\"dark\"\n          @select=\"selectRight\"\n          @row-dblclick=\"rowRightDblclick\"\n          @selection-change=\"handleSelectionRightChange\"\n        >\n          <el-table-column type=\"selection\" width=\"45\" />\n          <el-table-column\n            v-for=\"item in columns\"\n            :key=\"item.id\"\n            :prop=\"item.name\"\n            :label=\"item.label\"\n            :width=\"item.width\"\n          >\n            <div slot-scope=\"{ row }\">\n              <span v-if=\"item.name === 'rn'\">\n                {{ row.user_no }}-{{ row.user_name }}</span>\n            </div>\n          </el-table-column>\n        </el-table>\n      </div>\n      <!-- 右侧表格--end -->\n    </div>\n  </div>\n</template>\n\n<script>\nimport elDragDialog from '@/directive/el-drag-dialog' // 弹出框可拖动\nimport { SysTransfer } from './index.js'\nconst { queryUserOrgan, queryUserSecondOrgan } = SysTransfer\nimport { v1 as uuidv1 } from 'uuid'\nexport default {\n  name: 'SunTransfer',\n  directives: { elDragDialog },\n  inheritAttrs: false,\n  props: {\n    nocheckRule: {\n      type: String,\n      default: ''\n    },\n    nowFunction: {\n      type: String,\n      default: 'dialogSubmit'\n    },\n    dataType: {\n      type: Number,\n      default: 0\n      // 数据源类型\n    },\n    // 当数据源类型为2时，搜索框的全部人员结果\n    allUserList: {\n      type: Array,\n      default: () => {\n        return []\n      }\n    },\n    // 当数据源类型为2时，点击树获取的对应全部结果\n    organNoUserListMap: {\n      type: Object,\n      default: () => {\n        return Object\n      }\n    }\n  },\n  data() {\n    return {\n      countFlag: 0,\n      btn: true,\n      disabledFlag: false,\n      leftSelectedNumber: 0, // 左侧选中数量\n      leftAllNumber: 0, // 左侧全部数量\n      rightSelectedNumber: 0, // 右侧选中数量\n      rightAllNumber: 0, // 右侧全部数量\n      treeFlag: true, // 树初始化禁用判断\n      org_id: '', // 选中的树id\n      windowHeight: '', // 浏览器的高度\n      dialogHeight: '', // 弹窗的高度\n      selectValue: [], // 左侧选中数据\n      selectRightValue: [], // 右侧选中数据\n      flag: true, // 判断累加\n      leftColumns: [\n        // {\n        //   name: 'selected',\n        //   label: '',\n        //   id: uuidv1(),\n        //   width: '25'\n        // },\n        {\n          name: 'rn',\n          label: '可选用户',\n          id: uuidv1()\n        }\n      ],\n      columns: [\n        {\n          name: 'rn',\n          label: '已选用户',\n          id: uuidv1()\n        }\n      ],\n      tableLeftData: [], // 左侧框内源数据\n      tableRightData: [], // 右侧框内数据\n      userInput: '',\n      treeData: this.$store.getters.rightOrganTree,\n      defaultProps: {\n        children: 'children',\n        label: 'label'\n      }\n      // 选中的穿梭框数据\n      // 选中的树数据\n    }\n  },\n  watch: {\n    // 每当值发生改变时，向父组件传值\n    tableRightData: {\n      deep: true,\n      handler(newValue, oldValue) {\n        // 初始化判断哪些数据已经有了\n        if (newValue.length === 0) {\n          this.tableLeftData.forEach((element) => {\n            element.disabled = false\n            element.selected = false\n          })\n        }\n        newValue.forEach((element) => {\n          this.tableLeftData.forEach((item) => {\n            if (item.user_no === element.user_no) {\n              item.disabled = true\n              return\n            }\n          })\n        })\n        // 初始化判断哪些数据已经有了\n        this.$emit('valueChange', newValue)\n        this.rightAllNumber = newValue.length\n      }\n    },\n    tableLeftData: {\n      deep: true,\n      handler(newValue, oldValue) {\n        let num = 0\n        newValue.forEach((item) => {\n          if (item.selected || item.disabled) {\n            num = num + 1\n          }\n        })\n        this.leftSelectedNumber = num\n      }\n    }\n  },\n  mounted() {\n    // 初始化调用搜索查询接口\n    this.queryInfo()\n  },\n  methods: {\n    selectRight(val) {\n      this.rightSelectedNumber = val.length\n    },\n    // // 单击选中行\n    // rowClick(val) {\n    //   console.log(val)\n    //   val.selected = !val.selected\n    // },\n    // 双击选中行\n    rowDblclick(val) {\n      this.flag = true\n      this.tableRightData.forEach((element) => {\n        if (\n          val.user_no === element.user_no &&\n          val.user_name === element.user_name\n        ) {\n          this.flag = false\n          return\n        }\n      })\n      if (this.flag) {\n        val.disabled = true\n        val.selected = true\n        this.tableRightData.push(val)\n      }\n    },\n    rowRightDblclick(val) {\n      this.tableRightData.splice(this.tableRightData.indexOf(val), 1)\n      this.tableLeftData.forEach((item) => {\n        if (item.user_no === val.user_no && item.user_name === val.user_name) {\n          item.disabled = false\n          item.selected = false\n        }\n      })\n    },\n    // 校验是否有选中的数据,并设置禁用\n    checkDisabled() {\n      this.tableLeftData.forEach((item) => {\n        this.treeFlag = true\n        this.tableRightData.forEach((element) => {\n          if (\n            item.user_no === element.user_no &&\n            item.user_name === element.user_name\n          ) {\n            this.treeFlag = false\n            return\n          }\n        })\n        if (!this.treeFlag) {\n          item.disabled = true\n        }\n      })\n    },\n    // 左侧选中数据\n    handleSelectionLeftChange(val) {\n      if (val.length === 0) {\n        this.selectValue.forEach((element) => {\n          element.selected = false\n        })\n      } else {\n        val.forEach((element) => {\n          element.selected = true\n        })\n        this.selectValue = val\n      }\n    },\n    // 右侧选中数据\n    handleSelectionRightChange(val) {\n      this.rightSelectedNumber = val.length\n      this.selectRightValue = val\n    },\n    // 向左传数据\n    leftPush() {\n      this.selectRightValue.forEach((element) => {\n        this.rightSelectedNumber =\n          this.rightSelectedNumber <= 0 ? 0 : this.rightSelectedNumber - 1 // 每次左移都要减数量(小于0则为0)\n        this.tableRightData.splice(this.tableRightData.indexOf(element), 1)\n        this.tableLeftData.forEach((item) => {\n          if (\n            item.user_no === element.user_no &&\n            item.user_name === element.user_name\n          ) {\n            item.disabled = false\n            item.selected = false\n          }\n        })\n      })\n    },\n    // 向右传数据\n    rightPush() {\n      this.selectValue = []\n      this.tableLeftData.forEach((item) => {\n        if (item.selected) {\n          this.selectValue.push(item)\n        }\n      })\n      this.selectValue.forEach((item) => {\n        this.flag = true\n        this.tableRightData.forEach((element) => {\n          if (\n            item.user_no === element.user_no &&\n            item.user_name === element.user_name\n          ) {\n            this.flag = false\n            return\n          }\n        })\n        if (this.flag) {\n          item.disabled = true\n          this.tableRightData.push(item)\n        }\n      })\n      // this.$refs.multipleTable.clearSelection(this.selectValue)\n    },\n    // 查询按钮\n    queryInfo() {\n      this.tableLeftData = []\n      if (this.dataType === 0) {\n        const msg = {\n          parameterList: [{}],\n          sysMap: {\n            oper_type: 'sel_user',\n            org_tp: '',\n            org_id: '',\n            role_id: this.$store.getters.roleNo,\n            user_no: this.userInput\n          }\n        }\n        queryUserOrgan(msg).then((response) => {\n          response.retMap.users.forEach((element) => {\n            this.tableLeftData.push({\n              rn: element.rn,\n              user_no: element.user_no,\n              user_name: element.user_name,\n              selected: false, // 初始化保证左侧表格每个单选框都选中\n              disabled: false // 初始化保证左侧表格每个单选框都没禁用\n            })\n            this.checkDisabled()\n          })\n          this.leftAllNumber = this.tableLeftData.length\n        })\n      } else if (this.dataType === 1) {\n        const msg = {\n          parameterList: [{}],\n          sysMap: {\n            oper_type: 'sel_user',\n            org_tp: '',\n            org_id: '#' + this.$store.getters.organNo,\n            role_id: this.$store.getters.roleNo,\n            user_no: this.userInput\n          }\n        }\n        queryUserSecondOrgan(msg).then((response) => {\n          response.retMap.users.forEach((element) => {\n            this.tableLeftData.push({\n              rn: element.rn,\n              user_no: element.user_no,\n              user_name: element.user_name,\n              selected: false, // 初始化保证左侧表格每个单选框都选中\n              disabled: false // 初始化保证左侧表格每个单选框都没禁用\n            })\n            this.checkDisabled()\n          })\n          this.leftAllNumber = this.tableLeftData.length\n        })\n      } else if (this.dataType === 2) {\n        this.allUserList.forEach((element) => {\n          if (\n            element.user_name.indexOf(this.userInput) !== -1 ||\n            element.user_no.indexOf(this.userInput) !== -1\n          ) {\n            this.tableLeftData.push({\n              rn: element.rn,\n              user_no: element.user_no,\n              user_name: element.user_name,\n              selected: false, // 初始化保证左侧表格每个单选框都选中\n              disabled: false // 初始化保证左侧表格每个单选框都没禁用\n            })\n          }\n        })\n        this.checkDisabled()\n      }\n      this.leftAllNumber = this.tableLeftData.length\n    },\n    // 树节点单击事件\n    handleNodeClick(data) {\n      this.org_id = data.id\n      this.$emit('handleTreeClick', this.tableLeftData, this.org_id)\n      const msg = {\n        parameterList: [{}],\n        sysMap: {\n          role_id: this.$store.getters.roleNo,\n          oper_type: 'sel_user',\n          org_id: this.org_id,\n          user_no: '',\n          org_tp: ''\n        }\n      }\n      if (this.dataType === 0) {\n        queryUserOrgan(msg).then((response) => {\n          this.tableLeftData = []\n          response.retMap.users.forEach((element) => {\n            this.tableLeftData.push({\n              rn: element.rn,\n              user_no: element.user_no,\n              user_name: element.user_name,\n              selected: false, // 初始化保证左侧表格每个单选框都选中\n              disabled: false // 初始化保证左侧表格每个单选框都没禁用\n            })\n          })\n          this.leftAllNumber = this.tableLeftData.length\n          this.checkDisabled()\n        })\n      } else if (this.dataType === 1) {\n        queryUserSecondOrgan(msg).then((response) => {\n          this.tableLeftData = []\n          response.retMap.users.forEach((element) => {\n            this.tableLeftData.push({\n              rn: element.rn,\n              user_no: element.user_no,\n              user_name: element.user_name,\n              selected: false, // 初始化保证左侧表格每个单选框都选中\n              disabled: false // 初始化保证左侧表格每个单选框都没禁用\n            })\n          })\n          this.leftAllNumber = this.tableLeftData.length\n          this.checkDisabled()\n        })\n      } else if (this.dataType === 2) {\n        this.tableLeftData = []\n        this.organNoUserListMap[data.id].forEach((element) => {\n          this.tableLeftData.push({\n            rn: element.rn,\n            user_no: element.user_no,\n            user_name: element.user_name,\n            selected: false, // 初始化保证左侧表格每个单选框都选中\n            disabled: false // 初始化保证左侧表格每个单选框都没禁用\n          })\n        })\n        this.checkDisabled()\n      }\n      this.leftAllNumber = this.tableLeftData.length\n    }\n  }\n}\n</script>\n\n<style lang=\"scss\" scoped>\n.largeBox {\n  // width: 84rem;\n  width: 100%;\n  padding: 1rem;\n  border: 1px solid RGB(230, 233, 239);\n  .box {\n    display: flex;\n    // padding: 1rem;\n    // border: 1px solid RGB(230, 233, 239);\n    .leftTree {\n      float: left;\n      width: 22rem;\n      margin-right: 2rem;\n      height: 28rem;\n      overflow: auto;\n      .treeBody {\n        border-right: 2px rgb(230, 233, 239) solid;\n        overflow: auto;\n        height: 100%;\n        border: 1px solid RGB(230, 233, 239);\n      }\n    }\n    .leftTable {\n      float: left;\n      position: relative;\n      // width: 28rem;\n      width: 30%;\n      height: 28rem;\n      overflow: auto;\n      border: 1px solid RGB(230, 233, 239);\n      border-bottom: none;\n      .countNumber {\n        position: absolute;\n        top: 14px;\n        right: 20px;\n        width: 40px;\n        height: 10px;\n        z-index: 100;\n        color: #909399;\n        font-size: 14px;\n      }\n    }\n    .middleButton {\n      float: left;\n      width: 5%;\n      margin: 8% 2%;\n    }\n    .rightTable {\n      position: relative;\n      float: left;\n      // width: 28rem;\n      width: 30%;\n      height: 28rem;\n      overflow: auto;\n      border: 1px solid RGB(230, 233, 239);\n      border-bottom: none;\n      .countNumber {\n        position: absolute;\n        top: 14px;\n        right: 20px;\n        width: 40px;\n        height: 10px;\n        z-index: 100;\n        color: #909399;\n        font-size: 14px;\n      }\n    }\n  }\n}\n.dialog-footer {\n  display: flex;\n  justify-content: flex-end;\n  .footerRightBtn {\n    margin-right: 10px;\n    .rightBtn {\n      margin: 0rem 20rem 0rem 2rem;\n      position: absolute;\n      right: 2rem;\n    }\n  }\n}\n\n::v-deep .el-transfer {\n  .el-transfer-panel {\n    width: 30rem;\n  }\n  .leftBox {\n    display: none;\n  }\n  .el-transfer-panel:nth-child(1) {\n    .leftBox {\n      display: inline;\n    }\n  }\n}\n</style>\n"]}]}