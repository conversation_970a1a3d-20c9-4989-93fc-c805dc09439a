package com.sunyard.etl.nps.orm;

import java.sql.ResultSet;
import java.sql.SQLException;

import com.sunyard.etl.nps.model.NpBusinessData;
import com.sunyard.etl.system.orm.Orm;

public class NpBusinessDataOrm implements Orm<NpBusinessData> {

	public NpBusinessData orm(ResultSet rs) {
		NpBusinessData npBusinessData = new NpBusinessData();
		try {
			npBusinessData.setBusiDataNo(rs.getString("BUSI_DATA_NO"));
			npBusinessData.setCONTENTId(rs.getString("CONTENT_ID"));
			npBusinessData.setFormName(rs.getString("FORM_NAME"));
			npBusinessData.setOccurDate(rs.getString("OCCUR_DATE"));
			npBusinessData.setSiteNo(rs.getString("SITE_NO"));
			npBusinessData.setOperatorNo(rs.getString("OPERATOR_NO"));
			npBusinessData.setFlowId(rs.getString("FLOW_ID"));
			npBusinessData.setIndexValue(rs.getString("INDEX_VALUE"));
			npBusinessData.setDataSourceId(rs.getString("DATA_SOURCE_ID"));
		} catch (SQLException e) {
			e.printStackTrace();
		}
		return npBusinessData;
	}

}
