{"remainingRequest": "D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\node_modules\\thread-loader\\dist\\cjs.js!D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\node_modules\\babel-loader\\lib\\index.js!D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\src\\components\\Vendor\\Export2Excel.js", "dependencies": [{"path": "D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\src\\components\\Vendor\\Export2Excel.js", "mtime": 1686019810216}, {"path": "D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\babel.config.js", "mtime": 1686019818359}, {"path": "D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\.babelrc", "mtime": 1705285078668}, {"path": "D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\node_modules\\thread-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, null]}