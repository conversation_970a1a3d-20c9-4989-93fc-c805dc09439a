{"remainingRequest": "D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\node_modules\\babel-loader\\lib\\index.js!D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\node_modules\\eslint-loader\\index.js??ref--13-0!D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\src\\store\\modules\\user.js", "dependencies": [{"path": "D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\src\\store\\modules\\user.js", "mtime": 1686019811045}, {"path": "D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\babel.config.js", "mtime": 1686019818359}, {"path": "D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\node_modules\\eslint-loader\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["<PERSON><PERSON>", "Common", "getToken", "setToken", "removeToken", "encryptResult", "resetRouter", "getThemeColor", "defaultSettings", "query", "SysAudit", "login", "logout", "state", "token", "userNo", "userLevel", "avatar", "introduction", "userName", "roleNo", "roles", "organNo", "organLevel", "loginInfo", "btnAll", "routeM", "menuAuditData", "initParams", "windowPath", "includeModule", "mutations", "SET_TOKEN", "SET_WINDOWPATH", "path", "SET_INTRODUCTION", "SET_NAME", "name", "SET_AVATAR", "SET_ROLES", "SET_LOGININFO", "data", "SET_USER", "process", "item", "SET_ROUTE_M", "button_id", "MENU_AUDIT_DATA", "SET_INIT_PARAMS", "CHANGE_THEME", "customAttrMap", "theme", "actions", "userInfo", "commit", "dispatch", "systemNo", "username", "password", "code", "passwordSec", "enSecMap", "encryptType", "msg", "parameterList", "trim", "is<PERSON><PERSON><PERSON>", "loginTerminal", "loginType", "oper_type", "loginKind", "Promise", "resolve", "reject", "then", "response", "loginFlag", "retMap", "loginUser", "info", "login<PERSON><PERSON>", "key", "value", "catch", "error", "autoLogin", "Authorization", "getInfo", "sessionStorage", "clear", "user_no", "login_terminal", "flag", "root", "location", "reload", "resetToken", "getMenuAudit", "currentPage", "operationRequestConfig_pageNum", "menu_id", "menu_name", "check_flag", "res", "list", "temp", "k", "button_name", "arr", "split", "Object", "prototype", "hasOwnProperty", "call", "changeTheme", "namespaced"], "sources": ["D:/1_Project/XYD_Project/dop-4.0/dop-4.1-qianduan-unify/dopUnify/src/store/modules/user.js"], "sourcesContent": ["import { Login, Common } from '@/api'\nimport { getToken, setToken, removeToken } from '@/utils/auth'\nimport { encryptResult } from '@/utils/crypto'\nimport { resetRouter } from '@/router'\nimport { getThemeColor } from '@/utils/common'\nimport defaultSettings from '@/settings'\nconst { query } = Common.SysAudit\n// 登录\nconst { login, logout } = Login\nconst state = {\n  token: getToken(),\n  userNo: '',\n  userLevel: '',\n  avatar: '',\n  introduction: '',\n  userName: '',\n  roleNo: '',\n  roles: [],\n  organNo: '', // 机构号\n  organLevel: '', // 机构级别\n  loginInfo: {}, // 登入信息\n  btnAll: '', // 页面按钮权限\n  routeM: {}, // 路由页面信息\n  menuAuditData: [], // 菜单数据(包括菜单准入禁止条件、按钮id、菜单id、审核方式等数据)，用来判断菜单各个按钮操作的审核方式\n  initParams: {}, // 初始化参数\n  windowPath: '', // 当前微应用window\n  includeModule: '' // 用户所包含的模块\n}\n\nconst mutations = {\n  SET_TOKEN: (state, token) => {\n    state.token = token\n  },\n  SET_WINDOWPATH: (state, path) => {\n    // 当前微应用地址配置\n    state.windowPath = path\n  },\n  SET_INTRODUCTION: (state, introduction) => {\n    state.introduction = introduction\n  },\n  SET_NAME: (state, name) => {\n    state.userNo = name\n  },\n  SET_AVATAR: (state, avatar) => {\n    state.avatar = avatar\n  },\n  SET_ROLES: (state, roles) => {\n    state.roles = roles\n  },\n  SET_LOGININFO: (state, data) => {\n    state.loginInfo = data\n    state.organNo = data.organNo\n    state.organLevel = data.organLevel\n    state.userName = data.userName\n    state.roleNo = data.roleNo\n    state.includeModule = data.includeModule\n  },\n  SET_USER: (state, data) => {\n    // 配置所有属性\n    if (defaultSettings.process === 'development') {\n      setToken(data.token)\n    }\n    for (const item in data) {\n      state[item] = data[item]\n    }\n  },\n  // 设置路由页面信息\n  SET_ROUTE_M: (state, data) => {\n    state.routeM = data\n    state.btnAll = data.button_id\n  },\n  // 菜单按钮审核方式\n  MENU_AUDIT_DATA: (state, menuAuditData) => {\n    state.menuAuditData = menuAuditData\n  },\n  SET_INIT_PARAMS: (state, data) => {\n    state.initParams = data\n  },\n  // 修改主题色英文字段\n  CHANGE_THEME: (state, data) => {\n    state.loginInfo.customAttrMap.theme = data\n  }\n}\n\nconst actions = {\n  /**\n   * 用户登录\n   * @param userInfo: 用户登录信息\n   */\n  login({ commit, dispatch, state }, userInfo) {\n    const { systemNo, username, password, code } = userInfo\n    const passwordSec = encryptResult(\n      state.initParams.enSecMap.encryptType,\n      password\n    ) // 加密\n    const msg = {\n      parameterList: [\n        {\n          userNo: username.trim(),\n          password: passwordSec,\n          isCheck: '1',\n          loginTerminal: '1',\n          loginType: '1'\n        }\n      ],\n      code: code,\n      oper_type: '0',\n      loginKind: 'user_no',\n      systemNo: systemNo\n    }\n    return new Promise((resolve, reject) => {\n      login(msg)\n        .then((response) => {\n          // 登录请求\n          const { loginFlag } = response.retMap\n          if (loginFlag === 'loginSuccess') {\n            const { loginUser } = response.retMap\n            commit('SET_TOKEN', getToken()) // token设置\n            commit('SET_NAME', loginUser.userNo)\n            dispatch('getMenuAudit') // 调用菜单按钮审核方式\n            const info = {\n              ...response.retMap,\n              ...response.retMap.loginUser,\n              ...response.retMap.loginOrgan\n            }\n            commit('SET_LOGININFO', info)\n            // 处理主题色 begin\n            this.commit('settings/CHANGE_SETTING', {\n              key: 'theme',\n              value: getThemeColor(info.customAttrMap.theme)\n            })\n            // 处理主题色 end\n            // setToken(Authorization)\n            resolve()\n          } else {\n            reject(response)\n          }\n        })\n        .catch((error) => {\n          reject(error)\n        })\n    })\n  },\n\n  /**\n   * 免密登录\n   * @param userInfo: 免密登录\n   */\n  autoLogin({ commit }, userInfo) {\n    const { username, password } = userInfo\n    // const passwordAES = encryptResult(\n    //   this.$store.getters.initParams.enSecMap.encryptType,\n    //   password\n    // ) // 加密\n    const msg = {\n      parameterList: [\n        {\n          userNo: username.trim(),\n          password: password,\n          isCheck: '1',\n          loginTerminal: '1',\n          loginType: '1'\n        }\n      ],\n      oper_type: '0',\n      loginKind: 'user_no'\n    }\n    return new Promise((resolve, dispatch, reject) => {\n      login(msg)\n        .then((response) => {\n          // 登录请求\n          const { loginFlag } = response.retMap\n          if (loginFlag === 'loginSuccess') {\n            const { Authorization, loginUser } = response.retMap\n            commit('SET_TOKEN', Authorization)\n            commit('SET_NAME', loginUser.userNo)\n            dispatch('getMenuAudit') // 调用菜单按钮审核方式\n            const info = {\n              ...response.retMap,\n              ...response.retMap.loginUser,\n              ...response.retMap.loginOrgan\n            }\n            commit('SET_LOGININFO', info)\n            setToken(Authorization)\n            resolve()\n          } else {\n            reject(response)\n          }\n        })\n        .catch((error) => {\n          reject(error)\n        })\n    })\n  },\n\n  // 获取用户信息\n  getInfo({ commit, state }) {\n    return new Promise((resolve, reject) => {\n      const roles = ['admin']\n      commit('SET_ROLES', roles)\n      resolve({ roles })\n    })\n  },\n\n  // 系统退出\n  logout({ commit, state, dispatch }) {\n    sessionStorage.clear() // 清空所有缓存\n    return new Promise((resolve, reject) => {\n      const msg = {\n        oper_type: '0',\n        user_no: state.userNo,\n        login_terminal: state.loginInfo.loginTerminal,\n        flag: '1'\n      }\n      // reset visited views and cached views\n      // to fixed https://github.com/PanJiaChen/vue-element-admin/issues/2485\n      dispatch('tagsView/delAllViews', null, { root: true })\n\n      // resolve()\n      logout(msg)\n        .then(() => {\n          commit('SET_TOKEN', '')\n          commit('SET_ROLES', [])\n          removeToken()\n          resetRouter()\n          location.reload()\n        })\n        .catch((error) => {\n          commit('SET_TOKEN', '')\n          commit('SET_ROLES', [])\n          removeToken()\n          resetRouter()\n          location.reload()\n          reject(error)\n        })\n    })\n  },\n\n  // 移除 token\n  resetToken({ commit }) {\n    return new Promise((resolve) => {\n      commit('SET_TOKEN', '')\n      commit('SET_ROLES', [])\n      removeToken()\n      resolve()\n    })\n  },\n\n  // dynamically modify permissions\n  // async changeRoles({ commit, dispatch }, role) {\n  //   const token = role + '-token'\n\n  //   commit('SET_TOKEN', token)\n  //   setToken(token)\n\n  //   const { roles } = await dispatch('getInfo')\n\n  //   resetRouter()\n\n  //   // generate accessible routes map based on roles\n  //   const accessRoutes = await dispatch('permission/generateRoutes', roles, { root: true })\n  //   // dynamically add accessible routes\n  //   router.addRoutes(accessRoutes)\n\n  //   // reset visited views and cached views\n  //   dispatch('tagsView/delAllViews', null, { root: true })\n  // }\n  // 获取菜单审核方式\n  getMenuAudit({ commit, dispatch, state }) {\n    const msg = {\n      parameterList: [],\n      currentPage: -1,\n      operationRequestConfig_pageNum: 10,\n      menu_id: '',\n      menu_name: '',\n      check_flag: ''\n    }\n    query(msg).then((res) => {\n      const { list } = res.retMap\n      const temp = {}\n      for (const k of list) {\n        delete k.button_name\n        delete k.menu_name\n        const arr = k.button_id.split(',')\n        if (Object.prototype.hasOwnProperty.call(temp, k.menu_id)) {\n          // 循环对象时防止相同菜单id覆盖\n          for (const item of arr) {\n            temp[k.menu_id][item] = k\n          }\n        } else {\n          temp[k.menu_id] = {}\n          for (const item of arr) {\n            temp[k.menu_id][item] = k\n          }\n        }\n      }\n      commit('MENU_AUDIT_DATA', temp) // 将菜单审核数组存在store中\n    })\n  },\n  // 修改主题色英文字段\n  changeTheme({ commit }, data) {\n    commit('CHANGE_THEME', data)\n  }\n}\n\nexport default {\n  namespaced: true, // 开启namespace:true，该模块就成为命名空间模块了\n  state,\n  mutations,\n  actions\n}\n"], "mappings": ";;;;AAAA,SAASA,KAAK,EAAEC,MAAM,QAAQ,OAAO;AACrC,SAASC,QAAQ,EAAEC,QAAQ,EAAEC,WAAW,QAAQ,cAAc;AAC9D,SAASC,aAAa,QAAQ,gBAAgB;AAC9C,SAASC,WAAW,QAAQ,UAAU;AACtC,SAASC,aAAa,QAAQ,gBAAgB;AAC9C,OAAOC,eAAe,MAAM,YAAY;AACxC,IAAQC,KAAK,GAAKR,MAAM,CAACS,QAAQ,CAAzBD,KAAK;AACb;AACA,IAAQE,MAAK,GAAaX,KAAK,CAAvBW,KAAK;EAAEC,OAAM,GAAKZ,KAAK,CAAhBY,MAAM;AACrB,IAAMC,KAAK,GAAG;EACZC,KAAK,EAAEZ,QAAQ,EAAE;EACjBa,MAAM,EAAE,EAAE;EACVC,SAAS,EAAE,EAAE;EACbC,MAAM,EAAE,EAAE;EACVC,YAAY,EAAE,EAAE;EAChBC,QAAQ,EAAE,EAAE;EACZC,MAAM,EAAE,EAAE;EACVC,KAAK,EAAE,EAAE;EACTC,OAAO,EAAE,EAAE;EAAE;EACbC,UAAU,EAAE,EAAE;EAAE;EAChBC,SAAS,EAAE,CAAC,CAAC;EAAE;EACfC,MAAM,EAAE,EAAE;EAAE;EACZC,MAAM,EAAE,CAAC,CAAC;EAAE;EACZC,aAAa,EAAE,EAAE;EAAE;EACnBC,UAAU,EAAE,CAAC,CAAC;EAAE;EAChBC,UAAU,EAAE,EAAE;EAAE;EAChBC,aAAa,EAAE,EAAE,CAAC;AACpB,CAAC;;AAED,IAAMC,SAAS,GAAG;EAChBC,SAAS,EAAE,mBAACnB,KAAK,EAAEC,KAAK,EAAK;IAC3BD,KAAK,CAACC,KAAK,GAAGA,KAAK;EACrB,CAAC;EACDmB,cAAc,EAAE,wBAACpB,KAAK,EAAEqB,IAAI,EAAK;IAC/B;IACArB,KAAK,CAACgB,UAAU,GAAGK,IAAI;EACzB,CAAC;EACDC,gBAAgB,EAAE,0BAACtB,KAAK,EAAEK,YAAY,EAAK;IACzCL,KAAK,CAACK,YAAY,GAAGA,YAAY;EACnC,CAAC;EACDkB,QAAQ,EAAE,kBAACvB,KAAK,EAAEwB,IAAI,EAAK;IACzBxB,KAAK,CAACE,MAAM,GAAGsB,IAAI;EACrB,CAAC;EACDC,UAAU,EAAE,oBAACzB,KAAK,EAAEI,MAAM,EAAK;IAC7BJ,KAAK,CAACI,MAAM,GAAGA,MAAM;EACvB,CAAC;EACDsB,SAAS,EAAE,mBAAC1B,KAAK,EAAEQ,KAAK,EAAK;IAC3BR,KAAK,CAACQ,KAAK,GAAGA,KAAK;EACrB,CAAC;EACDmB,aAAa,EAAE,uBAAC3B,KAAK,EAAE4B,IAAI,EAAK;IAC9B5B,KAAK,CAACW,SAAS,GAAGiB,IAAI;IACtB5B,KAAK,CAACS,OAAO,GAAGmB,IAAI,CAACnB,OAAO;IAC5BT,KAAK,CAACU,UAAU,GAAGkB,IAAI,CAAClB,UAAU;IAClCV,KAAK,CAACM,QAAQ,GAAGsB,IAAI,CAACtB,QAAQ;IAC9BN,KAAK,CAACO,MAAM,GAAGqB,IAAI,CAACrB,MAAM;IAC1BP,KAAK,CAACiB,aAAa,GAAGW,IAAI,CAACX,aAAa;EAC1C,CAAC;EACDY,QAAQ,EAAE,kBAAC7B,KAAK,EAAE4B,IAAI,EAAK;IACzB;IACA,IAAIjC,eAAe,CAACmC,OAAO,KAAK,aAAa,EAAE;MAC7CxC,QAAQ,CAACsC,IAAI,CAAC3B,KAAK,CAAC;IACtB;IACA,KAAK,IAAM8B,IAAI,IAAIH,IAAI,EAAE;MACvB5B,KAAK,CAAC+B,IAAI,CAAC,GAAGH,IAAI,CAACG,IAAI,CAAC;IAC1B;EACF,CAAC;EACD;EACAC,WAAW,EAAE,qBAAChC,KAAK,EAAE4B,IAAI,EAAK;IAC5B5B,KAAK,CAACa,MAAM,GAAGe,IAAI;IACnB5B,KAAK,CAACY,MAAM,GAAGgB,IAAI,CAACK,SAAS;EAC/B,CAAC;EACD;EACAC,eAAe,EAAE,yBAAClC,KAAK,EAAEc,aAAa,EAAK;IACzCd,KAAK,CAACc,aAAa,GAAGA,aAAa;EACrC,CAAC;EACDqB,eAAe,EAAE,yBAACnC,KAAK,EAAE4B,IAAI,EAAK;IAChC5B,KAAK,CAACe,UAAU,GAAGa,IAAI;EACzB,CAAC;EACD;EACAQ,YAAY,EAAE,sBAACpC,KAAK,EAAE4B,IAAI,EAAK;IAC7B5B,KAAK,CAACW,SAAS,CAAC0B,aAAa,CAACC,KAAK,GAAGV,IAAI;EAC5C;AACF,CAAC;AAED,IAAMW,OAAO,GAAG;EACd;AACF;AACA;AACA;EACEzC,KAAK,uBAA8B0C,QAAQ,EAAE;IAAA;IAAA,IAArCC,MAAM,QAANA,MAAM;MAAEC,QAAQ,QAARA,QAAQ;MAAE1C,KAAK,QAALA,KAAK;IAC7B,IAAQ2C,QAAQ,GAA+BH,QAAQ,CAA/CG,QAAQ;MAAEC,QAAQ,GAAqBJ,QAAQ,CAArCI,QAAQ;MAAEC,QAAQ,GAAWL,QAAQ,CAA3BK,QAAQ;MAAEC,IAAI,GAAKN,QAAQ,CAAjBM,IAAI;IAC1C,IAAMC,WAAW,GAAGvD,aAAa,CAC/BQ,KAAK,CAACe,UAAU,CAACiC,QAAQ,CAACC,WAAW,EACrCJ,QAAQ,CACT,EAAC;IACF,IAAMK,GAAG,GAAG;MACVC,aAAa,EAAE,CACb;QACEjD,MAAM,EAAE0C,QAAQ,CAACQ,IAAI,EAAE;QACvBP,QAAQ,EAAEE,WAAW;QACrBM,OAAO,EAAE,GAAG;QACZC,aAAa,EAAE,GAAG;QAClBC,SAAS,EAAE;MACb,CAAC,CACF;MACDT,IAAI,EAAEA,IAAI;MACVU,SAAS,EAAE,GAAG;MACdC,SAAS,EAAE,SAAS;MACpBd,QAAQ,EAAEA;IACZ,CAAC;IACD,OAAO,IAAIe,OAAO,CAAC,UAACC,OAAO,EAAEC,MAAM,EAAK;MACtC9D,MAAK,CAACoD,GAAG,CAAC,CACPW,IAAI,CAAC,UAACC,QAAQ,EAAK;QAClB;QACA,IAAQC,SAAS,GAAKD,QAAQ,CAACE,MAAM,CAA7BD,SAAS;QACjB,IAAIA,SAAS,KAAK,cAAc,EAAE;UAChC,IAAQE,SAAS,GAAKH,QAAQ,CAACE,MAAM,CAA7BC,SAAS;UACjBxB,MAAM,CAAC,WAAW,EAAEpD,QAAQ,EAAE,CAAC,EAAC;UAChCoD,MAAM,CAAC,UAAU,EAAEwB,SAAS,CAAC/D,MAAM,CAAC;UACpCwC,QAAQ,CAAC,cAAc,CAAC,EAAC;UACzB,IAAMwB,IAAI,iDACLJ,QAAQ,CAACE,MAAM,GACfF,QAAQ,CAACE,MAAM,CAACC,SAAS,GACzBH,QAAQ,CAACE,MAAM,CAACG,UAAU,CAC9B;UACD1B,MAAM,CAAC,eAAe,EAAEyB,IAAI,CAAC;UAC7B;UACA,KAAI,CAACzB,MAAM,CAAC,yBAAyB,EAAE;YACrC2B,GAAG,EAAE,OAAO;YACZC,KAAK,EAAE3E,aAAa,CAACwE,IAAI,CAAC7B,aAAa,CAACC,KAAK;UAC/C,CAAC,CAAC;UACF;UACA;UACAqB,OAAO,EAAE;QACX,CAAC,MAAM;UACLC,MAAM,CAACE,QAAQ,CAAC;QAClB;MACF,CAAC,CAAC,CACDQ,KAAK,CAAC,UAACC,KAAK,EAAK;QAChBX,MAAM,CAACW,KAAK,CAAC;MACf,CAAC,CAAC;IACN,CAAC,CAAC;EACJ,CAAC;EAED;AACF;AACA;AACA;EACEC,SAAS,4BAAahC,QAAQ,EAAE;IAAA,IAApBC,MAAM,SAANA,MAAM;IAChB,IAAQG,QAAQ,GAAeJ,QAAQ,CAA/BI,QAAQ;MAAEC,QAAQ,GAAKL,QAAQ,CAArBK,QAAQ;IAC1B;IACA;IACA;IACA;IACA,IAAMK,GAAG,GAAG;MACVC,aAAa,EAAE,CACb;QACEjD,MAAM,EAAE0C,QAAQ,CAACQ,IAAI,EAAE;QACvBP,QAAQ,EAAEA,QAAQ;QAClBQ,OAAO,EAAE,GAAG;QACZC,aAAa,EAAE,GAAG;QAClBC,SAAS,EAAE;MACb,CAAC,CACF;MACDC,SAAS,EAAE,GAAG;MACdC,SAAS,EAAE;IACb,CAAC;IACD,OAAO,IAAIC,OAAO,CAAC,UAACC,OAAO,EAAEjB,QAAQ,EAAEkB,MAAM,EAAK;MAChD9D,MAAK,CAACoD,GAAG,CAAC,CACPW,IAAI,CAAC,UAACC,QAAQ,EAAK;QAClB;QACA,IAAQC,SAAS,GAAKD,QAAQ,CAACE,MAAM,CAA7BD,SAAS;QACjB,IAAIA,SAAS,KAAK,cAAc,EAAE;UAChC,uBAAqCD,QAAQ,CAACE,MAAM;YAA5CS,aAAa,oBAAbA,aAAa;YAAER,SAAS,oBAATA,SAAS;UAChCxB,MAAM,CAAC,WAAW,EAAEgC,aAAa,CAAC;UAClChC,MAAM,CAAC,UAAU,EAAEwB,SAAS,CAAC/D,MAAM,CAAC;UACpCwC,QAAQ,CAAC,cAAc,CAAC,EAAC;UACzB,IAAMwB,IAAI,iDACLJ,QAAQ,CAACE,MAAM,GACfF,QAAQ,CAACE,MAAM,CAACC,SAAS,GACzBH,QAAQ,CAACE,MAAM,CAACG,UAAU,CAC9B;UACD1B,MAAM,CAAC,eAAe,EAAEyB,IAAI,CAAC;UAC7B5E,QAAQ,CAACmF,aAAa,CAAC;UACvBd,OAAO,EAAE;QACX,CAAC,MAAM;UACLC,MAAM,CAACE,QAAQ,CAAC;QAClB;MACF,CAAC,CAAC,CACDQ,KAAK,CAAC,UAACC,KAAK,EAAK;QAChBX,MAAM,CAACW,KAAK,CAAC;MACf,CAAC,CAAC;IACN,CAAC,CAAC;EACJ,CAAC;EAED;EACAG,OAAO,0BAAoB;IAAA,IAAjBjC,MAAM,SAANA,MAAM;MAAEzC,KAAK,SAALA,KAAK;IACrB,OAAO,IAAI0D,OAAO,CAAC,UAACC,OAAO,EAAEC,MAAM,EAAK;MACtC,IAAMpD,KAAK,GAAG,CAAC,OAAO,CAAC;MACvBiC,MAAM,CAAC,WAAW,EAAEjC,KAAK,CAAC;MAC1BmD,OAAO,CAAC;QAAEnD,KAAK,EAALA;MAAM,CAAC,CAAC;IACpB,CAAC,CAAC;EACJ,CAAC;EAED;EACAT,MAAM,yBAA8B;IAAA,IAA3B0C,MAAM,SAANA,MAAM;MAAEzC,KAAK,SAALA,KAAK;MAAE0C,QAAQ,SAARA,QAAQ;IAC9BiC,cAAc,CAACC,KAAK,EAAE,EAAC;IACvB,OAAO,IAAIlB,OAAO,CAAC,UAACC,OAAO,EAAEC,MAAM,EAAK;MACtC,IAAMV,GAAG,GAAG;QACVM,SAAS,EAAE,GAAG;QACdqB,OAAO,EAAE7E,KAAK,CAACE,MAAM;QACrB4E,cAAc,EAAE9E,KAAK,CAACW,SAAS,CAAC2C,aAAa;QAC7CyB,IAAI,EAAE;MACR,CAAC;MACD;MACA;MACArC,QAAQ,CAAC,sBAAsB,EAAE,IAAI,EAAE;QAAEsC,IAAI,EAAE;MAAK,CAAC,CAAC;;MAEtD;MACAjF,OAAM,CAACmD,GAAG,CAAC,CACRW,IAAI,CAAC,YAAM;QACVpB,MAAM,CAAC,WAAW,EAAE,EAAE,CAAC;QACvBA,MAAM,CAAC,WAAW,EAAE,EAAE,CAAC;QACvBlD,WAAW,EAAE;QACbE,WAAW,EAAE;QACbwF,QAAQ,CAACC,MAAM,EAAE;MACnB,CAAC,CAAC,CACDZ,KAAK,CAAC,UAACC,KAAK,EAAK;QAChB9B,MAAM,CAAC,WAAW,EAAE,EAAE,CAAC;QACvBA,MAAM,CAAC,WAAW,EAAE,EAAE,CAAC;QACvBlD,WAAW,EAAE;QACbE,WAAW,EAAE;QACbwF,QAAQ,CAACC,MAAM,EAAE;QACjBtB,MAAM,CAACW,KAAK,CAAC;MACf,CAAC,CAAC;IACN,CAAC,CAAC;EACJ,CAAC;EAED;EACAY,UAAU,6BAAa;IAAA,IAAV1C,MAAM,SAANA,MAAM;IACjB,OAAO,IAAIiB,OAAO,CAAC,UAACC,OAAO,EAAK;MAC9BlB,MAAM,CAAC,WAAW,EAAE,EAAE,CAAC;MACvBA,MAAM,CAAC,WAAW,EAAE,EAAE,CAAC;MACvBlD,WAAW,EAAE;MACboE,OAAO,EAAE;IACX,CAAC,CAAC;EACJ,CAAC;EAED;EACA;EACA;EAEA;EACA;EAEA;EAEA;EAEA;EACA;EACA;EACA;EAEA;EACA;EACA;EACA;EACAyB,YAAY,+BAA8B;IAAA,IAA3B3C,MAAM,SAANA,MAAM;MAAEC,QAAQ,SAARA,QAAQ;MAAE1C,KAAK,SAALA,KAAK;IACpC,IAAMkD,GAAG,GAAG;MACVC,aAAa,EAAE,EAAE;MACjBkC,WAAW,EAAE,CAAC,CAAC;MACfC,8BAA8B,EAAE,EAAE;MAClCC,OAAO,EAAE,EAAE;MACXC,SAAS,EAAE,EAAE;MACbC,UAAU,EAAE;IACd,CAAC;IACD7F,KAAK,CAACsD,GAAG,CAAC,CAACW,IAAI,CAAC,UAAC6B,GAAG,EAAK;MACvB,IAAQC,IAAI,GAAKD,GAAG,CAAC1B,MAAM,CAAnB2B,IAAI;MACZ,IAAMC,IAAI,GAAG,CAAC,CAAC;MAAA,2CACCD,IAAI;QAAA;MAAA;QAApB,oDAAsB;UAAA,IAAXE,CAAC;UACV,OAAOA,CAAC,CAACC,WAAW;UACpB,OAAOD,CAAC,CAACL,SAAS;UAClB,IAAMO,GAAG,GAAGF,CAAC,CAAC5D,SAAS,CAAC+D,KAAK,CAAC,GAAG,CAAC;UAClC,IAAIC,MAAM,CAACC,SAAS,CAACC,cAAc,CAACC,IAAI,CAACR,IAAI,EAAEC,CAAC,CAACN,OAAO,CAAC,EAAE;YACzD;YAAA,4CACmBQ,GAAG;cAAA;YAAA;cAAtB,uDAAwB;gBAAA,IAAbhE,IAAI;gBACb6D,IAAI,CAACC,CAAC,CAACN,OAAO,CAAC,CAACxD,IAAI,CAAC,GAAG8D,CAAC;cAC3B;YAAC;cAAA;YAAA;cAAA;YAAA;UACH,CAAC,MAAM;YACLD,IAAI,CAACC,CAAC,CAACN,OAAO,CAAC,GAAG,CAAC,CAAC;YAAA,4CACDQ,GAAG;cAAA;YAAA;cAAtB,uDAAwB;gBAAA,IAAbhE,KAAI;gBACb6D,IAAI,CAACC,CAAC,CAACN,OAAO,CAAC,CAACxD,KAAI,CAAC,GAAG8D,CAAC;cAC3B;YAAC;cAAA;YAAA;cAAA;YAAA;UACH;QACF;MAAC;QAAA;MAAA;QAAA;MAAA;MACDpD,MAAM,CAAC,iBAAiB,EAAEmD,IAAI,CAAC,EAAC;IAClC,CAAC,CAAC;EACJ,CAAC;EACD;EACAS,WAAW,8BAAazE,IAAI,EAAE;IAAA,IAAhBa,MAAM,SAANA,MAAM;IAClBA,MAAM,CAAC,cAAc,EAAEb,IAAI,CAAC;EAC9B;AACF,CAAC;AAED,eAAe;EACb0E,UAAU,EAAE,IAAI;EAAE;EAClBtG,KAAK,EAALA,KAAK;EACLkB,SAAS,EAATA,SAAS;EACTqB,OAAO,EAAPA;AACF,CAAC"}]}