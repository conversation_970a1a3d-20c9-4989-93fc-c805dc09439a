{"remainingRequest": "E:\\华南区项目\\中国银行\\开发区\\科技公司运营平台\\前端\\dopUnify\\node_modules\\babel-loader\\lib\\index.js!E:\\华南区项目\\中国银行\\开发区\\科技公司运营平台\\前端\\dopUnify\\node_modules\\qiankun\\es\\sandbox\\patchers\\interval.js", "dependencies": [{"path": "E:\\华南区项目\\中国银行\\开发区\\科技公司运营平台\\前端\\dopUnify\\node_modules\\qiankun\\es\\sandbox\\patchers\\interval.js", "mtime": 1667130453000}, {"path": "E:\\华南区项目\\中国银行\\开发区\\科技公司运营平台\\前端\\dopUnify\\babel.config.js", "mtime": 1721631954662}, {"path": "E:\\华南区项目\\中国银行\\开发区\\科技公司运营平台\\前端\\dopUnify\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "E:\\华南区项目\\中国银行\\开发区\\科技公司运营平台\\前端\\dopUnify\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:aW1wb3J0ICJjb3JlLWpzL21vZHVsZXMvZXMuYXJyYXkuZmlsdGVyLmpzIjsKaW1wb3J0ICJjb3JlLWpzL21vZHVsZXMvZXMub2JqZWN0LnRvLXN0cmluZy5qcyI7CmltcG9ydCAiY29yZS1qcy9tb2R1bGVzL2VzLmFycmF5LmNvbmNhdC5qcyI7CmltcG9ydCAiY29yZS1qcy9tb2R1bGVzL3dlYi5kb20tY29sbGVjdGlvbnMuZm9yLWVhY2guanMiOwppbXBvcnQgX25vb3AgZnJvbSAibG9kYXNoL25vb3AiOwppbXBvcnQgX3RvQ29uc3VtYWJsZUFycmF5IGZyb20gIkBiYWJlbC9ydW50aW1lL2hlbHBlcnMvZXNtL3RvQ29uc3VtYWJsZUFycmF5IjsKdmFyIHJhd1dpbmRvd0ludGVydmFsID0gd2luZG93LnNldEludGVydmFsOwp2YXIgcmF3V2luZG93Q2xlYXJJbnRlcnZhbCA9IHdpbmRvdy5jbGVhckludGVydmFsOwpleHBvcnQgZGVmYXVsdCBmdW5jdGlvbiBwYXRjaChnbG9iYWwpIHsKICB2YXIgaW50ZXJ2YWxzID0gW107CiAgZ2xvYmFsLmNsZWFySW50ZXJ2YWwgPSBmdW5jdGlvbiAoaW50ZXJ2YWxJZCkgewogICAgaW50ZXJ2YWxzID0gaW50ZXJ2YWxzLmZpbHRlcihmdW5jdGlvbiAoaWQpIHsKICAgICAgcmV0dXJuIGlkICE9PSBpbnRlcnZhbElkOwogICAgfSk7CiAgICByZXR1cm4gcmF3V2luZG93Q2xlYXJJbnRlcnZhbC5jYWxsKHdpbmRvdywgaW50ZXJ2YWxJZCk7CiAgfTsKICBnbG9iYWwuc2V0SW50ZXJ2YWwgPSBmdW5jdGlvbiAoaGFuZGxlciwgdGltZW91dCkgewogICAgZm9yICh2YXIgX2xlbiA9IGFyZ3VtZW50cy5sZW5ndGgsIGFyZ3MgPSBuZXcgQXJyYXkoX2xlbiA+IDIgPyBfbGVuIC0gMiA6IDApLCBfa2V5ID0gMjsgX2tleSA8IF9sZW47IF9rZXkrKykgewogICAgICBhcmdzW19rZXkgLSAyXSA9IGFyZ3VtZW50c1tfa2V5XTsKICAgIH0KICAgIHZhciBpbnRlcnZhbElkID0gcmF3V2luZG93SW50ZXJ2YWwuYXBwbHkodm9pZCAwLCBbaGFuZGxlciwgdGltZW91dF0uY29uY2F0KGFyZ3MpKTsKICAgIGludGVydmFscyA9IFtdLmNvbmNhdChfdG9Db25zdW1hYmxlQXJyYXkoaW50ZXJ2YWxzKSwgW2ludGVydmFsSWRdKTsKICAgIHJldHVybiBpbnRlcnZhbElkOwogIH07CiAgcmV0dXJuIGZ1bmN0aW9uIGZyZWUoKSB7CiAgICBpbnRlcnZhbHMuZm9yRWFjaChmdW5jdGlvbiAoaWQpIHsKICAgICAgcmV0dXJuIGdsb2JhbC5jbGVhckludGVydmFsKGlkKTsKICAgIH0pOwogICAgZ2xvYmFsLnNldEludGVydmFsID0gcmF3V2luZG93SW50ZXJ2YWw7CiAgICBnbG9iYWwuY2xlYXJJbnRlcnZhbCA9IHJhd1dpbmRvd0NsZWFySW50ZXJ2YWw7CiAgICByZXR1cm4gX25vb3A7CiAgfTsKfQ=="}, {"version": 3, "names": ["_noop", "_toConsumableArray", "rawWindowInterval", "window", "setInterval", "rawWindowClearInterval", "clearInterval", "patch", "global", "intervals", "intervalId", "filter", "id", "call", "handler", "timeout", "_len", "arguments", "length", "args", "Array", "_key", "apply", "concat", "free", "for<PERSON>ach"], "sources": ["E:/华南区项目/中国银行/开发区/科技公司运营平台/前端/dopUnify/node_modules/qiankun/es/sandbox/patchers/interval.js"], "sourcesContent": ["import _noop from \"lodash/noop\";\nimport _toConsumableArray from \"@babel/runtime/helpers/esm/toConsumableArray\";\nvar rawWindowInterval = window.setInterval;\nvar rawWindowClearInterval = window.clearInterval;\nexport default function patch(global) {\n  var intervals = [];\n  global.clearInterval = function (intervalId) {\n    intervals = intervals.filter(function (id) {\n      return id !== intervalId;\n    });\n    return rawWindowClearInterval.call(window, intervalId);\n  };\n  global.setInterval = function (handler, timeout) {\n    for (var _len = arguments.length, args = new Array(_len > 2 ? _len - 2 : 0), _key = 2; _key < _len; _key++) {\n      args[_key - 2] = arguments[_key];\n    }\n    var intervalId = rawWindowInterval.apply(void 0, [handler, timeout].concat(args));\n    intervals = [].concat(_toConsumableArray(intervals), [intervalId]);\n    return intervalId;\n  };\n  return function free() {\n    intervals.forEach(function (id) {\n      return global.clearInterval(id);\n    });\n    global.setInterval = rawWindowInterval;\n    global.clearInterval = rawWindowClearInterval;\n    return _noop;\n  };\n}"], "mappings": ";;;;AAAA,OAAOA,KAAK,MAAM,aAAa;AAC/B,OAAOC,kBAAkB,MAAM,8CAA8C;AAC7E,IAAIC,iBAAiB,GAAGC,MAAM,CAACC,WAAW;AAC1C,IAAIC,sBAAsB,GAAGF,MAAM,CAACG,aAAa;AACjD,eAAe,SAASC,KAAK,CAACC,MAAM,EAAE;EACpC,IAAIC,SAAS,GAAG,EAAE;EAClBD,MAAM,CAACF,aAAa,GAAG,UAAUI,UAAU,EAAE;IAC3CD,SAAS,GAAGA,SAAS,CAACE,MAAM,CAAC,UAAUC,EAAE,EAAE;MACzC,OAAOA,EAAE,KAAKF,UAAU;IAC1B,CAAC,CAAC;IACF,OAAOL,sBAAsB,CAACQ,IAAI,CAACV,MAAM,EAAEO,UAAU,CAAC;EACxD,CAAC;EACDF,MAAM,CAACJ,WAAW,GAAG,UAAUU,OAAO,EAAEC,OAAO,EAAE;IAC/C,KAAK,IAAIC,IAAI,GAAGC,SAAS,CAACC,MAAM,EAAEC,IAAI,GAAG,IAAIC,KAAK,CAACJ,IAAI,GAAG,CAAC,GAAGA,IAAI,GAAG,CAAC,GAAG,CAAC,CAAC,EAAEK,IAAI,GAAG,CAAC,EAAEA,IAAI,GAAGL,IAAI,EAAEK,IAAI,EAAE,EAAE;MAC1GF,IAAI,CAACE,IAAI,GAAG,CAAC,CAAC,GAAGJ,SAAS,CAACI,IAAI,CAAC;IAClC;IACA,IAAIX,UAAU,GAAGR,iBAAiB,CAACoB,KAAK,CAAC,KAAK,CAAC,EAAE,CAACR,OAAO,EAAEC,OAAO,CAAC,CAACQ,MAAM,CAACJ,IAAI,CAAC,CAAC;IACjFV,SAAS,GAAG,EAAE,CAACc,MAAM,CAACtB,kBAAkB,CAACQ,SAAS,CAAC,EAAE,CAACC,UAAU,CAAC,CAAC;IAClE,OAAOA,UAAU;EACnB,CAAC;EACD,OAAO,SAASc,IAAI,GAAG;IACrBf,SAAS,CAACgB,OAAO,CAAC,UAAUb,EAAE,EAAE;MAC9B,OAAOJ,MAAM,CAACF,aAAa,CAACM,EAAE,CAAC;IACjC,CAAC,CAAC;IACFJ,MAAM,CAACJ,WAAW,GAAGF,iBAAiB;IACtCM,MAAM,CAACF,aAAa,GAAGD,sBAAsB;IAC7C,OAAOL,KAAK;EACd,CAAC;AACH"}]}