$(function() {
	// init date tables
	var jobTable = $("#job_list").dataTable({
		"deferRender": true,
		"processing" : true, 
	    "serverSide": true,
		"ajax": {
			url: base_url + "/jobinfo/pageList",
			type:"post",
	        data : function ( d ) {
	        	var obj = {};
	        	obj.jobGroup = $('#jobGroup').val();
	        	obj.executorHandler = $('#executorHandler').val();
	        	obj.start = d.start;
	        	obj.length = d.length;
                return obj;
            }
	    },
	    "searching": false,
	    "ordering": false,
	    //"scrollX": true,	// X轴滚动条，取消自适应
	    "columns": [
	                { "data": 'id', 
	                  "bSortable": false, 
	                  "visible" : true,
	      			   "render": function ( data, type, row ) {
						 		var jobKey = row.jobGroup + "_" + row.id;
						 		return jobKey;
						 }
	                },
	                { 
	                	"data": 'jobGroup', 
	                	"visible" : false,
	                	"render": function ( data, type, row ) {
	            			var groupMenu = $("#jobGroup").find("option");
	            			for ( var index in $("#jobGroup").find("option")) {
	            				if ($(groupMenu[index]).attr('value') == data) {
									return $(groupMenu[index]).html();
								}
							}
	            			return data;
	            		}
            		},
					// {
					// 	"data": 'childJobKey',
					// 	"width":'10%',
					// 	"visible" : true,
					// 	"render": function ( data, type, row ) {
					// 		var jobKey = row.jobGroup + "_" + row.id;
					// 		return jobKey;
					// 	}
					// },
	                { "data": 'jobDesc', "visible" : true,"width":'20%'},
					{
						"data": 'glueType',
						"width":'20%',
						"visible" : true,
						"render": function ( data, type, row ) {
							if ('GLUE_GROOVY'==row.glueType) {
								return "GLUE模式(Java)";
							} else if ('GLUE_SHELL'==row.glueType) {
								return "GLUE模式(Shell)";
							} else if ('GLUE_PYTHON'==row.glueType) {
								return "GLUE模式(Python)";
							} else if ('BEAN'==row.glueType) {
								return "BEAN模式：" + row.executorHandler;
							}
							return row.executorHandler;
						}
					},
	                { "data": 'executorParam', "visible" : false},
					{ "data": 'jobCron', "visible" : true,"width":'10%'},
	                { 
	                	"data": 'addTime', 
	                	"visible" : false, 
	                	"render": function ( data, type, row ) {
	                		return data?moment(new Date(data)).format("YYYY-MM-DD HH:mm:ss"):"";
	                	}
	                },
	                { 
	                	"data": 'updateTime', 
	                	"visible" : false, 
	                	"render": function ( data, type, row ) {
	                		return data?moment(new Date(data)).format("YYYY-MM-DD HH:mm:ss"):"";
	                	}
	                },
					{
						"data": 'writeType',
						"width":'15%',
						"visible" : true,
						"render": function ( data, type, row ) {
							if ('AUTO'==row.writeType) {
								return "定时";
							} else if ('REACTIVE'==row.writeType) {
								return "日终";
							}
						}
					},
	                { "data": 'alarmEmail', "visible" : false},
	                { "data": 'glueType', "visible" : false},
	                { 
	                	"data": 'jobStatus',
						"width":'10%',
	                	"visible" : true,
	                	"render": function ( data, type, row ) {
	                		if ('NORMAL' == data ) {
	                			return '<small class="label label-success" ><i class="fa fa-clock-o"></i>'+"正常" +'</small>';
							} else if ('ACQUIRED' == data ) {
	                			return '<small class="label label-success" ><i class="fa fa-clock-o"></i>'+ "正常执行" +'</small>';
							} else if ('PAUSED' == data){
								return '<small class="label label-default" title="暂停" ><i class="fa fa-clock-o"></i>'+ "暂停" +'</small>';
							} else if ('BLOCKED' == data){
								return '<small class="label label-default" title="阻塞[串行]" ><i class="fa fa-clock-o"></i>'+ "阻塞[串行]" +'</small>';
							} else if ('WAITING' == data){
								return '<small class="label label-default" title="等待执行" ><i class="fa fa-clock-o"></i>'+ "等待执行" +'</small>';
							} else if ('1' == data){
								return '<small class="label label-default" title="未执行" ><i class="fa fa-clock-o"></i>'+ "未执行" +'</small>';
							}else if ('2' == data){
								return '<small class="label label-success" title="执行成功" ><i class="fa fa-clock-o"></i>'+ "执行成功" +'</small>';
							}else if ('3' == data){
								return '<small class="label label-success" title="正在执行" ><i class="fa fa-clock-o"></i>'+ "正在执行" +'</small>';
							}else if ('4' == data){
								return '<small class="label label-default" title="执行出错" ><i class="fa fa-clock-o"></i>'+ "执行出错" +'</small>';
							}else if ('5' == data){
								return '<small class="label label-default" title="资源不足" ><i class="fa fa-clock-o"></i>'+ "资源不足" +'</small>';
							}else if ('6' == data){
								return '<small class="label label-default" title="配置错误" ><i class="fa fa-clock-o"></i>'+ "配置错误" +'</small>';
							}else {
                                return '<small class="label label-default" title="未知状态" ><i class="fa fa-clock-o"></i>'+ "定时任务" +'</small>';
                            }
	                		return data;
	                	}
	                },
	                {
						"data": '操作' ,
						"width":'15%',
	                	"render": function ( data, type, row ) {
	                		return function(){
	                			// status
	                			var pause_resume = "";
	                			var pause_reojob = "";
	                			if ('NORMAL' == row.jobStatus) {
	                				pause_resume = '<button class="btn btn-primary btn-xs job_operate" _type="job_pause" type="button">暂停</button>  ';
								} else if ('PAUSED' == row.jobStatus){
									pause_resume = '<button class="btn btn-primary btn-xs job_operate" _type="job_resume" type="button">恢复</button>  ';
								}
	                			// log url
	                			var logUrl = base_url +'/joblog/'+ row.id+"/"+row.writeType;
	                			var paramUrl = base_url +'/jobparam?jobId='+ row.id;

                                var mystarts=new Array("NORMAL","ACQUIRED","PAUSED","BLOCKED","WAITING")

								if('AUTO'==row.writeType){
                                	if("NORMAL"== row.jobStatus || "ACQUIRED" == row.jobStatus || "PAUSED"==row.jobStatus || "WAITING" == row.jobStatus){
                                        pause_reojob = '<button class="btn btn-danger btn-xs job_operate" _type="job_rem" type="button">移除</button>  ';
                                    } else {
                                        pause_reojob = '<button class="btn btn-danger btn-xs job_operate" _type="job_add" type="button">加入</button>  ';
                                    }
								}
	                	
								// html
                                tableData['key'+row.id] = row;
								var html = '<p id="'+ row.id +'" >'+
									'<button class="btn btn-primary btn-xs job_operate" _type="job_trigger" type="button">执行</button>  '+
									pause_resume +
                                    pause_reojob +
									'<button class="btn btn-primary btn-xs" type="job_del" type="button" onclick="javascript:window.open(\'' + logUrl  + '\''+','+'\'_self\')">日志</button>  '+
									'<button class="btn btn-warning btn-xs update" type="button">编辑</button>  '+
                                    '<button class="btn btn-warning btn-xs" type="job_del" type="button" onclick="javascript:window.open(\'' + paramUrl + '\''+','+'\'_self\')" >参数</button>  '+
                                    '<button class="btn btn-danger btn-xs job_operate" _type="job_del" type="button">删除</button>  '+
                                    '</p>';
	                			return html;
							};
	                	}
	                }
	            ],
		"language" : {
			"sProcessing" : "处理中...",
			"sLengthMenu" : "每页 _MENU_ 条记录",
			"sZeroRecords" : "没有匹配结果",
			"sInfo" : "第 _PAGE_ 页 ( 总共 _PAGES_ 页，_TOTAL_ 条记录 )",
			"sInfoEmpty" : "无记录",
			"sInfoFiltered" : "(由 _MAX_ 项结果过滤)",
			"sInfoPostFix" : "",
			"sSearch" : "搜索:",
			"sUrl" : "",
			"sEmptyTable" : "表中数据为空",
			"sLoadingRecords" : "载入中...",
			"sInfoThousands" : ",",
			"oPaginate" : {
				"sFirst" : "首页",
				"sPrevious" : "上页",
				"sNext" : "下页",
				"sLast" : "末页"
			},
			"oAria" : {
				"sSortAscending" : ": 以升序排列此列",
				"sSortDescending" : ": 以降序排列此列"
			}
		}
	});

    // table data
    var tableData = {};

	// 搜索按钮
	$('#searchBtn').on('click', function(){
		jobTable.fnDraw();
	});
	
	// jobGroup change
	$('#jobGroup').on('change', function(){
        //reload
        var jobGroup = $('#jobGroup').val();
        window.location.href = base_url + "/jobinfo?jobGroup=" + jobGroup;
    });
	
	// job operate
	$("#job_list").on('click', '.job_operate',function() {
		var typeName;
		var url;
		var needFresh = false;

		var type = $(this).attr("_type");
		if ("job_pause" == type) {
			typeName = "暂停";
			url = base_url + "/jobinfo/pause";
			needFresh = true;
		} else if ("job_resume" == type) {
			typeName = "恢复";
			url = base_url + "/jobinfo/resume";
			needFresh = true;
		} else if ("job_rem" == type) {
			typeName = "移除定时任务执行队列";
			url = base_url + "/jobinfo/remjob";
			needFresh = true;
		} else if ("job_add" == type) {
			typeName = "加入定时任务执行队列";
			url = base_url + "/jobinfo/addjob";
			needFresh = true;
		} else if ("job_del" == type) {
			typeName = "删除";
			url = base_url + "/jobinfo/remove";
			needFresh = true;
		} else if ("job_trigger" == type) {
			typeName = "执行";
			url = base_url + "/jobinfo/trigger";
		} else {
			return;
		}
		
		var id = $(this).parent('p').attr("id");

		layer.confirm('确认' + typeName + '?', {icon: 3, title:'系统提示'}, function(index){
			layer.close(index);

			$.ajax({
				type : 'POST',
				url : url,
				data : {
					"id" : id
				},
				dataType : "json",
				success : function(data){
					if (data.code == 200) {

						layer.open({
							title: '系统提示',
							content: typeName + "成功",
							icon: '1',
							end: function(layero, index){
								if (needFresh) {
									//window.location.reload();
									jobTable.fnDraw();
								}
							}
						});
					} else {
						layer.open({
							title: '系统提示',
							content: (data.msg || typeName + "失败"),
							icon: '2'
						});
					}
				},
			});
		});
	});
	
	// jquery.validate 自定义校验 “英文字母开头，只含有英文字母、数字和下划线”
	jQuery.validator.addMethod("myValid01", function(value, element) {
		var valid = /^[a-zA-Z][a-zA-Z0-9_]*$/;
		return this.optional(element) || valid.test(value);
	}, "只支持英文字母开头，只含有英文字母、数字和下划线");


	
	// 新增
	$(".add").click(function(){
		$('#addModal').modal({backdrop: false, keyboard: false}).modal('show');
		$('#addModal_jobGroup').change(function(){
			if("-1" == $('#addModal_jobGroup').val()){
				$('#addModal_executorHandler').empty();
				return;
			}
			var url = base_url + "/jobinfo/getHandlerByGroupId";
			var jobGroup = $('#addModal_jobGroup :selected').text();
			$.ajax({
				url:url,
				type:'POST',
				data:{
					"jobGroup" : jobGroup
				},
				dataType:'json',
				success:function(data){
					$('#addModal_executorHandler').empty();
					$.each(data, function(i){
//						alert(data[i]);
//						alert(i);
						$('#addModal_executorHandler').append($('<option value ="'+ i +'" >' + data[i] + '</option>'));
					});
				},
				error:function(){
					$('#addModal_executorHandler').empty();
					alert("执行器未注册");
				}
			});
		});
	});
	var addModalValidate = $("#addModal .form").validate({
		errorElement : 'span',  
        errorClass : 'help-block',
        focusInvalid : true,  
        rules : {
			jobDesc : {
				required : true,
				maxlength: 50
			},
            jobCron : {
            	required : true
            }
        }, 
        messages : {  
            jobDesc : {
            	required :"请输入“中文描述”."
            },
            jobCron : {
            	required :"请输入“Cron”."
            }
        },
		highlight : function(element) {  
            $(element).closest('.form-group').addClass('has-error');  
        },
        success : function(label) {  
            label.closest('.form-group').removeClass('has-error');  
            label.remove();  
        },
        errorPlacement : function(error, element) {  
            element.parent('div').append(error);  
        },
        submitHandler : function(form) {
        	$.post(base_url + "/jobinfo/add",  $("#addModal .form").serialize(), function(data, status) {
    			if (data.code == "200") {
					$('#addModal').modal('hide');
					layer.open({
						title: '系统提示',
						content: '新增任务成功',
						icon: '1',
						end: function(layero, index){
							jobTable.fnDraw();
							//window.location.reload();
						}
					});
    			} else {
					layer.open({
						title: '系统提示',
						content: (data.msg || "新增失败"),
						icon: '2'
					});
    			}
    		});
		}
	});
	$("#addModal").on('hide.bs.modal', function () {
		$("#addModal .form")[0].reset();
		addModalValidate.resetForm();
		$("#addModal .form .form-group").removeClass("has-error");
		$(".remote_panel").show();	// remote

		$("#addModal .form input[name='executorHandler']").removeAttr("readonly");
	});


    // 运行模式
    $(".glueType").change(function(){
		// executorHandler
        var $executorHandler = $(this).parents("form").find("input[name='executorHandler']");
        var glueType = $(this).val();
        if ('BEAN' != glueType) {
            $executorHandler.val("");
            $executorHandler.attr("readonly","readonly");
        } else {
            $executorHandler.removeAttr("readonly");
        }
    });

	$("#addModal .glueType").change(function(){
		// glueSource
		var glueType = $(this).val();
		if ('GLUE_GROOVY'==glueType){
			$("#addModal .form textarea[name='glueSource']").val( $("#addModal .form .glueSource_java").val() );
		} else if ('GLUE_SHELL'==glueType){
			$("#addModal .form textarea[name='glueSource']").val( $("#addModal .form .glueSource_shell").val() );
		} else if ('GLUE_PYTHON'==glueType){
			$("#addModal .form textarea[name='glueSource']").val( $("#addModal .form .glueSource_python").val() );
		}
	});

	// 更新
	$("#job_list").on('click', '.update',function() {

        var id = $(this).parent('p').attr("id");
        var row = tableData['key'+id];
        if (!row) {
            layer.open({
                title: '系统提示',
                content: ("任务信息加载失败，请刷新页面"),
                icon: '2'
            });
            return;
        }

		// base data
		$("#updateModal .form input[name='id']").val( row.id );
		$('#updateModal .form select[name=jobGroup] option[value='+ row.jobGroup +']').prop('selected', true);
		$("#updateModal .form input[name='jobDesc']").val( row.jobDesc );
		$("#updateModal .form input[name='jobCron']").val( row.jobCron );
		$("#updateModal .form input[name='author']").val( row.author );
		$("#updateModal .form input[name='alarmEmail']").val( row.alarmEmail );
		$('#updateModal .form select[name=executorRouteStrategy] option[value='+ row.executorRouteStrategy +']').prop('selected', true);
		// 2018-01-25 
//		$("#updateModal .form input[name='executorHandler']").val( row.executorHandler );
		$('#updateModal .form select[name=executorHandler] option[value='+row.executorHandler + ']').prop('selected', true);
		
		$("#updateModal .form input[name='executorParam']").val( row.executorParam );
        // $("#updateModal .form input[name='childJobKey']").val( row.childJobKey );
		$('#updateModal .form select[name=writeType] option[value='+ row.writeType +']').prop('selected', true);
		$('#updateModal .form select[name=executorBlockStrategy] option[value='+ row.executorBlockStrategy +']').prop('selected', true);
		$('#updateModal .form select[name=executorFailStrategy] option[value='+ row.executorFailStrategy +']').prop('selected', true);
		$('#updateModal .form select[name=glueType] option[value='+ row.glueType +']').prop('selected', true);

        $("#updateModal .form select[name=glueType]").change();

		// show
		$('#updateModal').modal({backdrop: false, keyboard: false}).modal('show');
	});
	var updateModalValidate = $("#updateModal .form").validate({
		errorElement : 'span',  
        errorClass : 'help-block',
        focusInvalid : true,

		rules : {
			jobDesc : {
				required : true,
				maxlength: 50
			},
			jobCron : {
				required : true
			}
		},
		messages : {
			jobDesc : {
				required :"请输入“描述”."
			},
			jobCron : {
				required :"请输入“Cron”."
			}
		},
		highlight : function(element) {
            $(element).closest('.form-group').addClass('has-error');  
        },
        success : function(label) {  
            label.closest('.form-group').removeClass('has-error');  
            label.remove();  
        },
        errorPlacement : function(error, element) {  
            element.parent('div').append(error);  
        },
        submitHandler : function(form) {
			// post
    		$.post(base_url + "/jobinfo/reschedule", $("#updateModal .form").serialize(), function(data, status) {
    			if (data.code == "200") {
					$('#updateModal').modal('hide');
					layer.open({
						title: '系统提示',
						content: '更新成功',
						icon: '1',
						end: function(layero, index){
							//window.location.reload();
							jobTable.fnDraw();
						}
					});
    			} else {
					layer.open({
						title: '系统提示',
						content: (data.msg || "更新失败"),
						icon: '2'
					});
    			}
    		});
		}
	});
	$("#updateModal").on('hide.bs.modal', function () {
		$("#updateModal .form")[0].reset()
	});

});
