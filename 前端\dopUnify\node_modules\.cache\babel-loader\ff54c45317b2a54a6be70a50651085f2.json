{"remainingRequest": "E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-q<PERSON><PERSON>n\\数字运营平台-统一门户工程\\dopUnify\\node_modules\\babel-loader\\lib\\index.js!E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-q<PERSON>duan\\数字运营平台-统一门户工程\\dopUnify\\node_modules\\eslint-loader\\index.js??ref--13-0!E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan\\数字运营平台-统一门户工程\\dopUnify\\src\\utils\\permissions.js", "dependencies": [{"path": "E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-<PERSON><PERSON><PERSON>n\\数字运营平台-统一门户工程\\dopUnify\\src\\utils\\permissions.js", "mtime": 1686019809841}, {"path": "E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-<PERSON><PERSON><PERSON>n\\数字运营平台-统一门户工程\\dopUnify\\babel.config.js", "mtime": 1686019818359}, {"path": "E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-<PERSON><PERSON><PERSON>n\\数字运营平台-统一门户工程\\dopUnify\\.babelrc", "mtime": 1705285078668}, {"path": "E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-<PERSON><PERSON><PERSON>n\\数字运营平台-统一门户工程\\dopUnify\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-<PERSON><PERSON><PERSON>n\\数字运营平台-统一门户工程\\dopUnify\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-<PERSON><PERSON><PERSON>n\\数字运营平台-统一门户工程\\dopUnify\\node_modules\\eslint-loader\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["store", "commonBlank", "dictionaryGet", "permissionsBtn", "btnRoute", "btnPage", "btnIdArr", "btnEnabledArr", "split", "btnArr", "item", "indexOf", "getters", "userNo", "commonCompareIncludeModule", "moduleId", "flag", "includeModule", "state", "user", "loginInfo"], "sources": ["E:/1_Project/XYD_Project/dop-4.0/dop-4.1-qian<PERSON>n/数字运营平台-统一门户工程/dopUnify/src/utils/permissions.js"], "sourcesContent": ["/* 权限配置 */\nimport store from '@/store'\nimport { commonBlank } from '@/utils/common'\nimport { dictionaryGet } from '@/utils/dictionary'\n// import { Common } from '@/api'\n// const { btnLimit } = Common\n\n/**\n * 按钮权限配置: 获取当前点击菜单的按钮权限: 返回按钮是否可用（true|false）的数组\n * @param {String} btnRoute 按钮标识， 路由返回当前用户已有权限\n * @param {Object} btnPage 按钮对象，当前页所有按钮\n */\nexport const permissionsBtn = (btnRoute, btnPage) => {\n  const btnIdArr = btnRoute\n  // const btnAll = ''\n  if (commonBlank(btnIdArr)) {\n    // const msg = {\n    //   'userNo': store.getters.userNo,\n    //   'oper_type': 'buttonIdCheck'\n    // }\n    // btnLimit(msg).then(response => {\n    //   const { list } = response\n    //   btnAll = list\n    //   // commonMsgSuccess(response.retMsg, this)\n    // }).catch(() => {\n    // })\n  }\n  const btnEnabledArr = btnRoute.split(',') // 当前页面按钮权限\n  const btnArr = {}\n  for (const item in btnPage) {\n    if (btnEnabledArr.indexOf(item) !== -1 ||\n        dictionaryGet('SYS_ADMIN').indexOf(store.getters.userNo) !== -1 // 系统超级管理员默认具有所有权限\n        // btnAll.indexOf(item) !== -1 // 普通用户查询出所有的权限按钮并获取权限\n    ) {\n      btnArr[item] = true\n    } else {\n      btnArr[item] = false\n    }\n  }\n  return btnArr\n}\n\n/**\n * 预警模块权限配置\n *  判断系统模块列表函数\n *  @param {String} moduleId : 判断当前系统是否包含的 模块编号\n *  flag :返回值 false-不包含 true-包含\n */\nexport const commonCompareIncludeModule = (moduleId) => {\n  // 返回值\n  let flag = false\n  // 获取系统模块列表\n  const includeModule = ',' + store.state.user.loginInfo.includeModule + ','\n  if (includeModule.indexOf(',' + moduleId + ',') !== -1) {\n    flag = true\n  }\n  return flag\n}\n"], "mappings": "AAAA;AACA,OAAOA,KAAK,MAAM,SAAS;AAC3B,SAASC,WAAW,QAAQ,gBAAgB;AAC5C,SAASC,aAAa,QAAQ,oBAAoB;AAClD;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA,OAAO,IAAMC,cAAc,GAAG,SAAjBA,cAAc,CAAIC,QAAQ,EAAEC,OAAO,EAAK;EACnD,IAAMC,QAAQ,GAAGF,QAAQ;EACzB;EACA,IAAIH,WAAW,CAACK,QAAQ,CAAC,EAAE;IACzB;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;EAAA;EAEF,IAAMC,aAAa,GAAGH,QAAQ,CAACI,KAAK,CAAC,GAAG,CAAC,EAAC;EAC1C,IAAMC,MAAM,GAAG,CAAC,CAAC;EACjB,KAAK,IAAMC,IAAI,IAAIL,OAAO,EAAE;IAC1B,IAAIE,aAAa,CAACI,OAAO,CAACD,IAAI,CAAC,KAAK,CAAC,CAAC,IAClCR,aAAa,CAAC,WAAW,CAAC,CAACS,OAAO,CAACX,KAAK,CAACY,OAAO,CAACC,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC;IAChE;IAAA,EACF;MACAJ,MAAM,CAACC,IAAI,CAAC,GAAG,IAAI;IACrB,CAAC,MAAM;MACLD,MAAM,CAACC,IAAI,CAAC,GAAG,KAAK;IACtB;EACF;EACA,OAAOD,MAAM;AACf,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,IAAMK,0BAA0B,GAAG,SAA7BA,0BAA0B,CAAIC,QAAQ,EAAK;EACtD;EACA,IAAIC,IAAI,GAAG,KAAK;EAChB;EACA,IAAMC,aAAa,GAAG,GAAG,GAAGjB,KAAK,CAACkB,KAAK,CAACC,IAAI,CAACC,SAAS,CAACH,aAAa,GAAG,GAAG;EAC1E,IAAIA,aAAa,CAACN,OAAO,CAAC,GAAG,GAAGI,QAAQ,GAAG,GAAG,CAAC,KAAK,CAAC,CAAC,EAAE;IACtDC,IAAI,GAAG,IAAI;EACb;EACA,OAAOA,IAAI;AACb,CAAC"}]}