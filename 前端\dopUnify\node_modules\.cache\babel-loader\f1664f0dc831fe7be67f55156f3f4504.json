{"remainingRequest": "E:\\华南区项目\\中国银行\\开发区\\科技公司运营平台\\前端\\dopUnify\\node_modules\\thread-loader\\dist\\cjs.js!E:\\华南区项目\\中国银行\\开发区\\科技公司运营平台\\前端\\dopUnify\\node_modules\\babel-loader\\lib\\index.js!E:\\华南区项目\\中国银行\\开发区\\科技公司运营平台\\前端\\dopUnify\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!E:\\华南区项目\\中国银行\\开发区\\科技公司运营平台\\前端\\dopUnify\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\华南区项目\\中国银行\\开发区\\科技公司运营平台\\前端\\dopUnify\\src\\views\\login\\index.vue?vue&type=script&lang=js&", "dependencies": [{"path": "E:\\华南区项目\\中国银行\\开发区\\科技公司运营平台\\前端\\dopUnify\\src\\views\\login\\index.vue", "mtime": 1721631954662}, {"path": "E:\\华南区项目\\中国银行\\开发区\\科技公司运营平台\\前端\\dopUnify\\babel.config.js", "mtime": 1721631954662}, {"path": "E:\\华南区项目\\中国银行\\开发区\\科技公司运营平台\\前端\\dopUnify\\.babelrc", "mtime": 1721631954662}, {"path": "E:\\华南区项目\\中国银行\\开发区\\科技公司运营平台\\前端\\dopUnify\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "E:\\华南区项目\\中国银行\\开发区\\科技公司运营平台\\前端\\dopUnify\\node_modules\\thread-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "E:\\华南区项目\\中国银行\\开发区\\科技公司运营平台\\前端\\dopUnify\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "E:\\华南区项目\\中国银行\\开发区\\科技公司运营平台\\前端\\dopUnify\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "E:\\华南区项目\\中国银行\\开发区\\科技公司运营平台\\前端\\dopUnify\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1655715099000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:aW1wb3J0ICJjb3JlLWpzL21vZHVsZXMvZXMuanNvbi5zdHJpbmdpZnkuanMiOwppbXBvcnQgImNvcmUtanMvbW9kdWxlcy9lcy5yZWdleHAuZXhlYy5qcyI7CmltcG9ydCAiY29yZS1qcy9tb2R1bGVzL2VzLnN0cmluZy5yZXBsYWNlLmpzIjsKaW1wb3J0ICJjb3JlLWpzL21vZHVsZXMvZXNuZXh0LnN0cmluZy5yZXBsYWNlLWFsbC5qcyI7CmltcG9ydCAiY29yZS1qcy9tb2R1bGVzL2VzLm9iamVjdC50by1zdHJpbmcuanMiOwppbXBvcnQgImNvcmUtanMvbW9kdWxlcy93ZWIuZG9tLWNvbGxlY3Rpb25zLmZvci1lYWNoLmpzIjsKaW1wb3J0ICJjb3JlLWpzL21vZHVsZXMvZXMuc3RyaW5nLnRyaW0uanMiOwppbXBvcnQgImNvcmUtanMvbW9kdWxlcy9lcy5hcnJheS5jb25jYXQuanMiOwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwoKaW1wb3J0ICdAL2Fzc2V0cy9zY3NzL2xvZ2luLnNjc3MnOyAvLyDmoLflvI8KaW1wb3J0IGRlZmF1bHRTZXR0aW5ncyBmcm9tICdAL3NldHRpbmdzJzsKaW1wb3J0IHsgZGF0ZU5vd0Zvcm1hdCB9IGZyb20gJ0AvdXRpbHMvZGF0ZSc7CmltcG9ydCB7IGNvbW1vbk1zZ0Vycm9yIH0gZnJvbSAnQC91dGlscy9tZXNzYWdlJzsKaW1wb3J0IHsgY29tbW9uQmxhbmsgfSBmcm9tICdAL3V0aWxzL2NvbW1vbic7CmltcG9ydCB7IGVuY3J5cHRSZXN1bHQsIGRlY3J5cHRSZXN1bHQgfSBmcm9tICdAL3V0aWxzL2NyeXB0byc7CmltcG9ydCB7IGNvbW1vbk1zZ1N1Y2Nlc3MsIGNvbW1vbk1zZ1dhcm4sIGNvbW1vbk1zZ0NvbmZpcm0gfSBmcm9tICdAL3V0aWxzL21lc3NhZ2UuanMnOyAvLyDmj5DnpLrkv6Hmga8KaW1wb3J0IHsgZGF0ZVRpbWVGb3JtYXQgfSBmcm9tICdAL2ZpbHRlcnMnOwppbXBvcnQgTG90dGllIGZyb20gJ3Z1ZS1sb3R0aWUvc3JjL2xvdHRpZS52dWUnOyAvLyDliqjnlLvnu4Tku7YKaW1wb3J0IHsgTG9naW5EYXRhSnNvbiB9IGZyb20gJy4vZGF0YSc7IC8vIOWKqOeUu2pzb24KaW1wb3J0IHsgZ2V0WFRva2VuIH0gZnJvbSAnQC91dGlscy9hdXRoJzsKaW1wb3J0IHsgY29uZmlnLCBtb2RpZnlDb25maWcgfSBmcm9tICcuL2luZm8nOyAvLyDooajljZXphY3nva4KaW1wb3J0IHsgTG9naW4gfSBmcm9tICdAL2FwaSc7CnZhciB0aXRsZSA9IGRlZmF1bHRTZXR0aW5ncy50aXRsZSwKICBzZXJ2aWNlID0gZGVmYXVsdFNldHRpbmdzLnNlcnZpY2U7CnZhciB0aW1lQ2hlY2sgPSBMb2dpbi50aW1lQ2hlY2ssCiAgY2hlY2tJbml0ID0gTG9naW4uY2hlY2tJbml0LAogIHZlcmlmeUlkZW50aWR5ID0gTG9naW4udmVyaWZ5SWRlbnRpZHksCiAgc2VuZFZlcmlmeUNvZGUgPSBMb2dpbi5zZW5kVmVyaWZ5Q29kZSwKICByZXNldFB3ZCA9IExvZ2luLnJlc2V0UHdkOwpleHBvcnQgZGVmYXVsdCB7CiAgbmFtZTogJ0xvZ2luJywKICBjb21wb25lbnRzOiB7CiAgICBMb3R0aWU6IExvdHRpZQogIH0sCiAgZGF0YTogZnVuY3Rpb24gZGF0YSgpIHsKICAgIHZhciB2YWxpZGF0ZVBhc3N3b3JkID0gZnVuY3Rpb24gdmFsaWRhdGVQYXNzd29yZChydWxlLCB2YWx1ZSwgY2FsbGJhY2spIHsKICAgICAgaWYgKHZhbHVlLmxlbmd0aCA8IDUpIHsKICAgICAgICBjYWxsYmFjayhuZXcgRXJyb3IoJ+WvhueggeS4jeiDveWwkeS6jjXkvY0nKSk7CiAgICAgIH0gZWxzZSB7CiAgICAgICAgY2FsbGJhY2soKTsKICAgICAgfQogICAgfTsKICAgIHJldHVybiB7CiAgICAgIGxvYWRMb2dpbjogZmFsc2UsCiAgICAgIC8vIOaOpeWPo+WKoOi9veS4rQogICAgICB0aXRsZTogdGl0bGUsCiAgICAgIGxvZ2luQnRuOiAn55m75b2VJywKICAgICAgbG9naW5Gb3JtOiB7CiAgICAgICAgYmxhbmtObzogJ1BCQycsCiAgICAgICAgdXNlcm5hbWU6ICcnLAogICAgICAgIHBhc3N3b3JkOiB0aGlzLiRzdG9yZS5nZXR0ZXJzLmVtcHR5UHN3ZCwKICAgICAgICBjb2RlOiAnJwogICAgICB9LAogICAgICBsb2dpblJ1bGVzOiB7CiAgICAgICAgdXNlcm5hbWU6IFt7CiAgICAgICAgICByZXF1aXJlZDogdHJ1ZSwKICAgICAgICAgIG1lc3NhZ2U6ICfor7fovpPlhaXmraPnoa7nmoTnlKjmiLflkI0nCiAgICAgICAgfV0sCiAgICAgICAgcGFzc3dvcmQ6IFt7CiAgICAgICAgICByZXF1aXJlZDogdHJ1ZSwKICAgICAgICAgIHZhbGlkYXRvcjogdmFsaWRhdGVQYXNzd29yZAogICAgICAgIH1dLAogICAgICAgIGNvZGU6IFt7CiAgICAgICAgICByZXF1aXJlZDogdHJ1ZSwKICAgICAgICAgIG1lc3NhZ2U6ICfpqozor4HnoIHkuI3og73kuLrnqbonCiAgICAgICAgfSwgewogICAgICAgICAgcGF0dGVybjogL15bXFNdezEsNX0kLywKICAgICAgICAgIG1lc3NhZ2U6ICfmnIDlpJrlj6/ovpM15Liq6Z2e56m65qC85a2X56ymJwogICAgICAgIH1dCiAgICAgIH0sCiAgICAgIHBzd2RUeXBlOiAncGFzc3dvcmQnLAogICAgICBjYXBzVG9vbHRpcDogZmFsc2UsCiAgICAgIGxvYWRpbmc6IGZhbHNlLAogICAgICByZWRpcmVjdDogdW5kZWZpbmVkLAogICAgICAvLyDph43lrprlkJEKICAgICAgYWN0aXZlTmFtZTogJ2ZpcnN0JywKICAgICAgLy8g5b2T5YmN6YCJ6aG55Y2hCgogICAgICBhbmltT3B0aW9uczogewogICAgICAgIGFuaW1hdGlvbkRhdGE6IExvZ2luRGF0YUpzb24sCiAgICAgICAgbG9vcDogdHJ1ZSwKICAgICAgICBhdXRvcGxheTogdHJ1ZQogICAgICB9LAogICAgICAvLyDliqjnlLvphY3nva4KICAgICAgZm9yZ2V0RGlhbG9nOiB7CiAgICAgICAgY29tcG9uZW50UHJvcHM6IHsKICAgICAgICAgIC8vIOW8ueWHuuahhumFjee9ruWxnuaApwogICAgICAgICAgd2lkdGg6ICc2MHJlbScsCiAgICAgICAgICB0aXRsZTogJ+aJvuWbnuWvhueggScKICAgICAgICB9LAogICAgICAgIHN1YlRpdGxlOiAn6YCa6L+H5omL5py65Y+35om+5ZueJywKICAgICAgICB2aXNpYmxlOiBmYWxzZSwKICAgICAgICBjdXN0b21CdXR0b246IHRydWUsCiAgICAgICAgZm9ybTogewogICAgICAgICAgY29uZmlnOiBjb25maWcodGhpcyksCiAgICAgICAgICBsYWJlbFdpZHRoOiAnMTVyZW0nLAogICAgICAgICAgLy8g5b2T5YmN6KGo5Y2V5qCH562+5a695bqm6YWN572uCiAgICAgICAgICBkZWZhdWx0Rm9ybTogewogICAgICAgICAgICB1c2VyX25vOiAnJywKICAgICAgICAgICAgcGhvbmU6ICcnLAogICAgICAgICAgICBjb2RlOiAnJwogICAgICAgICAgfQogICAgICAgIH0KICAgICAgfSwKICAgICAgLy8g5b+Y6K6w5a+G56CB5by556qXCiAgICAgIGNvZGVGbGFnOiAnMCcsCiAgICAgIC8vIOmqjOivgeeggeaYr+WQpuaYvuekugogICAgICBpbWdVcmw6ICcnLAogICAgICAvLyDpqozor4HnoIHlm77niYcKICAgICAgYnRuSW5kZXg6ICcxJywKICAgICAgLy8g5om+5Zue5a+G56CB5by556qXIOW9k+WJjeaYvuekuueahOaMiemSrgogICAgICBjdXJyQnRuSW5kZXg6ICcwJywKICAgICAgLy8g5b2T5YmN54K55Ye755qE5oyJ6ZKuIDAt6I635Y+W6aqM6K+B56CBIDEt5LiL5LiA5q2lIDIt5L+u5pS55a6M5oiQCiAgICAgIGNoZWNrQ29kZTogJycsCiAgICAgIC8vIOeUqOaIt+e8luWPt+agoemqjOeggQogICAgICBzeXN0ZW1MaXN0OiBbXSwKICAgICAgc2hvd0Jhbms6ICcnLAogICAgICAvLyDmmK/lkKblsZXnpLrpk7booYzlj7cKICAgICAgaW1nU3R5bGU6IHsKICAgICAgICB0cmFuc2Zvcm06ICd0cmFuc2xhdGUoMTc2cHgsIDQ1cHgpJwogICAgICB9CiAgICB9OwogIH0sCiAgd2F0Y2g6IHsKICAgICRyb3V0ZTogewogICAgICAvLyDlr7not6/nlLHlj5jljJbkvZzlh7rlk43lupQuLi4KICAgICAgaGFuZGxlcjogZnVuY3Rpb24gaGFuZGxlcihyb3V0ZSkgewogICAgICAgIHZhciBxdWVyeSA9IHJvdXRlLnF1ZXJ5OwogICAgICAgIGlmIChxdWVyeSkgewogICAgICAgICAgLy8gdGhpcy5yZWRpcmVjdCA9IHF1ZXJ5LnJlZGlyZWN0IC8vIOS/neWtmOmhtemdoumAgOWHuuWQjueahOm7mOiupOmAiemhueWNoQogICAgICAgIH0KICAgICAgfSwKICAgICAgaW1tZWRpYXRlOiB0cnVlIC8vIOehruiupOaYr+WQpuS7peW9k+WJjeeahOWIneWni+WAvOaJp+ihjGhhbmRsZXLnmoTlh73mlbDjgIIKICAgIH0sCiAgICAnZm9yZ2V0RGlhbG9nLnN1YlRpdGxlJzogZnVuY3Rpb24gZm9yZ2V0RGlhbG9nU3ViVGl0bGUodmFsKSB7CiAgICAgIC8vIOebkeWQrOaJvuWbnuWvhueggeW8ueeql+WJr+agh+mimOeahOWPmOWMliDmm7TmlLnlvLnnqpflhoXooajljZXku6Xlj4rmjInpkq4KICAgICAgaWYgKHZhbCA9PT0gJ+S/ruaUueWvhueggScpIHsKICAgICAgICB0aGlzLmZvcmdldERpYWxvZy5mb3JtLmNvbmZpZyA9IG1vZGlmeUNvbmZpZyh0aGlzKTsKICAgICAgICB2YXIgdXNlck5vID0gdGhpcy5mb3JnZXREaWFsb2cuZm9ybS5kZWZhdWx0Rm9ybS51c2VyX25vOwogICAgICAgIHRoaXMuZm9yZ2V0RGlhbG9nLmZvcm0uZGVmYXVsdEZvcm0gPSB7CiAgICAgICAgICB1c2VyX25vOiB1c2VyTm8sCiAgICAgICAgICBuZXdQYXNzd29yZDogdGhpcy4kc3RvcmUuZ2V0dGVycy5lbXB0eVBzd2QsCiAgICAgICAgICBjb25maXJtUGFzc3dvcmQ6IHRoaXMuJHN0b3JlLmdldHRlcnMuZW1wdHlQc3dkCiAgICAgICAgfTsKICAgICAgICB0aGlzLmJ0bkluZGV4ID0gJzInOwogICAgICB9CiAgICB9CiAgfSwKICBiZWZvcmVNb3VudDogZnVuY3Rpb24gYmVmb3JlTW91bnQoKSB7CiAgICB3aW5kb3cuYWRkRXZlbnRMaXN0ZW5lcigncmVzaXplJywgdGhpcy5yZXNpemVIYW5kbGVyKTsKICB9LAogIGJlZm9yZURlc3Ryb3k6IGZ1bmN0aW9uIGJlZm9yZURlc3Ryb3koKSB7CiAgICB3aW5kb3cucmVtb3ZlRXZlbnRMaXN0ZW5lcigncmVzaXplJywgdGhpcy5yZXNpemVIYW5kbGVyKTsKICB9LAogIG1vdW50ZWQ6IGZ1bmN0aW9uIG1vdW50ZWQoKSB7CiAgICB2YXIgX3RoaXMgPSB0aGlzOwogICAgdGhpcy4kcmVmcy51c2VybmFtZS5mb2N1cygpOwogICAgdGhpcy5oYW5kbGVJc1ZlcmlmeSgpOwogICAgLy8gdGhpcy5jaGVja1RpbWUoKQogICAgdGhpcy4kbmV4dFRpY2soZnVuY3Rpb24gKCkgewogICAgICBfdGhpcy5yZXNpemVIYW5kbGVyKCk7CiAgICB9KTsKICB9LAogIGRlc3Ryb3llZDogZnVuY3Rpb24gZGVzdHJveWVkKCkge30sCiAgbWV0aG9kczogewogICAgaGFuZGxlQ2xpY2s6IGZ1bmN0aW9uIGhhbmRsZUNsaWNrKCkgewogICAgICBpZiAodGhpcy5hY3RpdmVOYW1lID09PSAnZmlyc3QnKSB7CiAgICAgICAgdGhpcy5pbWdTdHlsZSA9IHsKICAgICAgICAgIHRyYW5zZm9ybTogJ3RyYW5zbGF0ZSgxNzZweCwgNDVweCknCiAgICAgICAgfTsKICAgICAgfSBlbHNlIHsKICAgICAgICB0aGlzLmltZ1N0eWxlID0gewogICAgICAgICAgdHJhbnNmb3JtOiAndHJhbnNsYXRlKDI3OXB4LCA0NXB4KScKICAgICAgICB9OwogICAgICB9CiAgICB9LAogICAgLyoqCiAgICAgKiDojrflj5bliJ3lp4vljJblj4LmlbAKICAgICAqLwogICAgaGFuZGxlSXNWZXJpZnk6IGZ1bmN0aW9uIGhhbmRsZUlzVmVyaWZ5KCkgewogICAgICB2YXIgX3RoaXMyID0gdGhpczsKICAgICAgdGhpcy5zeXN0ZW1MaXN0ID0gW107CiAgICAgIHZhciBwYXJhbSA9IHsKICAgICAgICBzVHh0OiBlbmNyeXB0UmVzdWx0KCdTTTQnLCBKU09OLnN0cmluZ2lmeSh7CiAgICAgICAgICBjdXJyZW50VGltZTogZGF0ZVRpbWVGb3JtYXQobmV3IERhdGUoKSkucmVwbGFjZUFsbCgnICcsICcnKS5yZXBsYWNlQWxsKCctJywgJycpLnJlcGxhY2VBbGwoJzonLCAnJykKICAgICAgICB9KSkKICAgICAgfTsKICAgICAgdGhpcy5sb2FkTG9naW4gPSB0cnVlOwogICAgICBjaGVja0luaXQocGFyYW0pLnRoZW4oZnVuY3Rpb24gKHJlc3BvbnNlKSB7CiAgICAgICAgLy8g6I635Y+WeC10b2tlbgogICAgICAgIHZhciB4VG9rZW4gPSBnZXRYVG9rZW4oKTsKICAgICAgICBfdGhpczIuJHN0b3JlLmNvbW1pdCgndXNlci9TRVRfWF9UT0tFTicsIHhUb2tlbik7CiAgICAgICAgdmFyIHJldE1hcCA9IHJlc3BvbnNlLnJldE1hcDsKICAgICAgICBfdGhpczIuc2hvd0JhbmsgPSByZXRNYXAuc2hvd0Jhbms7CiAgICAgICAgcmV0TWFwLnN5c3RlbUxpc3QuZm9yRWFjaChmdW5jdGlvbiAoaXRlbSkgewogICAgICAgICAgX3RoaXMyLnN5c3RlbUxpc3QucHVzaCh7CiAgICAgICAgICAgIHZhbHVlOiBpdGVtLmZpZWxkX25vLAogICAgICAgICAgICBsYWJlbDogaXRlbS5maWVsZF92YWx1ZSAvLyBgJHtpdGVtLmJsYW5rTm99LSR7aXRlbS5maWVsZF92YWx1ZX1gCiAgICAgICAgICB9KTsKICAgICAgICB9KTsKCiAgICAgICAgaWYgKHJldE1hcC50aW1lQ2hlY2sgPT09ICcyJykgewogICAgICAgICAgX3RoaXMyLiRyb3V0ZXIucmVwbGFjZSgnL2Vycm9yMicpOwogICAgICAgIH0KICAgICAgICB2YXIgZW5TZWNNYXAgPSBKU09OLnBhcnNlKGRlY3J5cHRSZXN1bHQoJ1NNNCcsIHJldE1hcC5lblNlYykpOwogICAgICAgIHZhciBtc2cgPSB7CiAgICAgICAgICBlblNlY01hcDogZW5TZWNNYXAsCiAgICAgICAgICBwYXNzUmVndWxhcjogcmV0TWFwLnBhc3NSZWd1bGFyLAogICAgICAgICAgLy8g5a+G56CB5qCh6aqM6KeE5YiZCiAgICAgICAgICBwYXNzVGlwczogcmV0TWFwLnBhc3NUaXBzIC8vIOWvhueggeaPkOekuuivrQogICAgICAgIH07CgogICAgICAgIF90aGlzMi4kc3RvcmUuY29tbWl0KCd1c2VyL1NFVF9JTklUX1BBUkFNUycsIG1zZyk7CiAgICAgICAgX3RoaXMyLmNvZGVGbGFnID0gcmV0TWFwLmNvZGVGbGFnOwogICAgICAgIGlmIChyZXNwb25zZS5yZXRNYXAuY29kZUZsYWcgPT09ICcxJykgewogICAgICAgICAgX3RoaXMyLmltZ1VybCA9IHByb2Nlc3MuZW52LlZVRV9BUFBfQkFTRV9BUEkgKyBzZXJ2aWNlLnN5c3RlbSArICcvYXV0aC9jb2RlLmRvP3RpbWU9JyArIG5ldyBEYXRlKCk7CiAgICAgICAgfQogICAgICAgIF90aGlzMi5sb2FkTG9naW4gPSBmYWxzZTsKICAgICAgfSkuY2F0Y2goZnVuY3Rpb24gKCkgewogICAgICAgIF90aGlzMi5sb2FkTG9naW4gPSBmYWxzZTsKICAgICAgfSk7CiAgICB9LAogICAgLyoqCiAgICAgKiDliLfmlrDpqozor4HnoIEKICAgICAqLwogICAgcmVzZXRJbWc6IGZ1bmN0aW9uIHJlc2V0SW1nKCkgewogICAgICB0aGlzLmltZ1VybCA9IHByb2Nlc3MuZW52LlZVRV9BUFBfQkFTRV9BUEkgKyBzZXJ2aWNlLnN5c3RlbSArICcvYXV0aC9jb2RlLmRvP3RpbWU9JyArIG5ldyBEYXRlKCk7CiAgICAgIHRoaXMubG9naW5Gb3JtLmNvZGUgPSAnJzsKICAgIH0sCiAgICAvKioKICAgICAqIOWkp+WGmemUgeWumumUruW8gOWQr+WFs+mXrSovCiAgICBjaGVja0NhcHNsb2NrOiBmdW5jdGlvbiBjaGVja0NhcHNsb2NrKGUpIHsKICAgICAgdmFyIGtleSA9IGUua2V5OwogICAgICB0aGlzLmNhcHNUb29sdGlwID0ga2V5ICYmIGtleS5sZW5ndGggPT09IDEgJiYga2V5ID49ICdBJyAmJiBrZXkgPD0gJ1onOwogICAgfSwKICAgIC8qKgogICAgICog54Sm54K56YWN572uIC0g5Zyo55So5oi35ZCN5qGG5Lit5Zue6L2mKi8KICAgIHBhc3NXb3JkRm9jdXM6IGZ1bmN0aW9uIHBhc3NXb3JkRm9jdXMoKSB7CiAgICAgIHRoaXMuJHJlZnMucGFzc3dvcmQuZm9jdXMoKTsKICAgIH0sCiAgICAvKioKICAgICAqIOeEpueCuemFjee9riAtIOWcqOWvhueggeahhuS4reWbnui9pgogICAgICovCiAgICBjb2RlRm9jdXM6IGZ1bmN0aW9uIGNvZGVGb2N1cygpIHsKICAgICAgaWYgKHRoaXMuY29kZUZsYWcgPT09ICcxJykgewogICAgICAgIHRoaXMuJHJlZnMuY29kZS5mb2N1cygpOwogICAgICAgIHJldHVybjsKICAgICAgfQogICAgICB0aGlzLmhhbmRsZUxvZ2luKCk7CiAgICB9LAogICAgLyoqCiAgICAgKiDlr4bnoIHmmL7npLrpmpDol48qLwogICAgc2hvd1B3ZDogZnVuY3Rpb24gc2hvd1B3ZCgpIHsKICAgICAgdmFyIF90aGlzMyA9IHRoaXM7CiAgICAgIGlmICh0aGlzLnBzd2RUeXBlID09PSAncGFzc3dvcmQnKSB7CiAgICAgICAgdGhpcy5wc3dkVHlwZSA9IHRoaXMuJHN0b3JlLmdldHRlcnMuZW1wdHlQc3dkOwogICAgICB9IGVsc2UgewogICAgICAgIHRoaXMucHN3ZFR5cGUgPSAncGFzc3dvcmQnOwogICAgICB9CiAgICAgIHRoaXMuJG5leHRUaWNrKGZ1bmN0aW9uICgpIHsKICAgICAgICBfdGhpczMuJHJlZnMucGFzc3dvcmQuZm9jdXMoKTsKICAgICAgfSk7CiAgICB9LAogICAgLyoqCiAgICAgKiDnmbvlvZUqLwogICAgaGFuZGxlTG9naW46IGZ1bmN0aW9uIGhhbmRsZUxvZ2luKCkgewogICAgICB2YXIgX3RoaXM0ID0gdGhpczsKICAgICAgdGhpcy4kcmVmcy5sb2dpbkZvcm0udmFsaWRhdGUoZnVuY3Rpb24gKHZhbGlkKSB7CiAgICAgICAgaWYgKHZhbGlkKSB7CiAgICAgICAgICBfdGhpczQubG9hZExvZ2luID0gdHJ1ZTsKICAgICAgICAgIF90aGlzNC5sb2FkaW5nID0gdHJ1ZTsKICAgICAgICAgIF90aGlzNC4kc3RvcmUuZGlzcGF0Y2goJ3VzZXIvbG9naW4nLCBfdGhpczQubG9naW5Gb3JtKS50aGVuKGZ1bmN0aW9uICgpIHsKICAgICAgICAgICAgLy8gbG9jYWxTdG9yYWdlLnNldEl0ZW0oJ3VzZXJTeXN0ZW1ObycsIHRoaXMubG9naW5Gb3JtLmJsYW5rTm8pCiAgICAgICAgICAgIHZhciBjaGlsZFVybCA9IGxvY2FsU3RvcmFnZS5nZXRJdGVtKCdjaGlsZFVybCcpOyAvLyDlj5blh7rku47lrZDlt6XnqIvkvKDlm57nmoTlnLDlnYAKICAgICAgICAgICAgaWYgKCFjb21tb25CbGFuayhjaGlsZFVybCkpIHsKICAgICAgICAgICAgICAvLyDphY3nva7mmK/lkKbov5vooYzlrZDlt6XnqIvot7PovawKICAgICAgICAgICAgICAvLyBjb25zdCBxdWVyeSA9IHsKICAgICAgICAgICAgICAvLyAgIHN0b3JlOiB7CiAgICAgICAgICAgICAgLy8gICAgIHVzZXI6IHsKICAgICAgICAgICAgICAvLyAgICAgICAuLi50aGlzLiRzdG9yZS5zdGF0ZS51c2VyLAogICAgICAgICAgICAgIC8vICAgICAgIHBhc3N3b3JkOiBlbmNyeXB0UmVzdWx0KAogICAgICAgICAgICAgIC8vICAgICAgICAgdGhpcy4kc3RvcmUuZ2V0dGVycy5pbml0UGFyYW1zLmVuU2VjTWFwLmVuY3J5cHRUeXBlLAogICAgICAgICAgICAgIC8vICAgICAgICAgdGhpcy5sb2dpbkZvcm0ucGFzc3dvcmQKICAgICAgICAgICAgICAvLyAgICAgICApCiAgICAgICAgICAgICAgLy8gICAgIH0KICAgICAgICAgICAgICAvLyAgIH0KICAgICAgICAgICAgICAvLyB9CiAgICAgICAgICAgICAgLy8gcXVlcnkuc3RvcmUudXNlci5pbml0UGFyYW1zID0ge30KCiAgICAgICAgICAgICAgLy8g6YWN572u6Lez6L2s5Yiw5a2Q5bel56iL55qE55m75b2V5pWw5o2uCiAgICAgICAgICAgICAgdmFyIF90aGlzNCRsb2dpbkZvcm0gPSBfdGhpczQubG9naW5Gb3JtLAogICAgICAgICAgICAgICAgYmxhbmtObyA9IF90aGlzNCRsb2dpbkZvcm0uYmxhbmtObywKICAgICAgICAgICAgICAgIHVzZXJuYW1lID0gX3RoaXM0JGxvZ2luRm9ybS51c2VybmFtZSwKICAgICAgICAgICAgICAgIHBhc3N3b3JkID0gX3RoaXM0JGxvZ2luRm9ybS5wYXNzd29yZCwKICAgICAgICAgICAgICAgIGNvZGUgPSBfdGhpczQkbG9naW5Gb3JtLmNvZGU7CiAgICAgICAgICAgICAgdmFyIHF1ZXJ5ID0gewogICAgICAgICAgICAgICAgLy8g6I635Y+W55m75b2V5Y+C5pWwCiAgICAgICAgICAgICAgICBzdG9yZTogewogICAgICAgICAgICAgICAgICB1c2VyOiB7CiAgICAgICAgICAgICAgICAgICAgLy8g55m75b2V5Y+C5pWwCiAgICAgICAgICAgICAgICAgICAgcGFyYW1ldGVyTGlzdDogW3sKICAgICAgICAgICAgICAgICAgICAgIHVzZXJObzogdXNlcm5hbWUudHJpbSgpLAogICAgICAgICAgICAgICAgICAgICAgcGFzc3dvcmQ6IGVuY3J5cHRSZXN1bHQoX3RoaXM0LiRzdG9yZS5nZXR0ZXJzLmluaXRQYXJhbXMuZW5TZWNNYXAuZW5jcnlwdFR5cGUsIHBhc3N3b3JkKSwKICAgICAgICAgICAgICAgICAgICAgIGlzQ2hlY2s6ICcxJywKICAgICAgICAgICAgICAgICAgICAgIGxvZ2luVGVybWluYWw6ICcxJywKICAgICAgICAgICAgICAgICAgICAgIGxvZ2luVHlwZTogJzEnCiAgICAgICAgICAgICAgICAgICAgfV0sCiAgICAgICAgICAgICAgICAgICAgY29kZTogY29kZSwKICAgICAgICAgICAgICAgICAgICBvcGVyX3R5cGU6ICcwJywKICAgICAgICAgICAgICAgICAgICBsb2dpbktpbmQ6ICd1c2VyX25vJywKICAgICAgICAgICAgICAgICAgICBibGFua05vOiBibGFua05vCiAgICAgICAgICAgICAgICAgIH0sCiAgICAgICAgICAgICAgICAgIHRva2VuOiBfdGhpczQuJHN0b3JlLmdldHRlcnMudG9rZW4sCiAgICAgICAgICAgICAgICAgIGluaXRQYXJhbXM6IHsKICAgICAgICAgICAgICAgICAgICBlblNlY01hcDogX3RoaXM0LiRzdG9yZS5nZXR0ZXJzLmluaXRQYXJhbXMuZW5TZWNNYXAKICAgICAgICAgICAgICAgICAgfQogICAgICAgICAgICAgICAgfQogICAgICAgICAgICAgIH07CiAgICAgICAgICAgICAgdmFyIHF1ZXJ5UyA9IEpTT04uc3RyaW5naWZ5KHF1ZXJ5KTsKICAgICAgICAgICAgICBsb2NhbFN0b3JhZ2UuY2xlYXIoKTsKICAgICAgICAgICAgICAvLyByZW1vdmVUb2tlbigpCiAgICAgICAgICAgICAgd2luZG93LmxvY2F0aW9uLmhyZWYgPSAiIi5jb25jYXQoY2hpbGRVcmwsICI/IikuY29uY2F0KHF1ZXJ5Uyk7CiAgICAgICAgICAgIH0gZWxzZSB7CiAgICAgICAgICAgICAgX3RoaXM0LiRuZXh0VGljayhmdW5jdGlvbiAoKSB7CiAgICAgICAgICAgICAgICBfdGhpczQuJHJvdXRlci5wdXNoKHsKICAgICAgICAgICAgICAgICAgcGF0aDogX3RoaXM0LnJlZGlyZWN0IHx8ICcvJwogICAgICAgICAgICAgICAgfSk7CiAgICAgICAgICAgICAgICAvLyB0aGlzLmxvYWRpbmcgPSBmYWxzZQogICAgICAgICAgICAgIH0pOwogICAgICAgICAgICB9CiAgICAgICAgICB9KS5jYXRjaChmdW5jdGlvbiAocmVzcG9uc2UpIHsKICAgICAgICAgICAgdmFyIGxvZ2luTXNnID0gcmVzcG9uc2UucmV0TWFwLmxvZ2luTXNnOwogICAgICAgICAgICBjb21tb25Nc2dFcnJvcihsb2dpbk1zZywgX3RoaXM0KTsKICAgICAgICAgICAgX3RoaXM0LmxvYWRpbmcgPSBmYWxzZTsKICAgICAgICAgICAgX3RoaXM0LnJlc2V0SW1nKCk7IC8vIOWIt+aWsOmqjOivgeeggQogICAgICAgICAgICBfdGhpczQubG9hZExvZ2luID0gZmFsc2U7CiAgICAgICAgICB9KTsKICAgICAgICB9IGVsc2UgewogICAgICAgICAgcmV0dXJuIGZhbHNlOwogICAgICAgIH0KICAgICAgfSk7CiAgICB9LAogICAgLy8gZW5kCiAgICAvKioKICAgICAqIOWuouaIt+iuv+mXruaXtumXtOagoemqjCovCiAgICBjaGVja1RpbWU6IGZ1bmN0aW9uIGNoZWNrVGltZSgpIHsKICAgICAgdmFyIF90aGlzNSA9IHRoaXM7CiAgICAgIHZhciBtc2cgPSB7CiAgICAgICAgY3VucnJlbnRUaW1lOiBkYXRlTm93Rm9ybWF0KCksCiAgICAgICAgb3Blcl90eXBlOiAnVGltZUNoZWNrJwogICAgICB9OwogICAgICB0aW1lQ2hlY2sobXNnKS50aGVuKGZ1bmN0aW9uIChyZXNwb25zZSkge30pLmNhdGNoKGZ1bmN0aW9uICgpIHsKICAgICAgICBfdGhpczUuJHJvdXRlci5wdXNoKHsKICAgICAgICAgIHBhdGg6ICcvZXJyb3InCiAgICAgICAgfSk7CiAgICAgIH0pOwogICAgfSwKICAgIC8vIGNoZWNrVGltZQogICAgLy8g5Yqo55S75bC65a+46K6h566XCiAgICByZXNpemVIYW5kbGVyOiBmdW5jdGlvbiByZXNpemVIYW5kbGVyKCkgewogICAgICB2YXIgaCA9IHRoaXMuJHJlZnMubG9naW5fY29udGFpbmVyLmNsaWVudEhlaWdodDsKICAgICAgdmFyIHcgPSB0aGlzLiRyZWZzLmxvZ2luX2NvbnRhaW5lci5jbGllbnRXaWR0aDsKICAgICAgdmFyIHBlcmMgPSAxOTIwICogaCAvIDEwODA7IC8vIOiOt+WPluWKqOeUu+etieavlOWuvQogICAgICBpZiAodyA+IHBlcmMpIHsKICAgICAgICB0aGlzLiRyZWZzLmxvZ2luX2FuaW1hdGlvbi5zdHlsZS53aWR0aCA9IHcgKyAncHgnOwogICAgICAgIHRoaXMuJHJlZnMubG9naW5fYW5pbWF0aW9uLnN0eWxlLmhlaWdodCA9IHcgKiAxMDgwIC8gMTkyMCArICdweCc7CiAgICAgIH0gZWxzZSB7CiAgICAgICAgdGhpcy4kcmVmcy5sb2dpbl9hbmltYXRpb24uc3R5bGUud2lkdGggPSBwZXJjICsgJ3B4JzsKICAgICAgICB0aGlzLiRyZWZzLmxvZ2luX2FuaW1hdGlvbi5zdHlsZS5oZWlnaHQgPSBwZXJjICogMTA4MCAvIDE5MjAgKyAncHgnOwogICAgICB9CiAgICB9LAogICAgLy8g5Yqo55S7CiAgICBoYW5kbGVBbmltYXRpb246IGZ1bmN0aW9uIGhhbmRsZUFuaW1hdGlvbihhbmltKSB7CiAgICAgIHRoaXMuYW5pbSA9IGFuaW07CiAgICB9LAogICAgLy8gYnRuIC0g5b+Y6K6w5a+G56CBCiAgICBoYW5kbGVGb3JnZXRQd2Q6IGZ1bmN0aW9uIGhhbmRsZUZvcmdldFB3ZCgpIHsKICAgICAgdGhpcy5mb3JnZXREaWFsb2cudmlzaWJsZSA9IHRydWU7CiAgICB9LAogICAgLyoqCiAgICAgKiDmib7lm57lr4bnoIHlvLnnqpcg5LiL5LiA5q2l5oyJ6ZKuCiAgICAgKi8KICAgIGhhbmRsZU5leHQ6IGZ1bmN0aW9uIGhhbmRsZU5leHQoKSB7CiAgICAgIHRoaXMuY3VyckJ0bkluZGV4ID0gJzEnOwogICAgICB0aGlzLiRyZWZzLmZvcm1SZWYudmFsaWRhdGVGb3JtKCk7CiAgICB9LAogICAgLyoqCiAgICAgKiDor7fmsYIgLSDkuIvkuIDmraUKICAgICAqLwogICAgbmV4dFJlcTogZnVuY3Rpb24gbmV4dFJlcSgpIHsKICAgICAgdmFyIF90aGlzNiA9IHRoaXM7CiAgICAgIHZhciBfdGhpcyRmb3JnZXREaWFsb2ckZm8gPSB0aGlzLmZvcmdldERpYWxvZy5mb3JtLmRlZmF1bHRGb3JtLAogICAgICAgIHVzZXJfbm8gPSBfdGhpcyRmb3JnZXREaWFsb2ckZm8udXNlcl9ubywKICAgICAgICBwaG9uZSA9IF90aGlzJGZvcmdldERpYWxvZyRmby5waG9uZSwKICAgICAgICBjb2RlID0gX3RoaXMkZm9yZ2V0RGlhbG9nJGZvLmNvZGU7CiAgICAgIGlmIChjb21tb25CbGFuayhjb2RlKSkgewogICAgICAgIGNvbW1vbk1zZ1dhcm4oJ+mqjOivgeeggeS4uuepuu+8jOivt+mHjeaWsOi+k+WFpSEnLCB0aGlzKTsKICAgICAgfSBlbHNlIHsKICAgICAgICB2YXIgbXNnID0gewogICAgICAgICAgcGFyYW1ldGVyTGlzdDogW10sCiAgICAgICAgICBzeXNNYXA6IHsKICAgICAgICAgICAgb3Blcl90eXBlOiAndmVyaWZ5SWRlbnRpZHknLAogICAgICAgICAgICB1c2VyX25vOiB1c2VyX25vLAogICAgICAgICAgICBwaG9uZTogcGhvbmUsCiAgICAgICAgICAgIHZlcmlmeUNvZGU6IGNvZGUKICAgICAgICAgIH0KICAgICAgICB9OwogICAgICAgIHZlcmlmeUlkZW50aWR5KG1zZykudGhlbihmdW5jdGlvbiAocmVzcG9uc2UpIHsKICAgICAgICAgIC8vIOi6q+S7vemqjOivgemAmui/hyDkv67mlLnlr4bnoIEKICAgICAgICAgIF90aGlzNi5mb3JnZXREaWFsb2cuc3ViVGl0bGUgPSAn5L+u5pS55a+G56CBJzsKICAgICAgICB9KTsKICAgICAgfQogICAgfSwKICAgIC8qKgogICAgICog6K+35rGCIC0g6I635Y+W6aqM6K+B56CBCiAgICAgKi8KICAgIHNlbmRWZXJpZnlDb2RlUmVxOiBmdW5jdGlvbiBzZW5kVmVyaWZ5Q29kZVJlcSgpIHsKICAgICAgdmFyIF90aGlzNyA9IHRoaXM7CiAgICAgIHZhciBfdGhpcyRmb3JnZXREaWFsb2ckZm8yID0gdGhpcy5mb3JnZXREaWFsb2cuZm9ybSwKICAgICAgICBjb25maWcgPSBfdGhpcyRmb3JnZXREaWFsb2ckZm8yLmNvbmZpZywKICAgICAgICBkZWZhdWx0Rm9ybSA9IF90aGlzJGZvcmdldERpYWxvZyRmbzIuZGVmYXVsdEZvcm07CiAgICAgIC8vIOWAkuiuoeaXtuW8gOWQrwogICAgICB2YXIgc2Vjb25kID0gNjA7CiAgICAgIHZhciB0aW1lciA9IG51bGw7CiAgICAgIHRpbWVyID0gc2V0SW50ZXJ2YWwoZnVuY3Rpb24gKCkgewogICAgICAgIHNlY29uZC0tOwogICAgICAgIGlmIChzZWNvbmQgPiAwKSB7CiAgICAgICAgICBjb25maWcudXNlcl9uby5zbG90T2JqLnRleHQgPSAiIi5jb25jYXQoc2Vjb25kLCAiXHU3OUQyXHU1NDBFXHU5MUNEXHU1M0QxIik7CiAgICAgICAgfSBlbHNlIHsKICAgICAgICAgIGNsZWFySW50ZXJ2YWwodGltZXIpOwogICAgICAgICAgY29uZmlnLnVzZXJfbm8uc2xvdE9iai50ZXh0ID0gJ+iOt+WPlumqjOivgeeggSc7CiAgICAgICAgfQogICAgICB9LCAxMDAwKTsKICAgICAgdmFyIG1zZyA9IHsKICAgICAgICBwYXJhbWV0ZXJMaXN0OiBbXSwKICAgICAgICBzeXNNYXA6IHsKICAgICAgICAgIG9wZXJfdHlwZTogJ3NlbmRWZXJpZnlDb2RlJywKICAgICAgICAgIHVzZXJfbm86IGRlZmF1bHRGb3JtLnVzZXJfbm8sCiAgICAgICAgICBwaG9uZTogZGVmYXVsdEZvcm0ucGhvbmUKICAgICAgICB9CiAgICAgIH07CiAgICAgIHNlbmRWZXJpZnlDb2RlKG1zZykudGhlbihmdW5jdGlvbiAocmVzcG9uc2UpIHsKICAgICAgICBpZiAoIWNvbW1vbkJsYW5rKHJlc3BvbnNlLnJldE1hcCkpIHsKICAgICAgICAgIF90aGlzNy5jaGVja0NvZGUgPSByZXNwb25zZS5yZXRNYXAuY2hlY2tDb2RlOwogICAgICAgICAgY29tbW9uTXNnQ29uZmlybSgiXHU2QjY0XHU2QjIxXHU2MjdFXHU1NkRFXHU1QkM2XHU3ODAxXHU5QThDXHU4QkMxXHU3ODAxOiIuY29uY2F0KHJlc3BvbnNlLnJldE1hcC52ZXJpZnlDb2RlKSwgX3RoaXM3LCBmdW5jdGlvbiAocGFyYW0pIHt9KTsKICAgICAgICB9IGVsc2UgewogICAgICAgICAgY29tbW9uTXNnU3VjY2Vzcygn55+t5L+h6aqM6K+B56CB5bey5Y+R6YCB77yM6K+35rOo5oSP5p+l5pS2IScsIF90aGlzNyk7CiAgICAgICAgfQogICAgICB9KTsKICAgICAgLy8gfQogICAgfSwKICAgIC8qKgogICAgICog5L+u5pS55a+G56CB5by556qXIOS/ruaUueWujOaIkOaMiemSrgogICAgICovCiAgICBoYW5kbGVTdWNjZXNzOiBmdW5jdGlvbiBoYW5kbGVTdWNjZXNzKCkgewogICAgICB0aGlzLmN1cnJCdG5JbmRleCA9ICcyJzsKICAgICAgdGhpcy4kcmVmcy5mb3JtUmVmLnZhbGlkYXRlRm9ybSgpOwogICAgfSwKICAgIC8qKgogICAgICog6K+35rGCIC0g6YeN572u5a+G56CBCiAgICAgKi8KICAgIHJlc2V0UHdkUmVxOiBmdW5jdGlvbiByZXNldFB3ZFJlcSgpIHsKICAgICAgdmFyIF90aGlzOCA9IHRoaXM7CiAgICAgIHZhciBfdGhpcyRmb3JnZXREaWFsb2ckZm8zID0gdGhpcy5mb3JnZXREaWFsb2cuZm9ybS5kZWZhdWx0Rm9ybSwKICAgICAgICB1c2VyX25vID0gX3RoaXMkZm9yZ2V0RGlhbG9nJGZvMy51c2VyX25vLAogICAgICAgIG5ld1Bhc3N3b3JkID0gX3RoaXMkZm9yZ2V0RGlhbG9nJGZvMy5uZXdQYXNzd29yZCwKICAgICAgICBjb25maXJtUGFzc3dvcmQgPSBfdGhpcyRmb3JnZXREaWFsb2ckZm8zLmNvbmZpcm1QYXNzd29yZDsKICAgICAgaWYgKG5ld1Bhc3N3b3JkLnRyaW0oKSAhPT0gY29uZmlybVBhc3N3b3JkLnRyaW0oKSkgewogICAgICAgIGNvbW1vbk1zZ1dhcm4oJ+ehruiupOWvhueggeS4jeato+ehru+8jOivt+S7lOe7huaguOWvuei+k+WFpSEnLCB0aGlzKTsKICAgICAgfSBlbHNlIHsKICAgICAgICB2YXIgbXNnID0gewogICAgICAgICAgcGFyYW1ldGVyTGlzdDogW10sCiAgICAgICAgICBzeXNNYXA6IHsKICAgICAgICAgICAgb3Blcl90eXBlOiAncmVzZXRQd2QnLAogICAgICAgICAgICB1c2VyX25vOiB1c2VyX25vLAogICAgICAgICAgICBwYXNzd29yZDogZW5jcnlwdFJlc3VsdCh0aGlzLiRzdG9yZS5nZXR0ZXJzLmluaXRQYXJhbXMuZW5TZWNNYXAuZW5jcnlwdFR5cGUsIG5ld1Bhc3N3b3JkKSwKICAgICAgICAgICAgY2hlY2tDb2RlOiB0aGlzLmNoZWNrQ29kZQogICAgICAgICAgfQogICAgICAgIH07CiAgICAgICAgcmVzZXRQd2QobXNnKS50aGVuKGZ1bmN0aW9uIChyZXNwb25zZSkgewogICAgICAgICAgY29tbW9uTXNnU3VjY2VzcyhyZXNwb25zZS5yZXRNc2csIF90aGlzOCk7CiAgICAgICAgICBfdGhpczguaGFuZGxlQ2xvc2UoKTsKICAgICAgICB9KTsKICAgICAgfQogICAgfSwKICAgIC8qKgogICAgICog5by556qXIOihqOWNleagoemqjAogICAgICovCiAgICB2YWxpZGF0ZUZvcm06IGZ1bmN0aW9uIHZhbGlkYXRlRm9ybSh2YWxpZCkgewogICAgICBpZiAodmFsaWQpIHsKICAgICAgICBpZiAodGhpcy5jdXJyQnRuSW5kZXggPT09ICcwJykgewogICAgICAgICAgdGhpcy5zZW5kVmVyaWZ5Q29kZVJlcSgpOyAvLyDojrflj5bpqozor4HnoIEKICAgICAgICB9IGVsc2UgaWYgKHRoaXMuY3VyckJ0bkluZGV4ID09PSAnMScpIHsKICAgICAgICAgIHRoaXMubmV4dFJlcSgpOyAvLyDkuIvkuIDmraUKICAgICAgICB9IGVsc2UgaWYgKHRoaXMuY3VyckJ0bkluZGV4ID09PSAnMicpIHsKICAgICAgICAgIHRoaXMucmVzZXRQd2RSZXEoKTsgLy8g5L+u5pS55a6M5oiQCiAgICAgICAgfQogICAgICB9IGVsc2UgewogICAgICAgIHJldHVybiBmYWxzZTsKICAgICAgfQogICAgfSwKICAgIC8qKgogICAgICog5YWz6Zet5om+5Zue5a+G56CB5by556qXCiAgICAgKi8KICAgIGhhbmRsZUNsb3NlOiBmdW5jdGlvbiBoYW5kbGVDbG9zZSgpIHsKICAgICAgdGhpcy5mb3JnZXREaWFsb2cudmlzaWJsZSA9IGZhbHNlOwogICAgICAvLyDlvLnnqpfooajljZXliJ3lp4vljJYKICAgICAgdGhpcy5mb3JnZXREaWFsb2cuc3ViVGl0bGUgPSAn6YCa6L+H5omL5py65Y+35om+5ZueJzsKICAgICAgdGhpcy5mb3JnZXREaWFsb2cuZm9ybS5jb25maWcgPSBjb25maWcodGhpcyk7CiAgICAgIHRoaXMuZm9yZ2V0RGlhbG9nLmZvcm0uZGVmYXVsdEZvcm0gPSB7CiAgICAgICAgdXNlcl9ubzogJycsCiAgICAgICAgcGhvbmU6ICcnLAogICAgICAgIGNvZGU6ICcnCiAgICAgIH07CiAgICAgIHRoaXMuYnRuSW5kZXggPSAnMSc7CiAgICAgIHRoaXMuY3VyckJ0bkluZGV4ID0gJzAnOwogICAgfQogIH0KfTs="}, null]}