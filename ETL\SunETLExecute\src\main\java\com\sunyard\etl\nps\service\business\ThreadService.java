package com.sunyard.etl.nps.service.business;


import com.sunyard.etl.nps.model.NpBusinessData;
import com.xxl.job.core.log.XxlJobLogger;

public class ThreadService<T> extends Thread {
	private NpBusinessData data;
	private T service;	
	
    public ThreadService(T service, NpBusinessData data) {
        super();
        this.service = service;
        this.data = data;
    }
    
    @Override
    public void run() {
        XxlJobLogger.log("开始ECM影像查询......业务号：" + data.getBusiDataNo());        
        String name  = service.getClass().toString();
    	switch (name.substring(name.lastIndexOf(".")+1)){
    		case "ImageQueryService" : ((ImageQueryService) service).ThreadService(data);
    	}
    }
}
