{"remainingRequest": "E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1\\数字运营平台-统一门户工程\\dopUnify\\node_modules\\babel-loader\\lib\\index.js!E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1\\数字运营平台-统一门户工程\\dopUnify\\node_modules\\eslint-loader\\index.js??ref--13-0!E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1\\数字运营平台-统一门户工程\\dopUnify\\src\\views\\system\\outManage\\definition\\component\\table\\info.js", "dependencies": [{"path": "E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1\\数字运营平台-统一门户工程\\dopUnify\\src\\views\\system\\outManage\\definition\\component\\table\\info.js", "mtime": 1716875178773}, {"path": "E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1\\数字运营平台-统一门户工程\\dopUnify\\babel.config.js", "mtime": 1716875191548}, {"path": "E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1\\数字运营平台-统一门户工程\\dopUnify\\.babelrc", "mtime": 1716875191502}, {"path": "E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1\\数字运营平台-统一门户工程\\dopUnify\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1\\数字运营平台-统一门户工程\\dopUnify\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1\\数字运营平台-统一门户工程\\dopUnify\\node_modules\\eslint-loader\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["v1", "uuidv1", "dictionaryFieds", "configTable", "that", "name", "label", "width", "id", "config", "td_no", "component", "colSpan", "rules", "required", "message", "pattern", "componentProps", "clearable", "disabled", "td_type", "min", "max", "options", "td_name", "td_service", "<PERSON><PERSON><PERSON>", "is_open", "td_desc", "type", "rows"], "sources": ["E:/1_Project/XYD_Project/dop-4.0/dop-4.1/数字运营平台-统一门户工程/dopUnify/src/views/system/outManage/definition/component/table/info.js"], "sourcesContent": ["import { v1 as uuidv1 } from 'uuid'\r\nimport { dictionaryFieds } from '@/utils/dictionary.js' // 字典常量\r\n// 表头\r\nexport const configTable = (that) => [\r\n  {\r\n    name: 'td_no',\r\n    label: '接口标识',\r\n    width: 100,\r\n    id: uuidv1()\r\n  },\r\n  {\r\n    name: 'td_name',\r\n    label: '接口名称',\r\n    width: 160,\r\n    id: uuidv1()\r\n  },\r\n  {\r\n    name: 'td_service',\r\n    label: '接口服务',\r\n    width: 240,\r\n    id: uuidv1()\r\n  },\r\n  {\r\n    name: 'td_desc',\r\n    label: '接口描述',\r\n    id: uuidv1()\r\n  },\r\n  {\r\n    name: 'td_type',\r\n    label: '接口分类',\r\n    id: uuidv1()\r\n  },\r\n  {\r\n    name: 'is_open',\r\n    label: '启用标志',\r\n    id: uuidv1()\r\n  }\r\n]\r\n\r\n// 新增、修改弹出框表单\r\nexport const config = (that) => ({\r\n  td_no: {\r\n    component: 'input',\r\n    label: '接口标识',\r\n    colSpan: 24,\r\n    name: 'td_no',\r\n    config: {\r\n      // form-item 配置\r\n      rules: [\r\n        { required: true, message: '接口标识为必输' },\r\n        // {[0-9a-zA-Z]{6,20}}\r\n        {\r\n          pattern: /^[0-9a-zA-Z]{1,20}$/,\r\n          message: '请填写 0 到 20 个英文或数字'\r\n        }\r\n      ]\r\n    },\r\n    componentProps: {\r\n      // input组件配置\r\n      clearable: true,\r\n      disabled: false\r\n    }\r\n  },\r\n  td_type: {\r\n    component: 'select',\r\n    label: '接口分类',\r\n    colSpan: 24,\r\n    name: 'td_type',\r\n    config: {\r\n      // form-item 配置\r\n      rules: [\r\n        { required: true, message: '接口分类为必输' },\r\n        { min: 0, max: 2, message: '请最多填写2个字符' }\r\n      ]\r\n    },\r\n    componentProps: {\r\n      // input组件配置\r\n      clearable: true\r\n    },\r\n    options: dictionaryFieds('TD_TYPE')\r\n  },\r\n  td_name: {\r\n    component: 'input',\r\n    label: '接口名称',\r\n    colSpan: 24,\r\n    name: 'td_name',\r\n    config: {\r\n      // form-item 配置\r\n      rules: [{ required: true, message: '接口名称为必输' }]\r\n    },\r\n    componentProps: {\r\n      // input组件配置\r\n      clearable: true\r\n    }\r\n  },\r\n\r\n  td_service: {\r\n    component: 'input',\r\n    label: '接口服务',\r\n    colSpan: 24,\r\n    name: 'td_service',\r\n    config: {\r\n      // form-item 配置\r\n      rules: [\r\n        { required: true, message: '接口服务不能为空' },\r\n        { pattern: /^\\S+$/, message: '此处不能有空格' },\r\n        { min: 0, max: 50, message: '请最多填写50个字符' }\r\n      ]\r\n    },\r\n    componentProps: {\r\n      // input组件配置\r\n      placehodler: '',\r\n      clearable: true\r\n    }\r\n  },\r\n  is_open: {\r\n    component: 'select',\r\n    label: '启用标志',\r\n    colSpan: 24,\r\n    name: 'is_open',\r\n    config: {\r\n      // form-item 配置\r\n      rules: [\r\n        { required: true, message: '启用标志不能为空' },\r\n        { min: 0, max: 1, message: '请最多填写1个字符' }\r\n      ]\r\n    },\r\n    componentProps: {\r\n      // input组件配置\r\n    },\r\n    options: dictionaryFieds('IS_OPEN')\r\n  },\r\n  td_desc: {\r\n    component: 'input',\r\n    label: '接口描述',\r\n    colSpan: 24,\r\n    name: 'td_desc',\r\n    config: {\r\n      // form-item 配置\r\n      rules: [{ min: 0, max: 170, message: '请最多填写170个字符' }]\r\n    },\r\n    componentProps: {\r\n      // input组件配置\r\n      clearable: true,\r\n      type: 'textarea',\r\n      rows: 2\r\n    }\r\n  }\r\n})\r\n"], "mappings": "AAAA,SAASA,EAAE,IAAIC,MAAM,QAAQ,MAAM;AACnC,SAASC,eAAe,QAAQ,uBAAuB,EAAC;AACxD;AACA,OAAO,IAAMC,WAAW,GAAG,SAAdA,WAAW,CAAIC,IAAI;EAAA,OAAK,CACnC;IACEC,IAAI,EAAE,OAAO;IACbC,KAAK,EAAE,MAAM;IACbC,KAAK,EAAE,GAAG;IACVC,EAAE,EAAEP,MAAM;EACZ,CAAC,EACD;IACEI,IAAI,EAAE,SAAS;IACfC,KAAK,EAAE,MAAM;IACbC,KAAK,EAAE,GAAG;IACVC,EAAE,EAAEP,MAAM;EACZ,CAAC,EACD;IACEI,IAAI,EAAE,YAAY;IAClBC,KAAK,EAAE,MAAM;IACbC,KAAK,EAAE,GAAG;IACVC,EAAE,EAAEP,MAAM;EACZ,CAAC,EACD;IACEI,IAAI,EAAE,SAAS;IACfC,KAAK,EAAE,MAAM;IACbE,EAAE,EAAEP,MAAM;EACZ,CAAC,EACD;IACEI,IAAI,EAAE,SAAS;IACfC,KAAK,EAAE,MAAM;IACbE,EAAE,EAAEP,MAAM;EACZ,CAAC,EACD;IACEI,IAAI,EAAE,SAAS;IACfC,KAAK,EAAE,MAAM;IACbE,EAAE,EAAEP,MAAM;EACZ,CAAC,CACF;AAAA;;AAED;AACA,OAAO,IAAMQ,MAAM,GAAG,SAATA,MAAM,CAAIL,IAAI;EAAA,OAAM;IAC/BM,KAAK,EAAE;MACLC,SAAS,EAAE,OAAO;MAClBL,KAAK,EAAE,MAAM;MACbM,OAAO,EAAE,EAAE;MACXP,IAAI,EAAE,OAAO;MACbI,MAAM,EAAE;QACN;QACAI,KAAK,EAAE,CACL;UAAEC,QAAQ,EAAE,IAAI;UAAEC,OAAO,EAAE;QAAU,CAAC;QACtC;QACA;UACEC,OAAO,EAAE,qBAAqB;UAC9BD,OAAO,EAAE;QACX,CAAC;MAEL,CAAC;MACDE,cAAc,EAAE;QACd;QACAC,SAAS,EAAE,IAAI;QACfC,QAAQ,EAAE;MACZ;IACF,CAAC;IACDC,OAAO,EAAE;MACPT,SAAS,EAAE,QAAQ;MACnBL,KAAK,EAAE,MAAM;MACbM,OAAO,EAAE,EAAE;MACXP,IAAI,EAAE,SAAS;MACfI,MAAM,EAAE;QACN;QACAI,KAAK,EAAE,CACL;UAAEC,QAAQ,EAAE,IAAI;UAAEC,OAAO,EAAE;QAAU,CAAC,EACtC;UAAEM,GAAG,EAAE,CAAC;UAAEC,GAAG,EAAE,CAAC;UAAEP,OAAO,EAAE;QAAY,CAAC;MAE5C,CAAC;MACDE,cAAc,EAAE;QACd;QACAC,SAAS,EAAE;MACb,CAAC;MACDK,OAAO,EAAErB,eAAe,CAAC,SAAS;IACpC,CAAC;IACDsB,OAAO,EAAE;MACPb,SAAS,EAAE,OAAO;MAClBL,KAAK,EAAE,MAAM;MACbM,OAAO,EAAE,EAAE;MACXP,IAAI,EAAE,SAAS;MACfI,MAAM,EAAE;QACN;QACAI,KAAK,EAAE,CAAC;UAAEC,QAAQ,EAAE,IAAI;UAAEC,OAAO,EAAE;QAAU,CAAC;MAChD,CAAC;MACDE,cAAc,EAAE;QACd;QACAC,SAAS,EAAE;MACb;IACF,CAAC;IAEDO,UAAU,EAAE;MACVd,SAAS,EAAE,OAAO;MAClBL,KAAK,EAAE,MAAM;MACbM,OAAO,EAAE,EAAE;MACXP,IAAI,EAAE,YAAY;MAClBI,MAAM,EAAE;QACN;QACAI,KAAK,EAAE,CACL;UAAEC,QAAQ,EAAE,IAAI;UAAEC,OAAO,EAAE;QAAW,CAAC,EACvC;UAAEC,OAAO,EAAE,OAAO;UAAED,OAAO,EAAE;QAAU,CAAC,EACxC;UAAEM,GAAG,EAAE,CAAC;UAAEC,GAAG,EAAE,EAAE;UAAEP,OAAO,EAAE;QAAa,CAAC;MAE9C,CAAC;MACDE,cAAc,EAAE;QACd;QACAS,WAAW,EAAE,EAAE;QACfR,SAAS,EAAE;MACb;IACF,CAAC;IACDS,OAAO,EAAE;MACPhB,SAAS,EAAE,QAAQ;MACnBL,KAAK,EAAE,MAAM;MACbM,OAAO,EAAE,EAAE;MACXP,IAAI,EAAE,SAAS;MACfI,MAAM,EAAE;QACN;QACAI,KAAK,EAAE,CACL;UAAEC,QAAQ,EAAE,IAAI;UAAEC,OAAO,EAAE;QAAW,CAAC,EACvC;UAAEM,GAAG,EAAE,CAAC;UAAEC,GAAG,EAAE,CAAC;UAAEP,OAAO,EAAE;QAAY,CAAC;MAE5C,CAAC;MACDE,cAAc,EAAE;QACd;MAAA,CACD;MACDM,OAAO,EAAErB,eAAe,CAAC,SAAS;IACpC,CAAC;IACD0B,OAAO,EAAE;MACPjB,SAAS,EAAE,OAAO;MAClBL,KAAK,EAAE,MAAM;MACbM,OAAO,EAAE,EAAE;MACXP,IAAI,EAAE,SAAS;MACfI,MAAM,EAAE;QACN;QACAI,KAAK,EAAE,CAAC;UAAEQ,GAAG,EAAE,CAAC;UAAEC,GAAG,EAAE,GAAG;UAAEP,OAAO,EAAE;QAAc,CAAC;MACtD,CAAC;MACDE,cAAc,EAAE;QACd;QACAC,SAAS,EAAE,IAAI;QACfW,IAAI,EAAE,UAAU;QAChBC,IAAI,EAAE;MACR;IACF;EACF,CAAC;AAAA,CAAC"}]}