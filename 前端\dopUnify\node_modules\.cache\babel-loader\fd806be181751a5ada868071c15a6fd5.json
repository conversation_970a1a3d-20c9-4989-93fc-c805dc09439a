{"remainingRequest": "E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-q<PERSON><PERSON>n\\数字运营平台-统一门户工程\\dopUnify\\node_modules\\babel-loader\\lib\\index.js!E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan\\数字运营平台-统一门户工程\\dopUnify\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan\\数字运营平台-统一门户工程\\dopUnify\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan\\数字运营平台-统一门户工程\\dopUnify\\src\\views\\home\\page\\components\\defaultItem.vue?vue&type=script&lang=js&", "dependencies": [{"path": "E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-<PERSON><PERSON><PERSON>n\\数字运营平台-统一门户工程\\dopUnify\\src\\views\\home\\page\\components\\defaultItem.vue", "mtime": 1686019809576}, {"path": "E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-<PERSON><PERSON><PERSON>n\\数字运营平台-统一门户工程\\dopUnify\\babel.config.js", "mtime": 1686019818359}, {"path": "E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-<PERSON><PERSON><PERSON>n\\数字运营平台-统一门户工程\\dopUnify\\.babelrc", "mtime": 1705285078668}, {"path": "E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-<PERSON><PERSON><PERSON>n\\数字运营平台-统一门户工程\\dopUnify\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-<PERSON><PERSON><PERSON>n\\数字运营平台-统一门户工程\\dopUnify\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-<PERSON><PERSON><PERSON>n\\数字运营平台-统一门户工程\\dopUnify\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-<PERSON><PERSON><PERSON>n\\数字运营平台-统一门户工程\\dopUnify\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1655715099000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:aW1wb3J0ICJjb3JlLWpzL21vZHVsZXMvZXMuZnVuY3Rpb24ubmFtZS5qcyI7Ci8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCgpleHBvcnQgZGVmYXVsdCB7CiAgbmFtZTogJ0RlZmF1bHRJdGVtJywKICBwcm9wczogewogICAgZGF0YU9iajogewogICAgICB0eXBlOiBPYmplY3QsCiAgICAgIGRlZmF1bHQ6IGZ1bmN0aW9uIF9kZWZhdWx0KCkgewogICAgICAgIHJldHVybiB7fTsKICAgICAgfQogICAgfQogIH0sCiAgZGF0YTogZnVuY3Rpb24gZGF0YSgpIHsKICAgIHJldHVybiB7fTsKICB9LAogIGNvbXB1dGVkOiB7CiAgICBzdmdOYW1lOiBmdW5jdGlvbiBzdmdOYW1lKCkgewogICAgICBpZiAodGhpcy5kYXRhT2JqLm5hbWUgPT09ICfkuYXmgqzmlbDph48nKSB7CiAgICAgICAgcmV0dXJuICcjaWNvbi1oYW5naW5nJzsKICAgICAgfQogICAgICBpZiAodGhpcy5kYXRhT2JqLm5hbWUgPT09ICfotKbmiLfmiqXlpIcnKSB7CiAgICAgICAgcmV0dXJuICcjaWNvbi1hY2NvdW50Uic7CiAgICAgIH0KICAgICAgaWYgKHRoaXMuZGF0YU9iai5uYW1lID09PSAn6LSm5oi35bm05qOAJykgewogICAgICAgIHJldHVybiAnI2ljb24tYWNjb3VudFknOwogICAgICB9CiAgICAgIHJldHVybiAnJzsKICAgIH0KICB9LAogIGNyZWF0ZWQ6IGZ1bmN0aW9uIGNyZWF0ZWQoKSB7fQp9Ow=="}, {"version": 3, "mappings": ";;;;;;;;;;;;;;;;AAeA;EACAA;EACAC;IACAC;MACAC;MACAC;QACA;MACA;IACA;EACA;EACAC;IACA;EACA;EACAC;IACAC;MACA;QACA;MACA;MACA;QACA;MACA;MACA;QACA;MACA;MACA;IACA;EACA;EACAC;AACA", "names": ["name", "props", "dataObj", "type", "default", "data", "computed", "svgName", "created"], "sourceRoot": "src/views/home/<USER>/components", "sources": ["defaultItem.vue"], "sourcesContent": ["<template>\n  <div class=\"default-item\">\n    <div class=\"top-list-cont\">\n      <svg>\n        <use :xlink:href=\"svgName\" />\n      </svg>\n    </div>\n    <div class=\"top-list-cont top-list-title\">\n      <span class=\"title-cont\">{{ dataObj.name }}</span>\n      <span class=\"title-num\">0</span>\n    </div>\n  </div>\n</template>\n\n<script>\nexport default {\n  name: 'DefaultItem',\n  props: {\n    dataObj: {\n      type: Object,\n      default: () => {\n        return {}\n      }\n    }\n  },\n  data() {\n    return {}\n  },\n  computed: {\n    svgName: function() {\n      if (this.dataObj.name === '久悬数量') {\n        return '#icon-hanging'\n      }\n      if (this.dataObj.name === '账户报备') {\n        return '#icon-accountR'\n      }\n      if (this.dataObj.name === '账户年检') {\n        return '#icon-accountY'\n      }\n      return ''\n    }\n  },\n  created() {}\n}\n</script>\n<style scoped lang=\"scss\">\n.default-item {\n  width: 100%;\n  height: 100%;\n  border-radius: 1rem;\n  background-color: #fff;\n  .top-list-cont {\n    width: 50%;\n    height: 100%;\n    position: relative;\n    float: left;\n     svg {\n      width: 70%;\n      height: 100%;\n      margin-left: 15%;\n    }\n    .title-cont {\n      top: 25%;\n    }\n    .title-num {\n      margin-top: 2rem;\n      font-weight: 600;\n      font-size: 2.6rem;\n      color: #2f9bff;\n    }\n  }\n  .top-list-title {\n    display: flex;\n    flex-direction: column;\n    justify-content: center;\n  }\n}\n</style>\n"]}]}