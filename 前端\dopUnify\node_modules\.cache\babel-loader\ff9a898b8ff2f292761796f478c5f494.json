{"remainingRequest": "E:\\华南区项目\\中国银行\\开发区\\科技公司运营平台\\前端\\dopUnify\\node_modules\\thread-loader\\dist\\cjs.js!E:\\华南区项目\\中国银行\\开发区\\科技公司运营平台\\前端\\dopUnify\\node_modules\\babel-loader\\lib\\index.js!E:\\华南区项目\\中国银行\\开发区\\科技公司运营平台\\前端\\dopUnify\\src\\api\\common\\index.js", "dependencies": [{"path": "E:\\华南区项目\\中国银行\\开发区\\科技公司运营平台\\前端\\dopUnify\\src\\api\\common\\index.js", "mtime": 1723107318891}, {"path": "E:\\华南区项目\\中国银行\\开发区\\科技公司运营平台\\前端\\dopUnify\\babel.config.js", "mtime": 1721631954662}, {"path": "E:\\华南区项目\\中国银行\\开发区\\科技公司运营平台\\前端\\dopUnify\\.babelrc", "mtime": 1721631954662}, {"path": "E:\\华南区项目\\中国银行\\开发区\\科技公司运营平台\\前端\\dopUnify\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "E:\\华南区项目\\中国银行\\开发区\\科技公司运营平台\\前端\\dopUnify\\node_modules\\thread-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "E:\\华南区项目\\中国银行\\开发区\\科技公司运营平台\\前端\\dopUnify\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, null]}