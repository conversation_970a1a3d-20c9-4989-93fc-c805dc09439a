{"remainingRequest": "D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\node_modules\\babel-loader\\lib\\index.js!D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\node_modules\\eslint-loader\\index.js??ref--13-0!D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\src\\views\\system\\config\\message\\systemMsg\\info.js", "dependencies": [{"path": "D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\src\\views\\system\\config\\message\\systemMsg\\info.js", "mtime": 1690352538872}, {"path": "D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\babel.config.js", "mtime": 1686019818359}, {"path": "D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\.babelrc", "mtime": 1705285078668}, {"path": "D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\node_modules\\eslint-loader\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["dictionaryFieds", "v1", "uuidv1", "dateConfig", "name", "label", "config", "that", "msg_content", "component", "colSpan", "componentProps", "placeholder", "clearable", "deal_state", "hidden", "options", "configTable", "width", "id", "formConfig", "remark", "rules", "required", "message", "type", "default", "SunOperDetailDialog"], "sources": ["D:/1_Project/XYD_Project/dop-4.0/dop-4.1-qian<PERSON>n-unify/dopUnify/src/views/system/config/message/systemMsg/info.js"], "sourcesContent": ["import { dictionaryFieds } from '@/utils/dictionary' // 字典配置\r\nimport { v1 as uuidv1 } from 'uuid'\r\nexport const dateConfig = () => [\r\n  {\r\n    name: 'last_modi_date',\r\n    label: '记录时间'\r\n  },\r\n  {\r\n    name: 'warn_time',\r\n    label: '提醒时间'\r\n  },\r\n  {\r\n    name: 'warn_note',\r\n    label: '短信提醒标识'\r\n  },\r\n  {\r\n    name: 'warn_phone',\r\n    label: '提醒手机号'\r\n  },\r\n  {\r\n    name: 'note_info',\r\n    label: '便签内容'\r\n  }\r\n]\r\nexport const config = (that) => ({\r\n  msg_content: {\r\n    component: 'input',\r\n    label: '消息内容',\r\n    colSpan: 8,\r\n    name: 'msg_content',\r\n    config: {\r\n      // form-item 配置\r\n    },\r\n    componentProps: {\r\n      // input组件配置\r\n      placeholder: '支持消息内容模糊查询',\r\n      clearable: true\r\n    }\r\n  },\r\n  deal_state: {\r\n    component: 'select',\r\n    label: '处理状态',\r\n    hidden: false,\r\n    colSpan: 8,\r\n    name: 'deal_state',\r\n    config: {},\r\n    componentProps: {\r\n      clearable: true\r\n    },\r\n    options: dictionaryFieds('DEAL_STATUS')\r\n  }\r\n})\r\n\r\nexport const configTable = (that) => [\r\n  {\r\n    name: 'msg_type',\r\n    label: '消息类型',\r\n    width: 85,\r\n    id: uuidv1()\r\n  },\r\n  {\r\n    name: 'msg_content',\r\n    label: '消息内容',\r\n    id: uuidv1()\r\n  },\r\n  {\r\n    name: 'create_user',\r\n    label: '发起人',\r\n    width: 65,\r\n    id: uuidv1()\r\n  },\r\n  {\r\n    name: 'create_time',\r\n    label: '发起时间',\r\n    width: 155,\r\n    id: uuidv1()\r\n  },\r\n  {\r\n    name: 'end_time',\r\n    label: '截止时间',\r\n    width: 150,\r\n    id: uuidv1()\r\n  },\r\n  {\r\n    name: 'deal_state',\r\n    label: '处理状态',\r\n    width: 80,\r\n    id: uuidv1()\r\n  },\r\n  {\r\n    name: 'dealed_time',\r\n    label: '处理时间',\r\n    width: 155,\r\n    id: uuidv1()\r\n  }\r\n]\r\n\r\n// 拒绝说明 弹出框表单\r\nexport const formConfig = (that) => ({\r\n  remark: {\r\n    component: 'input',\r\n    label: '拒绝说明',\r\n    colSpan: 24,\r\n    name: 'remark',\r\n    config: {\r\n      // form-item 配置\r\n      rules: [{ required: true, message: '此处不能为空' }]\r\n    },\r\n    componentProps: {\r\n      // input组件配置\r\n      clearable: true,\r\n      type: 'textarea'\r\n    }\r\n  }\r\n})\r\n\r\n// 导出公共组件方法\r\nexport { default as SunOperDetailDialog } from './SunOperDetailDialog'\r\n"], "mappings": "AAAA,SAASA,eAAe,QAAQ,oBAAoB,EAAC;AACrD,SAASC,EAAE,IAAIC,MAAM,QAAQ,MAAM;AACnC,OAAO,IAAMC,UAAU,GAAG,SAAbA,UAAU;EAAA,OAAS,CAC9B;IACEC,IAAI,EAAE,gBAAgB;IACtBC,KAAK,EAAE;EACT,CAAC,EACD;IACED,IAAI,EAAE,WAAW;IACjBC,KAAK,EAAE;EACT,CAAC,EACD;IACED,IAAI,EAAE,WAAW;IACjBC,KAAK,EAAE;EACT,CAAC,EACD;IACED,IAAI,EAAE,YAAY;IAClBC,KAAK,EAAE;EACT,CAAC,EACD;IACED,IAAI,EAAE,WAAW;IACjBC,KAAK,EAAE;EACT,CAAC,CACF;AAAA;AACD,OAAO,IAAMC,MAAM,GAAG,SAATA,MAAM,CAAIC,IAAI;EAAA,OAAM;IAC/BC,WAAW,EAAE;MACXC,SAAS,EAAE,OAAO;MAClBJ,KAAK,EAAE,MAAM;MACbK,OAAO,EAAE,CAAC;MACVN,IAAI,EAAE,aAAa;MACnBE,MAAM,EAAE;QACN;MAAA,CACD;MACDK,cAAc,EAAE;QACd;QACAC,WAAW,EAAE,YAAY;QACzBC,SAAS,EAAE;MACb;IACF,CAAC;IACDC,UAAU,EAAE;MACVL,SAAS,EAAE,QAAQ;MACnBJ,KAAK,EAAE,MAAM;MACbU,MAAM,EAAE,KAAK;MACbL,OAAO,EAAE,CAAC;MACVN,IAAI,EAAE,YAAY;MAClBE,MAAM,EAAE,CAAC,CAAC;MACVK,cAAc,EAAE;QACdE,SAAS,EAAE;MACb,CAAC;MACDG,OAAO,EAAEhB,eAAe,CAAC,aAAa;IACxC;EACF,CAAC;AAAA,CAAC;AAEF,OAAO,IAAMiB,WAAW,GAAG,SAAdA,WAAW,CAAIV,IAAI;EAAA,OAAK,CACnC;IACEH,IAAI,EAAE,UAAU;IAChBC,KAAK,EAAE,MAAM;IACba,KAAK,EAAE,EAAE;IACTC,EAAE,EAAEjB,MAAM;EACZ,CAAC,EACD;IACEE,IAAI,EAAE,aAAa;IACnBC,KAAK,EAAE,MAAM;IACbc,EAAE,EAAEjB,MAAM;EACZ,CAAC,EACD;IACEE,IAAI,EAAE,aAAa;IACnBC,KAAK,EAAE,KAAK;IACZa,KAAK,EAAE,EAAE;IACTC,EAAE,EAAEjB,MAAM;EACZ,CAAC,EACD;IACEE,IAAI,EAAE,aAAa;IACnBC,KAAK,EAAE,MAAM;IACba,KAAK,EAAE,GAAG;IACVC,EAAE,EAAEjB,MAAM;EACZ,CAAC,EACD;IACEE,IAAI,EAAE,UAAU;IAChBC,KAAK,EAAE,MAAM;IACba,KAAK,EAAE,GAAG;IACVC,EAAE,EAAEjB,MAAM;EACZ,CAAC,EACD;IACEE,IAAI,EAAE,YAAY;IAClBC,KAAK,EAAE,MAAM;IACba,KAAK,EAAE,EAAE;IACTC,EAAE,EAAEjB,MAAM;EACZ,CAAC,EACD;IACEE,IAAI,EAAE,aAAa;IACnBC,KAAK,EAAE,MAAM;IACba,KAAK,EAAE,GAAG;IACVC,EAAE,EAAEjB,MAAM;EACZ,CAAC,CACF;AAAA;;AAED;AACA,OAAO,IAAMkB,UAAU,GAAG,SAAbA,UAAU,CAAIb,IAAI;EAAA,OAAM;IACnCc,MAAM,EAAE;MACNZ,SAAS,EAAE,OAAO;MAClBJ,KAAK,EAAE,MAAM;MACbK,OAAO,EAAE,EAAE;MACXN,IAAI,EAAE,QAAQ;MACdE,MAAM,EAAE;QACN;QACAgB,KAAK,EAAE,CAAC;UAAEC,QAAQ,EAAE,IAAI;UAAEC,OAAO,EAAE;QAAS,CAAC;MAC/C,CAAC;MACDb,cAAc,EAAE;QACd;QACAE,SAAS,EAAE,IAAI;QACfY,IAAI,EAAE;MACR;IACF;EACF,CAAC;AAAA,CAAC;;AAEF;AACA,SAASC,OAAO,IAAIC,mBAAmB,QAAQ,uBAAuB"}]}