{"remainingRequest": "E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-q<PERSON><PERSON>n\\数字运营平台-统一门户工程\\dopUnify\\node_modules\\babel-loader\\lib\\index.js!E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan\\数字运营平台-统一门户工程\\dopUnify\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan\\数字运营平台-统一门户工程\\dopUnify\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan\\数字运营平台-统一门户工程\\dopUnify\\src\\components\\Dialog\\SunFormIconDialog\\index.vue?vue&type=script&lang=js&", "dependencies": [{"path": "E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-<PERSON><PERSON><PERSON>n\\数字运营平台-统一门户工程\\dopUnify\\src\\components\\Dialog\\SunFormIconDialog\\index.vue", "mtime": 1703583638381}, {"path": "E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-<PERSON><PERSON><PERSON>n\\数字运营平台-统一门户工程\\dopUnify\\babel.config.js", "mtime": 1686019818359}, {"path": "E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-<PERSON><PERSON><PERSON>n\\数字运营平台-统一门户工程\\dopUnify\\.babelrc", "mtime": 1705285078668}, {"path": "E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-<PERSON><PERSON><PERSON>n\\数字运营平台-统一门户工程\\dopUnify\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-<PERSON><PERSON><PERSON>n\\数字运营平台-统一门户工程\\dopUnify\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-<PERSON><PERSON><PERSON>n\\数字运营平台-统一门户工程\\dopUnify\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-<PERSON><PERSON><PERSON>n\\数字运营平台-统一门户工程\\dopUnify\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1655715099000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KCmltcG9ydCBkZWZhdWx0U2V0dGluZ3MgZnJvbSAnQC9zZXR0aW5ncyc7CmltcG9ydCBlbERyYWdEaWFsb2cgZnJvbSAnQC9kaXJlY3RpdmUvZWwtZHJhZy1kaWFsb2cnOyAvLyDlvLnlh7rmoYblj6/mi5bliqgKZXhwb3J0IGRlZmF1bHQgewogIG5hbWU6ICdTdW5Gb3JtSWNvbkRpYWxvZycsCiAgZGlyZWN0aXZlczogewogICAgZWxEcmFnRGlhbG9nOiBlbERyYWdEaWFsb2cKICB9LAogIGluaGVyaXRBdHRyczogZmFsc2UsCiAgcHJvcHM6IHsKICAgIGRpYWxvZ0NvbmZpZzogewogICAgICB0eXBlOiBPYmplY3QsCiAgICAgIGRlZmF1bHQ6IGZ1bmN0aW9uIF9kZWZhdWx0KCkgewogICAgICAgIHJldHVybiB7CiAgICAgICAgICBjdXN0b21CdXR0b246IGZhbHNlLAogICAgICAgICAgLy8g5piv5ZCm6ZyA6KaB6buY6K6k55qE5by556qX5oyJ6ZKuCiAgICAgICAgICBsb2FkaW5nOiBmYWxzZSwKICAgICAgICAgIC8vIOm7mOiupGxvYWRpbmfphY3nva4KICAgICAgICAgIHZpc2libGU6IGZhbHNlLAogICAgICAgICAgLy8g5pi+56S66ZqQ6JeP6YWN572uCiAgICAgICAgICBjb21wb25lbnRQcm9wczogewogICAgICAgICAgICAvLyDlvLnlh7rmoYblsZ7mgKcKICAgICAgICAgICAgdGl0bGU6ICfooajljZXlvLnlh7rmoYYnLAogICAgICAgICAgICAvLyDlvLnlh7rmoYbmoIfpopgKICAgICAgICAgICAgd2lkdGg6ICcnLAogICAgICAgICAgICAvLyDlvZPliY3lvLnlh7rmoYblrr3luqYg6buY6K6kODAlCiAgICAgICAgICAgIGRlc3Ryb3lPbkNsb3NlOiB0cnVlIC8vIOWFs+mXreaXtumUgOavgSBEaWFsb2cg5Lit55qE5YWD57SgCiAgICAgICAgICB9LAoKICAgICAgICAgIGZvcm06IHsKICAgICAgICAgICAgLy8g6KGo5Y2V5bGe5oCnCiAgICAgICAgICAgIGNvbmZpZzoge30sCiAgICAgICAgICAgIC8vIOihqOWNlemhuemFjee9rgogICAgICAgICAgICBkZWZhdWx0Rm9ybToge30sCiAgICAgICAgICAgIC8vIOm7mOiupOWAvOmFjee9rgogICAgICAgICAgICBsYWJlbFdpZHRoOiAnMTByZW0nIC8vIOW9k+WJjeihqOWNleagh+etvuWuveW6pumFjee9rgogICAgICAgICAgfQogICAgICAgIH07CiAgICAgIH0KICAgIH0sCgogICAgbm93RnVuY3Rpb246IHsKICAgICAgdHlwZTogU3RyaW5nLAogICAgICBkZWZhdWx0OiAnZGlhbG9nU3VibWl0JwogICAgfQogIH0sCiAgZGF0YTogZnVuY3Rpb24gZGF0YSgpIHsKICAgIHJldHVybiB7CiAgICAgIGNvbG9ySWNvbnNQYXRoOiBkZWZhdWx0U2V0dGluZ3MuY29sb3JJY29uc1BhdGgsCiAgICAgIC8vIOiPnOiJsuWbvuagh+i3r+W+hAogICAgICBidG46IGZhbHNlLAogICAgICB3aW5kb3dIZWlnaHQ6ICcnLAogICAgICAvLyDmtY/op4jlmajnmoTpq5jluqYKICAgICAgZGlhbG9nSGVpZ2h0OiAnJywKICAgICAgLy8g5by556qX55qE6auY5bqmCiAgICAgIGRyYWdGbGFnOiB0cnVlIC8vIOaLluaLveagh+ivhgogICAgfTsKICB9LAoKICB3YXRjaDogewogICAgJ2RpYWxvZ0NvbmZpZy52aXNpYmxlJzogewogICAgICAvLyDlvZPliY3pgInkuK3nu4Tku7bmlLnlj5jvvIzph43mlrDojrflj5blvZPliY3ooajljZXnu4Tku7bkuK3miYDmnInnmoQg5a2X5q615qCH6K+GIG5hbWUKICAgICAgaGFuZGxlcjogZnVuY3Rpb24gaGFuZGxlcih2YWwpIHsKICAgICAgICB2YXIgX3RoaXMgPSB0aGlzOwogICAgICAgIGlmICh2YWwgPT09IGZhbHNlKSB7CiAgICAgICAgICB0aGlzLiRuZXh0VGljayhmdW5jdGlvbiAoKSB7CiAgICAgICAgICAgIF90aGlzLmRyYWdGbGFnID0gZmFsc2U7CiAgICAgICAgICAgIF90aGlzLiRyZWZzWydyZWZGb3JtRGlhbG9nJ10ucmVzZXRGb3JtKCk7CiAgICAgICAgICB9KTsKICAgICAgICB9IGVsc2UgewogICAgICAgICAgdGhpcy4kbmV4dFRpY2soZnVuY3Rpb24gKCkgewogICAgICAgICAgICBfdGhpcy5kcmFnRmxhZyA9IHRydWU7CiAgICAgICAgICAgIF90aGlzLmdldEhlaWdodCgpOwogICAgICAgICAgfSk7CiAgICAgICAgfQogICAgICB9LAogICAgICBkZWVwOiB0cnVlCiAgICB9CiAgfSwKICBtb3VudGVkOiBmdW5jdGlvbiBtb3VudGVkKCkgewogICAgd2luZG93LmFkZEV2ZW50TGlzdGVuZXIoJ3Jlc2l6ZScsIHRoaXMuZ2V0SGVpZ2h0KTsKICB9LAogIGJlZm9yZURlc3Ryb3k6IGZ1bmN0aW9uIGJlZm9yZURlc3Ryb3koKSB7CiAgICB3aW5kb3cucmVtb3ZlRXZlbnRMaXN0ZW5lcigncmVzaXplJywgdGhpcy5nZXRIZWlnaHQpOwogIH0sCiAgbWV0aG9kczogewogICAgLyoqCiAgICAgKiDlvLnlh7rmoYbvvJrlhbPpl60KICAgICAqLwogICAgZGlhbG9nQ2xvc2U6IGZ1bmN0aW9uIGRpYWxvZ0Nsb3NlKCkgewogICAgICB2YXIgX3RoaXMyID0gdGhpczsKICAgICAgdGhpcy4kcmVmc1sncmVmRm9ybURpYWxvZyddLnJlc2V0Rm9ybSgpOwogICAgICB0aGlzLiRuZXh0VGljayhmdW5jdGlvbiAoKSB7CiAgICAgICAgX3RoaXMyLiRlbWl0KCdkaWFsb2dDbG9zZScsIGZhbHNlKTsKICAgICAgfSk7CiAgICB9LAogICAgLyoqCiAgICAgKiDnoa7lrpoqLwogICAgZGlhbG9nU3VibWl0OiBmdW5jdGlvbiBkaWFsb2dTdWJtaXQoKSB7CiAgICAgIHRoaXMuJHJlZnNbJ3JlZkZvcm1EaWFsb2cnXS52YWxpZGF0ZUZvcm0oKTsKICAgIH0sCiAgICAvKioKICAgICAqIOihqOWNleagoemqjAogICAgICogQHBhcmFtIHtCb29sZWFufXZhbGlkIOagoemqjOi/lOWbnuWAvCovCiAgICB2YWxpZGF0ZUZvcm06IGZ1bmN0aW9uIHZhbGlkYXRlRm9ybSh2YWxpZCkgewogICAgICBpZiAodmFsaWQpIHsKICAgICAgICB0aGlzLiRlbWl0KCdkaWFsb2dTdWJtaXQnLCB0aGlzLmRpYWxvZ0NvbmZpZy5mb3JtLmRlZmF1bHRGb3JtKTsKICAgICAgICAvLyB0aGlzLmRpYWxvZ0Nsb3NlKCkKICAgICAgfSBlbHNlIHsKICAgICAgICByZXR1cm4gZmFsc2U7CiAgICAgIH0KICAgIH0sCiAgICAvLyDojrflj5bmtY/op4jlmajnqpflj6Ppq5jluqbkuI7lvLnnqpfpq5jluqYKICAgIGdldEhlaWdodDogZnVuY3Rpb24gZ2V0SGVpZ2h0KCkgewogICAgICB2YXIgX3RoaXMzID0gdGhpczsKICAgICAgdGhpcy4kbmV4dFRpY2soZnVuY3Rpb24gKCkgewogICAgICAgIF90aGlzMy53aW5kb3dIZWlnaHQgPSB3aW5kb3cuaW5uZXJIZWlnaHQ7CiAgICAgICAgX3RoaXMzLmRpYWxvZ0hlaWdodCA9IF90aGlzMy4kcmVmcy5yZWZEaWFsb2cuJHJlZnMuZGlhbG9nLm9mZnNldEhlaWdodDsKICAgICAgICAvLyDliKTmlq3kuozogIXkuYvpl7TlpKflsI/lhbPns7vvvIzlgZrlh7rnm7jlupTmk43kvZwKICAgICAgICAvLyDlvZPmtY/op4jlmajnqpflj6M+5by556qX6auY5bqmCiAgICAgICAgaWYgKF90aGlzMy53aW5kb3dIZWlnaHQgPiBfdGhpczMuZGlhbG9nSGVpZ2h0KSB7CiAgICAgICAgICB2YXIgZGlhbG9nVG9wID0gX3RoaXMzLndpbmRvd0hlaWdodCAtIF90aGlzMy5kaWFsb2dIZWlnaHQ7CiAgICAgICAgICAvLyDorr7nva7lvLnnqpfkuIrlpJbovrnot50KICAgICAgICAgIC8vIHRoaXMuJHJlZnMucmVmRGlhbG9nLiRyZWZzLmRpYWxvZy5zdHlsZS5tYXJnaW5Ub3AgPQogICAgICAgICAgLy8gICBkaWFsb2dUb3AgLyAyICsgJ3B4JwogICAgICAgICAgdmFyIG1hcmdpblRvcCA9IGRpYWxvZ1RvcCAvIDIgKyAncHgnOwogICAgICAgICAgX3RoaXMzLiRyZWZzLnJlZkRpYWxvZy4kcmVmcy5kaWFsb2cuc3R5bGUubWFyZ2luID0gIiIuY29uY2F0KG1hcmdpblRvcCwgIiBhdXRvIDAiKTsKICAgICAgICB9IGVsc2UgewogICAgICAgICAgLy8g5b2T5rWP6KeI5Zmo56qX5Y+jPOW8ueeql+mrmOW6pgogICAgICAgICAgLy8g5by556qX5oC76auY5bqmCiAgICAgICAgICBfdGhpczMuJHJlZnMucmVmRGlhbG9nLiRyZWZzLmRpYWxvZy5zdHlsZS5oZWlnaHQgPSAnODMlJzsKICAgICAgICAgIC8vIOiOt+WPluabtOaUueWQjueahOaAu+mrmOW6pgogICAgICAgICAgdmFyIGRpYWxvZ0hlaWdodCA9IF90aGlzMy4kcmVmcy5yZWZEaWFsb2cuJHJlZnMuZGlhbG9nLm9mZnNldEhlaWdodDsKICAgICAgICAgIC8vIOW8ueeql2JvZHnljLrln5/nmb7liIbmr5Tpq5jluqYKICAgICAgICAgIF90aGlzMy4kcmVmcy5yZWZEaWFsb2cuJHJlZnMuZGlhbG9nLmNoaWxkTm9kZXNbMV0uc3R5bGUuaGVpZ2h0ID0gJzgzJSc7CiAgICAgICAgICBfdGhpczMuJHJlZnMucmVmRGlhbG9nLiRyZWZzLmRpYWxvZy5jaGlsZE5vZGVzWzFdLnN0eWxlLm92ZXJmbG93ID0gJ2F1dG8nOwogICAgICAgICAgLy8g6K6+572u5by556qX5LiK5aSW6L656LedCiAgICAgICAgICAvLyB0aGlzLiRyZWZzLnJlZkRpYWxvZy4kcmVmcy5kaWFsb2cuc3R5bGUubWFyZ2luVG9wID0KICAgICAgICAgIC8vICAgKHRoaXMud2luZG93SGVpZ2h0IC0gZGlhbG9nSGVpZ2h0KSAvIDIgKyAncHgnCiAgICAgICAgICB2YXIgX21hcmdpblRvcCA9IChfdGhpczMud2luZG93SGVpZ2h0IC0gZGlhbG9nSGVpZ2h0KSAvIDIgKyAncHgnOwogICAgICAgICAgX3RoaXMzLiRyZWZzLnJlZkRpYWxvZy4kcmVmcy5kaWFsb2cuc3R5bGUubWFyZ2luID0gIiIuY29uY2F0KF9tYXJnaW5Ub3AsICIgYXV0byAwIik7CiAgICAgICAgfQogICAgICB9KTsKICAgIH0KICB9Cn07"}, {"version": 3, "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAmFA;AACA;AACA;EACAA;EACAC;IAAAC;EAAA;EACAC;EACAC;IACAC;MACAC;MACAC;QACA;UACAC;UAAA;UACAC;UAAA;UACAC;UAAA;UACAC;YACA;YACAC;YAAA;YACAC;YAAA;YACAC;UACA;;UACAC;YACA;YACAC;YAAA;YACAC;YAAA;YACAC;UACA;QACA;MACA;IACA;;IACAC;MACAb;MACAC;IACA;EACA;EACAa;IACA;MACAC;MAAA;MACAC;MACAC;MAAA;MACAC;MAAA;MACAC;IACA;EACA;;EACAC;IACA;MACA;MACAC;QAAA;QACA;UACA;YACA;YACA;UACA;QACA;UACA;YACA;YACA;UACA;QACA;MACA;MACAC;IACA;EACA;EACAC;IACAC;EACA;EACAC;IACAD;EACA;EACAE;IACA;AACA;AACA;IACAC;MAAA;MACA;MACA;QACA;MACA;IACA;IACA;AACA;IACAC;MACA;IACA;IACA;AACA;AACA;IACAC;MACA;QACA;QACA;MACA;QACA;MACA;IACA;IACA;IACAC;MAAA;MACA;QACA;QACA;QACA;QACA;QACA;UACA;UACA;UACA;UACA;UACA;UACA;QACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA,mEACA;UACA;UACA;UACA;UACA;UACA;QACA;MACA;IACA;EACA;AACA", "names": ["name", "directives", "elDragDialog", "inheritAttrs", "props", "dialogConfig", "type", "default", "customButton", "loading", "visible", "componentProps", "title", "width", "destroyOnClose", "form", "config", "defaultForm", "labelWidth", "nowFunction", "data", "colorIconsPath", "btn", "windowHeight", "dialogHeight", "dragFlag", "watch", "handler", "deep", "mounted", "window", "<PERSON><PERSON><PERSON><PERSON>", "methods", "dialogClose", "dialogSubmit", "validateForm", "getHeight"], "sourceRoot": "src/components/Dialog/SunFormIconDialog", "sources": ["index.vue"], "sourcesContent": ["<!--\n* 弹出框：单个表单\n-->\n<template>\n  <el-dialog\n    v-if=\"dragFlag\"\n    ref=\"refDialog\"\n    v-el-drag-dialog\n    :visible.sync=\"dialogConfig.visible\"\n    :before-close=\"dialogClose\"\n    :destroy-on-close=\"true\"\n    :append-to-body=\"true\"\n    :close-on-click-modal=\"false\"\n    v-bind=\"dialogConfig.componentProps\"\n  >\n    <div v-loading=\"dialogConfig.loading\">\n      <sun-form\n        ref=\"refFormDialog\"\n        :query=\"btn\"\n        :reset=\"btn\"\n        v-bind=\"dialogConfig.form\"\n        @validateForm=\"validateForm\"\n      >\n        <template v-if=\"dialogConfig.form.menuIcon\">\n          <div\n            v-for=\"item in dialogConfig.form.config.menu_icon.options\"\n            :key=\"item.value\"\n            :slot=\"'menu_icon_' + item.value\"\n          >\n            <!-- <sun-svg-icon :icon-class=\"'color-'+ item.value\" /> -->\n            <img class=\"menuColorIconsSmall\" :src=\"colorIconsPath + 'color-'+ item.value + '.svg'\" :alt=\"item.value\">\n            {{ item.value }}\n          </div>\n        </template>\n      </sun-form>\n      <slot name=\"other\" />\n      <div slot=\"footer\" class=\"dialog-footer\">\n        <div class=\"footerRightBtn\">\n          <slot name=\"rightBtn\" />\n        </div>\n        <div v-if=\"!dialogConfig.customButton\">\n          <el-button @click=\"dialogClose\">取 消</el-button>\n          <el-button type=\"primary\" @click=\"dialogSubmit\">确 定</el-button>\n        </div>\n      </div>\n    </div>\n  </el-dialog>\n  <el-dialog\n    v-else\n    ref=\"refDialog\"\n    :visible.sync=\"dialogConfig.visible\"\n    :before-close=\"dialogClose\"\n    :destroy-on-close=\"true\"\n    :append-to-body=\"true\"\n    :close-on-click-modal=\"false\"\n    v-bind=\"dialogConfig.componentProps\"\n  >\n    <div v-loading=\"dialogConfig.loading\">\n      <sun-form\n        ref=\"refFormDialog\"\n        :query=\"btn\"\n        :reset=\"btn\"\n        v-bind=\"dialogConfig.form\"\n        @validateForm=\"validateForm\"\n      >\n        <!-- <template slot=\"header\">\n      </template> -->\n      </sun-form>\n      <slot name=\"other\" />\n      <div slot=\"footer\" class=\"dialog-footer\">\n        <div class=\"footerRightBtn\">\n          <slot name=\"rightBtn\" />\n        </div>\n        <div v-if=\"!dialogConfig.customButton\">\n          <el-button @click=\"dialogClose\">取 消</el-button>\n          <el-button type=\"primary\" @click=\"dialogSubmit\">确 定</el-button>\n        </div>\n      </div>\n    </div>\n  </el-dialog>\n</template>\n\n<script>\nimport defaultSettings from '@/settings'\nimport elDragDialog from '@/directive/el-drag-dialog' // 弹出框可拖动\nexport default {\n  name: 'SunFormIconDialog',\n  directives: { elDragDialog },\n  inheritAttrs: false,\n  props: {\n    dialogConfig: {\n      type: Object,\n      default: () => {\n        return {\n          customButton: false, // 是否需要默认的弹窗按钮\n          loading: false, // 默认loading配置\n          visible: false, // 显示隐藏配置\n          componentProps: {\n            // 弹出框属性\n            title: '表单弹出框', // 弹出框标题\n            width: '', // 当前弹出框宽度 默认80%\n            destroyOnClose: true // 关闭时销毁 Dialog 中的元素\n          },\n          form: {\n            // 表单属性\n            config: {}, // 表单项配置\n            defaultForm: {}, // 默认值配置\n            labelWidth: '10rem' // 当前表单标签宽度配置\n          }\n        }\n      }\n    },\n    nowFunction: {\n      type: String,\n      default: 'dialogSubmit'\n    }\n  },\n  data() {\n    return {\n      colorIconsPath: defaultSettings.colorIconsPath, // 菜色图标路径\n      btn: false,\n      windowHeight: '', // 浏览器的高度\n      dialogHeight: '', // 弹窗的高度\n      dragFlag: true // 拖拽标识\n    }\n  },\n  watch: {\n    'dialogConfig.visible': {\n      // 当前选中组件改变，重新获取当前表单组件中所有的 字段标识 name\n      handler(val) {\n        if (val === false) {\n          this.$nextTick(() => {\n            this.dragFlag = false\n            this.$refs['refFormDialog'].resetForm()\n          })\n        } else {\n          this.$nextTick(() => {\n            this.dragFlag = true\n            this.getHeight()\n          })\n        }\n      },\n      deep: true\n    }\n  },\n  mounted() {\n    window.addEventListener('resize', this.getHeight)\n  },\n  beforeDestroy() {\n    window.removeEventListener('resize', this.getHeight)\n  },\n  methods: {\n    /**\n     * 弹出框：关闭\n     */\n    dialogClose() {\n      this.$refs['refFormDialog'].resetForm()\n      this.$nextTick(() => {\n        this.$emit('dialogClose', false)\n      })\n    },\n    /**\n     * 确定*/\n    dialogSubmit() {\n      this.$refs['refFormDialog'].validateForm()\n    },\n    /**\n     * 表单校验\n     * @param {Boolean}valid 校验返回值*/\n    validateForm(valid) {\n      if (valid) {\n        this.$emit('dialogSubmit', this.dialogConfig.form.defaultForm)\n        // this.dialogClose()\n      } else {\n        return false\n      }\n    },\n    // 获取浏览器窗口高度与弹窗高度\n    getHeight() {\n      this.$nextTick(() => {\n        this.windowHeight = window.innerHeight\n        this.dialogHeight = this.$refs.refDialog.$refs.dialog.offsetHeight\n        // 判断二者之间大小关系，做出相应操作\n        // 当浏览器窗口>弹窗高度\n        if (this.windowHeight > this.dialogHeight) {\n          const dialogTop = this.windowHeight - this.dialogHeight\n          // 设置弹窗上外边距\n          // this.$refs.refDialog.$refs.dialog.style.marginTop =\n          //   dialogTop / 2 + 'px'\n          const marginTop = dialogTop / 2 + 'px'\n          this.$refs.refDialog.$refs.dialog.style.margin = `${marginTop} auto 0`\n        } else {\n          // 当浏览器窗口<弹窗高度\n          // 弹窗总高度\n          this.$refs.refDialog.$refs.dialog.style.height = '83%'\n          // 获取更改后的总高度\n          const dialogHeight = this.$refs.refDialog.$refs.dialog.offsetHeight\n          // 弹窗body区域百分比高度\n          this.$refs.refDialog.$refs.dialog.childNodes[1].style.height = '83%'\n          this.$refs.refDialog.$refs.dialog.childNodes[1].style.overflow =\n            'auto'\n          // 设置弹窗上外边距\n          // this.$refs.refDialog.$refs.dialog.style.marginTop =\n          //   (this.windowHeight - dialogHeight) / 2 + 'px'\n          const marginTop = (this.windowHeight - dialogHeight) / 2 + 'px'\n          this.$refs.refDialog.$refs.dialog.style.margin = `${marginTop} auto 0`\n        }\n      })\n    }\n  }\n}\n</script>\n\n<style lang=\"scss\" scoped>\n.menuColorIconsSmall{\n  width: 1em; // 图标大小\n}\n.dialog-footer {\n  display: flex;\n  justify-content: flex-end;\n  .footerRightBtn {\n    margin-right: 10px;\n    .rightBtn {\n      margin: 0rem 20rem 0rem 2rem;\n      position: absolute;\n      right: 2rem;\n    }\n  }\n}\n</style>\n"]}]}