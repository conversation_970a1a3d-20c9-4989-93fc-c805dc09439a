{"remainingRequest": "E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1\\数字运营平台-统一门户工程\\dopUnify\\node_modules\\babel-loader\\lib\\index.js!E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1\\数字运营平台-统一门户工程\\dopUnify\\node_modules\\eslint-loader\\index.js??ref--13-0!E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1\\数字运营平台-统一门户工程\\dopUnify\\src\\utils\\calendar.js", "dependencies": [{"path": "E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1\\数字运营平台-统一门户工程\\dopUnify\\src\\utils\\calendar.js", "mtime": 1716875180186}, {"path": "E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1\\数字运营平台-统一门户工程\\dopUnify\\babel.config.js", "mtime": 1716875191548}, {"path": "E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1\\数字运营平台-统一门户工程\\dopUnify\\.babelrc", "mtime": 1716875191502}, {"path": "E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1\\数字运营平台-统一门户工程\\dopUnify\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1\\数字运营平台-统一门户工程\\dopUnify\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1\\数字运营平台-统一门户工程\\dopUnify\\node_modules\\eslint-loader\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["calendar", "lunarInfo", "solarMonth", "Gan", "<PERSON><PERSON>", "Animals", "festival", "title", "lFestival", "getFestival", "getLunarFestival", "setFestival", "param", "setLunarFestival", "solarTerm", "sTermInfo", "nStr1", "nStr2", "nStr3", "lYearDays", "y", "i", "sum", "leapDays", "leapMonth", "monthDays", "m", "solarDays", "ms", "toGanZhiYear", "lYear", "gan<PERSON><PERSON>", "<PERSON>hi<PERSON><PERSON>", "to<PERSON><PERSON>", "cMonth", "cDay", "s", "arr", "substr", "toGanZhi", "offset", "getTerm", "n", "_table", "_info", "parseInt", "toString", "_calcDay", "to<PERSON>hina<PERSON><PERSON>", "toChinaDay", "d", "Math", "floor", "getAnimal", "solar2lunar", "yPara", "mPara", "dPara", "objDate", "Date", "leap", "temp", "getFullYear", "getMonth", "getDate", "UTC", "isTodayObj", "isToday", "nWeek", "getDay", "cWeek", "year", "isLeap", "month", "day", "sm", "gzY", "firstNode", "secondNode", "gzM", "isTerm", "Term", "dayCyclical", "gzD", "astro", "solarDate", "lunarDate", "festivalDate", "lunarFestivalDate", "date", "lunarFestival", "lMonth", "lDay", "Animal", "IMonthCn", "IDayCn", "cYear", "gzYear", "gzMonth", "gzDay", "ncWeek", "lunar2solar", "isLeapMonth", "_day", "isAdd", "strap", "calObj", "cY", "getUTCFullYear", "cM", "getUTCMonth", "cD", "getUTCDate"], "sources": ["E:/1_Project/XYD_Project/dop-4.0/dop-4.1/数字运营平台-统一门户工程/dopUnify/src/utils/calendar.js"], "sourcesContent": ["/* 日起组件 -农历及节日 */\r\n/**\r\n * @1900-2100区间内的公历、农历互转\r\n * @charset UTF-8\r\n * <AUTHOR>\r\n * @Time    2014-7-21\r\n * @Time    2016-8-13 Fixed 2033hex、Attribution Annals\r\n * @Time    2016-9-25 Fixed lunar LeapMonth Param Bug\r\n * @Time    2017-7-24 Fixed use getTerm Func Param Error.use solar year,NOT lunar year\r\n * @Version 1.0.3\r\n * @公历转农历：calendar.solar2lunar(1987,11,01); //[you can ignore params of prefix 0]\r\n * @农历转公历：calendar.lunar2solar(1987,09,10); //[you can ignore params of prefix 0]\r\n */\r\nconst calendar = {\r\n  /**\r\n   * 农历1900-2100的润大小信息表\r\n   * @Array Of Property\r\n   * @return Hex\r\n   */\r\n  lunarInfo: [\r\n    0x04bd8,\r\n    0x04ae0,\r\n    0x0a570,\r\n    0x054d5,\r\n    0x0d260,\r\n    0x0d950,\r\n    0x16554,\r\n    0x056a0,\r\n    0x09ad0,\r\n    0x055d2, // 1900-1909\r\n    0x04ae0,\r\n    0x0a5b6,\r\n    0x0a4d0,\r\n    0x0d250,\r\n    0x1d255,\r\n    0x0b540,\r\n    0x0d6a0,\r\n    0x0ada2,\r\n    0x095b0,\r\n    0x14977, // 1910-1919\r\n    0x04970,\r\n    0x0a4b0,\r\n    0x0b4b5,\r\n    0x06a50,\r\n    0x06d40,\r\n    0x1ab54,\r\n    0x02b60,\r\n    0x09570,\r\n    0x052f2,\r\n    0x04970, // 1920-1929\r\n    0x06566,\r\n    0x0d4a0,\r\n    0x0ea50,\r\n    0x16a95,\r\n    0x05ad0,\r\n    0x02b60,\r\n    0x186e3,\r\n    0x092e0,\r\n    0x1c8d7,\r\n    0x0c950, // 1930-1939\r\n    0x0d4a0,\r\n    0x1d8a6,\r\n    0x0b550,\r\n    0x056a0,\r\n    0x1a5b4,\r\n    0x025d0,\r\n    0x092d0,\r\n    0x0d2b2,\r\n    0x0a950,\r\n    0x0b557, // 1940-1949\r\n    0x06ca0,\r\n    0x0b550,\r\n    0x15355,\r\n    0x04da0,\r\n    0x0a5b0,\r\n    0x14573,\r\n    0x052b0,\r\n    0x0a9a8,\r\n    0x0e950,\r\n    0x06aa0, // 1950-1959\r\n    0x0aea6,\r\n    0x0ab50,\r\n    0x04b60,\r\n    0x0aae4,\r\n    0x0a570,\r\n    0x05260,\r\n    0x0f263,\r\n    0x0d950,\r\n    0x05b57,\r\n    0x056a0, // 1960-1969\r\n    0x096d0,\r\n    0x04dd5,\r\n    0x04ad0,\r\n    0x0a4d0,\r\n    0x0d4d4,\r\n    0x0d250,\r\n    0x0d558,\r\n    0x0b540,\r\n    0x0b6a0,\r\n    0x195a6, // 1970-1979\r\n    0x095b0,\r\n    0x049b0,\r\n    0x0a974,\r\n    0x0a4b0,\r\n    0x0b27a,\r\n    0x06a50,\r\n    0x06d40,\r\n    0x0af46,\r\n    0x0ab60,\r\n    0x09570, // 1980-1989\r\n    0x04af5,\r\n    0x04970,\r\n    0x064b0,\r\n    0x074a3,\r\n    0x0ea50,\r\n    0x06b58,\r\n    0x05ac0,\r\n    0x0ab60,\r\n    0x096d5,\r\n    0x092e0, // 1990-1999\r\n    0x0c960,\r\n    0x0d954,\r\n    0x0d4a0,\r\n    0x0da50,\r\n    0x07552,\r\n    0x056a0,\r\n    0x0abb7,\r\n    0x025d0,\r\n    0x092d0,\r\n    0x0cab5, // 2000-2009\r\n    0x0a950,\r\n    0x0b4a0,\r\n    0x0baa4,\r\n    0x0ad50,\r\n    0x055d9,\r\n    0x04ba0,\r\n    0x0a5b0,\r\n    0x15176,\r\n    0x052b0,\r\n    0x0a930, // 2010-2019\r\n    0x07954,\r\n    0x06aa0,\r\n    0x0ad50,\r\n    0x05b52,\r\n    0x04b60,\r\n    0x0a6e6,\r\n    0x0a4e0,\r\n    0x0d260,\r\n    0x0ea65,\r\n    0x0d530, // 2020-2029\r\n    0x05aa0,\r\n    0x076a3,\r\n    0x096d0,\r\n    0x04afb,\r\n    0x04ad0,\r\n    0x0a4d0,\r\n    0x1d0b6,\r\n    0x0d250,\r\n    0x0d520,\r\n    0x0dd45, // 2030-2039\r\n    0x0b5a0,\r\n    0x056d0,\r\n    0x055b2,\r\n    0x049b0,\r\n    0x0a577,\r\n    0x0a4b0,\r\n    0x0aa50,\r\n    0x1b255,\r\n    0x06d20,\r\n    0x0ada0, // 2040-2049\r\n    /** <NAME_EMAIL>**/\r\n    0x14b63,\r\n    0x09370,\r\n    0x049f8,\r\n    0x04970,\r\n    0x064b0,\r\n    0x168a6,\r\n    0x0ea50,\r\n    0x06b20,\r\n    0x1a6c4,\r\n    0x0aae0, // 2050-2059\r\n    0x092e0,\r\n    0x0d2e3,\r\n    0x0c960,\r\n    0x0d557,\r\n    0x0d4a0,\r\n    0x0da50,\r\n    0x05d55,\r\n    0x056a0,\r\n    0x0a6d0,\r\n    0x055d4, // 2060-2069\r\n    0x052d0,\r\n    0x0a9b8,\r\n    0x0a950,\r\n    0x0b4a0,\r\n    0x0b6a6,\r\n    0x0ad50,\r\n    0x055a0,\r\n    0x0aba4,\r\n    0x0a5b0,\r\n    0x052b0, // 2070-2079\r\n    0x0b273,\r\n    0x06930,\r\n    0x07337,\r\n    0x06aa0,\r\n    0x0ad50,\r\n    0x14b55,\r\n    0x04b60,\r\n    0x0a570,\r\n    0x054e4,\r\n    0x0d160, // 2080-2089\r\n    0x0e968,\r\n    0x0d520,\r\n    0x0daa0,\r\n    0x16aa6,\r\n    0x056d0,\r\n    0x04ae0,\r\n    0x0a9d4,\r\n    0x0a2d0,\r\n    0x0d150,\r\n    0x0f252, // 2090-2099\r\n    0x0d520\r\n  ], // 2100\r\n\r\n  /**\r\n   * 公历每个月份的天数普通表\r\n   * @Array Of Property\r\n   * @return Number\r\n   */\r\n  solarMonth: [31, 28, 31, 30, 31, 30, 31, 31, 30, 31, 30, 31],\r\n\r\n  /**\r\n   * 天干地支之天干速查表\r\n   * @Array Of Property trans[\"甲\",\"乙\",\"丙\",\"丁\",\"戊\",\"己\",\"庚\",\"辛\",\"壬\",\"癸\"]\r\n   * @return Cn string\r\n   */\r\n  Gan: [\r\n    '\\u7532',\r\n    '\\u4e59',\r\n    '\\u4e19',\r\n    '\\u4e01',\r\n    '\\u620a',\r\n    '\\u5df1',\r\n    '\\u5e9a',\r\n    '\\u8f9b',\r\n    '\\u58ec',\r\n    '\\u7678'\r\n  ],\r\n\r\n  /**\r\n   * 天干地支之地支速查表\r\n   * @Array Of Property\r\n   * @trans[\"子\",\"丑\",\"寅\",\"卯\",\"辰\",\"巳\",\"午\",\"未\",\"申\",\"酉\",\"戌\",\"亥\"]\r\n   * @return Cn string\r\n   */\r\n  Zhi: [\r\n    '\\u5b50',\r\n    '\\u4e11',\r\n    '\\u5bc5',\r\n    '\\u536f',\r\n    '\\u8fb0',\r\n    '\\u5df3',\r\n    '\\u5348',\r\n    '\\u672a',\r\n    '\\u7533',\r\n    '\\u9149',\r\n    '\\u620c',\r\n    '\\u4ea5'\r\n  ],\r\n\r\n  /**\r\n   * 天干地支之地支速查表<=>生肖\r\n   * @Array Of Property\r\n   * @trans[\"鼠\",\"牛\",\"虎\",\"兔\",\"龙\",\"蛇\",\"马\",\"羊\",\"猴\",\"鸡\",\"狗\",\"猪\"]\r\n   * @return Cn string\r\n   */\r\n  Animals: [\r\n    '\\u9f20',\r\n    '\\u725b',\r\n    '\\u864e',\r\n    '\\u5154',\r\n    '\\u9f99',\r\n    '\\u86c7',\r\n    '\\u9a6c',\r\n    '\\u7f8a',\r\n    '\\u7334',\r\n    '\\u9e21',\r\n    '\\u72d7',\r\n    '\\u732a'\r\n  ],\r\n\r\n  /**\r\n   * 阳历节日\r\n   */\r\n  festival: {\r\n    '1-1': { title: '元旦节' },\r\n    '2-14': { title: '情人节' },\r\n    '5-1': { title: '劳动节' },\r\n    '5-4': { title: '青年节' },\r\n    '6-1': { title: '儿童节' },\r\n    '9-10': { title: '教师节' },\r\n    '10-1': { title: '国庆节' },\r\n    '12-25': { title: '圣诞节' },\r\n\r\n    '3-8': { title: '妇女节' },\r\n    '3-12': { title: '植树节' },\r\n    '4-1': { title: '愚人节' },\r\n    '5-12': { title: '护士节' },\r\n    '7-1': { title: '建党节' },\r\n    '8-1': { title: '建军节' },\r\n    '12-24': { title: '平安夜' }\r\n  },\r\n\r\n  /**\r\n   * 农历节日\r\n   */\r\n  lFestival: {\r\n    '12-30': { title: '除夕' },\r\n    '1-1': { title: '春节' },\r\n    '1-15': { title: '元宵节' },\r\n    '2-2': { title: '龙抬头' },\r\n    '5-5': { title: '端午节' },\r\n    '7-7': { title: '七夕节' },\r\n    '7-15': { title: '中元节' },\r\n    '8-15': { title: '中秋节' },\r\n    '9-9': { title: '重阳节' },\r\n    '10-1': { title: '寒衣节' },\r\n    '10-15': { title: '下元节' },\r\n    '12-8': { title: '腊八节' },\r\n    '12-23': { title: '北方小年' },\r\n    '12-24': { title: '南方小年' }\r\n  },\r\n\r\n  /**\r\n   * 返回默认定义的阳历节日\r\n   */\r\n  getFestival() {\r\n    return this.festival\r\n  },\r\n\r\n  /**\r\n   * 返回默认定义的内容里节日\r\n   */\r\n  getLunarFestival() {\r\n    return this.lFestival\r\n  },\r\n\r\n  /**\r\n   *\r\n   * @param param {Object} 按照festival的格式输入数据，设置阳历节日\r\n   */\r\n  setFestival(param = {}) {\r\n    this.festival = param\r\n  },\r\n\r\n  /**\r\n   *\r\n   * @param param {Object} 按照lFestival的格式输入数据，设置农历节日\r\n   */\r\n  setLunarFestival(param = {}) {\r\n    this.lFestival = param\r\n  },\r\n\r\n  /**\r\n   * 24节气速查表\r\n   * @Array Of Property\r\n   * @trans[\"小寒\",\"大寒\",\"立春\",\"雨水\",\"惊蛰\",\"春分\",\"清明\",\"谷雨\",\"立夏\",\"小满\",\"芒种\",\"夏至\",\"小暑\",\"大暑\",\"立秋\",\"处暑\",\"白露\",\"秋分\",\"寒露\",\"霜降\",\"立冬\",\"小雪\",\"大雪\",\"冬至\"]\r\n   * @return Cn string\r\n   */\r\n  solarTerm: [\r\n    '\\u5c0f\\u5bd2',\r\n    '\\u5927\\u5bd2',\r\n    '\\u7acb\\u6625',\r\n    '\\u96e8\\u6c34',\r\n    '\\u60ca\\u86f0',\r\n    '\\u6625\\u5206',\r\n    '\\u6e05\\u660e',\r\n    '\\u8c37\\u96e8',\r\n    '\\u7acb\\u590f',\r\n    '\\u5c0f\\u6ee1',\r\n    '\\u8292\\u79cd',\r\n    '\\u590f\\u81f3',\r\n    '\\u5c0f\\u6691',\r\n    '\\u5927\\u6691',\r\n    '\\u7acb\\u79cb',\r\n    '\\u5904\\u6691',\r\n    '\\u767d\\u9732',\r\n    '\\u79cb\\u5206',\r\n    '\\u5bd2\\u9732',\r\n    '\\u971c\\u964d',\r\n    '\\u7acb\\u51ac',\r\n    '\\u5c0f\\u96ea',\r\n    '\\u5927\\u96ea',\r\n    '\\u51ac\\u81f3'\r\n  ],\r\n\r\n  /**\r\n   * 1900-2100各年的24节气日期速查表\r\n   * @Array Of Property\r\n   * @return 0x string For splice\r\n   */\r\n  sTermInfo: [\r\n    '9778397bd097c36b0b6fc9274c91aa',\r\n    '97b6b97bd19801ec9210c965cc920e',\r\n    '97bcf97c3598082c95f8c965cc920f',\r\n    '97bd0b06bdb0722c965ce1cfcc920f',\r\n    'b027097bd097c36b0b6fc9274c91aa',\r\n    '97b6b97bd19801ec9210c965cc920e',\r\n    '97bcf97c359801ec95f8c965cc920f',\r\n    '97bd0b06bdb0722c965ce1cfcc920f',\r\n    'b027097bd097c36b0b6fc9274c91aa',\r\n    '97b6b97bd19801ec9210c965cc920e',\r\n    '97bcf97c359801ec95f8c965cc920f',\r\n    '97bd0b06bdb0722c965ce1cfcc920f',\r\n    'b027097bd097c36b0b6fc9274c91aa',\r\n    '9778397bd19801ec9210c965cc920e',\r\n    '97b6b97bd19801ec95f8c965cc920f',\r\n    '97bd09801d98082c95f8e1cfcc920f',\r\n    '97bd097bd097c36b0b6fc9210c8dc2',\r\n    '9778397bd197c36c9210c9274c91aa',\r\n    '97b6b97bd19801ec95f8c965cc920e',\r\n    '97bd09801d98082c95f8e1cfcc920f',\r\n    '97bd097bd097c36b0b6fc9210c8dc2',\r\n    '9778397bd097c36c9210c9274c91aa',\r\n    '97b6b97bd19801ec95f8c965cc920e',\r\n    '97bcf97c3598082c95f8e1cfcc920f',\r\n    '97bd097bd097c36b0b6fc9210c8dc2',\r\n    '9778397bd097c36c9210c9274c91aa',\r\n    '97b6b97bd19801ec9210c965cc920e',\r\n    '97bcf97c3598082c95f8c965cc920f',\r\n    '97bd097bd097c35b0b6fc920fb0722',\r\n    '9778397bd097c36b0b6fc9274c91aa',\r\n    '97b6b97bd19801ec9210c965cc920e',\r\n    '97bcf97c3598082c95f8c965cc920f',\r\n    '97bd097bd097c35b0b6fc920fb0722',\r\n    '9778397bd097c36b0b6fc9274c91aa',\r\n    '97b6b97bd19801ec9210c965cc920e',\r\n    '97bcf97c359801ec95f8c965cc920f',\r\n    '97bd097bd097c35b0b6fc920fb0722',\r\n    '9778397bd097c36b0b6fc9274c91aa',\r\n    '97b6b97bd19801ec9210c965cc920e',\r\n    '97bcf97c359801ec95f8c965cc920f',\r\n    '97bd097bd097c35b0b6fc920fb0722',\r\n    '9778397bd097c36b0b6fc9274c91aa',\r\n    '97b6b97bd19801ec9210c965cc920e',\r\n    '97bcf97c359801ec95f8c965cc920f',\r\n    '97bd097bd07f595b0b6fc920fb0722',\r\n    '9778397bd097c36b0b6fc9210c8dc2',\r\n    '9778397bd19801ec9210c9274c920e',\r\n    '97b6b97bd19801ec95f8c965cc920f',\r\n    '97bd07f5307f595b0b0bc920fb0722',\r\n    '7f0e397bd097c36b0b6fc9210c8dc2',\r\n    '9778397bd097c36c9210c9274c920e',\r\n    '97b6b97bd19801ec95f8c965cc920f',\r\n    '97bd07f5307f595b0b0bc920fb0722',\r\n    '7f0e397bd097c36b0b6fc9210c8dc2',\r\n    '9778397bd097c36c9210c9274c91aa',\r\n    '97b6b97bd19801ec9210c965cc920e',\r\n    '97bd07f1487f595b0b0bc920fb0722',\r\n    '7f0e397bd097c36b0b6fc9210c8dc2',\r\n    '9778397bd097c36b0b6fc9274c91aa',\r\n    '97b6b97bd19801ec9210c965cc920e',\r\n    '97bcf7f1487f595b0b0bb0b6fb0722',\r\n    '7f0e397bd097c35b0b6fc920fb0722',\r\n    '9778397bd097c36b0b6fc9274c91aa',\r\n    '97b6b97bd19801ec9210c965cc920e',\r\n    '97bcf7f1487f595b0b0bb0b6fb0722',\r\n    '7f0e397bd097c35b0b6fc920fb0722',\r\n    '9778397bd097c36b0b6fc9274c91aa',\r\n    '97b6b97bd19801ec9210c965cc920e',\r\n    '97bcf7f1487f531b0b0bb0b6fb0722',\r\n    '7f0e397bd097c35b0b6fc920fb0722',\r\n    '9778397bd097c36b0b6fc9274c91aa',\r\n    '97b6b97bd19801ec9210c965cc920e',\r\n    '97bcf7f1487f531b0b0bb0b6fb0722',\r\n    '7f0e397bd07f595b0b6fc920fb0722',\r\n    '9778397bd097c36b0b6fc9274c91aa',\r\n    '97b6b97bd19801ec9210c9274c920e',\r\n    '97bcf7f0e47f531b0b0bb0b6fb0722',\r\n    '7f0e397bd07f595b0b0bc920fb0722',\r\n    '9778397bd097c36b0b6fc9210c91aa',\r\n    '97b6b97bd197c36c9210c9274c920e',\r\n    '97bcf7f0e47f531b0b0bb0b6fb0722',\r\n    '7f0e397bd07f595b0b0bc920fb0722',\r\n    '9778397bd097c36b0b6fc9210c8dc2',\r\n    '9778397bd097c36c9210c9274c920e',\r\n    '97b6b7f0e47f531b0723b0b6fb0722',\r\n    '7f0e37f5307f595b0b0bc920fb0722',\r\n    '7f0e397bd097c36b0b6fc9210c8dc2',\r\n    '9778397bd097c36b0b70c9274c91aa',\r\n    '97b6b7f0e47f531b0723b0b6fb0721',\r\n    '7f0e37f1487f595b0b0bb0b6fb0722',\r\n    '7f0e397bd097c35b0b6fc9210c8dc2',\r\n    '9778397bd097c36b0b6fc9274c91aa',\r\n    '97b6b7f0e47f531b0723b0b6fb0721',\r\n    '7f0e27f1487f595b0b0bb0b6fb0722',\r\n    '7f0e397bd097c35b0b6fc920fb0722',\r\n    '9778397bd097c36b0b6fc9274c91aa',\r\n    '97b6b7f0e47f531b0723b0b6fb0721',\r\n    '7f0e27f1487f531b0b0bb0b6fb0722',\r\n    '7f0e397bd097c35b0b6fc920fb0722',\r\n    '9778397bd097c36b0b6fc9274c91aa',\r\n    '97b6b7f0e47f531b0723b0b6fb0721',\r\n    '7f0e27f1487f531b0b0bb0b6fb0722',\r\n    '7f0e397bd097c35b0b6fc920fb0722',\r\n    '9778397bd097c36b0b6fc9274c91aa',\r\n    '97b6b7f0e47f531b0723b0b6fb0721',\r\n    '7f0e27f1487f531b0b0bb0b6fb0722',\r\n    '7f0e397bd07f595b0b0bc920fb0722',\r\n    '9778397bd097c36b0b6fc9274c91aa',\r\n    '97b6b7f0e47f531b0723b0787b0721',\r\n    '7f0e27f0e47f531b0b0bb0b6fb0722',\r\n    '7f0e397bd07f595b0b0bc920fb0722',\r\n    '9778397bd097c36b0b6fc9210c91aa',\r\n    '97b6b7f0e47f149b0723b0787b0721',\r\n    '7f0e27f0e47f531b0723b0b6fb0722',\r\n    '7f0e397bd07f595b0b0bc920fb0722',\r\n    '9778397bd097c36b0b6fc9210c8dc2',\r\n    '977837f0e37f149b0723b0787b0721',\r\n    '7f07e7f0e47f531b0723b0b6fb0722',\r\n    '7f0e37f5307f595b0b0bc920fb0722',\r\n    '7f0e397bd097c35b0b6fc9210c8dc2',\r\n    '977837f0e37f14998082b0787b0721',\r\n    '7f07e7f0e47f531b0723b0b6fb0721',\r\n    '7f0e37f1487f595b0b0bb0b6fb0722',\r\n    '7f0e397bd097c35b0b6fc9210c8dc2',\r\n    '977837f0e37f14998082b0787b06bd',\r\n    '7f07e7f0e47f531b0723b0b6fb0721',\r\n    '7f0e27f1487f531b0b0bb0b6fb0722',\r\n    '7f0e397bd097c35b0b6fc920fb0722',\r\n    '977837f0e37f14998082b0787b06bd',\r\n    '7f07e7f0e47f531b0723b0b6fb0721',\r\n    '7f0e27f1487f531b0b0bb0b6fb0722',\r\n    '7f0e397bd097c35b0b6fc920fb0722',\r\n    '977837f0e37f14998082b0787b06bd',\r\n    '7f07e7f0e47f531b0723b0b6fb0721',\r\n    '7f0e27f1487f531b0b0bb0b6fb0722',\r\n    '7f0e397bd07f595b0b0bc920fb0722',\r\n    '977837f0e37f14998082b0787b06bd',\r\n    '7f07e7f0e47f531b0723b0b6fb0721',\r\n    '7f0e27f1487f531b0b0bb0b6fb0722',\r\n    '7f0e397bd07f595b0b0bc920fb0722',\r\n    '977837f0e37f14998082b0787b06bd',\r\n    '7f07e7f0e47f149b0723b0787b0721',\r\n    '7f0e27f0e47f531b0b0bb0b6fb0722',\r\n    '7f0e397bd07f595b0b0bc920fb0722',\r\n    '977837f0e37f14998082b0723b06bd',\r\n    '7f07e7f0e37f149b0723b0787b0721',\r\n    '7f0e27f0e47f531b0723b0b6fb0722',\r\n    '7f0e397bd07f595b0b0bc920fb0722',\r\n    '977837f0e37f14898082b0723b02d5',\r\n    '7ec967f0e37f14998082b0787b0721',\r\n    '7f07e7f0e47f531b0723b0b6fb0722',\r\n    '7f0e37f1487f595b0b0bb0b6fb0722',\r\n    '7f0e37f0e37f14898082b0723b02d5',\r\n    '7ec967f0e37f14998082b0787b0721',\r\n    '7f07e7f0e47f531b0723b0b6fb0722',\r\n    '7f0e37f1487f531b0b0bb0b6fb0722',\r\n    '7f0e37f0e37f14898082b0723b02d5',\r\n    '7ec967f0e37f14998082b0787b06bd',\r\n    '7f07e7f0e47f531b0723b0b6fb0721',\r\n    '7f0e37f1487f531b0b0bb0b6fb0722',\r\n    '7f0e37f0e37f14898082b072297c35',\r\n    '7ec967f0e37f14998082b0787b06bd',\r\n    '7f07e7f0e47f531b0723b0b6fb0721',\r\n    '7f0e27f1487f531b0b0bb0b6fb0722',\r\n    '7f0e37f0e37f14898082b072297c35',\r\n    '7ec967f0e37f14998082b0787b06bd',\r\n    '7f07e7f0e47f531b0723b0b6fb0721',\r\n    '7f0e27f1487f531b0b0bb0b6fb0722',\r\n    '7f0e37f0e366aa89801eb072297c35',\r\n    '7ec967f0e37f14998082b0787b06bd',\r\n    '7f07e7f0e47f149b0723b0787b0721',\r\n    '7f0e27f1487f531b0b0bb0b6fb0722',\r\n    '7f0e37f0e366aa89801eb072297c35',\r\n    '7ec967f0e37f14998082b0723b06bd',\r\n    '7f07e7f0e47f149b0723b0787b0721',\r\n    '7f0e27f0e47f531b0723b0b6fb0722',\r\n    '7f0e37f0e366aa89801eb072297c35',\r\n    '7ec967f0e37f14998082b0723b06bd',\r\n    '7f07e7f0e37f14998083b0787b0721',\r\n    '7f0e27f0e47f531b0723b0b6fb0722',\r\n    '7f0e37f0e366aa89801eb072297c35',\r\n    '7ec967f0e37f14898082b0723b02d5',\r\n    '7f07e7f0e37f14998082b0787b0721',\r\n    '7f07e7f0e47f531b0723b0b6fb0722',\r\n    '7f0e36665b66aa89801e9808297c35',\r\n    '665f67f0e37f14898082b0723b02d5',\r\n    '7ec967f0e37f14998082b0787b0721',\r\n    '7f07e7f0e47f531b0723b0b6fb0722',\r\n    '7f0e36665b66a449801e9808297c35',\r\n    '665f67f0e37f14898082b0723b02d5',\r\n    '7ec967f0e37f14998082b0787b06bd',\r\n    '7f07e7f0e47f531b0723b0b6fb0721',\r\n    '7f0e36665b66a449801e9808297c35',\r\n    '665f67f0e37f14898082b072297c35',\r\n    '7ec967f0e37f14998082b0787b06bd',\r\n    '7f07e7f0e47f531b0723b0b6fb0721',\r\n    '7f0e26665b66a449801e9808297c35',\r\n    '665f67f0e37f1489801eb072297c35',\r\n    '7ec967f0e37f14998082b0787b06bd',\r\n    '7f07e7f0e47f531b0723b0b6fb0721',\r\n    '7f0e27f1487f531b0b0bb0b6fb0722'\r\n  ],\r\n\r\n  /**\r\n   * 数字转中文速查表\r\n   * @Array Of Property\r\n   * @trans ['日','一','二','三','四','五','六','七','八','九','十']\r\n   * @return Cn string\r\n   */\r\n  nStr1: [\r\n    '\\u65e5',\r\n    '\\u4e00',\r\n    '\\u4e8c',\r\n    '\\u4e09',\r\n    '\\u56db',\r\n    '\\u4e94',\r\n    '\\u516d',\r\n    '\\u4e03',\r\n    '\\u516b',\r\n    '\\u4e5d',\r\n    '\\u5341'\r\n  ],\r\n\r\n  /**\r\n   * 日期转农历称呼速查表\r\n   * @Array Of Property\r\n   * @trans ['初','十','廿','卅']\r\n   * @return Cn string\r\n   */\r\n  nStr2: ['\\u521d', '\\u5341', '\\u5eff', '\\u5345'],\r\n\r\n  /**\r\n   * 月份转农历称呼速查表\r\n   * @Array Of Property\r\n   * @trans ['正','一','二','三','四','五','六','七','八','九','十','冬','腊']\r\n   * @return Cn string\r\n   */\r\n  nStr3: [\r\n    '\\u6b63',\r\n    '\\u4e8c',\r\n    '\\u4e09',\r\n    '\\u56db',\r\n    '\\u4e94',\r\n    '\\u516d',\r\n    '\\u4e03',\r\n    '\\u516b',\r\n    '\\u4e5d',\r\n    '\\u5341',\r\n    '\\u51ac',\r\n    '\\u814a'\r\n  ],\r\n\r\n  /**\r\n   * 返回农历y年一整年的总天数\r\n   * @param y lunar Year\r\n   * @return Number\r\n   * @eg:var count = calendar.lYearDays(1987) ;//count=387\r\n   */\r\n  lYearDays: function(y) {\r\n    let i\r\n    let sum = 348\r\n    for (i = 0x8000; i > 0x8; i >>= 1) {\r\n      sum += this.lunarInfo[y - 1900] & i ? 1 : 0\r\n    }\r\n    return sum + this.leapDays(y)\r\n  },\r\n\r\n  /**\r\n   * 返回农历y年闰月是哪个月；若y年没有闰月 则返回0\r\n   * @param y lunar Year\r\n   * @return Number (0-12)\r\n   * @eg:var leapMonth = calendar.leapMonth(1987) ;//leapMonth=6\r\n   */\r\n  leapMonth: function(y) {\r\n    // 闰字编码 \\u95f0\r\n    return this.lunarInfo[y - 1900] & 0xf\r\n  },\r\n\r\n  /**\r\n   * 返回农历y年闰月的天数 若该年没有闰月则返回0\r\n   * @param y lunar Year\r\n   * @return Number (0、29、30)\r\n   * @eg:var leapMonthDay = calendar.leapDays(1987) ;//leapMonthDay=29\r\n   */\r\n  leapDays: function(y) {\r\n    if (this.leapMonth(y)) {\r\n      return this.lunarInfo[y - 1900] & 0x10000 ? 30 : 29\r\n    }\r\n    return 0\r\n  },\r\n\r\n  /**\r\n   * 返回农历y年m月（非闰月）的总天数，计算m为闰月时的天数请使用leapDays方法\r\n   * @param y lunar Year\r\n   * @param m lunar Month\r\n   * @return Number (-1、29、30)\r\n   * @eg:var MonthDay = calendar.monthDays(1987,9) ;//MonthDay=29\r\n   */\r\n  monthDays: function(y, m) {\r\n    if (m > 12 || m < 1) {\r\n      return -1\r\n    } // 月份参数从1至12，参数错误返回-1\r\n    return this.lunarInfo[y - 1900] & (0x10000 >> m) ? 30 : 29\r\n  },\r\n\r\n  /**\r\n   * 返回公历(!)y年m月的天数\r\n   * @param y solar Year\r\n   * @param m solar Month\r\n   * @return Number (-1、28、29、30、31)\r\n   * @eg:var solarMonthDay = calendar.leapDays(1987) ;//solarMonthDay=30\r\n   */\r\n  solarDays: function(y, m) {\r\n    if (m > 12 || m < 1) {\r\n      return -1\r\n    } // 若参数错误 返回-1\r\n    const ms = m - 1\r\n    if (ms === 1) {\r\n      // 2月份的闰平规律测算后确认返回28或29\r\n      return (y % 4 === 0 && y % 100 !== 0) || y % 400 === 0 ? 29 : 28\r\n    } else {\r\n      return this.solarMonth[ms]\r\n    }\r\n  },\r\n\r\n  /**\r\n   * 农历年份转换为干支纪年\r\n   * @param  lYear 农历年的年份数\r\n   * @return Cn string\r\n   */\r\n  toGanZhiYear: function(lYear) {\r\n    var ganKey = (lYear - 3) % 10\r\n    var zhiKey = (lYear - 3) % 12\r\n    if (ganKey === 0) ganKey = 10 // 如果余数为0则为最后一个天干\r\n    if (zhiKey === 0) zhiKey = 12 // 如果余数为0则为最后一个地支\r\n    return this.Gan[ganKey - 1] + this.Zhi[zhiKey - 1]\r\n  },\r\n\r\n  /**\r\n   * 公历月、日判断所属星座\r\n   * @param  cMonth [description]\r\n   * @param  cDay [description]\r\n   * @return Cn string\r\n   */\r\n  toAstro: function(cMonth, cDay) {\r\n    const s =\r\n      '\\u9b54\\u7faf\\u6c34\\u74f6\\u53cc\\u9c7c\\u767d\\u7f8a\\u91d1\\u725b\\u53cc\\u5b50\\u5de8\\u87f9\\u72ee\\u5b50\\u5904\\u5973\\u5929\\u79e4\\u5929\\u874e\\u5c04\\u624b\\u9b54\\u7faf'\r\n    const arr = [20, 19, 21, 21, 21, 22, 23, 23, 23, 23, 22, 22]\r\n    return s.substr(cMonth * 2 - (cDay < arr[cMonth - 1] ? 2 : 0), 2) + '\\u5ea7' // 座\r\n  },\r\n\r\n  /**\r\n   * 传入offset偏移量返回干支\r\n   * @param offset 相对甲子的偏移量\r\n   * @return Cn string\r\n   */\r\n  toGanZhi: function(offset) {\r\n    return this.Gan[offset % 10] + this.Zhi[offset % 12]\r\n  },\r\n\r\n  /**\r\n   * 传入公历(!)y年获得该年第n个节气的公历日期\r\n   * @param y y公历年(1900-2100)\r\n   * @param n n二十四节气中的第几个节气(1~24)；从n=1(小寒)算起\r\n   * @return day Number\r\n   * @eg:var _24 = calendar.getTerm(1987,3) ;//_24=4;意即1987年2月4日立春\r\n   */\r\n  getTerm: function(y, n) {\r\n    if (y < 1900 || y > 2100) {\r\n      return -1\r\n    }\r\n    if (n < 1 || n > 24) {\r\n      return -1\r\n    }\r\n    const _table = this.sTermInfo[y - 1900]\r\n    const _info = [\r\n      parseInt('0x' + _table.substr(0, 5)).toString(),\r\n      parseInt('0x' + _table.substr(5, 5)).toString(),\r\n      parseInt('0x' + _table.substr(10, 5)).toString(),\r\n      parseInt('0x' + _table.substr(15, 5)).toString(),\r\n      parseInt('0x' + _table.substr(20, 5)).toString(),\r\n      parseInt('0x' + _table.substr(25, 5)).toString()\r\n    ]\r\n    const _calcDay = [\r\n      _info[0].substr(0, 1),\r\n      _info[0].substr(1, 2),\r\n      _info[0].substr(3, 1),\r\n      _info[0].substr(4, 2),\r\n\r\n      _info[1].substr(0, 1),\r\n      _info[1].substr(1, 2),\r\n      _info[1].substr(3, 1),\r\n      _info[1].substr(4, 2),\r\n\r\n      _info[2].substr(0, 1),\r\n      _info[2].substr(1, 2),\r\n      _info[2].substr(3, 1),\r\n      _info[2].substr(4, 2),\r\n\r\n      _info[3].substr(0, 1),\r\n      _info[3].substr(1, 2),\r\n      _info[3].substr(3, 1),\r\n      _info[3].substr(4, 2),\r\n\r\n      _info[4].substr(0, 1),\r\n      _info[4].substr(1, 2),\r\n      _info[4].substr(3, 1),\r\n      _info[4].substr(4, 2),\r\n\r\n      _info[5].substr(0, 1),\r\n      _info[5].substr(1, 2),\r\n      _info[5].substr(3, 1),\r\n      _info[5].substr(4, 2)\r\n    ]\r\n    return parseInt(_calcDay[n - 1])\r\n  },\r\n\r\n  /**\r\n   * 传入农历数字月份返回汉语通俗表示法\r\n   * @param m lunar month\r\n   * @return Cn string\r\n   * @eg:var cnMonth = calendar.toChinaMonth(12) ;//cnMonth='腊月'\r\n   */\r\n  toChinaMonth: function(m) {\r\n    // 月 => \\u6708\r\n    if (m > 12 || m < 1) {\r\n      return -1\r\n    } // 若参数错误 返回-1\r\n    let s = this.nStr3[m - 1]\r\n    s += '\\u6708' // 加上月字\r\n    return s\r\n  },\r\n\r\n  /**\r\n   * 传入农历日期数字返回汉字表示法\r\n   * @param d lunar day\r\n   * @return Cn string\r\n   * @eg:var cnDay = calendar.toChinaDay(21) ;//cnMonth='廿一'\r\n   */\r\n  toChinaDay: function(d) {\r\n    // 日 => \\u65e5\r\n    let s\r\n    switch (d) {\r\n      case 10:\r\n        s = '\\u521d\\u5341'\r\n        break\r\n      case 20:\r\n        s = '\\u4e8c\\u5341'\r\n        break\r\n      case 30:\r\n        s = '\\u4e09\\u5341'\r\n        break\r\n      default:\r\n        s = this.nStr2[Math.floor(d / 10)]\r\n        s += this.nStr1[d % 10]\r\n    }\r\n    return s\r\n  },\r\n\r\n  /**\r\n   * 年份转生肖[!仅能大致转换] => 精确划分生肖分界线是“立春”\r\n   * @param y year\r\n   * @return Cn string\r\n   * @eg:var animal = calendar.getAnimal(1987) ;//animal='兔'\r\n   */\r\n  getAnimal: function(y) {\r\n    return this.Animals[(y - 4) % 12]\r\n  },\r\n\r\n  /**\r\n   * 传入阳历年月日获得详细的公历、农历object信息 <=>JSON\r\n   * !important! 公历参数区间1900.1.31~2100.12.31\r\n   * @param yPara  solar year\r\n   * @param mPara  solar month\r\n   * @param dPara  solar day\r\n   * @return JSON object\r\n   * @eg:console.log(calendar.solar2lunar(1987,11,01));\r\n   */\r\n  solar2lunar: function(yPara, mPara, dPara) {\r\n    let y = parseInt(yPara)\r\n    let m = parseInt(mPara)\r\n    let d = parseInt(dPara)\r\n    // 年份限定、上限\r\n    if (y < 1900 || y > 2100) {\r\n      return -1 // undefined转换为数字变为NaN\r\n    }\r\n    // 公历传参最下限\r\n    if (y === 1900 && m === 1 && d < 31) {\r\n      return -1\r\n    }\r\n\r\n    // 未传参  获得当天\r\n    let objDate\r\n    if (!y) {\r\n      objDate = new Date()\r\n    } else {\r\n      objDate = new Date(y, parseInt(m) - 1, d)\r\n    }\r\n    let i\r\n    let leap = 0\r\n    let temp = 0\r\n    // 修正ymd参数\r\n    y = objDate.getFullYear()\r\n    m = objDate.getMonth() + 1\r\n    d = objDate.getDate()\r\n    let offset =\r\n      (Date.UTC(objDate.getFullYear(), objDate.getMonth(), objDate.getDate()) -\r\n        Date.UTC(1900, 0, 31)) /\r\n      86400000\r\n    for (i = 1900; i < 2101 && offset > 0; i++) {\r\n      temp = this.lYearDays(i)\r\n      offset -= temp\r\n    }\r\n    if (offset < 0) {\r\n      offset += temp\r\n      i--\r\n    }\r\n\r\n    // 是否今天\r\n    const isTodayObj = new Date()\r\n    let isToday = false\r\n    if (\r\n      isTodayObj.getFullYear() === y &&\r\n      isTodayObj.getMonth() + 1 === m &&\r\n      isTodayObj.getDate() === d\r\n    ) {\r\n      isToday = true\r\n    }\r\n    // 星期几\r\n    let nWeek = objDate.getDay()\r\n    const cWeek = this.nStr1[nWeek]\r\n    // 数字表示周几顺应天朝周一开始的惯例\r\n    if (nWeek === 0) {\r\n      nWeek = 7\r\n    }\r\n    // 农历年\r\n    const year = i\r\n    leap = this.leapMonth(i) // 闰哪个月\r\n    let isLeap = false\r\n\r\n    // 效验闰月\r\n    for (i = 1; i < 13 && offset > 0; i++) {\r\n      // 闰月\r\n      if (leap > 0 && i === leap + 1 && isLeap === false) {\r\n        --i\r\n        isLeap = true\r\n        temp = this.leapDays(year) // 计算农历闰月天数\r\n      } else {\r\n        temp = this.monthDays(year, i) // 计算农历普通月天数\r\n      }\r\n      // 解除闰月\r\n      if (isLeap === true && i === leap + 1) {\r\n        isLeap = false\r\n      }\r\n      offset -= temp\r\n    }\r\n    // 闰月导致数组下标重叠取反\r\n    if (offset === 0 && leap > 0 && i === leap + 1) {\r\n      if (isLeap) {\r\n        isLeap = false\r\n      } else {\r\n        isLeap = true\r\n        --i\r\n      }\r\n    }\r\n    if (offset < 0) {\r\n      offset += temp\r\n      --i\r\n    }\r\n    // 农历月\r\n    const month = i\r\n    // 农历日\r\n    const day = offset + 1\r\n    // 天干地支处理\r\n    const sm = m - 1\r\n    const gzY = this.toGanZhiYear(year)\r\n\r\n    // 当月的两个节气\r\n    // bugfix-2017-7-24 11:03:38 use lunar Year Param `y` Not `year`\r\n    const firstNode = this.getTerm(y, m * 2 - 1) // 返回当月「节」为几日开始\r\n    const secondNode = this.getTerm(y, m * 2) // 返回当月「节」为几日开始\r\n\r\n    // 依据12节气修正干支月\r\n    let gzM = this.toGanZhi((y - 1900) * 12 + m + 11)\r\n    if (d >= firstNode) {\r\n      gzM = this.toGanZhi((y - 1900) * 12 + m + 12)\r\n    }\r\n\r\n    // 传入的日期的节气与否\r\n    let isTerm = false\r\n    let Term = null\r\n    if (firstNode === d) {\r\n      isTerm = true\r\n      Term = this.solarTerm[m * 2 - 2]\r\n    }\r\n    if (secondNode === d) {\r\n      isTerm = true\r\n      Term = this.solarTerm[m * 2 - 1]\r\n    }\r\n    // 日柱 当月一日与 1900/1/1 相差天数\r\n    const dayCyclical = Date.UTC(y, sm, 1, 0, 0, 0, 0) / 86400000 + 25567 + 10\r\n    const gzD = this.toGanZhi(dayCyclical + d - 1)\r\n    // 该日期所属的星座\r\n    const astro = this.toAstro(m, d)\r\n\r\n    const solarDate = y + '-' + m + '-' + d\r\n    const lunarDate = year + '-' + month + '-' + day\r\n\r\n    const festival = this.festival\r\n    const lFestival = this.lFestival\r\n\r\n    const festivalDate = m + '-' + d\r\n    const lunarFestivalDate = month + '-' + day\r\n\r\n    return {\r\n      date: solarDate,\r\n      lunarDate: lunarDate,\r\n      festival: festival[festivalDate] ? festival[festivalDate].title : null,\r\n      lunarFestival: lFestival[lunarFestivalDate]\r\n        ? lFestival[lunarFestivalDate].title\r\n        : null,\r\n      lYear: year,\r\n      lMonth: month,\r\n      lDay: day,\r\n      Animal: this.getAnimal(year),\r\n      IMonthCn: (isLeap ? '\\u95f0' : '') + this.toChinaMonth(month),\r\n      IDayCn: this.toChinaDay(day),\r\n      cYear: y,\r\n      cMonth: m,\r\n      cDay: d,\r\n      gzYear: gzY,\r\n      gzMonth: gzM,\r\n      gzDay: gzD,\r\n      isToday: isToday,\r\n      isLeap: isLeap,\r\n      nWeek: nWeek,\r\n      ncWeek: '\\u661f\\u671f' + cWeek,\r\n      isTerm: isTerm,\r\n      Term: Term,\r\n      astro: astro\r\n    }\r\n  },\r\n\r\n  /**\r\n   * 传入农历年月日以及传入的月份是否闰月获得详细的公历、农历object信息 <=>JSON\r\n   * !important! 参数区间1900.1.31~2100.12.1\r\n   * @param y  lunar year\r\n   * @param m  lunar month\r\n   * @param d  lunar day\r\n   * @param isLeapMonth  lunar month is leap or not.[如果是农历闰月第四个参数赋值true即可]\r\n   * @return JSON object\r\n   * @eg:console.log(calendar.lunar2solar(1987,9,10));\r\n   */\r\n  lunar2solar: function(y, m, d, isLeapMonth) {\r\n    y = parseInt(y)\r\n    m = parseInt(m)\r\n    d = parseInt(d)\r\n    isLeapMonth = !!isLeapMonth\r\n    // const leapOffset = 0\r\n    const leapMonth = this.leapMonth(y)\r\n    // const leapDay = this.leapDays(y)\r\n    if (isLeapMonth && leapMonth !== m) {\r\n      return -1\r\n    } // 传参要求计算该闰月公历 但该年得出的闰月与传参的月份并不同\r\n    if (\r\n      (y === 2100 && m === 12 && d > 1) ||\r\n      (y === 1900 && m === 1 && d < 31)\r\n    ) {\r\n      return -1\r\n    } // 超出了最大极限值\r\n    const day = this.monthDays(y, m)\r\n    let _day = day\r\n    // bugFix 2016-9-25\r\n    // if month is leap, _day use leapDays method\r\n    if (isLeapMonth) {\r\n      _day = this.leapDays(y, m)\r\n    }\r\n    if (y < 1900 || y > 2100 || d > _day) {\r\n      return -1\r\n    } // 参数合法性效验\r\n\r\n    // 计算农历的时间差\r\n    let offset = 0\r\n    let i\r\n    for (i = 1900; i < y; i++) {\r\n      offset += this.lYearDays(i)\r\n    }\r\n    let leap = 0\r\n    let isAdd = false\r\n    for (i = 1; i < m; i++) {\r\n      leap = this.leapMonth(y)\r\n      if (!isAdd) {\r\n        // 处理闰月\r\n        if (leap <= i && leap > 0) {\r\n          offset += this.leapDays(y)\r\n          isAdd = true\r\n        }\r\n      }\r\n      offset += this.monthDays(y, i)\r\n    }\r\n    // 转换闰月农历 需补充该年闰月的前一个月的时差\r\n    if (isLeapMonth) {\r\n      offset += day\r\n    }\r\n    // 1900年农历正月一日的公历时间为1900年1月30日0时0分0秒(该时间也是本农历的最开始起始点)\r\n    const strap = Date.UTC(1900, 1, 30, 0, 0, 0)\r\n    const calObj = new Date((offset + d - 31) * 86400000 + strap)\r\n    const cY = calObj.getUTCFullYear()\r\n    const cM = calObj.getUTCMonth() + 1\r\n    const cD = calObj.getUTCDate()\r\n\r\n    return this.solar2lunar(cY, cM, cD)\r\n  }\r\n}\r\n\r\nexport default calendar\r\n"], "mappings": ";;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAMA,QAAQ,GAAG;EACf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vB,MAAM,EAAE;MAAEA,KAAK,EAAE;IAAM,CAAC;IACxB,KAAK,EAAE;MAAEA,KAAK,EAAE;IAAM,CAAC;IACvB,KAAK,EAAE;MAAEA,KAAK,EAAE;IAAM,CAAC;IACvB,KAAK,EAAE;MAAEA,KAAK,EAAE;IAAM,CAAC;IACvB,MAAM,EAAE;MAAEA,KAAK,EAAE;IAAM,CAAC;IACxB,MAAM,EAAE;MAAEA,KAAK,EAAE;IAAM,CAAC;IACxB,OAAO,EAAE;MAAEA,KAAK,EAAE;IAAM,CAAC;IAEzB,KAAK,EAAE;MAAEA,KAAK,EAAE;IAAM,CAAC;IACvB,MAAM,EAAE;MAAEA,KAAK,EAAE;IAAM,CAAC;IACxB,KAAK,EAAE;MAAEA,KAAK,EAAE;IAAM,CAAC;IACvB,MAAM,EAAE;MAAEA,KAAK,EAAE;IAAM,CAAC;IACxB,KAAK,EAAE;MAAEA,KAAK,EAAE;IAAM,CAAC;IACvB,KAAK,EAAE;MAAEA,KAAK,EAAE;IAAM,CAAC;IACvB,OAAO,EAAE;MAAEA,KAAK,EAAE;IAAM;EAC1B,CAAC;EAED;AACF;AACA;EACEC,SAAS,EAAE;IACT,OAAO,EAAE;MAAED,KAAK,EAAE;IAAK,CAAC;IACxB,KAAK,EAAE;MAAEA,KAAK,EAAE;IAAK,CAAC;IACtB,MAAM,EAAE;MAAEA,KAAK,EAAE;IAAM,CAAC;IACxB,KAAK,EAAE;MAAEA,KAAK,EAAE;IAAM,CAAC;IACvB,KAAK,EAAE;MAAEA,KAAK,EAAE;IAAM,CAAC;IACvB,KAAK,EAAE;MAAEA,KAAK,EAAE;IAAM,CAAC;IACvB,MAAM,EAAE;MAAEA,KAAK,EAAE;IAAM,CAAC;IACxB,MAAM,EAAE;MAAEA,KAAK,EAAE;IAAM,CAAC;IACxB,KAAK,EAAE;MAAEA,KAAK,EAAE;IAAM,CAAC;IACvB,MAAM,EAAE;MAAEA,KAAK,EAAE;IAAM,CAAC;IACxB,OAAO,EAAE;MAAEA,KAAK,EAAE;IAAM,CAAC;IACzB,MAAM,EAAE;MAAEA,KAAK,EAAE;IAAM,CAAC;IACxB,OAAO,EAAE;MAAEA,KAAK,EAAE;IAAO,CAAC;IAC1B,OAAO,EAAE;MAAEA,KAAK,EAAE;IAAO;EAC3B,CAAC;EAED;AACF;AACA;EACEE,WAAW,yBAAG;IACZ,OAAO,IAAI,CAACH,QAAQ;EACtB,CAAC;EAED;AACF;AACA;EACEI,gBAAgB,8BAAG;IACjB,OAAO,IAAI,CAACF,SAAS;EACvB,CAAC;EAED;AACF;AACA;AACA;EACEG,WAAW,yBAAa;IAAA,IAAZC,KAAK,uEAAG,CAAC,CAAC;IACpB,IAAI,CAACN,QAAQ,GAAGM,KAAK;EACvB,CAAC;EAED;AACF;AACA;AACA;EACEC,gBAAgB,8BAAa;IAAA,IAAZD,KAAK,uEAAG,CAAC,CAAC;IACzB,IAAI,CAACJ,SAAS,GAAGI,KAAK;EACxB,CAAC;EAED;AACF;AACA;AACA;AACA;AACA;EACEE,SAAS,EAAE,CACT,cAAc,EACd,cAAc,EACd,cAAc,EACd,cAAc,EACd,cAAc,EACd,cAAc,EACd,cAAc,EACd,cAAc,EACd,cAAc,EACd,cAAc,EACd,cAAc,EACd,cAAc,EACd,cAAc,EACd,cAAc,EACd,cAAc,EACd,cAAc,EACd,cAAc,EACd,cAAc,EACd,cAAc,EACd,cAAc,EACd,cAAc,EACd,cAAc,EACd,cAAc,EACd,cAAc,CACf;EAED;AACF;AACA;AACA;AACA;EACEC,SAAS,EAAE,CACT,gCAAgC,EAChC,gCAAgC,EAChC,gCAAgC,EAChC,gCAAgC,EAChC,gCAAgC,EAChC,gCAAgC,EAChC,gCAAgC,EAChC,gCAAgC,EAChC,gCAAgC,EAChC,gCAAgC,EAChC,gCAAgC,EAChC,gCAAgC,EAChC,gCAAgC,EAChC,gCAAgC,EAChC,gCAAgC,EAChC,gCAAgC,EAChC,gCAAgC,EAChC,gCAAgC,EAChC,gCAAgC,EAChC,gCAAgC,EAChC,gCAAgC,EAChC,gCAAgC,EAChC,gCAAgC,EAChC,gCAAgC,EAChC,gCAAgC,EAChC,gCAAgC,EAChC,gCAAgC,EAChC,gCAAgC,EAChC,gCAAgC,EAChC,gCAAgC,EAChC,gCAAgC,EAChC,gCAAgC,EAChC,gCAAgC,EAChC,gCAAgC,EAChC,gCAAgC,EAChC,gCAAgC,EAChC,gCAAgC,EAChC,gCAAgC,EAChC,gCAAgC,EAChC,gCAAgC,EAChC,gCAAgC,EAChC,gCAAgC,EAChC,gCAAgC,EAChC,gCAAgC,EAChC,gCAAgC,EAChC,gCAAgC,EAChC,gCAAgC,EAChC,gCAAgC,EAChC,gCAAgC,EAChC,gCAAgC,EAChC,gCAAgC,EAChC,gCAAgC,EAChC,gCAAgC,EAChC,gCAAgC,EAChC,gCAAgC,EAChC,gCAAgC,EAChC,gCAAgC,EAChC,gCAAgC,EAChC,gCAAgC,EAChC,gCAAgC,EAChC,gCAAgC,EAChC,gCAAgC,EAChC,gCAAgC,EAChC,gCAAgC,EAChC,gCAAgC,EAChC,gCAAgC,EAChC,gCAAgC,EAChC,gCAAgC,EAChC,gCAAgC,EAChC,gCAAgC,EAChC,gCAAgC,EAChC,gCAAgC,EAChC,gCAAgC,EAChC,gCAAgC,EAChC,gCAAgC,EAChC,gCAAgC,EAChC,gCAAgC,EAChC,gCAAgC,EAChC,gCAAgC,EAChC,gCAAgC,EAChC,gCAAgC,EAChC,gCAAgC,EAChC,gCAAgC,EAChC,gCAAgC,EAChC,gCAAgC,EAChC,gCAAgC,EAChC,gCAAgC,EAChC,gCAAgC,EAChC,gCAAgC,EAChC,gCAAgC,EAChC,gCAAgC,EAChC,gCAAgC,EAChC,gCAAgC,EAChC,gCAAgC,EAChC,gCAAgC,EAChC,gCAAgC,EAChC,gCAAgC,EAChC,gCAAgC,EAChC,gCAAgC,EAChC,gCAAgC,EAChC,gCAAgC,EAChC,gCAAgC,EAChC,gCAAgC,EAChC,gCAAgC,EAChC,gCAAgC,EAChC,gCAAgC,EAChC,gCAAgC,EAChC,gCAAgC,EAChC,gCAAgC,EAChC,gCAAgC,EAChC,gCAAgC,EAChC,gCAAgC,EAChC,gCAAgC,EAChC,gCAAgC,EAChC,gCAAgC,EAChC,gCAAgC,EAChC,gCAAgC,EAChC,gCAAgC,EAChC,gCAAgC,EAChC,gCAAgC,EAChC,gCAAgC,EAChC,gCAAgC,EAChC,gCAAgC,EAChC,gCAAgC,EAChC,gCAAgC,EAChC,gCAAgC,EAChC,gCAAgC,EAChC,gCAAgC,EAChC,gCAAgC,EAChC,gCAAgC,EAChC,gCAAgC,EAChC,gCAAgC,EAChC,gCAAgC,EAChC,gCAAgC,EAChC,gCAAgC,EAChC,gCAAgC,EAChC,gCAAgC,EAChC,gCAAgC,EAChC,gCAAgC,EAChC,gCAAgC,EAChC,gCAAgC,EAChC,gCAAgC,EAChC,gCAAgC,EAChC,gCAAgC,EAChC,gCAAgC,EAChC,gCAAgC,EAChC,gCAAgC,EAChC,gCAAgC,EAChC,gCAAgC,EAChC,gCAAgC,EAChC,gCAAgC,EAChC,gCAAgC,EAChC,gCAAgC,EAChC,gCAAgC,EAChC,gCAAgC,EAChC,gCAAgC,EAChC,gCAAgC,EAChC,gCAAgC,EAChC,gCAAgC,EAChC,gCAAgC,EAChC,gCAAgC,EAChC,gCAAgC,EAChC,gCAAgC,EAChC,gCAAgC,EAChC,gCAAgC,EAChC,gCAAgC,EAChC,gCAAgC,EAChC,gCAAgC,EAChC,gCAAgC,EAChC,gCAAgC,EAChC,gCAAgC,EAChC,gCAAgC,EAChC,gCAAgC,EAChC,gCAAgC,EAChC,gCAAgC,EAChC,gCAAgC,EAChC,gCAAgC,EAChC,gCAAgC,EAChC,gCAAgC,EAChC,gCAAgC,EAChC,gCAAgC,EAChC,gCAAgC,EAChC,gCAAgC,EAChC,gCAAgC,EAChC,gCAAgC,EAChC,gCAAgC,EAChC,gCAAgC,EAChC,gCAAgC,EAChC,gCAAgC,EAChC,gCAAgC,EAChC,gCAAgC,EAChC,gCAAgC,EAChC,gCAAgC,EAChC,gCAAgC,EAChC,gCAAgC,EAChC,gCAAgC,EAChC,gCAAgC,EAChC,gCAAgC,EAChC,gCAAgC,EAChC,gCAAgC,EAChC,gCAAgC,CACjC;EAED;AACF;AACA;AACA;AACA;AACA;EACEC,KAAK,EAAE,CACL,QAAQ,EACR,QAAQ,EACR,QAAQ,EACR,QAAQ,EACR,QAAQ,EACR,QAAQ,EACR,QAAQ,EACR,QAAQ,EACR,QAAQ,EACR,QAAQ,EACR,QAAQ,CACT;EAED;AACF;AACA;AACA;AACA;AACA;EACEC,KAAK,EAAE,CAAC,QAAQ,EAAE,QAAQ,EAAE,QAAQ,EAAE,QAAQ,CAAC;EAE/C;AACF;AACA;AACA;AACA;AACA;EACEC,KAAK,EAAE,CACL,QAAQ,EACR,QAAQ,EACR,QAAQ,EACR,QAAQ,EACR,QAAQ,EACR,QAAQ,EACR,QAAQ,EACR,QAAQ,EACR,QAAQ,EACR,QAAQ,EACR,QAAQ,EACR,QAAQ,CACT;EAED;AACF;AACA;AACA;AACA;AACA;EACEC,SAAS,EAAE,mBAASC,CAAC,EAAE;IACrB,IAAIC,CAAC;IACL,IAAIC,GAAG,GAAG,GAAG;IACb,KAAKD,CAAC,GAAG,MAAM,EAAEA,CAAC,GAAG,GAAG,EAAEA,CAAC,KAAK,CAAC,EAAE;MACjCC,GAAG,IAAI,IAAI,CAACrB,SAAS,CAACmB,CAAC,GAAG,IAAI,CAAC,GAAGC,CAAC,GAAG,CAAC,GAAG,CAAC;IAC7C;IACA,OAAOC,GAAG,GAAG,IAAI,CAACC,QAAQ,CAACH,CAAC,CAAC;EAC/B,CAAC;EAED;AACF;AACA;AACA;AACA;AACA;EACEI,SAAS,EAAE,mBAASJ,CAAC,EAAE;IACrB;IACA,OAAO,IAAI,CAACnB,SAAS,CAACmB,CAAC,GAAG,IAAI,CAAC,GAAG,GAAG;EACvC,CAAC;EAED;AACF;AACA;AACA;AACA;AACA;EACEG,QAAQ,EAAE,kBAASH,CAAC,EAAE;IACpB,IAAI,IAAI,CAACI,SAAS,CAACJ,CAAC,CAAC,EAAE;MACrB,OAAO,IAAI,CAACnB,SAAS,CAACmB,CAAC,GAAG,IAAI,CAAC,GAAG,OAAO,GAAG,EAAE,GAAG,EAAE;IACrD;IACA,OAAO,CAAC;EACV,CAAC;EAED;AACF;AACA;AACA;AACA;AACA;AACA;EACEK,SAAS,EAAE,mBAASL,CAAC,EAAEM,CAAC,EAAE;IACxB,IAAIA,CAAC,GAAG,EAAE,IAAIA,CAAC,GAAG,CAAC,EAAE;MACnB,OAAO,CAAC,CAAC;IACX,CAAC,CAAC;IACF,OAAO,IAAI,CAACzB,SAAS,CAACmB,CAAC,GAAG,IAAI,CAAC,GAAI,OAAO,IAAIM,CAAE,GAAG,EAAE,GAAG,EAAE;EAC5D,CAAC;EAED;AACF;AACA;AACA;AACA;AACA;AACA;EACEC,SAAS,EAAE,mBAASP,CAAC,EAAEM,CAAC,EAAE;IACxB,IAAIA,CAAC,GAAG,EAAE,IAAIA,CAAC,GAAG,CAAC,EAAE;MACnB,OAAO,CAAC,CAAC;IACX,CAAC,CAAC;IACF,IAAME,EAAE,GAAGF,CAAC,GAAG,CAAC;IAChB,IAAIE,EAAE,KAAK,CAAC,EAAE;MACZ;MACA,OAAQR,CAAC,GAAG,CAAC,KAAK,CAAC,IAAIA,CAAC,GAAG,GAAG,KAAK,CAAC,IAAKA,CAAC,GAAG,GAAG,KAAK,CAAC,GAAG,EAAE,GAAG,EAAE;IAClE,CAAC,MAAM;MACL,OAAO,IAAI,CAAClB,UAAU,CAAC0B,EAAE,CAAC;IAC5B;EACF,CAAC;EAED;AACF;AACA;AACA;AACA;EACEC,YAAY,EAAE,sBAASC,KAAK,EAAE;IAC5B,IAAIC,MAAM,GAAG,CAACD,KAAK,GAAG,CAAC,IAAI,EAAE;IAC7B,IAAIE,MAAM,GAAG,CAACF,KAAK,GAAG,CAAC,IAAI,EAAE;IAC7B,IAAIC,MAAM,KAAK,CAAC,EAAEA,MAAM,GAAG,EAAE,EAAC;IAC9B,IAAIC,MAAM,KAAK,CAAC,EAAEA,MAAM,GAAG,EAAE,EAAC;IAC9B,OAAO,IAAI,CAAC7B,GAAG,CAAC4B,MAAM,GAAG,CAAC,CAAC,GAAG,IAAI,CAAC3B,GAAG,CAAC4B,MAAM,GAAG,CAAC,CAAC;EACpD,CAAC;EAED;AACF;AACA;AACA;AACA;AACA;EACEC,OAAO,EAAE,iBAASC,MAAM,EAAEC,IAAI,EAAE;IAC9B,IAAMC,CAAC,GACL,8JAA8J;IAChK,IAAMC,GAAG,GAAG,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC;IAC5D,OAAOD,CAAC,CAACE,MAAM,CAACJ,MAAM,GAAG,CAAC,IAAIC,IAAI,GAAGE,GAAG,CAACH,MAAM,GAAG,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,CAAC,GAAG,QAAQ,EAAC;EAC/E,CAAC;;EAED;AACF;AACA;AACA;AACA;EACEK,QAAQ,EAAE,kBAASC,MAAM,EAAE;IACzB,OAAO,IAAI,CAACrC,GAAG,CAACqC,MAAM,GAAG,EAAE,CAAC,GAAG,IAAI,CAACpC,GAAG,CAACoC,MAAM,GAAG,EAAE,CAAC;EACtD,CAAC;EAED;AACF;AACA;AACA;AACA;AACA;AACA;EACEC,OAAO,EAAE,iBAASrB,CAAC,EAAEsB,CAAC,EAAE;IACtB,IAAItB,CAAC,GAAG,IAAI,IAAIA,CAAC,GAAG,IAAI,EAAE;MACxB,OAAO,CAAC,CAAC;IACX;IACA,IAAIsB,CAAC,GAAG,CAAC,IAAIA,CAAC,GAAG,EAAE,EAAE;MACnB,OAAO,CAAC,CAAC;IACX;IACA,IAAMC,MAAM,GAAG,IAAI,CAAC5B,SAAS,CAACK,CAAC,GAAG,IAAI,CAAC;IACvC,IAAMwB,KAAK,GAAG,CACZC,QAAQ,CAAC,IAAI,GAAGF,MAAM,CAACL,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAACQ,QAAQ,EAAE,EAC/CD,QAAQ,CAAC,IAAI,GAAGF,MAAM,CAACL,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAACQ,QAAQ,EAAE,EAC/CD,QAAQ,CAAC,IAAI,GAAGF,MAAM,CAACL,MAAM,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAACQ,QAAQ,EAAE,EAChDD,QAAQ,CAAC,IAAI,GAAGF,MAAM,CAACL,MAAM,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAACQ,QAAQ,EAAE,EAChDD,QAAQ,CAAC,IAAI,GAAGF,MAAM,CAACL,MAAM,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAACQ,QAAQ,EAAE,EAChDD,QAAQ,CAAC,IAAI,GAAGF,MAAM,CAACL,MAAM,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAACQ,QAAQ,EAAE,CACjD;IACD,IAAMC,QAAQ,GAAG,CACfH,KAAK,CAAC,CAAC,CAAC,CAACN,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC,EACrBM,KAAK,CAAC,CAAC,CAAC,CAACN,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC,EACrBM,KAAK,CAAC,CAAC,CAAC,CAACN,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC,EACrBM,KAAK,CAAC,CAAC,CAAC,CAACN,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC,EAErBM,KAAK,CAAC,CAAC,CAAC,CAACN,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC,EACrBM,KAAK,CAAC,CAAC,CAAC,CAACN,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC,EACrBM,KAAK,CAAC,CAAC,CAAC,CAACN,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC,EACrBM,KAAK,CAAC,CAAC,CAAC,CAACN,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC,EAErBM,KAAK,CAAC,CAAC,CAAC,CAACN,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC,EACrBM,KAAK,CAAC,CAAC,CAAC,CAACN,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC,EACrBM,KAAK,CAAC,CAAC,CAAC,CAACN,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC,EACrBM,KAAK,CAAC,CAAC,CAAC,CAACN,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC,EAErBM,KAAK,CAAC,CAAC,CAAC,CAACN,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC,EACrBM,KAAK,CAAC,CAAC,CAAC,CAACN,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC,EACrBM,KAAK,CAAC,CAAC,CAAC,CAACN,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC,EACrBM,KAAK,CAAC,CAAC,CAAC,CAACN,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC,EAErBM,KAAK,CAAC,CAAC,CAAC,CAACN,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC,EACrBM,KAAK,CAAC,CAAC,CAAC,CAACN,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC,EACrBM,KAAK,CAAC,CAAC,CAAC,CAACN,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC,EACrBM,KAAK,CAAC,CAAC,CAAC,CAACN,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC,EAErBM,KAAK,CAAC,CAAC,CAAC,CAACN,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC,EACrBM,KAAK,CAAC,CAAC,CAAC,CAACN,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC,EACrBM,KAAK,CAAC,CAAC,CAAC,CAACN,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC,EACrBM,KAAK,CAAC,CAAC,CAAC,CAACN,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC,CACtB;IACD,OAAOO,QAAQ,CAACE,QAAQ,CAACL,CAAC,GAAG,CAAC,CAAC,CAAC;EAClC,CAAC;EAED;AACF;AACA;AACA;AACA;AACA;EACEM,YAAY,EAAE,sBAAStB,CAAC,EAAE;IACxB;IACA,IAAIA,CAAC,GAAG,EAAE,IAAIA,CAAC,GAAG,CAAC,EAAE;MACnB,OAAO,CAAC,CAAC;IACX,CAAC,CAAC;IACF,IAAIU,CAAC,GAAG,IAAI,CAAClB,KAAK,CAACQ,CAAC,GAAG,CAAC,CAAC;IACzBU,CAAC,IAAI,QAAQ,EAAC;IACd,OAAOA,CAAC;EACV,CAAC;EAED;AACF;AACA;AACA;AACA;AACA;EACEa,UAAU,EAAE,oBAASC,CAAC,EAAE;IACtB;IACA,IAAId,CAAC;IACL,QAAQc,CAAC;MACP,KAAK,EAAE;QACLd,CAAC,GAAG,cAAc;QAClB;MACF,KAAK,EAAE;QACLA,CAAC,GAAG,cAAc;QAClB;MACF,KAAK,EAAE;QACLA,CAAC,GAAG,cAAc;QAClB;MACF;QACEA,CAAC,GAAG,IAAI,CAACnB,KAAK,CAACkC,IAAI,CAACC,KAAK,CAACF,CAAC,GAAG,EAAE,CAAC,CAAC;QAClCd,CAAC,IAAI,IAAI,CAACpB,KAAK,CAACkC,CAAC,GAAG,EAAE,CAAC;IAAA;IAE3B,OAAOd,CAAC;EACV,CAAC;EAED;AACF;AACA;AACA;AACA;AACA;EACEiB,SAAS,EAAE,mBAASjC,CAAC,EAAE;IACrB,OAAO,IAAI,CAACf,OAAO,CAAC,CAACe,CAAC,GAAG,CAAC,IAAI,EAAE,CAAC;EACnC,CAAC;EAED;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACEkC,WAAW,EAAE,qBAASC,KAAK,EAAEC,KAAK,EAAEC,KAAK,EAAE;IACzC,IAAIrC,CAAC,GAAGyB,QAAQ,CAACU,KAAK,CAAC;IACvB,IAAI7B,CAAC,GAAGmB,QAAQ,CAACW,KAAK,CAAC;IACvB,IAAIN,CAAC,GAAGL,QAAQ,CAACY,KAAK,CAAC;IACvB;IACA,IAAIrC,CAAC,GAAG,IAAI,IAAIA,CAAC,GAAG,IAAI,EAAE;MACxB,OAAO,CAAC,CAAC,EAAC;IACZ;IACA;IACA,IAAIA,CAAC,KAAK,IAAI,IAAIM,CAAC,KAAK,CAAC,IAAIwB,CAAC,GAAG,EAAE,EAAE;MACnC,OAAO,CAAC,CAAC;IACX;;IAEA;IACA,IAAIQ,OAAO;IACX,IAAI,CAACtC,CAAC,EAAE;MACNsC,OAAO,GAAG,IAAIC,IAAI,EAAE;IACtB,CAAC,MAAM;MACLD,OAAO,GAAG,IAAIC,IAAI,CAACvC,CAAC,EAAEyB,QAAQ,CAACnB,CAAC,CAAC,GAAG,CAAC,EAAEwB,CAAC,CAAC;IAC3C;IACA,IAAI7B,CAAC;IACL,IAAIuC,IAAI,GAAG,CAAC;IACZ,IAAIC,IAAI,GAAG,CAAC;IACZ;IACAzC,CAAC,GAAGsC,OAAO,CAACI,WAAW,EAAE;IACzBpC,CAAC,GAAGgC,OAAO,CAACK,QAAQ,EAAE,GAAG,CAAC;IAC1Bb,CAAC,GAAGQ,OAAO,CAACM,OAAO,EAAE;IACrB,IAAIxB,MAAM,GACR,CAACmB,IAAI,CAACM,GAAG,CAACP,OAAO,CAACI,WAAW,EAAE,EAAEJ,OAAO,CAACK,QAAQ,EAAE,EAAEL,OAAO,CAACM,OAAO,EAAE,CAAC,GACrEL,IAAI,CAACM,GAAG,CAAC,IAAI,EAAE,CAAC,EAAE,EAAE,CAAC,IACvB,QAAQ;IACV,KAAK5C,CAAC,GAAG,IAAI,EAAEA,CAAC,GAAG,IAAI,IAAImB,MAAM,GAAG,CAAC,EAAEnB,CAAC,EAAE,EAAE;MAC1CwC,IAAI,GAAG,IAAI,CAAC1C,SAAS,CAACE,CAAC,CAAC;MACxBmB,MAAM,IAAIqB,IAAI;IAChB;IACA,IAAIrB,MAAM,GAAG,CAAC,EAAE;MACdA,MAAM,IAAIqB,IAAI;MACdxC,CAAC,EAAE;IACL;;IAEA;IACA,IAAM6C,UAAU,GAAG,IAAIP,IAAI,EAAE;IAC7B,IAAIQ,OAAO,GAAG,KAAK;IACnB,IACED,UAAU,CAACJ,WAAW,EAAE,KAAK1C,CAAC,IAC9B8C,UAAU,CAACH,QAAQ,EAAE,GAAG,CAAC,KAAKrC,CAAC,IAC/BwC,UAAU,CAACF,OAAO,EAAE,KAAKd,CAAC,EAC1B;MACAiB,OAAO,GAAG,IAAI;IAChB;IACA;IACA,IAAIC,KAAK,GAAGV,OAAO,CAACW,MAAM,EAAE;IAC5B,IAAMC,KAAK,GAAG,IAAI,CAACtD,KAAK,CAACoD,KAAK,CAAC;IAC/B;IACA,IAAIA,KAAK,KAAK,CAAC,EAAE;MACfA,KAAK,GAAG,CAAC;IACX;IACA;IACA,IAAMG,IAAI,GAAGlD,CAAC;IACduC,IAAI,GAAG,IAAI,CAACpC,SAAS,CAACH,CAAC,CAAC,EAAC;IACzB,IAAImD,MAAM,GAAG,KAAK;;IAElB;IACA,KAAKnD,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,EAAE,IAAImB,MAAM,GAAG,CAAC,EAAEnB,CAAC,EAAE,EAAE;MACrC;MACA,IAAIuC,IAAI,GAAG,CAAC,IAAIvC,CAAC,KAAKuC,IAAI,GAAG,CAAC,IAAIY,MAAM,KAAK,KAAK,EAAE;QAClD,EAAEnD,CAAC;QACHmD,MAAM,GAAG,IAAI;QACbX,IAAI,GAAG,IAAI,CAACtC,QAAQ,CAACgD,IAAI,CAAC,EAAC;MAC7B,CAAC,MAAM;QACLV,IAAI,GAAG,IAAI,CAACpC,SAAS,CAAC8C,IAAI,EAAElD,CAAC,CAAC,EAAC;MACjC;MACA;MACA,IAAImD,MAAM,KAAK,IAAI,IAAInD,CAAC,KAAKuC,IAAI,GAAG,CAAC,EAAE;QACrCY,MAAM,GAAG,KAAK;MAChB;MACAhC,MAAM,IAAIqB,IAAI;IAChB;IACA;IACA,IAAIrB,MAAM,KAAK,CAAC,IAAIoB,IAAI,GAAG,CAAC,IAAIvC,CAAC,KAAKuC,IAAI,GAAG,CAAC,EAAE;MAC9C,IAAIY,MAAM,EAAE;QACVA,MAAM,GAAG,KAAK;MAChB,CAAC,MAAM;QACLA,MAAM,GAAG,IAAI;QACb,EAAEnD,CAAC;MACL;IACF;IACA,IAAImB,MAAM,GAAG,CAAC,EAAE;MACdA,MAAM,IAAIqB,IAAI;MACd,EAAExC,CAAC;IACL;IACA;IACA,IAAMoD,KAAK,GAAGpD,CAAC;IACf;IACA,IAAMqD,GAAG,GAAGlC,MAAM,GAAG,CAAC;IACtB;IACA,IAAMmC,EAAE,GAAGjD,CAAC,GAAG,CAAC;IAChB,IAAMkD,GAAG,GAAG,IAAI,CAAC/C,YAAY,CAAC0C,IAAI,CAAC;;IAEnC;IACA;IACA,IAAMM,SAAS,GAAG,IAAI,CAACpC,OAAO,CAACrB,CAAC,EAAEM,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,EAAC;IAC7C,IAAMoD,UAAU,GAAG,IAAI,CAACrC,OAAO,CAACrB,CAAC,EAAEM,CAAC,GAAG,CAAC,CAAC,EAAC;;IAE1C;IACA,IAAIqD,GAAG,GAAG,IAAI,CAACxC,QAAQ,CAAC,CAACnB,CAAC,GAAG,IAAI,IAAI,EAAE,GAAGM,CAAC,GAAG,EAAE,CAAC;IACjD,IAAIwB,CAAC,IAAI2B,SAAS,EAAE;MAClBE,GAAG,GAAG,IAAI,CAACxC,QAAQ,CAAC,CAACnB,CAAC,GAAG,IAAI,IAAI,EAAE,GAAGM,CAAC,GAAG,EAAE,CAAC;IAC/C;;IAEA;IACA,IAAIsD,MAAM,GAAG,KAAK;IAClB,IAAIC,IAAI,GAAG,IAAI;IACf,IAAIJ,SAAS,KAAK3B,CAAC,EAAE;MACnB8B,MAAM,GAAG,IAAI;MACbC,IAAI,GAAG,IAAI,CAACnE,SAAS,CAACY,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;IAClC;IACA,IAAIoD,UAAU,KAAK5B,CAAC,EAAE;MACpB8B,MAAM,GAAG,IAAI;MACbC,IAAI,GAAG,IAAI,CAACnE,SAAS,CAACY,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;IAClC;IACA;IACA,IAAMwD,WAAW,GAAGvB,IAAI,CAACM,GAAG,CAAC7C,CAAC,EAAEuD,EAAE,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,GAAG,QAAQ,GAAG,KAAK,GAAG,EAAE;IAC1E,IAAMQ,GAAG,GAAG,IAAI,CAAC5C,QAAQ,CAAC2C,WAAW,GAAGhC,CAAC,GAAG,CAAC,CAAC;IAC9C;IACA,IAAMkC,KAAK,GAAG,IAAI,CAACnD,OAAO,CAACP,CAAC,EAAEwB,CAAC,CAAC;IAEhC,IAAMmC,SAAS,GAAGjE,CAAC,GAAG,GAAG,GAAGM,CAAC,GAAG,GAAG,GAAGwB,CAAC;IACvC,IAAMoC,SAAS,GAAGf,IAAI,GAAG,GAAG,GAAGE,KAAK,GAAG,GAAG,GAAGC,GAAG;IAEhD,IAAMpE,QAAQ,GAAG,IAAI,CAACA,QAAQ;IAC9B,IAAME,SAAS,GAAG,IAAI,CAACA,SAAS;IAEhC,IAAM+E,YAAY,GAAG7D,CAAC,GAAG,GAAG,GAAGwB,CAAC;IAChC,IAAMsC,iBAAiB,GAAGf,KAAK,GAAG,GAAG,GAAGC,GAAG;IAE3C,OAAO;MACLe,IAAI,EAAEJ,SAAS;MACfC,SAAS,EAAEA,SAAS;MACpBhF,QAAQ,EAAEA,QAAQ,CAACiF,YAAY,CAAC,GAAGjF,QAAQ,CAACiF,YAAY,CAAC,CAAChF,KAAK,GAAG,IAAI;MACtEmF,aAAa,EAAElF,SAAS,CAACgF,iBAAiB,CAAC,GACvChF,SAAS,CAACgF,iBAAiB,CAAC,CAACjF,KAAK,GAClC,IAAI;MACRuB,KAAK,EAAEyC,IAAI;MACXoB,MAAM,EAAElB,KAAK;MACbmB,IAAI,EAAElB,GAAG;MACTmB,MAAM,EAAE,IAAI,CAACxC,SAAS,CAACkB,IAAI,CAAC;MAC5BuB,QAAQ,EAAE,CAACtB,MAAM,GAAG,QAAQ,GAAG,EAAE,IAAI,IAAI,CAACxB,YAAY,CAACyB,KAAK,CAAC;MAC7DsB,MAAM,EAAE,IAAI,CAAC9C,UAAU,CAACyB,GAAG,CAAC;MAC5BsB,KAAK,EAAE5E,CAAC;MACRc,MAAM,EAAER,CAAC;MACTS,IAAI,EAAEe,CAAC;MACP+C,MAAM,EAAErB,GAAG;MACXsB,OAAO,EAAEnB,GAAG;MACZoB,KAAK,EAAEhB,GAAG;MACVhB,OAAO,EAAEA,OAAO;MAChBK,MAAM,EAAEA,MAAM;MACdJ,KAAK,EAAEA,KAAK;MACZgC,MAAM,EAAE,cAAc,GAAG9B,KAAK;MAC9BU,MAAM,EAAEA,MAAM;MACdC,IAAI,EAAEA,IAAI;MACVG,KAAK,EAAEA;IACT,CAAC;EACH,CAAC;EAED;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACEiB,WAAW,EAAE,qBAASjF,CAAC,EAAEM,CAAC,EAAEwB,CAAC,EAAEoD,WAAW,EAAE;IAC1ClF,CAAC,GAAGyB,QAAQ,CAACzB,CAAC,CAAC;IACfM,CAAC,GAAGmB,QAAQ,CAACnB,CAAC,CAAC;IACfwB,CAAC,GAAGL,QAAQ,CAACK,CAAC,CAAC;IACfoD,WAAW,GAAG,CAAC,CAACA,WAAW;IAC3B;IACA,IAAM9E,SAAS,GAAG,IAAI,CAACA,SAAS,CAACJ,CAAC,CAAC;IACnC;IACA,IAAIkF,WAAW,IAAI9E,SAAS,KAAKE,CAAC,EAAE;MAClC,OAAO,CAAC,CAAC;IACX,CAAC,CAAC;IACF,IACGN,CAAC,KAAK,IAAI,IAAIM,CAAC,KAAK,EAAE,IAAIwB,CAAC,GAAG,CAAC,IAC/B9B,CAAC,KAAK,IAAI,IAAIM,CAAC,KAAK,CAAC,IAAIwB,CAAC,GAAG,EAAG,EACjC;MACA,OAAO,CAAC,CAAC;IACX,CAAC,CAAC;IACF,IAAMwB,GAAG,GAAG,IAAI,CAACjD,SAAS,CAACL,CAAC,EAAEM,CAAC,CAAC;IAChC,IAAI6E,IAAI,GAAG7B,GAAG;IACd;IACA;IACA,IAAI4B,WAAW,EAAE;MACfC,IAAI,GAAG,IAAI,CAAChF,QAAQ,CAACH,CAAC,EAAEM,CAAC,CAAC;IAC5B;IACA,IAAIN,CAAC,GAAG,IAAI,IAAIA,CAAC,GAAG,IAAI,IAAI8B,CAAC,GAAGqD,IAAI,EAAE;MACpC,OAAO,CAAC,CAAC;IACX,CAAC,CAAC;;IAEF;IACA,IAAI/D,MAAM,GAAG,CAAC;IACd,IAAInB,CAAC;IACL,KAAKA,CAAC,GAAG,IAAI,EAAEA,CAAC,GAAGD,CAAC,EAAEC,CAAC,EAAE,EAAE;MACzBmB,MAAM,IAAI,IAAI,CAACrB,SAAS,CAACE,CAAC,CAAC;IAC7B;IACA,IAAIuC,IAAI,GAAG,CAAC;IACZ,IAAI4C,KAAK,GAAG,KAAK;IACjB,KAAKnF,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGK,CAAC,EAAEL,CAAC,EAAE,EAAE;MACtBuC,IAAI,GAAG,IAAI,CAACpC,SAAS,CAACJ,CAAC,CAAC;MACxB,IAAI,CAACoF,KAAK,EAAE;QACV;QACA,IAAI5C,IAAI,IAAIvC,CAAC,IAAIuC,IAAI,GAAG,CAAC,EAAE;UACzBpB,MAAM,IAAI,IAAI,CAACjB,QAAQ,CAACH,CAAC,CAAC;UAC1BoF,KAAK,GAAG,IAAI;QACd;MACF;MACAhE,MAAM,IAAI,IAAI,CAACf,SAAS,CAACL,CAAC,EAAEC,CAAC,CAAC;IAChC;IACA;IACA,IAAIiF,WAAW,EAAE;MACf9D,MAAM,IAAIkC,GAAG;IACf;IACA;IACA,IAAM+B,KAAK,GAAG9C,IAAI,CAACM,GAAG,CAAC,IAAI,EAAE,CAAC,EAAE,EAAE,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;IAC5C,IAAMyC,MAAM,GAAG,IAAI/C,IAAI,CAAC,CAACnB,MAAM,GAAGU,CAAC,GAAG,EAAE,IAAI,QAAQ,GAAGuD,KAAK,CAAC;IAC7D,IAAME,EAAE,GAAGD,MAAM,CAACE,cAAc,EAAE;IAClC,IAAMC,EAAE,GAAGH,MAAM,CAACI,WAAW,EAAE,GAAG,CAAC;IACnC,IAAMC,EAAE,GAAGL,MAAM,CAACM,UAAU,EAAE;IAE9B,OAAO,IAAI,CAAC1D,WAAW,CAACqD,EAAE,EAAEE,EAAE,EAAEE,EAAE,CAAC;EACrC;AACF,CAAC;AAED,eAAe/G,QAAQ"}]}