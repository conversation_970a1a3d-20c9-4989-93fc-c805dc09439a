{"remainingRequest": "E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-q<PERSON><PERSON>n\\数字运营平台-统一门户工程\\dopUnify\\node_modules\\babel-loader\\lib\\index.js!E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan\\数字运营平台-统一门户工程\\dopUnify\\node_modules\\eslint-loader\\index.js??ref--13-0!E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan\\数字运营平台-统一门户工程\\dopUnify\\src\\views\\system\\config\\external\\info.js", "dependencies": [{"path": "E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-<PERSON><PERSON><PERSON>n\\数字运营平台-统一门户工程\\dopUnify\\src\\views\\system\\config\\external\\info.js", "mtime": 1686019808044}, {"path": "E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-<PERSON><PERSON><PERSON>n\\数字运营平台-统一门户工程\\dopUnify\\babel.config.js", "mtime": 1686019818359}, {"path": "E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-<PERSON><PERSON><PERSON>n\\数字运营平台-统一门户工程\\dopUnify\\.babelrc", "mtime": 1705285078668}, {"path": "E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-<PERSON><PERSON><PERSON>n\\数字运营平台-统一门户工程\\dopUnify\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-<PERSON><PERSON><PERSON>n\\数字运营平台-统一门户工程\\dopUnify\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-<PERSON><PERSON><PERSON>n\\数字运营平台-统一门户工程\\dopUnify\\node_modules\\eslint-loader\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["dictionaryFieds", "config", "that", "start_date", "component", "label", "colSpan", "name", "componentProps", "clearable", "valueFormat", "placeholder", "end_date", "source_id", "source_name", "trans_modul", "options", "impl_class"], "sources": ["E:/1_Project/XYD_Project/dop-4.0/dop-4.1-qianduan/数字运营平台-统一门户工程/dopUnify/src/views/system/config/external/info.js"], "sourcesContent": ["import { dictionaryFieds } from '@/utils/dictionary' // 字典配置\r\n// 表单\r\nexport const config = (that) => ({\r\n  // date_two: {\r\n  //   component: 'date-picker',\r\n  //   label: '日期区间',\r\n  //   colSpan: 8,\r\n  //   name: 'date_two',\r\n  //   // config: {},\r\n  //   componentProps: {\r\n  //     type: 'daterange',\r\n  //     rangeSeparator: '-',\r\n  //     startPlaceholder: '开始日期',\r\n  //     endPlaceholder: '结束日期',\r\n  //     valueFormat: 'yyyyMMdd',\r\n  //     editable: false\r\n  //     // type: 'year'\r\n  //   }\r\n  // },\r\n  start_date: {\r\n    component: 'date-picker',\r\n    label: '开始日期',\r\n    colSpan: 8,\r\n    name: 'start_date',\r\n    componentProps: {\r\n      // input组件配置\r\n      clearable: true,\r\n      valueFormat: 'yyyyMMdd',\r\n      placeholder: '开始日期'\r\n    },\r\n    config: {}\r\n  },\r\n  end_date: {\r\n    component: 'date-picker',\r\n    label: '结束日期',\r\n    colSpan: 8,\r\n    name: 'end_date',\r\n    componentProps: {\r\n      // input组件配置\r\n      clearable: true,\r\n      valueFormat: 'yyyyMMdd',\r\n      placeholder: '结束日期'\r\n    },\r\n    config: {}\r\n  },\r\n  source_id: {\r\n    component: 'input',\r\n    label: '数据源id',\r\n    colSpan: 8,\r\n    name: 'source_id',\r\n    config: {},\r\n    componentProps: {\r\n      placeholder: '支持数据源id模糊查询',\r\n      clearable: true\r\n    }\r\n  },\r\n  source_name: {\r\n    component: 'input',\r\n    label: '数据源名称',\r\n    colSpan: 8,\r\n    name: 'source_name',\r\n    config: {},\r\n    componentProps: {\r\n      placeholder: '支持数据源名称模糊查询',\r\n      clearable: true\r\n    }\r\n  },\r\n  trans_modul: {\r\n    component: 'select',\r\n    label: '所属模块',\r\n    colSpan: 8,\r\n    name: 'trans_modul',\r\n    config: {},\r\n    componentProps: {\r\n      placeholder: '请选择',\r\n      clearable: true\r\n    },\r\n    options: dictionaryFieds('SERVICE_MODULE')\r\n  },\r\n  impl_class: {\r\n    component: 'input',\r\n    label: '调用类路径',\r\n    colSpan: 8,\r\n    name: 'impl_class',\r\n    config: {},\r\n    componentProps: {\r\n      placeholder: '支持调用类路径模糊查询',\r\n      clearable: true\r\n    }\r\n  }\r\n})\r\n"], "mappings": "AAAA,SAASA,eAAe,QAAQ,oBAAoB,EAAC;AACrD;AACA,OAAO,IAAMC,MAAM,GAAG,SAATA,MAAM,CAAIC,IAAI;EAAA,OAAM;IAC/B;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACAC,UAAU,EAAE;MACVC,SAAS,EAAE,aAAa;MACxBC,KAAK,EAAE,MAAM;MACbC,OAAO,EAAE,CAAC;MACVC,IAAI,EAAE,YAAY;MAClBC,cAAc,EAAE;QACd;QACAC,SAAS,EAAE,IAAI;QACfC,WAAW,EAAE,UAAU;QACvBC,WAAW,EAAE;MACf,CAAC;MACDV,MAAM,EAAE,CAAC;IACX,CAAC;IACDW,QAAQ,EAAE;MACRR,SAAS,EAAE,aAAa;MACxBC,KAAK,EAAE,MAAM;MACbC,OAAO,EAAE,CAAC;MACVC,IAAI,EAAE,UAAU;MAChBC,cAAc,EAAE;QACd;QACAC,SAAS,EAAE,IAAI;QACfC,WAAW,EAAE,UAAU;QACvBC,WAAW,EAAE;MACf,CAAC;MACDV,MAAM,EAAE,CAAC;IACX,CAAC;IACDY,SAAS,EAAE;MACTT,SAAS,EAAE,OAAO;MAClBC,KAAK,EAAE,OAAO;MACdC,OAAO,EAAE,CAAC;MACVC,IAAI,EAAE,WAAW;MACjBN,MAAM,EAAE,CAAC,CAAC;MACVO,cAAc,EAAE;QACdG,WAAW,EAAE,aAAa;QAC1BF,SAAS,EAAE;MACb;IACF,CAAC;IACDK,WAAW,EAAE;MACXV,SAAS,EAAE,OAAO;MAClBC,KAAK,EAAE,OAAO;MACdC,OAAO,EAAE,CAAC;MACVC,IAAI,EAAE,aAAa;MACnBN,MAAM,EAAE,CAAC,CAAC;MACVO,cAAc,EAAE;QACdG,WAAW,EAAE,aAAa;QAC1BF,SAAS,EAAE;MACb;IACF,CAAC;IACDM,WAAW,EAAE;MACXX,SAAS,EAAE,QAAQ;MACnBC,KAAK,EAAE,MAAM;MACbC,OAAO,EAAE,CAAC;MACVC,IAAI,EAAE,aAAa;MACnBN,MAAM,EAAE,CAAC,CAAC;MACVO,cAAc,EAAE;QACdG,WAAW,EAAE,KAAK;QAClBF,SAAS,EAAE;MACb,CAAC;MACDO,OAAO,EAAEhB,eAAe,CAAC,gBAAgB;IAC3C,CAAC;IACDiB,UAAU,EAAE;MACVb,SAAS,EAAE,OAAO;MAClBC,KAAK,EAAE,OAAO;MACdC,OAAO,EAAE,CAAC;MACVC,IAAI,EAAE,YAAY;MAClBN,MAAM,EAAE,CAAC,CAAC;MACVO,cAAc,EAAE;QACdG,WAAW,EAAE,aAAa;QAC1BF,SAAS,EAAE;MACb;IACF;EACF,CAAC;AAAA,CAAC"}]}