{"remainingRequest": "E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1\\数字运营平台-统一门户工程\\dopUnify\\node_modules\\babel-loader\\lib\\index.js!E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1\\数字运营平台-统一门户工程\\dopUnify\\node_modules\\eslint-loader\\index.js??ref--13-0!E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1\\数字运营平台-统一门户工程\\dopUnify\\src\\views\\system\\externalManage\\external\\info.js", "dependencies": [{"path": "E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1\\数字运营平台-统一门户工程\\dopUnify\\src\\views\\system\\externalManage\\external\\info.js", "mtime": 1716875178422}, {"path": "E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1\\数字运营平台-统一门户工程\\dopUnify\\babel.config.js", "mtime": 1716875191548}, {"path": "E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1\\数字运营平台-统一门户工程\\dopUnify\\.babelrc", "mtime": 1716875191502}, {"path": "E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1\\数字运营平台-统一门户工程\\dopUnify\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1\\数字运营平台-统一门户工程\\dopUnify\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1\\数字运营平台-统一门户工程\\dopUnify\\node_modules\\eslint-loader\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8gaW1wb3J0IHsgZGljdGlvbmFyeUZpZWRzIH0gZnJvbSAnQC91dGlscy9kaWN0aW9uYXJ5JyAvLyDlrZflhbgKLy8g6KGo5Y2VCmV4cG9ydCB2YXIgY29uZmlnID0gZnVuY3Rpb24gY29uZmlnKHRoYXQpIHsKICByZXR1cm4gewogICAgb3JnYW5fbm86IHsKICAgICAgY29tcG9uZW50OiAnc2VsZWN0LXRyZWUnLAogICAgICBsYWJlbDogJ+acuuaehOWPtycsCiAgICAgIGNvbFNwYW46IDgsCiAgICAgIG5hbWU6ICdvcmdhbl9ubycsCiAgICAgIGNvbXBvbmVudFByb3BzOiB7CiAgICAgICAgLy8gaW5wdXTnu4Tku7bphY3nva4KICAgICAgICBjbGVhcmFibGU6IHRydWUKICAgICAgfSwKICAgICAgb3B0aW9uczogW10KICAgIH0sCiAgICB1c2VyX25vOiB7CiAgICAgIGNvbXBvbmVudDogJ2lucHV0JywKICAgICAgbGFiZWw6ICfnlKjmiLfnvJblj7cnLAogICAgICBjb2xTcGFuOiA4LAogICAgICBuYW1lOiAndXNlcl9ubycsCiAgICAgIGNvbmZpZzogewogICAgICAgIHJ1bGVzOiBbewogICAgICAgICAgbWluOiAwLAogICAgICAgICAgbWF4OiAxNTAsCiAgICAgICAgICBtZXNzYWdlOiAn6K+35pyA5aSa5aGr5YaZMTUw5Liq5a2X56ymJwogICAgICAgIH1dCiAgICAgIH0sCiAgICAgIGNvbXBvbmVudFByb3BzOiB7CiAgICAgICAgcGxhY2Vob2xkZXI6ICfmlK/mjIHmjInnlKjmiLfnvJblj7fmqKHns4rmn6Xor6InLAogICAgICAgIGZpbHRlcmFibGU6IHRydWUsCiAgICAgICAgY2xlYXJhYmxlOiB0cnVlCiAgICAgIH0KICAgIH0sCiAgICBleHRlcm5hbF9zeXN0ZW1fbm86IHsKICAgICAgY29tcG9uZW50OiAnc2VsZWN0JywKICAgICAgbGFiZWw6ICfns7vnu5/nvJblj7cnLAogICAgICBjb2xTcGFuOiA4LAogICAgICBuYW1lOiAnZXh0ZXJuYWxfc3lzdGVtX25vJywKICAgICAgY29uZmlnOiB7CiAgICAgICAgLy8gZm9ybS1pdGVtIOmFjee9rgogICAgICB9LAogICAgICBjb21wb25lbnRQcm9wczogewogICAgICAgIC8vIGlucHV057uE5Lu26YWN572uCiAgICAgICAgcGxhY2Vob2xkZXI6ICfor7fpgInmi6knLAogICAgICAgIGNsZWFyYWJsZTogdHJ1ZQogICAgICB9LAogICAgICBvcHRpb25zOiBbXQogICAgfSwKICAgIGxhc3RfdGltZTogewogICAgICBjb21wb25lbnQ6ICdkYXRlLXBpY2tlcicsCiAgICAgIGxhYmVsOiAn5pyA5ZCO5L+u5pS55pe26Ze0JywKICAgICAgY29sU3BhbjogOCwKICAgICAgbmFtZTogJ2xhc3RfdGltZScsCiAgICAgIGNvbmZpZzoge30sCiAgICAgIGNvbXBvbmVudFByb3BzOiB7CiAgICAgICAgZmlsdGVyYWJsZTogdHJ1ZSwKICAgICAgICB0eXBlOiAnZGF0ZXJhbmdlJywKICAgICAgICBjbGVhcmFibGU6IHRydWUsCiAgICAgICAgc3RhcnRQbGFjZWhvbGRlcjogJ+W8gOWni+aXpeacnycsCiAgICAgICAgZW5kUGxhY2Vob2xkZXI6ICfnu5PmnZ/ml6XmnJ8nLAogICAgICAgIHZhbHVlRm9ybWF0OiAneXl5eU1NZGQnCiAgICAgIH0KICAgIH0KICB9Owp9Ow=="}, {"version": 3, "names": ["config", "that", "organ_no", "component", "label", "colSpan", "name", "componentProps", "clearable", "options", "user_no", "rules", "min", "max", "message", "placeholder", "filterable", "external_system_no", "last_time", "type", "startPlaceholder", "endPlaceholder", "valueFormat"], "sources": ["E:/1_Project/XYD_Project/dop-4.0/dop-4.1/数字运营平台-统一门户工程/dopUnify/src/views/system/externalManage/external/info.js"], "sourcesContent": ["// import { dictionaryFieds } from '@/utils/dictionary' // 字典\r\n// 表单\r\nexport const config = (that) => ({\r\n  organ_no: {\r\n    component: 'select-tree',\r\n    label: '机构号',\r\n    colSpan: 8,\r\n    name: 'organ_no',\r\n    componentProps: {\r\n      // input组件配置\r\n      clearable: true\r\n    },\r\n    options: []\r\n  },\r\n  user_no: {\r\n    component: 'input',\r\n    label: '用户编号',\r\n    colSpan: 8,\r\n    name: 'user_no',\r\n    config: {\r\n      rules: [\r\n        { min: 0, max: 150, message: '请最多填写150个字符' }\r\n      ]\r\n    },\r\n    componentProps: {\r\n      placeholder: '支持按用户编号模糊查询',\r\n      filterable: true,\r\n      clearable: true\r\n    }\r\n  },\r\n  external_system_no: {\r\n    component: 'select',\r\n    label: '系统编号',\r\n    colSpan: 8,\r\n    name: 'external_system_no',\r\n    config: {\r\n      // form-item 配置\r\n    },\r\n    componentProps: {\r\n      // input组件配置\r\n      placeholder: '请选择',\r\n      clearable: true\r\n    },\r\n    options: []\r\n  },\r\n  last_time: {\r\n    component: 'date-picker',\r\n    label: '最后修改时间',\r\n    colSpan: 8,\r\n    name: 'last_time',\r\n    config: {},\r\n    componentProps: {\r\n      filterable: true,\r\n      type: 'daterange',\r\n      clearable: true,\r\n      startPlaceholder: '开始日期',\r\n      endPlaceholder: '结束日期',\r\n      valueFormat: 'yyyyMMdd'\r\n    }\r\n  }\r\n})\r\n"], "mappings": "AAAA;AACA;AACA,OAAO,IAAMA,MAAM,GAAG,SAATA,MAAM,CAAIC,IAAI;EAAA,OAAM;IAC/BC,QAAQ,EAAE;MACRC,SAAS,EAAE,aAAa;MACxBC,KAAK,EAAE,KAAK;MACZC,OAAO,EAAE,CAAC;MACVC,IAAI,EAAE,UAAU;MAChBC,cAAc,EAAE;QACd;QACAC,SAAS,EAAE;MACb,CAAC;MACDC,OAAO,EAAE;IACX,CAAC;IACDC,OAAO,EAAE;MACPP,SAAS,EAAE,OAAO;MAClBC,KAAK,EAAE,MAAM;MACbC,OAAO,EAAE,CAAC;MACVC,IAAI,EAAE,SAAS;MACfN,MAAM,EAAE;QACNW,KAAK,EAAE,CACL;UAAEC,GAAG,EAAE,CAAC;UAAEC,GAAG,EAAE,GAAG;UAAEC,OAAO,EAAE;QAAc,CAAC;MAEhD,CAAC;MACDP,cAAc,EAAE;QACdQ,WAAW,EAAE,aAAa;QAC1BC,UAAU,EAAE,IAAI;QAChBR,SAAS,EAAE;MACb;IACF,CAAC;IACDS,kBAAkB,EAAE;MAClBd,SAAS,EAAE,QAAQ;MACnBC,KAAK,EAAE,MAAM;MACbC,OAAO,EAAE,CAAC;MACVC,IAAI,EAAE,oBAAoB;MAC1BN,MAAM,EAAE;QACN;MAAA,CACD;MACDO,cAAc,EAAE;QACd;QACAQ,WAAW,EAAE,KAAK;QAClBP,SAAS,EAAE;MACb,CAAC;MACDC,OAAO,EAAE;IACX,CAAC;IACDS,SAAS,EAAE;MACTf,SAAS,EAAE,aAAa;MACxBC,KAAK,EAAE,QAAQ;MACfC,OAAO,EAAE,CAAC;MACVC,IAAI,EAAE,WAAW;MACjBN,MAAM,EAAE,CAAC,CAAC;MACVO,cAAc,EAAE;QACdS,UAAU,EAAE,IAAI;QAChBG,IAAI,EAAE,WAAW;QACjBX,SAAS,EAAE,IAAI;QACfY,gBAAgB,EAAE,MAAM;QACxBC,cAAc,EAAE,MAAM;QACtBC,WAAW,EAAE;MACf;IACF;EACF,CAAC;AAAA,CAAC"}]}