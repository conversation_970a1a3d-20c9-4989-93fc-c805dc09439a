{"remainingRequest": "E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1\\数字运营平台-统一门户工程\\dopUnify\\node_modules\\babel-loader\\lib\\index.js!E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1\\数字运营平台-统一门户工程\\dopUnify\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1\\数字运营平台-统一门户工程\\dopUnify\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1\\数字运营平台-统一门户工程\\dopUnify\\src\\views\\system\\notice\\approval\\component\\table\\index.vue?vue&type=script&lang=js&", "dependencies": [{"path": "E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1\\数字运营平台-统一门户工程\\dopUnify\\src\\views\\system\\notice\\approval\\component\\table\\index.vue", "mtime": 1716875178192}, {"path": "E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1\\数字运营平台-统一门户工程\\dopUnify\\babel.config.js", "mtime": 1716875191548}, {"path": "E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1\\数字运营平台-统一门户工程\\dopUnify\\.babelrc", "mtime": 1716875191502}, {"path": "E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1\\数字运营平台-统一门户工程\\dopUnify\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1\\数字运营平台-统一门户工程\\dopUnify\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1\\数字运营平台-统一门户工程\\dopUnify\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1\\数字运营平台-统一门户工程\\dopUnify\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1655715099000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, null]}