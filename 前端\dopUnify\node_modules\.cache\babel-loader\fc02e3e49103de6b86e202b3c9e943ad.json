{"remainingRequest": "E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-q<PERSON><PERSON>n\\数字运营平台-统一门户工程\\dopUnify\\node_modules\\thread-loader\\dist\\cjs.js!E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan\\数字运营平台-统一门户工程\\dopUnify\\node_modules\\babel-loader\\lib\\index.js!E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan\\数字运营平台-统一门户工程\\dopUnify\\src\\store\\modules\\user.js", "dependencies": [{"path": "E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-<PERSON><PERSON><PERSON>n\\数字运营平台-统一门户工程\\dopUnify\\src\\store\\modules\\user.js", "mtime": 1705301835580}, {"path": "E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-<PERSON><PERSON><PERSON>n\\数字运营平台-统一门户工程\\dopUnify\\babel.config.js", "mtime": 1686019818359}, {"path": "E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-<PERSON><PERSON><PERSON>n\\数字运营平台-统一门户工程\\dopUnify\\.babelrc", "mtime": 1705285078668}, {"path": "E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-<PERSON><PERSON><PERSON>n\\数字运营平台-统一门户工程\\dopUnify\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-<PERSON><PERSON><PERSON>n\\数字运营平台-统一门户工程\\dopUnify\\node_modules\\thread-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-<PERSON><PERSON><PERSON>n\\数字运营平台-统一门户工程\\dopUnify\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:aW1wb3J0IF9jcmVhdGVGb3JPZkl0ZXJhdG9ySGVscGVyIGZyb20gIkU6LzFfUHJvamVjdC9YWURfUHJvamVjdC9kb3AtNC4wL2RvcC00LjEtcWlhbmR1YW4vXHU2NTcwXHU1QjU3XHU4RkQwXHU4NDI1XHU1RTczXHU1M0YwLVx1N0VERlx1NEUwMFx1OTVFOFx1NjIzN1x1NURFNVx1N0EwQi9kb3BVbmlmeS9ub2RlX21vZHVsZXMvQGJhYmVsL3J1bnRpbWUvaGVscGVycy9lc20vY3JlYXRlRm9yT2ZJdGVyYXRvckhlbHBlci5qcyI7CmltcG9ydCBfb2JqZWN0U3ByZWFkIGZyb20gIkU6LzFfUHJvamVjdC9YWURfUHJvamVjdC9kb3AtNC4wL2RvcC00LjEtcWlhbmR1YW4vXHU2NTcwXHU1QjU3XHU4RkQwXHU4NDI1XHU1RTczXHU1M0YwLVx1N0VERlx1NEUwMFx1OTVFOFx1NjIzN1x1NURFNVx1N0EwQi9kb3BVbmlmeS9ub2RlX21vZHVsZXMvQGJhYmVsL3J1bnRpbWUvaGVscGVycy9lc20vb2JqZWN0U3ByZWFkMi5qcyI7CmltcG9ydCAiY29yZS1qcy9tb2R1bGVzL2VzLnN0cmluZy50cmltLmpzIjsKaW1wb3J0ICJjb3JlLWpzL21vZHVsZXMvZXMub2JqZWN0LnRvLXN0cmluZy5qcyI7CmltcG9ydCB7IExvZ2luLCBDb21tb24gfSBmcm9tICdAL2FwaSc7CmltcG9ydCB7IGdldFRva2VuLCBzZXRUb2tlbiwgcmVtb3ZlVG9rZW4gfSBmcm9tICdAL3V0aWxzL2F1dGgnOwppbXBvcnQgeyBlbmNyeXB0UmVzdWx0IH0gZnJvbSAnQC91dGlscy9jcnlwdG8nOwppbXBvcnQgeyByZXNldFJvdXRlciB9IGZyb20gJ0Avcm91dGVyJzsKaW1wb3J0IHsgZ2V0VGhlbWVDb2xvciB9IGZyb20gJ0AvdXRpbHMvY29tbW9uJzsKaW1wb3J0IGRlZmF1bHRTZXR0aW5ncyBmcm9tICdAL3NldHRpbmdzJzsKdmFyIHF1ZXJ5ID0gQ29tbW9uLlN5c0F1ZGl0LnF1ZXJ5OwovLyDnmbvlvZUKdmFyIF9sb2dpbiA9IExvZ2luLmxvZ2luLAogIF9sb2dvdXQgPSBMb2dpbi5sb2dvdXQ7CnZhciBzdGF0ZSA9IHsKICBlbXB0eVBzd2Q6ICcnLAogIHRva2VuOiBnZXRUb2tlbigpLAogIHVzZXJObzogJycsCiAgdXNlckxldmVsOiAnJywKICBhdmF0YXI6ICcnLAogIGludHJvZHVjdGlvbjogJycsCiAgdXNlck5hbWU6ICcnLAogIHJvbGVObzogJycsCiAgcm9sZXM6IFtdLAogIG9yZ2FuTm86ICcnLAogIC8vIOacuuaehOWPtwogIG9yZ2FuTGV2ZWw6ICcnLAogIC8vIOacuuaehOe6p+WIqwogIGxvZ2luSW5mbzoge30sCiAgLy8g55m75YWl5L+h5oGvCiAgYnRuQWxsOiAnJywKICAvLyDpobXpnaLmjInpkq7mnYPpmZAKICByb3V0ZU06IHt9LAogIC8vIOi3r+eUsemhtemdouS/oeaBrwogIG1lbnVBdWRpdERhdGE6IFtdLAogIC8vIOiPnOWNleaVsOaNrijljIXmi6zoj5zljZXlh4blhaXnpoHmraLmnaHku7bjgIHmjInpkq5pZOOAgeiPnOWNlWlk44CB5a6h5qC45pa55byP562J5pWw5o2uKe+8jOeUqOadpeWIpOaWreiPnOWNleWQhOS4quaMiemSruaTjeS9nOeahOWuoeaguOaWueW8jwogIGluaXRQYXJhbXM6IHt9LAogIC8vIOWIneWni+WMluWPguaVsAogIHdpbmRvd1BhdGg6ICcnLAogIC8vIOW9k+WJjeW+ruW6lOeUqHdpbmRvdwogIGluY2x1ZGVNb2R1bGU6ICcnIC8vIOeUqOaIt+aJgOWMheWQq+eahOaooeWdlwp9OwoKdmFyIG11dGF0aW9ucyA9IHsKICBTRVRfVE9LRU46IGZ1bmN0aW9uIFNFVF9UT0tFTihzdGF0ZSwgdG9rZW4pIHsKICAgIHN0YXRlLnRva2VuID0gdG9rZW47CiAgfSwKICBTRVRfV0lORE9XUEFUSDogZnVuY3Rpb24gU0VUX1dJTkRPV1BBVEgoc3RhdGUsIHBhdGgpIHsKICAgIC8vIOW9k+WJjeW+ruW6lOeUqOWcsOWdgOmFjee9rgogICAgc3RhdGUud2luZG93UGF0aCA9IHBhdGg7CiAgfSwKICBTRVRfSU5UUk9EVUNUSU9OOiBmdW5jdGlvbiBTRVRfSU5UUk9EVUNUSU9OKHN0YXRlLCBpbnRyb2R1Y3Rpb24pIHsKICAgIHN0YXRlLmludHJvZHVjdGlvbiA9IGludHJvZHVjdGlvbjsKICB9LAogIFNFVF9OQU1FOiBmdW5jdGlvbiBTRVRfTkFNRShzdGF0ZSwgbmFtZSkgewogICAgc3RhdGUudXNlck5vID0gbmFtZTsKICB9LAogIFNFVF9BVkFUQVI6IGZ1bmN0aW9uIFNFVF9BVkFUQVIoc3RhdGUsIGF2YXRhcikgewogICAgc3RhdGUuYXZhdGFyID0gYXZhdGFyOwogIH0sCiAgU0VUX1JPTEVTOiBmdW5jdGlvbiBTRVRfUk9MRVMoc3RhdGUsIHJvbGVzKSB7CiAgICBzdGF0ZS5yb2xlcyA9IHJvbGVzOwogIH0sCiAgU0VUX0xPR0lOSU5GTzogZnVuY3Rpb24gU0VUX0xPR0lOSU5GTyhzdGF0ZSwgZGF0YSkgewogICAgc3RhdGUubG9naW5JbmZvID0gZGF0YTsKICAgIHN0YXRlLm9yZ2FuTm8gPSBkYXRhLm9yZ2FuTm87CiAgICBzdGF0ZS5vcmdhbkxldmVsID0gZGF0YS5vcmdhbkxldmVsOwogICAgc3RhdGUudXNlck5hbWUgPSBkYXRhLnVzZXJOYW1lOwogICAgc3RhdGUucm9sZU5vID0gZGF0YS5yb2xlTm87CiAgICBzdGF0ZS5pbmNsdWRlTW9kdWxlID0gZGF0YS5pbmNsdWRlTW9kdWxlOwogIH0sCiAgU0VUX1VTRVI6IGZ1bmN0aW9uIFNFVF9VU0VSKHN0YXRlLCBkYXRhKSB7CiAgICAvLyDphY3nva7miYDmnInlsZ7mgKcKICAgIGlmIChkZWZhdWx0U2V0dGluZ3MucHJvY2VzcyA9PT0gJ2RldmVsb3BtZW50JykgewogICAgICBzZXRUb2tlbihkYXRhLnRva2VuKTsKICAgIH0KICAgIGZvciAodmFyIGl0ZW0gaW4gZGF0YSkgewogICAgICBzdGF0ZVtpdGVtXSA9IGRhdGFbaXRlbV07CiAgICB9CiAgfSwKICAvLyDorr7nva7ot6/nlLHpobXpnaLkv6Hmga8KICBTRVRfUk9VVEVfTTogZnVuY3Rpb24gU0VUX1JPVVRFX00oc3RhdGUsIGRhdGEpIHsKICAgIHN0YXRlLnJvdXRlTSA9IGRhdGE7CiAgICBzdGF0ZS5idG5BbGwgPSBkYXRhLmJ1dHRvbl9pZDsKICB9LAogIC8vIOiPnOWNleaMiemSruWuoeaguOaWueW8jwogIE1FTlVfQVVESVRfREFUQTogZnVuY3Rpb24gTUVOVV9BVURJVF9EQVRBKHN0YXRlLCBtZW51QXVkaXREYXRhKSB7CiAgICBzdGF0ZS5tZW51QXVkaXREYXRhID0gbWVudUF1ZGl0RGF0YTsKICB9LAogIFNFVF9JTklUX1BBUkFNUzogZnVuY3Rpb24gU0VUX0lOSVRfUEFSQU1TKHN0YXRlLCBkYXRhKSB7CiAgICBzdGF0ZS5pbml0UGFyYW1zID0gZGF0YTsKICB9LAogIC8vIOS/ruaUueS4u+mimOiJsuiLseaWh+Wtl+autQogIENIQU5HRV9USEVNRTogZnVuY3Rpb24gQ0hBTkdFX1RIRU1FKHN0YXRlLCBkYXRhKSB7CiAgICBzdGF0ZS5sb2dpbkluZm8uY3VzdG9tQXR0ck1hcC50aGVtZSA9IGRhdGE7CiAgfQp9Owp2YXIgYWN0aW9ucyA9IHsKICAvKioKICAgKiDnlKjmiLfnmbvlvZUKICAgKiBAcGFyYW0gdXNlckluZm86IOeUqOaIt+eZu+W9leS/oeaBrwogICAqLwogIGxvZ2luOiBmdW5jdGlvbiBsb2dpbihfcmVmLCB1c2VySW5mbykgewogICAgdmFyIF90aGlzID0gdGhpczsKICAgIHZhciBzdG9yZSA9IF9yZWYuc3RvcmUsCiAgICAgIGNvbW1pdCA9IF9yZWYuY29tbWl0LAogICAgICBkaXNwYXRjaCA9IF9yZWYuZGlzcGF0Y2gsCiAgICAgIHN0YXRlID0gX3JlZi5zdGF0ZTsKICAgIHNlc3Npb25TdG9yYWdlLmNsZWFyKCk7IC8vIOa4heepuuaJgOaciee8k+WtmAogICAgY29tbWl0KCdwZXJtaXNzaW9uL1NFVF9ST1VURVMnLCBbXSk7IC8vIOa4heepuui3r+eUsQogICAgY29tbWl0KCdTRVRfUk9MRVMnLCBbXSk7IC8vIOenu+mZpOinkuiJsu+8jOmHjei9vei3r+eUsQogICAgdmFyIHN5c3RlbU5vID0gdXNlckluZm8uc3lzdGVtTm8sCiAgICAgIHVzZXJuYW1lID0gdXNlckluZm8udXNlcm5hbWUsCiAgICAgIHBhc3N3b3JkID0gdXNlckluZm8ucGFzc3dvcmQsCiAgICAgIGNvZGUgPSB1c2VySW5mby5jb2RlOwogICAgdmFyIHBhc3N3b3JkU2VjID0gZW5jcnlwdFJlc3VsdChzdGF0ZS5pbml0UGFyYW1zLmVuU2VjTWFwLmVuY3J5cHRUeXBlLCBwYXNzd29yZCk7IC8vIOWKoOWvhgogICAgdmFyIG1zZyA9IHsKICAgICAgcGFyYW1ldGVyTGlzdDogW3sKICAgICAgICB1c2VyTm86IHVzZXJuYW1lLnRyaW0oKSwKICAgICAgICBwYXNzd29yZDogcGFzc3dvcmRTZWMsCiAgICAgICAgaXNDaGVjazogJzEnLAogICAgICAgIGxvZ2luVGVybWluYWw6ICcxJywKICAgICAgICBsb2dpblR5cGU6ICcxJwogICAgICB9XSwKICAgICAgY29kZTogY29kZSwKICAgICAgb3Blcl90eXBlOiAnMCcsCiAgICAgIGxvZ2luS2luZDogJ3VzZXJfbm8nLAogICAgICBzeXN0ZW1Obzogc3lzdGVtTm8KICAgIH07CiAgICByZXR1cm4gbmV3IFByb21pc2UoZnVuY3Rpb24gKHJlc29sdmUsIHJlamVjdCkgewogICAgICBfbG9naW4obXNnKS50aGVuKGZ1bmN0aW9uIChyZXNwb25zZSkgewogICAgICAgIC8vIOeZu+W9leivt+axggogICAgICAgIHZhciBsb2dpbkZsYWcgPSByZXNwb25zZS5yZXRNYXAubG9naW5GbGFnOwogICAgICAgIGlmIChsb2dpbkZsYWcgPT09ICdsb2dpblN1Y2Nlc3MnKSB7CiAgICAgICAgICB2YXIgbG9naW5Vc2VyID0gcmVzcG9uc2UucmV0TWFwLmxvZ2luVXNlcjsKICAgICAgICAgIGNvbW1pdCgnU0VUX1RPS0VOJywgZ2V0VG9rZW4oKSk7IC8vIHRva2Vu6K6+572uCiAgICAgICAgICBjb21taXQoJ1NFVF9OQU1FJywgbG9naW5Vc2VyLnVzZXJObyk7CiAgICAgICAgICBkaXNwYXRjaCgnZ2V0TWVudUF1ZGl0Jyk7IC8vIOiwg+eUqOiPnOWNleaMiemSruWuoeaguOaWueW8jwogICAgICAgICAgdmFyIGluZm8gPSBfb2JqZWN0U3ByZWFkKF9vYmplY3RTcHJlYWQoX29iamVjdFNwcmVhZCh7fSwgcmVzcG9uc2UucmV0TWFwKSwgcmVzcG9uc2UucmV0TWFwLmxvZ2luVXNlciksIHJlc3BvbnNlLnJldE1hcC5sb2dpbk9yZ2FuKTsKICAgICAgICAgIGNvbW1pdCgnU0VUX0xPR0lOSU5GTycsIGluZm8pOwogICAgICAgICAgLy8g5aSE55CG5Li76aKY6ImyIGJlZ2luCiAgICAgICAgICBfdGhpcy5jb21taXQoJ3NldHRpbmdzL0NIQU5HRV9TRVRUSU5HJywgewogICAgICAgICAgICBrZXk6ICd0aGVtZScsCiAgICAgICAgICAgIHZhbHVlOiBnZXRUaGVtZUNvbG9yKGluZm8uY3VzdG9tQXR0ck1hcC50aGVtZSkKICAgICAgICAgIH0pOwogICAgICAgICAgLy8g5aSE55CG5Li76aKY6ImyIGVuZAogICAgICAgICAgLy8gc2V0VG9rZW4oQXV0aG9yaXphdGlvbikKICAgICAgICAgIHJlc29sdmUoKTsKICAgICAgICB9IGVsc2UgewogICAgICAgICAgcmVqZWN0KHJlc3BvbnNlKTsKICAgICAgICB9CiAgICAgIH0pLmNhdGNoKGZ1bmN0aW9uIChlcnJvcikgewogICAgICAgIHJlamVjdChlcnJvcik7CiAgICAgIH0pOwogICAgfSk7CiAgfSwKICAvKioKICAgKiDlhY3lr4bnmbvlvZUKICAgKiBAcGFyYW0gdXNlckluZm86IOWFjeWvhueZu+W9lQogICAqLwogIGF1dG9Mb2dpbjogZnVuY3Rpb24gYXV0b0xvZ2luKF9yZWYyLCB1c2VySW5mbykgewogICAgdmFyIGNvbW1pdCA9IF9yZWYyLmNvbW1pdDsKICAgIHZhciB1c2VybmFtZSA9IHVzZXJJbmZvLnVzZXJuYW1lLAogICAgICBwYXNzd29yZCA9IHVzZXJJbmZvLnBhc3N3b3JkOwogICAgLy8gY29uc3QgcGFzc3dvcmRBRVMgPSBlbmNyeXB0UmVzdWx0KAogICAgLy8gICB0aGlzLiRzdG9yZS5nZXR0ZXJzLmluaXRQYXJhbXMuZW5TZWNNYXAuZW5jcnlwdFR5cGUsCiAgICAvLyAgIHBhc3N3b3JkCiAgICAvLyApIC8vIOWKoOWvhgogICAgdmFyIG1zZyA9IHsKICAgICAgcGFyYW1ldGVyTGlzdDogW3sKICAgICAgICB1c2VyTm86IHVzZXJuYW1lLnRyaW0oKSwKICAgICAgICBwYXNzd29yZDogcGFzc3dvcmQsCiAgICAgICAgaXNDaGVjazogJzEnLAogICAgICAgIGxvZ2luVGVybWluYWw6ICcxJywKICAgICAgICBsb2dpblR5cGU6ICcxJwogICAgICB9XSwKICAgICAgb3Blcl90eXBlOiAnMCcsCiAgICAgIGxvZ2luS2luZDogJ3VzZXJfbm8nCiAgICB9OwogICAgcmV0dXJuIG5ldyBQcm9taXNlKGZ1bmN0aW9uIChyZXNvbHZlLCBkaXNwYXRjaCwgcmVqZWN0KSB7CiAgICAgIF9sb2dpbihtc2cpLnRoZW4oZnVuY3Rpb24gKHJlc3BvbnNlKSB7CiAgICAgICAgLy8g55m75b2V6K+35rGCCiAgICAgICAgdmFyIGxvZ2luRmxhZyA9IHJlc3BvbnNlLnJldE1hcC5sb2dpbkZsYWc7CiAgICAgICAgaWYgKGxvZ2luRmxhZyA9PT0gJ2xvZ2luU3VjY2VzcycpIHsKICAgICAgICAgIHZhciBfcmVzcG9uc2UkcmV0TWFwID0gcmVzcG9uc2UucmV0TWFwLAogICAgICAgICAgICBBdXRob3JpemF0aW9uID0gX3Jlc3BvbnNlJHJldE1hcC5BdXRob3JpemF0aW9uLAogICAgICAgICAgICBsb2dpblVzZXIgPSBfcmVzcG9uc2UkcmV0TWFwLmxvZ2luVXNlcjsKICAgICAgICAgIGNvbW1pdCgnU0VUX1RPS0VOJywgQXV0aG9yaXphdGlvbik7CiAgICAgICAgICBjb21taXQoJ1NFVF9OQU1FJywgbG9naW5Vc2VyLnVzZXJObyk7CiAgICAgICAgICBkaXNwYXRjaCgnZ2V0TWVudUF1ZGl0Jyk7IC8vIOiwg+eUqOiPnOWNleaMiemSruWuoeaguOaWueW8jwogICAgICAgICAgdmFyIGluZm8gPSBfb2JqZWN0U3ByZWFkKF9vYmplY3RTcHJlYWQoX29iamVjdFNwcmVhZCh7fSwgcmVzcG9uc2UucmV0TWFwKSwgcmVzcG9uc2UucmV0TWFwLmxvZ2luVXNlciksIHJlc3BvbnNlLnJldE1hcC5sb2dpbk9yZ2FuKTsKICAgICAgICAgIGNvbW1pdCgnU0VUX0xPR0lOSU5GTycsIGluZm8pOwogICAgICAgICAgc2V0VG9rZW4oQXV0aG9yaXphdGlvbik7CiAgICAgICAgICByZXNvbHZlKCk7CiAgICAgICAgfSBlbHNlIHsKICAgICAgICAgIHJlamVjdChyZXNwb25zZSk7CiAgICAgICAgfQogICAgICB9KS5jYXRjaChmdW5jdGlvbiAoZXJyb3IpIHsKICAgICAgICByZWplY3QoZXJyb3IpOwogICAgICB9KTsKICAgIH0pOwogIH0sCiAgLy8g6I635Y+W55So5oi35L+h5oGvCiAgZ2V0SW5mbzogZnVuY3Rpb24gZ2V0SW5mbyhfcmVmMykgewogICAgdmFyIGNvbW1pdCA9IF9yZWYzLmNvbW1pdCwKICAgICAgc3RhdGUgPSBfcmVmMy5zdGF0ZTsKICAgIHJldHVybiBuZXcgUHJvbWlzZShmdW5jdGlvbiAocmVzb2x2ZSwgcmVqZWN0KSB7CiAgICAgIHZhciByb2xlcyA9IFsnYWRtaW4nXTsKICAgICAgY29tbWl0KCdTRVRfUk9MRVMnLCByb2xlcyk7CiAgICAgIHJlc29sdmUoewogICAgICAgIHJvbGVzOiByb2xlcwogICAgICB9KTsKICAgIH0pOwogIH0sCiAgLy8g57O757uf6YCA5Ye6CiAgbG9nb3V0OiBmdW5jdGlvbiBsb2dvdXQoX3JlZjQpIHsKICAgIHZhciBjb21taXQgPSBfcmVmNC5jb21taXQsCiAgICAgIHN0YXRlID0gX3JlZjQuc3RhdGUsCiAgICAgIGRpc3BhdGNoID0gX3JlZjQuZGlzcGF0Y2g7CiAgICBzZXNzaW9uU3RvcmFnZS5jbGVhcigpOyAvLyDmuIXnqbrmiYDmnInnvJPlrZgKICAgIHJldHVybiBuZXcgUHJvbWlzZShmdW5jdGlvbiAocmVzb2x2ZSwgcmVqZWN0KSB7CiAgICAgIHZhciBtc2cgPSB7CiAgICAgICAgb3Blcl90eXBlOiAnMCcsCiAgICAgICAgdXNlcl9ubzogc3RhdGUudXNlck5vLAogICAgICAgIGxvZ2luX3Rlcm1pbmFsOiBzdGF0ZS5sb2dpbkluZm8ubG9naW5UZXJtaW5hbCwKICAgICAgICBmbGFnOiAnMScKICAgICAgfTsKICAgICAgLy8gcmVzZXQgdmlzaXRlZCB2aWV3cyBhbmQgY2FjaGVkIHZpZXdzCiAgICAgIC8vIHRvIGZpeGVkIGh0dHBzOi8vZ2l0aHViLmNvbS9QYW5KaWFDaGVuL3Z1ZS1lbGVtZW50LWFkbWluL2lzc3Vlcy8yNDg1CiAgICAgIGRpc3BhdGNoKCd0YWdzVmlldy9kZWxBbGxWaWV3cycsIG51bGwsIHsKICAgICAgICByb290OiB0cnVlCiAgICAgIH0pOwoKICAgICAgLy8gcmVzb2x2ZSgpCiAgICAgIF9sb2dvdXQobXNnKS50aGVuKGZ1bmN0aW9uICgpIHsKICAgICAgICBjb21taXQoJ1NFVF9UT0tFTicsICcnKTsKICAgICAgICBjb21taXQoJ1NFVF9ST0xFUycsIFtdKTsKICAgICAgICByZW1vdmVUb2tlbigpOwogICAgICAgIHJlc2V0Um91dGVyKCk7CiAgICAgICAgbG9jYXRpb24ucmVsb2FkKCk7CiAgICAgIH0pLmNhdGNoKGZ1bmN0aW9uIChlcnJvcikgewogICAgICAgIGNvbW1pdCgnU0VUX1RPS0VOJywgJycpOwogICAgICAgIGNvbW1pdCgnU0VUX1JPTEVTJywgW10pOwogICAgICAgIHJlbW92ZVRva2VuKCk7CiAgICAgICAgcmVzZXRSb3V0ZXIoKTsKICAgICAgICBsb2NhdGlvbi5yZWxvYWQoKTsKICAgICAgICByZWplY3QoZXJyb3IpOwogICAgICB9KTsKICAgIH0pOwogIH0sCiAgLy8g56e76ZmkIHRva2VuCiAgcmVzZXRUb2tlbjogZnVuY3Rpb24gcmVzZXRUb2tlbihfcmVmNSkgewogICAgdmFyIGNvbW1pdCA9IF9yZWY1LmNvbW1pdDsKICAgIHJldHVybiBuZXcgUHJvbWlzZShmdW5jdGlvbiAocmVzb2x2ZSkgewogICAgICBjb21taXQoJ1NFVF9UT0tFTicsICcnKTsKICAgICAgY29tbWl0KCdTRVRfUk9MRVMnLCBbXSk7CiAgICAgIHJlbW92ZVRva2VuKCk7CiAgICAgIHJlc29sdmUoKTsKICAgIH0pOwogIH0sCiAgLy8gZHluYW1pY2FsbHkgbW9kaWZ5IHBlcm1pc3Npb25zCiAgLy8gYXN5bmMgY2hhbmdlUm9sZXMoeyBjb21taXQsIGRpc3BhdGNoIH0sIHJvbGUpIHsKICAvLyAgIGNvbnN0IHRva2VuID0gcm9sZSArICctdG9rZW4nCiAgLy8gICBjb21taXQoJ1NFVF9UT0tFTicsIHRva2VuKQogIC8vICAgc2V0VG9rZW4odG9rZW4pCiAgLy8gICBjb25zdCB7IHJvbGVzIH0gPSBhd2FpdCBkaXNwYXRjaCgnZ2V0SW5mbycpCiAgLy8gICByZXNldFJvdXRlcigpCiAgLy8gICAvLyBnZW5lcmF0ZSBhY2Nlc3NpYmxlIHJvdXRlcyBtYXAgYmFzZWQgb24gcm9sZXMKICAvLyAgIGNvbnN0IGFjY2Vzc1JvdXRlcyA9IGF3YWl0IGRpc3BhdGNoKCdwZXJtaXNzaW9uL2dlbmVyYXRlUm91dGVzJywgcm9sZXMsIHsgcm9vdDogdHJ1ZSB9KQogIC8vICAgLy8gZHluYW1pY2FsbHkgYWRkIGFjY2Vzc2libGUgcm91dGVzCiAgLy8gICByb3V0ZXIuYWRkUm91dGVzKGFjY2Vzc1JvdXRlcykKICAvLyAgIC8vIHJlc2V0IHZpc2l0ZWQgdmlld3MgYW5kIGNhY2hlZCB2aWV3cwogIC8vICAgZGlzcGF0Y2goJ3RhZ3NWaWV3L2RlbEFsbFZpZXdzJywgbnVsbCwgeyByb290OiB0cnVlIH0pCiAgLy8gfQogIC8vIOiOt+WPluiPnOWNleWuoeaguOaWueW8jwogIGdldE1lbnVBdWRpdDogZnVuY3Rpb24gZ2V0TWVudUF1ZGl0KF9yZWY2KSB7CiAgICB2YXIgY29tbWl0ID0gX3JlZjYuY29tbWl0LAogICAgICBkaXNwYXRjaCA9IF9yZWY2LmRpc3BhdGNoLAogICAgICBzdGF0ZSA9IF9yZWY2LnN0YXRlOwogICAgdmFyIG1zZyA9IHsKICAgICAgcGFyYW1ldGVyTGlzdDogW10sCiAgICAgIGN1cnJlbnRQYWdlOiAtMSwKICAgICAgb3BlcmF0aW9uUmVxdWVzdENvbmZpZ19wYWdlTnVtOiAxMCwKICAgICAgbWVudV9pZDogJycsCiAgICAgIG1lbnVfbmFtZTogJycsCiAgICAgIGNoZWNrX2ZsYWc6ICcnCiAgICB9OwogICAgcXVlcnkobXNnKS50aGVuKGZ1bmN0aW9uIChyZXMpIHsKICAgICAgdmFyIGxpc3QgPSByZXMucmV0TWFwLmxpc3Q7CiAgICAgIHZhciB0ZW1wID0ge307CiAgICAgIHZhciBfaXRlcmF0b3IgPSBfY3JlYXRlRm9yT2ZJdGVyYXRvckhlbHBlcihsaXN0KSwKICAgICAgICBfc3RlcDsKICAgICAgdHJ5IHsKICAgICAgICBmb3IgKF9pdGVyYXRvci5zKCk7ICEoX3N0ZXAgPSBfaXRlcmF0b3IubigpKS5kb25lOykgewogICAgICAgICAgdmFyIGsgPSBfc3RlcC52YWx1ZTsKICAgICAgICAgIGRlbGV0ZSBrLmJ1dHRvbl9uYW1lOwogICAgICAgICAgZGVsZXRlIGsubWVudV9uYW1lOwogICAgICAgICAgdmFyIGFyciA9IGsuYnV0dG9uX2lkLnNwbGl0KCcsJyk7CiAgICAgICAgICBpZiAoT2JqZWN0LnByb3RvdHlwZS5oYXNPd25Qcm9wZXJ0eS5jYWxsKHRlbXAsIGsubWVudV9pZCkpIHsKICAgICAgICAgICAgLy8g5b6q546v5a+56LGh5pe26Ziy5q2i55u45ZCM6I+c5Y2VaWTopobnm5YKICAgICAgICAgICAgdmFyIF9pdGVyYXRvcjIgPSBfY3JlYXRlRm9yT2ZJdGVyYXRvckhlbHBlcihhcnIpLAogICAgICAgICAgICAgIF9zdGVwMjsKICAgICAgICAgICAgdHJ5IHsKICAgICAgICAgICAgICBmb3IgKF9pdGVyYXRvcjIucygpOyAhKF9zdGVwMiA9IF9pdGVyYXRvcjIubigpKS5kb25lOykgewogICAgICAgICAgICAgICAgdmFyIGl0ZW0gPSBfc3RlcDIudmFsdWU7CiAgICAgICAgICAgICAgICB0ZW1wW2subWVudV9pZF1baXRlbV0gPSBrOwogICAgICAgICAgICAgIH0KICAgICAgICAgICAgfSBjYXRjaCAoZXJyKSB7CiAgICAgICAgICAgICAgX2l0ZXJhdG9yMi5lKGVycik7CiAgICAgICAgICAgIH0gZmluYWxseSB7CiAgICAgICAgICAgICAgX2l0ZXJhdG9yMi5mKCk7CiAgICAgICAgICAgIH0KICAgICAgICAgIH0gZWxzZSB7CiAgICAgICAgICAgIHRlbXBbay5tZW51X2lkXSA9IHt9OwogICAgICAgICAgICB2YXIgX2l0ZXJhdG9yMyA9IF9jcmVhdGVGb3JPZkl0ZXJhdG9ySGVscGVyKGFyciksCiAgICAgICAgICAgICAgX3N0ZXAzOwogICAgICAgICAgICB0cnkgewogICAgICAgICAgICAgIGZvciAoX2l0ZXJhdG9yMy5zKCk7ICEoX3N0ZXAzID0gX2l0ZXJhdG9yMy5uKCkpLmRvbmU7KSB7CiAgICAgICAgICAgICAgICB2YXIgX2l0ZW0gPSBfc3RlcDMudmFsdWU7CiAgICAgICAgICAgICAgICB0ZW1wW2subWVudV9pZF1bX2l0ZW1dID0gazsKICAgICAgICAgICAgICB9CiAgICAgICAgICAgIH0gY2F0Y2ggKGVycikgewogICAgICAgICAgICAgIF9pdGVyYXRvcjMuZShlcnIpOwogICAgICAgICAgICB9IGZpbmFsbHkgewogICAgICAgICAgICAgIF9pdGVyYXRvcjMuZigpOwogICAgICAgICAgICB9CiAgICAgICAgICB9CiAgICAgICAgfQogICAgICB9IGNhdGNoIChlcnIpIHsKICAgICAgICBfaXRlcmF0b3IuZShlcnIpOwogICAgICB9IGZpbmFsbHkgewogICAgICAgIF9pdGVyYXRvci5mKCk7CiAgICAgIH0KICAgICAgY29tbWl0KCdNRU5VX0FVRElUX0RBVEEnLCB0ZW1wKTsgLy8g5bCG6I+c5Y2V5a6h5qC45pWw57uE5a2Y5Zyoc3RvcmXkuK0KICAgIH0pOwogIH0sCiAgLy8g5L+u5pS55Li76aKY6Imy6Iux5paH5a2X5q61CiAgY2hhbmdlVGhlbWU6IGZ1bmN0aW9uIGNoYW5nZVRoZW1lKF9yZWY3LCBkYXRhKSB7CiAgICB2YXIgY29tbWl0ID0gX3JlZjcuY29tbWl0OwogICAgY29tbWl0KCdDSEFOR0VfVEhFTUUnLCBkYXRhKTsKICB9Cn07CmV4cG9ydCBkZWZhdWx0IHsKICBuYW1lc3BhY2VkOiB0cnVlLAogIC8vIOW8gOWQr25hbWVzcGFjZTp0cnVl77yM6K+l5qih5Z2X5bCx5oiQ5Li65ZG95ZCN56m66Ze05qih5Z2X5LqGCiAgc3RhdGU6IHN0YXRlLAogIG11dGF0aW9uczogbXV0YXRpb25zLAogIGFjdGlvbnM6IGFjdGlvbnMKfTs="}, null]}