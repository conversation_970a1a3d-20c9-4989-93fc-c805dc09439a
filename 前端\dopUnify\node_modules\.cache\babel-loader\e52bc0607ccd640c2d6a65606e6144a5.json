{"remainingRequest": "D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\node_modules\\thread-loader\\dist\\cjs.js!D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\node_modules\\babel-loader\\lib\\index.js!D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\src\\views\\system\\dailyManage\\sysLink\\component\\table\\index.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\src\\views\\system\\dailyManage\\sysLink\\component\\table\\index.vue", "mtime": 1686019808544}, {"path": "D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\babel.config.js", "mtime": 1686019818359}, {"path": "D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\node_modules\\thread-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1655715099000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, null]}