package com.sunyard.etl.nps.model;

//无纸化业务数据表
public class NpBusinessData {

	private String occurDate;
	private String siteNo;
	private String operatorNo;
	private String flowId;
	private String otherFlowId;
	private String formName;
	private String contentId;
	private String newContentId;
	private int flag;
	private int errorFlag;
	private String busiDataNo;
	private String batchId;
	private String indexValue;
	
	private String dataSourceId;
	private String mix;
	
    /* @Fields processing : TODO 是否正在处理，0：否，1：是 */
    private Integer processing = 0;

	public String getOccurDate() {
		return occurDate;
	}

	public void setOccurDate(String occurDate) {
		this.occurDate = occurDate;
	}

	public String getSiteNo() {
		return siteNo;
	}

	public void setSiteNo(String siteNo) {
		this.siteNo = siteNo;
	}

	public String getOperatorNo() {
		return operatorNo;
	}

	public void setOperatorNo(String operatorNo) {
		this.operatorNo = operatorNo;
	}

	public String getFlowId() {
		return flowId;
	}

	public void setFlowId(String flowId) {
		this.flowId = flowId;
	}

	public String getOtherFlowId() {
		return otherFlowId;
	}

	public void setOtherFlowId(String otherFlowId) {
		this.otherFlowId = otherFlowId;
	}

	public String getFormName() {
		return formName;
	}

	public void setFormName(String formName) {
		this.formName = formName;
	}

	public String getContentId() {
		return contentId;
	}

	public void setCONTENTId(String contentId) {
		this.contentId = contentId;
	}

	public int getFlag() {
		return flag;
	}

	public void setFlag(int flag) {
		this.flag = flag;
	}


	public int getErrorFlag() {
		return errorFlag;
	}

	public void setErrorFlag(int errorFlag) {
		this.errorFlag = errorFlag;
	}
 
	public String getBatchId() {
		return batchId;
	}

	public void setBatchId(String batchId) {
		this.batchId = batchId;
	}

	public String getBusiDataNo() {
		return busiDataNo;
	}

	public void setBusiDataNo(String busiDataNo) {
		this.busiDataNo = busiDataNo;
	}

	public String getIndexValue() {
		return indexValue;
	}

	public void setIndexValue(String indexValue) {
		this.indexValue = indexValue;
	}

	public String getNewContentId() {
		return newContentId;
	}

	public void setNewContentId(String newContentId) {
		this.newContentId = newContentId;
	}

	public Integer getProcessing() {
		return processing;
	}

	public void setProcessing(Integer processing) {
		this.processing = processing;
	}

	public String getDataSourceId() {
		return dataSourceId;
	}

	public void setDataSourceId(String dataSourceId) {
		this.dataSourceId = dataSourceId;
	}

	public String getMix() {
		return mix;
	}

	public void setMix(String mix) {
		this.mix = mix;
	}

}
