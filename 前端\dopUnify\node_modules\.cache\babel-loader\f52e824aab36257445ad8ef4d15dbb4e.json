{"remainingRequest": "D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\node_modules\\babel-loader\\lib\\index.js!D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\node_modules\\eslint-loader\\index.js??ref--13-0!D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\src\\utils\\request.js", "dependencies": [{"path": "D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\src\\utils\\request.js", "mtime": 1690773061248}, {"path": "D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\babel.config.js", "mtime": 1686019818359}, {"path": "D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\.babelrc", "mtime": 1705285078668}, {"path": "D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\node_modules\\eslint-loader\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["axios", "MessageBox", "Notification", "commonBlank", "store", "getToken", "setToken", "encryptResult", "decryptResult", "defaultSettings", "system", "service", "create", "baseURL", "process", "env", "VUE_APP_BASE_API", "timeout", "headers", "interceptors", "request", "use", "config", "enSecMap", "getters", "initParams", "token", "userNo", "url", "method", "params", "isEncrypt", "encodeURIComponent", "JSON", "stringify", "message", "keys", "Object", "key", "substring", "length", "data", "error", "Promise", "reject", "response", "status", "statusText", "authorization", "commit", "res", "retCode", "confirm", "retMsg", "confirmButtonText", "showClose", "showCancelButton", "type", "then", "dispatch", "location", "reload", "title", "indexOf", "Error", "Reflect", "has", "retMap", "parse", "aa", "alert"], "sources": ["D:/1_Project/XYD_Project/dop-4.0/dop-4.1-qianduan-unify/dopUnify/src/utils/request.js"], "sourcesContent": ["import axios from 'axios'\nimport { MessageBox, Notification } from 'element-ui'\nimport { commonBlank } from '@/utils/common'\nimport store from '@/store'\nimport { getToken, setToken } from '@/utils/auth'\nimport { encryptResult, decryptResult } from '@/utils/crypto'\nimport defaultSettings from '@/settings'\nconst system = defaultSettings.service.system\n// import qs from 'qs'\n\n// 新建一个 axios 实例\nconst service = axios.create({\n  // `baseURL` 将自动加在 `url` 前面，可以通过设置一个 `baseURL` 便于为 axios 实例的方法传递相对 URL\n  baseURL: process.env.VUE_APP_BASE_API, // url = base url + request url\n  timeout: 50000, // 请求超时(毫秒)\n  // withCredentials: true, // 当跨域请求时发送cookie\n  headers: { 'If-Modified-Since': '0', 'Cache-Control': 'no-cache' } // 解决ie浏览器get请求会被缓存，导致结果页面无法正常显示的问题\n})\n\n// 请求拦截器\nservice.interceptors.request.use(\n  (config) => {\n    const enSecMap = store.getters.initParams.enSecMap\n    if (store.getters.token && store.getters.userNo) {\n      // 让每个请求携带 token\n      // ['X-Token'] 是一个自定义头键\n      // 请根据实际情况进行修改\n      config.headers['Authorization'] = getToken()\n        ? getToken()\n        : store.getters.token\n    }\n    // 请求头配置\n    // config.headers.Accept = 'application/x-www-form-urlencoded'\n    // 参数传递格式化\n    // config.transformRequest = [\n    //   function(data) {\n    //     return qs.stringify(data, { arrayFormat: 'BRACKETS' })\n    //   }\n    // ]\n    let url = config.url\n\n    // get参数编码\n    if (config.method === 'get' && config.params) {\n      url += '?'\n      if (!commonBlank(enSecMap) && enSecMap.isEncrypt === '1') {\n        // 需要加密\n        url += `message=${encodeURIComponent(\n          encryptResult('SM4', JSON.stringify(config.params.message))\n        )}&`\n      } else {\n        const keys = Object.keys(config.params)\n        for (const key of keys) {\n          url += `${key}=${encodeURIComponent(\n            JSON.stringify(config.params[key])\n          )}&`\n        }\n      }\n      url = url.substring(0, url.length - 1)\n      config.params = {}\n    }\n    if (\n      !commonBlank(config.data) &&\n      !commonBlank(enSecMap) && // 第一个接口中必定不存在加密参数属性Map\n      enSecMap.isEncrypt === '1' && // 是否进行加密\n      url !== system + '/checkInit.do' // 是否为第一个获取加密参数的接口\n    ) {\n      config.data = {\n        message: encryptResult('SM4', JSON.stringify(config.data))\n      }\n    }\n    config.url = url\n    return config\n  },\n  (error) => {\n    // 对请求错误做些什么\n    return Promise.reject(error)\n  }\n)\n\n// 响应拦截器\nservice.interceptors.response.use(\n  /**\n   * 通过自定义代码确定请求状态\n   * 可以通过HTTP状态码来判断状态\n   */\n  (response) => {\n    const enSecMap = store.getters.initParams.enSecMap\n    // 对响应数据做点什么\n    if (commonBlank(response)) {\n      response = {}\n    }\n    const { status, statusText, headers } = response\n    //\n    if (!commonBlank(headers.authorization)) {\n      // console.log(2, headers.authorization)\n      setToken(headers.authorization)\n      store.commit('user/SET_TOKEN', headers.authorization)\n    }\n    if (status === 200) {\n      // 请求正常\n      const res = response.data\n      // 如果定制代码不是200，则判定为错误。\n      if (res.retCode !== 200 && res.retCode !== '200') {\n        // 508:非法token;512:其他客户端登录;50014:Token过期;\n        if (res.retCode === 508 || res.retCode === 512 || res.retCode === 514) {\n          // 重新登录提示\n          MessageBox.confirm(`${res.retMsg}，请重新登录`, '确认注销', {\n            confirmButtonText: '重新登录',\n            showClose: false,\n            showCancelButton: false,\n            type: 'warning'\n          }).then(() => {\n            store.dispatch('user/resetToken').then(() => {\n              location.reload()\n            })\n          })\n        } else if (res.retCode === 410) {\n          // 服务端Token更新\n          const { token } = res.data\n          setToken(token) // 重置token\n          store.commit('SET_TOKEN', token) // 重置 token\n          return res\n        } else if (res.retCode === 500 || res.retCode === '500') {\n          if (res.retMsg !== 'hasChildren') {\n            Notification.error({\n              title: '错误',\n              message: res.retMsg || 'Error',\n              type: 'error'\n            })\n          }\n          if (res.retMsg.indexOf('非法获取用户信息') > -1) {\n            // if ('非法获取用户信息,用户不存在'.indexOf(res.retMsg) > -1) {\n            store.dispatch('user/resetToken').then(() => {\n              location.reload()\n            })\n          }\n        } else {\n          Notification.error({\n            title: '错误',\n            message: '后台报错：' + res.retCode + ' - ' + res.retMsg || 'Error',\n            type: 'error'\n          })\n          // MessageBox.alert(\n          //   '后台报错：' + res.retCode + ' - ' + res.retMsg || 'Error',\n          //   {\n          //     confirmButtonText: '确定'\n          //   }\n          // )\n          // Message({\n          //   message: '后台报错：' + res.retCode + ' - ' + res.retMsg || 'Error',\n          //   type: 'error',\n          //   duration: 5 * 1000\n          // })\n        }\n        return Promise.reject(new Error(res.retMsg || 'Error'))\n      } else {\n        if (\n          !Reflect.has(res.retMap, 'enSec') &&\n          !commonBlank(enSecMap) &&\n          enSecMap.isEncrypt === '1'\n        ) {\n          res.retMap = JSON.parse(decryptResult('SM4', res.retMap.aa))\n        }\n        return res\n      }\n    } else {\n      // 重新登录提示\n      MessageBox.confirm(`${statusText}，请重新登录`, '确认注销', {\n        confirmButtonText: '重新登录',\n        showClose: false,\n        showCancelButton: false,\n        type: 'warning'\n      }).then(() => {\n        store.dispatch('user/resetToken').then(() => {\n          location.reload()\n        })\n      })\n    }\n  },\n  (error) => {\n    // 对响应错误做点什么\n    const { status, statusText } = error.response\n    if (error.response.status === 401 || error.response.status === '401') {\n      // 重新登录提示\n      MessageBox.confirm(`${status}：${statusText}，请重新登录`, '确认注销', {\n        confirmButtonText: '重新登录',\n        showClose: false,\n        showCancelButton: false,\n        type: 'warning'\n      }).then(() => {\n        store.dispatch('user/resetToken').then(() => {\n          location.reload()\n        })\n      })\n    } else {\n      // Message({\n      //   message: `后台报错：${status} - ${statusText}`,\n      //   type: 'error',\n      //   duration: 5 * 1000\n      // })\n      MessageBox.alert(`后台报错：${status} - ${statusText}`, {\n        confirmButtonText: '确定'\n      })\n      return Promise.reject(error) // 返回一个带有拒绝原因的Promise对象,error表示Promise被拒绝的原因。\n    }\n    7\n  }\n)\n\nexport default service\n"], "mappings": ";;;;;AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,UAAU,EAAEC,YAAY,QAAQ,YAAY;AACrD,SAASC,WAAW,QAAQ,gBAAgB;AAC5C,OAAOC,KAAK,MAAM,SAAS;AAC3B,SAASC,QAAQ,EAAEC,QAAQ,QAAQ,cAAc;AACjD,SAASC,aAAa,EAAEC,aAAa,QAAQ,gBAAgB;AAC7D,OAAOC,eAAe,MAAM,YAAY;AACxC,IAAMC,MAAM,GAAGD,eAAe,CAACE,OAAO,CAACD,MAAM;AAC7C;;AAEA;AACA,IAAMC,OAAO,GAAGX,KAAK,CAACY,MAAM,CAAC;EAC3B;EACAC,OAAO,EAAEC,OAAO,CAACC,GAAG,CAACC,gBAAgB;EAAE;EACvCC,OAAO,EAAE,KAAK;EAAE;EAChB;EACAC,OAAO,EAAE;IAAE,mBAAmB,EAAE,GAAG;IAAE,eAAe,EAAE;EAAW,CAAC,CAAC;AACrE,CAAC,CAAC;;AAEF;AACAP,OAAO,CAACQ,YAAY,CAACC,OAAO,CAACC,GAAG,CAC9B,UAACC,MAAM,EAAK;EACV,IAAMC,QAAQ,GAAGnB,KAAK,CAACoB,OAAO,CAACC,UAAU,CAACF,QAAQ;EAClD,IAAInB,KAAK,CAACoB,OAAO,CAACE,KAAK,IAAItB,KAAK,CAACoB,OAAO,CAACG,MAAM,EAAE;IAC/C;IACA;IACA;IACAL,MAAM,CAACJ,OAAO,CAAC,eAAe,CAAC,GAAGb,QAAQ,EAAE,GACxCA,QAAQ,EAAE,GACVD,KAAK,CAACoB,OAAO,CAACE,KAAK;EACzB;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA,IAAIE,GAAG,GAAGN,MAAM,CAACM,GAAG;;EAEpB;EACA,IAAIN,MAAM,CAACO,MAAM,KAAK,KAAK,IAAIP,MAAM,CAACQ,MAAM,EAAE;IAC5CF,GAAG,IAAI,GAAG;IACV,IAAI,CAACzB,WAAW,CAACoB,QAAQ,CAAC,IAAIA,QAAQ,CAACQ,SAAS,KAAK,GAAG,EAAE;MACxD;MACAH,GAAG,sBAAeI,kBAAkB,CAClCzB,aAAa,CAAC,KAAK,EAAE0B,IAAI,CAACC,SAAS,CAACZ,MAAM,CAACQ,MAAM,CAACK,OAAO,CAAC,CAAC,CAC5D,MAAG;IACN,CAAC,MAAM;MACL,IAAMC,IAAI,GAAGC,MAAM,CAACD,IAAI,CAACd,MAAM,CAACQ,MAAM,CAAC;MACvC,yBAAkBM,IAAI,2BAAE;QAAnB,IAAME,GAAG;QACZV,GAAG,cAAOU,GAAG,cAAIN,kBAAkB,CACjCC,IAAI,CAACC,SAAS,CAACZ,MAAM,CAACQ,MAAM,CAACQ,GAAG,CAAC,CAAC,CACnC,MAAG;MACN;IACF;IACAV,GAAG,GAAGA,GAAG,CAACW,SAAS,CAAC,CAAC,EAAEX,GAAG,CAACY,MAAM,GAAG,CAAC,CAAC;IACtClB,MAAM,CAACQ,MAAM,GAAG,CAAC,CAAC;EACpB;EACA,IACE,CAAC3B,WAAW,CAACmB,MAAM,CAACmB,IAAI,CAAC,IACzB,CAACtC,WAAW,CAACoB,QAAQ,CAAC;EAAI;EAC1BA,QAAQ,CAACQ,SAAS,KAAK,GAAG;EAAI;EAC9BH,GAAG,KAAKlB,MAAM,GAAG,eAAe,CAAC;EAAA,EACjC;IACAY,MAAM,CAACmB,IAAI,GAAG;MACZN,OAAO,EAAE5B,aAAa,CAAC,KAAK,EAAE0B,IAAI,CAACC,SAAS,CAACZ,MAAM,CAACmB,IAAI,CAAC;IAC3D,CAAC;EACH;EACAnB,MAAM,CAACM,GAAG,GAAGA,GAAG;EAChB,OAAON,MAAM;AACf,CAAC,EACD,UAACoB,KAAK,EAAK;EACT;EACA,OAAOC,OAAO,CAACC,MAAM,CAACF,KAAK,CAAC;AAC9B,CAAC,CACF;;AAED;AACA/B,OAAO,CAACQ,YAAY,CAAC0B,QAAQ,CAACxB,GAAG;AAC/B;AACF;AACA;AACA;AACE,UAACwB,QAAQ,EAAK;EACZ,IAAMtB,QAAQ,GAAGnB,KAAK,CAACoB,OAAO,CAACC,UAAU,CAACF,QAAQ;EAClD;EACA,IAAIpB,WAAW,CAAC0C,QAAQ,CAAC,EAAE;IACzBA,QAAQ,GAAG,CAAC,CAAC;EACf;EACA,gBAAwCA,QAAQ;IAAxCC,MAAM,aAANA,MAAM;IAAEC,UAAU,aAAVA,UAAU;IAAE7B,OAAO,aAAPA,OAAO;EACnC;EACA,IAAI,CAACf,WAAW,CAACe,OAAO,CAAC8B,aAAa,CAAC,EAAE;IACvC;IACA1C,QAAQ,CAACY,OAAO,CAAC8B,aAAa,CAAC;IAC/B5C,KAAK,CAAC6C,MAAM,CAAC,gBAAgB,EAAE/B,OAAO,CAAC8B,aAAa,CAAC;EACvD;EACA,IAAIF,MAAM,KAAK,GAAG,EAAE;IAClB;IACA,IAAMI,GAAG,GAAGL,QAAQ,CAACJ,IAAI;IACzB;IACA,IAAIS,GAAG,CAACC,OAAO,KAAK,GAAG,IAAID,GAAG,CAACC,OAAO,KAAK,KAAK,EAAE;MAChD;MACA,IAAID,GAAG,CAACC,OAAO,KAAK,GAAG,IAAID,GAAG,CAACC,OAAO,KAAK,GAAG,IAAID,GAAG,CAACC,OAAO,KAAK,GAAG,EAAE;QACrE;QACAlD,UAAU,CAACmD,OAAO,WAAIF,GAAG,CAACG,MAAM,2CAAU,MAAM,EAAE;UAChDC,iBAAiB,EAAE,MAAM;UACzBC,SAAS,EAAE,KAAK;UAChBC,gBAAgB,EAAE,KAAK;UACvBC,IAAI,EAAE;QACR,CAAC,CAAC,CAACC,IAAI,CAAC,YAAM;UACZtD,KAAK,CAACuD,QAAQ,CAAC,iBAAiB,CAAC,CAACD,IAAI,CAAC,YAAM;YAC3CE,QAAQ,CAACC,MAAM,EAAE;UACnB,CAAC,CAAC;QACJ,CAAC,CAAC;MACJ,CAAC,MAAM,IAAIX,GAAG,CAACC,OAAO,KAAK,GAAG,EAAE;QAC9B;QACA,IAAQzB,KAAK,GAAKwB,GAAG,CAACT,IAAI,CAAlBf,KAAK;QACbpB,QAAQ,CAACoB,KAAK,CAAC,EAAC;QAChBtB,KAAK,CAAC6C,MAAM,CAAC,WAAW,EAAEvB,KAAK,CAAC,EAAC;QACjC,OAAOwB,GAAG;MACZ,CAAC,MAAM,IAAIA,GAAG,CAACC,OAAO,KAAK,GAAG,IAAID,GAAG,CAACC,OAAO,KAAK,KAAK,EAAE;QACvD,IAAID,GAAG,CAACG,MAAM,KAAK,aAAa,EAAE;UAChCnD,YAAY,CAACwC,KAAK,CAAC;YACjBoB,KAAK,EAAE,IAAI;YACX3B,OAAO,EAAEe,GAAG,CAACG,MAAM,IAAI,OAAO;YAC9BI,IAAI,EAAE;UACR,CAAC,CAAC;QACJ;QACA,IAAIP,GAAG,CAACG,MAAM,CAACU,OAAO,CAAC,UAAU,CAAC,GAAG,CAAC,CAAC,EAAE;UACvC;UACA3D,KAAK,CAACuD,QAAQ,CAAC,iBAAiB,CAAC,CAACD,IAAI,CAAC,YAAM;YAC3CE,QAAQ,CAACC,MAAM,EAAE;UACnB,CAAC,CAAC;QACJ;MACF,CAAC,MAAM;QACL3D,YAAY,CAACwC,KAAK,CAAC;UACjBoB,KAAK,EAAE,IAAI;UACX3B,OAAO,EAAE,OAAO,GAAGe,GAAG,CAACC,OAAO,GAAG,KAAK,GAAGD,GAAG,CAACG,MAAM,IAAI,OAAO;UAC9DI,IAAI,EAAE;QACR,CAAC,CAAC;QACF;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;MACF;;MACA,OAAOd,OAAO,CAACC,MAAM,CAAC,IAAIoB,KAAK,CAACd,GAAG,CAACG,MAAM,IAAI,OAAO,CAAC,CAAC;IACzD,CAAC,MAAM;MACL,IACE,CAACY,OAAO,CAACC,GAAG,CAAChB,GAAG,CAACiB,MAAM,EAAE,OAAO,CAAC,IACjC,CAAChE,WAAW,CAACoB,QAAQ,CAAC,IACtBA,QAAQ,CAACQ,SAAS,KAAK,GAAG,EAC1B;QACAmB,GAAG,CAACiB,MAAM,GAAGlC,IAAI,CAACmC,KAAK,CAAC5D,aAAa,CAAC,KAAK,EAAE0C,GAAG,CAACiB,MAAM,CAACE,EAAE,CAAC,CAAC;MAC9D;MACA,OAAOnB,GAAG;IACZ;EACF,CAAC,MAAM;IACL;IACAjD,UAAU,CAACmD,OAAO,WAAIL,UAAU,2CAAU,MAAM,EAAE;MAChDO,iBAAiB,EAAE,MAAM;MACzBC,SAAS,EAAE,KAAK;MAChBC,gBAAgB,EAAE,KAAK;MACvBC,IAAI,EAAE;IACR,CAAC,CAAC,CAACC,IAAI,CAAC,YAAM;MACZtD,KAAK,CAACuD,QAAQ,CAAC,iBAAiB,CAAC,CAACD,IAAI,CAAC,YAAM;QAC3CE,QAAQ,CAACC,MAAM,EAAE;MACnB,CAAC,CAAC;IACJ,CAAC,CAAC;EACJ;AACF,CAAC,EACD,UAACnB,KAAK,EAAK;EACT;EACA,sBAA+BA,KAAK,CAACG,QAAQ;IAArCC,MAAM,mBAANA,MAAM;IAAEC,UAAU,mBAAVA,UAAU;EAC1B,IAAIL,KAAK,CAACG,QAAQ,CAACC,MAAM,KAAK,GAAG,IAAIJ,KAAK,CAACG,QAAQ,CAACC,MAAM,KAAK,KAAK,EAAE;IACpE;IACA7C,UAAU,CAACmD,OAAO,WAAIN,MAAM,mBAAIC,UAAU,2CAAU,MAAM,EAAE;MAC1DO,iBAAiB,EAAE,MAAM;MACzBC,SAAS,EAAE,KAAK;MAChBC,gBAAgB,EAAE,KAAK;MACvBC,IAAI,EAAE;IACR,CAAC,CAAC,CAACC,IAAI,CAAC,YAAM;MACZtD,KAAK,CAACuD,QAAQ,CAAC,iBAAiB,CAAC,CAACD,IAAI,CAAC,YAAM;QAC3CE,QAAQ,CAACC,MAAM,EAAE;MACnB,CAAC,CAAC;IACJ,CAAC,CAAC;EACJ,CAAC,MAAM;IACL;IACA;IACA;IACA;IACA;IACA5D,UAAU,CAACqE,KAAK,yCAASxB,MAAM,gBAAMC,UAAU,GAAI;MACjDO,iBAAiB,EAAE;IACrB,CAAC,CAAC;IACF,OAAOX,OAAO,CAACC,MAAM,CAACF,KAAK,CAAC,EAAC;EAC/B;;EACA,CAAC;AACH,CAAC,CACF;AAED,eAAe/B,OAAO"}]}