package com.sunyard.etl.nps.dao;

import java.sql.SQLException;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.sunyard.util.dbutil.DBHandler;

import com.sun.rowset.CachedRowSetImpl;
import com.sunyard.etl.system.common.Constants;
import com.xxl.job.core.log.XxlJobLogger;

public class FlMonDataTbDao {
	protected final Logger log = LoggerFactory.getLogger(getClass());

	
	
	/**
	 * 
	 * @Title  查询流水导入
	 * @Description
	 * <AUTHOR>
	 * 2018年2月23日
	 * @param occurDate
	 * @return
	 */
	public boolean getByOccurDate(String occurDate){
		DBHandler dbHandler = new DBHandler(Constants.CONN_POOL_NAME);
		CachedRowSetImpl rs = null;
		try{
			String sql = "SELECT * FROM FL_MON_DATA_TB T WHERE T.OCCUR_DATE = ? AND T.OP_FLAG = '1' ";
			rs = dbHandler.queryRs(sql, occurDate);
			while (rs.next()) {
				return true;
			}
		} catch (SQLException e) {
			XxlJobLogger.log("查询流水导入失败", e);
		}
		return false;
	}
}
