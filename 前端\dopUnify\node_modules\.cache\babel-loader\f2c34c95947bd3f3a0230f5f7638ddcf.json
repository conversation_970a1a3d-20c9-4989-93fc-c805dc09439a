{"remainingRequest": "D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\node_modules\\babel-loader\\lib\\index.js!D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\src\\views\\system\\outManage\\registration\\component\\table\\index.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\src\\views\\system\\outManage\\registration\\component\\table\\index.vue", "mtime": 1686019809310}, {"path": "D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\babel.config.js", "mtime": 1686019818359}, {"path": "D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1655715099000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:aW1wb3J0IF9vYmplY3RTcHJlYWQgZnJvbSAiRDovMV9Qcm9qZWN0L1hZRF9Qcm9qZWN0L2RvcC00LjAvZG9wLTQuMS1xaWFuZHVhbi11bmlmeS9kb3BVbmlmeS9ub2RlX21vZHVsZXMvQGJhYmVsL3J1bnRpbWUvaGVscGVycy9lc20vb2JqZWN0U3ByZWFkMi5qcyI7CmltcG9ydCAiY29yZS1qcy9tb2R1bGVzL2VzLm9iamVjdC50by1zdHJpbmcuanMiOwppbXBvcnQgImNvcmUtanMvbW9kdWxlcy93ZWIuZG9tLWNvbGxlY3Rpb25zLmZvci1lYWNoLmpzIjsKaW1wb3J0ICJjb3JlLWpzL21vZHVsZXMvZXMuYXJyYXkuY29uY2F0LmpzIjsKLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KCmltcG9ydCB7IGNvbmZpZ1RhYmxlIH0gZnJvbSAnLi9pbmZvJzsgLy8g6KGo5aS044CB6KGo5Y2V6YWN572uCmltcG9ydCBSZXNpemVNaXhpbiBmcm9tICdAL3V0aWxzL1Jlc2l6ZUhhbmRsZXInOyAvLyDmlbTkvZPpobXpnaLmmK/lkKbmoLnmja7mgLvpq5jphY3nva4KCmltcG9ydCB7IHN5c3RlbSB9IGZyb20gJ0AvYXBpJzsKdmFyIHF1ZXJ5ID0gc3lzdGVtLlN5c091dFJlZy5xdWVyeTsKZXhwb3J0IGRlZmF1bHQgewogIG5hbWU6ICdUYWJsZUxpc3QnLAogIG1peGluczogW1Jlc2l6ZU1peGluXSwKICBwcm9wczogewogICAgZGVmYXVsdEZvcm06IHsKICAgICAgdHlwZTogT2JqZWN0LAogICAgICBkZWZhdWx0OiBmdW5jdGlvbiBfZGVmYXVsdCgpIHsKICAgICAgICByZXR1cm4ge307CiAgICAgIH0KICAgIH0KICB9LAogIGRhdGE6IGZ1bmN0aW9uIGRhdGEoKSB7CiAgICByZXR1cm4gewogICAgICBsaXN0TG9hZGluZzogZmFsc2UsCiAgICAgIHRhYmxlOiB7CiAgICAgICAgLy8g6KGo5qC86YWN572uCiAgICAgICAgdGFibGVDb2x1bW5zOiBjb25maWdUYWJsZSgpLAogICAgICAgIC8vIOihqOWktOmFjee9rgogICAgICAgIHJlZjogJ3RhYmxlUmVmJywKICAgICAgICBzZWxlY3Rpb246IGZhbHNlLAogICAgICAgIC8vIOWkjemAiQogICAgICAgIGluZGV4TnVtYmVyOiB0cnVlLAogICAgICAgIC8vIOW6j+WPtwogICAgICAgIGxvYWRpbmc6IGZhbHNlLAogICAgICAgIGNvbXBvbmVudFByb3BzOiB7CiAgICAgICAgICBkYXRhOiBbXSwKICAgICAgICAgIC8vIOihqOagvOaVsOaNrgogICAgICAgICAgaGVpZ2h0OiAnMzcwcHgnLAogICAgICAgICAgZm9ybVJvdzogMSAvLyDooajljZXooYzmlbAKICAgICAgICB9LAoKICAgICAgICBwYWdlTGlzdDogewogICAgICAgICAgdG90YWxOdW06IDAsCiAgICAgICAgICBjdXJyZW50UGFnZTogMSwKICAgICAgICAgIC8vIOW9k+W<PERSON><PERSON>mhtQogICAgICAgICAgcGFnZVNpemU6IHRoaXMuJHN0b3JlLmdldHRlcnMucGFnZVNpemUgLy8g5b2T5YmN6aG15pi+56S65p2h5pWwCiAgICAgICAgfSwKCiAgICAgICAgY3VycmVudFJvdzogW10gLy8g6YCJ5Lit6KGMCiAgICAgIH0sCgogICAgICBkaWFsb2c6IHsKICAgICAgICB2aXNpYmxlOiBmYWxzZSwKICAgICAgICBsYWJlbFdpZHRoOiAnMTVyZW0nIC8vIOW9k+WJ<PERSON>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"}, {"version": 3, "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA8CA;AACA;;AAEA;AACA;AACA;EACAA;EACAC;EACAC;IACAC;MACAC;MACAC;QACA;MACA;IACA;EACA;EACAC;IACA;MACAC;MACAC;QACA;QACAC;QAAA;QACAC;QACAC;QAAA;QACAC;QAAA;QACAC;QACAC;UACAR;UAAA;UACAS;UACAC;QACA;;QACAC;UACAC;UACAC;UAAA;UACAC;QACA;;QACAC;MACA;;MACAC;QACAC;QACAC;MACA;;MACAC;MACAC;MAAA;MACAC;IACA;EACA;;EACAC;IACAf;MACA;IACA;EACA;EACAgB;IAAA;IACA;MACA;IACA;EACA;EACAC;IACA;AACA;AACA;AACA;AACA;IACAC;MACA;MACA;QACA;QACA;UACA;UACAC;QACA;QACAC;MACA;MACA;IACA;IACA;AACA;IACAC;MAAA;MACA;MACA;QACAC;QACAhB;QACAC;MACA;MACA;MACAgB;QACA;UAAAC;UAAAnB;UAAAC;QACA;QACA;QACA;QACA;QACA;UACA;YACA;cACAmB;YACA;UACA;UACA;YACA;cACAA;YACA;UACA;QACA;QACA;MACA;IACA;IACA;AACA;AACA;IACAC;MACA;IACA;IACA;AACA;IACAC;MAAA;MACA;MACAC;QACA;QACA;QACA;MACA;IACA;IACA;AACA;IACAC;MACA;QAAAtB;MACA;MACA;MACA;IACA;IACA;AACA;IACAuB;MACA;IACA;EACA;AACA", "names": ["name", "mixins", "props", "defaultForm", "type", "default", "data", "listLoading", "table", "tableColumns", "ref", "selection", "indexNumber", "loading", "componentProps", "height", "formRow", "pageList", "totalNum", "currentPage", "pageSize", "currentRow", "dialog", "visible", "labelWidth", "labelPosition", "applyText", "resText", "watch", "created", "methods", "commonChoices", "jsonObj", "jsonArr", "queryList", "parameterList", "query", "returnList", "item", "changeVisible", "rowClick", "setTimeout", "getList", "showLoading"], "sourceRoot": "src/views/system/outManage/registration/component/table", "sources": ["index.vue"], "sourcesContent": ["<template>\r\n  <div class=\"sun-content\">\r\n    <sun-table :table-config=\"table\" @pagination=\"getList\">\r\n      <template slot=\"tableColumn\">\r\n        <el-table-column\r\n          v-for=\"item in table.tableColumns\"\r\n          :key=\"item.id\"\r\n          :prop=\"item.name\"\r\n          :label=\"item.label\"\r\n          :width=\"item.width\"\r\n        >\r\n          <div slot-scope=\"{ row }\">\r\n            <span\r\n              v-if=\"item.name === 'order_no'\"\r\n              class=\"orderNo\"\r\n              @click=\"rowClick(row)\"\r\n            >{{ row[item.name] }}</span>\r\n            <span v-else-if=\"item.name === 'last_modi_date'\">{{\r\n              row[item.name] | dateTimeFormat\r\n            }}</span>\r\n            <span v-else>{{ row[item.name] }}</span>\r\n          </div>\r\n        </el-table-column>\r\n      </template>\r\n    </sun-table>\r\n    <el-dialog\r\n      :visible.sync=\"dialog.visible\"\r\n      title=\"详情\"\r\n      width=\"70%\"\r\n      @dialogClose=\"changeVisible\"\r\n    >\r\n      <el-form\r\n        :label-position=\"labelPosition\"\r\n        :disabled=\"true\"\r\n        class=\"demo-form-inline\"\r\n      >\r\n        <el-form-item label=\"请求报文\">\r\n          <el-input v-model=\"applyText\" type=\"textarea\" :rows=\"16\" />\r\n        </el-form-item>\r\n        <el-form-item label=\"返回报文\">\r\n          <el-input v-model=\"resText\" type=\"textarea\" :rows=\"16\" />\r\n        </el-form-item>\r\n      </el-form> </el-dialog><!--详情弹出框-->\r\n  </div>\r\n</template>\r\n<script>\r\nimport { configTable } from './info' // 表头、表单配置\r\nimport ResizeMixin from '@/utils/ResizeHandler' // 整体页面是否根据总高配置\r\n\r\nimport { system } from '@/api'\r\nconst { query } = system.SysOutReg\r\nexport default {\r\n  name: 'TableList',\r\n  mixins: [ResizeMixin],\r\n  props: {\r\n    defaultForm: {\r\n      type: Object,\r\n      default: function() {\r\n        return {}\r\n      }\r\n    }\r\n  },\r\n  data() {\r\n    return {\r\n      listLoading: false,\r\n      table: {\r\n        // 表格配置\r\n        tableColumns: configTable(), // 表头配置\r\n        ref: 'tableRef',\r\n        selection: false, // 复选\r\n        indexNumber: true, // 序号\r\n        loading: false,\r\n        componentProps: {\r\n          data: [], // 表格数据\r\n          height: '370px',\r\n          formRow: 1 // 表单行数\r\n        },\r\n        pageList: {\r\n          totalNum: 0,\r\n          currentPage: 1, // 当前页\r\n          pageSize: this.$store.getters.pageSize // 当前页显示条数\r\n        },\r\n        currentRow: [] // 选中行\r\n      },\r\n      dialog: {\r\n        visible: false,\r\n        labelWidth: '15rem' // 当前表单标签宽度配置\r\n      },\r\n      labelPosition: 'top',\r\n      applyText: '', // 请求报文\r\n      resText: '' // 返回报文\r\n    }\r\n  },\r\n  watch: {\r\n    loading(value) {\r\n      this.listLoading = this.loading\r\n    }\r\n  },\r\n  created() {\r\n    this.$nextTick(() => {\r\n      this.queryList()\r\n    })\r\n  },\r\n  methods: {\r\n    /**\r\n     * 多行数据拼写报文的方法\r\n     * @param dataArr\t选择行的数组\r\n     * @param attrArr  放置的参数数组\r\n     */\r\n    commonChoices(dataArr, attrArr) {\r\n      const jsonArr = []\r\n      for (let i = 0; i < dataArr.length; i++) {\r\n        const jsonObj = {}\r\n        for (let j = 0; j < attrArr.length; j++) {\r\n          const name = attrArr[j]\r\n          jsonObj[name] = dataArr[i][name]\r\n        }\r\n        jsonArr.push(jsonObj)\r\n      }\r\n      return jsonArr\r\n    },\r\n    /**\r\n     * 按钮：查询*/\r\n    queryList(currentPage) {\r\n      this.showLoading(true)\r\n      const msg = {\r\n        parameterList: [{ ...this.defaultForm }],\r\n        currentPage: currentPage || this.table.pageList.currentPage,\r\n        pageSize: this.table.pageList.pageSize\r\n      }\r\n      // 查询\r\n      query(msg).then((res) => {\r\n        const { returnList, totalNum, currentPage } = res.retMap\r\n        this.table.componentProps.data = returnList\r\n        this.table.pageList.totalNum = totalNum\r\n        this.table.pageList.currentPage = currentPage\r\n        // 格式化系统标识和接口标识\r\n        this.table.componentProps.data.forEach((item) => {\r\n          this.$store.getters.externalData.ALL_SYS_ID.forEach((item2) => {\r\n            if (item2.value === item.sys_id) {\r\n              item.sys_id = `${item2.value}-${item2.label}`\r\n            }\r\n          })\r\n          this.$store.getters.externalData.TD_NO.forEach((item2) => {\r\n            if (item2.value === item.td_no) {\r\n              item.td_no = `${item2.value}-${item2.label}`\r\n            }\r\n          })\r\n        })\r\n        this.showLoading(false)\r\n      })\r\n    },\r\n    /**\r\n     * 弹出框 - 关闭\r\n     * @param {Boolean} param 弹出框显示隐藏配置*/\r\n    changeVisible(param) {\r\n      this.dialog.visible = false\r\n    },\r\n    /**\r\n     *流水号：详情*/\r\n    rowClick(row) {\r\n      this.dialog.visible = true\r\n      setTimeout(() => {\r\n        // 详情弹框赋值\r\n        this.applyText = row.req_msg\r\n        this.resText = row.resp_msg\r\n      }, 100)\r\n    },\r\n    /**\r\n     *页码更新 */\r\n    getList(pageParam) {\r\n      const { currentPage, pageSize } = pageParam\r\n      this.table.pageList.pageSize = pageSize\r\n      this.table.pageList.currentPage = currentPage\r\n      this.queryList()\r\n    },\r\n    /**\r\n     * 加载中动画配置*/\r\n    showLoading() {\r\n      this.listLoading = !this.listLoading\r\n    }\r\n  }\r\n}\r\n</script>\r\n<style lang=\"scss\" scoped>\r\n.orderNo {\r\n  color: #0000ff;\r\n}\r\n.demo-form-inline {\r\n  display: flex;\r\n  justify-content: space-evenly;\r\n}\r\n.el-form-item {\r\n  margin-left: -2px;\r\n  width: 40%;\r\n}\r\n</style>\r\n"]}]}