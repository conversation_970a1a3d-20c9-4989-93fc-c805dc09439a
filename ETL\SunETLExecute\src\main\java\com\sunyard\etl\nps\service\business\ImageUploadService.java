package com.sunyard.etl.nps.service.business;

import java.io.File;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.Iterator;
import java.util.List;
import java.util.Map;

import org.dom4j.DocumentException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.sunyard.util.date.DateUtil;
import org.sunyard.util.map.MapUtil;

import com.sunyard.etl.nps.common.NPSContants;
import com.sunyard.etl.nps.dao.ADMS5Dao;
import com.sunyard.etl.nps.dao.NpBusinessDataTbDao;
import com.sunyard.etl.nps.dao.NpFtpDataDateDao;
import com.sunyard.etl.nps.dao.NpImageDataTbDao;
import com.sunyard.etl.nps.dao.SmDataSourceSetTbDao;
import com.sunyard.etl.nps.model.BpTmpbatchTb;
import com.sunyard.etl.nps.model.NpBusinessData;
import com.sunyard.etl.nps.model.NpImageData;
import com.sunyard.etl.nps.model.SmDataSourceSetTb;
import com.sunyard.etl.nps.service.base.ECMService;
import com.sunyard.etl.nps.service.base.FileService;
import com.sunyard.etl.system.dao.SmServiceRegDao;
import com.sunyard.etl.system.dao.impl.SmServiceRegDaoImpl;
import com.sunyard.etl.system.model.JobParam;
import com.sunyard.etl.system.model.SmServiceReg;
import com.xxl.job.core.log.XxlJobLogger;

public class ImageUploadService {
	private static String tableName = "ImageUploadService";
	protected final Logger log = LoggerFactory.getLogger(getClass());

	private ECMService UploadECMService;
	private FileService FileService = new FileService();
	
	private JobParam jobParam;
	private NpImageDataTbDao npImageDataTbDao = new NpImageDataTbDao("tableName");
	private NpBusinessDataTbDao npBusinessDataTbDao = new NpBusinessDataTbDao("tableName");
	private NpFtpDataDateDao npFtpDataDateDao = new NpFtpDataDateDao();
	private SmDataSourceSetTbDao sourceDao  = new SmDataSourceSetTbDao();
	private SmServiceRegDao smServiceRegDao = new SmServiceRegDaoImpl();
	private ADMS5Dao ADMS5Dao = new ADMS5Dao();


	public String init(JobParam jobParam) {
		SmDataSourceSetTb source = sourceDao.getById("0");
		if(null != source){
			SmServiceReg ECM = smServiceRegDao.getServiceRegByServiceIdDao(source.getServiceId());
			UploadECMService = new ECMService(ECM.getServiceIp(),Integer.parseInt(ECM.getServicePort()),ECM.getLoginName(),ECM.getLoginPass());
			UploadECMService.set(source.getGroupName(), source.getModeCode(),source.getFilePartName(), source.getIndexName());
			XxlJobLogger.log("初始化ECM服务参数...", tableName);
			XxlJobLogger.log("ip:"+ECM.getServiceIp(), tableName);
			XxlJobLogger.log("port:"+ECM.getServicePort(), tableName);
			XxlJobLogger.log("username:"+ECM.getLoginName(), tableName);
			XxlJobLogger.log("password:"+ECM.getLoginPass(), tableName);
			XxlJobLogger.log("GroupName:"+source.getGroupName(), tableName);
			XxlJobLogger.log("ModeCode:"+source.getModeCode(), tableName);
			XxlJobLogger.log("FilePartName:"+source.getFilePartName(), tableName);
			XxlJobLogger.log("IndexName:"+source.getIndexName(), tableName);
		} else {
			XxlJobLogger.log("SmDataSourceSetTb 表中数据异常",tableName);
		}
		jobParam.setDefoultFormName("无纸化凭证");
		jobParam.setDataSourceId("0");
	 	this.jobParam = jobParam;
	 	return "SUCCESS|成功";
	}
	
	/*
	 * 上传到ECM并在无纸化表中生成数据
	 */
	public String updateECM() {
		String dataSourceId = this.jobParam.getDataSourceId();
		List<File> batchFileList;
		try {
			batchFileList = FileService.getBatchFileList(this.jobParam.getLocalPath());
		} catch (SQLException e1) {
			e1.printStackTrace();
			XxlJobLogger.log(e1.toString(), tableName);
			XxlJobLogger.log("获取批次文件失败", tableName);
			return "FAIL|获取批次文件失败";
		}
		for (File batch : batchFileList) {
			String occurDate = batch.getParentFile().getParentFile().getName();
			String siteNo = batch.getParentFile().getName();
			String operatorNo = batch.getName();
			String indexValue = "";
			String batchId = "";
			String contentId = "";
			boolean mix = false;
			BpTmpbatchTb mixedBatch = new BpTmpbatchTb();

			
			if (this.jobParam.getIsMix().equals("1")){ // 混合 前置判断
				mixedBatch.setOccurDate(occurDate);
				mixedBatch.setSiteNo(siteNo);
				mixedBatch.setOperatorNo(operatorNo);
				String result = null;
				try {
					result = checkMixedBatch(mixedBatch);
				} catch (SQLException | DocumentException e) {
					XxlJobLogger.log(e.getMessage());
					return "FAIL|异常";
				}
				if (null == result) {
					return "FAIL|异常";
				} else if (result.equals("0")) { 
					mix = false;
				} else if (result.equals("2")) {// 存在多个混合批次，异常
					return "FAIL|存在多个混合批次";
				} else { 
					contentId = result;
					mix = true; // 柜员当天已经存在混合批次，追加影像到CONTENT_ID
				}
			}
			
			
			if(mix){
				indexValue = mixedBatch.getInputDate();
				batchId = mixedBatch.getBatchId();
				String ECMresult = UploadECMService.update(batch.getPath(),contentId, indexValue);
				if (ECMresult.contains("FAIL")) {
					XxlJobLogger.log("追加影像失败", tableName);
					continue;
				}
			} else {
				indexValue = DateUtil.getNewDate("yyyyMMdd");
				batchId = DateUtil.generateBatchId();
				contentId = UploadECMService.upload(batchId,batch.getPath(), indexValue); // 上传ECM，返回contentId
				if (contentId.contains("FAIL") || contentId.length() < 1) {
					XxlJobLogger.log("上传失败", tableName);
					return "FAIL|上传失败";
				}
			}
			
			/*
			 * 组装数据
			 */
			Map<String, List<String>> imgMap = FileService.getImgMap(batch.getPath()); // 获取所有
			if (imgMap == null || imgMap.size() == 0) {
				XxlJobLogger.log("解析文件名失败" + batch.getPath(), tableName);
				return "FAIL|解析文件名失败";
			}
			imgMap = MapUtil.sortMapByKey(imgMap);// 按流水号进行排序
			List<NpBusinessData> npBusinessDataList = new ArrayList<NpBusinessData>();//拼装业务数据表
			for (Iterator<String> i = imgMap.keySet().iterator(); i.hasNext();) {
				String flowId = i.next();
				NpBusinessData npBusinessData = new NpBusinessData();
				npBusinessData.setOccurDate(occurDate);
				npBusinessData.setSiteNo(siteNo);
				npBusinessData.setOperatorNo(operatorNo);
				npBusinessData.setFlowId(flowId);
				npBusinessData.setDataSourceId(dataSourceId);
				npBusinessData.setBatchId(batchId);
				npBusinessData.setFormName(this.jobParam.getDefoultFormName());
				npBusinessData.setCONTENTId(contentId);
				npBusinessData.setIndexValue(indexValue);
				npBusinessData.setMix(mix==true?"1":"0");
				npBusinessDataList.add(npBusinessData);
			}
			try {// 导入NP_BUSINESS_DATA_TB前清理
				Map<String, String> businessNoMapOld = npBusinessDataTbDao.getBusiDataNoByDataList(npBusinessDataList);
				if (businessNoMapOld != null && businessNoMapOld.size() > 0) {
					String busiDataNo = businessNoMapOld.values().toString();
					busiDataNo = busiDataNo.substring(1, busiDataNo.length() - 1).replace(",", "','").replace(" ", "");
					boolean deleteBusiResult = npBusinessDataTbDao.cleanByBusiDataNo(busiDataNo);
					if (!deleteBusiResult) {
						XxlJobLogger.log("重新导入时清理历史数据失败:", tableName);
						return "FAIL|重新导入时清理历史数据失败";
					}
				}
			} catch (SQLException e) {
				XxlJobLogger.log("重新导入时清理历史数据失败:" + e.toString(), tableName);
				return "FAIL|重新导入时清理历史数据失败";
			}
			boolean insertBusiResult = npBusinessDataTbDao.insertNpBusinessData(npBusinessDataList);// 插入无纸化业务数据表
			if (!insertBusiResult) {
				XxlJobLogger.log("插入无纸化业务数据表失败:", tableName);
				return "FAIL|插入无纸化业务数据表失败";
			}
			try {// 将图像数据插入到无纸化影像数据表中 // 获取业务流水号与BusiDataNo的对应关系
				Map<String, String> businessNoMapNew = npBusinessDataTbDao.getBusiDataNoByDataList(npBusinessDataList);
				List<NpImageData> npImageDataList = new ArrayList<NpImageData>();
				for (Iterator<String> i = imgMap.keySet().iterator(); i.hasNext();) {
					String flowId = i.next(); // 一笔业务
					String psLevel = NPSContants.ADMS5_PS_LEVEL_MAIN;
					String formName = this.jobParam.getDefoultFormName()+ "-主件";
					for (int j = 1; j < imgMap.get(flowId).size(); j++) {
						NpImageData npImageData = new NpImageData();
						npImageData.setBusiDataNo(businessNoMapNew.get(flowId));
						npImageData.setFormName(formName);
						npImageData.setPsLevel(psLevel);
						npImageData.setFileName(flowId + "-"+ imgMap.get(flowId).get(j) + "."+ imgMap.get(flowId).get(0));
						npImageData.setBackFileName("");
						npImageData.setImageSize(0);
						npImageData.setBackImageSize(0);
						npImageDataList.add(npImageData);
						psLevel = NPSContants.ADMS5_PS_LEVEL_ANNEX;// 附件属性
						formName = this.jobParam.getDefoultFormName() + "-附件";
					}
				}
				
				//如果有统一主附件
				if(null != this.jobParam.getPsLevel() && !this.jobParam.getPsLevel().equals("")){
					for (int i = 0; i<npImageDataList.size(); i++){
						npImageDataList.get(i).setPsLevel(this.jobParam.getPsLevel());
					}
				}
				
				
				boolean insertImgResult = npImageDataTbDao.insertNpImageData(npImageDataList);
				if (!insertImgResult) {
					XxlJobLogger.log("插入无纸化图像数据表失败:", tableName);
					return "FAIL|插入无纸化图像数据表失败";
				}
			} catch (SQLException e) {
				e.printStackTrace();
			}
			// 更新FTP_DATA_DATE
			try {
				if (!npFtpDataDateDao.finish(occurDate, dataSourceId)) {
					XxlJobLogger.log("更新FTP_DATA_DATE失败: " + occurDate + ":"	+ dataSourceId, tableName);
				}
			} catch (SQLException e) {
				XxlJobLogger.log(e.getMessage());
				XxlJobLogger.log("更新FTP_DATA_DATE失败: " + occurDate + ":"+ dataSourceId, tableName);
			}
		}
		return "SUCCESS|成功";
	}
	
	
	
	/*
	 * 检查混合批次是否存在，如果已经存在则返回content_id
	 */
	public String checkMixedBatch(BpTmpbatchTb batch) throws SQLException, DocumentException{
		List<BpTmpbatchTb> mixedBatchList = ADMS5Dao.queryMixedBpTmpbatch(batch.getOccurDate(), batch.getSiteNo(), batch.getOperatorNo());
		if (mixedBatchList != null ){
			if (mixedBatchList.size() == 1) {
				batch = mixedBatchList.get(0);
				batch = ADMS5Dao.queryBpTmpbatch(batch.getBatchId());
				String resultXML = UploadECMService.heightQuery(batch.getBatchId(),batch.getInputDate());
				return UploadECMService.parseHeightQueryXML(resultXML);
			} else if (mixedBatchList.size() > 1){
				return "2";
			}
		}
		return "0";
	}
	
//	public static void main(String[] args) {
//		FileService FileService = new FileService();
//		Map<String, List<String>> imgMap = FileService.getImgMap("D:\\FTP\\ADMS\\20170901\\020100\\200003"); // 获取所有
//		if (imgMap == null || imgMap.size() == 0) {
//  		}
//		imgMap = MapUtil.sortMapByKey(imgMap);// 按流水号进行排序
//		System.out.println("111");
//	}
}
