package com.sunyard.etl.custom.handler;

import java.io.BufferedReader;
import java.io.File;
import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.InputStreamReader;
import java.io.OutputStreamWriter;
import java.io.PrintWriter;
import java.text.SimpleDateFormat;
import com.sunyard.etl.system.common.Constants;
import org.springframework.stereotype.Service;

import com.sunyard.etl.system.dao.DataDateDAO;
import com.sunyard.etl.system.dao.impl.DataDateDAOImpl;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.enums.JobStartEnum;
import com.xxl.job.core.handler.IJobHandler;
import com.xxl.job.core.handler.annotation.JobHandler;
import com.xxl.job.core.log.XxlJobLogger;

@JobHandler(value = "FileFormat_PIVS0030", name = "PIVS0030文件格式化")
@Service
public class FileFormat_PIVS0030 extends IJobHandler {

	private static final long serialVersionUID = 1L;

	private static DataDateDAO dateDao = new DataDateDAOImpl();

	@Override
	public ReturnT<String> execute(String jobId, String... arg1) throws Exception {
		XxlJobLogger.log("开始PIVS0030文件格式化...");
		String jobDate = dateDao.getDataDate();
		if (null != arg1[0]) {
			String dirPath = arg1[0].toString().replace("@", jobDate);
			File dir = new File(dirPath);
			if (!dir.isDirectory()) {
				XxlJobLogger.log("INFO: 资源不足，目录不存在：" + dir, jobId + "");
				return new ReturnT<String>(ReturnT.FAIL_CODE, JobStartEnum.TASK_STATE_NO_RESOURCE.getCode(),
						"文件目录" + dir.getPath() + "不存在");
			}
			File preFile = new File(dir, "PIVS0030_" + jobDate + ".txt");
			File file = new File(dir, "PIVS0030_" + jobDate + "_proc.txt");
			if (file.exists()) {
				XxlJobLogger.log("INFO: 文件已存在，删除后再转换：" + file.getPath(), jobId + "");
				file.delete();
			}
			PrintWriter pw;
			pw = new PrintWriter(new OutputStreamWriter(new FileOutputStream(file), "GBK"));
			if (preFile.length() == 0) {
				pw.print("|||");
				pw.flush();
				pw.close();
				XxlJobLogger.log("INFO: 源文件不存在，生成转换文件：" + preFile.getPath() + " >> " + file.getPath(), jobId + "");
				return ReturnT.SUCCESS;
			}

			BufferedReader br = new BufferedReader(new InputStreamReader(new FileInputStream(preFile), "GBK"));

			String line = null;
			String siteNo = "";
			String siteName = "";
			String occurDate = "";
			try {
				while ((line = br.readLine()) != null) {
					if (line.matches("[ ]{10,}\\d{4}年\\d{2}月\\d{2}日")) {
						occurDate = line.trim().replace("年", "").replace("月", "").replace("日", "");
						continue;
					}
					if (line.contains("所在机构：")) {
						siteNo = line.substring(line.indexOf("所在机构：(") + "所在机构：(".length(), line.indexOf(")")).trim();
						siteName = line.substring(line.indexOf(")")+1).replaceAll("　", "");
						continue;
					}
					if ("".equals(line.trim()) || "||||||||||".equals(line.replace(" ", "")) || line.contains("核查结果为\"")  
							|| (line.contains("柜员号") && line.contains("核查时间") && line.contains("联网核查结果"))
							|| line.trim().matches("^\\d+$") ||line.contains("-----------------------------------") || "中国银行联网核查日志统计表".equals(line.trim())) {
						continue;
					}
					String[] arr = line.split("\\|");
					String newLine = occurDate + "|" + siteNo + "|" + siteName + "|" + arr[1].trim() + "|" // 柜员号
							+ arr[2].trim() + "|" // 核查时间
							+ arr[3].trim() + "|" // 客户姓名
							+ arr[4].trim() + "|" // 证件号码
							+ arr[5].trim() + "|" // 联网核查结果
							+ arr[6].trim() + "|" // 业务类型
							+ arr[7].trim() + "|" // 系统标识
							+ arr[8].trim() + "|" // 联网核查编号
							+ arr[9].trim(); // 备注
					pw.print(newLine + System.getProperty("line.separator"));
				}
			} catch (IOException e) {
				e.printStackTrace();
			} finally {
				pw.flush();
				pw.close();
			}
			XxlJobLogger.log("INFO: 文件转换成功：" + file.getPath(), jobId + "");
			return ReturnT.SUCCESS;

		}
		return ReturnT.FAIL;
	}

	public static int indexOf(String str,String instr,int n){
		int i=1;
		int index=-1;
		if(n<=0){
			return -1;
		}else{
			while(i<=n){
				i++;
				index = str.indexOf(instr,index+1);
				if(index==-1){
					return -1;
				}
			}
			return index;
		}
		
	}
	public static void main(String[] args) {
		FileFormat_PIVS0030 ff = new FileFormat_PIVS0030();
		try {
			ff.execute("9", "C:\\@");
		} catch (Exception e) {
			// TODO Auto-generated catch block
			e.printStackTrace();
		}
	}
}
