package com.sunyard.etl.nps.orm;

import java.sql.ResultSet;
import java.sql.SQLException;

import com.sunyard.etl.nps.model.BpTmpbatchTb;
import com.sunyard.etl.nps.model.SmDataSourceSetTb;
import com.sunyard.etl.system.orm.Orm;

public class SmDataSourceSetTbOrm implements Orm<SmDataSourceSetTb> {


	public SmDataSourceSetTb orm(ResultSet rs) {
		SmDataSourceSetTb source = new SmDataSourceSetTb();
		try {
			source.setCustId(rs.getString("CUST_ID"));
			source.setDataDesc(rs.getString("DATA_DESC"));
			source.setDataSourceId(rs.getString("DATA_SOURCE_ID"));
			source.setFilePartName(rs.getString("FILE_PART_NAME"));
			source.setGroupName(rs.getString("GROUP_NAME"));
			source.setIndexName(rs.getString("INDEX_NAME"));
			source.setModeCode(rs.getString("MODE_CODE"));
			source.setUaIp(rs.getString("UA_IP"));
			source.setUaPort(rs.getString("UA_PORT"));
			source.setServiceId(rs.getString("SERVICE_ID"));
		} catch (SQLException e) {
			e.printStackTrace();
		}
		return source;
	}


}
