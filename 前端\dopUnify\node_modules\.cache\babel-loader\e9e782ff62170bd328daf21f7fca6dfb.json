{"remainingRequest": "D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\node_modules\\thread-loader\\dist\\cjs.js!D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\node_modules\\babel-loader\\lib\\index.js!D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\src\\views\\system\\role\\component\\tree\\index.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\src\\views\\system\\role\\component\\tree\\index.vue", "mtime": 1702370267676}, {"path": "D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\babel.config.js", "mtime": 1686019818359}, {"path": "D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\node_modules\\thread-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1655715099000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, null]}