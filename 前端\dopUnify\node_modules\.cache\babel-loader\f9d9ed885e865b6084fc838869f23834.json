{"remainingRequest": "D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\node_modules\\babel-loader\\lib\\index.js!D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\node_modules\\eslint-loader\\index.js??ref--13-0!D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\src\\utils\\auth.js", "dependencies": [{"path": "D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\src\\utils\\auth.js", "mtime": 1686019809826}, {"path": "D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\babel.config.js", "mtime": 1686019818359}, {"path": "D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\.babelrc", "mtime": 1705285078668}, {"path": "D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\node_modules\\eslint-loader\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8g6Lqr5Lu96aqM6K+BCmltcG9ydCBDb29raWVzIGZyb20gJ2pzLWNvb2tpZSc7CnZhciBUb2tlbktleSA9ICdBdXRob3JpemF0aW9uJzsKLy8gKyBwYXJzZUludChNYXRoLnJhbmRvbSgpICogMTAwKQovLyBjb25zdCBUb2tlbktleSA9IG5ldyBEYXRlKCkKCmV4cG9ydCBmdW5jdGlvbiBnZXRUb2tlbigpIHsKICByZXR1cm4gQ29va2llcy5nZXQoVG9rZW5LZXkpOwp9CmV4cG9ydCBmdW5jdGlvbiBzZXRUb2tlbih0b2tlbikgewogIHJldHVybiBDb29raWVzLnNldChUb2tlbktleSwgdG9rZW4pOwp9CmV4cG9ydCBmdW5jdGlvbiByZW1vdmVUb2tlbigpIHsKICByZXR1cm4gQ29va2llcy5yZW1vdmUoVG9rZW5LZXkpOwp9"}, {"version": 3, "names": ["Cookies", "TokenKey", "getToken", "get", "setToken", "token", "set", "removeToken", "remove"], "sources": ["D:/1_Project/XYD_Project/dop-4.0/dop-4.1-qianduan-unify/dopUnify/src/utils/auth.js"], "sourcesContent": ["// 身份验证\nimport Cookies from 'js-cookie'\n\nconst TokenKey = 'Authorization'\n// + parseInt(Math.random() * 100)\n// const TokenKey = new Date()\n\nexport function getToken() {\n  return Cookies.get(TokenKey)\n}\n\nexport function setToken(token) {\n  return Cookies.set(To<PERSON><PERSON><PERSON>, token)\n}\n\nexport function removeToken() {\n  return Cookies.remove(TokenKey)\n}\n"], "mappings": "AAAA;AACA,OAAOA,OAAO,MAAM,WAAW;AAE/B,IAAMC,QAAQ,GAAG,eAAe;AAChC;AACA;;AAEA,OAAO,SAASC,QAAQ,GAAG;EACzB,OAAOF,OAAO,CAACG,GAAG,CAACF,QAAQ,CAAC;AAC9B;AAEA,OAAO,SAASG,QAAQ,CAACC,KAAK,EAAE;EAC9B,OAAOL,OAAO,CAACM,GAAG,CAACL,QAAQ,EAAEI,KAAK,CAAC;AACrC;AAEA,OAAO,SAASE,WAAW,GAAG;EAC5B,OAAOP,OAAO,CAACQ,MAAM,CAACP,QAAQ,CAAC;AACjC"}]}