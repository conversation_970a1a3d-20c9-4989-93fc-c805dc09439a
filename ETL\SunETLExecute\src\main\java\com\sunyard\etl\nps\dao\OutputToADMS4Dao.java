package com.sunyard.etl.nps.dao;

import java.sql.Connection;
import java.sql.SQLException;
import java.util.List;
import java.util.Map;

import com.sunyard.etl.nps.model.BpTmpbatchTb;
import com.sunyard.etl.nps.model.BpTmpdata1Tb;
import com.sunyard.etl.nps.model.SmMixedNopaperTb;



public interface OutputToADMS4Dao {

	/**
	 * 前置检查
	 */
	String armsDataDate() throws SQLException;// 获取TMS待处理日期

	String vtmDataDate() throws SQLException;// 获取VTM待处理日期

//	boolean isFlowFinished(String date);// 根据日期检查流水导入任务是否完成
	
	List<String> getPendingDateList() throws SQLException;// 获取批次表中需要处理的业务日期
	
	/**
	 * 批次信息
	 */
	List<BpTmpbatchTb> getBatchInfo() throws SQLException;// 从混合业务表提取批次
	
	List<BpTmpbatchTb> getPendingBatch(String occurDate) throws SQLException;// 从批次表提取混合批次

	Map<String, String> getJPGPathMap(String occurDate) throws SQLException;//获取每笔业务对应的JPG图像路径（条件：日期）
	
	List<SmMixedNopaperTb> getFlowInfo(BpTmpbatchTb bpTmpbatchTb) throws SQLException;// 从混合业务表提取流水信息

	void getMoreBatchInfo(BpTmpbatchTb bpTmpbatchTb) throws SQLException;// 从批次表提取更多的批次信息
	
	List<BpTmpdata1Tb> getBpTmpData(String batchId) throws SQLException;// 根据批次号获取数据表信息
	
	Long getMaxOffset (String batchId) throws SQLException;// 获取偏移量最大的图像的偏移量和图像大小
	
	/**
	 * 数据更新
	 * @throws SQLException 
	 */
	int updateInccodeinBatch(Connection conn, String batchId, String inccodeinBatch) throws SQLException;// 更新批内码

	int inertBpTmpata1Tb(Connection conn, BpTmpdata1Tb bpTmpdata1Tb) throws SQLException;// 插入附件的图像数据

	int updateBatchInfo(Connection conn, BpTmpbatchTb bpTmpbatchTb) throws SQLException;// 更新批次表的信息
	
	int updateSmMixedNopaperTb (Connection conn, String occurDate, String flowId) throws SQLException; // 更新混合业务表中数据的处理标志
	
	
	/**
	 * 剩余附件处理
	 */
	
	// 根据流水号找到勾兑主件的皮内码
	// 获取未处理的混合业务信息
	// 根据批次索引获取纸质批次号
	// 根据批次号获取批次具体信息
	// 检查图片是否解压完成

}
