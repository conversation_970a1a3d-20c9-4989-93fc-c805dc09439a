package com.sunyard.etl.common.dao;

import com.sun.rowset.CachedRowSetImpl;
import com.sunyard.etl.ocr.bean.DataSource;
import com.sunyard.etl.system.common.Constants;
import com.xxl.job.core.log.XxlJobLogger;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.sunyard.util.dbutil.DBHandler;

import java.sql.Connection;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class DataSourceDao {
    protected final Logger log = LoggerFactory.getLogger(getClass());
    protected final Logger logger = LoggerFactory.getLogger("ocrTaskSendLogger");

    public Map<String, String> getParamsMap(int id){
        Map<String, String> params = null;
        CachedRowSetImpl rs = null;
        DBHandler dbHandler = new DBHandler(Constants.CONN_POOL_NAME);
        try {
            String sql = "SELECT * FROM QRTZ_JOB_PARAM T WHERE T.JOBID = ?";
            XxlJobLogger.log("查询OCR识别任务发送参数:" + sql);
            rs = dbHandler.queryRs(sql, id);
            params = new HashMap();
            while(rs.next()){
                params.put(rs.getString("PARAM_FIELD"), rs.getString("PARAM_VALUE"));
            }
        } catch (SQLException e) {
            //e.printStackTrace();
            XxlJobLogger.log("根据配置获取参数配置异常", e);
        }
        return params;
    }

    /**
     * 检查数据库连接是否正常
     * @return 数据库连接是否正常
     * @throws SQLException 如果数据库连接异常
     */
    public boolean checkConnection() {
        DBHandler dbHandler = new DBHandler(Constants.CONN_POOL_NAME);
        Connection connection = null;
        boolean isConnected = false;
        try {
            connection = dbHandler.getConnection();
            if (connection != null && !connection.isClosed()) {
                isConnected = true;
            } else {
                logger.error("数据库连接池 '{}' 的连接为空或已关闭", Constants.CONN_POOL_NAME);
            }
        } catch (SQLException e) {
            logger.error("检查数据库连接池 '{}' 时发生异常，数据库服务器可能已掉线", Constants.CONN_POOL_NAME, e);
        } finally {
            if (connection != null) {
                try {
                    connection.close();
                } catch (SQLException e) {
                    logger.error("关闭数据库连接时发生异常", e);
                }
            }
        }
        return isConnected;
    }

    /**
     * 根据ID查找数据源
     * @param id 数据源ID
     * @return 数据源对象
     */
    public DataSource findById(int id) {
        DataSource dataSource = null;
        CachedRowSetImpl rs = null;
        DBHandler dbHandler = new DBHandler(Constants.CONN_POOL_NAME);
        try {
            String sql = "SELECT * FROM QRTZ_DATASOURCE_TB WHERE ID = ?";
            rs = dbHandler.queryRs(sql, id);
            if (rs.next()) {
                dataSource = new DataSource();
                dataSource.setId(rs.getInt("id"));
                dataSource.setName(rs.getString("name"));
                dataSource.setType(rs.getInt("type"));
                dataSource.setDriver(rs.getString("driver"));
                dataSource.setConnStr(rs.getString("connect_str"));
                dataSource.setUser(rs.getString("uname"));
                dataSource.setPassword(rs.getString("pwd"));
                dataSource.setDsDes(rs.getString("other"));
            }
        } catch (SQLException e) {
            logger.error("根据ID查找数据源时发生异常", e);
        }
        return dataSource;
    }

    /**
     * 查找所有数据源
     * @return 数据源列表
     */
    public List<DataSource> findAllDataSource() {
        List<DataSource> list = new ArrayList<>();
        CachedRowSetImpl rs = null;
        DBHandler dbHandler = new DBHandler(Constants.CONN_POOL_NAME);
        try {
            String sql = "SELECT * FROM QRTZ_DATASOURCE_TB";
            rs = dbHandler.queryRs(sql);
            while (rs.next()) {
                DataSource dataSource = new DataSource();
                dataSource.setId(rs.getInt("id"));
                dataSource.setName(rs.getString("name"));
                dataSource.setType(rs.getInt("type"));
                dataSource.setDriver(rs.getString("driver"));
                dataSource.setConnStr(rs.getString("connect_str"));
                dataSource.setUser(rs.getString("uname"));
                dataSource.setPassword(rs.getString("pwd"));
                dataSource.setDsDes(rs.getString("other"));
                list.add(dataSource);
            }
        } catch (SQLException e) {
            logger.error("查找所有数据源时发生异常", e);
        }
        return list;
    }

}
