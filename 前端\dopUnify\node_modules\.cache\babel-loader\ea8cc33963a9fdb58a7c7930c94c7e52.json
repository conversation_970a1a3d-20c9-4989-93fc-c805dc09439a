{"remainingRequest": "D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\node_modules\\thread-loader\\dist\\cjs.js!D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\node_modules\\babel-loader\\lib\\index.js!D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\src\\views\\system\\config\\menu\\component\\dialog\\index.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\src\\views\\system\\config\\menu\\component\\dialog\\index.vue", "mtime": 1686019807826}, {"path": "D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\babel.config.js", "mtime": 1686019818359}, {"path": "D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\node_modules\\thread-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1655715099000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, null]}