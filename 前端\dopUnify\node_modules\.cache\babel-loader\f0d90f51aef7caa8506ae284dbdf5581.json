{"remainingRequest": "E:\\华南区项目\\中国银行\\开发区\\科技公司运营平台\\前端\\dopUnify\\node_modules\\thread-loader\\dist\\cjs.js!E:\\华南区项目\\中国银行\\开发区\\科技公司运营平台\\前端\\dopUnify\\node_modules\\babel-loader\\lib\\index.js!E:\\华南区项目\\中国银行\\开发区\\科技公司运营平台\\前端\\dopUnify\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!E:\\华南区项目\\中国银行\\开发区\\科技公司运营平台\\前端\\dopUnify\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\华南区项目\\中国银行\\开发区\\科技公司运营平台\\前端\\dopUnify\\src\\views\\system\\externalManage\\user\\component\\table\\index.vue?vue&type=script&lang=js&", "dependencies": [{"path": "E:\\华南区项目\\中国银行\\开发区\\科技公司运营平台\\前端\\dopUnify\\src\\views\\system\\externalManage\\user\\component\\table\\index.vue", "mtime": 1721631954662}, {"path": "E:\\华南区项目\\中国银行\\开发区\\科技公司运营平台\\前端\\dopUnify\\babel.config.js", "mtime": 1721631954662}, {"path": "E:\\华南区项目\\中国银行\\开发区\\科技公司运营平台\\前端\\dopUnify\\.babelrc", "mtime": 1721631954662}, {"path": "E:\\华南区项目\\中国银行\\开发区\\科技公司运营平台\\前端\\dopUnify\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "E:\\华南区项目\\中国银行\\开发区\\科技公司运营平台\\前端\\dopUnify\\node_modules\\thread-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "E:\\华南区项目\\中国银行\\开发区\\科技公司运营平台\\前端\\dopUnify\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "E:\\华南区项目\\中国银行\\开发区\\科技公司运营平台\\前端\\dopUnify\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "E:\\华南区项目\\中国银行\\开发区\\科技公司运营平台\\前端\\dopUnify\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1655715099000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, null]}