{"remainingRequest": "E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-q<PERSON><PERSON>n\\数字运营平台-统一门户工程\\dopUnify\\node_modules\\babel-loader\\lib\\index.js!E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan\\数字运营平台-统一门户工程\\dopUnify\\node_modules\\qiankun\\es\\sandbox\\patchers\\css.js", "dependencies": [{"path": "E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan\\数字运营平台-统一门户工程\\dopUnify\\node_modules\\qiankun\\es\\sandbox\\patchers\\css.js", "mtime": 1667130453000}, {"path": "E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-<PERSON><PERSON><PERSON>n\\数字运营平台-统一门户工程\\dopUnify\\babel.config.js", "mtime": 1686019818359}, {"path": "E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-<PERSON><PERSON><PERSON>n\\数字运营平台-统一门户工程\\dopUnify\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-<PERSON><PERSON><PERSON>n\\数字运营平台-统一门户工程\\dopUnify\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, null]}