package com.sunyard.etl.nps.service.base;

import java.io.File;
import java.io.IOException;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.Comparator;
import java.util.List;

import org.dom4j.Document;
import org.dom4j.DocumentException;
import org.dom4j.DocumentHelper;
import org.dom4j.Element;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.sunyard.util.file.FileUtil;
import org.sunyard.util.file.MD5Util;
//import org.sunyard.util.log.SimpleLog;
import org.sunyard.util.url.UrlUtil;

import com.sunyard.client.SunEcmClientApi;
import com.sunyard.client.bean.ClientBatchBean;
import com.sunyard.client.bean.ClientBatchFileBean;
import com.sunyard.client.bean.ClientBatchIndexBean;
import com.sunyard.client.bean.ClientFileBean;
import com.sunyard.client.bean.ClientHeightQuery;
import com.sunyard.client.impl.SunEcmClientSocketApiImpl;
import com.sunyard.etl.nps.model.NpBusinessData;
import com.sunyard.etl.nps.model.NpImageData;
import com.sunyard.util.OptionKey;
import com.xxl.job.core.log.XxlJobLogger;

import org.sunyard.util.xml.XmlUtil;

public class ECMService {
	protected final Logger log = LoggerFactory.getLogger(getClass());
	private String tableName = "ECM_LOG";
	
	private String ECMIP;
	private int ECMPort;
	private String username;
	private String password;

	private	String groupName;
	private String indexName;// 分表字段

//	private String indexValue;// 分表字段值
	private String modelCode; // 内容模型代码
	private String filePartName;// 文档部件模型代码
	
	
	
	public ECMService(){
		
	}
	
	public ECMService(String ECMIP, int ECMPort, String username, String password) {
		this.ECMIP = ECMIP;
		this.ECMPort = ECMPort;
		this.username = username;
		this.password = password;
	}
	
	
	public void set(String groupName, String modelCode, String filePartName, String IndexName){
		this.groupName = groupName;
		this.modelCode = modelCode;
		this.filePartName = filePartName;
		this.indexName = IndexName;
	}
	
	public void copy(ECMService ser){
		ser = new ECMService(this.ECMIP,this.ECMPort,this.username,this.password);
	}
	
	public boolean login() throws Exception {
		SunEcmClientApi clientApi = new SunEcmClientSocketApiImpl(ECMIP, ECMPort);
		String resultMsg = clientApi.login(username, password);
		XxlJobLogger.log("#######登陆返回的信息[" + resultMsg + "]#######", tableName);
		if (resultMsg.equals("SUCCESS"))
			return true;
		return false;
	}

	public void logout() throws Exception {
		SunEcmClientApi clientApi = new SunEcmClientSocketApiImpl(ECMIP, ECMPort);
		String resultMsg = clientApi.logout(username);
		XxlJobLogger.log("#######登出返回的信息[" + resultMsg + "]#######", tableName);
	}

	public String upload(String batchId , String filePath, String indexValue) {	
		String contentId = "";
		File imagefile = new File(filePath);
		FileUtil.deleteXml(imagefile);
		List<File> imagefiles = Arrays.asList(imagefile.listFiles());
		if (imagefile == null || imagefiles == null || imagefiles.size() == 0) {
			XxlJobLogger.log("路径下文件不存在");
			return "FAIL";
		}
		/*
		 * 进行排序
		 */
		Collections.sort(imagefiles, new Comparator<File>(){
		    @Override
		    public int compare(File o1, File o2) {
		    if(o1.isDirectory() && o2.isFile())
		        return -1;
		    if(o1.isFile() && o2.isDirectory())
		            return 1;
		    return o1.getName().compareTo(o2.getName());
		    }
		});
		ClientBatchBean clientBatchBean = new ClientBatchBean();
		clientBatchBean.setModelCode(modelCode);
		clientBatchBean.setUser(username);
		clientBatchBean.setPassWord(password);
		clientBatchBean.setBreakPoint(false); // 是否作为断点续传上传
		clientBatchBean.setOwnMD5(false); // 是否为批次下的文件添加MD5码
		// =========================设置索引对象信息开始=========================
		ClientBatchIndexBean clientBatchIndexBean = new ClientBatchIndexBean();
		clientBatchIndexBean.setAmount("");
//		clientBatchIndexBean.setContentID(contentId);
		// 索引自定义属性
		clientBatchIndexBean.addCustomMap("BUSI_SERIAL_NO", batchId);
//		clientBatchIndexBean.addCustomMap("CREATEDATE", DateUtil.getNow());
		clientBatchIndexBean.addCustomMap(indexName, indexValue);
		// =========================设置索引对象信息结束=========================

		// =========================设置文档部件信息开始=========================
		ClientBatchFileBean clientBatchFileBean = new ClientBatchFileBean();
		clientBatchFileBean.setFilePartName(filePartName);
		// =========================设置文档部件信息结束=========================
		Document sortdoc = DocumentHelper.createDocument();
		Element sortElem = sortdoc.addElement("root"); 
		Element sortnodeElem=sortElem.addElement("node");
		sortnodeElem.addAttribute("name", "KJ_IMAGE");
		// =========================添加文件=========================
		for (int i = 1; i < imagefiles.size() + 1; i++) {
			File imageFile = imagefiles.get(i-1);
			String imagepath = imageFile.getPath();
			String fileName = imageFile.getName();
			XxlJobLogger.log(batchId+"<><><>文件部件添加:"+fileName);
//			String fileFormat = fileName.substring(fileName.lastIndexOf(".")+1);
			
//			if(!Contants.IMAGE_FORMAT.equalsIgnoreCase(fileFormat)){
//				break;
//			}
			// 添加FileBean
			ClientFileBean fileBean = new ClientFileBean();
			fileBean.setFileName(imageFile.getPath());
			fileBean.setFileFormat(fileName.substring(fileName.lastIndexOf(".")+1)); //
			//fileBean.setFilesize(imageFile.length() + "");
			// fileBean.setMd5Str(MD5Util.getHash(imagepath,"MD5"));
			fileBean.addOtherAtt("SHOWNAME", batchId + "-" + i);// 00016-ft
			// sort_20160315043417619.xml
			fileBean.addOtherAtt("FILEFORM", "KJ_IMAGE");// KJ_IMAGE// SORT_
			fileBean.addOtherAtt("TRUENAME", fileName);
			fileBean.addOtherAtt("FILEATTR", "1"); // 0,2
			fileBean.addOtherAtt("FILEMD5", MD5Util.getHash(imagepath, "MD5"));

			clientBatchFileBean.addFile(fileBean);

			Element sortitemElem = sortnodeElem.addElement("item");
			sortitemElem.addAttribute("filename", fileName);
		}
		
		// =======================添加排序文档============================
		// 生产排序报文
		String sortfileName = "sort_" + System.currentTimeMillis() + ".xml";
		XmlUtil.createXML(sortdoc, filePath + File.separator + sortfileName);
		ClientFileBean sortFileBean = new ClientFileBean();
		sortFileBean.setFileName(filePath + File.separator + sortfileName);
		sortFileBean.setFileFormat("xml"); //
		// sortFileBean.setFilesize(new File(sortfileName).length()+"");
		// sortFileBean.setMd5Str(MD5Util.getHash(filePath + File.separator +
		// batchId+File.separator+sortfileName, "MD5"));
		sortFileBean.addOtherAtt("FILEMD5", MD5Util.getHash(filePath
				+ File.separator + sortfileName, "MD5"));

		sortFileBean.addOtherAtt("FILEATTR", "0"); // 0,2
		sortFileBean.addOtherAtt("FILEFORM", "SORT_");// KJ_IMAGE
		sortFileBean.addOtherAtt("SHOWNAME", sortfileName);// KJ_IMAGE
		sortFileBean.addOtherAtt("TRUENAME", sortfileName);// KJ_IMAGE

		clientBatchFileBean.addFile(sortFileBean);

		// =========================添加文件=========================
		clientBatchBean.setIndex_Object(clientBatchIndexBean);
		clientBatchBean.addDocument_Object(clientBatchFileBean);

		SunEcmClientApi clientApi = new SunEcmClientSocketApiImpl(ECMIP, ECMPort);
		try {
			String resultMsg = clientApi.upload(clientBatchBean, groupName);
			XxlJobLogger.log("#######上传批次返回的信息[" + resultMsg + "]#######");
			XxlJobLogger.log("#######上传批次返回的信息[" + resultMsg + "]#######");
			if (resultMsg.contains("SUCCESS")){
				XxlJobLogger.log("上传成功");
				contentId = resultMsg.replace("SUCCESS<<::>>", "");
			} else if (resultMsg.contains("FAIL")){
				XxlJobLogger.log("上传失败");
				return "FAIL";
			}
		} catch (Exception e) {
			e.printStackTrace();
			return "FAIL";
		}
		return contentId;
	}
	
	
	
	public String update(String filePath, String contentID, String indexValue) {
		ClientBatchBean clientBatchBean = new ClientBatchBean();
		clientBatchBean.setModelCode(modelCode);
		clientBatchBean.setUser(username);
		clientBatchBean.setPassWord(password);
		clientBatchBean.getIndex_Object().setContentID(contentID);
		clientBatchBean.getIndex_Object().addCustomMap(indexName, indexValue);
		ClientBatchFileBean batchFileBean = new ClientBatchFileBean();
		batchFileBean.setFilePartName(filePartName);
		// // 新增一个文件
		File imagefile = new File(filePath);
		FileUtil.deleteXml(imagefile);
//		String occurDate = imagefile.getParentFile().getParentFile().getName();
		List<File> imagefiles = Arrays.asList(imagefile.listFiles());
		if (imagefile == null || imagefiles == null || imagefiles.size() == 0) {
			XxlJobLogger.log("路径下文件不存在");
			return "FAIL";
		}
		for (int i = 1; i < imagefiles.size() + 1; i++) {
			File imageFile = imagefiles.get(i-1);
			String imagepath = imageFile.getPath();
			String fileName = imageFile.getName();
			XxlJobLogger.log(contentID+"<><><>文件部件添加:"+fileName);
			// 添加FileBean
			ClientFileBean fileBean = new ClientFileBean();
			fileBean.setOptionType(OptionKey.U_ADD);
			fileBean.setFileName(imageFile.getPath());
			fileBean.setFileFormat(fileName.substring(fileName.lastIndexOf(".")+1)); //
			//fileBean.setFilesize(imageFile.length() + "");
			// fileBean.setMd5Str(MD5Util.getHash(imagepath,"MD5"));
			fileBean.addOtherAtt("SHOWNAME", i + "");// 00016-ft
			// sort_20160315043417619.xml
			fileBean.addOtherAtt("FILEFORM", "KJ_IMAGE");// KJ_IMAGE// SORT_
			fileBean.addOtherAtt("TRUENAME", fileName);
			fileBean.addOtherAtt("FILEATTR", "1"); // 0,2
			fileBean.addOtherAtt("FILEMD5", MD5Util.getHash(imagepath, "MD5"));
			batchFileBean.addFile(fileBean);
		}
		// // 替换一个文件
		// ClientFileBean clientFileBean2 = new ClientFileBean();
		// clientFileBean2.setOptionType(OptionKey.U_REPLACE);
		// clientFileBean2.setFileNO("B73A7B76-96A8-1094-806F-7730DCFFC024");
		// clientFileBean2.setFileName("D:\\1.jpg");
		// clientFileBean2.setFileFormat("jpg");
		// batchFileBean.addFile(clientFileBean2);
//
//		// 删除一个文件
//		ClientFileBean clientFileBean3 = new ClientFileBean();
//		clientFileBean3.setOptionType(OptionKey.U_DEL);
//		clientFileBean3.setFileNO("8186E0C6-FEC5-1CD5-7559-B8B6F687674F");
//		batchFileBean.addFile(clientFileBean3);
//
//		ClientFileBean clientFileBean4 = new ClientFileBean();
//		clientFileBean4.setOptionType(OptionKey.U_DEL);
//		clientFileBean4.setFileNO("E114898E-9DFE-1E39-8ED4-5C36807455B4");
//		batchFileBean.addFile(clientFileBean4);

		// // 修改文档部件字段
		// ClientFileBean clientFileBean = new ClientFileBean();
		// clientFileBean.setOptionType(OptionKey.U_MODIFY);
		// clientFileBean.setFileNO("B7F0E665-EB2E-A68C-0443-333C2BC80DC4");
		// clientFileBean.addOtherAtt("IMAGEPAGEID", "1");
		// batchFileBean.addFile(clientFileBean);
		// //
		clientBatchBean.addDocument_Object(batchFileBean);
		String resultMsg = "FAIL";
		SunEcmClientApi clientApi = new SunEcmClientSocketApiImpl(ECMIP, ECMPort);
		try {
			resultMsg = clientApi.update(clientBatchBean, groupName,true);
			XxlJobLogger.log("#######更新批次返回的信息[" + resultMsg + "]#######");
		} catch (Exception e) {
			e.printStackTrace();
		}
		return resultMsg;
	}
	
	
	
	public String queryBatch(String contentID, String indexValue) {
		String resultMsg = null;
		ClientBatchBean clientBatchBean = new ClientBatchBean();
		clientBatchBean.setModelCode(modelCode);
		clientBatchBean.setUser(username);
		clientBatchBean.setPassWord(password);
		clientBatchBean.setDownLoad(false);
		clientBatchBean.getIndex_Object().setVersion("0");
		clientBatchBean.getIndex_Object().setContentID(contentID);
		clientBatchBean.getIndex_Object().addCustomMap(indexName, indexValue);
		try {
			SunEcmClientApi clientApi = new SunEcmClientSocketApiImpl(ECMIP, ECMPort);
			resultMsg = clientApi.queryBatch(clientBatchBean, groupName);
			XxlJobLogger.log("#######查询批次返回的信息[" + resultMsg + "]#######");
		} catch (Exception e) {
			e.printStackTrace();
		}
		if(resultMsg != null){
			resultMsg = resultMsg.replace("0001<<::>>", "");
		}
		return resultMsg;
	}
	

	
	
	
	/*
	 * 高级查询（后督）
	 */
	public String heightQuery(String batchId, String indexValue) {
		String resultMsg = "FAIL";
		ClientHeightQuery heightQuery = new ClientHeightQuery();
		heightQuery.setUserName(username);
		heightQuery.setPassWord(password);
		heightQuery.setLimit(10);
		heightQuery.setPage(1);
		heightQuery.setModelCode(modelCode);
		heightQuery.addCustomAtt(indexName, indexValue);
		heightQuery.addCustomAtt("BUSI_SERIAL_NO", batchId);
		SunEcmClientApi clientApi = new SunEcmClientSocketApiImpl(ECMIP, ECMPort);
		try {
			resultMsg = clientApi.heightQuery(heightQuery, groupName);
			XxlJobLogger.log("#######调用高级搜索返回的信息[" + resultMsg + "]#######");
		} catch (Exception e) {
			e.printStackTrace();
		}
		resultMsg = resultMsg.replace("0001<<::>>", "");
		return resultMsg;
	}
	
	
	/*
	 * 高级查询（通用）
	 */
	public String heightQueryCommon(String fieldName, String batchId, String indexValue) {
		String resultMsg = "FAIL";
		ClientHeightQuery heightQuery = new ClientHeightQuery();
		heightQuery.setUserName(username);
		heightQuery.setPassWord(password);
		heightQuery.setLimit(10);
		heightQuery.setPage(1);
		heightQuery.setModelCode(modelCode);
		heightQuery.addCustomAtt(indexName, indexValue);
		heightQuery.addCustomAtt(fieldName, batchId);
		SunEcmClientApi clientApi = new SunEcmClientSocketApiImpl(ECMIP, ECMPort);
		try {
			resultMsg = clientApi.heightQuery(heightQuery, groupName);
			XxlJobLogger.log("#######调用高级搜索返回的信息[" + resultMsg + "]#######");
		} catch (Exception e) {
			e.printStackTrace();
		}
		resultMsg = resultMsg.replace("0001<<::>>", "");
		return resultMsg;
	}
	
	
	
	
	

	/**
	 * 解析QUERYBATCH方法返回的XML，提取图片信息，返回NpImageData集合
	 * <AUTHOR>
	 * 2017年7月11日
	 * @param XML
	 * @return
	 * @throws DocumentException
	 */
	@SuppressWarnings("unchecked")
	public List<NpImageData> parseXML(String XML, NpBusinessData busi) throws DocumentException {
		List<NpImageData> npImageDataList = new ArrayList<NpImageData>();
		Document document = XmlUtil.stringConvertDoc(XML);
		Element root = document.getRootElement();
		Element BatchBean = root.element("BatchBean");
		Element documentObjects = BatchBean.element("document_Objects");
		Element BatchFileBean = documentObjects.element("BatchFileBean");
		Element files = BatchFileBean.element("files");
		List<Element> FileBeanList = files.elements();
		Element FileBean = null;
		String psLevel = "1"; // 第一张是主件
		int orderNum = 1; // 图片顺序
		for (int j = 0; j < FileBeanList.size(); j++) {
			// 一个FileBean是一张图片
			NpImageData npImageData = new NpImageData();
			FileBean = FileBeanList.get(j);
//			String urlString = FileBean.attributeValue("URL");
			String fileNo = FileBean.attributeValue("FILE_NO");
			String fileFormat = FileBean.attributeValue("FILE_FORMAT");
			if(fileFormat.equals("xml")){
				continue;
			}
			String tureName = fileNo+"."+fileFormat;
			npImageData.setFileName(tureName);
			npImageData.setPsLevel(psLevel);
			npImageData.setBusiDataNo(busi.getBusiDataNo());
			npImageData.setFormName(busi.getFormName());
			npImageData.setOrderNum(orderNum+"");
			npImageDataList.add(npImageData);
			psLevel = "0";
			orderNum ++;
		}		
		return npImageDataList;
	}
	
	

	/**
	 * 
	 * @Title 
	 * @Description
	 * <AUTHOR>
	 * 2018年3月13日
	 * @param busi
	 * @param XML
	 * @param path
	 * @param type = 0  按xml解析顺序排; type = 1 PS_LEVEL 排序
	 * @return
	 * @throws Exception
	 */
	@SuppressWarnings("unchecked")
	public String dowmload(NpBusinessData busi, String XML, String path,String type) throws Exception {
		UrlUtil urlUtil = new UrlUtil();
		Document document = XmlUtil.stringConvertDoc(XML);
		Element root = document.getRootElement();
		Element BatchBean = root.element("BatchBean");
		Element documentObjects = BatchBean.element("document_Objects");
		Element BatchFileBean = documentObjects.element("BatchFileBean");
		Element files = BatchFileBean.element("files");
		List<Element> FileBeanList = files.elements();

		switch (type) {
		case "0": {
			int number = 1;
			for (int j = 0; j < FileBeanList.size(); j++) {
				Element FileBean = FileBeanList.get(j);
				String fileFormat = FileBean.attributeValue("FILE_FORMAT");
				if (fileFormat.equals("xml")) {
					continue;
				}
				String urlString = FileBean.attributeValue("URL");
				String imgPath = "";
				imgPath = path + File.separator + busi.getFlowId()+  "-" + number + "." + fileFormat;
				File file = new File(imgPath);
				if (!file.exists()) {
					file.createNewFile();
				} else {
					file.delete();
					file.createNewFile();
				}
				urlUtil.download(urlString, imgPath);
				number ++;
			}
			return "SUCCESS|下图成功";
		}
		case "1": {
			int number = 2;
			for (int j = 0; j < FileBeanList.size(); j++) {
				Element FileBean = FileBeanList.get(j);
				String fileFormat = FileBean.attributeValue("FILE_FORMAT");
				if (fileFormat.equals("xml")) {
					continue;
				}
				String urlString = FileBean.attributeValue("URL");
				String psLevel = FileBean.element("otherAtt").element("PS_LEVEL").elementText("string");
				String imgPath = "";
				if (psLevel.equals("1")) {
					imgPath = path + File.separator + busi.getFlowId()+ "-" + "1" + "." + fileFormat;
				} else {
					imgPath = path + File.separator + busi.getFlowId()+ "-" + number + "." + fileFormat;
					number++;
				}
				File file = new File(imgPath);
				if (!file.exists()) {
					file.createNewFile();
				} else {
					file.delete();
					file.createNewFile();
				}
				urlUtil.download(urlString, imgPath);
			}
		}
		}
		return "SUCCESS|下图成功";
	}
	
	/**
	 * 
	 * @Title 
	 * @Description 解析XML并且下载影像到本地，返回影像信息集合
	 * <AUTHOR>
	 * 2017年7月23日
	 * @param XML
	 * @return
	 * @throws DocumentException
	 */
	@SuppressWarnings("unchecked")
	public List<NpImageData> parseXmlAndDownload(NpBusinessData busi, String XML, String path)  {
		UrlUtil urlUtil = new UrlUtil();
		List<NpImageData> npImageDataList = new ArrayList<NpImageData>();
		try {
			Document document = XmlUtil.stringConvertDoc(XML);
			Element root = document.getRootElement();
			Element BatchBean = root.element("BatchBean");
			Element documentObjects = BatchBean.element("document_Objects");
			Element BatchFileBean = documentObjects.element("BatchFileBean");
			Element files = BatchFileBean.element("files");
			List<Element> FileBeanList = files.elements();
			Element FileBean = null;
			String psLevel = "1"; // 第一张是主件
			int orderNum = 1; // 图片顺序
			for (int j = 0; j < FileBeanList.size(); j++) {// 一个FileBean是一张图片
				/*
				 * 获取XML中的元素信息
				 */
				FileBean = FileBeanList.get(j);
				String fileFormat = FileBean.attributeValue("FILE_FORMAT");
				if(fileFormat.equals("xml")){
					continue;
				}
				String urlString = FileBean.attributeValue("URL");
				String tureName = FileBean.element("otherAtt").element("TRUENAME").elementText("string");
				
				/*
				 * 拼装影像信息集合
				 */
				NpImageData npImageData = new NpImageData();
				npImageData.setFileName(tureName);
				npImageData.setPsLevel(psLevel);
				npImageData.setBusiDataNo(busi.getBusiDataNo());
				npImageData.setFormName(busi.getFormName());
				npImageData.setOrderNum(orderNum+"");
				npImageDataList.add(npImageData);
				psLevel = "0";
				orderNum ++;
				/*
				 * 下载影像文件
				 */
				String imgPath = path + File.separator + tureName;
				File file  = new File(imgPath);
				if (!file.exists()){
					file.createNewFile();
				} else {
					file.delete();
					file.createNewFile();
				}
				urlUtil.download(urlString, imgPath);
			}
		} catch (DocumentException e) {
			e.printStackTrace();
			return null;
		} catch (IOException e) {
			e.printStackTrace();
			return null;
		} catch (Exception e) {
			e.printStackTrace();
			return null;
		}
		return npImageDataList;
	}
	
	
	
	/**
	 * 
	 * @Title 
	 * @Description
	 * <AUTHOR>
	 * 2017年7月24日
	 * @param XML
	 * @param path
	 * @return
	 */
	@SuppressWarnings("unchecked")
	public List<NpImageData> parseXml(String XML, String path)  {
		UrlUtil urlUtil = new UrlUtil();
		List<NpImageData> npImageDataList = new ArrayList<NpImageData>();
		try {
			Document document = XmlUtil.stringConvertDoc(XML);
			Element root = document.getRootElement();
			Element BatchBean = root.element("BatchBean");
			Element documentObjects = BatchBean.element("document_Objects");
			List<Element> BatchFileBeanList = documentObjects.elements();
			Element BatchFileBean = null;
			// 遍历 BatchFileBean，在孙子节点FileBean的属性中找到tureName
			for (int j = 0; j < BatchFileBeanList.size(); j++) {
				NpImageData npImageData = new NpImageData();
				BatchFileBean = BatchFileBeanList.get(j);
				Element FileBean = BatchFileBean.element("Files").element("FileBean");
				String tureName = FileBean.attribute("tureName").getValue();
				npImageData.setFileName(tureName);
				npImageDataList.add(npImageData);
				String urlString = "";
				String imgPath = path + File.separator + j ;
				File file  = new File(path);
				if (!file.exists()){
					file.createNewFile();
				}
				urlUtil.download(urlString, imgPath);
			}
		} catch (DocumentException e) {
			e.printStackTrace();
		} catch (IOException e) {
			e.printStackTrace();
		} catch (Exception e) {
			e.printStackTrace();
		}
		return npImageDataList;
	}
	
	
	
	/*
	 * 解析高级查询返回的XML，提取CONTENT_ID
	 */
	public String  parseHeightQueryXML(String XML) throws DocumentException {
		String contentId = "";
		Document document = XmlUtil.stringConvertDoc(XML);
		Element root = document.getRootElement();
		Element HeightQuery = root.element("HeightQuery");
		Element indexBeans = HeightQuery.element("indexBeans");
		Element BatchIndexBean = indexBeans.element("BatchIndexBean");
		contentId = BatchIndexBean.attributeValue("CONTENT_ID");
		return contentId;
	}



}
