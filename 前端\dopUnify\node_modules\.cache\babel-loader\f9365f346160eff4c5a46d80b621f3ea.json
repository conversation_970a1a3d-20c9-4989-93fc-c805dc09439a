{"remainingRequest": "E:\\华南区项目\\中国银行\\开发区\\科技公司运营平台\\前端\\dopUnify\\node_modules\\babel-loader\\lib\\index.js!E:\\华南区项目\\中国银行\\开发区\\科技公司运营平台\\前端\\dopUnify\\node_modules\\qiankun\\es\\sandbox\\legacy\\sandbox.js", "dependencies": [{"path": "E:\\华南区项目\\中国银行\\开发区\\科技公司运营平台\\前端\\dopUnify\\node_modules\\qiankun\\es\\sandbox\\legacy\\sandbox.js", "mtime": 1667130453000}, {"path": "E:\\华南区项目\\中国银行\\开发区\\科技公司运营平台\\前端\\dopUnify\\babel.config.js", "mtime": 1721631954662}, {"path": "E:\\华南区项目\\中国银行\\开发区\\科技公司运营平台\\前端\\dopUnify\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "E:\\华南区项目\\中国银行\\开发区\\科技公司运营平台\\前端\\dopUnify\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["_toConsumableArray", "_typeof", "_classCallCheck", "_createClass", "SandBoxType", "getTargetValue", "isPropConfigurable", "target", "prop", "descriptor", "Object", "getOwnPropertyDescriptor", "configurable", "LegacySandbox", "name", "_this", "globalContext", "arguments", "length", "undefined", "window", "addedPropsMapInSandbox", "Map", "modifiedPropsOriginalValueMapInSandbox", "currentUpdatedPropsValueMap", "proxy", "type", "sandboxRunning", "latestSetProp", "LegacyProxy", "rawWindow", "fakeWindow", "create", "setTrap", "p", "value", "originalValue", "sync2Window", "hasOwnProperty", "set", "has", "process", "env", "NODE_ENV", "console", "warn", "concat", "toString", "Proxy", "_", "get", "defineProperty", "attributes", "done", "Reflect", "key", "setWindowProp", "toDelete", "writable", "active", "_this2", "for<PERSON>ach", "v", "inactive", "_this3", "info", "keys", "default"], "sources": ["E:/华南区项目/中国银行/开发区/科技公司运营平台/前端/dopUnify/node_modules/qiankun/es/sandbox/legacy/sandbox.js"], "sourcesContent": ["import _toConsumableArray from \"@babel/runtime/helpers/esm/toConsumableArray\";\nimport _typeof from \"@babel/runtime/helpers/esm/typeof\";\nimport _classCallCheck from \"@babel/runtime/helpers/esm/classCallCheck\";\nimport _createClass from \"@babel/runtime/helpers/esm/createClass\";\nimport { SandBoxType } from '../../interfaces';\nimport { getTargetValue } from '../common';\nfunction isPropConfigurable(target, prop) {\n  var descriptor = Object.getOwnPropertyDescriptor(target, prop);\n  return descriptor ? descriptor.configurable : true;\n}\n/**\n * 基于 Proxy 实现的沙箱\n * TODO: 为了兼容性 singular 模式下依旧使用该沙箱，等新沙箱稳定之后再切换\n */\nvar LegacySandbox = /*#__PURE__*/function () {\n  function LegacySandbox(name) {\n    var _this = this;\n    var globalContext = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : window;\n    _classCallCheck(this, LegacySandbox);\n    this.addedPropsMapInSandbox = new Map();\n    this.modifiedPropsOriginalValueMapInSandbox = new Map();\n    this.currentUpdatedPropsValueMap = new Map();\n    this.name = void 0;\n    this.proxy = void 0;\n    this.globalContext = void 0;\n    this.type = void 0;\n    this.sandboxRunning = true;\n    this.latestSetProp = null;\n    this.name = name;\n    this.globalContext = globalContext;\n    this.type = SandBoxType.LegacyProxy;\n    var addedPropsMapInSandbox = this.addedPropsMapInSandbox,\n      modifiedPropsOriginalValueMapInSandbox = this.modifiedPropsOriginalValueMapInSandbox,\n      currentUpdatedPropsValueMap = this.currentUpdatedPropsValueMap;\n    var rawWindow = globalContext;\n    var fakeWindow = Object.create(null);\n    var setTrap = function setTrap(p, value, originalValue) {\n      var sync2Window = arguments.length > 3 && arguments[3] !== undefined ? arguments[3] : true;\n      if (_this.sandboxRunning) {\n        if (!rawWindow.hasOwnProperty(p)) {\n          addedPropsMapInSandbox.set(p, value);\n        } else if (!modifiedPropsOriginalValueMapInSandbox.has(p)) {\n          // 如果当前 window 对象存在该属性，且 record map 中未记录过，则记录该属性初始值\n          modifiedPropsOriginalValueMapInSandbox.set(p, originalValue);\n        }\n        currentUpdatedPropsValueMap.set(p, value);\n        if (sync2Window) {\n          // 必须重新设置 window 对象保证下次 get 时能拿到已更新的数据\n          rawWindow[p] = value;\n        }\n        _this.latestSetProp = p;\n        return true;\n      }\n      if (process.env.NODE_ENV === 'development') {\n        console.warn(\"[qiankun] Set window.\".concat(p.toString(), \" while sandbox destroyed or inactive in \").concat(name, \"!\"));\n      }\n      // 在 strict-mode 下，Proxy 的 handler.set 返回 false 会抛出 TypeError，在沙箱卸载的情况下应该忽略错误\n      return true;\n    };\n    var proxy = new Proxy(fakeWindow, {\n      set: function set(_, p, value) {\n        var originalValue = rawWindow[p];\n        return setTrap(p, value, originalValue, true);\n      },\n      get: function get(_, p) {\n        // avoid who using window.window or window.self to escape the sandbox environment to touch the really window\n        // or use window.top to check if an iframe context\n        // see https://github.com/eligrey/FileSaver.js/blob/master/src/FileSaver.js#L13\n        if (p === 'top' || p === 'parent' || p === 'window' || p === 'self') {\n          return proxy;\n        }\n        var value = rawWindow[p];\n        return getTargetValue(rawWindow, value);\n      },\n      // trap in operator\n      // see https://github.com/styled-components/styled-components/blob/master/packages/styled-components/src/constants.js#L12\n      has: function has(_, p) {\n        return p in rawWindow;\n      },\n      getOwnPropertyDescriptor: function getOwnPropertyDescriptor(_, p) {\n        var descriptor = Object.getOwnPropertyDescriptor(rawWindow, p);\n        // A property cannot be reported as non-configurable, if it does not exists as an own property of the target object\n        if (descriptor && !descriptor.configurable) {\n          descriptor.configurable = true;\n        }\n        return descriptor;\n      },\n      defineProperty: function defineProperty(_, p, attributes) {\n        var originalValue = rawWindow[p];\n        var done = Reflect.defineProperty(rawWindow, p, attributes);\n        var value = rawWindow[p];\n        setTrap(p, value, originalValue, false);\n        return done;\n      }\n    });\n    this.proxy = proxy;\n  }\n  _createClass(LegacySandbox, [{\n    key: \"setWindowProp\",\n    value: /** 沙箱期间新增的全局变量 */\n\n    /** 沙箱期间更新的全局变量 */\n\n    /** 持续记录更新的(新增和修改的)全局变量的 map，用于在任意时刻做 snapshot */\n\n    function setWindowProp(prop, value, toDelete) {\n      if (value === undefined && toDelete) {\n        // eslint-disable-next-line no-param-reassign\n        delete this.globalContext[prop];\n      } else if (isPropConfigurable(this.globalContext, prop) && _typeof(prop) !== 'symbol') {\n        Object.defineProperty(this.globalContext, prop, {\n          writable: true,\n          configurable: true\n        });\n        // eslint-disable-next-line no-param-reassign\n        this.globalContext[prop] = value;\n      }\n    }\n  }, {\n    key: \"active\",\n    value: function active() {\n      var _this2 = this;\n      if (!this.sandboxRunning) {\n        this.currentUpdatedPropsValueMap.forEach(function (v, p) {\n          return _this2.setWindowProp(p, v);\n        });\n      }\n      this.sandboxRunning = true;\n    }\n  }, {\n    key: \"inactive\",\n    value: function inactive() {\n      var _this3 = this;\n      if (process.env.NODE_ENV === 'development') {\n        console.info(\"[qiankun:sandbox] \".concat(this.name, \" modified global properties restore...\"), [].concat(_toConsumableArray(this.addedPropsMapInSandbox.keys()), _toConsumableArray(this.modifiedPropsOriginalValueMapInSandbox.keys())));\n      }\n      // renderSandboxSnapshot = snapshot(currentUpdatedPropsValueMapForSnapshot);\n      // restore global props to initial snapshot\n      this.modifiedPropsOriginalValueMapInSandbox.forEach(function (v, p) {\n        return _this3.setWindowProp(p, v);\n      });\n      this.addedPropsMapInSandbox.forEach(function (_, p) {\n        return _this3.setWindowProp(p, undefined, true);\n      });\n      this.sandboxRunning = false;\n    }\n  }]);\n  return LegacySandbox;\n}();\nexport { LegacySandbox as default };"], "mappings": ";;;;;;;;;;AAAA,OAAOA,kBAAkB,MAAM,8CAA8C;AAC7E,OAAOC,OAAO,MAAM,mCAAmC;AACvD,OAAOC,eAAe,MAAM,2CAA2C;AACvE,OAAOC,YAAY,MAAM,wCAAwC;AACjE,SAASC,WAAW,QAAQ,kBAAkB;AAC9C,SAASC,cAAc,QAAQ,WAAW;AAC1C,SAASC,kBAAkB,CAACC,MAAM,EAAEC,IAAI,EAAE;EACxC,IAAIC,UAAU,GAAGC,MAAM,CAACC,wBAAwB,CAACJ,MAAM,EAAEC,IAAI,CAAC;EAC9D,OAAOC,UAAU,GAAGA,UAAU,CAACG,YAAY,GAAG,IAAI;AACpD;AACA;AACA;AACA;AACA;AACA,IAAIC,aAAa,GAAG,aAAa,YAAY;EAC3C,SAASA,aAAa,CAACC,IAAI,EAAE;IAC3B,IAAIC,KAAK,GAAG,IAAI;IAChB,IAAIC,aAAa,GAAGC,SAAS,CAACC,MAAM,GAAG,CAAC,IAAID,SAAS,CAAC,CAAC,CAAC,KAAKE,SAAS,GAAGF,SAAS,CAAC,CAAC,CAAC,GAAGG,MAAM;IAC9FlB,eAAe,CAAC,IAAI,EAAEW,aAAa,CAAC;IACpC,IAAI,CAACQ,sBAAsB,GAAG,IAAIC,GAAG,EAAE;IACvC,IAAI,CAACC,sCAAsC,GAAG,IAAID,GAAG,EAAE;IACvD,IAAI,CAACE,2BAA2B,GAAG,IAAIF,GAAG,EAAE;IAC5C,IAAI,CAACR,IAAI,GAAG,KAAK,CAAC;IAClB,IAAI,CAACW,KAAK,GAAG,KAAK,CAAC;IACnB,IAAI,CAACT,aAAa,GAAG,KAAK,CAAC;IAC3B,IAAI,CAACU,IAAI,GAAG,KAAK,CAAC;IAClB,IAAI,CAACC,cAAc,GAAG,IAAI;IAC1B,IAAI,CAACC,aAAa,GAAG,IAAI;IACzB,IAAI,CAACd,IAAI,GAAGA,IAAI;IAChB,IAAI,CAACE,aAAa,GAAGA,aAAa;IAClC,IAAI,CAACU,IAAI,GAAGtB,WAAW,CAACyB,WAAW;IACnC,IAAIR,sBAAsB,GAAG,IAAI,CAACA,sBAAsB;MACtDE,sCAAsC,GAAG,IAAI,CAACA,sCAAsC;MACpFC,2BAA2B,GAAG,IAAI,CAACA,2BAA2B;IAChE,IAAIM,SAAS,GAAGd,aAAa;IAC7B,IAAIe,UAAU,GAAGrB,MAAM,CAACsB,MAAM,CAAC,IAAI,CAAC;IACpC,IAAIC,OAAO,GAAG,SAASA,OAAO,CAACC,CAAC,EAAEC,KAAK,EAAEC,aAAa,EAAE;MACtD,IAAIC,WAAW,GAAGpB,SAAS,CAACC,MAAM,GAAG,CAAC,IAAID,SAAS,CAAC,CAAC,CAAC,KAAKE,SAAS,GAAGF,SAAS,CAAC,CAAC,CAAC,GAAG,IAAI;MAC1F,IAAIF,KAAK,CAACY,cAAc,EAAE;QACxB,IAAI,CAACG,SAAS,CAACQ,cAAc,CAACJ,CAAC,CAAC,EAAE;UAChCb,sBAAsB,CAACkB,GAAG,CAACL,CAAC,EAAEC,KAAK,CAAC;QACtC,CAAC,MAAM,IAAI,CAACZ,sCAAsC,CAACiB,GAAG,CAACN,CAAC,CAAC,EAAE;UACzD;UACAX,sCAAsC,CAACgB,GAAG,CAACL,CAAC,EAAEE,aAAa,CAAC;QAC9D;QACAZ,2BAA2B,CAACe,GAAG,CAACL,CAAC,EAAEC,KAAK,CAAC;QACzC,IAAIE,WAAW,EAAE;UACf;UACAP,SAAS,CAACI,CAAC,CAAC,GAAGC,KAAK;QACtB;QACApB,KAAK,CAACa,aAAa,GAAGM,CAAC;QACvB,OAAO,IAAI;MACb;MACA,IAAIO,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,aAAa,EAAE;QAC1CC,OAAO,CAACC,IAAI,CAAC,uBAAuB,CAACC,MAAM,CAACZ,CAAC,CAACa,QAAQ,EAAE,EAAE,0CAA0C,CAAC,CAACD,MAAM,CAAChC,IAAI,EAAE,GAAG,CAAC,CAAC;MAC1H;MACA;MACA,OAAO,IAAI;IACb,CAAC;IACD,IAAIW,KAAK,GAAG,IAAIuB,KAAK,CAACjB,UAAU,EAAE;MAChCQ,GAAG,EAAE,SAASA,GAAG,CAACU,CAAC,EAAEf,CAAC,EAAEC,KAAK,EAAE;QAC7B,IAAIC,aAAa,GAAGN,SAAS,CAACI,CAAC,CAAC;QAChC,OAAOD,OAAO,CAACC,CAAC,EAAEC,KAAK,EAAEC,aAAa,EAAE,IAAI,CAAC;MAC/C,CAAC;MACDc,GAAG,EAAE,SAASA,GAAG,CAACD,CAAC,EAAEf,CAAC,EAAE;QACtB;QACA;QACA;QACA,IAAIA,CAAC,KAAK,KAAK,IAAIA,CAAC,KAAK,QAAQ,IAAIA,CAAC,KAAK,QAAQ,IAAIA,CAAC,KAAK,MAAM,EAAE;UACnE,OAAOT,KAAK;QACd;QACA,IAAIU,KAAK,GAAGL,SAAS,CAACI,CAAC,CAAC;QACxB,OAAO7B,cAAc,CAACyB,SAAS,EAAEK,KAAK,CAAC;MACzC,CAAC;MACD;MACA;MACAK,GAAG,EAAE,SAASA,GAAG,CAACS,CAAC,EAAEf,CAAC,EAAE;QACtB,OAAOA,CAAC,IAAIJ,SAAS;MACvB,CAAC;MACDnB,wBAAwB,EAAE,SAASA,wBAAwB,CAACsC,CAAC,EAAEf,CAAC,EAAE;QAChE,IAAIzB,UAAU,GAAGC,MAAM,CAACC,wBAAwB,CAACmB,SAAS,EAAEI,CAAC,CAAC;QAC9D;QACA,IAAIzB,UAAU,IAAI,CAACA,UAAU,CAACG,YAAY,EAAE;UAC1CH,UAAU,CAACG,YAAY,GAAG,IAAI;QAChC;QACA,OAAOH,UAAU;MACnB,CAAC;MACD0C,cAAc,EAAE,SAASA,cAAc,CAACF,CAAC,EAAEf,CAAC,EAAEkB,UAAU,EAAE;QACxD,IAAIhB,aAAa,GAAGN,SAAS,CAACI,CAAC,CAAC;QAChC,IAAImB,IAAI,GAAGC,OAAO,CAACH,cAAc,CAACrB,SAAS,EAAEI,CAAC,EAAEkB,UAAU,CAAC;QAC3D,IAAIjB,KAAK,GAAGL,SAAS,CAACI,CAAC,CAAC;QACxBD,OAAO,CAACC,CAAC,EAAEC,KAAK,EAAEC,aAAa,EAAE,KAAK,CAAC;QACvC,OAAOiB,IAAI;MACb;IACF,CAAC,CAAC;IACF,IAAI,CAAC5B,KAAK,GAAGA,KAAK;EACpB;EACAtB,YAAY,CAACU,aAAa,EAAE,CAAC;IAC3B0C,GAAG,EAAE,eAAe;IACpBpB,KAAK,EAAE;;IAEP;;IAEA;;IAEA,SAASqB,aAAa,CAAChD,IAAI,EAAE2B,KAAK,EAAEsB,QAAQ,EAAE;MAC5C,IAAItB,KAAK,KAAKhB,SAAS,IAAIsC,QAAQ,EAAE;QACnC;QACA,OAAO,IAAI,CAACzC,aAAa,CAACR,IAAI,CAAC;MACjC,CAAC,MAAM,IAAIF,kBAAkB,CAAC,IAAI,CAACU,aAAa,EAAER,IAAI,CAAC,IAAIP,OAAO,CAACO,IAAI,CAAC,KAAK,QAAQ,EAAE;QACrFE,MAAM,CAACyC,cAAc,CAAC,IAAI,CAACnC,aAAa,EAAER,IAAI,EAAE;UAC9CkD,QAAQ,EAAE,IAAI;UACd9C,YAAY,EAAE;QAChB,CAAC,CAAC;QACF;QACA,IAAI,CAACI,aAAa,CAACR,IAAI,CAAC,GAAG2B,KAAK;MAClC;IACF;EACF,CAAC,EAAE;IACDoB,GAAG,EAAE,QAAQ;IACbpB,KAAK,EAAE,SAASwB,MAAM,GAAG;MACvB,IAAIC,MAAM,GAAG,IAAI;MACjB,IAAI,CAAC,IAAI,CAACjC,cAAc,EAAE;QACxB,IAAI,CAACH,2BAA2B,CAACqC,OAAO,CAAC,UAAUC,CAAC,EAAE5B,CAAC,EAAE;UACvD,OAAO0B,MAAM,CAACJ,aAAa,CAACtB,CAAC,EAAE4B,CAAC,CAAC;QACnC,CAAC,CAAC;MACJ;MACA,IAAI,CAACnC,cAAc,GAAG,IAAI;IAC5B;EACF,CAAC,EAAE;IACD4B,GAAG,EAAE,UAAU;IACfpB,KAAK,EAAE,SAAS4B,QAAQ,GAAG;MACzB,IAAIC,MAAM,GAAG,IAAI;MACjB,IAAIvB,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,aAAa,EAAE;QAC1CC,OAAO,CAACqB,IAAI,CAAC,oBAAoB,CAACnB,MAAM,CAAC,IAAI,CAAChC,IAAI,EAAE,wCAAwC,CAAC,EAAE,EAAE,CAACgC,MAAM,CAAC9C,kBAAkB,CAAC,IAAI,CAACqB,sBAAsB,CAAC6C,IAAI,EAAE,CAAC,EAAElE,kBAAkB,CAAC,IAAI,CAACuB,sCAAsC,CAAC2C,IAAI,EAAE,CAAC,CAAC,CAAC;MAC3O;MACA;MACA;MACA,IAAI,CAAC3C,sCAAsC,CAACsC,OAAO,CAAC,UAAUC,CAAC,EAAE5B,CAAC,EAAE;QAClE,OAAO8B,MAAM,CAACR,aAAa,CAACtB,CAAC,EAAE4B,CAAC,CAAC;MACnC,CAAC,CAAC;MACF,IAAI,CAACzC,sBAAsB,CAACwC,OAAO,CAAC,UAAUZ,CAAC,EAAEf,CAAC,EAAE;QAClD,OAAO8B,MAAM,CAACR,aAAa,CAACtB,CAAC,EAAEf,SAAS,EAAE,IAAI,CAAC;MACjD,CAAC,CAAC;MACF,IAAI,CAACQ,cAAc,GAAG,KAAK;IAC7B;EACF,CAAC,CAAC,CAAC;EACH,OAAOd,aAAa;AACtB,CAAC,EAAE;AACH,SAASA,aAAa,IAAIsD,OAAO"}]}