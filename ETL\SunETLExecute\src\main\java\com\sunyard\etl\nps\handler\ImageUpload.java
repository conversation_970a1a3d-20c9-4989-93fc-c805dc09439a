package com.sunyard.etl.nps.handler;

import com.xxl.job.core.handler.annotation.JobHandler;

import org.springframework.stereotype.Service;

import com.sunyard.etl.nps.common.NPSContants;
import com.sunyard.etl.nps.common.SystemInit;
import com.sunyard.etl.nps.service.business.ImageUploadService;
import com.sunyard.etl.system.dao.JobParamDao;
import com.sunyard.etl.system.dao.impl.JobParamDaoImpl;
import com.sunyard.etl.system.model.JobParam;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.IJobHandler;
import com.xxl.job.core.log.XxlJobLogger;


@JobHandler(value="ImageUpload",name = "影像上传任务")
@Service
public  class ImageUpload extends IJobHandler{

	private static final long serialVersionUID = 1L;
	private String tableName = "simpleECM";
	
	public ReturnT<String> execute(String jobId,String... params) throws Exception {
		
		if(!NPSContants.SYSTEM_INIT){
			XxlJobLogger.log("正在进行初始化...", tableName);
			String result = SystemInit.serviceInit();
			if (result.contains("SUCCESS")) {
				NPSContants.SYSTEM_INIT = true;
			} else {
				XxlJobLogger.log("系统初始化失败", tableName);
				return ReturnT.FAIL;
			}
		}
		
		JobParamDao jobParamDao = new JobParamDaoImpl();
		JobParam jobParam = jobParamDao.JobParam(Integer.parseInt(jobId));

		ImageUploadService ser = new ImageUploadService();
		String initResult = ser.init(jobParam);
		XxlJobLogger.log(initResult, tableName);
		if (initResult.contains("FAIL")) {
			return ReturnT.FAIL;
		}
		
		String updateECMFResult = ser.updateECM();
		XxlJobLogger.log(updateECMFResult, tableName);
		if(updateECMFResult.contains("FAIL")){
			return ReturnT.FAIL;
		}
		
		return ReturnT.SUCCESS;	
	}

	public static void main(String[] args) {
		ImageUpload a = new ImageUpload();
		try {
			a.execute("112", "");
		} catch (Exception e) {
			e.printStackTrace();
		}
	}
	
}
