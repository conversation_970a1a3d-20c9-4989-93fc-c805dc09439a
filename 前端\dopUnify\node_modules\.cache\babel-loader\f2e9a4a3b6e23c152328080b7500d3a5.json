{"remainingRequest": "D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\node_modules\\babel-loader\\lib\\index.js!D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\node_modules\\eslint-loader\\index.js??ref--13-0!D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\src\\assets\\img\\icons\\index.js", "dependencies": [{"path": "D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\src\\assets\\img\\icons\\index.js", "mtime": 1686019817218}, {"path": "D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\babel.config.js", "mtime": 1686019818359}, {"path": "D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\.babelrc", "mtime": 1705285078668}, {"path": "D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\node_modules\\eslint-loader\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:aW1wb3J0ICJjb3JlLWpzL21vZHVsZXMvZXMuYXJyYXkubWFwLmpzIjsKaW1wb3J0ICJjb3JlLWpzL21vZHVsZXMvZXMub2JqZWN0LnRvLXN0cmluZy5qcyI7CmltcG9ydCAiY29yZS1qcy9tb2R1bGVzL3dlYi5kb20tY29sbGVjdGlvbnMuaXRlcmF0b3IuanMiOwppbXBvcnQgVnVlIGZyb20gJ3Z1ZSc7CmltcG9ydCBTdW5TdmdJY29uIGZyb20gJ0AvY29tcG9uZW50cy9TdW5TdmdJY29uJzsgLy8gc3ZnIGNvbXBvbmVudAoKLy8gcmVnaXN0ZXIgZ2xvYmFsbHkKVnVlLmNvbXBvbmVudCgnc3VuLXN2Zy1pY29uJywgU3VuU3ZnSWNvbik7CnZhciByZXEgPSByZXF1aXJlLmNvbnRleHQoJy4vc3ZnJywgZmFsc2UsIC9cLnN2ZyQvKTsKdmFyIHJlcXVpcmVBbGwgPSBmdW5jdGlvbiByZXF1aXJlQWxsKHJlcXVpcmVDb250ZXh0KSB7CiAgcmV0dXJuIHJlcXVpcmVDb250ZXh0LmtleXMoKS5tYXAocmVxdWlyZUNvbnRleHQpOwp9OwpyZXF1aXJlQWxsKHJlcSk7"}, {"version": 3, "names": ["<PERSON><PERSON>", "SunSvgIcon", "component", "req", "require", "context", "requireAll", "requireContext", "keys", "map"], "sources": ["D:/1_Project/XYD_Project/dop-4.0/dop-4.1-qianduan-unify/dopUnify/src/assets/img/icons/index.js"], "sourcesContent": ["import Vue from 'vue'\nimport SunSvgIcon from '@/components/SunSvgIcon'// svg component\n\n// register globally\nVue.component('sun-svg-icon', SunSvgIcon)\n\nconst req = require.context('./svg', false, /\\.svg$/)\nconst requireAll = requireContext => requireContext.keys().map(requireContext)\nrequireAll(req)\n"], "mappings": ";;;AAAA,OAAOA,GAAG,MAAM,KAAK;AACrB,OAAOC,UAAU,MAAM,yBAAyB;;AAEhD;AACAD,GAAG,CAACE,SAAS,CAAC,cAAc,EAAED,UAAU,CAAC;AAEzC,IAAME,GAAG,GAAGC,OAAO,CAACC,OAAO,CAAC,OAAO,EAAE,KAAK,EAAE,QAAQ,CAAC;AACrD,IAAMC,UAAU,GAAG,SAAbA,UAAU,CAAGC,cAAc;EAAA,OAAIA,cAAc,CAACC,IAAI,EAAE,CAACC,GAAG,CAACF,cAAc,CAAC;AAAA;AAC9ED,UAAU,CAACH,GAAG,CAAC"}]}