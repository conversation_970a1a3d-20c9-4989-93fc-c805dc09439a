{"remainingRequest": "E:\\华南区项目\\中国银行\\开发区\\科技公司运营平台\\前端\\dopUnify\\node_modules\\thread-loader\\dist\\cjs.js!E:\\华南区项目\\中国银行\\开发区\\科技公司运营平台\\前端\\dopUnify\\node_modules\\babel-loader\\lib\\index.js!E:\\华南区项目\\中国银行\\开发区\\科技公司运营平台\\前端\\dopUnify\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!E:\\华南区项目\\中国银行\\开发区\\科技公司运营平台\\前端\\dopUnify\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\华南区项目\\中国银行\\开发区\\科技公司运营平台\\前端\\dopUnify\\src\\views\\system\\config\\menu\\component\\nodeSys\\index.vue?vue&type=script&lang=js&", "dependencies": [{"path": "E:\\华南区项目\\中国银行\\开发区\\科技公司运营平台\\前端\\dopUnify\\src\\views\\system\\config\\menu\\component\\nodeSys\\index.vue", "mtime": 1721631954662}, {"path": "E:\\华南区项目\\中国银行\\开发区\\科技公司运营平台\\前端\\dopUnify\\babel.config.js", "mtime": 1721631954662}, {"path": "E:\\华南区项目\\中国银行\\开发区\\科技公司运营平台\\前端\\dopUnify\\.babelrc", "mtime": 1721631954662}, {"path": "E:\\华南区项目\\中国银行\\开发区\\科技公司运营平台\\前端\\dopUnify\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "E:\\华南区项目\\中国银行\\开发区\\科技公司运营平台\\前端\\dopUnify\\node_modules\\thread-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "E:\\华南区项目\\中国银行\\开发区\\科技公司运营平台\\前端\\dopUnify\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "E:\\华南区项目\\中国银行\\开发区\\科技公司运营平台\\前端\\dopUnify\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "E:\\华南区项目\\中国银行\\开发区\\科技公司运营平台\\前端\\dopUnify\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1655715099000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, null]}