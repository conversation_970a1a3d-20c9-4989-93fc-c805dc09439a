{"remainingRequest": "E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-q<PERSON><PERSON>n\\数字运营平台-统一门户工程\\dopUnify\\node_modules\\babel-loader\\lib\\index.js!E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan\\数字运营平台-统一门户工程\\dopUnify\\node_modules\\eslint-loader\\index.js??ref--13-0!E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan\\数字运营平台-统一门户工程\\dopUnify\\src\\store\\modules\\permission.js", "dependencies": [{"path": "E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-<PERSON><PERSON><PERSON>n\\数字运营平台-统一门户工程\\dopUnify\\src\\store\\modules\\permission.js", "mtime": 1712806340320}, {"path": "E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-<PERSON><PERSON><PERSON>n\\数字运营平台-统一门户工程\\dopUnify\\babel.config.js", "mtime": 1686019818359}, {"path": "E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-<PERSON><PERSON><PERSON>n\\数字运营平台-统一门户工程\\dopUnify\\.babelrc", "mtime": 1705285078668}, {"path": "E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-<PERSON><PERSON><PERSON>n\\数字运营平台-统一门户工程\\dopUnify\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-<PERSON><PERSON><PERSON>n\\数字运营平台-统一门户工程\\dopUnify\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-<PERSON><PERSON><PERSON>n\\数字运营平台-统一门户工程\\dopUnify\\node_modules\\eslint-loader\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, null]}