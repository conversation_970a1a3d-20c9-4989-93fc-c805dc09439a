{"remainingRequest": "E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-q<PERSON><PERSON>n\\数字运营平台-统一门户工程\\dopUnify\\node_modules\\thread-loader\\dist\\cjs.js!E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan\\数字运营平台-统一门户工程\\dopUnify\\node_modules\\babel-loader\\lib\\index.js!E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan\\数字运营平台-统一门户工程\\dopUnify\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan\\数字运营平台-统一门户工程\\dopUnify\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-q<PERSON>duan\\数字运营平台-统一门户工程\\dopUnify\\src\\layout\\extend\\index.vue?vue&type=script&lang=js&", "dependencies": [{"path": "E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-<PERSON><PERSON><PERSON>n\\数字运营平台-统一门户工程\\dopUnify\\src\\layout\\extend\\index.vue", "mtime": 1703583640644}, {"path": "E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-<PERSON><PERSON><PERSON>n\\数字运营平台-统一门户工程\\dopUnify\\babel.config.js", "mtime": 1686019818359}, {"path": "E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-<PERSON><PERSON><PERSON>n\\数字运营平台-统一门户工程\\dopUnify\\.babelrc", "mtime": 1705285078668}, {"path": "E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-<PERSON><PERSON><PERSON>n\\数字运营平台-统一门户工程\\dopUnify\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-<PERSON><PERSON><PERSON>n\\数字运营平台-统一门户工程\\dopUnify\\node_modules\\thread-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-<PERSON><PERSON><PERSON>n\\数字运营平台-统一门户工程\\dopUnify\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-<PERSON><PERSON><PERSON>n\\数字运营平台-统一门户工程\\dopUnify\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-<PERSON><PERSON><PERSON>n\\数字运营平台-统一门户工程\\dopUnify\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1655715099000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:aW1wb3J0IF9vYmplY3RTcHJlYWQgZnJvbSAiRTovMV9Qcm9qZWN0L1hZRF9Qcm9qZWN0L2RvcC00LjAvZG9wLTQuMS1xaWFuZHVhbi9cdTY1NzBcdTVCNTdcdThGRDBcdTg0MjVcdTVFNzNcdTUzRjAtXHU3RURGXHU0RTAwXHU5NUU4XHU2MjM3XHU1REU1XHU3QTBCL2RvcFVuaWZ5L25vZGVfbW9kdWxlcy9AYmFiZWwvcnVudGltZS9oZWxwZXJzL2VzbS9vYmplY3RTcHJlYWQyLmpzIjsKaW1wb3J0IF9kZWZpbmVQcm9wZXJ0eSBmcm9tICJFOi8xX1Byb2plY3QvWFlEX1Byb2plY3QvZG9wLTQuMC9kb3AtNC4xLXFpYW5kdWFuL1x1NjU3MFx1NUI1N1x1OEZEMFx1ODQyNVx1NUU3M1x1NTNGMC1cdTdFREZcdTRFMDBcdTk1RThcdTYyMzdcdTVERTVcdTdBMEIvZG9wVW5pZnkvbm9kZV9tb2R1bGVzL0BiYWJlbC9ydW50aW1lL2hlbHBlcnMvZXNtL2RlZmluZVByb3BlcnR5LmpzIjsKaW1wb3J0IF9yZWdlbmVyYXRvclJ1bnRpbWUgZnJvbSAiRTovMV9Qcm9qZWN0L1hZRF9Qcm9qZWN0L2RvcC00LjAvZG9wLTQuMS1xaWFuZHVhbi9cdTY1NzBcdTVCNTdcdThGRDBcdTg0MjVcdTVFNzNcdTUzRjAtXHU3RURGXHU0RTAwXHU5NUU4XHU2MjM3XHU1REU1XHU3QTBCL2RvcFVuaWZ5L25vZGVfbW9kdWxlcy9AYmFiZWwvcnVudGltZS9oZWxwZXJzL2VzbS9yZWdlbmVyYXRvclJ1bnRpbWUuanMiOwppbXBvcnQgX2FzeW5jVG9HZW5lcmF0b3IgZnJvbSAiRTovMV9Qcm9qZWN0L1hZRF9Qcm9qZWN0L2RvcC00LjAvZG9wLTQuMS1xaWFuZHVhbi9cdTY1NzBcdTVCNTdcdThGRDBcdTg0MjVcdTVFNzNcdTUzRjAtXHU3RURGXHU0RTAwXHU5NUU4XHU2MjM3XHU1REU1XHU3QTBCL2RvcFVuaWZ5L25vZGVfbW9kdWxlcy9AYmFiZWwvcnVudGltZS9oZWxwZXJzL2VzbS9hc3luY1RvR2VuZXJhdG9yLmpzIjsKaW1wb3J0ICJjb3JlLWpzL21vZHVsZXMvZXMuYXJyYXkuZmluZC5qcyI7CmltcG9ydCAiY29yZS1qcy9tb2R1bGVzL2VzLm9iamVjdC50by1zdHJpbmcuanMiOwppbXBvcnQgImNvcmUtanMvbW9kdWxlcy9lcy5hcnJheS5zbGljZS5qcyI7CmltcG9ydCAiY29yZS1qcy9tb2R1bGVzL2VzLmFycmF5LmNvbmNhdC5qcyI7CmltcG9ydCAiY29yZS1qcy9tb2R1bGVzL2VzLmpzb24uc3RyaW5naWZ5LmpzIjsKLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KCi8vIGltcG9ydCB7IFRhZ3NWaWV3IH0gZnJvbSAnQC9sYXlvdXQvY29tcG9uZW50cycKaW1wb3J0IFN1bkZsb3dEaWFsb2cgZnJvbSAnQC9jb21wb25lbnRzL0RpYWxvZy9TdW5GbG93RGlhbG9nJzsgLy8g5rWB56iL6K+m5oOF5by556qXCmltcG9ydCBzdG9yZSBmcm9tICdAL3N0b3JlJzsKaW1wb3J0IHsgY29tbW9uQmxhbmsgfSBmcm9tICdAL3V0aWxzL2NvbW1vbic7CmltcG9ydCB7IGRpY3Rpb25hcnlGaWVkcyB9IGZyb20gJ0AvdXRpbHMvZGljdGlvbmFyeSc7CmltcG9ydCBzY3JlZW5mdWxsIGZyb20gJ3NjcmVlbmZ1bGwnOwppbXBvcnQgeyBDb21tb24gfSBmcm9tICdAL2FwaSc7CnZhciBmaWVsZENvbnRyb2xsZXIgPSBDb21tb24uZmllbGRDb250cm9sbGVyOwpleHBvcnQgZGVmYXVsdCB7CiAgbmFtZTogJ0V4dGVuZCcsCiAgY29tcG9uZW50czogewogICAgU3VuRmxvd0RpYWxvZzogU3VuRmxvd0RpYWxvZwogIH0sCiAgZGF0YTogZnVuY3Rpb24gZGF0YSgpIHsKICAgIHJldHVybiB7CiAgICAgIGlmcmFtZVVybDogJycsCiAgICAgIHBhdGhTcmM6ICcnLAogICAgICAvLyDlrZDlt6XnqIvlnLDlnYAKICAgICAgZXh0ZW5kUmVmOiAnZXh0ZW5kUmVmJyArIG5ldyBEYXRlKCkuZ2V0VGltZSgpLAogICAgICBpc0NoaWxkcmVuUmVhZHk6IGZhbHNlLAogICAgICBwcm9qZWN0TmFtZTogJycsCiAgICAgIC8vIOWtkOmhueebruWQjeensAogICAgICBmbG93Q29uZmlnOiB7CiAgICAgICAgLy8g5rWB56iL5qih5p2/6LCD55So5by556qXCiAgICAgICAgdmlzaWJsZTogZmFsc2UsCiAgICAgICAgY29tcG9uZW50UHJvcHM6IHsKICAgICAgICAgIC8vIOW8ueWHuuahhuWxnuaApwogICAgICAgICAgdGl0bGU6ICcnLAogICAgICAgICAgLy8g5by55Ye65qGG5qCH6aKYCiAgICAgICAgICB0b3A6ICcwcHgnLAogICAgICAgICAgd2lkdGg6ICcxMDAlJyAvLyDlvZPliY3lvLnlh7rmoYblrr3luqYg6buY6K6kODAlCiAgICAgICAgfSwKCiAgICAgICAgbW9kdWxlX2lkOiAnJywKICAgICAgICAvLyDmqKHmnb9pZAogICAgICAgIHRhc2tfaWQ6ICcnLAogICAgICAgIC8vIOa1geeoi2lkCiAgICAgICAgY2hpbGRGbG93VXJsOiAnc3RhdGljL2h0bWwvZmxvdy93b3JrZmxvdy5odG1sJyAvLyDlrZDlt6XnqIvmtYHnqIvpobXpnaJ1cmwKICAgICAgfSwKCiAgICAgIGluZGNEaWFsb2c6IHsKICAgICAgICAvLyDmjIfmoIflvLnmoYYKICAgICAgICB2aXNpYmxlOiBmYWxzZSwKICAgICAgICB0aXRsZTogJycsCiAgICAgICAgdXJsOiAnJwogICAgICB9LAogICAgICBzeXN0ZW1OdW06ICcnCiAgICB9OwogIH0sCiAgd2F0Y2g6IHsKICAgIGlzQ2hpbGRyZW5SZWFkeTogZnVuY3Rpb24gaXNDaGlsZHJlblJlYWR5KHZhbCkgewogICAgICBpZiAodmFsKSB7CiAgICAgICAgY29uc29sZS5sb2coJ2lzQ2hpbGRyZW5SZWFkeScsIHZhbCk7CiAgICAgICAgdGhpcy5wb3N0TWVzcygpOwogICAgICB9CiAgICB9CiAgfSwKICBtb3VudGVkOiBmdW5jdGlvbiBtb3VudGVkKCkgewogICAgdmFyIF90aGlzID0gdGhpczsKICAgIHZhciBjdXJyVmlldyA9IHRoaXMuJHN0b3JlLmdldHRlcnMuY2FjaGVkVmlld3MucG9wKCk7CiAgICB2YXIgbWFwRnJhbWUgPSB0aGlzLiRyZWZzW3RoaXMuZXh0ZW5kUmVmXTsKICAgIHZhciBpZnJhbWVXaW4gPSBtYXBGcmFtZS5jb250ZW50V2luZG93OwogICAgdGhpcy4kc3RvcmUuY29tbWl0KCdhcHAvQ0hBTkdFX0xPQURJTkcnLCB0cnVlKTsKICAgIHRoaXMuJHN0b3JlLmNvbW1pdCgnZmxvd1BhdGgvU0VUX0VYVEVORF9MSVNUJywgewogICAgICB0aXRsZTogY3VyclZpZXcsCiAgICAgIGlmcmFtZVdpbjogaWZyYW1lV2luCiAgICB9KTsKICAgIHRoaXMuJG5leHRUaWNrKCkudGhlbihmdW5jdGlvbiAoKSB7CiAgICAgIF90aGlzLiRyb3V0ZXIub25SZWFkeSggLyojX19QVVJFX18qL19hc3luY1RvR2VuZXJhdG9yKCAvKiNfX1BVUkVfXyovX3JlZ2VuZXJhdG9yUnVudGltZSgpLm1hcmsoZnVuY3Rpb24gX2NhbGxlZSgpIHsKICAgICAgICB2YXIgcGF0aEluZGV4LCByb3V0ZU1lc3NhZ2UsIHBhdGgsIHN5c3RlbV9ubywgZGVwbE5hbWUsIHF1ZXJ5X2NvbmRpdGlvbnMsIHN5c3RlbURpYywgZGVwbE5hbWVsYWJlbCwgYmVmb3JlUGF0aDsKICAgICAgICByZXR1cm4gX3JlZ2VuZXJhdG9yUnVudGltZSgpLndyYXAoZnVuY3Rpb24gX2NhbGxlZSQoX2NvbnRleHQpIHsKICAgICAgICAgIHdoaWxlICgxKSB7CiAgICAgICAgICAgIHN3aXRjaCAoX2NvbnRleHQucHJldiA9IF9jb250ZXh0Lm5leHQpIHsKICAgICAgICAgICAgICBjYXNlIDA6CiAgICAgICAgICAgICAgICBwYXRoSW5kZXggPSBfdGhpcy4kcm91dGUubWF0Y2hlZC5sZW5ndGggLSAxOwogICAgICAgICAgICAgICAgcm91dGVNZXNzYWdlID0gX3RoaXMuJHJvdXRlLm1hdGNoZWRbcGF0aEluZGV4XS5wcm9wcy5kZWZhdWx0OwogICAgICAgICAgICAgICAgcGF0aCA9IHJvdXRlTWVzc2FnZS5wYXRoLCBzeXN0ZW1fbm8gPSByb3V0ZU1lc3NhZ2Uuc3lzdGVtX25vLCBkZXBsTmFtZSA9IHJvdXRlTWVzc2FnZS5kZXBsTmFtZSwgcXVlcnlfY29uZGl0aW9ucyA9IHJvdXRlTWVzc2FnZS5xdWVyeV9jb25kaXRpb25zOwogICAgICAgICAgICAgICAgX3RoaXMuJHN0b3JlLmNvbW1pdCgnY29tbW9uL1NFVF9GTE9XX0NPTkRJVElPTicsIHF1ZXJ5X2NvbmRpdGlvbnMpOwogICAgICAgICAgICAgICAgX3RoaXMuc3lzdGVtTnVtID0gc3lzdGVtX25vOyAvLyDns7vnu5/lj7cKICAgICAgICAgICAgICAgIC8vIOiOt+WPluWkluezu+e7n+iPnOWNlSDmlbDmja7lrZflhbgKICAgICAgICAgICAgICAgIGlmIChzeXN0ZW1fbm8gaW4gX3RoaXMuJHN0b3JlLnN0YXRlLmNvbW1vbi5kaWN0aW9uYXJ5TGV0KSB7CiAgICAgICAgICAgICAgICAgIF9jb250ZXh0Lm5leHQgPSAxMzsKICAgICAgICAgICAgICAgICAgYnJlYWs7CiAgICAgICAgICAgICAgICB9CiAgICAgICAgICAgICAgICBzeXN0ZW1EaWMgPSBKU09OLnBhcnNlKGxvY2FsU3RvcmFnZS5nZXRJdGVtKF90aGlzLnN5c3RlbU51bSkpOwogICAgICAgICAgICAgICAgaWYgKCFjb21tb25CbGFuayhzeXN0ZW1EaWMpKSB7CiAgICAgICAgICAgICAgICAgIF9jb250ZXh0Lm5leHQgPSAxMjsKICAgICAgICAgICAgICAgICAgYnJlYWs7CiAgICAgICAgICAgICAgICB9CiAgICAgICAgICAgICAgICBfY29udGV4dC5uZXh0ID0gMTA7CiAgICAgICAgICAgICAgICByZXR1cm4gX3RoaXMuZ2V0RGljdGlvbmFyeURhdGEoc3lzdGVtX25vKTsKICAgICAgICAgICAgICBjYXNlIDEwOgogICAgICAgICAgICAgICAgX2NvbnRleHQubmV4dCA9IDEzOwogICAgICAgICAgICAgICAgYnJlYWs7CiAgICAgICAgICAgICAgY2FzZSAxMjoKICAgICAgICAgICAgICAgIF90aGlzLiRzdG9yZS5jb21taXQoJ2NvbW1vbi9BRERfRElDVElPTkFSWUxFVCcsIHsKICAgICAgICAgICAgICAgICAgc3lzdGVtTm86IF90aGlzLnN5c3RlbU51bSwKICAgICAgICAgICAgICAgICAgdmFsdWU6IHN5c3RlbURpYywKICAgICAgICAgICAgICAgICAgZGljdGlvbmFyeTogSlNPTi5wYXJzZShsb2NhbFN0b3JhZ2UuZ2V0SXRlbShfdGhpcy5zeXN0ZW1OdW0gKyAnZGljdGlvbmFyeScpKQogICAgICAgICAgICAgICAgfSk7CiAgICAgICAgICAgICAgY2FzZSAxMzoKICAgICAgICAgICAgICAgIC8vIGh0dHA6Ly8xNzIuMS4xMS41MTo5NTI4L3JlZGlyZWN0L3JlcG9ydC8jL3JlcG9ydC9kYXRhU2V0CiAgICAgICAgICAgICAgICAvLyBodHRwOi8vMTcyLjEuMS4yMS9kb3BVbmlmeS9yZWRpcmVjdC9yZXBvcnQvIy9yZXBvcnQvZGF0YVNldAogICAgICAgICAgICAgICAgX3RoaXMucGF0aFNyYyA9IHBhdGg7CiAgICAgICAgICAgICAgICBpZiAoIWNvbW1vbkJsYW5rKHBhdGgpKSB7CiAgICAgICAgICAgICAgICAgIHN0b3JlLmNvbW1pdCgndXNlci9TRVRfUk9VVEVfTScsIHJvdXRlTWVzc2FnZSk7CiAgICAgICAgICAgICAgICAgIGlmICghY29tbW9uQmxhbmsoZGVwbE5hbWUpKSB7CiAgICAgICAgICAgICAgICAgICAgLy8g5pyJ5YGa5Luj55CGCiAgICAgICAgICAgICAgICAgICAgZGVwbE5hbWVsYWJlbCA9IGRpY3Rpb25hcnlGaWVkcygnRVhUX1NZU19ERVBMX05BTUUnKS5maW5kKGZ1bmN0aW9uIChpdGVtKSB7CiAgICAgICAgICAgICAgICAgICAgICByZXR1cm4gaXRlbS52YWx1ZSA9PT0gZGVwbE5hbWU7CiAgICAgICAgICAgICAgICAgICAgfSkubGFiZWw7CiAgICAgICAgICAgICAgICAgICAgYmVmb3JlUGF0aCA9ICcnOyAvLyDliKTmlq3mmK9pZei/mOaYr2Nocm9tZQogICAgICAgICAgICAgICAgICAgIGlmIChuYXZpZ2F0b3IudXNlckFnZW50LmluZGV4T2YoJ0Nocm9tZScpICE9PSAtMSkgewogICAgICAgICAgICAgICAgICAgICAgLy8g5b2T5YmN5rWP6KeI5Zmo5pivIENocm9tZQogICAgICAgICAgICAgICAgICAgICAgYmVmb3JlUGF0aCA9ICIiLmNvbmNhdCh3aW5kb3cubG9jYXRpb24ub3JpZ2luKTsKICAgICAgICAgICAgICAgICAgICB9IGVsc2UgaWYgKG5hdmlnYXRvci51c2VyQWdlbnQuaW5kZXhPZignVHJpZGVudCcpICE9PSAtMSkgewogICAgICAgICAgICAgICAgICAgICAgLy8g5b2T5YmN5rWP6KeI5Zmo5pivIElFCiAgICAgICAgICAgICAgICAgICAgICBiZWZvcmVQYXRoID0gd2luZG93LmxvY2F0aW9uLmhyZWYuc2xpY2UoMCwgd2luZG93LmxvY2F0aW9uLmhyZWYuaW5kZXhPZih3aW5kb3cubG9jYXRpb24ucGF0aG5hbWUpKTsKICAgICAgICAgICAgICAgICAgICB9CiAgICAgICAgICAgICAgICAgICAgaWYgKHBhdGguaW5kZXhPZignc3RhdGljL2h0bWwnKSAhPT0gLTEpIHsKICAgICAgICAgICAgICAgICAgICAgIC8vIOWFvOWuueiAgeWfuue6v3N1bmFvcwogICAgICAgICAgICAgICAgICAgICAgLy8gaHR0cDovLzE3Mi4xLjEuMTAxL2RvcFVuaWZ5L3JlZGlyZWN0L3VuaWZ5L2lmcmFtZS5odG1sCiAgICAgICAgICAgICAgICAgICAgICBfdGhpcy5pZnJhbWVVcmwgPSAiIi5jb25jYXQoYmVmb3JlUGF0aCkuY29uY2F0KHdpbmRvdy5sb2NhdGlvbi5wYXRobmFtZSwgInJlZGlyZWN0LyIpLmNvbmNhdChkZXBsTmFtZWxhYmVsLCAiL2lmcmFtZS5odG1sIik7CiAgICAgICAgICAgICAgICAgICAgICAvLyB0aGlzLmlmcmFtZVVybCA9IGBodHRwOi8vMTcyLjEuMTAuMTg1L2RvcFVuaWZ5L3JlZGlyZWN0LyR7ZGVwbE5hbWVsYWJlbH0vaWZyYW1lLmh0bWxgCiAgICAgICAgICAgICAgICAgICAgfSBlbHNlIHsKICAgICAgICAgICAgICAgICAgICAgIF90aGlzLmlmcmFtZVVybCA9ICIiLmNvbmNhdChiZWZvcmVQYXRoKS5jb25jYXQod2luZG93LmxvY2F0aW9uLnBhdGhuYW1lLCAicmVkaXJlY3QvIikuY29uY2F0KGRlcGxOYW1lbGFiZWwsICIvIy8iKS5jb25jYXQocGF0aCk7CiAgICAgICAgICAgICAgICAgICAgICAvLyB0aGlzLmlmcmFtZVVybCA9IGBodHRwOi8vMTcyLjE2LjE1LjgvZG9wVW5pZnkvcmVkaXJlY3QvJHtkZXBsTmFtZWxhYmVsfS8jLyR7cGF0aH1gCiAgICAgICAgICAgICAgICAgICAgfQogICAgICAgICAgICAgICAgICB9IGVsc2UgewogICAgICAgICAgICAgICAgICAgIC8vIOacrOWcsOa1i+ivlQogICAgICAgICAgICAgICAgICAgIF90aGlzLmlmcmFtZVVybCA9IHBhdGg7CiAgICAgICAgICAgICAgICAgIH0KICAgICAgICAgICAgICAgICAgLy8gdGhpcy5leHRlbmRSZWYgPSB0aGlzLmV4dGVuZFJlZiArIHRoaXMuJHJvdXRlLnBhdGgKICAgICAgICAgICAgICAgICAgLy8gY29uc29sZS5sb2coJ3RoaXMuaWZyYW1lVXJsOicsIHRoaXMuaWZyYW1lVXJsKQogICAgICAgICAgICAgICAgfQoKICAgICAgICAgICAgICAgIF90aGlzLnBvc3RQYXJhbSgpOwogICAgICAgICAgICAgIGNhc2UgMTY6CiAgICAgICAgICAgICAgY2FzZSAiZW5kIjoKICAgICAgICAgICAgICAgIHJldHVybiBfY29udGV4dC5zdG9wKCk7CiAgICAgICAgICAgIH0KICAgICAgICAgIH0KICAgICAgICB9LCBfY2FsbGVlKTsKICAgICAgfSkpKTsKICAgIH0pOwogIH0sCiAgLy8g6ZSA5q+B5a+55bqU6Ieq5a6a5LmJ5LqL5Lu2CiAgYmVmb3JlRGVzdHJveTogZnVuY3Rpb24gYmVmb3JlRGVzdHJveSgpIHsKICAgIGNsZWFyVGltZW91dCh0aGlzLnRpbWVvdXQxKTsKICAgIGNsZWFyVGltZW91dCh0aGlzLnRpbWVvdXQyKTsKICAgIGNsZWFyVGltZW91dCh0aGlzLnRpbWVvdXQzKTsKICB9LAogIG1ldGhvZHM6IHsKICAgIC8qKgogICAgICog5YWz6Zet5by55qGGCiAgICAgKi8KICAgIGhhbmRsZUNsb3NlOiBmdW5jdGlvbiBoYW5kbGVDbG9zZSgpIHsKICAgICAgdGhpcy5pbmRjRGlhbG9nLnZpc2libGUgPSBmYWxzZTsKICAgIH0sCiAgICAvKioKICAgICAqIOiOt+WPluaVsOaNruWtl+WFuAogICAgICovCiAgICBnZXREaWN0aW9uYXJ5RGF0YTogZnVuY3Rpb24gZ2V0RGljdGlvbmFyeURhdGEoc3lzdGVtTm8pIHsKICAgICAgdmFyIF90aGlzMiA9IHRoaXM7CiAgICAgIHJldHVybiBuZXcgUHJvbWlzZShmdW5jdGlvbiAocmVzb2x2ZSkgewogICAgICAgIHZhciBtc2cgPSB7CiAgICAgICAgICBwYXJhbWV0ZXJMaXN0OiBbJyddLAogICAgICAgICAgZmllbGRUaW1lOiAnJywKICAgICAgICAgIG9wZXJUeXBlOiAnMScsCiAgICAgICAgICAvLyDpl6jmiLfmk43kvZzmoIfor4YKICAgICAgICAgIHN5c3RlbU5vOiBzeXN0ZW1ObwogICAgICAgIH07CiAgICAgICAgZmllbGRDb250cm9sbGVyKG1zZykudGhlbihmdW5jdGlvbiAocmVzKSB7CiAgICAgICAgICAvLyDmlrDlop7lpJbns7vnu5/lrZflhbgKICAgICAgICAgIGxvY2FsU3RvcmFnZS5zZXRJdGVtKHN5c3RlbU5vLCBKU09OLnN0cmluZ2lmeShyZXMucmV0TWFwW3N5c3RlbU5vXSkpOwogICAgICAgICAgbG9jYWxTdG9yYWdlLnNldEl0ZW0oc3lzdGVtTm8gKyAnZGljdGlvbmFyeScsIEpTT04uc3RyaW5naWZ5KF9kZWZpbmVQcm9wZXJ0eSh7fSwgc3lzdGVtTm8sIHJlcy5yZXRNYXAuZGljdGlvbmFyeVtzeXN0ZW1Ob10pKSk7CiAgICAgICAgICBfdGhpczIuJHN0b3JlLmNvbW1pdCgnY29tbW9uL0FERF9ESUNUSU9OQVJZTEVUJywgewogICAgICAgICAgICBzeXN0ZW1Obzogc3lzdGVtTm8sCiAgICAgICAgICAgIHZhbHVlOiByZXMucmV0TWFwW3N5c3RlbU5vXSwKICAgICAgICAgICAgZGljdGlvbmFyeTogcmVzLnJldE1hcC5kaWN0aW9uYXJ5CiAgICAgICAgICB9KTsKICAgICAgICAgIHJlc29sdmUoKTsKICAgICAgICB9KTsKICAgICAgfSk7CiAgICB9LAogICAgcG9zdE1lc3M6IGZ1bmN0aW9uIHBvc3RNZXNzKCkgewogICAgICB2YXIgdGhhdCA9IHRoaXM7CiAgICAgIC8vIHNldFRpbWVvdXQoKCkgPT4gewogICAgICB2YXIgbWFwRnJhbWUgPSB0aGlzLiRyZWZzW3RoaXMuZXh0ZW5kUmVmXTsKICAgICAgdGhhdC4kc3RvcmUuY29tbWl0KCdhcHAvQ0hBTkdFX0xPQURJTkcnLCBmYWxzZSk7CiAgICAgIC8vIG1hcEZyYW1lLm9ubG9hZCA9IGZ1bmN0aW9uKCkgewogICAgICAvLyAgIGNvbnNvbGUubG9nKCdtYXBGcmFtZW1hcEZyYW1lbWFwRnJhbWUtLW9ubG9hZCcpCiAgICAgIC8vICAgdGhhdC4kc3RvcmUuY29tbWl0KCdhcHAvQ0hBTkdFX0xPQURJTkcnLCBmYWxzZSkKICAgICAgLy8gfQogICAgICB2YXIgaWZyYW1lV2luID0gbWFwRnJhbWUuY29udGVudFdpbmRvdzsKCiAgICAgIC8vIOWQiOW5tueItuWtkOW3peeoi+aVsOaNruWtl+WFuCBiZWdpbgogICAgICB2YXIgY29tbW9uRGF0YSA9IEpTT04ucGFyc2UoSlNPTi5zdHJpbmdpZnkodGhpcy4kc3RvcmUuc3RhdGUuY29tbW9uKSk7CiAgICAgIGlmICh0aGlzLnN5c3RlbU51bSAhPT0gJ1VOSUZZJykgewogICAgICAgIHZhciBkaWN0aW9uYXJ5UCA9IEpTT04ucGFyc2UoSlNPTi5zdHJpbmdpZnkodGhpcy4kc3RvcmUuc3RhdGUuY29tbW9uLmRpY3Rpb25hcnlMZXQpKTsKICAgICAgICB2YXIgZGljdGlvbmFyeVBVbmZpeSA9IHt9OwogICAgICAgIGZvciAodmFyIGtleSBpbiBkaWN0aW9uYXJ5UCkgewogICAgICAgICAgaWYgKGtleSA9PT0gdGhpcy5zeXN0ZW1OdW0pIHsKICAgICAgICAgICAgZGljdGlvbmFyeVBVbmZpeSA9IF9vYmplY3RTcHJlYWQoX29iamVjdFNwcmVhZCh7fSwgZGljdGlvbmFyeVBbJ1VOSUZZJ10pLCBkaWN0aW9uYXJ5UFtrZXldKTsKICAgICAgICAgIH0KICAgICAgICB9CiAgICAgICAgdmFyIGNoaWxkU3lzdGVtTm8gPSAnJzsKICAgICAgICAvLyDljZXni6zkuLrmjIfmoIfkv67mlLnkvKDlgLzmqKHlvI8KICAgICAgICBpZiAodGhpcy5wcm9qZWN0TmFtZSA9PT0gJ2luZGljYXRvcicpIHsKICAgICAgICAgIGNoaWxkU3lzdGVtTm8gPSAnQU9TJzsKICAgICAgICAgIGNvbW1vbkRhdGEuZGljdGlvbmFyeUxldCA9IE9iamVjdC5hc3NpZ24oe30sIGNvbW1vbkRhdGEuZGljdGlvbmFyeUxldCwgX2RlZmluZVByb3BlcnR5KHt9LCBjaGlsZFN5c3RlbU5vLCBkaWN0aW9uYXJ5UFVuZml5KSk7CiAgICAgICAgfSBlbHNlIHsKICAgICAgICAgIGNoaWxkU3lzdGVtTm8gPSAnVU5JRlknOwogICAgICAgICAgY29tbW9uRGF0YS5kaWN0aW9uYXJ5TGV0ID0gT2JqZWN0LmFzc2lnbih7fSwgY29tbW9uRGF0YS5kaWN0aW9uYXJ5TGV0LCBfZGVmaW5lUHJvcGVydHkoe30sIGNoaWxkU3lzdGVtTm8sIGRpY3Rpb25hcnlQVW5maXkpKTsKICAgICAgICAgIGNvbW1vbkRhdGEgPSBPYmplY3QuYXNzaWduKHt9LCBjb21tb25EYXRhLCB7CiAgICAgICAgICAgIGRpY3Rpb25hcnlUcmVlOiBjb21tb25EYXRhLmRpY3Rpb25hcnlUcmVlW3RoaXMuc3lzdGVtTnVtXQogICAgICAgICAgfSk7CiAgICAgICAgfQogICAgICAgIGlmIChjaGlsZFN5c3RlbU5vID09PSAnVU5JRlknKSB7CiAgICAgICAgICBmb3IgKHZhciBpdGVtIGluIGNvbW1vbkRhdGEuZGljdGlvbmFyeUxldCkgewogICAgICAgICAgICBpZiAoaXRlbSAhPT0gJ1VOSUZZJyAmJiBpdGVtICE9PSAnZGljdGlvbmFyeScgJiYgaXRlbSAhPT0gJ2ZpZWxkVGltZScpIHsKICAgICAgICAgICAgICBkZWxldGUgY29tbW9uRGF0YS5kaWN0aW9uYXJ5TGV0W2l0ZW1dOwogICAgICAgICAgICB9CiAgICAgICAgICB9CiAgICAgICAgfSBlbHNlIHsKICAgICAgICAgIGlmICh0aGlzLnByb2plY3ROYW1lICE9PSAnaW5kaWNhdG9yJykgewogICAgICAgICAgICBmb3IgKHZhciBfaXRlbSBpbiBjb21tb25EYXRhLmRpY3Rpb25hcnlMZXQpIHsKICAgICAgICAgICAgICBpZiAoX2l0ZW0gIT09IHRoaXMuc3lzdGVtTnVtICYmIF9pdGVtICE9PSAnZGljdGlvbmFyeScgJiYgX2l0ZW0gIT09ICdmaWVsZFRpbWUnKSB7CiAgICAgICAgICAgICAgICBkZWxldGUgY29tbW9uRGF0YS5kaWN0aW9uYXJ5TGV0W19pdGVtXTsgLy8g6Zeo5oi35bel56iL6ZyA6KaB55So5Yiw6Zeo5oi34oCY6YOo572y5bel56iL4oCZ55qE5a2X5YW456CB77yM5LiN6IO95Yig6ZmkCiAgICAgICAgICAgICAgfQogICAgICAgICAgICB9CiAgICAgICAgICB9CiAgICAgICAgfQogICAgICB9CiAgICAgIC8vIOWQiOW5tueItuWtkOW3peeoi+aVsOaNruWtl+WFuCBlbmQKCiAgICAgIGlmIChpZnJhbWVXaW4pIHsKICAgICAgICB2YXIgbXNnID0gewogICAgICAgICAgdXNlcjogdGhpcy4kc3RvcmUuc3RhdGUudXNlciwKICAgICAgICAgIGNvbW1vbjogY29tbW9uRGF0YSwKICAgICAgICAgIHR5cGU6ICdkb3BVbmlmeScsCiAgICAgICAgICBwcm9qZWN0TmFtZTogdGhpcy5wcm9qZWN0TmFtZQogICAgICAgIH07CiAgICAgICAgY29uc29sZS5sb2coJ+WQiOW5tueItuWtkOW3peeoi+aVsOaNruWtl+WFuCcsIHdpbmRvdy5sb2NhdGlvbi5vcmlnaW4pOwogICAgICAgIGlmcmFtZVdpbi5wb3N0TWVzc2FnZSgKICAgICAgICAvLyDlkJHlrZDlt6XnqIvlj5HpgIEKICAgICAgICBKU09OLnBhcnNlKEpTT04uc3RyaW5naWZ5KG1zZykpLCBwcm9jZXNzLmVudi5OT0RFX0VOViA9PT0gJ2RldmVsb3BtZW50JyA/ICcqJyA6IHdpbmRvdy5sb2NhdGlvbi5vcmlnaW4pOwogICAgICB9CiAgICAgIC8vIH0sIDEwMDApCiAgICB9LAogICAgLyoqCiAgICAgKiDlkJFpZnJhbWXlrZDpobXpnaLlj5HpgIHmtojmga8KICAgICAqLwogICAgcG9zdFBhcmFtOiBmdW5jdGlvbiBwb3N0UGFyYW0oKSB7CiAgICAgIC8vIHdpbmRvdy5hZGRFdmVudExpc3RlbmVyKCdtZXNzYWdlJywgdGhpcy5nZXRDaGlsZERhdGEpCiAgICAgIHdpbmRvdy5vbm1lc3NhZ2UgPSB0aGlzLmdldENoaWxkRGF0YTsKICAgIH0sCiAgICAvKioKICAgICAqCiAgICAgKiDojrflj5blrZDlt6XnqIvnmoTmlbDmja4KICAgICAqLwogICAgZ2V0Q2hpbGREYXRhOiBmdW5jdGlvbiBnZXRDaGlsZERhdGEoZXZ0KSB7CiAgICAgIHZhciBfdGhpczMgPSB0aGlzOwogICAgICAvLyBjb25zb2xlLmxvZygn5o6l5pS25a2Q5bel56iL5pWw5o2uJywgZXZ0LCA2NjY2LCBldnQuZGF0YS50eXBlKQogICAgICAvLyDmjqXmlLblrZDlt6XnqIvmlbDmja4KICAgICAgLy8g6Iul5a2Q6aG16Z2i5bey57uP5Yqg6L295aW96YCa55+l54i25bel56iL5L+u5pS55LqGaXNDaGlsZHJlblJlYWR555qE54q25oCBCiAgICAgIGNvbnNvbGUubG9nKCfojrflj5blrZDlt6XnqIvnmoTmlbDmja4nLCBldnQpOwogICAgICBpZiAoZXZ0LmRhdGEudHlwZSA9PT0gJ2RvcFVuaWZ5JykgewogICAgICAgIHRoaXMuaXNDaGlsZHJlblJlYWR5ID0gdHJ1ZTsKICAgICAgICB0aGlzLnByb2plY3ROYW1lID0gZXZ0LmRhdGEucHJvamVjdE5hbWU7CiAgICAgIH0gZWxzZSBpZiAoZXZ0LmRhdGEudHlwZSA9PT0gJ3NhdmVEYXRhJykgewogICAgICAgIC8vIOWtkOW3peeoi+eahOaVsOaNruWtmOWCqAogICAgICAgIHRoaXMuJHN0b3JlLmNvbW1pdCgnY29tbW9uL1NFVF9TQVZFX0RBVEEnLCB7CiAgICAgICAgICBrZXk6IGV2dC5kYXRhLnByb2plY3ROYW1lLAogICAgICAgICAgZGF0YTogZXZ0LmRhdGEuZGF0YQogICAgICAgIH0pOwogICAgICB9IGVsc2UgaWYgKGV2dC5kYXRhLnR5cGUgPT09ICd1bmlmeUZsb3cnKSB7CiAgICAgICAgLy8g5rWB56iL5by55Ye65qGGCiAgICAgICAgdGhpcy5kaWFsb2dGbG93KGV2dC5kYXRhLmRhdGEpOwogICAgICB9IGVsc2UgaWYgKGV2dC5kYXRhLnR5cGUgPT09ICdjbG9zZUN1cnJlbnRUYWInIHx8IGV2dC5kYXRhLnR5cGUgPT09ICdjbG9zZUN1cnJlbnREaWFsb2cnKSB7CiAgICAgICAgLy8g5YWz6Zet5b2T5YmN6aG1CiAgICAgICAgLy8gd2luZG93LmNsb3NlQ3VycmVudFRhYigpCiAgICAgICAgZm9yICh2YXIgaSBpbiB0aGlzLiRzdG9yZS5nZXR0ZXJzLnZpc2l0ZWRWaWV3cykgewogICAgICAgICAgdmFyIHRlbXAgPSB0aGlzLiRzdG9yZS5nZXR0ZXJzLnZpc2l0ZWRWaWV3c1tpXTsKICAgICAgICAgIGlmICh0ZW1wLnBhdGggPT09IHRoaXMuJHJvdXRlLnBhdGgpIHsKICAgICAgICAgICAgdGhpcy4kc3RvcmUuY29tbWl0KCdjb21tb24vVGVzdCcsIHRlbXApOwogICAgICAgICAgfQogICAgICAgIH0KICAgICAgfSBlbHNlIGlmIChldnQuZGF0YS50eXBlID09PSAnY2xvc2VGbG93RGlhbG9nJyAmJiBldnQuZGF0YS5wcm9qZWN0TmFtZSAhPT0gJ3N1bmFvcycpIHsKICAgICAgICB0aGlzLmRpYWxvZ0Zsb3dDbG9zZSgpOwogICAgICB9IGVsc2UgaWYgKGV2dC5kYXRhLnR5cGUgPT09ICdvcGVuSW5kYycpIHsKICAgICAgICAvLyDmn6XnnIvmjIfmoIfmmI7nu4YKICAgICAgICB2YXIgdHlwZSA9IGV2dC5kYXRhLmRhdGEudGl0bGUuaW5kZXhPZign6KGA57yYJykgIT09IC0xID8gZXZ0LmRhdGEuZGF0YS5pbmRjVHlwZSAhPT0gJzInID8gJ2Jsb29kJyA6ICdibG9vZE5ldycgOiAnY2hlY2tEZXRhaWwnOwogICAgICAgIHZhciBwYXRoID0gJ2luZGljYXRvcnMvaW5kaWNhdG9ycy9jeWNsZS9pbmRpY2F0b3JzL2N5Y2xlLycgKyB0eXBlOwogICAgICAgIHRoaXMuaW5kY0RpYWxvZy51cmwgPSAiIi5jb25jYXQod2luZG93LmxvY2F0aW9uLm9yaWdpbikuY29uY2F0KHdpbmRvdy5sb2NhdGlvbi5wYXRobmFtZSwgInJlZGlyZWN0L2RhcEluZGljYXRvci8jLyIpLmNvbmNhdChwYXRoKTsKICAgICAgICB0aGlzLmluZGNEaWFsb2cuaGVpZ2h0ID0gd2luZG93LmlubmVySGVpZ2h0IC0gNTUgLSA2MiAtIDMyIC0gNjsKICAgICAgICB0aGlzLmluZGNEaWFsb2cudGl0bGUgPSBldnQuZGF0YS5kYXRhLnRpdGxlOwogICAgICAgIHRoaXMuaW5kY0RpYWxvZy52aXNpYmxlID0gdHJ1ZTsKICAgICAgICB0aGlzLnRpbWVvdXQzID0gc2V0VGltZW91dChmdW5jdGlvbiAoKSB7CiAgICAgICAgICB2YXIgbWFwRnJhbWUgPSBkb2N1bWVudC5xdWVyeVNlbGVjdG9yKCcjaWRjJyk7CiAgICAgICAgICB2YXIgaWZyYW1lV2luID0gbWFwRnJhbWUuY29udGVudFdpbmRvdzsKICAgICAgICAgIGlmIChpZnJhbWVXaW4pIHsKICAgICAgICAgICAgdmFyIG1zZyA9IHsKICAgICAgICAgICAgICB1c2VyOiBfdGhpczMuJHN0b3JlLnN0YXRlLnVzZXIsCiAgICAgICAgICAgICAgY29tbW9uOiBfdGhpczMuJHN0b3JlLnN0YXRlLmNvbW1vbiwKICAgICAgICAgICAgICBpbmRjTm86IGV2dC5kYXRhLmRhdGEuaW5kY05vcywKICAgICAgICAgICAgICBpbmRjVHlwZTogZXZ0LmRhdGEuZGF0YS5pbmRjVHlwZSwKICAgICAgICAgICAgICBpbmRjVXJsOiB3aW5kb3cubG9jYXRpb24ub3JpZ2luICsgd2luZG93LmxvY2F0aW9uLnBhdGhuYW1lICsgJ3JlZGlyZWN0L2RhcFZpc3VhbGl0eScsCiAgICAgICAgICAgICAgdHlwZTogJ2RvcFVuaWZ5JywKICAgICAgICAgICAgICBwcm9qZWN0TmFtZTogJ2luZGljYXRvcicKICAgICAgICAgICAgfTsKICAgICAgICAgICAgaWZyYW1lV2luLnBvc3RNZXNzYWdlKAogICAgICAgICAgICAvLyDlkJHlrZDlt6XnqIvlj5HpgIEKICAgICAgICAgICAgSlNPTi5wYXJzZShKU09OLnN0cmluZ2lmeShtc2cpKSwgcHJvY2Vzcy5lbnYuTk9ERV9FTlYgPT09ICdkZXZlbG9wbWVudCcgPyAnKicgOiB3aW5kb3cubG9jYXRpb24ub3JpZ2luKTsKICAgICAgICAgIH0KICAgICAgICB9LCAxMDAwKTsKICAgICAgfSBlbHNlIGlmIChldnQuZGF0YS50eXBlID09PSAnb3BlbkRhcCcpIHsKICAgICAgICAvLyDlj6/op4bljJYKICAgICAgICB2YXIgX3BhdGggPSAicmVwb3J0L3ByZXZpZXc/Ii5jb25jYXQoZW5jb2RlVVJJQ29tcG9uZW50KEpTT04uc3RyaW5naWZ5KGV2dC5kYXRhLmRhdGEucXVlcnkpKSk7CiAgICAgICAgLy8gY29uc3QgcGF0aCA9IGByZXBvcnQvZGF0YVNldGAKICAgICAgICB0aGlzLmluZGNEaWFsb2cudXJsID0gIiIuY29uY2F0KHdpbmRvdy5sb2NhdGlvbi5vcmlnaW4pLmNvbmNhdCh3aW5kb3cubG9jYXRpb24ucGF0aG5hbWUsICJyZWRpcmVjdC9kYXBWaXN1YWxpdHkvIy8iKS5jb25jYXQoX3BhdGgpOwogICAgICAgIC8vIHRoaXMuaW5kY0RpYWxvZy51cmwgPSBgaHR0cDovL2xvY2FsaG9zdDo1MDAzLyMvcmVwb3J0L3ByZXZpZXc/JHtlbmNvZGVVUklDb21wb25lbnQoCiAgICAgICAgLy8gICBKU09OLnN0cmluZ2lmeShldnQuZGF0YS5kYXRhLnF1ZXJ5KQogICAgICAgIC8vICl9YAogICAgICAgIC8vIHRoaXMuaW5kY0RpYWxvZy51cmwgPSBgaHR0cDovL2xvY2FsaG9zdDo1MDAzLyMvcmVwb3J0L2RhdGFTZXRgCgogICAgICAgIHRoaXMuaW5kY0RpYWxvZy5oZWlnaHQgPSB3aW5kb3cuaW5uZXJIZWlnaHQgLSA1NSAtIDYyIC0gMzIgLSA2OwogICAgICAgIHRoaXMuaW5kY0RpYWxvZy50aXRsZSA9IGV2dC5kYXRhLmRhdGEudGl0bGU7CiAgICAgICAgdGhpcy5pbmRjRGlhbG9nLnZpc2libGUgPSB0cnVlOwogICAgICAgIHRoaXMudGltZW91dDEgPSBzZXRUaW1lb3V0KGZ1bmN0aW9uICgpIHsKICAgICAgICAgIHZhciBtYXBGcmFtZSA9IGRvY3VtZW50LnF1ZXJ5U2VsZWN0b3IoJyNpZGMnKTsKICAgICAgICAgIHZhciBpZnJhbWVXaW4gPSBtYXBGcmFtZS5jb250ZW50V2luZG93OwogICAgICAgICAgaWYgKGlmcmFtZVdpbikgewogICAgICAgICAgICB2YXIgbXNnID0gewogICAgICAgICAgICAgIHVzZXI6IF90aGlzMy4kc3RvcmUuc3RhdGUudXNlciwKICAgICAgICAgICAgICBjb21tb246IF90aGlzMy4kc3RvcmUuc3RhdGUuY29tbW9uLAogICAgICAgICAgICAgIHR5cGU6ICdkb3BVbmlmeScsCiAgICAgICAgICAgICAgcHJvamVjdE5hbWU6ICd2aXN1YWwnCiAgICAgICAgICAgIH07CiAgICAgICAgICAgIGlmcmFtZVdpbi5wb3N0TWVzc2FnZSgKICAgICAgICAgICAgLy8g5ZCR5a2Q5bel56iL5Y+R6YCBCiAgICAgICAgICAgIEpTT04ucGFyc2UoSlNPTi5zdHJpbmdpZnkobXNnKSksIHByb2Nlc3MuZW52Lk5PREVfRU5WID09PSAnZGV2ZWxvcG1lbnQnID8gJyonIDogd2luZG93LmxvY2F0aW9uLm9yaWdpbik7CiAgICAgICAgICB9CiAgICAgICAgfSwgMTAwMCk7CiAgICAgIH0gZWxzZSBpZiAoZXZ0LmRhdGEudHlwZSA9PT0gJ2Z1bGxzY3JlZW4nKSB7CiAgICAgICAgLy8g6ICB5Z+657q/LeiAg+ivleeuoeeQhi3lhajlsY8KICAgICAgICBpZiAoIXNjcmVlbmZ1bGwuaXNGdWxsc2NyZWVuICYmIGV2dC5kYXRhLmRhdGEub3BlcmF0aW9uID09PSAnb3BlbicpIHsKICAgICAgICAgIC8vIOWFqOWxjwogICAgICAgICAgdGhpcy5yZXNpemVIYW5kbGVyKCk7CiAgICAgICAgfSBlbHNlIGlmIChzY3JlZW5mdWxsLmlzRnVsbHNjcmVlbiAmJiBldnQuZGF0YS5kYXRhLm9wZXJhdGlvbiA9PT0gJ2Nsb3NlJykgewogICAgICAgICAgLy8g6YCA5Ye65YWo5bGPCiAgICAgICAgICBzY3JlZW5mdWxsLmV4aXQoKTsKICAgICAgICB9CiAgICAgIH0gZWxzZSBpZiAoZXZ0LmRhdGEudHlwZSA9PT0gJ2dvTG9naW4nKSB7CiAgICAgICAgLy8g5aaC5p6c5Z+657q/dG9rZW4gIOmXqOaIt+i3s+i9rOeZu+W9lemhtQogICAgICAgIHRoaXMuJHN0b3JlLmRpc3BhdGNoKCd1c2VyL3Jlc2V0VG9rZW4nKS50aGVuKGZ1bmN0aW9uICgpIHsKICAgICAgICAgIGxvY2F0aW9uLnJlbG9hZCgpOwogICAgICAgIH0pOwogICAgICB9IGVsc2UgaWYgKGV2dC5kYXRhLnR5cGUgPT09ICdvcGVuQmFzZWxpbmVzUGFnZScpIHsKICAgICAgICB2YXIgZGF0YXMgPSBldnQuZGF0YS5kYXRhOwogICAgICAgIHZhciByb3V0ZXIgPSB0aGlzLiRzdG9yZS5nZXR0ZXJzLnBlcm1pc3Npb25fcm91dGVzOyAvLyDmiYDmnInoj5zljZUKICAgICAgICB2YXIgbWhNZW51SWQgPSB0aGlzLiRzdG9yZS5nZXR0ZXJzLm1lbnVBcnIuZmluZChmdW5jdGlvbiAoaXRlbSkgewogICAgICAgICAgcmV0dXJuIGl0ZW0ubWVudV9uYW1lLmluZGV4T2YoZGF0YXMuZmlyc3QpID4gLTEgJiYgaXRlbS5tZW51X2F0dHIgPT09ICcxJzsKICAgICAgICB9KTsKICAgICAgICB2YXIgZ2dNZW51SWQgPSB0aGlzLiRzdG9yZS5nZXR0ZXJzLm1lbnVBcnIuZmluZChmdW5jdGlvbiAoaXRlbSkgewogICAgICAgICAgcmV0dXJuIGl0ZW0ubWVudV9uYW1lLmluZGV4T2YoZGF0YXMuc2Vjb25kKSA+IC0xICYmIGl0ZW0ubWVudV9hdHRyID09PSAnMic7CiAgICAgICAgfSk7CiAgICAgICAgdmFyIGNxTWVudUlkID0gdGhpcy4kc3RvcmUuZ2V0dGVycy5tZW51QXJyLmZpbmQoZnVuY3Rpb24gKGl0ZW0pIHsKICAgICAgICAgIHJldHVybiBpdGVtLm1lbnVfbmFtZS5pbmRleE9mKGRhdGFzLnRoaXJkKSA+IC0xICYmIGl0ZW0ubWVudV9hdHRyID09PSAnMic7CiAgICAgICAgfSk7CiAgICAgICAgdmFyIHVuaWZ5UiA9IHJvdXRlci5maW5kKGZ1bmN0aW9uIChpdGVtKSB7CiAgICAgICAgICByZXR1cm4gaXRlbS5tZW51X2lkID09PSBtaE1lbnVJZC5tZW51X2lkOwogICAgICAgIH0pOwogICAgICAgIHZhciBub3RpY2VSID0gdW5pZnlSLmNoaWxkcmVuLmZpbmQoZnVuY3Rpb24gKGl0ZW0pIHsKICAgICAgICAgIHJldHVybiBpdGVtLm1lbnVfaWQgPT09IGdnTWVudUlkLm1lbnVfaWQ7CiAgICAgICAgfSk7CiAgICAgICAgdmFyIHF1ZXJ5UiA9IG5vdGljZVIuY2hpbGRyZW4uZmluZChmdW5jdGlvbiAoaXRlbSkgewogICAgICAgICAgcmV0dXJuIGl0ZW0ubWVudV9pZCA9PT0gY3FNZW51SWQubWVudV9pZDsKICAgICAgICB9KTsKICAgICAgICB0aGlzLiRyb3V0ZXIucHVzaCh1bmlmeVIucGF0aCArICcvJyArIG5vdGljZVIucGF0aCArICcvJyArIHF1ZXJ5Ui5wYXRoKTsKICAgICAgfQogICAgfSwKICAgIC8qKgogICAgICog5rWB56iL5by55Ye65qGG77yaZmxvd0NvbmZpZwogICAgICovCiAgICBkaWFsb2dGbG93OiBmdW5jdGlvbiBkaWFsb2dGbG93KGRhdGEpIHsKICAgICAgaWYgKGRhdGEuZmxvd0RhdGEpIHsKICAgICAgICB0aGlzLmZsb3dDb25maWcuZmxvd0RhdGEgPSBkYXRhLmZsb3dEYXRhOwogICAgICB9CiAgICAgIHZhciBtb2R1bGVfaWQgPSBkYXRhLm1vZHVsZV9pZCwKICAgICAgICBjb21wb25lbnRQcm9wcyA9IGRhdGEuY29tcG9uZW50UHJvcHMsCiAgICAgICAgY2hpbGRGbG93VXJsID0gZGF0YS5jaGlsZEZsb3dVcmw7CiAgICAgIHRoaXMuZmxvd0NvbmZpZy5jb21wb25lbnRQcm9wcy50aXRsZSA9IGNvbXBvbmVudFByb3BzLnRpdGxlOwogICAgICB0aGlzLmZsb3dDb25maWcubW9kdWxlX2lkID0gbW9kdWxlX2lkOwogICAgICB0aGlzLmZsb3dDb25maWcudmlzaWJsZSA9IHRydWU7CiAgICAgIHRoaXMuZmxvd0NvbmZpZy5jaGlsZEZsb3dVcmwgPSBjaGlsZEZsb3dVcmw7CiAgICB9LAogICAgLyoqCiAgICAgKiDliKTmlq3mmK/lkKblhajlsY86IOWfuue6vy3ogIPor5XnrqHnkIYt5oiR55qE6ICD6K+VCiAgICAgKi8KICAgIHJlc2l6ZUhhbmRsZXI6IGZ1bmN0aW9uIHJlc2l6ZUhhbmRsZXIoKSB7CiAgICAgIC8vIOWFqOWxj+S4i+ebkeaOp+aYr+WQpuaMiemUruS6hkVTQwogICAgICBpZiAoIXNjcmVlbmZ1bGwuaXNGdWxsc2NyZWVuKSB7CiAgICAgICAgLy8g5YWo5bGP5LiL5oyJ6ZSuZXNj5ZCO6KaB5omn6KGM55qE5Yqo5L2cCiAgICAgICAgaWYgKHRoaXMucGF0aFNyYy5pbmRleE9mKCdzdGF0aWMvaHRtbC9leGFtL2V4YW0vbXlFeGFtLmh0bWwnKSAhPT0gLTEpIHsKICAgICAgICAgIHZhciBtYXBGcmFtZSA9IHRoaXMuJHJlZnNbdGhpcy5leHRlbmRSZWZdOwogICAgICAgICAgdmFyIGlmcmFtZVdpbiA9IG1hcEZyYW1lLmNvbnRlbnRXaW5kb3c7CiAgICAgICAgICBpZiAoaWZyYW1lV2luKSB7CiAgICAgICAgICAgIHZhciBtc2cgPSB7CiAgICAgICAgICAgICAgdHlwZTogJ2RvcFVuaWZ5JywKICAgICAgICAgICAgICBwcm9qZWN0TmFtZTogJ2V4YW0nLAogICAgICAgICAgICAgIGRhdGE6ICcnCiAgICAgICAgICAgIH07CiAgICAgICAgICAgIGlmcmFtZVdpbi5wb3N0TWVzc2FnZSgKICAgICAgICAgICAgLy8g5ZCR5a2Q5bel56iL5Y+R6YCBCiAgICAgICAgICAgIEpTT04ucGFyc2UoSlNPTi5zdHJpbmdpZnkobXNnKSksICcqJyk7CiAgICAgICAgICB9CiAgICAgICAgfQogICAgICB9CiAgICB9LAogICAgLy8g5rWB56iL5by556qX5YWz6ZetCiAgICBkaWFsb2dGbG93Q2xvc2U6IGZ1bmN0aW9uIGRpYWxvZ0Zsb3dDbG9zZSgpIHsKICAgICAgdmFyIF90aGlzNCA9IHRoaXM7CiAgICAgIHZhciBtYXBGcmFtZSA9IHRoaXMuJHJlZnNbdGhpcy5leHRlbmRSZWZdOwogICAgICB2YXIgaWZyYW1lV2luID0gbWFwRnJhbWUuY29udGVudFdpbmRvdzsKICAgICAgdmFyIGlmcmFtZUluZm9EYXRhID0gdGhpcy5mbG93Q29uZmlnOwogICAgICB2YXIgYW9zSWZyYW1lV2luID0gdGhpcy4kcmVmc1snZmxvd0RpYWxvZyddLiRyZWZzWydyZWZEaWFsb2cnXS4kZWwucXVlcnlTZWxlY3RvcignaWZyYW1lJykuY29udGVudFdpbmRvdzsKICAgICAgYW9zSWZyYW1lV2luLnBvc3RNZXNzYWdlKHsKICAgICAgICB0eXBlOiAnZG9wVW5pZnknLAogICAgICAgIHByb2plY3ROYW1lOiAncmVzZXRGbG93JywKICAgICAgICB0YXNrX2lkOiB0aGlzLmZsb3dDb25maWcudGFza19pZAogICAgICB9LCBwcm9jZXNzLmVudi5OT0RFX0VOViA9PT0gJ2RldmVsb3BtZW50JyA/ICcqJyA6IHdpbmRvdy5sb2NhdGlvbi5vcmlnaW4pOwogICAgICBpZiAoaWZyYW1lSW5mb0RhdGEuZmxvd0RhdGEpIHsKICAgICAgICBpZiAoaWZyYW1lSW5mb0RhdGEuZmxvd0RhdGEuYXBwcm92ZVR5cGUpIHsKICAgICAgICAgIC8vIAkvLyDmjIfmoIfkuIvnur8KICAgICAgICAgIC8vIGlmIChpZnJhbWVJbmZvRGF0YS5hbGxkYXRhLmZsb3dEYXRhLmFwcHJvdmVUeXBlID09JzInKSB7CiAgICAgICAgICAvLyAJbWFpbkNvbnQuZmxvd0RlYWxEZXRhaWxTaG93KGlmcmFtZUluZm9EYXRhLmFsbGRhdGEuZmxvd0RhdGEpOwogICAgICAgICAgLy8gfWVsc2V7CiAgICAgICAgICAvLyAJCS8vIOaMh+agh+WPkeW4gwogICAgICAgICAgLy8gCW1haW5Db250LmluZFB1Ymxpc2goaWZyYW1lSW5mb0RhdGEuYWxsZGF0YS5mbG93RGF0YSk7CiAgICAgICAgICAvLyB9CiAgICAgICAgICBpZnJhbWVXaW4ucG9zdE1lc3NhZ2UoewogICAgICAgICAgICB0eXBlOiAnY2xvc2VEaWFsb2ctaW5kJwogICAgICAgICAgfSwgcHJvY2Vzcy5lbnYuTk9ERV9FTlYgPT09ICdkZXZlbG9wbWVudCcgPyAnKicgOiB3aW5kb3cubG9jYXRpb24ub3JpZ2luKTsKICAgICAgICB9IGVsc2UgewogICAgICAgICAgLy8gbWFpbkNvbnQucGFnZUFwcGx5KGlmcmFtZUluZm9EYXRhLmFsbGRhdGEuZmxvd0RhdGEpCiAgICAgICAgICBpZnJhbWVXaW4ucG9zdE1lc3NhZ2UoewogICAgICAgICAgICB0eXBlOiAnY2xvc2VEaWFsb2ctdmlzJwogICAgICAgICAgfSwgcHJvY2Vzcy5lbnYuTk9ERV9FTlYgPT09ICdkZXZlbG9wbWVudCcgPyAnKicgOiB3aW5kb3cubG9jYXRpb24ub3JpZ2luKTsKICAgICAgICB9CiAgICAgIH0KICAgICAgdGhpcy50aW1lb3V0MiA9IHNldFRpbWVvdXQoZnVuY3Rpb24gKCkgewogICAgICAgIF90aGlzNC4kc3RvcmUuc3RhdGUuY29tbW9uLmZsb3cubW9kdWxlX2lkID0gJyc7IC8vIOe9ruepuua1geeoi0lECiAgICAgICAgLy8gdGhpcy4kc3RvcmUuc3RhdGUuY29tbW9uLmZsb3cud29ya2Zsb3dfZGF0YVN0ci5mbG93X3R5cGUgPSAnYWRkJwogICAgICAgIF90aGlzNC5mbG93Q29uZmlnLnZpc2libGUgPSBmYWxzZTsKICAgICAgfSwgMTAwMCk7CiAgICAgIC8vIHdpbmRvdy5yZW1vdmVFdmVudExpc3RlbmVyKCdtZXNzYWdlJywgdGhpcy5nZXRDaGlsZERhdGEpCiAgICB9LAogICAgLyoqCiAgICAgKiDmjIfmoIfkuIvnur/ljoblj7LvvIzlj5HluIPljoblj7Lor6bmg4XlvLnmoYblhbPpl60KICAgICAqLwogICAgY2hhbmdlVmlzaWJsZTogZnVuY3Rpb24gY2hhbmdlVmlzaWJsZSgpIHsKICAgICAgdGhpcy5kaWFsb2dEZXRhaWwudmlzaWJsZSA9IGZhbHNlOwogICAgfQogIH0KfTs="}, null]}