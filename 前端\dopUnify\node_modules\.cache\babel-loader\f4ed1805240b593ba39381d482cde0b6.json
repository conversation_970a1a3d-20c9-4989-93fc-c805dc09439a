{"remainingRequest": "E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-q<PERSON><PERSON>n\\数字运营平台-统一门户工程\\dopUnify\\node_modules\\babel-loader\\lib\\index.js!E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan\\数字运营平台-统一门户工程\\dopUnify\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan\\数字运营平台-统一门户工程\\dopUnify\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan\\数字运营平台-统一门户工程\\dopUnify\\src\\layout\\index.vue?vue&type=script&lang=js&", "dependencies": [{"path": "E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-<PERSON><PERSON><PERSON>n\\数字运营平台-统一门户工程\\dopUnify\\src\\layout\\index.vue", "mtime": 1705285078368}, {"path": "E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-<PERSON><PERSON><PERSON>n\\数字运营平台-统一门户工程\\dopUnify\\babel.config.js", "mtime": 1686019818359}, {"path": "E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-<PERSON><PERSON><PERSON>n\\数字运营平台-统一门户工程\\dopUnify\\.babelrc", "mtime": 1705285078668}, {"path": "E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-<PERSON><PERSON><PERSON>n\\数字运营平台-统一门户工程\\dopUnify\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-<PERSON><PERSON><PERSON>n\\数字运营平台-统一门户工程\\dopUnify\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-<PERSON><PERSON><PERSON>n\\数字运营平台-统一门户工程\\dopUnify\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-<PERSON><PERSON><PERSON>n\\数字运营平台-统一门户工程\\dopUnify\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1655715099000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAyCA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;EACAA;EACAC;IACAC;IACAC;IACAC;IACAC;EACA;EACAC;EACAC;IACA;MACAC;MAAA;MACAC;MACAC;IACA;EACA;;EACAC,0CACAC;IACAC;MAAA;IAAA;IACAC;MAAA;IAAA;IACA;IACAC;MAAA;IAAA;IACAC;MAAA;IAAA;EACA;IACAC;MACA;QACAC;QACAC;QACAC;QACAC;MACA;IACA;IACAC;MACA,IACA,8BACA,4CACAC,2CACA;QACA;MACA;QACA;QACA;QACA;QACA;QACA;QACA;MACA;IACA;EAAA,EACA;EACAC;IACA;IACA;MACA;IACA;IACA;IACA;IACA;IACA;IACA;EACA;EACAC;IACA;IACA;IACA;IACAC;EACA;EACAC;IACA;IACAC;MAAA;MACA;MACA;QACAC,mBACA,gCACA,MACA;UACAC;UACAC;UACAC;UACAC;QACA,EACA;UACA;QACA;QACA;QACAC;UACA;QACA;MACA;QACA;QACA;QACA;QACA;UACAC;QACA;UACAA;QACA;UACAA;QACA;UACAA;QACA;QACAN;UACAC;UACAC;UACAC;UACAC;UACAG;QACA;MACA;QACA;MACA;IACA;IACA;IACAC;MACA;MACA;MACA;IACA;IAEA;IACAC;MACAZ;MACA;MACAa;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;;MAEA;MACAC,6BACAC,kOACA,QACA,gBACA,aACA;IACA;IACA;AACA;IACAC;MACA;MACA;QAAAtB;MAAA;IACA;IACA;AACA;IACAuB;MAAA;MAAA;QAAA;UAAA;YAAA;cAAA;gBAAA;gBAAA,OACA;cAAA;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IACA;EACA;AACA", "names": ["name", "components", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Sidebar", "TagsView", "mixins", "data", "process", "proName", "menuDir", "computed", "mapState", "sidebar", "device", "needTagsView", "fixedHeader", "classObj", "hideSidebar", "openSidebar", "withoutAnimation", "mobile", "isChanged", "commonBlank", "created", "<PERSON><PERSON><PERSON><PERSON>", "clearTimeout", "methods", "wsMessage", "MessageBox", "confirmButtonText", "showClose", "showCancelButton", "type", "timeout", "boxType", "closeOnClickModal", "wsError", "requstWs", "closeWebsocket", "sendWebsocket", "pathUrl", "handleClickOutside", "logout"], "sourceRoot": "src/layout", "sources": ["index.vue"], "sourcesContent": ["<template>\n  <div\n    :class=\"classObj\"\n    class=\"app-wrapper\"\n    :style=\"{\n      // background: $store.state.settings.isDefault\n      //   ? $store.state.settings.defaultCorlor\n      //   : isChanged\n    }\"\n  >\n    <div\n      v-if=\"device === 'mobile' && sidebar.opened\"\n      class=\"drawer-bg\"\n      @click=\"handleClickOutside\"\n    />\n    <!-- 菜单区域 -->\n    <sidebar\n      v-if=\"process === 'development'\"\n      :class=\"[menuDir === 'left' ? 'sidebar-container' : 'topbar-container']\"\n    />\n    <!-- 内容区域 -->\n    <div\n      :class=\"{\n        hasTagsView: needTagsView,\n        'main-container': process === 'development',\n        'is-menu-top': menuDir === 'top'\n      }\"\n    >\n      <div\n        v-if=\"process === 'development'\"\n        :class=\"{ 'fixed-header': fixedHeader }\"\n      >\n        <navbar />\n        <tags-view v-if=\"needTagsView\" />\n      </div>\n      <app-main />\n    </div>\n  </div>\n</template>\n\n<script>\nlet timeout\nimport bgPng from '@/assets/img/main/bg.png'\nimport { AppMain, Navbar, Sidebar, TagsView } from './components'\nimport ResizeMixin from './mixin/ResizeHandler' // 整体页面是否根据总宽配置 左侧显示方式\nimport { mapState } from 'vuex'\nimport { MessageBox } from 'element-ui'\nimport { sendWebsocket, closeWebsocket } from '@/utils/websocket.js'\nimport { commonBlank } from '@/utils/common'\nimport defaultSettings from '@/settings'\nconst prefix = defaultSettings.service.system\n// import { getToken } from '@/utils/auth'\n\nexport default {\n  name: 'Layout',\n  components: {\n    AppMain,\n    Navbar,\n    Sidebar,\n    TagsView\n  },\n  mixins: [ResizeMixin],\n  data() {\n    return {\n      process: defaultSettings.process, // 配置环境\n      proName: defaultSettings.name,\n      menuDir: defaultSettings.menuDir // 菜单栏显示左侧left还是上侧top\n    }\n  },\n  computed: {\n    ...mapState({\n      sidebar: (state) => state.app.sidebar,\n      device: (state) => state.app.device,\n      // showSettings: state => state.settings.showSettings,\n      needTagsView: (state) => state.settings.tagsView,\n      fixedHeader: (state) => state.settings.fixedHeader\n    }),\n    classObj() {\n      return {\n        hideSidebar: !this.sidebar.opened,\n        openSidebar: this.sidebar.opened,\n        withoutAnimation: this.sidebar.withoutAnimation,\n        mobile: this.device === 'mobile'\n      }\n    },\n    isChanged() {\n      if (\n        this.$store.getters.theme ===\n          this.$store.state.settings.defaultCorlor &&\n        commonBlank(this.$store.state.user.theme)\n      ) {\n        return `url(${bgPng})`\n      } else {\n        // this.$store.commit('settings/defaultColors', false)\n        // this.$store.dispatch('settings/changeSetting', {\n        //   key: 'theme',\n        //   value: this.$store.state.user.theme\n        // })\n        return this.$store.state.settings.theme\n      }\n    }\n  },\n  created() {\n    // 生产环境不启用ws请求\n    if (process.env.NODE_ENV !== 'development') {\n      this.requstWs()\n    }\n    // 配置默认色\n    // this.$store.dispatch('settings/changeSetting', {\n    //   key: 'theme',\n    //   value: this.$store.getters.theme\n    // })\n  },\n  beforeDestroy() {\n    // 页面销毁时关闭ws。因为有可能ws连接接收数据尚未完成，用户就跳转了页面\n    // 在需要主动关闭ws的地方都可以调用该方法\n    // closeWebsocket()\n    clearTimeout(timeout)\n  },\n  methods: {\n    // ws连接成功，后台返回的ws数据，组件要拿数据渲染页面等操作\n    wsMessage(e) {\n      // 这里写拿到数据后的业务代码\n      if (e.msg_type === '018' || e.msg_type === '019') {\n        MessageBox.confirm(\n          '您的账号已在其他地点登录，如非本人操作，请及时修改密码！',\n          '提示',\n          {\n            confirmButtonText: '确定',\n            showClose: false,\n            showCancelButton: false,\n            type: 'warning'\n          }\n        ).then(() => {\n          this.logout()\n        })\n        // 默认5分钟，用户没点击确定按钮，自动跳转到登录页面\n        timeout = setTimeout(function() {\n          this.logout()\n        }, 5 * 60 * 1000)\n      } else if (e.msg_type === '01c') {\n        const alertMsg = e.alert_msg\n        const alertType = e.alert_type\n        let boxType = ''\n        if (alertType === 'ok') {\n          boxType = 'success'\n        } else if (alertType === 'error') {\n          boxType = 'error'\n        } else if (alertType === 'warn') {\n          boxType = 'warning'\n        } else if (alertType === 'info') {\n          boxType = 'info'\n        }\n        MessageBox.confirm(alertMsg, '提示', {\n          confirmButtonText: '确定',\n          showClose: false,\n          showCancelButton: false,\n          type: boxType,\n          closeOnClickModal: false // 是否点击遮罩层关闭弹窗\n        })\n      } else if (e.msg_type === '010') {\n        this.$bus.$emit('socketMsg', e.data)\n      }\n    },\n    // ws连接失败，组件要执行的代码\n    wsError() {\n      // 重新进行socket链接初始化\n      this.requstWs()\n      // 比如取消页面的loading\n    },\n\n    // 初始化websocket连接\n    requstWs() {\n      clearTimeout(timeout)\n      // 防止用户多次连续点击发起请求，所以要先关闭上次的ws请求。\n      closeWebsocket()\n      // const obj = {\n      //   monitorUrl: 'xxxxxxxxxxxxx',\n      //   userName: 'xxxxxxxxxx'\n      // }\n      const pathUrl = `${window.location.origin}`.split('//')[1]\n      const userNo = this.$store.getters.userNo\n      const token = this.$store.getters.token\n      const isSingleLogin = this.$store.getters.singleLogin\n\n      // 发起ws请求\n      sendWebsocket(\n        `ws://${pathUrl}${window.location.pathname}${process.env.VUE_APP_BASE_API}/${prefix}/ws/websocket.do?userNo=${userNo}&loginTag=1&singleLogin=${isSingleLogin}&Authorization=${token}`,\n        'init',\n        this.wsMessage,\n        this.wsError\n      )\n    },\n    /**\n     * 左侧布局显示隐藏*/\n    handleClickOutside() {\n      1``\n      this.$store.dispatch('app/closeSideBar', { withoutAnimation: false })\n    },\n    /**\n     * 系统退出: 确认*/\n    async logout() {\n      await this.$store.dispatch('user/logout')\n      this.$router.push(`/login?redirect=${this.$route.fullPath}`)\n    }\n  }\n}\n</script>\n\n<style lang=\"scss\" scoped>\n@import '~@/assets/scss/common/variable/mixin.scss';\n@import '~@/assets/scss/common/variable/variables.scss';\n\n.app-wrapper {\n  @include clearfix;\n  position: relative;\n  height: 100%;\n  width: 100%;\n  // background: $bgContent no-repeat;\n  background-size: 100% $topHeight;\n\n  &.mobile.openSidebar {\n    position: fixed;\n    top: 0;\n  }\n}\n\n.drawer-bg {\n  background: #000;\n  opacity: 0.3;\n  width: 100%;\n  top: 0;\n  height: 100%;\n  position: absolute;\n  z-index: 999;\n}\n\n.fixed-header {\n  position: fixed;\n  top: 0;\n  right: 0;\n  z-index: 9;\n  width: calc(100% - #{$sideBarWidth});\n  transition: width 0.28s;\n}\n\n.hideSidebar .fixed-header {\n  width: calc(100% - 54px);\n}\n\n.mobile .fixed-header {\n  width: 100%;\n}\n</style>\n"]}]}