{"remainingRequest": "D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\node_modules\\thread-loader\\dist\\cjs.js!D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\node_modules\\babel-loader\\lib\\index.js!D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\src\\components\\Dialog\\SunNoticeDialog\\index.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\src\\components\\Dialog\\SunNoticeDialog\\index.vue", "mtime": 1689922250581}, {"path": "D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\babel.config.js", "mtime": 1686019818359}, {"path": "D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\node_modules\\thread-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1655715099000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, null]}