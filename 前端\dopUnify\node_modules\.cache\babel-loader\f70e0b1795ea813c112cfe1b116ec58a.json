{"remainingRequest": "E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-q<PERSON><PERSON>n\\数字运营平台-统一门户工程\\dopUnify\\node_modules\\babel-loader\\lib\\index.js!E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan\\数字运营平台-统一门户工程\\dopUnify\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan\\数字运营平台-统一门户工程\\dopUnify\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan\\数字运营平台-统一门户工程\\dopUnify\\src\\views\\home\\component\\noticeList\\index.vue?vue&type=script&lang=js&", "dependencies": [{"path": "E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-<PERSON><PERSON><PERSON>n\\数字运营平台-统一门户工程\\dopUnify\\src\\views\\home\\component\\noticeList\\index.vue", "mtime": 1686019809388}, {"path": "E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-<PERSON><PERSON><PERSON>n\\数字运营平台-统一门户工程\\dopUnify\\babel.config.js", "mtime": 1686019818359}, {"path": "E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-<PERSON><PERSON><PERSON>n\\数字运营平台-统一门户工程\\dopUnify\\.babelrc", "mtime": 1705285078668}, {"path": "E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-<PERSON><PERSON><PERSON>n\\数字运营平台-统一门户工程\\dopUnify\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-<PERSON><PERSON><PERSON>n\\数字运营平台-统一门户工程\\dopUnify\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-<PERSON><PERSON><PERSON>n\\数字运营平台-统一门户工程\\dopUnify\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-<PERSON><PERSON><PERSON>n\\数字运营平台-统一门户工程\\dopUnify\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1655715099000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAmHA;AACA;AACA;AACA;AACA;AACA;EAAAA;EAAAC;AAEA;AACA;AACA;EACAC;EACAC;IAAAC;IAAAC;EAAA;EACAC;IACA;MACAC;MACAC;MACAC;MACAC;MACAC;MAAA;MACAC;MAAA;MACAC;QACAC;QAAA;QACAC;QAAA;QACAC;QAAA;QACAC;UACA;UACAV;UACAW;QACA;;QACAC;UACAC;UAAA;UACAb;UAAA;UACAc;UAAA;UACArB;UAAA;UACAsB;UAAA;UACAC;QACA;MACA;MACAC;MAAA;MACAC;MAAA;MACAC;MAAA;MACAC;MAAA;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MAAA;MACAC;MACAC;MACAC;MACAC;MAAA;MACAC;MAAA;MACAC;MACAC;MACAC;IACA;EACA;;EACAC;IACA;IACAC;MACA;QACA;MACA;IACA;EACA;EACAC;EACAC;IAAA;IACA;IACA;IACA;MACA;QACA;QACA;MACA;IACA;IACA;IACA;MACA;QACA;MACA;IACA;IACA;IACA;MACA;QACA;MACA;IACA;IACA;EACA;EACA;EACAC;IACA;IACA;IACA;IACA;IACAC;EACA;EACAC;IACA;MACA;IACA;MACA;IACA;IACA;EACA;EACAC;IACAF;IACA;EACA;EACAG;IACAC;MAAA;MACA;QACAJ;MACA;QACAA;QACA;QACA;MACA;MACA;;MAEA;MACA;MACA;MACA;MACA;MACA;;MAEA;QACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;YACA;YACA;YACAK;YACAA,2BACA,SACA,iDACA;YACAC;UACA;UACA;UACA;QACA;MACA;IACA;IACAC;MAAA;MACA;MACA;QACA;QACA;MACA;MACAC;QACAR;QACA;MACA;MACA;MACAQ;QACA;MACA;IACA;IACAC;MAAA;MACA;QACA;UACA;YACA;YACA;YACA;YACA;YACA;YACA;UACA;YACA;YACA;YACA;YACA;YACA;UACA;QACA;MACA;IACA;IACAC;MACA,0CACA;QAAA;MAAA,EACA;MACA;IACA;IACAC;MACA;QACAC;UAAAC;UAAAC;QAAA;MACA;MACA,QACAC;QACAC;QACAC;QACAC;MACA,GACAH;QAAAC;QAAAC;QAAAC;MAAA,GACAH;QAAAI;MAAA,YACAJ;QAAAI;MAAA,YACAJ;QAAAI;MAAA,YACAJ,GACA,yCACAhC,6BACAD,cACA,EACA;QAAAqC;MAAA,GACA,QACA;;MAEA;IACA;IACAC;MACA;MACA;MAEA;QACA;QACA;MACA;MACA;QACA;QACA;MACA;MACA;QACA;QACA;QACA;MACA;MACA;MAEA;MACA;MACA;IACA;IACA;AACA;AACA;IACAC;MAAA;MACA;QACA;QACA;QACA;QACA;UACA;UACA;UACA;UACA;YACAR;YAAA;YACAS;cAAAC;YAAA;YACAA;YACA;YACAT;YACA;YACA;YACA;YACAU;cACA,uBACAC,+BACAC,6BACAC,cACAA;cACA;cACA;cACA;cACA;cACA;gBACA;kBACA;gBACA;cACA;cACA;YACA;YACAC;cACA;cACA;cACA;cACA;cACA;cACA;cACA;YACA;UACA;UACApB;YACA;UACA;UACA;UACAA;YACA;UACA;QACA;MACA;IACA;IACA;IACAqB;MACA;QACAC;QACA3E;MACA;IACA;IACA;AACA;AACA;IACA4E;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBACA;gBACA;gBACA;gBACA;gBACA;kBACAC;oBACAC;oBACAC;oBACAC;oBACAC;kBACA;kBACA;gBACA;gBACA;gBACAC;kBACAC;kBACAL;kBACAM;kBACAC;kBACAC;gBACA;gBAAA;gBAAA,OACAxF;cAAA;gBAAAM;gBACA;kBACA;kBACA;kBACA;kBACA;kBACA;kBACA;kBACA;kBACAmF;kBACAA;kBACAA;kBACApE;kBACA;kBACA;kBACA;oBACA;kBACA;oBACA;kBACA;kBACA;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IACA;IACA;AACA;IACAqE;MAAA;MACA;MACA;QACA3C;QACA;MACA;QACAA;QACA;QACA;QACA;MACA;MACA;MACA;QACAsC;QACAE;QACAI;QACAL;QACAM;QAAA;QACA;QACAlE;MACA;MACAmE;QACA;UAAAlF;UAAAC;UAAAkF;QACA;QACA;QACA;QACAA;UACA;YACA,iBACAC;cACAC;YACA,GACA;UACA;YACA;YACA;cACA;YACA;UACA;QACA;QACA;QACA;UACA;UACA;YACA;UACA;YACA;UACA;QACA;QACA;MACA;IACA;IACA;AACA;AACA;AACA;AACA;AACA;IACAC;MAAA;MACA;MACA;QACAjB;QACAM;QACAC;QACAW;QACAC;QACAC;MACA;MACAnG;QACA;QACA;MACA;IACA;IACA;IACAoG;MACA;IACA;IACA;AACA;IACAC;MACA;IACA;IACA;AACA;AACA;IACAC;MACA;MACA;IACA;EACA;AACA", "names": ["readNum", "noticeModify", "name", "components", "SunNoticeDialog", "Print", "data", "title", "tween", "list", "chartActiveNum", "unread", "totalNum", "dialog", "visible", "btnCancle", "btnSubmit", "componentProps", "width", "noticeConfig", "imgSrc", "info", "content", "files", "flag", "num", "currentPage", "totalPage", "elements", "currentImage", "nextImage", "upcomingImage", "tl", "maxCan<PERSON>ee", "cloneList", "tableTimer", "componentTimer", "tableLineHeight", "tableTop", "count", "transition", "updateData", "computed", "varColor", "watch", "mounted", "<PERSON><PERSON><PERSON><PERSON>", "clearInterval", "activated", "deactivated", "methods", "actionFun", "cloneItem", "ulDom", "tableTimerFun", "document", "noticeContinue", "filterActiveImages", "slideImage", "defaults", "duration", "ease", "to", "rotation", "xPercent", "scale", "zIndex", "nextAnimation", "scrollY", "startAt", "y", "onUpdate", "querySelector", "style", "split", "onComplete", "getMore", "path", "handleClick", "param<PERSON><PERSON>", "notice_id", "notice_title", "notice_content", "file_url", "readMsg", "parameterList", "user_no", "organ_no", "publish_user", "infoObj", "getList", "role_no", "pageSize", "NoticeQuery", "notice_info_list", "Object", "publish_time", "firstPage", "read_time", "msg_no", "parpamData", "handlePrint", "dialogSumbit", "changeVisible"], "sourceRoot": "src/views/home/<USER>/noticeList", "sources": ["index.vue"], "sourcesContent": ["<!--\n* 首页-公告信息\n-->\n<template>\n  <div class=\"homeContent\">\n    <div class=\"homePageTitle\">\n      {{ title }}\n      <span class=\"title\">\n        (未读 : <span style=\"color: red\">{{ unread }}</span> / 总数 :\n        <span>{{ totalNum }}</span>)\n      </span>\n      <span class=\"InfoMore\" @click=\"getMore\">更多 ></span>\n    </div>\n    <el-row>\n      <el-col class=\"imageBox\" :span=\"10\">\n        <div class=\"underlyingBox\">\n          <div class=\"interlayerBox\">\n            <div class=\"image image-1\">\n              <img class=\"topBox\" src=\"@/assets/img/other/home_images/1.png\">\n            </div>\n            <div class=\"image image-2\">\n              <img class=\"topBox\" src=\"@/assets/img/other/home_images/2.png\">\n            </div>\n            <div class=\"image image-3\">\n              <img class=\"topBox\" src=\"@/assets/img/other/home_images/3.png\">\n            </div>\n            <div class=\"image image-4\">\n              <img class=\"topBox\" src=\"@/assets/img/other/home_images/4.png\">\n            </div>\n            <div class=\"image image-5\">\n              <img class=\"topBox\" src=\"@/assets/img/other/home_images/5.png\">\n            </div>\n          </div>\n        </div>\n        <!-- <button role=\"button\" class=\"button button-next\" @click=\"nextAnimation\">\n          >\n        </button> -->\n      </el-col>\n      <el-col class=\"noticeBox\" :span=\"14\">\n        <div id=\"normalContent\" class=\"homePageBox\" :style=\"varColor\">\n          <ul\n            v-if=\"num\"\n            id=\"normalNotice\"\n            class=\"bottom\"\n            :style=\"{ marginTop: tableTop + 'px', transition: transition }\"\n          >\n            <li\n              v-for=\"(item, index) in list\"\n              :key=\"item.id\"\n              :class=\"{\n                importantSpan: item.notice_level === '2',\n                isActive: chartActiveNum === index\n              }\"\n              @click=\"handleClick(item)\"\n            >\n              <span\n                class=\"notice-icon\"\n                :class=\"{\n                  unreadNotice: item.read_state === '0',\n                  importantSpan: item.notice_level === '2'\n                }\"\n              />\n              <span class=\"Title\" :title=\"item.notice_title\">\n                <span class=\"title-text\">{{ item.notice_title }}</span>\n\n                <span\n                  v-if=\"item.notice_level === '2' ? true : false\"\n                  class=\"center\"\n                >\n                  <img\n                    class=\"importantIcon\"\n                    src=\"@/assets/img/other/home_images/fire.gif\"\n                  >\n                </span>\n                <span\n                  v-else-if=\"item.read_state === '0' ? true : false\"\n                  class=\"center\"\n                >\n                  <img src=\"@/assets/img/other/home_images/new.png\">\n                </span>\n              </span>\n              <span class=\"Time\">{{ item.publish_time }}</span>\n            </li>\n          </ul>\n          <!-- <ul id=\"normalNoticeCopy\" class=\"bottom\" /> -->\n        </div>\n      </el-col>\n    </el-row>\n\n    <!--公告弹出框begin-->\n    <sun-notice-dialog\n      :dialog-config=\"dialog\"\n      @dialogClose=\"changeVisible\"\n      @dialogSubmit=\"dialogSumbit\"\n    >\n      <!--按钮插槽begin-->\n      <div slot=\"noticeSlot\" class=\"printBtn\">\n        <a title=\"打印\">\n          <svg class=\"link-svg\">\n            <use xlink:href=\"#icon-print\" @click=\"handlePrint\" />\n          </svg>\n        </a>\n      </div>\n      <!--按钮插槽end-->\n    </sun-notice-dialog>\n    <!--公告弹出框end-->\n    <!--打印组件begin-->\n    <div style=\"display: none\">\n      <Print ref=\"printRef\" :notice-config=\"dialog.noticeConfig\" />\n    </div>\n    <!--打印组件end-->\n  </div>\n</template>\n\n<script>\nimport { date10Format } from '@/filters'\nimport { SunNoticeDialog } from '@/components' // 公共组件\nimport Print from './component/print/print.vue' // 打印组件\nimport { uploadFile } from '@/utils/common' // 公共方法\nimport { Home } from '@/api'\nconst { NoticeQuery, readNum, noticeModify } = Home\n\nimport { dateNowFormat10 } from '@/utils/date.js' // 日期格式化\nimport gsap from 'gsap' // 滚动插件\nexport default {\n  name: 'NoticeList',\n  components: { SunNoticeDialog, Print },\n  data() {\n    return {\n      title: '通知公告',\n      tween: null,\n      list: [],\n      chartActiveNum: null,\n      unread: '', // 未读公告数量\n      totalNum: '', // 公告总数\n      dialog: {\n        visible: false, // 开启/关闭弹窗\n        btnCancle: false, // 取消按钮\n        btnSubmit: false, // 确定按钮\n        componentProps: {\n          // 弹出框配置属性\n          title: '公告详情',\n          width: '74.8rem' // 当前弹出框宽度\n        },\n        noticeConfig: {\n          imgSrc: require('@/assets/img/other/notice_images/noticeBackground.png'), // 背景图片加载\n          title: '标题', // 公告标题\n          info: [], // 发布机构、发布者名称、发布时间\n          readNum: 0, // 阅读量\n          content: '', // 公告正文\n          files: []\n        }\n      },\n      flag: false, // 滚动标识\n      num: true, // 消息条数\n      currentPage: 1, // 当前页数\n      totalPage: 0, // 所有页数\n      elements: ['.image-1', '.image-2', '.image-3', '.image-4', '.image-5'],\n      currentImage: 1,\n      nextImage: 2,\n      upcomingImage: 3,\n      tl: null,\n      maxCanSee: 0, // maxCanSee代表可视范围内的最大完整数据条数\n      cloneList: 0,\n      tableTimer: null,\n      componentTimer: null,\n      tableLineHeight: 40, // tableLineHeight代表列表中一行的高度\n      tableTop: 0, // 列表向上移动的像素\n      count: 0,\n      transition: 'all 0.5s',\n      updateData: false // 是否更新消息列表\n    }\n  },\n  computed: {\n    // 计算属性调用store中的主题色\n    varColor() {\n      return {\n        '--color': this.$store.state.settings.theme\n      }\n    }\n  },\n  watch: {},\n  mounted() {\n    this.getList()\n    // 系统消息流程处理完毕 更新未处理列表（公告预览）|| 首页公告局部刷新\n    this.$bus.$on('isUpdateNotice', (data) => {\n      if (data) {\n        this.updateData = true\n        // this.getList()\n      }\n    })\n    // 右下角弹窗公告处理完毕 重新查询系统消息页面公告\n    this.$bus.$on('cardNoticeUpdate', (data) => {\n      if (data) {\n        this.getList()\n      }\n    })\n    // 公告查看页面（公告预览）\n    this.$bus.$on('menuNotice', (data) => {\n      if (data) {\n        this.getList()\n      }\n    })\n    this.$bus.$on('resizeNoticeM', this.actionFun)\n  },\n  // 组件销毁前，清除定时器\n  beforeDestroy() {\n    this.$bus.$off('isUpdateNotice')\n    this.$bus.$off('cardNoticeUpdate')\n    this.$bus.$off('menuNotice')\n    this.$bus.$off('resizeNoticeM')\n    clearInterval(this.tableTimer)\n  },\n  activated() {\n    if (this.updateData) {\n      this.getList()\n    } else {\n      this.noticeContinue()\n    }\n    this.updateData = false\n  },\n  deactivated() {\n    clearInterval(this.tableTimer)\n    this.tableTimer = null\n  },\n  methods: {\n    actionFun(flag) {\n      if (flag) {\n        clearInterval(this.tableTimer)\n      } else {\n        clearInterval(this.tableTimer)\n        this.count = 0\n        this.tableTop = 0\n      }\n      // this.num = true\n\n      // this.chartActiveNum = this.count\n      const box1Height = document.querySelector('#normalContent').offsetHeight // 外面盒子高度\n      const box2Height = this.list.length * 40 // 列表高度\n      // console.log(box1Height)\n      // console.log(box2Height)\n      // console.log(this.list)\n\n      if (box2Height > box1Height) {\n        this.$nextTick(() => {\n          this.maxCanSee = this.list.length\n          this.cloneList = this.list.length * 2\n          const ulDom = document.querySelector('#normalNotice')\n          // 判断是否有公告信息\n          // 克隆节点\n          const fD = ulDom.cloneNode(true).children\n          // console.log(ulDom.childNodes)\n          // console.log(fD)\n          // console.log(fD.length)\n          for (let item = 0; item < fD.length; item++) {\n            // // 添加克隆节点事件\n            const cloneItem = fD[item].cloneNode(true)\n            cloneItem.classList.remove('isActive')\n            cloneItem.addEventListener(\n              'click',\n              this.handleClick.bind(null, this.list[item])\n            )\n            ulDom.appendChild(cloneItem)\n          }\n          this.chartActiveNum = this.count\n          this.tableTimerFun()\n        })\n      }\n    },\n    tableTimerFun() {\n      // 每滚动一次，count加1\n      if (this.cloneList > this.maxCanSee) {\n        // tableList是列表的数据对象，maxCanSee代表可视范围内的最大完整数据条数\n        this.noticeContinue()\n      }\n      document.getElementById('normalNotice').onmouseover = () => {\n        clearInterval(this.tableTimer)\n        this.tableTimer = null\n      }\n      // 滚动区域内移出鼠标 滚动暂停 再次点击鼠标 继续滚动\n      document.getElementById('normalNotice').onmouseout = () => {\n        this.noticeContinue()\n      }\n    },\n    noticeContinue() {\n      if (this.tableTimer === null) {\n        this.tableTimer = setInterval(() => {\n          if (this.count < this.cloneList - this.maxCanSee - 1) {\n            this.transition = 'all 0.5s'\n            // 如果还没滚动到最后一条数据，则列表向上移动以上的高度\n            this.tableTop -= this.tableLineHeight // tableLineHeight代表列表中一行的高度\n            this.count++ // 每滚动一次，count加1\n            this.chartActiveNum = this.count\n            this.nextAnimation()\n          } else {\n            // 如果滚动到最后一条，则恢复初始状态\n            this.transition = ''\n            this.count = 0\n            this.tableTop = 0\n            this.chartActiveNum = this.count\n          }\n        }, 4000)\n      }\n    },\n    filterActiveImages(image1, image2) {\n      const filteredResult = this.elements.filter(\n        (element) => element !== image1 && element !== image2\n      )\n      return filteredResult\n    },\n    slideImage(currentImage, nextImage, upcomingImage) {\n      this.tl = gsap.timeline({\n        defaults: { duration: 1, ease: 'Power1.easeInOut' }\n      })\n      this.tl\n        .to(`.image-${currentImage}`, {\n          rotation: -10,\n          xPercent: -120,\n          scale: 0.8\n        })\n        .to(`.image-${currentImage}`, { rotation: 0, xPercent: 0, scale: 1 })\n        .to(`.image-${nextImage}`, { zIndex: 2 }, '-=1.6')\n        .to(`.image-${currentImage}`, { zIndex: -1 }, '-=1.6')\n        .to(`.image-${upcomingImage}`, { zIndex: 1 }, '-=1.6')\n        .to(\n          this.filterActiveImages(\n            `.image-${nextImage}`,\n            `.image-${currentImage}`\n          ),\n          { zIndex: 0 },\n          '-=2.4'\n        )\n\n      // return this.tl\n    },\n    nextAnimation() {\n      const images = document.querySelectorAll('.image')\n      const totalImages = images.length\n\n      if (this.currentImage === totalImages - 1) {\n        this.nextImage = this.currentImage + 1\n        this.upcomingImage = 1\n      }\n      if (this.currentImage === totalImages) {\n        this.nextImage = 1\n        this.upcomingImage = this.nextImage + 1\n      }\n      if (this.currentImage === totalImages + 1) {\n        this.currentImage = 1\n        this.nextImage = 2\n        this.upcomingImage = 3\n      }\n      this.slideImage(this.currentImage, this.nextImage, this.upcomingImage)\n\n      this.currentImage = this.nextImage\n      this.nextImage = this.currentImage + 1\n      this.upcomingImage = this.nextImage + 1\n    },\n    /**\n     * 纵向滚动\n     */\n    scrollY() {\n      this.$nextTick(() => {\n        const fD = document.querySelector('#normalNotice').firstElementChild\n        // const text = document.querySelector('#normalContent')\n        // 判断是否有重要信息\n        if (fD) {\n          // document\n          //   .querySelector('#normalNotice')\n          //   .appendChild(fD.cloneNode(true))\n          this.tween = gsap.to('#normalNotice', {\n            duration: this.num * 2, // 动画延迟秒数\n            startAt: { y: 0 },\n            y: -39 * this.num,\n            // delay: 1,\n            ease: 'none',\n            // repeat: 0,\n            // repeatDelay: 2,\n            // each: 20,\n            onUpdate: () => {\n              const height1 = document\n                .querySelector('#normalNotice')\n                .style.transform.substring(4)\n                .split(',')[1]\n                .split('.')[0]\n              const height2 = parseInt(height1.substring(2, height1.length)) // ul滚动高度\n              const activeLiIndex = Math.trunc(height2 / 40)\n              // console.log(text.scrollTop)\n              // text.scrollTop = height2\n              if (activeLiIndex >= 1) {\n                if (this.chartActiveNum !== activeLiIndex) {\n                  this.nextAnimation()\n                }\n              }\n              this.chartActiveNum = activeLiIndex\n            },\n            onComplete: () => {\n              this.tween.kill()\n              // this.currentPage++\n              // if (this.currentPage > this.totalPage) {\n              //   this.currentPage = 1\n              // }\n              this.getList(true)\n              this.nextAnimation()\n            }\n          })\n          document.getElementById('normalContent').onmouseover = () => {\n            this.tween.pause()\n          }\n          // 滚动区域内移出鼠标 滚动暂停 再次点击鼠标 继续滚动\n          document.getElementById('normalContent').onmouseout = () => {\n            this.tween.play()\n          }\n        }\n      })\n    },\n    // 点击更多进行路由跳转\n    getMore() {\n      this.$router.push({\n        path: 'system/notice/query',\n        name: 'Query'\n      })\n    },\n    /**\n     * 点击-初始化公告弹窗\n     * @param {Object} val 当前点击的弹窗信息 */\n    async handleClick(val) {\n      // 清空每次弹窗数据\n      this.dialog.noticeConfig.info = []\n      this.dialog.visible = true\n      // 弹窗打开 如果阅读状态为未阅，改为已阅，同时修改系统消息相关信息\n      if (val.read_state === '0') {\n        const paramJson = {\n          notice_id: val.notice_id,\n          notice_title: val.notice_title,\n          notice_content: val.notice_content,\n          file_url: val.file_url\n        }\n        this.firstPage(val.notice_id, '', paramJson)\n      }\n      // 查询阅读量\n      const readMsg = {\n        parameterList: [],\n        notice_id: val.notice_id,\n        user_no: this.$store.getters.userNo,\n        organ_no: this.$store.getters.organNo,\n        publish_user: val.publish_user\n      }\n      const data = await readNum(readMsg)\n      this.$nextTick(() => {\n        // 弹窗加载完毕后赋值\n        this.dialog.noticeConfig.title = val.notice_title // 标题\n        const info = []\n        const publish_organ = val.publish_organ\n        const publish_user = val.publish_user\n        const publish_time = val.publish_time\n        const infoObj = {}\n        infoObj['publish_organ'] = publish_organ\n        infoObj['publish_user'] = publish_user\n        infoObj['publish_time'] = publish_time\n        info.push(infoObj)\n        this.dialog.noticeConfig.info = info\n        this.dialog.noticeConfig.content = val.notice_content // 公告内容\n        if (val.file_url) {\n          this.dialog.noticeConfig.files = uploadFile(val.file_url)\n        } else {\n          this.dialog.noticeConfig.files = []\n        }\n        this.dialog.noticeConfig.readNum = data.retMap.read_num\n      })\n    },\n    /**\n     * 查询-公告列表 */\n    getList(flag) {\n      this.num = false\n      if (flag) {\n        clearInterval(this.tableTimer)\n        this.tableTimer = null\n      } else {\n        clearInterval(this.tableTimer)\n        this.count = 0\n        this.tableTop = 0\n        this.tableTimer = null\n      }\n      // this.chartActiveNum = this.count\n      const msg = {\n        parameterList: [],\n        organ_no: this.$store.getters.organNo,\n        role_no: this.$store.getters.roleNo,\n        user_no: this.$store.getters.userNo,\n        pageSize: 0, // 当前页显示条数\n        // pageSize: 15, // 当前页显示条数-基线的显示条数是15条\n        currentPage: this.currentPage\n      }\n      NoticeQuery(msg).then((response) => {\n        const { unread, totalNum, notice_info_list } = response.retMap\n        this.unread = parseInt(unread) // 未读数量\n        this.totalNum = totalNum // 公告总数\n        this.list = []\n        notice_info_list.forEach((element) => {\n          if (element.publish_user !== this.$store.getters.userNo) {\n            this.list.push(\n              Object.assign(element, {\n                publish_time: date10Format(element.publish_time)\n              })\n            )\n          } else {\n            this.totalNum = this.totalNum - 1\n            if (element.read_state === '0') {\n              this.unread = this.unread - 1\n            }\n          }\n        })\n        this.totalPage = Math.ceil(totalNum / 10) // 总页数\n        this.$nextTick(() => {\n          this.num = true\n          if (flag) {\n            this.actionFun(flag)\n          } else {\n            this.actionFun()\n          }\n        })\n        // this.num = true\n      })\n    },\n    /**\n     * 公告信息: 更改阅读状态\n     * @param notice_id:公告id\n     * @param msg_no:系统消息编号\n     * @param paramJson：系统消息参数\n     * */\n    firstPage(notice_id, msg_no, paramJson) {\n      const read_time = dateNowFormat10() // 当前时间的十位数格式\n      const msg = {\n        notice_id: notice_id,\n        user_no: this.$store.getters.userNo,\n        organ_no: this.$store.getters.organNo,\n        read_time: read_time,\n        msg_no: msg_no,\n        parpamData: paramJson\n      }\n      noticeModify(msg).then((response) => {\n        this.getList()\n        this.$bus.$emit('homeNotice', true)\n      })\n    },\n    // 打印\n    handlePrint() {\n      this.$print(this.$refs.printRef)\n    },\n    /**\n     * 弹出框 - 确认*/\n    dialogSumbit() {\n      this.changeVisible(false)\n    },\n    /**\n     * 弹出框 - 关闭\n     * @param {Boolean} param 弹出框显示隐藏配置*/\n    changeVisible(param) {\n      this.dialog.visible = param\n      if (!param) this.getList(true)\n    }\n  }\n}\n</script>\n\n<style lang=\"scss\" scoped>\n@import '~@/assets/scss/common/variable/variable/size.scss';\n@import '~@/assets/scss/common/variable/variable/color.scss';\n$underlyingColor: #d9e3f4; // 底层盒子颜色\n$interlayerColor: #f0e3d3; // 夹层盒子颜色\n$impColor: #3764fc; // 重要公告颜色\n$nomalLiColor: #99a2b2; // 普通li日期文字颜色\n$nomalLiTextColor: #333333; // 普通li内容文字颜色\n$nomalColor: #d5d5d5;\n.homeContent {\n  min-width: 41rem;\n}\n.InfoMore {\n  float: right;\n  color: $nomalLiColor;\n  font-size: $noteFomt;\n  cursor: pointer;\n}\n.title {\n  font-size: $contFont;\n}\n.el-row {\n  height: 91%;\n  .imageBox {\n    height: 100%;\n    display: flex;\n    justify-content: center;\n    align-items: center;\n    .underlyingBox {\n      // background: $underlyingColor;\n      border-radius: 0.6rem;\n      width: 13.6rem;\n      height: 23.2rem;\n      position: relative;\n      .interlayerBox {\n        position: absolute;\n        // background: $interlayerColor;\n        border-radius: 0.6rem;\n        width: 15rem;\n        height: 21.6rem;\n        top: 50%;\n        left: 50%;\n        transform: translate(-50%, -50%);\n        .image {\n          z-index: 0;\n          position: absolute;\n          width: 16.6rem;\n          height: 20rem;\n          // transform-origin: 100% 100%;\n          // transform: translate(-50%, -50%);\n          .topBox {\n            position: absolute;\n            border-radius: 0.6rem;\n            width: 14.6rem;\n            height: 18rem;\n            top: 50%;\n            left: 50%;\n            transform: translate(-50%, -50%);\n          }\n        }\n        .image-1 {\n          z-index: 2;\n        }\n        .image-2 {\n          z-index: 1;\n        }\n        .image-5 {\n          z-index: -1;\n        }\n      }\n    }\n    .button-next {\n      padding: 8px 10px;\n      font-size: 32px;\n      background: transparent;\n      outline: none;\n      border: 3px solid black;\n      cursor: pointer;\n      color: black;\n    }\n  }\n  .noticeBox {\n    height: 100%;\n    display: flex;\n    justify-content: center;\n    align-items: center;\n    .homePageBox {\n      // overflow: hidden;\n      // margin-top: 1%;\n      // 适配各种浏览器中 滚动条宽度\n      padding: 1rem 1rem;\n      // height: 25.2rem;\n      cursor: pointer;\n      // IE浏览器隐藏滚动条 begin\n      -ms-scroll-chaining: chained;\n      -ms-overflow-style: none;\n      -ms-content-zooming: zoom;\n      -ms-scroll-rails: none;\n      -ms-content-zoom-limit-min: 100%;\n      -ms-content-zoom-limit-max: 500%;\n      // -ms-scroll-snap-type: proximity;\n      -ms-scroll-snap-points-x: snapList(100%, 200%, 300%, 400%, 500%);\n      -ms-overflow-style: none;\n      overflow: auto;\n      // IE浏览器隐藏滚动条 end\n      &::-webkit-scrollbar {\n        width: 0px; // y轴滚动条宽度\n      }\n      &::-webkit-scrollbar-thumb {\n        // 滚动条配置\n        width: 0px;\n      }\n      &::-webkit-scrollbar-track {\n        width: 0px;\n      }\n      .bottom {\n        // position: absolute;\n        // transition: all 0.5s;\n        list-style-type: none;\n        padding: 0;\n        li:hover {\n          // background-color: #ececec;\n          box-shadow: 0px 0px 6px rgba(107, 143, 199, 0.37);\n          border-radius: 0.6rem;\n        }\n        .isActive {\n          box-shadow: 0px 0px 6px rgba(107, 143, 199, 0.37);\n          border-radius: 0.6rem;\n        }\n        li {\n          cursor: pointer;\n          padding: 0 1rem;\n          height: 4rem;\n          line-height: 4rem;\n          overflow: hidden;\n          .notice-icon {\n            position: relative;\n            top: -0.2rem;\n            width: 0.5rem;\n            height: 0.5rem;\n            // background: $color_font;\n            background: $nomalColor;\n            margin-right: 0.6rem;\n            border-radius: 50%;\n            display: inline-block;\n          }\n\n          .Title {\n            font-size: 1.4rem;\n            color: $nomalLiTextColor;\n            overflow: hidden;\n            text-overflow: ellipsis;\n            width: 50%;\n            white-space: nowrap;\n            display: inline-block;\n            vertical-align: middle;\n            line-height: 1.6rem;\n            .center {\n              margin-left: 0.4rem;\n              .importantIcon {\n                width: 1.4rem;\n              }\n            }\n            .title-text {\n              display: inline-block;\n              width: calc(100% - 3rem);\n              text-overflow: ellipsis;\n              white-space: nowrap;\n              overflow: hidden;\n            }\n          }\n          .Time {\n            float: right;\n            color: $nomalLiColor;\n          }\n        }\n        // 公告级别为重要的样式\n        .importantSpan {\n          color: $impColor;\n        }\n        .unreadNotice {\n          background: $impColor !important;\n        }\n      }\n    }\n  }\n}\n\n// 打印按钮\n.printBtn {\n  position: absolute;\n  right: 2%;\n  top: 20%;\n  .link-svg {\n    cursor: pointer;\n    width: 1.4rem;\n    height: 1.4rem;\n    fill: currentColor;\n    overflow: hidden;\n    float: left;\n  }\n}\n</style>\n"]}]}