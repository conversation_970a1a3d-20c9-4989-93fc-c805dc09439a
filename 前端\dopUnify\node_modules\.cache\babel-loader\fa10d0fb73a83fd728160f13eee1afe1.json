{"remainingRequest": "E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1\\数字运营平台-统一门户工程\\dopUnify\\node_modules\\babel-loader\\lib\\index.js!E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1\\数字运营平台-统一门户工程\\dopUnify\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1\\数字运营平台-统一门户工程\\dopUnify\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1\\数字运营平台-统一门户工程\\dopUnify\\src\\layout\\components\\SublicenseLog\\index.vue?vue&type=script&lang=js&", "dependencies": [{"path": "E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1\\数字运营平台-统一门户工程\\dopUnify\\src\\layout\\components\\SublicenseLog\\index.vue", "mtime": 1716875180376}, {"path": "E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1\\数字运营平台-统一门户工程\\dopUnify\\babel.config.js", "mtime": 1716875191548}, {"path": "E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1\\数字运营平台-统一门户工程\\dopUnify\\.babelrc", "mtime": 1716875191502}, {"path": "E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1\\数字运营平台-统一门户工程\\dopUnify\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1\\数字运营平台-统一门户工程\\dopUnify\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1\\数字运营平台-统一门户工程\\dopUnify\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1\\数字运营平台-统一门户工程\\dopUnify\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1655715099000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, null]}