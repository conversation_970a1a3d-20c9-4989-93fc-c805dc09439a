{"remainingRequest": "D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\node_modules\\thread-loader\\dist\\cjs.js!D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\node_modules\\babel-loader\\lib\\index.js!D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\src\\utils\\websocket.js", "dependencies": [{"path": "D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\src\\utils\\websocket.js", "mtime": 1686019809826}, {"path": "D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\babel.config.js", "mtime": 1686019818359}, {"path": "D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\node_modules\\thread-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:aW1wb3J0ICJjb3JlLWpzL21vZHVsZXMvZXMuanNvbi5zdHJpbmdpZnkuanMiOwppbXBvcnQgeyBNZXNzYWdlIH0gZnJvbSAnZWxlbWVudC11aSc7CmltcG9ydCB7IGNvbW1vbkJsYW5rIH0gZnJvbSAnQC91dGlscy9jb21tb24nOyAvLyDku45jb29raWXkuK3ojrflj5Z0b2tlbgoKdmFyIHdlYnNvY2sgPSBudWxsOwp2YXIgbWVzc2FnZUNhbGxiYWNrID0gbnVsbDsgLy8g5o6l5pS25Yiwd3PmlbDmja7vvIzlr7nmlbDmja7ov5vooYzlpITnkIbnmoTlm57osIPlh73mlbAKdmFyIGVycm9yQ2FsbGJhY2sgPSBudWxsOyAvLyDpk77mjqXlpLHotKXlkI7nmoTlm57osIPlh73mlbAKdmFyIHdzVXJsID0gJyc7IC8vIOiHquW3seeahHdlYnNvY2tldOaOpeWPowp2YXIgdHJ5VGltZSA9IDA7CgovLyDmjqXmlLZ3c+W<PERSON><PERSON><PERSON><PERSON>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"}, null]}