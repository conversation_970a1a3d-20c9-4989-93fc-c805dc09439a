{"remainingRequest": "D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\node_modules\\thread-loader\\dist\\cjs.js!D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\node_modules\\babel-loader\\lib\\index.js!D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\src\\views\\system\\dataAuditing\\component\\table\\index.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\src\\views\\system\\dataAuditing\\component\\table\\index.vue", "mtime": 1686019808310}, {"path": "D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\babel.config.js", "mtime": 1686019818359}, {"path": "D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\node_modules\\thread-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1655715099000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:aW1wb3J0IF9yZWdlbmVyYXRvclJ1bnRpbWUgZnJvbSAiRDovMV9Qcm9qZWN0L1hZRF9Qcm9qZWN0L2RvcC00LjAvZG9wLTQuMS1xaWFuZHVhbi11bmlmeS9kb3BVbmlmeS9ub2RlX21vZHVsZXMvQGJhYmVsL3J1bnRpbWUvaGVscGVycy9lc20vcmVnZW5lcmF0b3JSdW50aW1lLmpzIjsKaW1wb3J0IF9hc3luY1RvR2VuZXJhdG9yIGZyb20gIkQ6LzFfUHJvamVjdC9YWURfUHJvamVjdC9kb3AtNC4wL2RvcC00LjEtcWlhbmR1YW4tdW5pZnkvZG9wVW5pZnkvbm9kZV9tb2R1bGVzL0BiYWJlbC9ydW50aW1lL2hlbHBlcnMvZXNtL2FzeW5jVG9HZW5lcmF0b3IuanMiOwppbXBvcnQgImNvcmUtanMvbW9kdWxlcy9lcy5vYmplY3QudG8tc3RyaW5nLmpzIjsKaW1wb3J0ICJjb3JlLWpzL21vZHVsZXMvd2ViLmRvbS1jb2xsZWN0aW9ucy5mb3ItZWFjaC5qcyI7CmltcG9ydCAiY29yZS1qcy9tb2R1bGVzL2VzLmFycmF5Lm1hcC5qcyI7CmltcG9ydCAiY29yZS1qcy9tb2R1bGVzL2VzLmFycmF5LmNvbmNhdC5qcyI7Ci8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCgppbXBvcnQgeyBjb25maWdUYWJsZSwgY29uZmlnIH0gZnJvbSAnLi9pbmZvJzsgLy8g6KGo5aS044CB6KGo5Y2V6YWN572uCmltcG9ydCBTdW5PcGVyRGV0YWlsRGlhbG9nIGZyb20gJ0Avdmlld3Mvc3lzdGVtL2NvbmZpZy9tZXNzYWdlL3N5c3RlbU1zZy9TdW5PcGVyRGV0YWlsRGlhbG9nJzsKaW1wb3J0IHsgY29tbW9uTXNnSW5mbyB9IGZyb20gJ0AvdXRpbHMvbWVzc2FnZS5qcyc7IC8vIOaPkOekuuS/oeaBrwppbXBvcnQgeyBjb21tb25CbGFuayB9IGZyb20gJ0AvdXRpbHMvY29tbW9uJzsKaW1wb3J0IHsgcmVsZWFzZUZsb3dUYXNrIH0gZnJvbSAnQC91dGlscy9mbG93UGF0aC5qcyc7CmltcG9ydCB7IHN5c01lc3NhZ2VPcGVyYXRpb25SZXF1ZXN0RGVhdGlsU2hvdyB9IGZyb20gJ0AvdXRpbHMvZmxvd1BhdGgnOwppbXBvcnQgUmVzaXplTWl4aW4gZnJvbSAnQC91dGlscy9SZXNpemVIYW5kbGVyJzsgLy8g5pW05L2T6aG16Z2i5piv5ZCm5qC55o2u5oC76auY6YWN572uCgppbXBvcnQgeyBvcmdhbk5hbWVGb3JtYXQgfSBmcm9tICdAL2ZpbHRlcnMnOwppbXBvcnQgeyBDb21tb24gfSBmcm9tICdAL2FwaSc7CnZhciBxdWVyeSA9IENvbW1vbi5EYXRhQXVkaXRpbmcucXVlcnk7CmV4cG9ydCBkZWZhdWx0IHsKICBuYW1lOiAnVGFibGVMaXN0JywKICBjb21wb25lbnRzOiB7CiAgICBTdW5PcGVyRGV0YWlsRGlhbG9nOiBTdW5PcGVyRGV0YWlsRGlhbG9nCiAgfSwKICBtaXhpbnM6IFtSZXNpemVNaXhpbl0sCiAgcHJvcHM6IHsKICAgIGRlZmF1bHRGb3JtOiB7CiAgICAgIHR5cGU6IE9iamVjdCwKICAgICAgZGVmYXVsdDogZnVuY3Rpb24gX2RlZmF1bHQoKSB7CiAgICAgICAgcmV0dXJuIHt9OwogICAgICB9CiAgICB9LAogICAgYnRuQWxsOiB7CiAgICAgIHR5cGU6IE9iamVjdCwKICAgICAgZGVmYXVsdDogZnVuY3Rpb24gX2RlZmF1bHQoKSB7CiAgICAgICAgcmV0dXJuIHt9OwogICAgICB9CiAgICB9CiAgfSwKICBkYXRhOiBmdW5jdGlvbiBkYXRhKCkgewogICAgcmV0dXJuIHsKICAgICAgLy8gdGFibGXliJfooajnm7jlhbPlj4LmlbAKICAgICAgbGlzdExvYWRpbmc6IGZhbHNlLAogICAgICB0YWJsZTogewogICAgICAgIC8vIOihqOagvOmFjee9rgogICAgICAgIHRhYmxlQ29sdW1uczogY29uZmlnVGFibGUoKSwKICAgICAgICAvLyDooajlpLTphY3nva4KICAgICAgICByZWY6ICd0YWJsZVJlZicsCiAgICAgICAgc2VsZWN0aW9uOiBmYWxzZSwKICAgICAgICAvLyDlpI3pgIkKICAgICAgICBpbmRleE51bWJlcjogdHJ1ZSwKICAgICAgICAvLyDluo/lj7cKICAgICAgICBsb2FkaW5nOiBmYWxzZSwKICAgICAgICBjb21wb25lbnRQcm9wczogewogICAgICAgICAgZGF0YTogW10sCiAgICAgICAgICAvLyDooajmoLzmlbDmja4KICAgICAgICAgIGhlaWdodDogJzMwMHB4JywKICAgICAgICAgIGZvcm1Sb3c6IDIgLy8g6KGo5Y2V6KGM5pWwCiAgICAgICAgfSwKCiAgICAgICAgcGFnZUxpc3Q6IHsKICAgICAgICAgIHRvdGFsTnVtOiAwLAogICAgICAgICAgY3VycmVudFBhZ2U6IDEsCiAgICAgICAgICAvLyDlvZPliY3pobUKICAgICAgICAgIHBhZ2VTaXplOiB0aGlzLiRzdG9yZS5nZXR0ZXJzLnBhZ2VTaXplIC8vIOW9k+WJjemhteaYvuekuuadoeaVsAogICAgICAgIH0KICAgICAgfSwKCiAgICAgIGFwcHJvdmVfYWdyZWUyOiAn5pyq57uI5a6hJywKICAgICAgcmVkQ29sb3I6IHsKICAgICAgICBjb2xvcjogJ3JlZCcKICAgICAgfSwKICAgICAgYmx1ZUJsYWNrOiB7CiAgICAgICAgY29sb3I6ICcjMTU1MThjJwogICAgICB9LAogICAgICAvLyDlrqHmibnnu5Pmnpzor6bmg4Xnm7jlhbPlj4LmlbAKICAgICAgYXByb3ZlQ3VycmVudFJvdzogW10sCiAgICAgIC8vIOmAieS4reihjAogICAgICBkaWFsb2cyOiB7CiAgICAgICAgLy8g5a6h5om557uT5p6cLS3miafooYzlvILluLgt5by55qGGCiAgICAgICAgdmlzaWJsZTogZmFsc2UsCiAgICAgICAgdGl0bGU6ICflpITnkIbor6bmg4UnCiAgICAgIH0sCiAgICAgIGVycm9yTXNnOiAnJywKICAgICAgLy8g5a6h5om557uT5p6cLeWuoeaguOW8guW4uC3or6bmg4UKICAgICAgZXJyRGV0YWlsOiB7CiAgICAgICAgLy8g5a6h5qC457uT5p6cLeWkhOeQhuaYjue7hi3ooajmoLzphY3nva4KICAgICAgICB0YWJsZUNvbHVtbnM6IFtdLAogICAgICAgIC8vIOihqOWktOmFjee9rgogICAgICAgIHNlbGVjdGlvbjogZmFsc2UsCiAgICAgICAgLy8g5aSN6YCJCiAgICAgICAgaW5kZXhOdW1iZXI6IHRydWUsCiAgICAgICAgLy8g5bqP5Y+3CiAgICAgICAgbG9hZGluZzogZmFsc2UsCiAgICAgICAgY29tcG9uZW50UHJvcHM6IHsKICAgICAgICAgIGRhdGE6IFtdLAogICAgICAgICAgLy8g6KGo5qC85pWw5o2uCiAgICAgICAgICBoZWlnaHQ6ICcxMDBweCcsCiAgICAgICAgICBmb3JtUm93OiAyIC8vIOihqOWNleihjOaVsAogICAgICAgIH0sCgogICAgICAgIHBhZ2VMaXN0OiB7CiAgICAgICAgICB0b3RhbE51bTogMCwKICAgICAgICAgIGN1cnJlbnRQYWdlOiAxLAogICAgICAgICAgLy8g5b2T5YmN6aG1CiAgICAgICAgICBwYWdlU2l6ZTogdGhpcy4kc3RvcmUuZ2V0dGVycy5wYWdlU2l6ZSAvLyDlvZPliY3pobXmmL7npLrmnaHmlbAKICAgICAgICB9CiAgICAgIH0sCgogICAgICAvLyDmlofku7bkuIrkvKDnm7jlhbPlj4LmlbAKICAgICAgZGlhbG9nRmlsZTogewogICAgICAgIC8vIOS4iuS8oOaWh+S7tuihqOWNlQogICAgICAgIHZpc2libGU6IGZhbHNlLAogICAgICAgIGNvbXBvbmVudFByb3BzOiB7CiAgICAgICAgICAvLyDlvLnlh7rmoYbphY3nva7lsZ7mgKcKICAgICAgICAgIHRpdGxlOiAn5a6h5qC45o+Q5LqkJywKICAgICAgICAgIHdpZHRoOiAnNjByZW0nIC8vIOW9k+WJjeW8ueWHuuahhuWuveW6pgogICAgICAgIH0sCgogICAgICAgIGZvcm06IHsKICAgICAgICAgIGxhYmVsV2lkdGg6ICcxNXJlbScsCiAgICAgICAgICBjb25maWc6IGNvbmZpZyh0aGlzKSwKICAgICAgICAgIGRlZmF1bHRGb3JtOiB7CiAgICAgICAgICAgIGZpbGVfdXJsOiAnJywKICAgICAgICAgICAgLy8g6KGo5Y2V5Zyw5Z2ACiAgICAgICAgICAgIHBvc3RzY3JpcHQ6ICcnIC8vIOmZhOiogAogICAgICAgICAgfQogICAgICAgIH0KICAgICAgfSwKCiAgICAgIGZpbGVMaXN0OiBbXSwKICAgICAgLy8g5LiK5Lyg5paH5Lu25YiX6KGoCiAgICAgIGRlbEZpbGU6IFtdLAogICAgICAvLyDliKDpmaTmlofku7blkI7nmoTmlofku7bliJfooagKICAgICAgZmlsZVJvdzogW10sCiAgICAgIC8vIOS4i+i9vemZhOS7tuW9k+WJjXJvd+S/oeaBrwogICAgICBidG5JZDogJycsCiAgICAgIC8vIOS4iuS8oOaWh+S7tuaPkOS6pOaXtuWIpOaWreaYr+WQjOaEj+aTjeS9nOaIluS4jeWQjOaEj+aTjeS9nOagh+W/lwogICAgICAvLyDmk43kvZzor6bmg4XlvLnnqpfnm7jlhbPlj4LmlbAKICAgICAgZGlhbG9nT3BlcjogewogICAgICAgIC8vIOa1geeoi+S6i+mhuS3mk43kvZznlLPor7flvLnmoYbmlbDmja4KICAgICAgICB2aXNpYmxlOiBmYWxzZSwKICAgICAgICBjb21wb25lbnRQcm9wczogewogICAgICAgICAgLy8g5by55Ye65qGG5bGe5oCnCiAgICAgICAgICB0aXRsZTogJycsCiAgICAgICAgICAvLyDlvLnlh7rmoYbmoIfpopgKICAgICAgICAgIHdpZHRoOiAnODAlJyAvLyDlvZPliY3lvLnlh7rmoYblrr3luqYg6buY6K6kODAlCiAgICAgICAgfSwKCiAgICAgICAgcXVlcnlUeXBlOiAnJywKICAgICAgICBjdXJyZW50Um93OiBbXSwKICAgICAgICAvLyDpgInkuK3ooYwKICAgICAgICB0YWJsZURhdGE6IHsKICAgICAgICAgIC8vIOaTjeS9nOivpuaDheW8ueeqly0tLeWIoOmZpC3ooajmoLwKICAgICAgICAgIHRhYmxlQ29sdW1uczogW10sCiAgICAgICAgICAvLyDooajlpLTphY3nva4KICAgICAgICAgIHNlbGVjdGlvbjogZmFsc2UsCiAgICAgICAgICAvLyDlpI3pgIkKICAgICAgICAgIGluZGV4TnVtYmVyOiB0cnVlLAogICAgICAgICAgLy8g5bqP5Y+3CiAgICAgICAgICBsb2FkaW5nOiBmYWxzZSwKICAgICAgICAgIGNvbXBvbmVudFByb3BzOiB7CiAgICAgICAgICAgIGRhdGE6IFtdLAogICAgICAgICAgICAvLyDooajmoLzmlbDmja4KICAgICAgICAgICAgaGVpZ2h0OiAnMTAwcHgnLAogICAgICAgICAgICBmb3JtUm93OiAwIC8vIOihqOWNleihjOaVsAogICAgICAgICAgfSwKCiAgICAgICAgICBwYWdlTGlzdDogewogICAgICAgICAgICB0b3RhbE51bTogMCwKICAgICAgICAgICAgY3VycmVudFBhZ2U6IDEsCiAgICAgICAgICAgIC8vIOW9k+WJjemhtQogICAgICAgICAgICBwYWdlU2l6ZTogdGhpcy4kc3RvcmUuZ2V0dGVycy5wYWdlU2l6ZSAvLyDlvZPliY3pobXmmL7npLrmnaHmlbAKICAgICAgICAgIH0sCgogICAgICAgICAgc2hvd1BhZ2U6IGZhbHNlCiAgICAgICAgfSwKICAgICAgICBkZXNEYXRhOiB7fSwKICAgICAgICAvLyDmk43kvZzor6bmg4XooajljZV2YWx1ZQogICAgICAgIGRlZmF1bHRGb3JtMjogW10sCiAgICAgICAgLy8g5pON5L2c6K+m5oOF6KGo5Y2Va2V5CiAgICAgICAgdGFibGVEZXRhaWxEYXRhOiBbXSwKICAgICAgICAvLyDooajmoLzlpITnkIbmmI7nu4bmlbDmja4KICAgICAgICBidG5JZDogJycgLy8g5LiK5Lyg5paH5Lu25o+Q5Lqk5pe25Yik5pat5piv5ZCM5oSP5pON5L2c5oiW5LiN5ZCM5oSP5pON5L2c5qCH5b+XCiAgICAgIH0KICAgIH07CiAgfSwKCiAgd2F0Y2g6IHsKICAgIGxvYWRpbmc6IGZ1bmN0aW9uIGxvYWRpbmcodmFsdWUpIHsKICAgICAgdGhpcy5saXN0TG9hZGluZyA9IHRoaXMubG9hZGluZzsKICAgIH0sCiAgICAnZGlhbG9nLnZpc2libGUnOiB7CiAgICAgIC8vIOWmguaenOa1j+iniOWZqOato+W4uOWFs+mXreeKtuaAgQogICAgICBoYW5kbGVyOiBmdW5jdGlvbiBoYW5kbGVyKHZhbCkgewogICAgICAgIGlmICh2YWwgPT09IGZhbHNlKSB7CiAgICAgICAgICAvLyDnm5HlkKzmtYHmsLTlj7fmk43kvZzor6bmg4XlvLnnqpflhbPpl63nirbmgIEgICDlhbPpl63ml7bph4rmlL7ku7vliqEKICAgICAgICAgIGlmICh0aGlzLiRzdG9yZS5nZXR0ZXJzLnJlbGVhc2VUYXNrRmxhZyAmJiAhY29tbW9uQmxhbmsodGhpcy5iaW5kVGFza0lkKSkgewogICAgICAgICAgICByZWxlYXNlRmxvd1Rhc2sodGhpcy5iaW5kVGFza0lkKTsKICAgICAgICAgIH0KICAgICAgICB9CiAgICAgIH0sCiAgICAgIGRlZXA6IHRydWUKICAgIH0KICB9LAogIGNyZWF0ZWQ6IGZ1bmN0aW9uIGNyZWF0ZWQoKSB7CiAgICB0aGlzLnF1ZXJ5TGlzdCgpOwogIH0sCiAgbW91bnRlZDogZnVuY3Rpb24gbW91bnRlZCgpIHsKICAgIHZhciBfdGhpcyA9IHRoaXM7CiAgICAvLyDmtYHnqIvlpITnkIblrozmr5Ug5pu05paw5pWw5o2u5a6h5qC45bel5L2c5Y+w5pyq5aSE55CG5YiX6KGoCiAgICB0aGlzLiRidXMuJG9uKCdvcGVyQXBwcm92ZVVwZGF0ZScsIGZ1bmN0aW9uIChkYXRhKSB7CiAgICAgIGlmIChkYXRhKSB7CiAgICAgICAgX3RoaXMucXVlcnlMaXN0KCk7CiAgICAgIH0KICAgIH0pOwogIH0sCiAgLy8g6ZSA5q+B5a+55bqU6Ieq5a6a5LmJ5LqL5Lu2CiAgYmVmb3JlRGVzdHJveTogZnVuY3Rpb24gYmVmb3JlRGVzdHJveSgpIHsKICAgIHRoaXMuJGJ1cy4kb2ZmKCdvcGVyQXBwcm92ZVVwZGF0ZScpOwogIH0sCiAgbWV0aG9kczogewogICAgLyoqDQogICAgICog5oyJ6ZKu77ya5p+l6K+iKi8KICAgIHF1ZXJ5TGlzdDogZnVuY3Rpb24gcXVlcnlMaXN0KGN1cnJlbnRQYWdlKSB7CiAgICAgIHZhciBfdGhpczIgPSB0aGlzOwogICAgICB0aGlzLnNob3dMb2FkaW5nKHRydWUpOwogICAgICB2YXIgbXNnID0gewogICAgICAgIHBhcmFtZXRlckxpc3Q6IFtdLAogICAgICAgIHF1ZXJ5X3R5cGU6IHRoaXMuZGVmYXVsdEZvcm0ucXVlcnlfdHlwZSwKICAgICAgICBhcHByb3ZlX3R5cGU6IHRoaXMuZGVmYXVsdEZvcm0uYXBwcm92ZV90eXBlLAogICAgICAgIGFwcHJvdmVfYWdyZWU6IHRoaXMuZGVmYXVsdEZvcm0uYXBwcm92ZV9hZ3JlZSwKICAgICAgICBpbnN0X2lkOiB0aGlzLmRlZmF1bHRGb3JtLmluc3RfaWQsCiAgICAgICAgb3Blcl90eXBlX2ZsYWc6IHRoaXMuZGVmYXVsdEZvcm0ub3Blcl90eXBlX2ZsYWcsCiAgICAgICAgYXBwbHlfdXNlcjogdGhpcy5kZWZhdWx0Rm9ybS5hcHBseV91c2VyLAogICAgICAgIHN0YXJ0X2FwcGx5X3RpbWU6IHRoaXMuZGVmYXVsdEZvcm0uZGF0ZV90d28gIT09IG51bGwgPyB0aGlzLmRlZmF1bHRGb3JtLmRhdGVfdHdvWzBdID09PSB1bmRlZmluZWQgPyAnJyA6IHRoaXMuZGVmYXVsdEZvcm0uZGF0ZV90d29bMF0gKyAnMDAwMDAwJyAvLyDmoLzlvI/ljJYgeXl5eU1NZGRISG1tc3MKICAgICAgICA6ICcnLAogICAgICAgIGVuZF9hcHBseV90aW1lOiB0aGlzLmRlZmF1bHRGb3JtLmRhdGVfdHdvICE9PSBudWxsID8gdGhpcy5kZWZhdWx0Rm9ybS5kYXRlX3R3b1sxXSA9PT0gdW5kZWZpbmVkID8gJycgOiB0aGlzLmRlZmF1bHRGb3JtLmRhdGVfdHdvWzFdICsgJzI0MDAwMCcgLy8g5qC85byP5YyWIHl5eXlNTWRkSEhtbXNzCiAgICAgICAgOiAnJywKICAgICAgICBjdXJyZW50UGFnZTogY3VycmVudFBhZ2UgfHwgdGhpcy50YWJsZS5wYWdlTGlzdC5jdXJyZW50UGFnZSwKICAgICAgICBvcGVyYXRpb25SZXF1ZXN0V29ya2JlbmNoX3BhZ2VOdW06IHRoaXMudGFibGUucGFnZUxpc3QucGFnZVNpemUKICAgICAgfTsKICAgICAgLy8g5p+l6K+iCiAgICAgIHF1ZXJ5KG1zZykudGhlbihmdW5jdGlvbiAocmVzKSB7CiAgICAgICAgdmFyIF9yZXMkcmV0TWFwID0gcmVzLnJldE1hcCwKICAgICAgICAgIGxpc3QgPSBfcmVzJHJldE1hcC5saXN0LAogICAgICAgICAgdG90YWxOdW0gPSBfcmVzJHJldE1hcC50b3RhbE51bSwKICAgICAgICAgIGN1cnJlbnRQYWdlID0gX3JlcyRyZXRNYXAuY3VycmVudFBhZ2U7CiAgICAgICAgX3RoaXMyLnRhYmxlLmNvbXBvbmVudFByb3BzLmRhdGEgPSBsaXN0OwogICAgICAgIF90aGlzMi50YWJsZS5wYWdlTGlzdC50b3RhbE51bSA9IHRvdGFsTnVtOwogICAgICAgIF90aGlzMi50YWJsZS5wYWdlTGlzdC5jdXJyZW50UGFnZSA9IGN1cnJlbnRQYWdlOwogICAgICAgIF90aGlzMi50YWJsZS5jb21wb25lbnRQcm9wcy5kYXRhLmZvckVhY2goZnVuY3Rpb24gKGl0ZW0sIGluZGV4KSB7CiAgICAgICAgICAvLyDlrqHmibnnirbmgIHmoLzlvI/ljJYKICAgICAgICAgIGlmIChpdGVtLmFwcHJvdmVfYWdyZWUgPT09ICcyJykgewogICAgICAgICAgICAvLyAgaXRlbS5hcHByb3ZlX2FncmVlID0gJ+acque7iOWuoScgLy8g5a6h5om554q25oCBCiAgICAgICAgICAgIGlmICghY29tbW9uQmxhbmsoaXRlbS5jdXJyZW50X2FwcHJvdmVfb3JnYW4pKSB7CiAgICAgICAgICAgICAgcmV0dXJuIG9yZ2FuTmFtZUZvcm1hdChpdGVtLmN1cnJlbnRfYXBwcm92ZV9vcmdhbik7CiAgICAgICAgICAgIH0KICAgICAgICAgICAgaWYgKCFjb21tb25CbGFuayhpdGVtLnJvbGVfbm8pKSB7CiAgICAgICAgICAgICAgLy8g5b6F5a6h5om56KeS6Imy5qC85byP5YyWCiAgICAgICAgICAgICAgX3RoaXMyLiRzdG9yZS5nZXR0ZXJzLnVzZXJMaXN0Lm1hcChmdW5jdGlvbiAodmFsKSB7CiAgICAgICAgICAgICAgICBpZiAoIWNvbW1vbkJsYW5rKHZhbC5yb2xlX25vKSkgewogICAgICAgICAgICAgICAgICB2YWwucm9sZV9uby5zcGxpdCgnLCcpLmZvckVhY2goZnVuY3Rpb24gKGkpIHsKICAgICAgICAgICAgICAgICAgICBpZiAoaXRlbS5yb2xlX25vID09PSBpKSB7CiAgICAgICAgICAgICAgICAgICAgICBpdGVtLnJvbGVfbm8gPSAiIi5jb25jYXQoaSwgIi0iKS5jb25jYXQodmFsLnZhbHVlKTsKICAgICAgICAgICAgICAgICAgICB9CiAgICAgICAgICAgICAgICAgIH0pOwogICAgICAgICAgICAgICAgfQogICAgICAgICAgICAgIH0pOwogICAgICAgICAgICB9CiAgICAgICAgICB9IGVsc2UgewogICAgICAgICAgICAvLyBpdGVtLmFwcHJvdmVfYWdyZWUgPSBhcHByb3ZlQWdyZWUoaXRlbS5hcHByb3ZlX2FncmVlKQogICAgICAgICAgICBpdGVtLmN1cnJlbnRfYXBwcm92ZV9vcmdhbiA9ICcnOwogICAgICAgICAgICBpdGVtLnJvbGVfbm8gPSAnJzsKICAgICAgICAgIH0KICAgICAgICB9KTsKICAgICAgICBfdGhpczIuc2hvd0xvYWRpbmcoZmFsc2UpOwogICAgICB9KTsKICAgIH0sCiAgICAvKioNCiAgICAgKuihqOagvC3lrqHmibnnu5Pmnpzngrnlh7vkuovku7YNCiAgICAgKiBAcGFyYW0gcm93IOW9k+WJjeihjOaVsOaNrg0KICAgICAqLwogICAgYXBwcm92ZVJlc3Q6IGZ1bmN0aW9uIGFwcHJvdmVSZXN0KHJvdykgewogICAgICB0aGlzLmFwcm92ZUN1cnJlbnRSb3cgPSByb3c7CiAgICAgIGlmIChyb3cpIHsKICAgICAgICAvLyDlrqHmibnnu5PmnpzkuLoxLeW3sumAmui/hyYm5Y+C5pWw6YWN572u57G75Z6L5Li65a+85Ye6JibnlLPor7fnlKjmiLc9PT3lvZPliY3nlKjmiLcgICDngrnlh7vlrqHmibnnu5Pmnpzlj6/kuIvovb3lr7zlh7rmlofku7YKICAgICAgICBpZiAocm93LmFwcHJvdmVfYWdyZWUgPT09ICcxJyAmJiByb3cub3Blcl90eXBlX2ZsYWcgPT09ICflr7zlh7onICYmIHJvdy5hcHBseV91c2VyID09PSB0aGlzLiRzdG9yZS5nZXR0ZXJzLnVzZXJObykgewogICAgICAgICAgLy8g6LCD55So5LiL6L295pa55rOVCiAgICAgICAgICB2YXIgZG93blBhdGhBcnIgPSByb3cuZmlsZV9wYXRoLnNwbGl0KCdcXCcpOwogICAgICAgICAgdmFyIGZpbGVfbmFtZSA9IGRvd25QYXRoQXJyWzFdOwogICAgICAgICAgdmFyIGZpbGVfcGF0aCA9IGRvd25QYXRoQXJyWzBdICsgJyMjJyArIGRvd25QYXRoQXJyWzFdOwogICAgICAgICAgLy8gY29uc3QgZmlsZV9wYXRoID0gcm93LmZpbGVfcGF0aC5yZXBsYWNlKCdcXCcsICcvJykKICAgICAgICAgIC8vIGNvbnN0IGZpbGVfbmFtZSA9IHRoaXMuZ2V0QXR0YWNobWVudE5hbWUoZmlsZV9wYXRoKQogICAgICAgICAgdGhpcy5kb3dubG9hZEZpbGUoZmlsZV9uYW1lLCBmaWxlX3BhdGggKyAnIyMnICsgZmlsZV9uYW1lKTsKICAgICAgICB9IGVsc2UgaWYgKHJvdy5hcHByb3ZlX2FncmVlID09PSAnMycpIHsKICAgICAgICAgIC8vIOWuoeaJueW8guW4uAogICAgICAgICAgdGhpcy5kaWFsb2cyLnZpc2libGUgPSB0cnVlOwogICAgICAgICAgaWYgKHJvdy5vcGVyX3R5cGVfZmxhZyA9PT0gJ+WvvOWFpScpIHsKICAgICAgICAgICAgLy8g5aaC5p6c5Y+C5pWw6YWN572u57G75Z6L5Li65a+85YWlICAg6LCD55So5a+85YWl5aSx6LSl6ZSZ6K+v6K+m5oOFCiAgICAgICAgICAgIHRoaXMuYXBwcm92ZURpc0FncmVlRGV0YWlsKHJvdyk7CiAgICAgICAgICB9IGVsc2UgewogICAgICAgICAgICAvLyDlhbbku5bmk43kvZzlvILluLjor6bmg4UKICAgICAgICAgICAgdGhpcy5kaWFsb2dPcGVyLmRlZmF1bHRGb3JtMi5wdXNoKHsKICAgICAgICAgICAgICBsYWJlbDogJ+mUmeivr+ivpuaDhScsCiAgICAgICAgICAgICAgbmFtZTogJ2Vycm9yRGV0YWlsJwogICAgICAgICAgICB9KTsKICAgICAgICAgICAgdGhpcy5lcnJvck1zZyA9IHJvdy5lcnJvcl9tc2c7IC8vIHZhbHVl5YC8CiAgICAgICAgICB9CiAgICAgICAgfQogICAgICB9CiAgICB9LAogICAgLyoqDQogICAgICog5qC55o2u6ZmE5Lu26Lev5b6E6I635Y+W6ZmE5Lu25ZCN56ewDQogICAgICogQHBhcmFtCXBhdGgJ6ZmE5Lu26Lev5b6EDQogICAgICovCiAgICBnZXRBdHRhY2htZW50TmFtZTogZnVuY3Rpb24gZ2V0QXR0YWNobWVudE5hbWUocGF0aCkgewogICAgICB2YXIgaW5kZXggPSBwYXRoLmxhc3RJbmRleE9mKCdcXCcpOwogICAgICBpZiAoaW5kZXggPT09IC0xKSB7CiAgICAgICAgaW5kZXggPSBwYXRoLmxhc3RJbmRleE9mKCcvJyk7CiAgICAgIH0KICAgICAgdmFyIG5hbWUgPSBwYXRoLnN1YnN0cihpbmRleCk7CiAgICAgIGluZGV4ID0gbmFtZS5pbmRleE9mKCdfJyk7CiAgICAgIG5hbWUgPSBuYW1lLnN1YnN0cihpbmRleCArIDEpOwogICAgICBpZiAobmFtZS5pbmRleE9mKCcjIycpICE9PSAtMSkgewogICAgICAgIG5hbWUgPSBuYW1lLnNwbGl0KCcjIycpWzFdOwogICAgICB9CiAgICAgIHJldHVybiBuYW1lOwogICAgfSwKICAgIC8qKg0KICAgICAq5aSx6LSl6ZSZ6K+v5o+Q56S65pa55rOVICovCiAgICBhcHByb3ZlRGlzQWdyZWVEZXRhaWw6IGZ1bmN0aW9uIGFwcHJvdmVEaXNBZ3JlZURldGFpbChyb3cpIHsKICAgICAgdGhpcy5kaWFsb2dPcGVyLmRlZmF1bHRGb3JtMiA9IFtdOwogICAgICB0aGlzLmRpYWxvZzIudGl0bGUgPSAn5a+85YWl5aSx6LSl5pWw5o2uJzsKICAgICAgLy8g5a+85YWl5aSx6LSl6ZSZ6K+v6K+m5oOFLy9ib3grdGFibGUKICAgICAgdmFyIG9wZXJhdGlvblZhbHVlID0gSlNPTi5wYXJzZShyb3cub3BlcmF0aW9uX3ZhbHVlKTsKICAgICAgdmFyIHJldERhdGEgPSBKU09OLnBhcnNlKHJvdy5yZW1hcmtzKTsKICAgICAgLy8gZGVzY3BpcHRpb25zICDmlbDmja4KCiAgICAgIHRoaXMuZGlhbG9nT3Blci5kZWZhdWx0Rm9ybTIucHVzaCh7CiAgICAgICAgbGFiZWw6ICflr7zlhaXmiJDlip/mlbDph48o5p2hKScsCiAgICAgICAgbmFtZTogJ3N1Y2NDb3VudCcKICAgICAgfSwgewogICAgICAgIGxhYmVsOiAn5a+85YWl5aSx6LSl5pWw6YePKOadoSknLAogICAgICAgIG5hbWU6ICdmYWlsQ291bnQnCiAgICAgIH0sIHsKICAgICAgICBsYWJlbDogJ+WvvOWFpeWksei0peaVsOaNricsCiAgICAgICAgbmFtZTogJ2ZhaWxEYXRhJwogICAgICB9KTsKICAgICAgdGhpcy5kaWFsb2dPcGVyLmRlc0RhdGFbJ3N1Y2NDb3VudCddID0gcmV0RGF0YS5zdWNjQ291bnQ7IC8vIOaIkOWKn+aVsOmHjwogICAgICB0aGlzLmRpYWxvZ09wZXIuZGVzRGF0YVsnZmFpbENvdW50J10gPSByZXREYXRhLmZhaWxDb3VudDsgLy8g5aSx6LSl5pWw6YePCgogICAgICAvLyDlpITnkIbmmI7nu4YKICAgICAgdGhpcy5lcnJEZXRhaWwudGFibGVDb2x1bW5zID0gb3BlcmF0aW9uVmFsdWUudGFibGVfY29sdW1uOyAvLyDooajmoLwgIOihqOWktAogICAgICB0aGlzLmVyckRldGFpbC5jb21wb25lbnRQcm9wcy5kYXRhID0gcmV0RGF0YS5saXN0OyAvLyDooajmoLwgIOaVsOaNrgogICAgfSwKICAgIC8qKg0KICAgICAqIOWuoeaJuee7k+aenC3miafooYzlvILluLgt5LiL6L29DQogICAgICovCiAgICBkb3dubG9hZEVyckZpbGU6IGZ1bmN0aW9uIGRvd25sb2FkRXJyRmlsZShyb3cpIHsKICAgICAgdmFyIGRvd25QYXRoID0gSlNPTi5wYXJzZShyb3cucmVtYXJrcykuaW1wb3J0RmFpbEV4Y2VsUGF0aDsKICAgICAgdmFyIGRvd25QYXRoQXJyID0gZG93blBhdGguc3BsaXQoJ1xcJyk7CiAgICAgIHZhciBmaWxlTmFtZSA9IGRvd25QYXRoQXJyWzFdOwogICAgICAvLyBjb25zdCBmaWxlX3BhdGggPSBkb3duUGF0aC5yZXBsYWNlKCdcXCcsICcvJykKICAgICAgdmFyIGZpbGVfcGF0aCA9IGRvd25QYXRoQXJyWzBdICsgJyMjJyArIGRvd25QYXRoQXJyWzFdOwogICAgICAvLyBjb25zb2xlLmxvZygncGF0aCcsZG93blBhdGgsIGRvd25QYXRoQXJyLGZpbGVfcGF0aCk7CiAgICAgIHRoaXMuZG93bmxvYWRGaWxlKGZpbGVOYW1lLCBmaWxlX3BhdGgpOwogICAgfSwKICAgIC8qKg0KICAgICAq5rWB5rC05Y+377ya5pON5L2c6K+m5oOFDQogICAgICogQHBhcmFtIHJvdyDlvZPliY3ooYzmlbDmja4NCiAgICAgKi8KICAgIHJvd0NsaWNrOiBmdW5jdGlvbiByb3dDbGljayhyb3cpIHsKICAgICAgdmFyIF90aGlzMyA9IHRoaXM7CiAgICAgIHJldHVybiBfYXN5bmNUb0dlbmVyYXRvciggLyojX19QVVJFX18qL19yZWdlbmVyYXRvclJ1bnRpbWUoKS5tYXJrKGZ1bmN0aW9uIF9jYWxsZWUoKSB7CiAgICAgICAgdmFyIGRldGFpbERhdGE7CiAgICAgICAgcmV0dXJuIF9yZWdlbmVyYXRvclJ1bnRpbWUoKS53cmFwKGZ1bmN0aW9uIF9jYWxsZWUkKF9jb250ZXh0KSB7CiAgICAgICAgICB3aGlsZSAoMSkgewogICAgICAgICAgICBzd2l0Y2ggKF9jb250ZXh0LnByZXYgPSBfY29udGV4dC5uZXh0KSB7CiAgICAgICAgICAgICAgY2FzZSAwOgogICAgICAgICAgICAgICAgX2NvbnRleHQubmV4dCA9IDI7CiAgICAgICAgICAgICAgICByZXR1cm4gc3lzTWVzc2FnZU9wZXJhdGlvblJlcXVlc3REZWF0aWxTaG93KHJvdywgX3RoaXMzLmRlZmF1bHRGb3JtLnF1ZXJ5X3R5cGUpOwogICAgICAgICAgICAgIGNhc2UgMjoKICAgICAgICAgICAgICAgIGRldGFpbERhdGEgPSBfY29udGV4dC5zZW50OwogICAgICAgICAgICAgICAgaWYgKCFjb21tb25CbGFuayhkZXRhaWxEYXRhKSAmJiBkZXRhaWxEYXRhWzJdID09PSB0cnVlKSB7CiAgICAgICAgICAgICAgICAgIC8vIGhhdmVSaWdodCDkuLp0cnVlCiAgICAgICAgICAgICAgICAgIF90aGlzMy5jaGFuZ2VPcGVyVmlzaWJsZSh0cnVlKTsKICAgICAgICAgICAgICAgICAgX3RoaXMzLmRpYWxvZ09wZXIuY3VycmVudFJvdyA9IHJvdzsKICAgICAgICAgICAgICAgICAgX3RoaXMzLmRpYWxvZ09wZXIucXVlcnlUeXBlID0gZGV0YWlsRGF0YVsxXTsgLy8g5p+l6K+i57G75Z6LMC3miJHnmoTnlLPor7cgMS3miJHnmoTku6Plip4gMi3miJHnmoTlt7Llip4KICAgICAgICAgICAgICAgICAgX3RoaXMzLmRpYWxvZ09wZXIudGFibGVEZXRhaWxEYXRhID0gZGV0YWlsRGF0YVszXTsKICAgICAgICAgICAgICAgIH0gZWxzZSBpZiAoIWRldGFpbERhdGFbMl0pIHsKICAgICAgICAgICAgICAgICAgLy8gaGF2ZVJpZ2h0IOS4umZhbHNlCiAgICAgICAgICAgICAgICAgIGNvbW1vbk1zZ0luZm8oJ+afpeivouivpuaDheaVsOaNruS4uuepuu+8gScsIF90aGlzMyk7CiAgICAgICAgICAgICAgICB9CiAgICAgICAgICAgICAgY2FzZSA0OgogICAgICAgICAgICAgIGNhc2UgImVuZCI6CiAgICAgICAgICAgICAgICByZXR1cm4gX2NvbnRleHQuc3RvcCgpOwogICAgICAgICAgICB9CiAgICAgICAgICB9CiAgICAgICAgfSwgX2NhbGxlZSk7CiAgICAgIH0pKSgpOwogICAgfSwKICAgIC8qKg0KICAgICAqIOa1geeoi+S6i+mhuS3mk43kvZznlLPor7ct5pON5L2c6K+m5oOFIC0g5by556qX5YWz6ZetDQogICAgICogQHBhcmFtIHtCb29sZWFufSBwYXJhbSDlvLnlh7rmoYbmmL7npLrpmpDol4/phY3nva4qLwogICAgY2hhbmdlT3BlclZpc2libGU6IGZ1bmN0aW9uIGNoYW5nZU9wZXJWaXNpYmxlKHBhcmFtKSB7CiAgICAgIHRoaXMuZGlhbG9nT3Blci52aXNpYmxlID0gcGFyYW07CiAgICB9LAogICAgLyoqDQogICAgICog5a6h5om557uT5p6c5omn6KGM5byC5bi45by55Ye65qGGIC0g5YWz6ZetDQogICAgICovCiAgICBkaWFsb2cyQ2xvc2U6IGZ1bmN0aW9uIGRpYWxvZzJDbG9zZSgpIHsKICAgICAgdGhpcy5kaWFsb2cyLnZpc2libGUgPSBmYWxzZTsKICAgIH0sCiAgICAvKioNCiAgICAgKumhteeggeabtOaWsCAqLwogICAgZ2V0TGlzdDogZnVuY3Rpb24gZ2V0TGlzdChwYWdlUGFyYW0pIHsKICAgICAgdmFyIGN1cnJlbnRQYWdlID0gcGFnZVBhcmFtLmN1cnJlbnRQYWdlLAogICAgICAgIHBhZ2VTaXplID0gcGFnZVBhcmFtLnBhZ2VTaXplOwogICAgICB0aGlzLnRhYmxlLnBhZ2VMaXN0LnBhZ2VTaXplID0gcGFnZVNpemU7CiAgICAgIHRoaXMudGFibGUucGFnZUxpc3QuY3VycmVudFBhZ2UgPSBjdXJyZW50UGFnZTsKICAgICAgdGhpcy5xdWVyeUxpc3QoKTsKICAgIH0sCiAgICAvKioNCiAgICAgKiDliqDovb3kuK3liqjnlLvphY3nva4qLwogICAgc2hvd0xvYWRpbmc6IGZ1bmN0aW9uIHNob3dMb2FkaW5nKCkgewogICAgICB0aGlzLmxpc3RMb2FkaW5nID0gIXRoaXMubGlzdExvYWRpbmc7CiAgICB9CiAgfQp9Ow=="}, null]}