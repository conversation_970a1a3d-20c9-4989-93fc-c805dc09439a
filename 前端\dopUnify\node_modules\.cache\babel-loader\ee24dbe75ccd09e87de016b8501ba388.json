{"remainingRequest": "E:\\华南区项目\\中国银行\\开发区\\科技公司运营平台\\前端\\dopUnify\\node_modules\\thread-loader\\dist\\cjs.js!E:\\华南区项目\\中国银行\\开发区\\科技公司运营平台\\前端\\dopUnify\\node_modules\\babel-loader\\lib\\index.js!E:\\华南区项目\\中国银行\\开发区\\科技公司运营平台\\前端\\dopUnify\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!E:\\华南区项目\\中国银行\\开发区\\科技公司运营平台\\前端\\dopUnify\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\华南区项目\\中国银行\\开发区\\科技公司运营平台\\前端\\dopUnify\\src\\layout\\components\\SublicenseMan\\index.vue?vue&type=script&lang=js&", "dependencies": [{"path": "E:\\华南区项目\\中国银行\\开发区\\科技公司运营平台\\前端\\dopUnify\\src\\layout\\components\\SublicenseMan\\index.vue", "mtime": 1721631954662}, {"path": "E:\\华南区项目\\中国银行\\开发区\\科技公司运营平台\\前端\\dopUnify\\babel.config.js", "mtime": 1721631954662}, {"path": "E:\\华南区项目\\中国银行\\开发区\\科技公司运营平台\\前端\\dopUnify\\.babelrc", "mtime": 1721631954662}, {"path": "E:\\华南区项目\\中国银行\\开发区\\科技公司运营平台\\前端\\dopUnify\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "E:\\华南区项目\\中国银行\\开发区\\科技公司运营平台\\前端\\dopUnify\\node_modules\\thread-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "E:\\华南区项目\\中国银行\\开发区\\科技公司运营平台\\前端\\dopUnify\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "E:\\华南区项目\\中国银行\\开发区\\科技公司运营平台\\前端\\dopUnify\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "E:\\华南区项目\\中国银行\\开发区\\科技公司运营平台\\前端\\dopUnify\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1655715099000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, null]}