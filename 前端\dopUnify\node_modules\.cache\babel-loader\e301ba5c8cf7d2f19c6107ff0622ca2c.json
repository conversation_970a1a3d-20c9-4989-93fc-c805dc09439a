{"remainingRequest": "D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\node_modules\\babel-loader\\lib\\index.js!D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\node_modules\\eslint-loader\\index.js??ref--13-0!D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\src\\views\\system\\config\\application\\info.js", "dependencies": [{"path": "D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\src\\views\\system\\config\\application\\info.js", "mtime": 1688371436271}, {"path": "D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\babel.config.js", "mtime": 1686019818359}, {"path": "D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\node_modules\\eslint-loader\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:aW1wb3J0IHsgZGljdGlvbmFyeUZpZWRzIH0gZnJvbSAnQC91dGlscy9kaWN0aW9uYXJ5JzsgLy8g5a2X5YW46YWN572uCgovLyDooajljZUKZXhwb3J0IHZhciBjb25maWcgPSBmdW5jdGlvbiBjb25maWcodGhhdCkgewogIHJldHVybiB7CiAgICBvcmdhbl9ubzogewogICAgICBjb21wb25lbnQ6ICdzZWxlY3QtdHJlZScsCiAgICAgIGxhYmVsOiAn5omA5bGe5py65p6EJywKICAgICAgY29uZmlnOiB7CiAgICAgICAgcnVsZXM6IFt7CiAgICAgICAgICBtaW46IDAsCiAgICAgICAgICBtYXg6IDEwLAogICAgICAgICAgbWVzc2FnZTogJ+ivt+acgOWkmuWhq+WGmTEw5Liq5a2X56ymJwogICAgICAgIH1dCiAgICAgIH0sCiAgICAgIGNvbFNwYW46IDgsCiAgICAgIG5hbWU6ICdvcmdhbl9ubycsCiAgICAgIGNvbXBvbmVudFByb3BzOiB7CiAgICAgICAgLy8gaW5wdXTnu4Tku7bphY3nva4KICAgICAgICBjbGVhcmFibGU6IHRydWUsCiAgICAgICAgZmlsdGVyYWJsZTogdHJ1ZSAvLyDmmK/lkKblj6/mkJzntKIKICAgICAgfSwKCiAgICAgIG9wdGlvbnM6IFtdCiAgICB9LAogICAgc2VyX3R5cGU6IHsKICAgICAgY29tcG9uZW50OiAnc2VsZWN0JywKICAgICAgbGFiZWw6ICfmnI3liqHnsbvlnosnLAogICAgICBjb2xTcGFuOiA4LAogICAgICBuYW1lOiAnc2VyX3R5cGUnLAogICAgICBjb25maWc6IHsKICAgICAgICBydWxlczogW3sKICAgICAgICAgIG1pbjogMCwKICAgICAgICAgIG1heDogMSwKICAgICAgICAgIG1lc3NhZ2U6ICfor7fmnIDlpJrloavlhpkx5Liq5a2X56ymJwogICAgICAgIH1dCiAgICAgIH0sCiAgICAgIGNvbXBvbmVudFByb3BzOiB7CiAgICAgICAgcGxhY2Vob2xkZXI6ICfor7fpgInmi6knLAogICAgICAgIGNsZWFyYWJsZTogdHJ1ZQogICAgICB9LAogICAgICBvcHRpb25zOiBkaWN0aW9uYXJ5RmllZHMoJ1NFUlZFUl9UWVBFJykKICAgIH0KICB9Owp9Ow=="}, {"version": 3, "names": ["dictionaryFieds", "config", "that", "organ_no", "component", "label", "rules", "min", "max", "message", "colSpan", "name", "componentProps", "clearable", "filterable", "options", "ser_type", "placeholder"], "sources": ["D:/1_Project/XYD_Project/dop-4.0/dop-4.1-qianduan-unify/dopUnify/src/views/system/config/application/info.js"], "sourcesContent": ["import { dictionaryFieds } from '@/utils/dictionary' // 字典配置\n\n// 表单\nexport const config = (that) => ({\n  organ_no: {\n    component: 'select-tree',\n    label: '所属机构',\n    config: {\n      rules: [{ min: 0, max: 10, message: '请最多填写10个字符' }]\n    },\n    colSpan: 8,\n    name: 'organ_no',\n    componentProps: {\n      // input组件配置\n      clearable: true,\n      filterable: true // 是否可搜索\n    },\n    options: []\n  },\n  ser_type: {\n    component: 'select',\n    label: '服务类型',\n    colSpan: 8,\n    name: 'ser_type',\n    config: { rules: [{ min: 0, max: 1, message: '请最多填写1个字符' }] },\n    componentProps: {\n      placeholder: '请选择',\n      clearable: true\n    },\n    options: dictionaryFieds('SERVER_TYPE')\n  }\n})\n"], "mappings": "AAAA,SAASA,eAAe,QAAQ,oBAAoB,EAAC;;AAErD;AACA,OAAO,IAAMC,MAAM,GAAG,SAATA,MAAM,CAAIC,IAAI;EAAA,OAAM;IAC/BC,QAAQ,EAAE;MACRC,SAAS,EAAE,aAAa;MACxBC,KAAK,EAAE,MAAM;MACbJ,MAAM,EAAE;QACNK,KAAK,EAAE,CAAC;UAAEC,GAAG,EAAE,CAAC;UAAEC,GAAG,EAAE,EAAE;UAAEC,OAAO,EAAE;QAAa,CAAC;MACpD,CAAC;MACDC,OAAO,EAAE,CAAC;MACVC,IAAI,EAAE,UAAU;MAChBC,cAAc,EAAE;QACd;QACAC,SAAS,EAAE,IAAI;QACfC,UAAU,EAAE,IAAI,CAAC;MACnB,CAAC;;MACDC,OAAO,EAAE;IACX,CAAC;IACDC,QAAQ,EAAE;MACRZ,SAAS,EAAE,QAAQ;MACnBC,KAAK,EAAE,MAAM;MACbK,OAAO,EAAE,CAAC;MACVC,IAAI,EAAE,UAAU;MAChBV,MAAM,EAAE;QAAEK,KAAK,EAAE,CAAC;UAAEC,GAAG,EAAE,CAAC;UAAEC,GAAG,EAAE,CAAC;UAAEC,OAAO,EAAE;QAAY,CAAC;MAAE,CAAC;MAC7DG,cAAc,EAAE;QACdK,WAAW,EAAE,KAAK;QAClBJ,SAAS,EAAE;MACb,CAAC;MACDE,OAAO,EAAEf,eAAe,CAAC,aAAa;IACxC;EACF,CAAC;AAAA,CAAC"}]}