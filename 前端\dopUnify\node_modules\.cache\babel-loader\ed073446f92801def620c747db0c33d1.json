{"remainingRequest": "D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\node_modules\\babel-loader\\lib\\index.js!D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\node_modules\\eslint-loader\\index.js??ref--13-0!D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\src\\utils\\dictionary.js", "dependencies": [{"path": "D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\src\\utils\\dictionary.js", "mtime": 1692784431448}, {"path": "D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\babel.config.js", "mtime": 1686019818359}, {"path": "D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\.babelrc", "mtime": 1705285078668}, {"path": "D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\node_modules\\eslint-loader\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["store", "commonBlank", "whatType", "commonMsgError", "dictionaryVar", "Common", "flagChange", "dictionaryFieds", "fieldId", "systemNo", "systemType", "fieldStr", "defaultSystemNo", "type", "state", "common", "dictionaryLet", "dictionaryGet", "Object", "keys", "length", "datas", "fieldArr", "split", "sort", "key", "arr", "id", "label", "value", "dictionary", "dictionarySet", "obj", "flag", "dictionaryConst", "assign", "dispatch", "treeDataTranslate", "data", "pid", "name", "parents", "roleTypeList", "dataDic", "i", "indexOf", "role_mode", "roleTypeMap", "children", "parent", "undefined", "j", "push", "commonOrganDataUp", "organNo", "organList", "hasSelf", "allOrganData", "organData", "item", "isParent", "pId", "apply", "rightTreeDataTranslate", "parentOrgan", "datass", "falg", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "tree<PERSON>ata<PERSON>rgan", "organ_no", "dataNode", "organTreeChildren", "getDataValue", "getDataLabel", "home_show", "btnDatas", "handle", "getExtendSource", "extendSource", "constructor", "String", "msg", "parameterList", "Promise", "resolve", "reject", "then", "response", "list", "retMap", "for<PERSON>ach", "catch", "err", "alert", "dictionarySubsystemFieds"], "sources": ["D:/1_Project/XYD_Project/dop-4.0/dop-4.1-qianduan-unify/dopUnify/src/utils/dictionary.js"], "sourcesContent": ["// 字典:\nimport store from '@/store'\nimport { commonBlank, whatType } from './common'\nimport { commonMsgError } from '@/utils/message.js' // 提示信息\nimport dictionaryVar from './dictionaryVar' // 字段码值\nimport { Common } from '@/api'\nlet flagChange = false // 是否更新\n\n/**\n * 获取字典： 后台传入字典, 获取数组\n * @param {String} fieldId 字典key值，从后台获取\n * @param {Stringd} systemNo 当前字典所在系统的系统号 不传默认是门户当中的字典\n * @param {Boolean} type 当前字典中是否存在该字段 dictionaryFieds('CM7000', 'UNIFY', true)/dictionaryFieds('SERVICE_MODULE', 'UNIFY')\n * */\nexport function dictionaryFieds(fieldId, systemNo, systemType) {\n  let fieldStr = ''\n  let defaultSystemNo = 'UNIFY'\n  let type = false\n  if (systemNo) {\n    if (whatType(systemNo) === 'Boolean') {\n      systemType = systemNo\n    } else {\n      defaultSystemNo = 'UNIFY'\n    }\n  }\n  if (systemType) {\n    type = systemType\n  }\n  if (type) {\n    fieldStr = store.state.common.dictionaryLet[defaultSystemNo][fieldId]\n  } else {\n    fieldStr =\n      store.state.common.dictionaryLet[defaultSystemNo][dictionaryGet(fieldId)]\n  }\n  if (commonBlank(fieldStr)) {\n    return []\n  }\n\n  if (Object.keys(fieldStr).length === 0) {\n    // console.log('本地存储中没有字典号 ' + fieldId + ' 对应的数据')\n    return []\n  }\n  let datas = []\n  const fieldArr = fieldStr.split('&').sort()\n  for (const key of fieldArr) {\n    const arr = key.split('#-#')\n    datas = [...datas, { id: arr[0], label: arr[2], value: arr[1] }]\n  }\n  return datas\n}\n\n/**\n * 获取字典: 常量\n * @param {String} key 字典key\n * */\nexport function dictionaryGet(key) {\n  const datas = dictionary('get', key)\n  return datas\n}\n\n/**\n * 设置字典: 常量\n * @param {Objext} obj 设置字典的对象*/\nexport function dictionarySet(obj) {\n  dictionary('set', obj)\n}\n\n/**\n * 字典配置: 常量\n * @param {String} flag 字典配置 get、set\n * @param {Object/String} obj\n */\nexport function dictionary(flag, obj) {\n  let dictionary = dictionaryVar\n  if (flag === 'get') {\n    if (flagChange) {\n      dictionary = store.state.common.dictionaryConst\n    }\n    return dictionary[obj]\n  } else {\n    dictionary = Object.assign({}, dictionary, obj)\n    store.dispatch('common/dictionary', dictionary)\n    flagChange = true\n  }\n}\n\n/**\n * 树形数据转换 角色\n * @param {*} data\n */\nexport function treeDataTranslate(data) {\n  const id = 'role_no'\n  const pid = 'role_mode'\n  const name = 'role_name'\n  // 建立个树形结构,需要定义个最顶层的父节点，pId是1\n  let parents = []\n  let roleTypeList = []\n  const dataDic = dictionaryFieds('ROLE_MODE')\n  for (let i = 0; i < data.length; i++) {\n    if (roleTypeList.indexOf(data[i].role_mode) === -1) {\n      const roleTypeMap = {\n        label: dataDic[data[i].role_mode * 1 - 1].label,\n        id: data[i].role_mode,\n        pid: '00000',\n        children: []\n      }\n      roleTypeList = [...roleTypeList, data[i].role_mode]\n      parents = [...parents, roleTypeMap]\n    }\n  }\n  // 调用子节点方法,参数为父节点的数组\n  const datas = children(parents, data, id, pid, name)\n  return datas\n}\n\n/**\n * 树形数据转换\n * @param {*} parent\n * @param {*} data\n * @param {*} id\n * @param {*} pid\n * @param {*} name\n */\nfunction children(parent, data, id, pid, name) {\n  if (data === undefined) {\n    return []\n  }\n  if (parent.length === 0) {\n    return\n  }\n  if (data.length !== 0) {\n    for (let i = 0; i < parent.length; i++) {\n      for (let j = 0; j < data.length; j++) {\n        if (parent[i].id === data[j][pid]) {\n          const obj = {\n            label: data[j][id] + '-' + data[j][name],\n            id: data[j][id],\n            pid: data[i][pid],\n            children: []\n          }\n          parent[i].children.push(obj)\n        }\n      }\n      children(parent[i].children, data, id, pid, name)\n    }\n  }\n  return parent\n}\n/**\n * 获取指定机构的 上级 机构树信息（数组对象）\n * 包括所有上级（父级、爷级等一直到最顶层）机构，可包含本身\n * @param organNo    目标机构号\n * @param organList    机构号列表: 不同系统之间系统号列表会有所不同\n * @param hasSelf    是否包含本身，默认为 true\n */\nexport const commonOrganDataUp = (organNo, organList, hasSelf) => {\n  const allOrganData = organList\n  if (allOrganData == null) {\n    // console.log('本地存储中没有机构信息')\n    return []\n  }\n  const organData = []\n  for (const item of allOrganData) {\n    if (item.id === organNo) {\n      if (hasSelf !== false) {\n        item.isParent = false\n        organData.push(item)\n      }\n      if (item.pId !== organNo && item.pId !== '') {\n        organData.push.apply(organData, commonOrganDataUp(item.pId))\n      }\n    }\n  }\n  return organData\n}\n/**\n * 智能排班-权限机构树形数据转换\n * @param {Array} obj 机构数组\n */\nexport const rightTreeDataTranslate = (obj) => {\n  const data = obj\n  const id = 'id'\n  const pid = 'pId'\n  const name = 'name'\n  const parentOrgan = '000001'\n  let parents = []\n  if (!data) {\n    return\n  }\n  const datass = data\n  for (let i = 0; i < data.length; i++) {\n    let falg = false\n    for (let j = 0; j < data.length; j++) {\n      if (data[i][pid] === data[j][id] && data[i][id] !== data[j][id]) {\n        falg = true\n      }\n    }\n    if (!falg) {\n      if (data[i][id] === parentOrgan) {\n        const obj = {\n          label: data[i][name],\n          id: data[i][id],\n          pid: '0000000',\n          children: []\n        }\n        datass[i].pId = '0000000'\n        parents = [...parents, obj]\n      } else {\n        const obj = {\n          label: data[i][name],\n          id: data[i][id],\n          pid: data[i][pid],\n          children: []\n        }\n        parents = [...parents, obj]\n      }\n    }\n  }\n  let datas = []\n  if (data.length > 0) {\n    datas = rightChildren(parents, datass, id, pid, name)\n  }\n  return datas\n}\n/**\n * 智能排班-机构初始化\n * @param {*} parent\n * @param {*} data\n * @param {*} id\n * @param {*} pid\n * @param {*} name\n */\nfunction rightChildren(parent, data, id, pid, name) {\n  if (data === undefined) {\n    return []\n  }\n  if (parent.length === 0) {\n    return\n  }\n  if (data.length !== 0) {\n    for (let i = 0; i < parent.length; i++) {\n      for (let j = 0; j < data.length; j++) {\n        if (parent[i].id === data[j][pid]) {\n          const obj = {\n            label: data[j][name],\n            id: data[j][id],\n            pid: data[i][pid],\n            children: []\n          }\n          parent[i].children.push(obj)\n        }\n      }\n      rightChildren(parent[i].children, data, id, pid, name)\n    }\n  }\n  return parent\n}\n\n/**\n * 获取organ_no下的所有机构\n * @param {Sting} organ_no 机构号\n * @param {Array} organList 机构树列表*/\nexport function treeDataOrgan(organ_no, organList) {\n  // const data = store.getters.organTree\n  const data = organList\n  let dataNode = []\n  for (const key of data) {\n    if (key.id === organ_no) {\n      dataNode = [key]\n      break\n    } else {\n      if (key.children) {\n        dataNode = organTreeChildren(key.children, organ_no)\n      }\n    }\n  }\n  return dataNode\n}\n/**\n * 获取organ_no下的所有机构 子节点遍历\n * @param {*} organ_no 机构号*/\nfunction organTreeChildren(data, organ_no) {\n  if (data.length === 0) {\n    return []\n  }\n  let dataNode = []\n  for (const key of data) {\n    if (key.id === organ_no) {\n      dataNode = [key]\n      break\n    } else {\n      if (key.children) {\n        organTreeChildren(key.children, organ_no)\n      }\n    }\n  }\n  return dataNode\n}\n\n/* ---------------字典 begin --------------- */\n/**\n * 通过value，获取label\n * @param {Array} data 字典\n * @param {String} value 值\n */\nexport function getDataValue(data, value) {\n  for (const key of data) {\n    if (key.value === value) {\n      return key\n    }\n  }\n}\n/**\n * 通过label，获取value\n * @param {Array} data 字典\n * @param {String} label 值\n */\nexport function getDataLabel(data, label) {\n  for (const key of data) {\n    if (key.label === label) {\n      return key\n    }\n  }\n}\n/**\n * 菜单 显示方式\n */\nexport const home_show = [\n  { value: '0', label: '仅在菜单显示' },\n  { value: '1', label: '同时在菜单首页显示' },\n  { value: '2', label: '仅在首页显示' }\n]\n/**\n * 按钮配置\n */\nexport const btnDatas = [\n  { value: 'btnQuery', label: '查询', type: 'primary', handle: 'handleQuery' },\n  { value: 'btnAdd', label: '新增', type: 'primary', handle: 'handleAdd' },\n  {\n    value: 'btnRegister',\n    label: '登记',\n    type: 'primary',\n    handle: 'handleRegister'\n  },\n  {\n    value: 'btnModify',\n    label: '修改',\n    type: 'success',\n    handle: 'handleModify'\n  },\n  {\n    value: 'btnModifyInfo',\n    label: '修改批次信息',\n    type: 'success',\n    handle: 'handleModifyInfo'\n  },\n  {\n    value: 'btnAbolish',\n    label: '作废',\n    type: 'primary',\n    handle: 'handleAbolish'\n  },\n  {\n    value: 'btnAnnul',\n    label: '废止',\n    type: 'danger',\n    handle: 'handleAnnul'\n  },\n  {\n    value: 'btnChangeFLow',\n    label: '变更',\n    type: 'success',\n    handle: 'handleChangeFLow'\n  },\n  { value: 'btnDelete', label: '删除', type: 'danger', handle: 'handleDelete' },\n  {\n    value: 'btnLoginOut',\n    label: '强制登出',\n    type: 'danger',\n    handle: 'handleLoginOut'\n  },\n  {\n    value: 'btnBatch',\n    label: '批量配置',\n    type: 'primary',\n    handle: 'handleBatch'\n  },\n  {\n    value: 'btnResetPsd',\n    label: '重置密码',\n    type: 'warning',\n    handle: 'handleResetPsd'\n  },\n  {\n    value: 'btnEnableDisable',\n    label: '启用/停用',\n    type: 'danger',\n    handle: 'handleEnableDisable'\n  },\n\n  {\n    value: 'btnCheckBtn',\n    label: '查看页面按钮',\n    type: 'primary',\n    handle: 'handleCheckBtn'\n  },\n  {\n    value: 'btnOperatorBtn',\n    label: '编辑页面按钮',\n    type: 'primary',\n    handle: 'handleOperatorBtn'\n  },\n\n  {\n    value: 'btnReportAdd',\n    label: '报备新增',\n    type: 'primary',\n    handle: 'handleReportAdd'\n  },\n\n  { value: 'btnSave', label: '保存', type: 'primary', handle: 'handleSave' },\n  { value: 'btnCancle', label: '取消', type: 'danger', handle: 'handleCancle' },\n\n  { value: 'btnPrev', label: '上一步', type: 'primary', handle: 'handlePrev' },\n  { value: 'btnNext', label: '下一步', type: 'primary', handle: 'handleNext' },\n  { value: 'btnPrint', label: '打印', type: 'primary', handle: 'handlePrint' },\n\n  { value: 'btnReset', label: '重置', type: 'warning', handle: 'handleReset' },\n  {\n    value: 'btnImport',\n    label: '导入',\n    type: 'primary',\n    handle: 'handleImport'\n  },\n  {\n    value: 'btnExport',\n    label: '导出',\n    type: 'primary',\n    handle: 'handleExport'\n  },\n\n  {\n    value: 'btnHandreport',\n    label: '手动报备',\n    type: 'primary',\n    handle: 'handleHandreport'\n  },\n  {\n    value: 'btnDownload',\n    label: '下载',\n    type: 'primary',\n    handle: 'handleDownload'\n  },\n  {\n    value: 'btnWatchpsd',\n    label: '查看密码',\n    type: 'primary',\n    handle: 'handleWatchpsd'\n  },\n  {\n    value: 'editAccType',\n    label: '修改账户类型',\n    type: 'primary',\n    handle: 'editAccType'\n  },\n  {\n    value: 'btnReport',\n    label: '线下报备',\n    type: 'primary',\n    handle: 'handleReport'\n  },\n  {\n    value: 'btnHanging',\n    label: '久悬上报',\n    type: 'primary',\n    handle: 'handleHanging'\n  },\n  { value: 'btnEmpty', label: '清空', type: 'warning', handle: 'handleEmpty' },\n  {\n    value: 'btnChangeState',\n    label: '更改状态',\n    type: 'primary',\n    handle: 'handleChangeState'\n  },\n  {\n    value: 'btnPrintBasicAccount',\n    label: '打印基本存款账户信息',\n    type: 'primary',\n    handle: 'handlePrintBasicAccount'\n  },\n  {\n    value: 'btnPrintPassword',\n    label: '打印基本存款人密码',\n    type: 'primary',\n    handle: 'handlePrintPassword'\n  },\n  {\n    value: 'btnSynchro',\n    label: '同步',\n    type: 'primary',\n    handle: 'handleSynchro'\n  },\n  {\n    value: 'btnUpdate',\n    label: '更新',\n    type: 'primary',\n    handle: 'handleUpdate'\n  },\n  {\n    value: 'btnPublish',\n    label: '发布',\n    type: 'success',\n    handle: 'handlePublish'\n  },\n  {\n    value: 'btnRetract',\n    label: '撤回',\n    type: 'warning',\n    handle: 'handleRetract'\n  },\n  {\n    value: 'btnApprovalStatus',\n    label: '审批情况',\n    type: 'primary',\n    handle: 'handleApprovalStatus'\n  },\n  {\n    value: 'btnReadStatus',\n    label: '阅读情况',\n    type: 'primary',\n    handle: 'handleReadStatus'\n  },\n  {\n    value: 'btnSaveDraft',\n    label: '保存草稿',\n    type: 'primary',\n    handle: 'handleSaveDraft'\n  },\n  {\n    value: 'btnSurePublish',\n    label: '确定发布',\n    type: 'primary',\n    handle: 'handleSurePublish'\n  },\n  {\n    value: 'btnApproval',\n    label: '审批',\n    type: 'primary',\n    handle: 'handleApproval'\n  },\n  {\n    value: 'btnPass',\n    label: '通过',\n    type: 'primary',\n    handle: 'handlePass'\n  },\n  {\n    value: 'btnRefuse',\n    label: '拒绝',\n    type: 'danger',\n    handle: 'handleRefuse'\n  },\n  {\n    value: 'btnSubmit',\n    label: '提交',\n    type: 'primary',\n    handle: 'handleSubmit'\n  },\n  {\n    value: 'btnRevoke',\n    label: '撤销',\n    type: 'primary',\n    handle: 'handleRevoke'\n  },\n  {\n    value: 'btnCallin',\n    label: '收回授权',\n    type: 'success',\n    handle: 'handleCallin'\n  },\n  {\n    value: 'btnAgree',\n    label: '同意',\n    type: 'primary',\n    handle: 'handleAgree'\n  },\n  {\n    value: 'btnNotAgree',\n    label: '不同意',\n    type: 'danger',\n    handle: 'handleNotAgree'\n  },\n  {\n    value: 'btnEdit',\n    label: '编辑',\n    type: 'primary',\n    handle: 'handleEdit'\n  },\n  {\n    value: 'btnHandleDo',\n    label: '手动执行',\n    type: 'primary',\n    handle: 'handleHandleDo'\n  },\n  {\n    value: 'btnElementAdd',\n    label: '添加要素',\n    type: 'primary',\n    handle: 'handleAddElement'\n  },\n  {\n    value: 'btnElementDelete',\n    label: '删除要素',\n    type: 'danger',\n    handle: 'handleDeleteElement'\n  },\n  {\n    value: 'btnMakeExam',\n    label: '补考',\n    type: 'primary',\n    handle: 'handleMakeExam'\n  },\n  {\n    value: 'btnCopy',\n    label: '复制',\n    type: 'primary',\n    handle: 'handleCopy'\n  },\n  {\n    value: 'btnStart',\n    label: '启动',\n    type: 'primary',\n    handle: 'handleStart'\n  },\n  {\n    value: 'btnExecute',\n    label: '执行',\n    type: 'primary',\n    handle: 'handleExecute'\n  },\n  {\n    value: 'btnShowLogs',\n    label: '查看日志',\n    type: 'primary',\n    handle: 'handleShowLogs'\n  },\n  {\n    value: 'btnStop',\n    label: '暂停',\n    type: 'primary',\n    handle: 'handleStop'\n  },\n  {\n    value: 'btnShowWarnInfo',\n    label: '查看预警信息',\n    type: 'primary',\n    handle: 'handleShowWarnInfo'\n  },\n  {\n    value: 'btnDoModelExecute',\n    label: '执行模型训练',\n    type: 'primary',\n    handle: 'handleDoModelExecute'\n  },\n  {\n    value: 'btnModeDownload',\n    label: '导入模版下载',\n    type: 'primary',\n    handle: 'handleModeDownload'\n  },\n  {\n    value: 'btnFinish',\n    label: '完成',\n    type: 'primary',\n    handle: 'handleFinish'\n  },\n  {\n    value: 'btnFinishConfig',\n    label: '完成配置',\n    type: 'primary',\n    handle: 'handleFinishConfig'\n  },\n  {\n    value: 'btnCensus',\n    label: '执行统计任务',\n    type: 'primary',\n    handle: 'handleCensus'\n  },\n  {\n    value: 'btnForecast',\n    label: '执行预测任务',\n    type: 'success',\n    handle: 'handleForecast'\n  },\n  {\n    value: 'btnAnswer',\n    label: '回复',\n    type: 'primary',\n    handle: 'handleAnswer'\n  },\n  {\n    value: 'btnTransfer',\n    label: '转办',\n    type: 'primary',\n    handle: 'handleTransfer'\n  },\n  {\n    value: 'btnReturn',\n    label: '回退',\n    type: 'primary',\n    handle: 'handleReturn'\n  },\n  {\n    value: 'btnBan',\n    label: '禁言',\n    type: 'primary',\n    handle: 'handleBan'\n  },\n  {\n    value: 'btnLiftingTheBan',\n    label: '解除禁言',\n    type: 'primary',\n    handle: 'liftingTheBan'\n  },\n  {\n    value: 'btnConfirm',\n    label: '回复确认',\n    type: 'primary',\n    handle: 'handleConfirm'\n  },\n  {\n    value: 'btnEvaluate',\n    label: '回复评价',\n    type: 'primary',\n    handle: 'handleEvaluate'\n  },\n  {\n    value: 'btnRelaunch',\n    label: '重新发起',\n    type: 'success',\n    handle: 'handleRelaunch'\n  },\n  {\n    value: 'btnWareHouse',\n    label: '问题入库',\n    type: 'success',\n    handle: 'handleWareHouse'\n  },\n  {\n    value: 'btnLinkKnowledge',\n    label: '关联知识库',\n    type: 'success',\n    handle: 'handleLinkKnowledge'\n  },\n  {\n    value: 'btnImportForeData',\n    label: '导入预测数据',\n    type: 'primary',\n    handle: 'handleImportForeData'\n  },\n  {\n    value: 'btnExportForeData',\n    label: '导出预测数据',\n    type: 'primary',\n    handle: 'handleExportForeData'\n  },\n  {\n    value: 'btnAdjustShift',\n    label: '调班申请',\n    type: 'primary',\n    handle: 'handleAdjustShift'\n  },\n  {\n    value: 'btnChangeShift',\n    label: '换班申请',\n    type: 'primary',\n    handle: 'handleChangeShift'\n  },\n  {\n    value: 'btnLeave',\n    label: '请假申请',\n    type: 'primary',\n    handle: 'handleLeave'\n  },\n  {\n    value: 'btnWorkOvertime',\n    label: '加班申请',\n    type: 'primary',\n    handle: 'handleWorkOvertime'\n  },\n  {\n    value: 'btnEditing',\n    label: '编辑中',\n    type: 'primary',\n    handle: 'handleEditing'\n  },\n  {\n    value: 'btnHolidayDownload',\n    label: '下载节假日模板',\n    type: 'primary',\n    handle: 'handleHolidayDownload'\n  },\n  {\n    value: 'btnHandOver',\n    label: '交接',\n    type: 'primary',\n    handle: 'handleHandOver'\n  },\n  {\n    value: 'btnImportCont',\n    label: '单价合同导入',\n    type: 'primary',\n    handle: 'handleImportCont'\n  },\n  {\n    value: 'btnImportTem',\n    label: '单价模板下载',\n    type: 'primary',\n    handle: 'handleImportTem'\n  },\n  {\n    value: 'btnInfoRegister',\n    label: '付款信息登记',\n    type: 'primary',\n    handle: 'handleInfo'\n  },\n  {\n    value: 'btnHumanConversion',\n    label: '人力转换',\n    type: 'primary',\n    handle: 'handleHumanConversion'\n  },\n  {\n    value: 'btnEmploy',\n    label: '录用',\n    type: 'primary',\n    handle: 'handleEmploy'\n  },\n  {\n    value: 'btnClosed',\n    label: '关闭',\n    type: 'primary',\n    handle: 'handleClosed'\n  }\n]\n/* ---------------字典 end --------------- */\n\n/**\n * 格式化外表数据源\n * @param {String}key 外表key值\n */\nexport function getExtendSource(key) {\n  const { extendSource } = Common\n  let datas = []\n  if (typeof key === 'string' && key.constructor === String && key !== '') {\n    const msg = {\n      parameterList: [],\n      key: key\n    }\n    return new Promise((resolve, reject) => {\n      extendSource(msg)\n        .then((response) => {\n          const { list } = response.retMap\n          if (list) {\n            list.forEach((item) => {\n              datas = [\n                ...datas,\n                {\n                  label: item.value,\n                  value: item.key\n                }\n              ]\n            })\n          }\n          resolve(datas)\n        })\n        .catch((err) => {\n          commonMsgError(err, '外表查询出错')\n        })\n    })\n  } else {\n    alert('外表对应key有误')\n  }\n}\n\n/**\n * 获取字典： 门户获取子系统数据字典\n * @param {String} fieldId 字典key值，从后台获取\n * @param {Stringd} systemNo 当前字典所在系统的系统号 不传默认是门户当中的字典\n * @param {Boolean} type 当前字典中是否存在该字段 dictionaryFieds('CM7000', 'UNIFY', true)/dictionaryFieds('SERVICE_MODULE', 'UNIFY')\n * */\nexport function dictionarySubsystemFieds(fieldId, systemNo, systemType) {\n  let fieldStr = ''\n  let defaultSystemNo = 'UNIFY'\n  let type = false\n  if (systemNo) {\n    defaultSystemNo = systemNo\n  }\n  if (systemType) {\n    type = systemType\n  }\n  if (type) {\n    fieldStr = store.state.common.dictionaryLet[defaultSystemNo][fieldId]\n  } else {\n    fieldStr =\n      store.state.common.dictionaryLet[defaultSystemNo][dictionaryGet(fieldId)]\n  }\n  if (commonBlank(fieldStr)) {\n    return []\n  }\n\n  if (Object.keys(fieldStr).length === 0) {\n    // console.log('本地存储中没有字典号 ' + fieldId + ' 对应的数据')\n    return []\n  }\n  let datas = []\n  const fieldArr = fieldStr.split('&').sort()\n  for (const key of fieldArr) {\n    const arr = key.split('#-#')\n    datas = [...datas, { id: arr[0], label: arr[2], value: arr[1] }]\n  }\n  return datas\n}\n"], "mappings": ";;;;;;;;AAAA;AACA,OAAOA,KAAK,MAAM,SAAS;AAC3B,SAASC,WAAW,EAAEC,QAAQ,QAAQ,UAAU;AAChD,SAASC,cAAc,QAAQ,oBAAoB,EAAC;AACpD,OAAOC,aAAa,MAAM,iBAAiB,EAAC;AAC5C,SAASC,MAAM,QAAQ,OAAO;AAC9B,IAAIC,UAAU,GAAG,KAAK,EAAC;;AAEvB;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASC,eAAe,CAACC,OAAO,EAAEC,QAAQ,EAAEC,UAAU,EAAE;EAC7D,IAAIC,QAAQ,GAAG,EAAE;EACjB,IAAIC,eAAe,GAAG,OAAO;EAC7B,IAAIC,IAAI,GAAG,KAAK;EAChB,IAAIJ,QAAQ,EAAE;IACZ,IAAIP,QAAQ,CAACO,QAAQ,CAAC,KAAK,SAAS,EAAE;MACpCC,UAAU,GAAGD,QAAQ;IACvB,CAAC,MAAM;MACLG,eAAe,GAAG,OAAO;IAC3B;EACF;EACA,IAAIF,UAAU,EAAE;IACdG,IAAI,GAAGH,UAAU;EACnB;EACA,IAAIG,IAAI,EAAE;IACRF,QAAQ,GAAGX,KAAK,CAACc,KAAK,CAACC,MAAM,CAACC,aAAa,CAACJ,eAAe,CAAC,CAACJ,OAAO,CAAC;EACvE,CAAC,MAAM;IACLG,QAAQ,GACNX,KAAK,CAACc,KAAK,CAACC,MAAM,CAACC,aAAa,CAACJ,eAAe,CAAC,CAACK,aAAa,CAACT,OAAO,CAAC,CAAC;EAC7E;EACA,IAAIP,WAAW,CAACU,QAAQ,CAAC,EAAE;IACzB,OAAO,EAAE;EACX;EAEA,IAAIO,MAAM,CAACC,IAAI,CAACR,QAAQ,CAAC,CAACS,MAAM,KAAK,CAAC,EAAE;IACtC;IACA,OAAO,EAAE;EACX;EACA,IAAIC,KAAK,GAAG,EAAE;EACd,IAAMC,QAAQ,GAAGX,QAAQ,CAACY,KAAK,CAAC,GAAG,CAAC,CAACC,IAAI,EAAE;EAAA,2CACzBF,QAAQ;IAAA;EAAA;IAA1B,oDAA4B;MAAA,IAAjBG,GAAG;MACZ,IAAMC,GAAG,GAAGD,GAAG,CAACF,KAAK,CAAC,KAAK,CAAC;MAC5BF,KAAK,gCAAOA,KAAK,IAAE;QAAEM,EAAE,EAAED,GAAG,CAAC,CAAC,CAAC;QAAEE,KAAK,EAAEF,GAAG,CAAC,CAAC,CAAC;QAAEG,KAAK,EAAEH,GAAG,CAAC,CAAC;MAAE,CAAC,EAAC;IAClE;EAAC;IAAA;EAAA;IAAA;EAAA;EACD,OAAOL,KAAK;AACd;;AAEA;AACA;AACA;AACA;AACA,OAAO,SAASJ,aAAa,CAACQ,GAAG,EAAE;EACjC,IAAMJ,KAAK,GAAGS,UAAU,CAAC,KAAK,EAAEL,GAAG,CAAC;EACpC,OAAOJ,KAAK;AACd;;AAEA;AACA;AACA;AACA,OAAO,SAASU,aAAa,CAACC,GAAG,EAAE;EACjCF,UAAU,CAAC,KAAK,EAAEE,GAAG,CAAC;AACxB;;AAEA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASF,UAAU,CAACG,IAAI,EAAED,GAAG,EAAE;EACpC,IAAIF,UAAU,GAAG1B,aAAa;EAC9B,IAAI6B,IAAI,KAAK,KAAK,EAAE;IAClB,IAAI3B,UAAU,EAAE;MACdwB,UAAU,GAAG9B,KAAK,CAACc,KAAK,CAACC,MAAM,CAACmB,eAAe;IACjD;IACA,OAAOJ,UAAU,CAACE,GAAG,CAAC;EACxB,CAAC,MAAM;IACLF,UAAU,GAAGZ,MAAM,CAACiB,MAAM,CAAC,CAAC,CAAC,EAAEL,UAAU,EAAEE,GAAG,CAAC;IAC/ChC,KAAK,CAACoC,QAAQ,CAAC,mBAAmB,EAAEN,UAAU,CAAC;IAC/CxB,UAAU,GAAG,IAAI;EACnB;AACF;;AAEA;AACA;AACA;AACA;AACA,OAAO,SAAS+B,iBAAiB,CAACC,IAAI,EAAE;EACtC,IAAMX,EAAE,GAAG,SAAS;EACpB,IAAMY,GAAG,GAAG,WAAW;EACvB,IAAMC,IAAI,GAAG,WAAW;EACxB;EACA,IAAIC,OAAO,GAAG,EAAE;EAChB,IAAIC,YAAY,GAAG,EAAE;EACrB,IAAMC,OAAO,GAAGpC,eAAe,CAAC,WAAW,CAAC;EAC5C,KAAK,IAAIqC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGN,IAAI,CAAClB,MAAM,EAAEwB,CAAC,EAAE,EAAE;IACpC,IAAIF,YAAY,CAACG,OAAO,CAACP,IAAI,CAACM,CAAC,CAAC,CAACE,SAAS,CAAC,KAAK,CAAC,CAAC,EAAE;MAClD,IAAMC,WAAW,GAAG;QAClBnB,KAAK,EAAEe,OAAO,CAACL,IAAI,CAACM,CAAC,CAAC,CAACE,SAAS,GAAG,CAAC,GAAG,CAAC,CAAC,CAAClB,KAAK;QAC/CD,EAAE,EAAEW,IAAI,CAACM,CAAC,CAAC,CAACE,SAAS;QACrBP,GAAG,EAAE,OAAO;QACZS,QAAQ,EAAE;MACZ,CAAC;MACDN,YAAY,gCAAOA,YAAY,IAAEJ,IAAI,CAACM,CAAC,CAAC,CAACE,SAAS,EAAC;MACnDL,OAAO,gCAAOA,OAAO,IAAEM,WAAW,EAAC;IACrC;EACF;EACA;EACA,IAAM1B,KAAK,GAAG2B,QAAQ,CAACP,OAAO,EAAEH,IAAI,EAAEX,EAAE,EAAEY,GAAG,EAAEC,IAAI,CAAC;EACpD,OAAOnB,KAAK;AACd;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS2B,QAAQ,CAACC,MAAM,EAAEX,IAAI,EAAEX,EAAE,EAAEY,GAAG,EAAEC,IAAI,EAAE;EAC7C,IAAIF,IAAI,KAAKY,SAAS,EAAE;IACtB,OAAO,EAAE;EACX;EACA,IAAID,MAAM,CAAC7B,MAAM,KAAK,CAAC,EAAE;IACvB;EACF;EACA,IAAIkB,IAAI,CAAClB,MAAM,KAAK,CAAC,EAAE;IACrB,KAAK,IAAIwB,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGK,MAAM,CAAC7B,MAAM,EAAEwB,CAAC,EAAE,EAAE;MACtC,KAAK,IAAIO,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGb,IAAI,CAAClB,MAAM,EAAE+B,CAAC,EAAE,EAAE;QACpC,IAAIF,MAAM,CAACL,CAAC,CAAC,CAACjB,EAAE,KAAKW,IAAI,CAACa,CAAC,CAAC,CAACZ,GAAG,CAAC,EAAE;UACjC,IAAMP,GAAG,GAAG;YACVJ,KAAK,EAAEU,IAAI,CAACa,CAAC,CAAC,CAACxB,EAAE,CAAC,GAAG,GAAG,GAAGW,IAAI,CAACa,CAAC,CAAC,CAACX,IAAI,CAAC;YACxCb,EAAE,EAAEW,IAAI,CAACa,CAAC,CAAC,CAACxB,EAAE,CAAC;YACfY,GAAG,EAAED,IAAI,CAACM,CAAC,CAAC,CAACL,GAAG,CAAC;YACjBS,QAAQ,EAAE;UACZ,CAAC;UACDC,MAAM,CAACL,CAAC,CAAC,CAACI,QAAQ,CAACI,IAAI,CAACpB,GAAG,CAAC;QAC9B;MACF;MACAgB,QAAQ,CAACC,MAAM,CAACL,CAAC,CAAC,CAACI,QAAQ,EAAEV,IAAI,EAAEX,EAAE,EAAEY,GAAG,EAAEC,IAAI,CAAC;IACnD;EACF;EACA,OAAOS,MAAM;AACf;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,IAAMI,iBAAiB,GAAG,SAApBA,iBAAiB,CAAIC,OAAO,EAAEC,SAAS,EAAEC,OAAO,EAAK;EAChE,IAAMC,YAAY,GAAGF,SAAS;EAC9B,IAAIE,YAAY,IAAI,IAAI,EAAE;IACxB;IACA,OAAO,EAAE;EACX;EACA,IAAMC,SAAS,GAAG,EAAE;EAAA,4CACDD,YAAY;IAAA;EAAA;IAA/B,uDAAiC;MAAA,IAAtBE,IAAI;MACb,IAAIA,IAAI,CAAChC,EAAE,KAAK2B,OAAO,EAAE;QACvB,IAAIE,OAAO,KAAK,KAAK,EAAE;UACrBG,IAAI,CAACC,QAAQ,GAAG,KAAK;UACrBF,SAAS,CAACN,IAAI,CAACO,IAAI,CAAC;QACtB;QACA,IAAIA,IAAI,CAACE,GAAG,KAAKP,OAAO,IAAIK,IAAI,CAACE,GAAG,KAAK,EAAE,EAAE;UAC3CH,SAAS,CAACN,IAAI,CAACU,KAAK,CAACJ,SAAS,EAAEL,iBAAiB,CAACM,IAAI,CAACE,GAAG,CAAC,CAAC;QAC9D;MACF;IACF;EAAC;IAAA;EAAA;IAAA;EAAA;EACD,OAAOH,SAAS;AAClB,CAAC;AACD;AACA;AACA;AACA;AACA,OAAO,IAAMK,sBAAsB,GAAG,SAAzBA,sBAAsB,CAAI/B,GAAG,EAAK;EAC7C,IAAMM,IAAI,GAAGN,GAAG;EAChB,IAAML,EAAE,GAAG,IAAI;EACf,IAAMY,GAAG,GAAG,KAAK;EACjB,IAAMC,IAAI,GAAG,MAAM;EACnB,IAAMwB,WAAW,GAAG,QAAQ;EAC5B,IAAIvB,OAAO,GAAG,EAAE;EAChB,IAAI,CAACH,IAAI,EAAE;IACT;EACF;EACA,IAAM2B,MAAM,GAAG3B,IAAI;EACnB,KAAK,IAAIM,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGN,IAAI,CAAClB,MAAM,EAAEwB,CAAC,EAAE,EAAE;IACpC,IAAIsB,IAAI,GAAG,KAAK;IAChB,KAAK,IAAIf,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGb,IAAI,CAAClB,MAAM,EAAE+B,CAAC,EAAE,EAAE;MACpC,IAAIb,IAAI,CAACM,CAAC,CAAC,CAACL,GAAG,CAAC,KAAKD,IAAI,CAACa,CAAC,CAAC,CAACxB,EAAE,CAAC,IAAIW,IAAI,CAACM,CAAC,CAAC,CAACjB,EAAE,CAAC,KAAKW,IAAI,CAACa,CAAC,CAAC,CAACxB,EAAE,CAAC,EAAE;QAC/DuC,IAAI,GAAG,IAAI;MACb;IACF;IACA,IAAI,CAACA,IAAI,EAAE;MACT,IAAI5B,IAAI,CAACM,CAAC,CAAC,CAACjB,EAAE,CAAC,KAAKqC,WAAW,EAAE;QAC/B,IAAMhC,IAAG,GAAG;UACVJ,KAAK,EAAEU,IAAI,CAACM,CAAC,CAAC,CAACJ,IAAI,CAAC;UACpBb,EAAE,EAAEW,IAAI,CAACM,CAAC,CAAC,CAACjB,EAAE,CAAC;UACfY,GAAG,EAAE,SAAS;UACdS,QAAQ,EAAE;QACZ,CAAC;QACDiB,MAAM,CAACrB,CAAC,CAAC,CAACiB,GAAG,GAAG,SAAS;QACzBpB,OAAO,gCAAOA,OAAO,IAAET,IAAG,EAAC;MAC7B,CAAC,MAAM;QACL,IAAMA,KAAG,GAAG;UACVJ,KAAK,EAAEU,IAAI,CAACM,CAAC,CAAC,CAACJ,IAAI,CAAC;UACpBb,EAAE,EAAEW,IAAI,CAACM,CAAC,CAAC,CAACjB,EAAE,CAAC;UACfY,GAAG,EAAED,IAAI,CAACM,CAAC,CAAC,CAACL,GAAG,CAAC;UACjBS,QAAQ,EAAE;QACZ,CAAC;QACDP,OAAO,gCAAOA,OAAO,IAAET,KAAG,EAAC;MAC7B;IACF;EACF;EACA,IAAIX,KAAK,GAAG,EAAE;EACd,IAAIiB,IAAI,CAAClB,MAAM,GAAG,CAAC,EAAE;IACnBC,KAAK,GAAG8C,aAAa,CAAC1B,OAAO,EAAEwB,MAAM,EAAEtC,EAAE,EAAEY,GAAG,EAAEC,IAAI,CAAC;EACvD;EACA,OAAOnB,KAAK;AACd,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS8C,aAAa,CAAClB,MAAM,EAAEX,IAAI,EAAEX,EAAE,EAAEY,GAAG,EAAEC,IAAI,EAAE;EAClD,IAAIF,IAAI,KAAKY,SAAS,EAAE;IACtB,OAAO,EAAE;EACX;EACA,IAAID,MAAM,CAAC7B,MAAM,KAAK,CAAC,EAAE;IACvB;EACF;EACA,IAAIkB,IAAI,CAAClB,MAAM,KAAK,CAAC,EAAE;IACrB,KAAK,IAAIwB,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGK,MAAM,CAAC7B,MAAM,EAAEwB,CAAC,EAAE,EAAE;MACtC,KAAK,IAAIO,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGb,IAAI,CAAClB,MAAM,EAAE+B,CAAC,EAAE,EAAE;QACpC,IAAIF,MAAM,CAACL,CAAC,CAAC,CAACjB,EAAE,KAAKW,IAAI,CAACa,CAAC,CAAC,CAACZ,GAAG,CAAC,EAAE;UACjC,IAAMP,GAAG,GAAG;YACVJ,KAAK,EAAEU,IAAI,CAACa,CAAC,CAAC,CAACX,IAAI,CAAC;YACpBb,EAAE,EAAEW,IAAI,CAACa,CAAC,CAAC,CAACxB,EAAE,CAAC;YACfY,GAAG,EAAED,IAAI,CAACM,CAAC,CAAC,CAACL,GAAG,CAAC;YACjBS,QAAQ,EAAE;UACZ,CAAC;UACDC,MAAM,CAACL,CAAC,CAAC,CAACI,QAAQ,CAACI,IAAI,CAACpB,GAAG,CAAC;QAC9B;MACF;MACAmC,aAAa,CAAClB,MAAM,CAACL,CAAC,CAAC,CAACI,QAAQ,EAAEV,IAAI,EAAEX,EAAE,EAAEY,GAAG,EAAEC,IAAI,CAAC;IACxD;EACF;EACA,OAAOS,MAAM;AACf;;AAEA;AACA;AACA;AACA;AACA,OAAO,SAASmB,aAAa,CAACC,QAAQ,EAAEd,SAAS,EAAE;EACjD;EACA,IAAMjB,IAAI,GAAGiB,SAAS;EACtB,IAAIe,QAAQ,GAAG,EAAE;EAAA,4CACChC,IAAI;IAAA;EAAA;IAAtB,uDAAwB;MAAA,IAAbb,GAAG;MACZ,IAAIA,GAAG,CAACE,EAAE,KAAK0C,QAAQ,EAAE;QACvBC,QAAQ,GAAG,CAAC7C,GAAG,CAAC;QAChB;MACF,CAAC,MAAM;QACL,IAAIA,GAAG,CAACuB,QAAQ,EAAE;UAChBsB,QAAQ,GAAGC,iBAAiB,CAAC9C,GAAG,CAACuB,QAAQ,EAAEqB,QAAQ,CAAC;QACtD;MACF;IACF;EAAC;IAAA;EAAA;IAAA;EAAA;EACD,OAAOC,QAAQ;AACjB;AACA;AACA;AACA;AACA,SAASC,iBAAiB,CAACjC,IAAI,EAAE+B,QAAQ,EAAE;EACzC,IAAI/B,IAAI,CAAClB,MAAM,KAAK,CAAC,EAAE;IACrB,OAAO,EAAE;EACX;EACA,IAAIkD,QAAQ,GAAG,EAAE;EAAA,4CACChC,IAAI;IAAA;EAAA;IAAtB,uDAAwB;MAAA,IAAbb,GAAG;MACZ,IAAIA,GAAG,CAACE,EAAE,KAAK0C,QAAQ,EAAE;QACvBC,QAAQ,GAAG,CAAC7C,GAAG,CAAC;QAChB;MACF,CAAC,MAAM;QACL,IAAIA,GAAG,CAACuB,QAAQ,EAAE;UAChBuB,iBAAiB,CAAC9C,GAAG,CAACuB,QAAQ,EAAEqB,QAAQ,CAAC;QAC3C;MACF;IACF;EAAC;IAAA;EAAA;IAAA;EAAA;EACD,OAAOC,QAAQ;AACjB;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASE,YAAY,CAAClC,IAAI,EAAET,KAAK,EAAE;EAAA,4CACtBS,IAAI;IAAA;EAAA;IAAtB,uDAAwB;MAAA,IAAbb,GAAG;MACZ,IAAIA,GAAG,CAACI,KAAK,KAAKA,KAAK,EAAE;QACvB,OAAOJ,GAAG;MACZ;IACF;EAAC;IAAA;EAAA;IAAA;EAAA;AACH;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASgD,YAAY,CAACnC,IAAI,EAAEV,KAAK,EAAE;EAAA,4CACtBU,IAAI;IAAA;EAAA;IAAtB,uDAAwB;MAAA,IAAbb,GAAG;MACZ,IAAIA,GAAG,CAACG,KAAK,KAAKA,KAAK,EAAE;QACvB,OAAOH,GAAG;MACZ;IACF;EAAC;IAAA;EAAA;IAAA;EAAA;AACH;AACA;AACA;AACA;AACA,OAAO,IAAMiD,SAAS,GAAG,CACvB;EAAE7C,KAAK,EAAE,GAAG;EAAED,KAAK,EAAE;AAAS,CAAC,EAC/B;EAAEC,KAAK,EAAE,GAAG;EAAED,KAAK,EAAE;AAAY,CAAC,EAClC;EAAEC,KAAK,EAAE,GAAG;EAAED,KAAK,EAAE;AAAS,CAAC,CAChC;AACD;AACA;AACA;AACA,OAAO,IAAM+C,QAAQ,GAAG,CACtB;EAAE9C,KAAK,EAAE,UAAU;EAAED,KAAK,EAAE,IAAI;EAAEf,IAAI,EAAE,SAAS;EAAE+D,MAAM,EAAE;AAAc,CAAC,EAC1E;EAAE/C,KAAK,EAAE,QAAQ;EAAED,KAAK,EAAE,IAAI;EAAEf,IAAI,EAAE,SAAS;EAAE+D,MAAM,EAAE;AAAY,CAAC,EACtE;EACE/C,KAAK,EAAE,aAAa;EACpBD,KAAK,EAAE,IAAI;EACXf,IAAI,EAAE,SAAS;EACf+D,MAAM,EAAE;AACV,CAAC,EACD;EACE/C,KAAK,EAAE,WAAW;EAClBD,KAAK,EAAE,IAAI;EACXf,IAAI,EAAE,SAAS;EACf+D,MAAM,EAAE;AACV,CAAC,EACD;EACE/C,KAAK,EAAE,eAAe;EACtBD,KAAK,EAAE,QAAQ;EACff,IAAI,EAAE,SAAS;EACf+D,MAAM,EAAE;AACV,CAAC,EACD;EACE/C,KAAK,EAAE,YAAY;EACnBD,KAAK,EAAE,IAAI;EACXf,IAAI,EAAE,SAAS;EACf+D,MAAM,EAAE;AACV,CAAC,EACD;EACE/C,KAAK,EAAE,UAAU;EACjBD,KAAK,EAAE,IAAI;EACXf,IAAI,EAAE,QAAQ;EACd+D,MAAM,EAAE;AACV,CAAC,EACD;EACE/C,KAAK,EAAE,eAAe;EACtBD,KAAK,EAAE,IAAI;EACXf,IAAI,EAAE,SAAS;EACf+D,MAAM,EAAE;AACV,CAAC,EACD;EAAE/C,KAAK,EAAE,WAAW;EAAED,KAAK,EAAE,IAAI;EAAEf,IAAI,EAAE,QAAQ;EAAE+D,MAAM,EAAE;AAAe,CAAC,EAC3E;EACE/C,KAAK,EAAE,aAAa;EACpBD,KAAK,EAAE,MAAM;EACbf,IAAI,EAAE,QAAQ;EACd+D,MAAM,EAAE;AACV,CAAC,EACD;EACE/C,KAAK,EAAE,UAAU;EACjBD,KAAK,EAAE,MAAM;EACbf,IAAI,EAAE,SAAS;EACf+D,MAAM,EAAE;AACV,CAAC,EACD;EACE/C,KAAK,EAAE,aAAa;EACpBD,KAAK,EAAE,MAAM;EACbf,IAAI,EAAE,SAAS;EACf+D,MAAM,EAAE;AACV,CAAC,EACD;EACE/C,KAAK,EAAE,kBAAkB;EACzBD,KAAK,EAAE,OAAO;EACdf,IAAI,EAAE,QAAQ;EACd+D,MAAM,EAAE;AACV,CAAC,EAED;EACE/C,KAAK,EAAE,aAAa;EACpBD,KAAK,EAAE,QAAQ;EACff,IAAI,EAAE,SAAS;EACf+D,MAAM,EAAE;AACV,CAAC,EACD;EACE/C,KAAK,EAAE,gBAAgB;EACvBD,KAAK,EAAE,QAAQ;EACff,IAAI,EAAE,SAAS;EACf+D,MAAM,EAAE;AACV,CAAC,EAED;EACE/C,KAAK,EAAE,cAAc;EACrBD,KAAK,EAAE,MAAM;EACbf,IAAI,EAAE,SAAS;EACf+D,MAAM,EAAE;AACV,CAAC,EAED;EAAE/C,KAAK,EAAE,SAAS;EAAED,KAAK,EAAE,IAAI;EAAEf,IAAI,EAAE,SAAS;EAAE+D,MAAM,EAAE;AAAa,CAAC,EACxE;EAAE/C,KAAK,EAAE,WAAW;EAAED,KAAK,EAAE,IAAI;EAAEf,IAAI,EAAE,QAAQ;EAAE+D,MAAM,EAAE;AAAe,CAAC,EAE3E;EAAE/C,KAAK,EAAE,SAAS;EAAED,KAAK,EAAE,KAAK;EAAEf,IAAI,EAAE,SAAS;EAAE+D,MAAM,EAAE;AAAa,CAAC,EACzE;EAAE/C,KAAK,EAAE,SAAS;EAAED,KAAK,EAAE,KAAK;EAAEf,IAAI,EAAE,SAAS;EAAE+D,MAAM,EAAE;AAAa,CAAC,EACzE;EAAE/C,KAAK,EAAE,UAAU;EAAED,KAAK,EAAE,IAAI;EAAEf,IAAI,EAAE,SAAS;EAAE+D,MAAM,EAAE;AAAc,CAAC,EAE1E;EAAE/C,KAAK,EAAE,UAAU;EAAED,KAAK,EAAE,IAAI;EAAEf,IAAI,EAAE,SAAS;EAAE+D,MAAM,EAAE;AAAc,CAAC,EAC1E;EACE/C,KAAK,EAAE,WAAW;EAClBD,KAAK,EAAE,IAAI;EACXf,IAAI,EAAE,SAAS;EACf+D,MAAM,EAAE;AACV,CAAC,EACD;EACE/C,KAAK,EAAE,WAAW;EAClBD,KAAK,EAAE,IAAI;EACXf,IAAI,EAAE,SAAS;EACf+D,MAAM,EAAE;AACV,CAAC,EAED;EACE/C,KAAK,EAAE,eAAe;EACtBD,KAAK,EAAE,MAAM;EACbf,IAAI,EAAE,SAAS;EACf+D,MAAM,EAAE;AACV,CAAC,EACD;EACE/C,KAAK,EAAE,aAAa;EACpBD,KAAK,EAAE,IAAI;EACXf,IAAI,EAAE,SAAS;EACf+D,MAAM,EAAE;AACV,CAAC,EACD;EACE/C,KAAK,EAAE,aAAa;EACpBD,KAAK,EAAE,MAAM;EACbf,IAAI,EAAE,SAAS;EACf+D,MAAM,EAAE;AACV,CAAC,EACD;EACE/C,KAAK,EAAE,aAAa;EACpBD,KAAK,EAAE,QAAQ;EACff,IAAI,EAAE,SAAS;EACf+D,MAAM,EAAE;AACV,CAAC,EACD;EACE/C,KAAK,EAAE,WAAW;EAClBD,KAAK,EAAE,MAAM;EACbf,IAAI,EAAE,SAAS;EACf+D,MAAM,EAAE;AACV,CAAC,EACD;EACE/C,KAAK,EAAE,YAAY;EACnBD,KAAK,EAAE,MAAM;EACbf,IAAI,EAAE,SAAS;EACf+D,MAAM,EAAE;AACV,CAAC,EACD;EAAE/C,KAAK,EAAE,UAAU;EAAED,KAAK,EAAE,IAAI;EAAEf,IAAI,EAAE,SAAS;EAAE+D,MAAM,EAAE;AAAc,CAAC,EAC1E;EACE/C,KAAK,EAAE,gBAAgB;EACvBD,KAAK,EAAE,MAAM;EACbf,IAAI,EAAE,SAAS;EACf+D,MAAM,EAAE;AACV,CAAC,EACD;EACE/C,KAAK,EAAE,sBAAsB;EAC7BD,KAAK,EAAE,YAAY;EACnBf,IAAI,EAAE,SAAS;EACf+D,MAAM,EAAE;AACV,CAAC,EACD;EACE/C,KAAK,EAAE,kBAAkB;EACzBD,KAAK,EAAE,WAAW;EAClBf,IAAI,EAAE,SAAS;EACf+D,MAAM,EAAE;AACV,CAAC,EACD;EACE/C,KAAK,EAAE,YAAY;EACnBD,KAAK,EAAE,IAAI;EACXf,IAAI,EAAE,SAAS;EACf+D,MAAM,EAAE;AACV,CAAC,EACD;EACE/C,KAAK,EAAE,WAAW;EAClBD,KAAK,EAAE,IAAI;EACXf,IAAI,EAAE,SAAS;EACf+D,MAAM,EAAE;AACV,CAAC,EACD;EACE/C,KAAK,EAAE,YAAY;EACnBD,KAAK,EAAE,IAAI;EACXf,IAAI,EAAE,SAAS;EACf+D,MAAM,EAAE;AACV,CAAC,EACD;EACE/C,KAAK,EAAE,YAAY;EACnBD,KAAK,EAAE,IAAI;EACXf,IAAI,EAAE,SAAS;EACf+D,MAAM,EAAE;AACV,CAAC,EACD;EACE/C,KAAK,EAAE,mBAAmB;EAC1BD,KAAK,EAAE,MAAM;EACbf,IAAI,EAAE,SAAS;EACf+D,MAAM,EAAE;AACV,CAAC,EACD;EACE/C,KAAK,EAAE,eAAe;EACtBD,KAAK,EAAE,MAAM;EACbf,IAAI,EAAE,SAAS;EACf+D,MAAM,EAAE;AACV,CAAC,EACD;EACE/C,KAAK,EAAE,cAAc;EACrBD,KAAK,EAAE,MAAM;EACbf,IAAI,EAAE,SAAS;EACf+D,MAAM,EAAE;AACV,CAAC,EACD;EACE/C,KAAK,EAAE,gBAAgB;EACvBD,KAAK,EAAE,MAAM;EACbf,IAAI,EAAE,SAAS;EACf+D,MAAM,EAAE;AACV,CAAC,EACD;EACE/C,KAAK,EAAE,aAAa;EACpBD,KAAK,EAAE,IAAI;EACXf,IAAI,EAAE,SAAS;EACf+D,MAAM,EAAE;AACV,CAAC,EACD;EACE/C,KAAK,EAAE,SAAS;EAChBD,KAAK,EAAE,IAAI;EACXf,IAAI,EAAE,SAAS;EACf+D,MAAM,EAAE;AACV,CAAC,EACD;EACE/C,KAAK,EAAE,WAAW;EAClBD,KAAK,EAAE,IAAI;EACXf,IAAI,EAAE,QAAQ;EACd+D,MAAM,EAAE;AACV,CAAC,EACD;EACE/C,KAAK,EAAE,WAAW;EAClBD,KAAK,EAAE,IAAI;EACXf,IAAI,EAAE,SAAS;EACf+D,MAAM,EAAE;AACV,CAAC,EACD;EACE/C,KAAK,EAAE,WAAW;EAClBD,KAAK,EAAE,IAAI;EACXf,IAAI,EAAE,SAAS;EACf+D,MAAM,EAAE;AACV,CAAC,EACD;EACE/C,KAAK,EAAE,WAAW;EAClBD,KAAK,EAAE,MAAM;EACbf,IAAI,EAAE,SAAS;EACf+D,MAAM,EAAE;AACV,CAAC,EACD;EACE/C,KAAK,EAAE,UAAU;EACjBD,KAAK,EAAE,IAAI;EACXf,IAAI,EAAE,SAAS;EACf+D,MAAM,EAAE;AACV,CAAC,EACD;EACE/C,KAAK,EAAE,aAAa;EACpBD,KAAK,EAAE,KAAK;EACZf,IAAI,EAAE,QAAQ;EACd+D,MAAM,EAAE;AACV,CAAC,EACD;EACE/C,KAAK,EAAE,SAAS;EAChBD,KAAK,EAAE,IAAI;EACXf,IAAI,EAAE,SAAS;EACf+D,MAAM,EAAE;AACV,CAAC,EACD;EACE/C,KAAK,EAAE,aAAa;EACpBD,KAAK,EAAE,MAAM;EACbf,IAAI,EAAE,SAAS;EACf+D,MAAM,EAAE;AACV,CAAC,EACD;EACE/C,KAAK,EAAE,eAAe;EACtBD,KAAK,EAAE,MAAM;EACbf,IAAI,EAAE,SAAS;EACf+D,MAAM,EAAE;AACV,CAAC,EACD;EACE/C,KAAK,EAAE,kBAAkB;EACzBD,KAAK,EAAE,MAAM;EACbf,IAAI,EAAE,QAAQ;EACd+D,MAAM,EAAE;AACV,CAAC,EACD;EACE/C,KAAK,EAAE,aAAa;EACpBD,KAAK,EAAE,IAAI;EACXf,IAAI,EAAE,SAAS;EACf+D,MAAM,EAAE;AACV,CAAC,EACD;EACE/C,KAAK,EAAE,SAAS;EAChBD,KAAK,EAAE,IAAI;EACXf,IAAI,EAAE,SAAS;EACf+D,MAAM,EAAE;AACV,CAAC,EACD;EACE/C,KAAK,EAAE,UAAU;EACjBD,KAAK,EAAE,IAAI;EACXf,IAAI,EAAE,SAAS;EACf+D,MAAM,EAAE;AACV,CAAC,EACD;EACE/C,KAAK,EAAE,YAAY;EACnBD,KAAK,EAAE,IAAI;EACXf,IAAI,EAAE,SAAS;EACf+D,MAAM,EAAE;AACV,CAAC,EACD;EACE/C,KAAK,EAAE,aAAa;EACpBD,KAAK,EAAE,MAAM;EACbf,IAAI,EAAE,SAAS;EACf+D,MAAM,EAAE;AACV,CAAC,EACD;EACE/C,KAAK,EAAE,SAAS;EAChBD,KAAK,EAAE,IAAI;EACXf,IAAI,EAAE,SAAS;EACf+D,MAAM,EAAE;AACV,CAAC,EACD;EACE/C,KAAK,EAAE,iBAAiB;EACxBD,KAAK,EAAE,QAAQ;EACff,IAAI,EAAE,SAAS;EACf+D,MAAM,EAAE;AACV,CAAC,EACD;EACE/C,KAAK,EAAE,mBAAmB;EAC1BD,KAAK,EAAE,QAAQ;EACff,IAAI,EAAE,SAAS;EACf+D,MAAM,EAAE;AACV,CAAC,EACD;EACE/C,KAAK,EAAE,iBAAiB;EACxBD,KAAK,EAAE,QAAQ;EACff,IAAI,EAAE,SAAS;EACf+D,MAAM,EAAE;AACV,CAAC,EACD;EACE/C,KAAK,EAAE,WAAW;EAClBD,KAAK,EAAE,IAAI;EACXf,IAAI,EAAE,SAAS;EACf+D,MAAM,EAAE;AACV,CAAC,EACD;EACE/C,KAAK,EAAE,iBAAiB;EACxBD,KAAK,EAAE,MAAM;EACbf,IAAI,EAAE,SAAS;EACf+D,MAAM,EAAE;AACV,CAAC,EACD;EACE/C,KAAK,EAAE,WAAW;EAClBD,KAAK,EAAE,QAAQ;EACff,IAAI,EAAE,SAAS;EACf+D,MAAM,EAAE;AACV,CAAC,EACD;EACE/C,KAAK,EAAE,aAAa;EACpBD,KAAK,EAAE,QAAQ;EACff,IAAI,EAAE,SAAS;EACf+D,MAAM,EAAE;AACV,CAAC,EACD;EACE/C,KAAK,EAAE,WAAW;EAClBD,KAAK,EAAE,IAAI;EACXf,IAAI,EAAE,SAAS;EACf+D,MAAM,EAAE;AACV,CAAC,EACD;EACE/C,KAAK,EAAE,aAAa;EACpBD,KAAK,EAAE,IAAI;EACXf,IAAI,EAAE,SAAS;EACf+D,MAAM,EAAE;AACV,CAAC,EACD;EACE/C,KAAK,EAAE,WAAW;EAClBD,KAAK,EAAE,IAAI;EACXf,IAAI,EAAE,SAAS;EACf+D,MAAM,EAAE;AACV,CAAC,EACD;EACE/C,KAAK,EAAE,QAAQ;EACfD,KAAK,EAAE,IAAI;EACXf,IAAI,EAAE,SAAS;EACf+D,MAAM,EAAE;AACV,CAAC,EACD;EACE/C,KAAK,EAAE,kBAAkB;EACzBD,KAAK,EAAE,MAAM;EACbf,IAAI,EAAE,SAAS;EACf+D,MAAM,EAAE;AACV,CAAC,EACD;EACE/C,KAAK,EAAE,YAAY;EACnBD,KAAK,EAAE,MAAM;EACbf,IAAI,EAAE,SAAS;EACf+D,MAAM,EAAE;AACV,CAAC,EACD;EACE/C,KAAK,EAAE,aAAa;EACpBD,KAAK,EAAE,MAAM;EACbf,IAAI,EAAE,SAAS;EACf+D,MAAM,EAAE;AACV,CAAC,EACD;EACE/C,KAAK,EAAE,aAAa;EACpBD,KAAK,EAAE,MAAM;EACbf,IAAI,EAAE,SAAS;EACf+D,MAAM,EAAE;AACV,CAAC,EACD;EACE/C,KAAK,EAAE,cAAc;EACrBD,KAAK,EAAE,MAAM;EACbf,IAAI,EAAE,SAAS;EACf+D,MAAM,EAAE;AACV,CAAC,EACD;EACE/C,KAAK,EAAE,kBAAkB;EACzBD,KAAK,EAAE,OAAO;EACdf,IAAI,EAAE,SAAS;EACf+D,MAAM,EAAE;AACV,CAAC,EACD;EACE/C,KAAK,EAAE,mBAAmB;EAC1BD,KAAK,EAAE,QAAQ;EACff,IAAI,EAAE,SAAS;EACf+D,MAAM,EAAE;AACV,CAAC,EACD;EACE/C,KAAK,EAAE,mBAAmB;EAC1BD,KAAK,EAAE,QAAQ;EACff,IAAI,EAAE,SAAS;EACf+D,MAAM,EAAE;AACV,CAAC,EACD;EACE/C,KAAK,EAAE,gBAAgB;EACvBD,KAAK,EAAE,MAAM;EACbf,IAAI,EAAE,SAAS;EACf+D,MAAM,EAAE;AACV,CAAC,EACD;EACE/C,KAAK,EAAE,gBAAgB;EACvBD,KAAK,EAAE,MAAM;EACbf,IAAI,EAAE,SAAS;EACf+D,MAAM,EAAE;AACV,CAAC,EACD;EACE/C,KAAK,EAAE,UAAU;EACjBD,KAAK,EAAE,MAAM;EACbf,IAAI,EAAE,SAAS;EACf+D,MAAM,EAAE;AACV,CAAC,EACD;EACE/C,KAAK,EAAE,iBAAiB;EACxBD,KAAK,EAAE,MAAM;EACbf,IAAI,EAAE,SAAS;EACf+D,MAAM,EAAE;AACV,CAAC,EACD;EACE/C,KAAK,EAAE,YAAY;EACnBD,KAAK,EAAE,KAAK;EACZf,IAAI,EAAE,SAAS;EACf+D,MAAM,EAAE;AACV,CAAC,EACD;EACE/C,KAAK,EAAE,oBAAoB;EAC3BD,KAAK,EAAE,SAAS;EAChBf,IAAI,EAAE,SAAS;EACf+D,MAAM,EAAE;AACV,CAAC,EACD;EACE/C,KAAK,EAAE,aAAa;EACpBD,KAAK,EAAE,IAAI;EACXf,IAAI,EAAE,SAAS;EACf+D,MAAM,EAAE;AACV,CAAC,EACD;EACE/C,KAAK,EAAE,eAAe;EACtBD,KAAK,EAAE,QAAQ;EACff,IAAI,EAAE,SAAS;EACf+D,MAAM,EAAE;AACV,CAAC,EACD;EACE/C,KAAK,EAAE,cAAc;EACrBD,KAAK,EAAE,QAAQ;EACff,IAAI,EAAE,SAAS;EACf+D,MAAM,EAAE;AACV,CAAC,EACD;EACE/C,KAAK,EAAE,iBAAiB;EACxBD,KAAK,EAAE,QAAQ;EACff,IAAI,EAAE,SAAS;EACf+D,MAAM,EAAE;AACV,CAAC,EACD;EACE/C,KAAK,EAAE,oBAAoB;EAC3BD,KAAK,EAAE,MAAM;EACbf,IAAI,EAAE,SAAS;EACf+D,MAAM,EAAE;AACV,CAAC,EACD;EACE/C,KAAK,EAAE,WAAW;EAClBD,KAAK,EAAE,IAAI;EACXf,IAAI,EAAE,SAAS;EACf+D,MAAM,EAAE;AACV,CAAC,EACD;EACE/C,KAAK,EAAE,WAAW;EAClBD,KAAK,EAAE,IAAI;EACXf,IAAI,EAAE,SAAS;EACf+D,MAAM,EAAE;AACV,CAAC,CACF;AACD;;AAEA;AACA;AACA;AACA;AACA,OAAO,SAASC,eAAe,CAACpD,GAAG,EAAE;EACnC,IAAQqD,YAAY,GAAKzE,MAAM,CAAvByE,YAAY;EACpB,IAAIzD,KAAK,GAAG,EAAE;EACd,IAAI,OAAOI,GAAG,KAAK,QAAQ,IAAIA,GAAG,CAACsD,WAAW,KAAKC,MAAM,IAAIvD,GAAG,KAAK,EAAE,EAAE;IACvE,IAAMwD,GAAG,GAAG;MACVC,aAAa,EAAE,EAAE;MACjBzD,GAAG,EAAEA;IACP,CAAC;IACD,OAAO,IAAI0D,OAAO,CAAC,UAACC,OAAO,EAAEC,MAAM,EAAK;MACtCP,YAAY,CAACG,GAAG,CAAC,CACdK,IAAI,CAAC,UAACC,QAAQ,EAAK;QAClB,IAAQC,IAAI,GAAKD,QAAQ,CAACE,MAAM,CAAxBD,IAAI;QACZ,IAAIA,IAAI,EAAE;UACRA,IAAI,CAACE,OAAO,CAAC,UAAC/B,IAAI,EAAK;YACrBtC,KAAK,gCACAA,KAAK,IACR;cACEO,KAAK,EAAE+B,IAAI,CAAC9B,KAAK;cACjBA,KAAK,EAAE8B,IAAI,CAAClC;YACd,CAAC,EACF;UACH,CAAC,CAAC;QACJ;QACA2D,OAAO,CAAC/D,KAAK,CAAC;MAChB,CAAC,CAAC,CACDsE,KAAK,CAAC,UAACC,GAAG,EAAK;QACdzF,cAAc,CAACyF,GAAG,EAAE,QAAQ,CAAC;MAC/B,CAAC,CAAC;IACN,CAAC,CAAC;EACJ,CAAC,MAAM;IACLC,KAAK,CAAC,WAAW,CAAC;EACpB;AACF;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASC,wBAAwB,CAACtF,OAAO,EAAEC,QAAQ,EAAEC,UAAU,EAAE;EACtE,IAAIC,QAAQ,GAAG,EAAE;EACjB,IAAIC,eAAe,GAAG,OAAO;EAC7B,IAAIC,IAAI,GAAG,KAAK;EAChB,IAAIJ,QAAQ,EAAE;IACZG,eAAe,GAAGH,QAAQ;EAC5B;EACA,IAAIC,UAAU,EAAE;IACdG,IAAI,GAAGH,UAAU;EACnB;EACA,IAAIG,IAAI,EAAE;IACRF,QAAQ,GAAGX,KAAK,CAACc,KAAK,CAACC,MAAM,CAACC,aAAa,CAACJ,eAAe,CAAC,CAACJ,OAAO,CAAC;EACvE,CAAC,MAAM;IACLG,QAAQ,GACNX,KAAK,CAACc,KAAK,CAACC,MAAM,CAACC,aAAa,CAACJ,eAAe,CAAC,CAACK,aAAa,CAACT,OAAO,CAAC,CAAC;EAC7E;EACA,IAAIP,WAAW,CAACU,QAAQ,CAAC,EAAE;IACzB,OAAO,EAAE;EACX;EAEA,IAAIO,MAAM,CAACC,IAAI,CAACR,QAAQ,CAAC,CAACS,MAAM,KAAK,CAAC,EAAE;IACtC;IACA,OAAO,EAAE;EACX;EACA,IAAIC,KAAK,GAAG,EAAE;EACd,IAAMC,QAAQ,GAAGX,QAAQ,CAACY,KAAK,CAAC,GAAG,CAAC,CAACC,IAAI,EAAE;EAAA,4CACzBF,QAAQ;IAAA;EAAA;IAA1B,uDAA4B;MAAA,IAAjBG,GAAG;MACZ,IAAMC,GAAG,GAAGD,GAAG,CAACF,KAAK,CAAC,KAAK,CAAC;MAC5BF,KAAK,gCAAOA,KAAK,IAAE;QAAEM,EAAE,EAAED,GAAG,CAAC,CAAC,CAAC;QAAEE,KAAK,EAAEF,GAAG,CAAC,CAAC,CAAC;QAAEG,KAAK,EAAEH,GAAG,CAAC,CAAC;MAAE,CAAC,EAAC;IAClE;EAAC;IAAA;EAAA;IAAA;EAAA;EACD,OAAOL,KAAK;AACd"}]}