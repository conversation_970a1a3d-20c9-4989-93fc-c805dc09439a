/*
 * @DataStatisticsDao.java    2012-9-19 下午11:09:12
 *
 * Copyright (c) 2011 Sunyard System Engineering Co., Ltd.
 * All rights reserved.
 *
 * This software is the confidential and proprietary information of
 * Sunyard System Engineering Co., Ltd. ("Confidential Information").
 * You shall not disclose such Confidential Information and shall use it
 * only in accordance with the terms of the license agreement you entered
 * into with Sunyard.
 */

 package com.sunyard.dao.arms;

 import static org.hamcrest.CoreMatchers.nullValue;
 
 import java.util.ArrayList;
 import java.util.List;
 
 import org.hibernate.Hibernate;
 import org.hibernate.Query;
 import org.hibernate.SQLQuery;
 import org.hibernate.Session;
 import org.slf4j.Logger;
 import org.slf4j.LoggerFactory;
 import org.springframework.stereotype.Repository;
 
 
 
 
 import com.sunyard.base.orm.hibernate.HibernateDao;
 import com.sunyard.common.constants.Constants;
 import com.sunyard.common.model.DataStatistics;
 import com.sunyard.common.model.DataTransmission;
 import com.sunyard.common.model.ModelInfo;
 import com.sunyard.common.model.OrganDataStatistics;
 import com.sunyard.common.util.StringUtil;
 
 /**
  * @ClassName: DataStatisticsDao
  * @Description: TODO 预警数据统计持久化处理
  * @Author：陈慧民
  * @Date： 2012-9-19 下午11:09:12 (创建文件的精确时间)
  */
 // Spring DAO Bean的标识
 @Repository
 public class ArmsDataStatisticsDao extends HibernateDao<Object, Integer> {
 
	 protected final Logger logger = LoggerFactory.getLogger(getClass());
 
 
	 @SuppressWarnings("unchecked")
	 public List<Object[]> bankDataStatisticsDao(String parentOrgan,
												 String userNo, ModelInfo modelInfo, String condition) throws Exception {
		 String sql = bankDataStatisticsDaoSQL(parentOrgan, userNo, modelInfo, condition);
		 logger.info("统计分行模型数据处理表：" + sql);
		 Session session = this.getSessionFactory().getCurrentSession();
		 SQLQuery query = session.createSQLQuery(sql);
 
		 query.addScalar("ORGAN_NO", Hibernate.STRING);
		 query.addScalar("ORGAN_NAME", Hibernate.STRING);
		 query.addScalar("3", Hibernate.INTEGER);
		 query.addScalar("4", Hibernate.INTEGER);
		 query.addScalar("5", Hibernate.INTEGER);
		 query.addScalar("6", Hibernate.INTEGER);
 
		 return query.list();
	 }
 
	 /**
	  * @Title: bankDataStatisticsDaoSQL
	  * @Description: 获取分行统计语句SQL,东莞农商不用到
	  * @return String
	  * <AUTHOR>
	  */
	 private String bankDataStatisticsDaoSQL(String parentOrgan, String userNo, ModelInfo modelInfo, String condition) {
		 StringBuffer sql = new StringBuffer("");
		 sql.append("SELECT I.PARENT_ORGAN_2 AS ORGAN_NO,J.ORGAN_NAME AS ORGAN_NAME,SUM(I.NOT_DEALED_COUNT),SUM(I.HAVE_DEALED_COUNT),SUM(I.PASS_COUNT),SUM(I.SLIP_COUNT) ")
				 .append("FROM (SELECT A.MODEL_ID,A.MODEL_SIMPLE_NAME,A.SITE_NO,A.OCCUR_DATE,A.NOT_DEALED_COUNT,A.HAVE_DEALED_COUNT,A.PASS_COUNT,A.SLIP_COUNT,")
				 .append("CASE WHEN B.ORGAN_LEVEL='")
				 .append(Constants.CREUNIT_LEVEL)
				 .append("' THEN B.ORGAN_NO ELSE B.PARENT_ORGAN END AS PARENT_ORGAN_1,")
				 .append("CASE WHEN C.ORGAN_LEVEL='")
				 .append(Constants.BANK_LEVEL)
				 .append("' THEN C.ORGAN_NO ELSE C.PARENT_ORGAN END AS PARENT_ORGAN_2,")
				 .append("CASE WHEN D.ORGAN_LEVEL='")
				 .append(Constants.BRANCH_BANK_LEVEL)
				 .append("' THEN D.ORGAN_NO ELSE D.PARENT_ORGAN END AS PARENT_ORGAN_3,")
				 .append("CASE WHEN E.ORGAN_LEVEL='")
				 .append(Constants.CENTER_BANK_LEVEL)
				 .append("' THEN E.ORGAN_NO ELSE E.PARENT_ORGAN END AS PARENT_ORGAN_4 ")
				 .append("FROM ARMS_STATISTIC_TB A ")
				 .append("JOIN SM_ORGAN_TB B ON A.SITE_NO=B.ORGAN_NO ")
				 .append("JOIN SM_ORGAN_TB C ON B.PARENT_ORGAN=C.ORGAN_NO  ")
				 .append("JOIN SM_ORGAN_TB D ON C.PARENT_ORGAN=D.ORGAN_NO ")
				 .append("JOIN SM_ORGAN_TB E ON D.PARENT_ORGAN=E.ORGAN_NO ")
				 .append("JOIN SM_USER_ORGAN_TB F ON A.SITE_NO=F.ORGAN_NO WHERE F.USER_NO='")
				 .append(userNo)
				 .append("') I JOIN SM_ORGAN_TB J ON I.PARENT_ORGAN_2=J.ORGAN_NO ")
				 .append("WHERE 1=1 ")
				 .append(condition.replaceAll("and ", "and I."))
				 .append(" AND I.PARENT_ORGAN_4='").append(parentOrgan)
				 .append("' GROUP BY I.PARENT_ORGAN_2,J.ORGAN_NAME");
		 return sql.toString();
	 }
 
 
	 /**
	  *
	  * @Title: creunitDataStatisticsDaoSQL
	  * @Description: 获取支行统计语句SQL,东莞农商直接到支行
	  * @param parentOrgan
	  * @param userNo
	  * @param modelInfo
	  * @param condition
	  * @return
	  */
	 private String creunitDataStatisticsDaoSQL(String parentOrgan, String userNo, ModelInfo modelInfo, String condition) {
		 StringBuffer sql = new StringBuffer("");
		 sql.append("SELECT I.PARENT_ORGAN_1 AS ORGAN_NO,J.ORGAN_NAME AS ORGAN_NAME,SUM(I.NOT_DEALED_COUNT) S3,SUM(I.HAVE_DEALED_COUNT) S4,SUM(I.PASS_COUNT) S5,SUM(I.SLIP_COUNT)S6 ")
				 .append("FROM (SELECT A.MODEL_ID,A.MODEL_SIMPLE_NAME,A.SITE_NO,A.OCCUR_DATE,A.NOT_DEALED_COUNT,A.HAVE_DEALED_COUNT,A.PASS_COUNT,A.SLIP_COUNT,")
				 .append("CASE WHEN B.ORGAN_LEVEL='")
				 .append(Constants.CREUNIT_LEVEL)
				 .append("' THEN B.ORGAN_NO ELSE B.PARENT_ORGAN END AS PARENT_ORGAN_1,")
				 .append("CASE WHEN C.ORGAN_LEVEL='")
				 .append(Constants.BANK_LEVEL)
				 .append("' THEN C.ORGAN_NO ELSE C.PARENT_ORGAN END AS PARENT_ORGAN_2,")
				 .append("CASE WHEN D.ORGAN_LEVEL='")
				 .append(Constants.BRANCH_BANK_LEVEL)
				 .append("' THEN D.ORGAN_NO ELSE D.PARENT_ORGAN END AS PARENT_ORGAN_3,")
				 .append("CASE WHEN E.ORGAN_LEVEL='")
				 .append(Constants.CENTER_BANK_LEVEL)
				 .append("' THEN E.ORGAN_NO ELSE E.PARENT_ORGAN END AS PARENT_ORGAN_4 ")
				 .append("FROM ARMS_STATISTIC_TB A ")
				 .append("JOIN SM_ORGAN_TB B ON A.SITE_NO=B.ORGAN_NO ")
				 .append("JOIN SM_ORGAN_TB C ON B.PARENT_ORGAN=C.ORGAN_NO  ")
				 .append("JOIN SM_ORGAN_TB D ON C.PARENT_ORGAN=D.ORGAN_NO ")
				 .append("JOIN SM_ORGAN_TB E ON D.PARENT_ORGAN=E.ORGAN_NO ")
				 .append("JOIN SM_USER_ORGAN_TB F ON A.SITE_NO=F.ORGAN_NO WHERE F.USER_NO='")
				 .append(userNo)
				 .append("') I JOIN SM_ORGAN_TB J ON I.PARENT_ORGAN_1=J.ORGAN_NO ")
				 .append(" where 1=1 ")
				 .append(condition.replaceAll("and ", "and I."))
				 .append(" AND I.PARENT_ORGAN_4='").append(parentOrgan)
				 .append("' GROUP BY I.PARENT_ORGAN_1,J.ORGAN_NAME");
		 return sql.toString();
	 }
	 /**
	  *
	  * @Title: siteDataStatisticsDaoSQL
	  * @Description: 获取网点统计语句SQL,东莞农商使用机构权限查看方式
	  * @param parentOrgan
	  * @param userNo
	  * @param modelInfo
	  * @param condition
	  * @return
	  */
	 private String siteDataStatisticsDaoSQL(String parentOrgan, String userNo, ModelInfo modelInfo, String condition) {
		 StringBuffer sql = new StringBuffer("");
		 sql.append("SELECT I.SITE_NO AS ORGAN_NO,I.ORGAN_NAME,SUM(I.NOT_DEALED_COUNT),SUM(I.HAVE_DEALED_COUNT),SUM(I.PASS_COUNT),SUM(I.SLIP_COUNT) ")
				 .append("FROM (SELECT A.MODEL_ID,A.MODEL_SIMPLE_NAME,A.SITE_NO,A.OCCUR_DATE,A.NOT_DEALED_COUNT,A.HAVE_DEALED_COUNT,A.PASS_COUNT,A.SLIP_COUNT,B.ORGAN_NAME,")
				 .append("CASE WHEN B.ORGAN_LEVEL='")
				 .append(Constants.CREUNIT_LEVEL)
				 .append("' THEN B.ORGAN_NO ELSE B.PARENT_ORGAN END AS PARENT_ORGAN_1 ")
				 .append("FROM ARMS_STATISTIC_TB A ")
				 .append("JOIN SM_ORGAN_TB B ON A.SITE_NO=B.ORGAN_NO ")
				 .append("JOIN SM_ORGAN_TB C ON B.PARENT_ORGAN=C.ORGAN_NO ")
				 .append("JOIN SM_USER_ORGAN_TB F ON A.SITE_NO=F.ORGAN_NO WHERE F.USER_NO='")
				 .append(userNo)
				 .append("') I  ")
				 .append("WHERE  1=1 ")
				 .append(condition.replaceAll("and ", "and I."))
				 .append(" AND (I.SITE_NO='")
				 .append(parentOrgan)
				 .append("' OR I.PARENT_ORGAN_1='")
				 .append(parentOrgan)
				 .append("') GROUP BY I.SITE_NO,I.ORGAN_NAME");
		 return sql.toString();
	 }
 
	 /**
	  * 查询有多少退回的任务
	  *
	  */
	 public String BackSql(String conString,String userNo){
		 StringBuffer sb=new StringBuffer();
		 conString=conString.replaceAll("OCCUR_DATE", "business_date");
		 sb.append("select count(*),model_Id from mc_model_zjd where 1=1  ").append(conString).append(" and state = '3' and user_no_s='"+userNo+"' GROUP BY Model_Id ");
 
		 return sb.toString();
	 }
	 /**
	  *
	  * @param modelInfos
	  * @param condition
	  * @param userNo
	  * @return
	  * @throws Exception
	  * <AUTHOR>
	  */
	 public List dataStatisticDao2(List<ModelInfo> modelInfos,
								   String condition, String userNo) throws Exception {
		 List<DataStatistics> dataStatisticsList = new ArrayList<DataStatistics>();
		 DataStatistics dataStatistics = null;
		 ModelInfo modelInfo = null;
		 Object[] object = null;
		 Object[] obj=null;
		 long canDeal = 0l;
		 long cannotDeal = 0l;
		 long dealed = 0l;
		 long notDealed = 0l;
		 long passCount = 0l;
		 long slipCount = 0l;
		 long backCount=0l;
		 String sql = getDataStatisticsSQL2(condition, userNo);
		 logger.info("统计模型数据处理表：" + sql);
		 Query query = createSQLQuery(sql);
		 List<Object[]> objects = query.list();
		 String sqls=BackSql(condition,userNo);
		 query=createSQLQuery(sqls);
		 List<Object[]> ob=query.list();
		 if(objects !=null && objects.size() > 0){
			 for(int i=0;i<modelInfos.size();i++){
				 modelInfo = modelInfos.get(i);
 
				 for(int j=0;j<objects.size();j++){
					 object = objects.get(j);
 
					 if(null != object[4] && Integer.parseInt(object[4].toString()) == modelInfo.getModelId()){
 
						 notDealed = Long.parseLong(object[0].toString());
						 dealed = Long.parseLong(object[1].toString());
						 passCount = Long.parseLong(object[2].toString());
						 slipCount = Long.parseLong(object[3].toString());
						 dataStatistics = new DataStatistics();
						 dataStatistics.setModelInfo(modelInfo);
						 dataStatistics.setDealed(dealed);
						 dataStatistics.setNotDealed(notDealed);
						 dataStatistics.setPassCount(passCount);
						 dataStatistics.setSlipCount(slipCount);
						 dataStatistics.setTotalNumber(notDealed + dealed);
						 for(int k=0;k<ob.size();k++){
							 obj=ob.get(k);
 
							 if(null!=obj[1] && obj[1].toString().equals(object[4].toString())){
								 dataStatistics.setBackCount(Long.parseLong(obj[0].toString()));
								 break;
							 }
							 else{
								 dataStatistics.setBackCount((long) 0);
							 }
						 }
						 dataStatisticsList.add(dataStatistics);
						 break;
					 }
				 }
			 }
		 }
		 return dataStatisticsList;
	 }
 
 
 
 
	 /**
	  * @Title: getDataStatisticsSQL
	  * @Description: 获取统计语句SQL
	  * @return String
	  * <AUTHOR>
	  */
	 private String getDataStatisticsSQL2(String condition, String userNo) {
		 StringBuffer sql = new StringBuffer();
		 sql.append("SELECT sum(a.NOT_DEALED_COUNT),sum(a.HAVE_DEALED_COUNT),sum(a.SUPERVISE_PASS_COUNT),sum(a.SUPERVISE_SLIP_COUNT),")
				 .append("a.MODEL_ID ")
				 .append("FROM SUPERVISE_STATISTIC_TB a, SM_USER_ORGAN_TB b    WHERE  1=1 ")
				 .append(condition.replaceAll("and ", "and a."));
		 sql.append(" and a.site_no = b.organ_no and b.user_no = '")
				 .append(userNo).append("'");
		 sql.append(" and a.user_no = b.user_no");
 
		 sql.append(" GROUP BY a.MODEL_ID");
		 return sql.toString();
	 }
 
	 /**
	  *
	  */
	 @SuppressWarnings("unchecked")
	 public DataStatistics dataStatisticDaoAll(ModelInfo modelInfo,
											   String condition, String... privOrgans) throws Exception {
		 String sql = getDataStatisticsSQLAll(modelInfo, condition, privOrgans);
		 logger.info("统计模型数据处理表：" + sql);
		 DataStatistics dataStatistics = new DataStatistics();
		 long totalNumber=0l;
		 //Object[] object = null;
		 Query query = createSQLQuery(sql.toString());
		 List objects = query.list();
		 for (int i = 0; i < objects.size(); i++) {
			 //System.out.println(objects.get(i));
			 totalNumber += Long.parseLong(objects.get(i).toString());
		 }
		 modelInfo.setTableName(modelInfo.getTableName().replaceAll("_HIS", ""));
		 String sqls=getDataStatisticsSQLAll(modelInfo, condition, privOrgans);
		 query=createSQLQuery(sqls.toString());
		 objects=query.list();
		 for (int i = 0; i < objects.size(); i++) {
 
			 totalNumber += Long.parseLong(objects.get(i).toString());
		 }
		 dataStatistics.setModelInfo(modelInfo);
		 dataStatistics.setTotalNumber(totalNumber);
		 return dataStatistics;
	 }
 
	 /**
	  * @Title: dataStatisticDao
	  * @Description: 统计模型所有数据
	  * @param dataStatistics
	  * @return DataStatistics
	  * @throws Exception
	  */
	 @SuppressWarnings("unchecked")
	 public DataStatistics dataStatisticDao(ModelInfo modelInfo,
											String condition, String... privOrgans) throws Exception {
		 String sql = getDataStatisticsSQL(modelInfo, condition, privOrgans);
		 logger.info("统计模型数据处理表：" + sql);
		 long dealed = 0l;
		 long notDealed = 0l;
		 DataStatistics dataStatistics = new DataStatistics();
		 Object[] object = null;
		 Query query = createSQLQuery(sql.toString());
		 List<Object[]> objects = query.list();
		 for (int i = 0; i < objects.size(); i++) {
			 object = objects.get(i);
			 // 还未处理数据
			 if (Integer.parseInt(object[1].toString()) == Constants.ISHANDLER_UNTREATED) {
				 notDealed += Long.parseLong(object[0].toString());
				 // 已处理数据
			 } else {
				 dealed += Long.parseLong(object[0].toString());
			 }
		 }
		 dataStatistics.setModelInfo(modelInfo);
		 dataStatistics.setDealed(dealed);
		 dataStatistics.setNotDealed(notDealed);
		 dataStatistics.setTotalNumber(notDealed + dealed);
		 return dataStatistics;
	 }
 
	 /**
	  * @Title: organDataStatisticDao
	  * @Description: 按机构号分类统计模型数据
	  * @param modelInfo
	  * @param condition
	  * @param privOrgans
	  * @return OrganDataStatistics
	  * <AUTHOR>
	  */
	 @SuppressWarnings("unchecked")
	 public List<OrganDataStatistics> organDataStatisticDao(ModelInfo modelInfo,
															String condition, String... privOrgans) throws Exception {
		 String sql = getOrganDataStatisticsSQL(modelInfo, condition, privOrgans);
		 logger.info("按机构号分类统计模型数据处理表：" + sql);
		 List<OrganDataStatistics> organDataStatisticsList = new ArrayList<OrganDataStatistics>();
		 OrganDataStatistics organDataStatistics = null;
		 Object[] object = null;
		 Query query = createSQLQuery(sql.toString());
		 List<Object[]> objects = query.list();
		 for (int i = 0; i < objects.size(); i++) {
			 object = objects.get(i);
			 organDataStatistics = new OrganDataStatistics();
			 organDataStatistics.setTotalNumber((Long) object[0]);
			 organDataStatistics.setOrganNo(object[1] + "");
			 organDataStatisticsList.add(organDataStatistics);
		 }
		 return organDataStatisticsList;
	 }
	 /**
	  * @Title: getDataStatisticsSQL
	  * @Description: 获取统计语句SQL
	  * @return String
	  * <AUTHOR>
	  */
	 private String getDataStatisticsSQLAll(ModelInfo modelInfo, String condition,
											String... privOrgans) {
		 StringBuffer sql = new StringBuffer();
		 sql.append("select count(*) from ");
		 sql.append(modelInfo.getTableName() + " ");
		 sql.append("where list_flag=0 ");
 
		 sql.append("and model_id=");
 
		 sql.append(modelInfo.getModelId() + " ");
		 sql.append(condition);
		 if (modelInfo.getModelDataCheckWay() == Constants.MODEL_DATA_CHECK_WAY_PRIVORGAN) {
			 if (privOrgans.length > 0 && !StringUtil.checkNull(privOrgans[0])) {
				 // 权限机构
				 logger.debug("模型权限机构为：" + privOrgans[0]);
				 sql.append("and site_no in(");
				 sql.append(privOrgans[0] + ") ");
			 } else {
				 sql.append("and 1=2 ");
			 }
		 }
 
		 return sql.toString();
	 }
 
	 /**
	  * @Title: getDataStatisticsSQL
	  * @Description: 获取统计语句SQL
	  * @return String
	  * <AUTHOR>
	  */
	 private String getDataStatisticsSQL(ModelInfo modelInfo, String condition,
										 String... privOrgans) {
		 StringBuffer sql = new StringBuffer();
		 sql.append("select count(ishandle),ishandle from ");
		 sql.append(modelInfo.getTableName() + " ");
		 sql.append("where list_flag=0 ");
 
		 sql.append("and model_id=");
 
		 sql.append(modelInfo.getModelId() + " ");
		 sql.append(condition);
		 if (modelInfo.getModelDataCheckWay() == Constants.MODEL_DATA_CHECK_WAY_PRIVORGAN) {
			 if (privOrgans.length > 0 && !StringUtil.checkNull(privOrgans[0])) {
				 // 权限机构
				 logger.debug("模型权限机构为：" + privOrgans[0]);
				 sql.append("and site_no in(");
				 sql.append(privOrgans[0] + ") ");
			 } else {
				 sql.append("and 1=2 ");
			 }
		 }
 
		 sql.append("group by ishandle");
		 return sql.toString();
	 }
 
	 /**
	  * @Title: getOrganDataStatisticsSQL
	  * @Description: 按机构统计模型数据
	  * @param modelInfo
	  * @param condition
	  * @param privOrgans
	  * @return String
	  * <AUTHOR>
	  */
	 private String getOrganDataStatisticsSQL(ModelInfo modelInfo,
											  String condition, String... privOrgans) {
		 StringBuffer sql = new StringBuffer();
		 sql.append("select count(1),site_no from ");
		 sql.append(modelInfo.getTableName() + " ");
		 sql.append("where list_flag=0 ");
		 sql.append("and model_id=");
		 sql.append(modelInfo.getModelId() + " ");
		 sql.append(condition);
		 if (modelInfo.getModelDataCheckWay() == Constants.MODEL_DATA_CHECK_WAY_PRIVORGAN) {
			 if (privOrgans.length > 0 && !StringUtil.checkNull(privOrgans[0])) {
				 // 权限机构
				 logger.debug("模型权限机构为：" + privOrgans[0]);
				 sql.append("and site_no in(");
				 sql.append(privOrgans[0] + ") ");
			 } else {
				 sql.append("and 1=2 ");
			 }
		 }
		 sql.append("group by site_no");
		 return sql.toString();
	 }
 
 
	 /**
	  * 当天数据监督下发差错时，需要维护统计表
	  * @param dataTransmission
	  * @return
	  * @throws Exception
	  * <AUTHOR>
	  */
	 public int updateStatisticTbSlip(DataTransmission dataTransmission)throws Exception {
		 String sql = updateStatisticTbSlipSQL(dataTransmission);
		 logger.info("当天数据监下发差错同步更新模型统计表：" + sql);
		 Session session = this.getSessionFactory().getCurrentSession();
		 return session.createSQLQuery(sql).executeUpdate();
	 }
 
 
	 /**
	  * @Title: updateStatisticTbSlipSQL
	  * @Description: 获取统计语句SQL
	  * @return String
	  * <AUTHOR>
	  */
	 private String updateStatisticTbSlipSQL(DataTransmission dataTransmission) {
		 StringBuffer sql = new StringBuffer();
		 sql.append("UPDATE SUPERVISE_STATISTIC_TB SET NOT_DEALED_COUNT=NOT_DEALED_COUNT-1,")
				 .append(" HAVE_DEALED_COUNT=HAVE_DEALED_COUNT+1,SUPERVISE_SLIP_COUNT=SUPERVISE_SLIP_COUNT+1")
				 .append(" WHERE MODEL_ID=")
				 .append(dataTransmission.getModelInfo().getModelId())
				 .append(" AND SITE_NO='")
				 .append(dataTransmission.getSiteNo())
				 .append("' AND OCCUR_DATE='")
				 .append(dataTransmission.getBusiDataDate())
				 .append("'");
		 return sql.toString();
	 }
 
	 public int updateStatisticTbRepealTOSlip(DataTransmission dataTransmission)throws Exception {
		 StringBuffer sql = new StringBuffer();
		 sql.append("UPDATE SUPERVISE_STATISTIC_TB SET SUPERVISE_PASS_COUNT=SUPERVISE_PASS_COUNT-1,")
				 .append("SUPERVISE_SLIP_COUNT=SUPERVISE_SLIP_COUNT+1")
				 .append(" WHERE MODEL_ID=")
				 .append(dataTransmission.getModelInfo().getModelId())
				 .append(" AND SITE_NO='")
				 .append(dataTransmission.getSiteNo())
				 .append("' AND OCCUR_DATE='")
				 .append(dataTransmission.getBusiDataDate())
				 .append("'");
		 logger.info("补发同步更新模型统计表：" + sql.toString());
		 Session session = this.getSessionFactory().getCurrentSession();
		 return session.createSQLQuery(sql.toString()).executeUpdate();
	 }
	 /**
	  * 当天数据录入检查情况时，需要维护统计表
	  * @param dataTransmission
	  * @return
	  * @throws Exception
	  * <AUTHOR>
	  */
	 public int updateStatisticTbRepeal(DataTransmission dataTransmission)throws Exception {
		 String sql = updateStatisticTbRepealSQL(dataTransmission);
		 logger.info("当天数据监督录入检查情况同步更新模型统计表：" + sql);
		 Session session = this.getSessionFactory().getCurrentSession();
		 return session.createSQLQuery(sql).executeUpdate();
	 }
	 /**
	  * @Title: updateStatisticTbRepealSQL
	  * @Description: 获取统计语句SQL
	  * @return String
	  * <AUTHOR>
	  */
	 private String updateStatisticTbRepealSQL(DataTransmission dataTransmission) {
		 StringBuffer sql = new StringBuffer();
		 sql.append("UPDATE ARMS_STATISTIC_TB SET NOT_DEALED_COUNT=NOT_DEALED_COUNT-1,")
				 .append(" HAVE_DEALED_COUNT=HAVE_DEALED_COUNT+1,PASS_COUNT=PASS_COUNT+1")
				 .append(" WHERE MODEL_ID=")
				 .append(dataTransmission.getModelInfo().getModelId())
				 .append(" AND SITE_NO='")
				 .append(dataTransmission.getSiteNo())
				 .append("' AND OCCUR_DATE='")
				 .append(dataTransmission.getBusiDataDate())
				 .append("'");
		 return sql.toString();
	 }
 
	 /**
	  * 当天数据录入检查情况时，需要维护统计表
	  * @param modelTableName
	  * @param modelId
	  * @param modelRowId
	  * @return
	  * @throws Exception
	  */
	 public int updateStatisticTbRepeal2(String modelTableName,int modelId,int modelRowId)throws Exception {
		 StringBuffer sql = new StringBuffer();
		 sql.append("UPDATE SUPERVISE_STATISTIC_TB T SET NOT_DEALED_COUNT=NOT_DEALED_COUNT-1,")
				 .append(" HAVE_DEALED_COUNT=HAVE_DEALED_COUNT+1,SUPERVISE_PASS_COUNT=SUPERVISE_PASS_COUNT+1")
				 .append(" WHERE EXISTS (SELECT 1 FROM  ")
				 .append(modelTableName)
				 .append(" Y WHERE Y.SITE_NO = T.SITE_NO ")
				 .append(" AND Y.OCCUR_DATE = T.OCCUR_DATE")
				 .append(" AND Y.MODELROW_ID = " +modelRowId)
				 .append(" AND T.MODEL_ID = "+modelId+")");
		 logger.info("当天数据监督录入检查情况同步更新模型统计表：" + sql);
		 Session session = this.getSessionFactory().getCurrentSession();
		 return session.createSQLQuery(sql.toString()).executeUpdate();
	 }
 
 
	 public int updateStatisticTbRepeal2All(String modelTableName,int modelId,int modelRowId,int count)throws Exception {
		 StringBuffer sql = new StringBuffer();
		 sql.append("UPDATE SUPERVISE_STATISTIC_TB T SET NOT_DEALED_COUNT=NOT_DEALED_COUNT-'"+count+"',")
				 .append(" HAVE_DEALED_COUNT=HAVE_DEALED_COUNT+'"+count+"',SUPERVISE_PASS_COUNT=SUPERVISE_PASS_COUNT+'"+count+"'")
				 .append(" WHERE EXISTS (SELECT 1 FROM  ")
				 .append(modelTableName)
				 .append(" Y WHERE Y.SITE_NO = T.SITE_NO ")
				 .append(" AND Y.OCCUR_DATE = T.OCCUR_DATE")
				 .append(" AND Y.MODELROW_ID = " +modelRowId)
				 .append(" AND T.MODEL_ID = "+modelId+")");
		 logger.info("当天数据监督录入检查情况同步更新模型统计表：" + sql);
		 Session session = this.getSessionFactory().getCurrentSession();
		 return session.createSQLQuery(sql.toString()).executeUpdate();
	 }
 
 
	 /**
	  *
	  * @param parentOrgan
	  * @param userNo
	  * @param modelInfo
	  * @param condition
	  * @return
	  * <AUTHOR>
	  */
	 public List<Object[]> creunitDataStatisticsDao(String parentOrgan,
													String userNo, ModelInfo modelInfo, String condition) throws Exception{
		 String sql = creunitDataStatisticsDaoSQL(parentOrgan, userNo, modelInfo, condition);
		 logger.info("统计支行模型数据处理表：" + sql);
		 Session session = this.getSessionFactory().getCurrentSession();
		 SQLQuery query = session.createSQLQuery(sql);
 
		 query.addScalar("ORGAN_NO", Hibernate.STRING);
		 query.addScalar("ORGAN_NAME", Hibernate.STRING);
		 query.addScalar("S3", Hibernate.INTEGER);
		 query.addScalar("S4", Hibernate.INTEGER);
		 query.addScalar("S5", Hibernate.INTEGER);
		 query.addScalar("S6", Hibernate.INTEGER);
 
		 return query.list();
	 }
	 public List<Object[]> siteDataStatisticsDao(String parentOrgan,
												 String userNo, ModelInfo modelInfo, String condition) {
		 String sql = siteDataStatisticsDaoSQL(parentOrgan, userNo, modelInfo, condition);
		 logger.info("统计网点模型数据处理表：" + sql);
		 Session session = this.getSessionFactory().getCurrentSession();
		 SQLQuery query = session.createSQLQuery(sql);
 
		 query.addScalar("ORGAN_NO", Hibernate.STRING);
		 query.addScalar("ORGAN_NAME", Hibernate.STRING);
		 query.addScalar("3", Hibernate.INTEGER);
		 query.addScalar("4", Hibernate.INTEGER);
		 query.addScalar("5", Hibernate.INTEGER);
		 query.addScalar("6", Hibernate.INTEGER);
 
		 return query.list();
	 }
 
 
 
 
 }
 