{"remainingRequest": "E:\\华南区项目\\中国银行\\开发区\\科技公司运营平台\\前端\\dopUnify\\node_modules\\babel-loader\\lib\\index.js!E:\\华南区项目\\中国银行\\开发区\\科技公司运营平台\\前端\\dopUnify\\node_modules\\eslint-loader\\index.js??ref--13-0!E:\\华南区项目\\中国银行\\开发区\\科技公司运营平台\\前端\\dopUnify\\src\\utils\\validate.js", "dependencies": [{"path": "E:\\华南区项目\\中国银行\\开发区\\科技公司运营平台\\前端\\dopUnify\\src\\utils\\validate.js", "mtime": 1721631954662}, {"path": "E:\\华南区项目\\中国银行\\开发区\\科技公司运营平台\\前端\\dopUnify\\babel.config.js", "mtime": 1721631954662}, {"path": "E:\\华南区项目\\中国银行\\开发区\\科技公司运营平台\\前端\\dopUnify\\.babelrc", "mtime": 1721631954662}, {"path": "E:\\华南区项目\\中国银行\\开发区\\科技公司运营平台\\前端\\dopUnify\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "E:\\华南区项目\\中国银行\\开发区\\科技公司运营平台\\前端\\dopUnify\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "E:\\华南区项目\\中国银行\\开发区\\科技公司运营平台\\前端\\dopUnify\\node_modules\\eslint-loader\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:aW1wb3J0ICJjb3JlLWpzL21vZHVsZXMvZXMucmVnZXhwLmV4ZWMuanMiOwppbXBvcnQgImNvcmUtanMvbW9kdWxlcy9lcy5yZWdleHAudGVzdC5qcyI7CmltcG9ydCAiY29yZS1qcy9tb2R1bGVzL2VzLnN0cmluZy50cmltLmpzIjsKaW1wb3J0ICJjb3JlLWpzL21vZHVsZXMvZXMub2JqZWN0LnRvLXN0cmluZy5qcyI7Ci8qKgogKiDlhazlhbHmoKHpqozphY3nva4qLwovKioKICogQHBhcmFtIHtzdHJpbmd9IHBhdGgKICogQHJldHVybnMge0Jvb2xlYW59CiAqLwpleHBvcnQgZnVuY3Rpb24gaXNFeHRlcm5hbChwYXRoKSB7CiAgcmV0dXJuIC9eKGh0dHBzPzp8bWFpbHRvOnx0ZWw6KS8udGVzdChwYXRoKTsKfQoKLyoqCiAqIEBwYXJhbSB7c3RyaW5nfSBzdHIKICogQHJldHVybnMge0Jvb2xlYW59CiAqLwpleHBvcnQgZnVuY3Rpb24gdmFsaWRVc2VybmFtZShzdHIpIHsKICB2YXIgdmFsaWRfbWFwID0gWydhZG1pbicsICdlZGl0b3InXTsKICByZXR1cm4gdmFsaWRfbWFwLmluZGV4T2Yoc3RyLnRyaW0oKSkgPj0gMDsKfQoKLyoqCiAqIEBwYXJhbSB7c3RyaW5nfSB1cmwKICogQHJldHVybnMge0Jvb2xlYW59CiAqLwpleHBvcnQgZnVuY3Rpb24gdmFsaWRVUkwodXJsKSB7CiAgdmFyIHJlZyA9IC9eKGh0dHBzP3xmdHApOlwvXC8oW2EtekEtWjAtOS4tXSsoOlthLXpBLVowLTkuJiUkLV0rKSpAKSooKDI1WzAtNV18MlswLTRdWzAtOV18MVswLTldezJ9fFsxLTldWzAtOV0/KShcLigyNVswLTVdfDJbMC00XVswLTldfDFbMC05XXsyfXxbMS05XT9bMC05XSkpezN9fChbYS16QS1aMC05LV0rXC4pKlthLXpBLVowLTktXStcLihjb218ZWR1fGdvdnxpbnR8bWlsfG5ldHxvcmd8Yml6fGFycGF8aW5mb3xuYW1lfHByb3xhZXJvfGNvb3B8bXVzZXVtfFthLXpBLVpdezJ9KSkoOlswLTldKykqKFwvKCR8W2EtekEtWjAtOS4sPydcXCsmJSQjPX5fLV0rKSkqJC87CiAgcmV0dXJuIHJlZy50ZXN0KHVybCk7Cn0KCi8qKgogKiBAcGFyYW0ge3N0cmluZ30gc3RyCiAqIEByZXR1cm5zIHtCb29sZWFufQogKi8KZXhwb3J0IGZ1bmN0aW9uIHZhbGlkTG93ZXJDYXNlKHN0cikgewogIHZhciByZWcgPSAvXlthLXpdKyQvOwogIHJldHVybiByZWcudGVzdChzdHIpOwp9CgovKioKICogQHBhcmFtIHtzdHJpbmd9IHN0cgogKiBAcmV0dXJucyB7Qm9vbGVhbn0KICovCmV4cG9ydCBmdW5jdGlvbiB2YWxpZFVwcGVyQ2FzZShzdHIpIHsKICB2YXIgcmVnID0gL15bQS1aXSskLzsKICByZXR1cm4gcmVnLnRlc3Qoc3RyKTsKfQoKLyoqCiAqIEBwYXJhbSB7c3RyaW5nfSBzdHIKICogQHJldHVybnMge0Jvb2xlYW59CiAqLwpleHBvcnQgZnVuY3Rpb24gdmFsaWRBbHBoYWJldHMoc3RyKSB7CiAgdmFyIHJlZyA9IC9eW0EtWmEtel0rJC87CiAgcmV0dXJuIHJlZy50ZXN0KHN0cik7Cn0KCi8qKgogKiBAcGFyYW0ge3N0cmluZ30gZW1haWwKICogQHJldHVybnMge0Jvb2xlYW59CiAqLwpleHBvcnQgZnVuY3Rpb24gdmFsaWRFbWFpbChlbWFpbCkgewogIHZhciByZWcgPSAvXigoW148PigpXFtcXVxcLiw7OlxzQCJdKyhcLltePD4oKVxbXF1cXC4sOzpcc0AiXSspKil8KCIuKyIpKUAoKFxbWzAtOV17MSwzfVwuWzAtOV17MSwzfVwuWzAtOV17MSwzfVwuWzAtOV17MSwzfVxdKXwoKFthLXpBLVpcLTAtOV0rXC4pK1thLXpBLVpdezIsfSkpJC87CiAgcmV0dXJuIHJlZy50ZXN0KGVtYWlsKTsKfQoKLyoqCiAqIEBwYXJhbSB7c3RyaW5nfSBzdHIKICogQHJldHVybnMge0Jvb2xlYW59CiAqLwpleHBvcnQgZnVuY3Rpb24gaXNTdHJpbmcoc3RyKSB7CiAgaWYgKHR5cGVvZiBzdHIgPT09ICdzdHJpbmcnIHx8IHN0ciBpbnN0YW5jZW9mIFN0cmluZykgewogICAgcmV0dXJuIHRydWU7CiAgfQogIHJldHVybiBmYWxzZTsKfQoKLyoqCiAqIEBwYXJhbSB7QXJyYXl9IGFyZwogKiBAcmV0dXJucyB7Qm9vbGVhbn0KICovCmV4cG9ydCBmdW5jdGlvbiBpc0FycmF5KGFyZykgewogIGlmICh0eXBlb2YgQXJyYXkuaXNBcnJheSA9PT0gJ3VuZGVmaW5lZCcpIHsKICAgIHJldHVybiBPYmplY3QucHJvdG90eXBlLnRvU3RyaW5nLmNhbGwoYXJnKSA9PT0gJ1tvYmplY3QgQXJyYXldJzsKICB9CiAgcmV0dXJuIEFycmF5LmlzQXJyYXkoYXJnKTsKfQ=="}, {"version": 3, "names": ["isExternal", "path", "test", "validUsername", "str", "valid_map", "indexOf", "trim", "validURL", "url", "reg", "validLowerCase", "validUpperCase", "validAlphabets", "validEmail", "email", "isString", "String", "isArray", "arg", "Array", "Object", "prototype", "toString", "call"], "sources": ["E:/华南区项目/中国银行/开发区/科技公司运营平台/前端/dopUnify/src/utils/validate.js"], "sourcesContent": ["/**\n * 公共校验配置*/\n/**\n * @param {string} path\n * @returns {Boolean}\n */\nexport function isExternal(path) {\n  return /^(https?:|mailto:|tel:)/.test(path)\n}\n\n/**\n * @param {string} str\n * @returns {Boolean}\n */\nexport function validUsername(str) {\n  const valid_map = ['admin', 'editor']\n  return valid_map.indexOf(str.trim()) >= 0\n}\n\n/**\n * @param {string} url\n * @returns {Boolean}\n */\nexport function validURL(url) {\n  const reg = /^(https?|ftp):\\/\\/([a-zA-Z0-9.-]+(:[a-zA-Z0-9.&%$-]+)*@)*((25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9][0-9]?)(\\.(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9]?[0-9])){3}|([a-zA-Z0-9-]+\\.)*[a-zA-Z0-9-]+\\.(com|edu|gov|int|mil|net|org|biz|arpa|info|name|pro|aero|coop|museum|[a-zA-Z]{2}))(:[0-9]+)*(\\/($|[a-zA-Z0-9.,?'\\\\+&%$#=~_-]+))*$/\n  return reg.test(url)\n}\n\n/**\n * @param {string} str\n * @returns {Boolean}\n */\nexport function validLowerCase(str) {\n  const reg = /^[a-z]+$/\n  return reg.test(str)\n}\n\n/**\n * @param {string} str\n * @returns {Boolean}\n */\nexport function validUpperCase(str) {\n  const reg = /^[A-Z]+$/\n  return reg.test(str)\n}\n\n/**\n * @param {string} str\n * @returns {Boolean}\n */\nexport function validAlphabets(str) {\n  const reg = /^[A-Za-z]+$/\n  return reg.test(str)\n}\n\n/**\n * @param {string} email\n * @returns {Boolean}\n */\nexport function validEmail(email) {\n  const reg = /^(([^<>()\\[\\]\\\\.,;:\\s@\"]+(\\.[^<>()\\[\\]\\\\.,;:\\s@\"]+)*)|(\".+\"))@((\\[[0-9]{1,3}\\.[0-9]{1,3}\\.[0-9]{1,3}\\.[0-9]{1,3}\\])|(([a-zA-Z\\-0-9]+\\.)+[a-zA-Z]{2,}))$/\n  return reg.test(email)\n}\n\n/**\n * @param {string} str\n * @returns {Boolean}\n */\nexport function isString(str) {\n  if (typeof str === 'string' || str instanceof String) {\n    return true\n  }\n  return false\n}\n\n/**\n * @param {Array} arg\n * @returns {Boolean}\n */\nexport function isArray(arg) {\n  if (typeof Array.isArray === 'undefined') {\n    return Object.prototype.toString.call(arg) === '[object Array]'\n  }\n  return Array.isArray(arg)\n}\n"], "mappings": ";;;;AAAA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASA,UAAU,CAACC,IAAI,EAAE;EAC/B,OAAO,yBAAyB,CAACC,IAAI,CAACD,IAAI,CAAC;AAC7C;;AAEA;AACA;AACA;AACA;AACA,OAAO,SAASE,aAAa,CAACC,GAAG,EAAE;EACjC,IAAMC,SAAS,GAAG,CAAC,OAAO,EAAE,QAAQ,CAAC;EACrC,OAAOA,SAAS,CAACC,OAAO,CAACF,GAAG,CAACG,IAAI,EAAE,CAAC,IAAI,CAAC;AAC3C;;AAEA;AACA;AACA;AACA;AACA,OAAO,SAASC,QAAQ,CAACC,GAAG,EAAE;EAC5B,IAAMC,GAAG,GAAG,4TAA4T;EACxU,OAAOA,GAAG,CAACR,IAAI,CAACO,GAAG,CAAC;AACtB;;AAEA;AACA;AACA;AACA;AACA,OAAO,SAASE,cAAc,CAACP,GAAG,EAAE;EAClC,IAAMM,GAAG,GAAG,UAAU;EACtB,OAAOA,GAAG,CAACR,IAAI,CAACE,GAAG,CAAC;AACtB;;AAEA;AACA;AACA;AACA;AACA,OAAO,SAASQ,cAAc,CAACR,GAAG,EAAE;EAClC,IAAMM,GAAG,GAAG,UAAU;EACtB,OAAOA,GAAG,CAACR,IAAI,CAACE,GAAG,CAAC;AACtB;;AAEA;AACA;AACA;AACA;AACA,OAAO,SAASS,cAAc,CAACT,GAAG,EAAE;EAClC,IAAMM,GAAG,GAAG,aAAa;EACzB,OAAOA,GAAG,CAACR,IAAI,CAACE,GAAG,CAAC;AACtB;;AAEA;AACA;AACA;AACA;AACA,OAAO,SAASU,UAAU,CAACC,KAAK,EAAE;EAChC,IAAML,GAAG,GAAG,yJAAyJ;EACrK,OAAOA,GAAG,CAACR,IAAI,CAACa,KAAK,CAAC;AACxB;;AAEA;AACA;AACA;AACA;AACA,OAAO,SAASC,QAAQ,CAACZ,GAAG,EAAE;EAC5B,IAAI,OAAOA,GAAG,KAAK,QAAQ,IAAIA,GAAG,YAAYa,MAAM,EAAE;IACpD,OAAO,IAAI;EACb;EACA,OAAO,KAAK;AACd;;AAEA;AACA;AACA;AACA;AACA,OAAO,SAASC,OAAO,CAACC,GAAG,EAAE;EAC3B,IAAI,OAAOC,KAAK,CAACF,OAAO,KAAK,WAAW,EAAE;IACxC,OAAOG,MAAM,CAACC,SAAS,CAACC,QAAQ,CAACC,IAAI,CAACL,GAAG,CAAC,KAAK,gBAAgB;EACjE;EACA,OAAOC,KAAK,CAACF,OAAO,CAACC,GAAG,CAAC;AAC3B"}]}