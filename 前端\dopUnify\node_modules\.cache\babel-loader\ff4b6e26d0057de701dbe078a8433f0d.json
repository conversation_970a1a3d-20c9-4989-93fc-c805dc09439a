{"remainingRequest": "E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1\\数字运营平台-统一门户工程\\dopUnify\\node_modules\\babel-loader\\lib\\index.js!E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1\\数字运营平台-统一门户工程\\dopUnify\\node_modules\\@babel\\runtime\\helpers\\isNativeReflectConstruct.js", "dependencies": [{"path": "E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1\\数字运营平台-统一门户工程\\dopUnify\\node_modules\\@babel\\runtime\\helpers\\isNativeReflectConstruct.js", "mtime": 456789000000}, {"path": "E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1\\数字运营平台-统一门户工程\\dopUnify\\babel.config.js", "mtime": 1716875191548}, {"path": "E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1\\数字运营平台-统一门户工程\\dopUnify\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1\\数字运营平台-统一门户工程\\dopUnify\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:cmVxdWlyZSgiY29yZS1qcy9tb2R1bGVzL2VzLm9iamVjdC50by1zdHJpbmcuanMiKTsKcmVxdWlyZSgiY29yZS1qcy9tb2R1bGVzL2VzLnJlZmxlY3QuY29uc3RydWN0LmpzIik7CmZ1bmN0aW9uIF9pc05hdGl2ZVJlZmxlY3RDb25zdHJ1Y3QoKSB7CiAgaWYgKHR5cGVvZiBSZWZsZWN0ID09PSAidW5kZWZpbmVkIiB8fCAhUmVmbGVjdC5jb25zdHJ1Y3QpIHJldHVybiBmYWxzZTsKICBpZiAoUmVmbGVjdC5jb25zdHJ1Y3Quc2hhbSkgcmV0dXJuIGZhbHNlOwogIGlmICh0eXBlb2YgUHJveHkgPT09ICJmdW5jdGlvbiIpIHJldHVybiB0cnVlOwogIHRyeSB7CiAgICBCb29sZWFuLnByb3RvdHlwZS52YWx1ZU9mLmNhbGwoUmVmbGVjdC5jb25zdHJ1Y3QoQm9vbGVhbiwgW10sIGZ1bmN0aW9uICgpIHt9KSk7CiAgICByZXR1cm4gdHJ1ZTsKICB9IGNhdGNoIChlKSB7CiAgICByZXR1cm4gZmFsc2U7CiAgfQp9Cm1vZHVsZS5leHBvcnRzID0gX2lzTmF0aXZlUmVmbGVjdENvbnN0cnVjdCwgbW9kdWxlLmV4cG9ydHMuX19lc01vZHVsZSA9IHRydWUsIG1vZHVsZS5leHBvcnRzWyJkZWZhdWx0Il0gPSBtb2R1bGUuZXhwb3J0czs="}, {"version": 3, "names": ["_isNativeReflectConstruct", "Reflect", "construct", "sham", "Proxy", "Boolean", "prototype", "valueOf", "call", "e", "module", "exports", "__esModule"], "sources": ["E:/1_Project/XYD_Project/dop-4.0/dop-4.1/数字运营平台-统一门户工程/dopUnify/node_modules/@babel/runtime/helpers/isNativeReflectConstruct.js"], "sourcesContent": ["function _isNativeReflectConstruct() {\n  if (typeof Reflect === \"undefined\" || !Reflect.construct) return false;\n  if (Reflect.construct.sham) return false;\n  if (typeof Proxy === \"function\") return true;\n  try {\n    Boolean.prototype.valueOf.call(Reflect.construct(<PERSON><PERSON><PERSON>, [], function () {}));\n    return true;\n  } catch (e) {\n    return false;\n  }\n}\nmodule.exports = _isNativeReflectConstruct, module.exports.__esModule = true, module.exports[\"default\"] = module.exports;"], "mappings": ";;AAAA,SAASA,yBAAyB,GAAG;EACnC,IAAI,OAAOC,OAAO,KAAK,WAAW,IAAI,CAACA,OAAO,CAACC,SAAS,EAAE,OAAO,KAAK;EACtE,IAAID,OAAO,CAACC,SAAS,CAACC,IAAI,EAAE,OAAO,KAAK;EACxC,IAAI,OAAOC,KAAK,KAAK,UAAU,EAAE,OAAO,IAAI;EAC5C,IAAI;IACFC,OAAO,CAACC,SAAS,CAACC,OAAO,CAACC,IAAI,CAACP,OAAO,CAACC,SAAS,CAACG,OAAO,EAAE,EAAE,EAAE,YAAY,CAAC,CAAC,CAAC,CAAC;IAC9E,OAAO,IAAI;EACb,CAAC,CAAC,OAAOI,CAAC,EAAE;IACV,OAAO,KAAK;EACd;AACF;AACAC,MAAM,CAACC,OAAO,GAAGX,yBAAyB,EAAEU,MAAM,CAACC,OAAO,CAACC,UAAU,GAAG,IAAI,EAAEF,MAAM,CAACC,OAAO,CAAC,SAAS,CAAC,GAAGD,MAAM,CAACC,OAAO"}]}