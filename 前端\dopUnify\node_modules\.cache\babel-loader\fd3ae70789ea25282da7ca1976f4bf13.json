{"remainingRequest": "E:\\华南区项目\\中国银行\\开发区\\科技公司运营平台\\前端\\dopUnify\\node_modules\\babel-loader\\lib\\index.js!E:\\华南区项目\\中国银行\\开发区\\科技公司运营平台\\前端\\dopUnify\\node_modules\\eslint-loader\\index.js??ref--13-0!E:\\华南区项目\\中国银行\\开发区\\科技公司运营平台\\前端\\dopUnify\\src\\layout\\components\\SublicenseLog\\info.js", "dependencies": [{"path": "E:\\华南区项目\\中国银行\\开发区\\科技公司运营平台\\前端\\dopUnify\\src\\layout\\components\\SublicenseLog\\info.js", "mtime": 1721631954662}, {"path": "E:\\华南区项目\\中国银行\\开发区\\科技公司运营平台\\前端\\dopUnify\\babel.config.js", "mtime": 1721631954662}, {"path": "E:\\华南区项目\\中国银行\\开发区\\科技公司运营平台\\前端\\dopUnify\\.babelrc", "mtime": 1721631954662}, {"path": "E:\\华南区项目\\中国银行\\开发区\\科技公司运营平台\\前端\\dopUnify\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "E:\\华南区项目\\中国银行\\开发区\\科技公司运营平台\\前端\\dopUnify\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "E:\\华南区项目\\中国银行\\开发区\\科技公司运营平台\\前端\\dopUnify\\node_modules\\eslint-loader\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["v1", "uuidv1", "config", "that", "date_two", "component", "label", "colSpan", "name", "componentProps", "type", "rangeSeparator", "startPlaceholder", "endPlaceholder", "valueFormat", "editable", "date_two2", "target_user_no", "clearable", "placeholder", "sublicense_reason", "options", "is_retake", "configTable", "id", "width"], "sources": ["E:/华南区项目/中国银行/开发区/科技公司运营平台/前端/dopUnify/src/layout/components/SublicenseLog/info.js"], "sourcesContent": ["import { v1 as uuidv1 } from 'uuid'\r\n// 表单\r\nexport const config = (that) => ({\r\n  date_two: {\r\n    component: 'date-picker',\r\n    label: '开始日期',\r\n    colSpan: 8,\r\n    name: 'date_two',\r\n    // config: {},\r\n    componentProps: {\r\n      type: 'daterange',\r\n      rangeSeparator: '-',\r\n      startPlaceholder: '开始日期',\r\n      endPlaceholder: '结束日期',\r\n      valueFormat: 'yyyyMMdd',\r\n      editable: false\r\n      // type: 'year'\r\n    }\r\n  },\r\n  date_two2: {\r\n    component: 'date-picker',\r\n    label: '结束日期',\r\n    colSpan: 8,\r\n    name: 'date_two2',\r\n    // config: {},\r\n    componentProps: {\r\n      type: 'daterange',\r\n      rangeSeparator: '-',\r\n      startPlaceholder: '开始日期',\r\n      endPlaceholder: '结束日期',\r\n      valueFormat: 'yyyyMMdd',\r\n      editable: false\r\n      // type: 'year'\r\n    }\r\n  },\r\n  target_user_no: {\r\n    component: 'input',\r\n    label: '目标用户',\r\n    colSpan: 8,\r\n    name: 'target_user_no',\r\n    componentProps: {\r\n      clearable: true,\r\n      placeholder: '支持目标用户模糊查询'\r\n    }\r\n  },\r\n  sublicense_reason: {\r\n    component: 'select',\r\n    label: '授权原因',\r\n    colSpan: 8,\r\n    name: 'sublicense_reason',\r\n    componentProps: {\r\n      clearable: true,\r\n      placeholder: '请选择'\r\n    },\r\n    options: []\r\n  },\r\n  is_retake: {\r\n    component: 'select',\r\n    label: '授权状态',\r\n    colSpan: 8,\r\n    name: 'is_retake',\r\n    componentProps: {\r\n      clearable: true\r\n    },\r\n    options: []\r\n  }\r\n})\r\n\r\n// 表头\r\nexport const configTable = (that) => [\r\n  {\r\n    name: 'user_no',\r\n    label: '授权用户',\r\n    id: uuidv1(),\r\n    width: 200\r\n  },\r\n  {\r\n    name: 'target_user_no',\r\n    label: '目标用户',\r\n    id: uuidv1()\r\n  },\r\n  {\r\n    name: 'start_date',\r\n    label: '开始日期',\r\n    id: uuidv1()\r\n  },\r\n  {\r\n    name: 'end_date',\r\n    label: '结束日期',\r\n    id: uuidv1()\r\n  },\r\n  {\r\n    name: 'is_retake',\r\n    label: '授权状态',\r\n    id: uuidv1()\r\n  },\r\n  {\r\n    name: 'sublicense_reason',\r\n    label: '授权原因',\r\n    id: uuidv1()\r\n  },\r\n  {\r\n    name: 'last_modi_date',\r\n    label: '收回日期',\r\n    id: uuidv1()\r\n  }\r\n]\r\n"], "mappings": "AAAA,SAASA,EAAE,IAAIC,MAAM,QAAQ,MAAM;AACnC;AACA,OAAO,IAAMC,MAAM,GAAG,SAATA,MAAM,CAAIC,IAAI;EAAA,OAAM;IAC/BC,QAAQ,EAAE;MACRC,SAAS,EAAE,aAAa;MACxBC,KAAK,EAAE,MAAM;MACbC,OAAO,EAAE,CAAC;MACVC,IAAI,EAAE,UAAU;MAChB;MACAC,cAAc,EAAE;QACdC,IAAI,EAAE,WAAW;QACjBC,cAAc,EAAE,GAAG;QACnBC,gBAAgB,EAAE,MAAM;QACxBC,cAAc,EAAE,MAAM;QACtBC,WAAW,EAAE,UAAU;QACvBC,QAAQ,EAAE;QACV;MACF;IACF,CAAC;;IACDC,SAAS,EAAE;MACTX,SAAS,EAAE,aAAa;MACxBC,KAAK,EAAE,MAAM;MACbC,OAAO,EAAE,CAAC;MACVC,IAAI,EAAE,WAAW;MACjB;MACAC,cAAc,EAAE;QACdC,IAAI,EAAE,WAAW;QACjBC,cAAc,EAAE,GAAG;QACnBC,gBAAgB,EAAE,MAAM;QACxBC,cAAc,EAAE,MAAM;QACtBC,WAAW,EAAE,UAAU;QACvBC,QAAQ,EAAE;QACV;MACF;IACF,CAAC;;IACDE,cAAc,EAAE;MACdZ,SAAS,EAAE,OAAO;MAClBC,KAAK,EAAE,MAAM;MACbC,OAAO,EAAE,CAAC;MACVC,IAAI,EAAE,gBAAgB;MACtBC,cAAc,EAAE;QACdS,SAAS,EAAE,IAAI;QACfC,WAAW,EAAE;MACf;IACF,CAAC;IACDC,iBAAiB,EAAE;MACjBf,SAAS,EAAE,QAAQ;MACnBC,KAAK,EAAE,MAAM;MACbC,OAAO,EAAE,CAAC;MACVC,IAAI,EAAE,mBAAmB;MACzBC,cAAc,EAAE;QACdS,SAAS,EAAE,IAAI;QACfC,WAAW,EAAE;MACf,CAAC;MACDE,OAAO,EAAE;IACX,CAAC;IACDC,SAAS,EAAE;MACTjB,SAAS,EAAE,QAAQ;MACnBC,KAAK,EAAE,MAAM;MACbC,OAAO,EAAE,CAAC;MACVC,IAAI,EAAE,WAAW;MACjBC,cAAc,EAAE;QACdS,SAAS,EAAE;MACb,CAAC;MACDG,OAAO,EAAE;IACX;EACF,CAAC;AAAA,CAAC;;AAEF;AACA,OAAO,IAAME,WAAW,GAAG,SAAdA,WAAW,CAAIpB,IAAI;EAAA,OAAK,CACnC;IACEK,IAAI,EAAE,SAAS;IACfF,KAAK,EAAE,MAAM;IACbkB,EAAE,EAAEvB,MAAM,EAAE;IACZwB,KAAK,EAAE;EACT,CAAC,EACD;IACEjB,IAAI,EAAE,gBAAgB;IACtBF,KAAK,EAAE,MAAM;IACbkB,EAAE,EAAEvB,MAAM;EACZ,CAAC,EACD;IACEO,IAAI,EAAE,YAAY;IAClBF,KAAK,EAAE,MAAM;IACbkB,EAAE,EAAEvB,MAAM;EACZ,CAAC,EACD;IACEO,IAAI,EAAE,UAAU;IAChBF,KAAK,EAAE,MAAM;IACbkB,EAAE,EAAEvB,MAAM;EACZ,CAAC,EACD;IACEO,IAAI,EAAE,WAAW;IACjBF,KAAK,EAAE,MAAM;IACbkB,EAAE,EAAEvB,MAAM;EACZ,CAAC,EACD;IACEO,IAAI,EAAE,mBAAmB;IACzBF,KAAK,EAAE,MAAM;IACbkB,EAAE,EAAEvB,MAAM;EACZ,CAAC,EACD;IACEO,IAAI,EAAE,gBAAgB;IACtBF,KAAK,EAAE,MAAM;IACbkB,EAAE,EAAEvB,MAAM;EACZ,CAAC,CACF;AAAA"}]}