package com.sunyard.etl.nps.service.business;


import java.io.File;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.Executors;
import java.util.concurrent.TimeUnit;

import org.dom4j.DocumentException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
 
import com.sunyard.etl.nps.common.NPSContants;
import com.sunyard.etl.nps.dao.ADMS5Dao;
import com.sunyard.etl.nps.model.BpTmpdata1Tb;
import com.sunyard.etl.nps.model.NpBusinessData;
import com.sunyard.etl.nps.model.NpImageData;
import com.sunyard.etl.nps.model.SmDataSourceSetTb;
import com.sunyard.etl.nps.service.base.ADMS5Service;
import com.sunyard.etl.nps.service.base.ECMService;
import com.sunyard.etl.system.common.Constants;
import com.sunyard.etl.system.dao.SmServiceRegDao;
import com.sunyard.etl.system.dao.impl.SmServiceRegDaoImpl;
import com.sunyard.etl.system.model.JobParam;
import com.sunyard.etl.system.model.SmServiceReg;
import com.xxl.job.core.log.XxlJobLogger;
import com.sunyard.etl.nps.dao.NpImageDataTbDao;
import com.sunyard.etl.nps.dao.NpBusinessDataTbDao;
import com.sunyard.etl.nps.dao.SmDataSourceSetTbDao;


/**
 * 
 * @Title SimpleECMService
 * @Description 影像文件存储在ECM,不需要重新上传
 * <AUTHOR>
 * 2017年8月7日
 * @return
 */
public class ImageQueryService {
	private String tableName = "ImageQueryService";
	protected final Logger log = LoggerFactory.getLogger(getClass());

	private JobParam jobParam;
	
	private ECMService myECMService;
	private ADMS5Service ADMS5Service = new ADMS5Service("tableName");
	
	private ADMS5Dao ADMS5Dao = new ADMS5Dao();
	private NpImageDataTbDao npImageDataTbDao = new NpImageDataTbDao("tableName");
	private NpBusinessDataTbDao npBusinessDataTbDao = new NpBusinessDataTbDao("tableName");
	private SmDataSourceSetTbDao sourceDao  = new SmDataSourceSetTbDao();
	private SmServiceRegDao smServiceRegDao = new SmServiceRegDaoImpl();
	
	public ImageQueryService(){
		
	}
	
	public String init(JobParam jobParam) {
//		XxlJobLogger.log("SmDataSourceSetTb 表中数据异常", tableName);
		this.jobParam = jobParam;
		String sourceId = jobParam.getDataSourceId();
		XxlJobLogger.log("获取到sourceId" + sourceId, tableName);
		SmDataSourceSetTb source = sourceDao.getById(sourceId);
				
		if (null != source) {
			SmServiceReg ECM = smServiceRegDao.getServiceRegByServiceIdDao(source.getServiceId());
			myECMService = new ECMService(ECM.getServiceIp(),Integer.parseInt(ECM.getServicePort()),ECM.getLoginName(),ECM.getLoginPass());
			myECMService.set(source.getGroupName(), source.getModeCode(),source.getFilePartName(), source.getIndexName());
			XxlJobLogger.log("初始化ECM服务参数...", tableName);
			XxlJobLogger.log("ip:"+ECM.getServiceIp(), tableName);
			XxlJobLogger.log("port:"+ECM.getServicePort(), tableName);
			XxlJobLogger.log("username:"+ECM.getLoginName(), tableName);
			XxlJobLogger.log("password:"+ECM.getLoginPass(), tableName);
			XxlJobLogger.log("GroupName:"+source.getGroupName(), tableName);
			XxlJobLogger.log("ModeCode:"+source.getModeCode(), tableName);
			XxlJobLogger.log("FilePartName:"+source.getFilePartName(), tableName);
			XxlJobLogger.log("IndexName:"+source.getIndexName(), tableName);
		} else {
			XxlJobLogger.log("SmDataSourceSetTb 表中数据异常", tableName);
			return "FAIL|初始化失败";
		}
		return "SUCCESS|成功";
	}

	/*
	 * 业务处理
	 */
	@SuppressWarnings({ "unchecked", "rawtypes" })
	public String queryECM() {
		String dataSourceId = this.jobParam.getDataSourceId();
		List<NpBusinessData> pendingBusinessData = npBusinessDataTbDao.getPendingBusinessData(dataSourceId);
		if (pendingBusinessData.size() == 0 || pendingBusinessData == null) {
			XxlJobLogger.log("当前需要进行ECM查询的业务", tableName);
			return "SUCCESS|当前需要进行ECM查询的业务";
		}
		try {
			if (!myECMService.login()) {
				XxlJobLogger.log("登录ECM失败", tableName);
				return "FALSE|登录ECM失";
			}
		} catch (Exception e) {
			XxlJobLogger.log(e.toString());		
			return "FALSE|登录ECM失";
		}
		/*
		 * 多线程下图改造
		 */
		NPSContants.NPSPool = Executors.newFixedThreadPool(NPSContants.maxServices);
		for (int i = 0; i < pendingBusinessData.size(); i++) {
			if (pendingBusinessData.get(i).getProcessing() == Constants.NOT_PROCESS) {
				pendingBusinessData.get(i).setProcessing(Constants.IN_PROCESS);
				ImageQueryService ser = new ImageQueryService();
				ser.init(this.jobParam);
				NPSContants.NPSPool.execute(new ThreadService(ser,pendingBusinessData.get(i)));
			}
		}
		NPSContants.NPSPool.shutdown();
		try {
			int i = 0;
			while (!NPSContants.NPSPool.awaitTermination(10, TimeUnit.SECONDS)) {
				XxlJobLogger.log("等待多线程执行完毕，等待次数 ： " + i);
				i++;
			}
		} catch (InterruptedException e) {
			XxlJobLogger.log("等待多线程异常", e.getMessage());
		}
		XxlJobLogger.log("多线程执行完毕");
		return "SUCCESS|成功";
	}
	

	/*
	 * 生成批次号
	 */
	public void generateBatchId(String dataSourceId) throws SQLException{
		ADMS5Service.generateBatchId(dataSourceId);
	}
	
	
	/*
	 * 差错业务处理
	 */
	public boolean businessError() {
		String dataSourceId = this.jobParam.getDataSourceId();
		XxlJobLogger.log("开始补扫处理任务，dataSourceId: " + dataSourceId , tableName);
		 try {
			List<NpBusinessData>  errorBusi = npBusinessDataTbDao.getPendingErrorBusinessData(dataSourceId);
			if(errorBusi.size() == 0){
				XxlJobLogger.log("暂时没有补扫业务: " + dataSourceId , tableName);
				return true;
			}
			for (NpBusinessData busi : errorBusi) {
				/*
				 * 将差错业务在无纸化数据表中的数据与ADMS5数据表中该笔业务的图像数据进行对比
				 */
				List<NpImageData> NPSImgList = npImageDataTbDao.queryByBusiDataNo(busi.getBusiDataNo());
				List<BpTmpdata1Tb> ADMS5ImgList = ADMS5Dao.queryBasiData(busi);
				BpTmpdata1Tb lastImg = new BpTmpdata1Tb();
				int maxInccodeinBatch = 0;
				if (NPSImgList == null || ADMS5ImgList == null) {
					return false;
				}
				for (NpImageData i : NPSImgList) {
					boolean has = false;
					for (BpTmpdata1Tb j : ADMS5ImgList) {
						if(j.getFileName().equals(i.getFileName())){
							has = true;
							break;
						}
						if(j.getInccodeinBatch() > maxInccodeinBatch){
							maxInccodeinBatch = j.getInccodeinBatch();
							lastImg = j;
						}
					}
					if (!has) {
						/*
						 * 拼装数据表对象
						 */
						int inccodeinBatch = lastImg.getInccodeinBatch()+1;
						List<BpTmpdata1Tb> list = new ArrayList<BpTmpdata1Tb>();
						BpTmpdata1Tb bpTmpdata1Tb = new BpTmpdata1Tb();
						bpTmpdata1Tb.setBatchId(lastImg.getBatchId());
						bpTmpdata1Tb.setPrimaryInccodein(lastImg.getPrimaryInccodein());
						bpTmpdata1Tb.setCheckFlag("0");
						bpTmpdata1Tb.setFlowId(lastImg.getFlowId());
						bpTmpdata1Tb.setFormName(NPSContants.NP_ERROR_FORMNAME);
						bpTmpdata1Tb.setInccodeinBatch(inccodeinBatch);
						bpTmpdata1Tb.setProcessState(lastImg.getProcessState());
						bpTmpdata1Tb.setPsLevel("0");
						bpTmpdata1Tb.setFileName(i.getFileName());
						bpTmpdata1Tb.setBackFileName(i.getBackFileName());
						bpTmpdata1Tb.setContentId(lastImg.getContentId());//由原业务系统追加到同一个CONTENTID中直接取原索引
						list.add(bpTmpdata1Tb);
						/*
						 * 更新批内码
						 */
						boolean result = ADMS5Dao.updateBpTmpdata1(lastImg.getBatchId(), inccodeinBatch);
						if(!result){
							XxlJobLogger.log("更新图像数据表失败:", tableName);
							return false;
						}
						/*
						 * 插入一个批次
						 */
						result = ADMS5Dao.insertBpTmpdata1(list);
						if(!result){
							XxlJobLogger.log("插入图像数据表失败:", tableName);
							// 这里要实现数据回滚
							return false;
						}
						/*
						 * 更新批次表
						 */
						result = ADMS5Dao.updateBpTmpBatch(lastImg.getBatchId());
						if(!result){
							XxlJobLogger.log("更新批次表失败:", tableName);
							// 这里要实现数据回滚
//							ADMS5Dao.deleteBpTmpBatch(lastImg.getBatchId(), inccodeinBatch);
							return false;
						}
					}
				}
				/*
				 * 更新差错业务状态
				 */
				npBusinessDataTbDao.updateFlag(busi.getBusiDataNo(), NPSContants.ADMS5);
			}
		 } catch (SQLException e) {
			e.printStackTrace();
		}
		return true;
	}



//	public String outBatch() {
//		String dataSourceId = this.jobParam.getDataSourceId();
//		try {
//			XxlJobLogger.log("正在输出批次到后督...", tableName);
//			List<String> batchIdList = npBusinessDataTbDao.getBatchId(dataSourceId);
//			if (batchIdList.size() == 0 || batchIdList == null) {
//				XxlJobLogger.log("当前NP_BUSINESS_DATA_TB表中没有可输出批次", tableName);
//				return "SUCCESS|没有可输出批次";
//			}
//			for (String batchId : batchIdList) {
//				XxlJobLogger.log("输出批次号：" + batchId , tableName);
//				BpTmpbatchTb bpTmpbatchTb = new BpTmpbatchTb();
//				bpTmpbatchTb.setBatchId(batchId);
//				boolean outputResult = ADMS5Service.outputBatch(bpTmpbatchTb,this.jobParam,NPSContants.NP_MIXED_ECM);
//				if(!outputResult){
//					XxlJobLogger.log("批次" + batchId + "输出失败", tableName);
//					continue;
//				}
//				if (!npBusinessDataTbDao.updateTarget(batchId, NPSContants.ADMS5)) {
//					XxlJobLogger.log("无纸化批次 " + batchId + "更新状态失败，删除插入的批次", tableName);
//					ADMS5Dao.cleanTmpBatch(batchId);
//					ADMS5Dao.cleanTmpData(batchId);
//					XxlJobLogger.log("无纸化批次 " + batchId + "删除成功", tableName);
//					return "FALSE|更新无纸化业务表处理状态异常";
//				}
//				XxlJobLogger.log("批次" + batchId + "输出完成", tableName);
//			}
//		} catch (SQLException e) {
//			XxlJobLogger.log("正在输出批次到后督失败");
//			XxlJobLogger.log(e.toString());
//			return "FALSE|输出批次到后督失败";
//		}		
//		return "SUCCESS|输出批次到后督成功";
//	}
	
	
	public String ThreadService(NpBusinessData busi){
		if (this.jobParam.getIsDownload().equals("1")){
			String occurDate = busi.getOccurDate();
			String siteNo = busi.getSiteNo();
			String operatorNo = busi.getOperatorNo();
			String dirPath = this.jobParam.getLocalPath() + File.separator + occurDate + File.separator + siteNo +  File.separator + operatorNo + File.separator;
			XxlJobLogger.log("业务影像存放路径：" + dirPath, tableName);
			File localFile = new File(dirPath);
			if (!localFile.exists()) {
				localFile.mkdirs();
			}
			String resultXml = myECMService.queryBatch(busi.getContentId(),busi.getIndexValue());// 索引查询返回XML
			if (resultXml == null) {
				XxlJobLogger.log("ECM查询失败", tableName);
				XxlJobLogger.log("本次接入结束，请系统管理管理员检查数据", tableName);
				return "FALSE|失败";
			}
			try {
				String result = myECMService.dowmload(busi, resultXml, dirPath,"0");
				if (result.contains("SUCCESS")){
					XxlJobLogger.log(result);
				} else {
					XxlJobLogger.log("下图失败");
				}
			} catch (Exception e) {
				XxlJobLogger.log("下图失败", e.toString());
			}
			try {
				int result = npBusinessDataTbDao.updateFlag("-1", busi.getBusiDataNo());
				if (result == 0) {
					XxlJobLogger.log("更新失败");
				}
			} catch (SQLException e) {
				XxlJobLogger.log("更新失败", e.toString());
			}
		} else if (this.jobParam.getIsDownload() == "0") {
			String resultXml = myECMService.queryBatch(busi.getContentId(),busi.getIndexValue());// 索引查询返回XML
			if (resultXml == null) {
				XxlJobLogger.log("ECM查询失败", tableName);
				XxlJobLogger.log("本次接入结束，请系统管理管理员检查数据", tableName);
				return "FALSE|失败";
			}
			List<NpImageData> npImageDataList;
			try {
				npImageDataList = myECMService.parseXML(resultXml, busi);// 解析XML
			} catch (DocumentException e) {
				e.printStackTrace();
				XxlJobLogger.log("解析XML失败", tableName);
				XxlJobLogger.log("本次接入结束，请系统管理管理员检查数据", tableName);
				return "FALSE|失败";
			}
			if (npImageDataList == null || npImageDataList.size() == 0) {
				XxlJobLogger.log("ECM图像下载失败", tableName);
				XxlJobLogger.log("本次接入结束，请系统管理管理员检查数据", tableName);
				return "FALSE|失败";
			}
			npImageDataTbDao.deleteByBusiDataNo(busi.getBusiDataNo());// 避免重复插入先删除
			boolean insertImgResult = npImageDataTbDao.insert(npImageDataList);
			if (!insertImgResult) {
				XxlJobLogger.log("插入NP_IMAGE_DATA_TB失败:", tableName);
				XxlJobLogger.log("本次接入结束，请系统管理管理员检查数据", tableName);
				return "FALSE|失败";
			}
		}
		return "SUCCESS|成功";
	}
	
}
