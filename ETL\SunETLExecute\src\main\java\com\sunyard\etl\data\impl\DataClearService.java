package com.sunyard.etl.data.impl;

import com.sunyard.etl.system.common.Constants;
import com.sunyard.etl.system.dao.*;
import com.sunyard.etl.system.dao.impl.*;
import com.sunyard.etl.system.model.SmTableDef;
import com.sunyard.etl.system.model.data.FlCheckoff;
import com.sunyard.etl.system.model.data.SmOrganData;
import com.xxl.job.core.log.XxlJobLogger;

/**
 * 清理程序实现类
 * <AUTHOR>
 *
 */
public class DataClearService extends Thread{
    private FlCheckoff flCheckoff;

    private SmOrganDataDao smOrganDataDao = new SmOrganDataDaoImpl();
    private SmTableDefDao smTableDefDao = new SmTableDefDaoImpl();
    private BpTmpBatchDao bpTmpBatchDao = new BpTmpBatchDaoImpl();
    private FlFlowDao flFlowDao = new FlFlowDaoImpl();
    private FlCheckoffDao flCheckoffDao = new FlCheckoffDaoImpl();
    private FlMonDataDao flMonDataDao = new FlMonDataDaoImpl();

    public DataClearService(FlCheckoff flCheckoff) {
        super();
        this.flCheckoff = flCheckoff;
    }

    public void run() {
        XxlJobLogger.log("业务日期：" + flCheckoff.getOccurDate() + "网点："
                + flCheckoff.getSiteNo() + "柜员：" + flCheckoff.getOperatorNo()
                + "开始清理！");

        //相关数据枷锁，保证唯一性
        if(flCheckoffDao.flcheckOnLock(flCheckoff.getOccurDate(),flCheckoff.getSiteNo(),flCheckoff.getOperatorNo()) <= 0){
            XxlJobLogger.log("业务日期：" + flCheckoff.getOccurDate() + "网点："
                    + flCheckoff.getSiteNo() + "柜员：" + flCheckoff.getOperatorNo()+"正在被处理,直接进入下一笔任务");
            return;
        }

        SmOrganData smOrganData = smOrganDataDao
                .getOrganDataByOrganNoDao(flCheckoff.getSiteNo());
        if (null == smOrganData) {
            XxlJobLogger.log("机构数据表未定义，请检查！");
            return;
        }
        SmTableDef batchTableDef = smTableDefDao
                .getTableDefByTableIdDao(smOrganData.getTmpbatchTbId());
        SmTableDef batchDataTableDef = smTableDefDao
                .getTableDefByTableIdDao(smOrganData.getTmpdataTbId());
        SmTableDef flowTableDef = smTableDefDao
                .getTableDefByTableIdDao(smOrganData.getFlowTbId());
        if (null == batchTableDef || null == batchDataTableDef
                || null == flowTableDef) {
            XxlJobLogger.log("未查询到机构对应相关表，请检查表定义表！");
            return;
        }
        boolean batchCleanFlag = bpTmpBatchDao.batchCleanDao(
                batchTableDef.getTableName(), batchDataTableDef.getTableName(), flCheckoff,
                Constants.IMAGE_STATUS_CLEAR);
        if (batchCleanFlag) {
            XxlJobLogger.log("影像表清理成功，批次图像状态更新成功！");
            boolean batchExistsFlag = bpTmpBatchDao.existsBatch(
                    batchTableDef.getTableName(), flCheckoff,
                    Constants.IMAGE_STATUS_CLEAR);
            if(!batchExistsFlag){
                XxlJobLogger.log("已无符合条件的批次表未清理，可以清理流水表，扎帐表！");
                boolean deleteFlag = flFlowDao.flowCleanDao(
                        flowTableDef.getTableName(), flCheckoff);
                if(deleteFlag){
                    XxlJobLogger.log("流水表与轧帐表清理成功！");
                }
            }
            boolean flowExistsFlag = flFlowDao.existsFlowByOccurDate(
                    flowTableDef.getTableName(), flCheckoff.getOccurDate());
            if (!flowExistsFlag) {
                XxlJobLogger.log("当天批次流水已全部归档清理，删除流水导入标识表！");
                boolean deleteFlMonDataFlag = flMonDataDao
                        .deleteFlMonDataByOccurDateDao(flCheckoff
                                .getOccurDate());
                if (deleteFlMonDataFlag) {
                    XxlJobLogger.log("流水导入标识表删除成功！");
                }
            }


            flCheckoffDao.flcheckUnLock(flCheckoff.getOccurDate(),flCheckoff.getSiteNo(),flCheckoff.getOperatorNo());
        }
    }

}
