{"remainingRequest": "E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1\\数字运营平台-统一门户工程\\dopUnify\\node_modules\\thread-loader\\dist\\cjs.js!E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1\\数字运营平台-统一门户工程\\dopUnify\\node_modules\\babel-loader\\lib\\index.js!E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1\\数字运营平台-统一门户工程\\dopUnify\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1\\数字运营平台-统一门户工程\\dopUnify\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1\\数字运营平台-统一门户工程\\dopUnify\\src\\views\\system\\organ\\component\\table\\index.vue?vue&type=script&lang=js&", "dependencies": [{"path": "E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1\\数字运营平台-统一门户工程\\dopUnify\\src\\views\\system\\organ\\component\\table\\index.vue", "mtime": 1719452474966}, {"path": "E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1\\数字运营平台-统一门户工程\\dopUnify\\babel.config.js", "mtime": 1716875191548}, {"path": "E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1\\数字运营平台-统一门户工程\\dopUnify\\.babelrc", "mtime": 1716875191502}, {"path": "E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1\\数字运营平台-统一门户工程\\dopUnify\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1\\数字运营平台-统一门户工程\\dopUnify\\node_modules\\thread-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1\\数字运营平台-统一门户工程\\dopUnify\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1\\数字运营平台-统一门户工程\\dopUnify\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1\\数字运营平台-统一门户工程\\dopUnify\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1655715099000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, null]}