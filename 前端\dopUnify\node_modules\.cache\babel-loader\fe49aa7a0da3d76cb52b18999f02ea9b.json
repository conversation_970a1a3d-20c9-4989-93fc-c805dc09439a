{"remainingRequest": "D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\node_modules\\babel-loader\\lib\\index.js!D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\src\\components\\Layout\\HeaderSearch\\index.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\src\\components\\Layout\\HeaderSearch\\index.vue", "mtime": 1686019810013}, {"path": "D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\babel.config.js", "mtime": 1686019818359}, {"path": "D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\.babelrc", "mtime": 1705285078668}, {"path": "D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1655715099000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA8BA;AACA;AACA;AACA;AAEA;EACAA;EACAC;IACA;MACAC;MACAC;MACAC;MACAC;MACAC;IACA;EACA;EACAC;IACAC;MACA;IACA;EACA;EACAC;IACAD;MACA;IACA;IACAJ;MACA;IACA;IACAC;MACA;MACA;QACAK;MACA;QACAA;MACA;IACA;EACA;EACAC;IACA;EACA;EACAC;IACAC;MACA;MAEA;QACA;MACA;IACA;IACAC;MACA;MACA;MACA;IACA;IACAC;MAAA;MACA;MACA;MACA;MACA;QACA;MACA;IACA;IACAC;MACA;QACAC;QACAC;QACAC;QACAC;QACAC;QACAC;QACAC,OACA;UACAvB;UACAwB;QACA,GACA;UACAxB;UACAwB;QACA;MAEA;IACA;IACA;IACA;IACAC;MAAA;MAAA;MACA;MAAA,2CAEAjB;QAAA;MAAA;QAAA;UAAA;UACA;UACA;YACA;UACA;UACA;YACAkB;YACAC;YAAA;YACAC;UACA;UAEA;YACA3B;YAEA;cACA;cACA;cACA4B;YACA;UACA;;UAEA;UACA;YACA,qCACAC,iBACA7B,WACAA,WACA;YACA;cACA4B;YACA;UACA;QACA;MAAA;QAAA;MAAA;QAAA;MAAA;MACA;IACA;IACAE;MACA;QACA,yBACA7B,cACA8B;UAAA;QAAA;MACA;QACA;MACA;IACA;EACA;AACA", "names": ["name", "data", "search", "options", "searchPool", "show", "fuse", "computed", "routes", "watch", "document", "mounted", "methods", "click", "close", "change", "initFuse", "shouldSort", "threshold", "location", "distance", "max<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "minMatchChar<PERSON>ength", "keys", "weight", "generateRoutes", "path", "is_parent", "title", "res", "router", "querySearch", "filter"], "sourceRoot": "src/components/Layout/HeaderSearch", "sources": ["index.vue"], "sourcesContent": ["<template>\n  <div :class=\"{ show: show }\" class=\"header-search\">\n    <sun-svg-icon\n      class=\"search\"\n      class-name=\"search-icon\"\n      icon-class=\"search\"\n      @click.stop=\"click\"\n    />\n    <el-select\n      ref=\"headerSearchSelect\"\n      v-model=\"search\"\n      :remote-method=\"querySearch\"\n      filterable\n      default-first-option\n      remote\n      placeholder=\"菜单搜索\"\n      class=\"header-search-select\"\n      @change=\"change\"\n    >\n      <el-option\n        v-for=\"item in options\"\n        :key=\"item.path\"\n        :value=\"item\"\n        :label=\"item.title.join(' > ')\"\n      />\n    </el-select>\n  </div>\n</template>\n\n<script>\n// fuse is a lightweight fuzzy-search module\n// make search results more in line with expectations\nimport Fuse from 'fuse.js'\nimport path from 'path'\n\nexport default {\n  name: 'HeaderSearch',\n  data() {\n    return {\n      search: '',\n      options: [],\n      searchPool: [],\n      show: false,\n      fuse: undefined\n    }\n  },\n  computed: {\n    routes() {\n      return this.$store.getters.permission_routes\n    }\n  },\n  watch: {\n    routes() {\n      this.searchPool = this.generateRoutes(this.routes)\n    },\n    searchPool(list) {\n      this.initFuse(list)\n    },\n    show(value) {\n      this.$store.commit('common/SET_SEARCH_FLAG', value)\n      if (value) {\n        document.body.addEventListener('click', this.close)\n      } else {\n        document.body.removeEventListener('click', this.close)\n      }\n    }\n  },\n  mounted() {\n    this.searchPool = this.generateRoutes(this.routes)\n  },\n  methods: {\n    click() {\n      this.show = !this.show\n\n      if (this.show) {\n        this.$refs.headerSearchSelect && this.$refs.headerSearchSelect.focus()\n      }\n    },\n    close() {\n      this.$refs.headerSearchSelect && this.$refs.headerSearchSelect.blur()\n      this.options = []\n      this.show = false\n    },\n    change(val) {\n      this.$router.push(val.path)\n      this.search = ''\n      this.options = []\n      this.$nextTick(() => {\n        this.show = false\n      })\n    },\n    initFuse(list) {\n      this.fuse = new Fuse(list, {\n        shouldSort: true,\n        threshold: 0.4,\n        location: 0,\n        distance: 100,\n        maxPatternLength: 32,\n        minMatchCharLength: 1,\n        keys: [\n          {\n            name: 'title',\n            weight: 0.7\n          },\n          {\n            name: 'path',\n            weight: 0.3\n          }\n        ]\n      })\n    },\n    // Filter out the routes that can be displayed in the sidebar\n    // And generate the internationalized title\n    generateRoutes(routes, basePath = '/', prefixTitle = []) {\n      let res = []\n\n      for (const router of routes) {\n        // skip hidden router\n        if (router.hidden) {\n          continue\n        }\n        const data = {\n          path: path.resolve(basePath, router.path),\n          is_parent: router.isParent, // 添加是否是父级菜单属性\n          title: [...prefixTitle]\n        }\n\n        if (router.meta && router.meta.title) {\n          data.title = [...data.title, router.meta.title]\n\n          if (router.redirect !== 'noRedirect') {\n            // only push the routes with title\n            // special case: need to exclude parent router without redirect\n            res.push(data)\n          }\n        }\n\n        // recursive child routes\n        if (router.children) {\n          const tempRoutes = this.generateRoutes(\n            router.children,\n            data.path,\n            data.title\n          )\n          if (tempRoutes.length >= 1) {\n            res = [...res, ...tempRoutes]\n          }\n        }\n      }\n      return res\n    },\n    querySearch(query) {\n      if (query !== '') {\n        this.options = this.fuse\n          .search(query)\n          .filter((item) => item.is_parent === '0')\n      } else {\n        this.options = []\n      }\n    }\n  }\n}\n</script>\n\n<style lang=\"scss\" scoped>\n@import '~@/assets/scss/common/index';\n.header-search {\n  position: relative;\n  &:after {\n    position: absolute;\n    content: '';\n    display: block;\n    width: 1px;\n    height: 15px;\n    background-color: #ccc;\n    top: 10px;\n    right: -12px;\n  }\n  font-size: 0 !important;\n\n  .search-icon {\n    cursor: pointer;\n    font-size: 18px;\n    vertical-align: middle;\n  }\n\n  .header-search-select {\n    font-size: 18px;\n    transition: width 0.2s;\n    width: 0;\n    overflow: hidden;\n    background: transparent;\n    border-radius: 0;\n    display: inline-block;\n    vertical-align: middle;\n\n    ::v-deep .el-input__inner {\n      border-radius: 0.5rem;\n      border: 0;\n      padding-left: 1rem;\n      padding-right: 1rem;\n      box-shadow: none !important;\n      border-bottom: 1px solid #d9d9d9;\n      vertical-align: middle;\n    }\n  }\n\n  &.show {\n    .header-search-select {\n      width: 120px;\n      margin-left: 10px;\n    }\n  }\n}\n.sun-svg-icon {\n  &.search {\n    width: 1.6rem;\n    height: 1.6rem;\n    fill: $pro_iconColor;\n    &:hover {\n      fill: $primary;\n    }\n  }\n}\n</style>\n"]}]}