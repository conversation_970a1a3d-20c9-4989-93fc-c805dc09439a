package com.sunyard.etl.nps.service.business;

import java.sql.SQLException;
import java.util.List;

import com.sunyard.etl.nps.common.NPSContants;
import com.sunyard.etl.nps.dao.ADMS5Dao;
import com.sunyard.etl.nps.dao.NpBusinessDataTbDao;
import com.sunyard.etl.nps.model.BpTmpbatchTb;
import com.sunyard.etl.nps.service.base.ADMS5Service;
import com.sunyard.etl.system.model.JobParam;
import com.xxl.job.core.log.XxlJobLogger;

public class OutBatchService {
	
	private JobParam jobParam;
	private ADMS5Service ADMS5Service = new ADMS5Service("tableName");
	private ADMS5Dao ADMS5Dao = new ADMS5Dao();
	private NpBusinessDataTbDao npBusinessDataTbDao = new NpBusinessDataTbDao("tableName");

	
	public OutBatchService (JobParam jobParam){
		this.jobParam = jobParam;
	}
	
	public String outBatch(){
		String dataSourceId = this.jobParam.getDataSourceId();
		try {
			XxlJobLogger.log("正在输出批次到后督...");
			List<String> batchIdList = npBusinessDataTbDao.getBatchId(dataSourceId);
			if (batchIdList.size() == 0 || batchIdList == null) {
				XxlJobLogger.log("当前NP_BUSINESS_DATA_TB表中没有可输出批次");
				return "SUCCESS|没有可输出批次";
			}
			for (String batchId : batchIdList) {
				XxlJobLogger.log("输出批次号：" + batchId);
				BpTmpbatchTb bpTmpbatchTb = new BpTmpbatchTb();
				bpTmpbatchTb.setBatchId(batchId);
				
				// 检查批次是否需要混合
				boolean mix = false;
				List<String> list  = npBusinessDataTbDao.checkMix(batchId);
				if(null != list){
					if(list.size() == 1){
						mix = list.get(0)=="1"?true:false;
					} 
					if(list.size() >= 2){
						XxlJobLogger.log("该批次数据异常，无法处理");
						continue;
					}
				}
				
				boolean outputResult = false;
				if (mix) {
					outputResult = ADMS5Service.AddBatch(bpTmpbatchTb, jobParam);

				} else {
					outputResult = ADMS5Service.outputBatch(bpTmpbatchTb,this.jobParam);
				}
				if(!outputResult){
					XxlJobLogger.log("批次" + batchId + "输出失败" );
					continue;
				}
				if (!npBusinessDataTbDao.updateTarget(batchId, NPSContants.ADMS5)) {
					XxlJobLogger.log("无纸化批次 " + batchId + "更新状态失败，删除插入的批次" );
					ADMS5Dao.cleanTmpBatch(batchId);
					ADMS5Dao.cleanTmpData(batchId);
					XxlJobLogger.log("无纸化批次 " + batchId + "删除成功" );
					return "FALSE|更新无纸化业务表处理状态异常";
				}
				XxlJobLogger.log("批次" + batchId + "输出完成" );
			}
		} catch (SQLException e) {
			XxlJobLogger.log(e.toString());
			return "FALSE|输出批次到后督失败";
		}		
		return "SUCCESS|输出批次到后督成功";
	}
}
