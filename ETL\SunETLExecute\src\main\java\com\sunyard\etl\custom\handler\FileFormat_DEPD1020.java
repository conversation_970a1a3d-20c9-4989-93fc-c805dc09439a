package com.sunyard.etl.custom.handler;

import java.io.BufferedReader;
import java.io.File;
import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.InputStreamReader;
import java.io.OutputStreamWriter;
import java.io.PrintWriter;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.List;

import com.sunyard.etl.system.common.Constants;
import org.springframework.stereotype.Service;

import com.sunyard.etl.system.dao.DataDateDAO;
import com.sunyard.etl.system.dao.impl.DataDateDAOImpl;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.enums.JobStartEnum;
import com.xxl.job.core.handler.IJobHandler;
import com.xxl.job.core.handler.annotation.JobHandler;
import com.xxl.job.core.log.XxlJobLogger;

@JobHandler(value = "FileFormat_DEPD1020", name = "DEPD1020文件格式化")
@Service
public class FileFormat_DEPD1020 extends IJobHandler {

	private static final long serialVersionUID = 1L;

	private static DataDateDAO dateDao = new DataDateDAOImpl();

	@Override
	public ReturnT<String> execute(String jobId, String... arg1) throws Exception {
		XxlJobLogger.log("开始DEPD1020文件格式化...");
		String jobDate = dateDao.getDataDate();
		if (null != arg1[0]) {
			String dirPath = arg1[0].toString().replace("@", jobDate);
			File dir = new File(dirPath);
			if (!dir.isDirectory()) {
				XxlJobLogger.log("INFO: 资源不足，目录不存在：" + dir, jobId + "");
				return new ReturnT<String>(ReturnT.FAIL_CODE, JobStartEnum.TASK_STATE_NO_RESOURCE.getCode(),
						"文件目录" + dir.getPath() + "不存在");
			}
			File preFile = new File(dir, "DEPD1020_"+jobDate+".txt");
			File file = new File(dir, "DEPD1020_"+jobDate+"_proc.txt");
			if (file.exists()) {
				XxlJobLogger.log("INFO: 文件已存在，先删除后再转换：" + file.getPath(), jobId + "");
				file.delete();
			}
			PrintWriter pw;
			pw = new PrintWriter(new OutputStreamWriter(new FileOutputStream(file), "GBK"));
			if (preFile.length()==0) {
				pw.print("|||");
				pw.flush();
				pw.close();
				XxlJobLogger.log("INFO: 源文件不存在，生成转换文件：" + preFile.getPath() + " >> " + file.getPath(), jobId + "");
				return ReturnT.SUCCESS;
			}
			BufferedReader br = new BufferedReader(new InputStreamReader(new FileInputStream(preFile), "GBK"));
			
			String line = null;
			String siteNo = "";
			String siteName = "";
			String occurDate = "";
			String slFlag = "";
			int index = -1;
			String tradeType="";
			String tradeCode ="";
			String gsType="";
			String acountNo="";
			String cehao="";
			String productType="";
			String bgsVouchType="";
			String newVouchType="";
			String gsDate="";
			String khSiteNo="";
			String dealWay="";
			String tellerNo="";
			String authTellerNo="";
			String remark="";
			
			String btLine1 = "";
			String[] strArr1 = null;
			List list1 = new ArrayList();
			String btLine2 = "";
			String[] strArr2 = null;
			List list2 = new ArrayList();
			try {
				while ((line = br.readLine()) != null) {
					if(line.contains("1              ") && line.contains("-DEPD1020")){
						siteNo = line.substring(line.indexOf("( ")+"( ".length(),line.indexOf("-DEPD1020"));
						index = -1;
						continue;
					} 
					if(line.contains("机构 :") && line.contains("日期 :")){
						siteName = line.substring(line.indexOf("机构 :  ")+"机构 :  ".length(),line.indexOf("日期 :")).trim().replace("　", "");
						occurDate = line.substring(line.indexOf("日期 : ")+"日期 : ".length(),line.indexOf("日期 : ")+"日期 : ".length()+10).trim();
						occurDate = new SimpleDateFormat("yyyyMMdd").format(new SimpleDateFormat("yyyy/MM/dd").parse(occurDate));
						index = -1;
						continue;
					}
					if(line.contains("受理标识 :")){
						slFlag = line.substring(line.indexOf("受理标识 :  ")+"受理标识 :  ".length()).trim();
						index = -1;
						continue;
					}
					if(line.contains("交易类型") && line.contains("交易代码") && line.contains("产品类别") ){
						btLine1 = line;
						strArr1 = new String[]{
								btLine1.substring(0,btLine1.indexOf("交易类型 ")),	
								btLine1.substring(btLine1.indexOf("交易类型 "),btLine1.indexOf("交易代码 ")),	
								btLine1.substring(btLine1.indexOf("交易代码 "),btLine1.indexOf("挂失类型 ")),	
								btLine1.substring(btLine1.indexOf("挂失类型 "),btLine1.indexOf("账号")),	
								btLine1.substring(btLine1.indexOf("账号 "),btLine1.indexOf("册号")),	
								btLine1.substring(btLine1.indexOf("册号 "),btLine1.indexOf("产品类别")),	
								btLine1.substring(btLine1.indexOf("产品类别 "),btLine1.indexOf("被挂失凭证号码")),	
								btLine1.substring(btLine1.indexOf("被挂失凭证号码"),btLine1.indexOf("新凭证号")),	
								btLine1.substring(btLine1.indexOf("新凭证号"))};
						int idx = 0;
						list1 = new ArrayList();
						for(String str:strArr1){
							list1.add(new int[]{idx, str.getBytes("GBK").length});
							idx += str.getBytes("GBK").length;
						}
						index = -1;
						continue;
					}
					if(line.contains("挂失日期") && line.contains("解挂处理方式") && line.contains("授权柜员")){
						btLine2 = line;
						strArr2 = new String[]{
								btLine2.substring(0,btLine2.indexOf("挂失日期 ")),	
								btLine2.substring(btLine2.indexOf("挂失日期 "),btLine2.indexOf("开户网点 ")),	
								btLine2.substring(btLine2.indexOf("开户网点 "),btLine2.indexOf("解挂处理方式 ")),	
								btLine2.substring(btLine2.indexOf("解挂处理方式 "),btLine2.indexOf("交易柜员 ")),	
								btLine2.substring(btLine2.indexOf("交易柜员 "),btLine2.indexOf("授权柜员 ")),	
								btLine2.substring(btLine2.indexOf("授权柜员 "),btLine2.indexOf("备注")),
								btLine2.substring(btLine2.indexOf("备注"))};
						int idx = 0;
						list2 = new ArrayList();
						for(String str:strArr2){
							list2.add(new int[]{idx, str.getBytes("GBK").length});
							idx += str.getBytes("GBK").length;
						}
						index = 0;
						continue;
					}
					if("".equals(line.trim()) ||line.contains("==========================")||"挂失／解挂交易清单".equals(line.trim())){
						index = -1;
						continue;
					}
					if(line.contains("交易类型")&&line.contains("挂失笔数")&&line.contains("解挂笔数")){
						index = 999;
						continue;
					}
					if(index == 999){
						index = -1;
						continue;
					}
					if(index == 0){
						int lineLength = line.getBytes().length;
						int btLength = btLine1.getBytes().length;
						if(lineLength<btLength){
							line = String.format("%-"+btLength+"s", line);
						}
						byte[] arr = line.getBytes("GBK");
						tradeType = new String(arr,((int[])list1.get(1))[0],((int[])list1.get(1))[1],"GBK").trim();//机构号
						tradeCode = new String(arr,((int[])list1.get(2))[0],((int[])list1.get(2))[1],"GBK").trim();//机构名称
						gsType = new String(arr,((int[])list1.get(3))[0],((int[])list1.get(3))[1],"GBK").trim();//卡号
						acountNo = new String(arr,((int[])list1.get(4))[0],((int[])list1.get(4))[1],"GBK").trim();//卡号
						cehao = new String(arr,((int[])list1.get(5))[0],((int[])list1.get(5))[1],"GBK").trim();//卡号
						productType = new String(arr,((int[])list1.get(6))[0],((int[])list1.get(6))[1],"GBK").trim();//卡号
						bgsVouchType = new String(arr,((int[])list1.get(7))[0],((int[])list1.get(7))[1],"GBK").trim();//卡号
						newVouchType = new String(arr,((int[])list1.get(8))[0],line.getBytes("GBK").length-((int[])list1.get(8))[0],"GBK").trim();//户名
						index = 1;
						continue;
					}
					if(index == 1){
						int lineLength = line.getBytes().length;
						int btLength = btLine2.getBytes().length;
						if(lineLength<btLength){
							line = String.format("%-"+btLength+"s", line);
						}
						byte[] arr = line.getBytes("GBK");
						gsDate = new String(arr,((int[])list2.get(1))[0],((int[])list2.get(1))[1],"GBK");//机构号
						if(gsDate.trim().contentEquals("0000/00/00")){
							gsDate = "        ";
						}else{
							gsDate = new SimpleDateFormat("yyyyMMdd").format(new SimpleDateFormat("yyyy/MM/dd").parse(gsDate));
						}
						khSiteNo = new String(arr,((int[])list2.get(2))[0],((int[])list2.get(2))[1],"GBK").trim();//机构名称
						dealWay = new String(arr,((int[])list2.get(3))[0],((int[])list2.get(3))[1],"GBK").trim();//卡号
						tellerNo = new String(arr,((int[])list2.get(4))[0],((int[])list2.get(4))[1],"GBK").trim();//机构名称
						authTellerNo = new String(arr,((int[])list2.get(5))[0],((int[])list2.get(5))[1],"GBK").trim();//卡号
						remark = new String(arr,((int[])list2.get(6))[0],line.getBytes("GBK").length-((int[])list2.get(6))[0],"GBK").trim();//户名
						
						String newLine = siteNo+"|"+siteName+"|"+occurDate+"|"
								+slFlag+"|"	//交易柜员号
								+tradeType+"|"	//交易流水号
								+tradeCode+"|"	//交易时间
								+gsType+"|"	//交易代码
								+acountNo+"|"	//授权柜员号
								+cehao+"|"	//授权柜员网点号
								+productType+"|"	//授权原因
								+bgsVouchType+"|"	//授权原因
								+newVouchType+"|"	//授权原因
								+gsDate+"|"	//授权原因
								+khSiteNo+"|"	//授权原因
								+dealWay+"|"	//授权原因
								+tellerNo+"|"	//授权原因
								+authTellerNo+"|"	//授权原因
								+remark;	//授权原因
						pw.print(newLine+System.getProperty("line.separator"));
						index = 0;
						continue;
					}
				}
			} catch (IOException e) {
				e.printStackTrace();
			}finally{
				pw.flush();
				pw.close();
			}
			XxlJobLogger.log("INFO: 文件转换成功：" + file.getPath(), jobId + "");
			return ReturnT.SUCCESS;
		}
		return ReturnT.FAIL;
	}

	public static void main(String[] args) throws ParseException {
		FileFormat_DEPD1020 ff = new FileFormat_DEPD1020();
		try {
			ff.execute("9", "C:\\@");
		} catch (Exception e) {
			// TODO Auto-generated catch block
			e.printStackTrace();
		}
	}
}
