package com.sunyard.etl.data.impl;

import com.sunyard.etl.system.common.Constants;
import com.sunyard.etl.system.dao.*;
import com.sunyard.etl.system.dao.impl.*;
import com.sunyard.etl.system.model.SmTableDef;
import com.sunyard.etl.system.model.data.BpTmpbatch;
import com.sunyard.etl.system.model.data.FlCheckoff;
import com.sunyard.etl.system.model.data.SmOrganData;
import com.sunyard.etl.system.model.data.SmTableField;
import com.xxl.job.core.log.XxlJobLogger;

import java.util.List;

/**
 * 归档实现类
 * <AUTHOR>
 *
 */
public class DaService extends Thread{
    private FlCheckoff flCheckoff;


    public DaService(FlCheckoff flCheckoff) {
        super();
        this.flCheckoff = flCheckoff;
    }


    private FlCheckoffDao flCheckoffDao = new FlCheckoffDaoImpl();
    private SmOrganDataDao smOrganDataDao = new SmOrganDataDaoImpl();
    private SmTableDefDao smTableDefDao = new SmTableDefDaoImpl();
    private BpTmpBatchDao bpTmpBatchDao = new BpTmpBatchDaoImpl();
    private SmTableFieldDao smTableFieldDao = new SmTableFieldDaoImpl();


    @Override
    public void run() {

        XxlJobLogger.log("业务日期：" + flCheckoff.getOccurDate() + "网点："
                + flCheckoff.getSiteNo() + "柜员：" + flCheckoff.getOperatorNo()
                + "开始归档！");



        //相关数据枷锁，保证唯一性
        if(flCheckoffDao.flcheckOnLock(flCheckoff.getOccurDate(),flCheckoff.getSiteNo(),flCheckoff.getOperatorNo()) <= 0){
            XxlJobLogger.log("业务日期：" + flCheckoff.getOccurDate() + "网点："
                    + flCheckoff.getSiteNo() + "柜员：" + flCheckoff.getOperatorNo()+"正在被处理,直接进入下一笔任务");
            return;
        }

        // 批次表数据不归档
        // String hisBatchTbName = "";
        String hisBatchDataTbName = "";
        String hisFlowTbName = "";
        SmOrganData smOrganData = smOrganDataDao
                .getOrganDataByOrganNoDao(flCheckoff.getSiteNo());
        if (null == smOrganData) {
            XxlJobLogger.log("机构数据表未定义，请检查！");
            return;
        }
        SmTableDef batchTableDef = smTableDefDao
                .getTableDefByTableIdDao(smOrganData.getTmpbatchTbId());
        SmTableDef batchDataTableDef = smTableDefDao
                .getTableDefByTableIdDao(smOrganData.getTmpdataTbId());
        SmTableDef flowTableDef = smTableDefDao
                .getTableDefByTableIdDao(smOrganData.getFlowTbId());
        if (null == batchTableDef || null == batchDataTableDef
                || null == flowTableDef) {
            XxlJobLogger.log("未查询到机构对应相关表，请检查表定义表！");
            return;
        }

        //取消多表归档
        hisBatchDataTbName = batchDataTableDef.getTableName()
                + Constants.HIS_TABLE_SUFFIX;
        hisFlowTbName = flowTableDef.getTableName()
                + Constants.HIS_TABLE_SUFFIX;

        //更改归档逻辑 modify by Juing 2013/6/20
        //判断批次表中是否有未归档并且是有效的批次，否则修改扎帐表状态
        boolean hasDaBatchFlag = bpTmpBatchDao.hasDaBatch(batchTableDef.getTableName(), flCheckoff, Constants.PROGRESS_FLAG_INDEX_UPDATE);
        if(hasDaBatchFlag){
            //获取需要归档的批次
            List<BpTmpbatch> archveBatchs = bpTmpBatchDao
                    .getBatchByCheckOffDataDao(batchTableDef.getTableName(),
                            flCheckoff);
            if(null != archveBatchs && archveBatchs.size()>0){//进行归档
                XxlJobLogger.log("archveBatchs.size:"+archveBatchs.size());
                String batchIds = "";
                for (int i = 0; i < archveBatchs.size(); i++) {
                    batchIds += ",'" + archveBatchs.get(i).getBatchId() + "'";
                }
                if(!"".equals(batchIds)){
                    batchIds = batchIds.substring(1);
                }
                //对数据表归档
                boolean batchDataArchveFlag = dataArchve(batchDataTableDef,
                        hisBatchDataTbName, archveBatchs);
                if(batchDataArchveFlag){
                    XxlJobLogger.log("数据表" + hisBatchDataTbName + "归档成功！");
                    //查询当前归档的是否是凭证
                    boolean isVouh = bpTmpBatchDao.isVouhDao(batchTableDef.getTableName(), batchIds);
                    //当一个柜员有多个凭证批次时，需增加一个判断，是否已无凭证类型的批次未归档
                    boolean hasVouh = bpTmpBatchDao.hasVouhDao(batchTableDef.getTableName(), flCheckoff);
                    boolean flowArchveFlag = true;
                    if(isVouh && !hasVouh){//如果当前对凭证进行归档，并且已无未走完流程的凭证批次，则对流水表归档
                        //对流水表归档
                        flowArchveFlag = dataArchve(flowTableDef, hisFlowTbName, null);
                        if(flowArchveFlag){
                            XxlJobLogger.log("流水表" + hisFlowTbName + "归档成功！");
                        }else{
                            //如果流水表更新失败，需要回滚
                            XxlJobLogger.log("流水表" + hisFlowTbName + "归档失败，进行数据回滚！");
                            bpTmpBatchDao.daDataAndFlowRollBackDao(batchTableDef.getTableName(),
                                    hisBatchDataTbName, hisFlowTbName, flCheckoff, batchIds);
                        }
                    }
                    if(flowArchveFlag){
                        //对影像平台数据进行归档更新
//						boolean indexUpdateFlag = indexUpdate(batchTableDef,
//								batchDataTableDef, flowTableDef, batchIds);
                        boolean indexUpdateFlag = true;
                        if (indexUpdateFlag) {
                            XxlJobLogger.log("影像平台归档成功！");
                            //再次判断是否还存在未归档并且不在当前归档范围内的，却需要归档的有效批次
                            hasDaBatchFlag = bpTmpBatchDao.hasDaBatch(batchTableDef.getTableName(), flCheckoff, Constants.PROGRESS_FLAG_98);
                            //更新批次、扎帐表
                            boolean updateFlag = bpTmpBatchDao.daSucceUpdateDao(batchTableDef.getTableName(),
                                    flCheckoff, batchIds, hasDaBatchFlag);
                            if(updateFlag){
                                XxlJobLogger.log("批次/扎帐信息更新成功，归档完成！");
                            }else{
                                XxlJobLogger.log("批次/扎帐信息更新失败，归档失败，进行数据回滚！");
                                if(isVouh && !hasVouh){//如果对流水表进行归档，则需一起删除流水表数据
                                    bpTmpBatchDao.daDataAndFlowRollBackDao(batchTableDef.getTableName(),
                                            hisBatchDataTbName, hisFlowTbName, flCheckoff, batchIds);
                                }else{//否则只删除影像数据表数据
                                    bpTmpBatchDao.daDataTbRollBackDao(hisBatchDataTbName, batchIds);
                                }
                            }
                        } else {
                            XxlJobLogger.log("影像平台归档失败，进行数据回滚！");
                            if(isVouh && !hasVouh){//如果对流水表进行归档，则需一起删除流水表数据
                                bpTmpBatchDao.daDataAndFlowRollBackDao(batchTableDef.getTableName(),
                                        hisBatchDataTbName, hisFlowTbName, flCheckoff, batchIds);
                            }else{//否则只删除影像数据表数据
                                bpTmpBatchDao.daDataTbRollBackDao(hisBatchDataTbName, batchIds);
                            }
                        }
                    }
                }else{
                    XxlJobLogger.log("数据表" + hisBatchDataTbName + "归档失败！进行数据回滚");
                    bpTmpBatchDao.daDataTbRollBackDao(hisBatchDataTbName, batchIds);
                }
            }
        }else{//更新扎帐表状态
            flCheckoffDao.daSucceUpdateCheckoffDao(flCheckoff);
        }
        flCheckoffDao.flcheckUnLock(flCheckoff.getOccurDate(),flCheckoff.getSiteNo(),flCheckoff.getOperatorNo());

    }
    /**
     * @Title: dataArchve
     * @Description: 数据归档
     * @param batchDataTableDef
     * @param hisBatchDataTbName
     * @param archveBatchs
     * @return boolean
     */
    private boolean dataArchve(SmTableDef batchDataTableDef, String hisBatchDataTbName, List<BpTmpbatch> archveBatchs) {

        List<SmTableField> smTableFields = null;
        String insertSql = "";
        boolean executeFlag = true;
		/* 影像表数据迁移 */
        smTableFields = smTableFieldDao.getTableFieldByTableIdDao(batchDataTableDef
                .getTableId());
        if (null == smTableFields || smTableFields.size() == 0) {
            XxlJobLogger.log("请检查表字段定义，表ID为：" + batchDataTableDef.getTableId());
            return false;
        }
        insertSql = smTableDefDao.insertTableSql(batchDataTableDef, flCheckoff,
                smTableFields, archveBatchs);
        if (null != insertSql) {
            XxlJobLogger.log("开始表数据迁移：" + insertSql);
            executeFlag = smTableDefDao.executeBySqlDao(insertSql);
        }
        if (!executeFlag) {
            XxlJobLogger.log("表数据迁移失败！");
            return false;
        }
        return true;

    }


}
