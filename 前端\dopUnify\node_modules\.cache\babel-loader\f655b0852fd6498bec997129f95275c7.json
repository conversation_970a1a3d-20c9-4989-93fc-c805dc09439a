{"remainingRequest": "E:\\华南区项目\\中国银行\\开发区\\科技公司运营平台\\前端\\dopUnify\\node_modules\\thread-loader\\dist\\cjs.js!E:\\华南区项目\\中国银行\\开发区\\科技公司运营平台\\前端\\dopUnify\\node_modules\\babel-loader\\lib\\index.js!E:\\华南区项目\\中国银行\\开发区\\科技公司运营平台\\前端\\dopUnify\\node_modules\\qiankun\\es\\apis.js", "dependencies": [{"path": "E:\\华南区项目\\中国银行\\开发区\\科技公司运营平台\\前端\\dopUnify\\node_modules\\qiankun\\es\\apis.js", "mtime": 1667130453000}, {"path": "E:\\华南区项目\\中国银行\\开发区\\科技公司运营平台\\前端\\dopUnify\\babel.config.js", "mtime": 1721631954662}, {"path": "E:\\华南区项目\\中国银行\\开发区\\科技公司运营平台\\前端\\dopUnify\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "E:\\华南区项目\\中国银行\\开发区\\科技公司运营平台\\前端\\dopUnify\\node_modules\\thread-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "E:\\华南区项目\\中国银行\\开发区\\科技公司运营平台\\前端\\dopUnify\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, null]}