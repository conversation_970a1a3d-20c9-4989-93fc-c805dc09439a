<%@ page language="java" contentType="text/html; charset=GBK"%>
<%@ include file="/common/taglibs.jsp"%>
<!DOCTYPE html PUBLIC "$//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1$transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content$Type" content="text/html; charset=gbk" />
<title>风险预警数据表</title>
	<link href="../css/style.css" rel="stylesheet" type="text/css" />
    <script type="text/javascript" src="../js/plugin/jquery/jquery-1.7.2.min.js"></script>
	<script type="text/javascript" src="../js/plugin/jquery/jquery-ui.js"></script>
	<script type="text/javascript" src="../js/plugin/jquery/jquery.layout.js"></script>
	<script type="text/javascript" src="../js/plugin/layout/sub_layout_ie6.js"></script>
	<script type="text/javascript" src="../js/plugin/tab/tab.js"></script><!--选项卡切换-->
    <script type="text/javascript" src="../js/plugin/table/choose_color_table.js"></script>
    <script type="text/javascript" src="../js/plugin/main/loading.js"></script>

	<script type="text/javascript"src="../js/plugin/My97DatePicker/WdatePicker.js"></script>
    <script type="text/javascript" src="../js/action/arms/dataStatistics.js"></script>

    <script type="text/javascript" src="../js/plugin/validate/validate.js"></script>
    <script type="text/javascript" src="../js/plugin/validate/jquery.validate.js"></script>

    <!--树形结构-->
    <script language="javascript" type="text/javascript" src="../js/plugin/tree/jquery.ztree.core-3.5.js"></script>
    <script language="javascript" type="text/javascript" src="../js/plugin/tree/jquery.ztree.excheck-3.5.js"></script>
    <script language="javascript" type="text/javascript" src="../js/plugin/tree/jquery.ztree.exedit-3.5.js"></script>
    <script type="text/javascript" src="../js/action/arms/organTree.js"></script>

</head>
<body>
<form action="" id="form1" name="form1" method="post">

<!--content--begin-->
<div class="ui-layout-center con_left cont_none">
     <!--查询首页--begin-->
     <div class="ui-layout-content tab_list tab_content" id="tab1">
           <!--north--begin-->
            <div class="ui-layout-north sub_north" >
                <div class="ui-widget-header arms_title">风险预警一览表</div>
                <input type="hidden"  name="isFirstIn"  value="0"/>
                <div class="ui-layout-content">
                      <table class="table_t">
                          <tr>
                            <th>预警模型</th>
                            <td><select class="select" id="modelId"
                                title="选择重点监督模型" name="filter$EQUAL$MODEL_ID$int"
                                style="width:250px">
                                    <option value="">全部</option>
                                    <c:forEach items="${requestScope.dataStatistics}"
                                        var='data' varStatus='i'>
                                        <!-- 不是关联模型 -->
                                        <c:if test="${data.modelInfo.isRelateModel eq 0}">
                                            <option value="${data.modelInfo.modelId}"
                                                <c:forEach items="${requestScope.filters}"
                                    var="filter" varStatus="j">
                                    <!-- 只显示自己的模型-->
                                    <c:if test="${filter.propertyName eq 'MODEL_ID' and filter.value eq data.modelInfo.modelId}">selected</c:if>
                                    </c:forEach>>
                                                ${data.modelInfo.modelName}</option>
                                        </c:if>
                                    </c:forEach>
                            </select>
                            </td>
                            <th>交易机构</th>
                            <td><input type="text" class="input" id="site_no"
                                name="filter$LIKE$SITE_NO"
                                title="可输入多个机构，如：01001,01002..."
                                <c:forEach items="${requestScope.filters}"
                                var="filter" varStatus="j">
                                <c:if test="${filter.propertyName eq 'SITE_NO'}">value="${filter.value}"</c:if>
                                </c:forEach> />
                                <input type="button" value="选择"
                                onclick="javascript:treeOpen();" />
                            </td>
                        </tr>
                        <tr>
                             <th>交易开始日期</th>
                            <td><input type="text" class="input"
                                title="交易开始日期" id="filter$GE$OCCUR_DATE"
                                name="filter$GE$OCCUR_DATE"
                                onclick="WdatePicker({dateFmt:'yyyy-MM-dd',maxDate:'%y-%M-{%d}'});"
                                <c:forEach items="${requestScope.filters}"
                                var="filter" varStatus="j">
                                <c:if test="${filter.propertyName eq 'OCCUR_DATE' and filter.matchType eq 'GE'}">value="${filter.value}"</c:if>
                                </c:forEach> />

                                <font color="red"><span style="position:relative; bottom:2px;"> *</span> </font>
                            </td>
                            <th>交易结束日期</th>
                            <td><input type="text" class="input"
                                title="交易结束日期" id="filter$LE$OCCUR_DATE"
                                name="filter$LE$OCCUR_DATE"
                                onclick="WdatePicker({dateFmt:'yyyy-MM-dd',maxDate:'%y-%M-{%d}'});"
                                <c:forEach items="${requestScope.filters}"
                                var="filter" varStatus="j">
                                <c:if test="${filter.propertyName eq 'OCCUR_DATE' and filter.matchType eq 'LE'}">value="${filter.value}"</c:if>
                                </c:forEach> />

                                <font color="red"><span
                                    style="position:relative; bottom:2px;"> *</span> </font>
                            </td>

                        </tr>
                      </table>
                </div>
                <div class="ui-widget-content footer">
                    <div class="button_bor">
                       <input type='submit' value='查询' class="button_t" id="searchBt"/>
                       <c:if test="${requestScope.dataStatistics[0].modelInfo.modelType eq '0' }">
                       <input type='button' value='批量通过' class="button_t" id="passByOrg" onclick="passAllByModelIdAndOrg()"/>
                       </c:if>
                       <input type="button" value="获取随机优先任务" onclick="getRandomTask()"/>
                    </div>
                </div>
            </div>
            <!--north--end-->
            <!--center--begin-->
            <div class="ui-layout-center sub_center">
                <div class="ui-layout-content ">
                   <table class="data_table">
                      <thead>
                            <tr>
                                <th>序号</th>
                                <th>风险预警模型名称</th>
                                <th>未处理(已退回)</th>
                                <th>已处理</th>
                                <th>通过</th>
                                <th>下发差错</th>
                                <th>总计</th>
                            </tr>
                        </thead>
                        <tbody>
                            <c:forEach items="${requestScope.dataStatistics}" var="dataStatistic" varStatus="i">
                                <c:if test="${dataStatistic.totalNumber > 0}">
                                <tr>
                                    <td>${i.index + 1 }</td>
                                    <td style="text-align:left">${dataStatistic.modelInfo.modelName}</td>
                                    <td><input type="button" value="获取指定模型任务" onclick="getTask(${dataStatistic.modelInfo.modelId})"/></td>
                                    <td class="count_link">
                                    <c:if test="${dataStatistic.notDealed > 0 }">
                                    <a
                                        href="javascript:void(0);"
                                        onclick="exhibitData(${dataStatistic.modelInfo.modelId},${dataStatistic.modelInfo.relatingModelId},'0');return false">${dataStatistic.notDealed}(${dataStatistic.backCount})</a>
                                    </c:if>
                                    <c:if test="${dataStatistic.notDealed <= 0 }">0(${dataStatistic.backCount})</c:if>
                                    </td>
                                    
                                    <td class="count_link"><a href="###"
                                        onclick="exhibitData(${dataStatistic.modelInfo.modelId},${dataStatistic.modelInfo.relatingModelId},'1|2');">${dataStatistic.dealed}</a>
                                    </td>
                                        <td class="count_link">
                                    <a href="###" onclick="exhibitData(${dataStatistic.modelInfo.modelId},${dataStatistic.modelInfo.relatingModelId},'1');">
                                    ${dataStatistic.passCount}
                                    </a>
                                        </td>
                                        <td class="count_link"><a href="###"
                                        onclick="exhibitData(${dataStatistic.modelInfo.modelId},${dataStatistic.modelInfo.relatingModelId},'2');">
                                        ${dataStatistic.slipCount}</a>
                                        </td>
                                    <td class="count_link">
                                        ${dataStatistic.totalNumber}
                                    </td>
                                </tr>
                                </c:if>
                            </c:forEach>
                        </tbody>
                   </table>
                </div>

            </div>
            <!--center--end-->
     </div>
     <!--查询首页--end-->

</div>
<!--content--end-->
<!-- tree begin -->
    <div id="tree_organ" title="机构选择">
        <div class="ui-layout-center priv_dia">
          <div class="ui-layout-content">
             <ul id="tt1" class="ztree"> </ul>
          </div>
          <div class="ui-widget-footer button_bor" >
            <a href="###"><input type="button" value="确定"  onclick="javascript:treeclose()" class="button_t" /></a>
            <a href="###"><input type="button" value="取消"  onclick="javascript:treeCancel()" class="button_t" /></a>
          </div>
        </div>
    </div>
    <iframe frameborder="0" id="obj_bg"></iframe><!--遮住控件并兼容IE6-->
<!-- tree end -->
</form>
	<!-- 用于查看机构汇总统计与明细 -->
	<form action="" id="form2" name="form2" method="post" target="main_iframe">
		<!-- 存放统计查询后的查询条件 -->
		<c:forEach items="${requestScope.filters}" var="filter" varStatus="j">
		<c:if test="${filter.propertyName eq 'ISHANDLE'}">
		<input type="hidden" id="h_ishandle" name="filter$EQUAL$ISHANDLE"
			value="${filter.value}"/></c:if>
		<c:if test="${filter.propertyName eq 'CREATE_DATE'}">
		<input type="hidden" id="h_create_date" name="filter$EQUAL$CREATE_DATE"
			value="${filter.value}"/></c:if>
		<c:if test="${filter.propertyName eq 'ALERT_DATE'}">
		<input type="hidden" id="h_alert_date" name="filter$EQUAL$ALERT_DATE"
			value="${filter.value}"/></c:if>
		<c:if test="${filter.propertyName eq 'ALERT_USER'}">
		<input type="hidden" id="h_alert_user" name="filter$EQUAL$ALERT_USER"
			value="${filter.value}"/></c:if>
		<c:if test="${filter.propertyName eq 'FORM_ID'}">
		<input type="hidden" id="h_form_id" name="filter$EQUAL$FORM_ID"
			value="${filter.value}"/></c:if>
		<c:if test="${filter.propertyName eq 'SITE_NO'}">
		<input type="hidden" id="h_site_no" name="filter$LIKE$SITE_NO"
			value="${filter.value}"/></c:if>
		<c:if test="${filter.propertyName eq 'OCCUR_DATE' and filter.matchType eq 'GE'}">
		<input type="hidden" id="h_startDate" name="filter$GE$OCCUR_DATE"
			value="${filter.value}"/></c:if>
		<c:if test="${filter.propertyName eq 'OCCUR_DATE' and filter.matchType eq 'LE'}">
		<input type="hidden" id="h_endDate" name="filter$LE$OCCUR_DATE"
			value="${filter.value}"/></c:if>
		</c:forEach>
		<!-- 选中的节点 -->
		<input type="hidden" id="h_modelId" name="filter$EQUAL$MODEL_ID$int"
			value="" /> <input type="hidden" id="selectedImageState"
			name="filter$EQUAL$IMAGE_STATE$int" value="" /> <input type="hidden"
			id="selectedSiteNo" name="SITE_NO" value="" /> <input type="hidden"
			id="selectedIshandle" name="ISHANDLE" value="" />
	</form>
    <!--loading--begin-->
        <div id="loading">
          <div class="loading_con"></div>
        </div>
         <div id="bg_zz"></div><!--ie6-->
        <iframe frameborder="0" id="obj_bg"></iframe><!--遮住控件并兼容IE6-->
    <!--loading--end-->
</body>
</html>
