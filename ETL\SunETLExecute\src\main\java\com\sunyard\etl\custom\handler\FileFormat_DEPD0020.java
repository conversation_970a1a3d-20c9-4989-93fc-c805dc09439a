package com.sunyard.etl.custom.handler;

import java.io.BufferedReader;
import java.io.File;
import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.InputStreamReader;
import java.io.OutputStreamWriter;
import java.io.PrintWriter;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.List;
import com.sunyard.etl.system.common.Constants;

import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import com.sunyard.etl.system.dao.DataDateDAO;
import com.sunyard.etl.system.dao.impl.DataDateDAOImpl;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.enums.JobStartEnum;
import com.xxl.job.core.handler.IJobHandler;
import com.xxl.job.core.handler.annotation.JobHandler;
import com.xxl.job.core.log.XxlJobLogger;

@JobHandler(value = "FileFormat_DEPD0020", name = "DEPD0020文件格式化")
@Service
public class FileFormat_DEPD0020 extends IJobHandler {

	private static final long serialVersionUID = 1L;

	private static DataDateDAO dateDao = new DataDateDAOImpl();

	@Override
	public ReturnT<String> execute(String jobId, String... arg1) throws Exception {
		XxlJobLogger.log("开始DEPD0020文件格式化...");
		String jobDate = dateDao.getDataDate();
		if (null != arg1[0]) {
			String dirPath = arg1[0].toString().replace("@", jobDate);
			File dir = new File(dirPath);
			if (!dir.exists()) {
				XxlJobLogger.log("INFO: 资源不足，目录不存在：" + dir, jobId + "");
				return new ReturnT<String>(ReturnT.FAIL_CODE, JobStartEnum.TASK_STATE_NO_RESOURCE.getCode(),
						"文件目录" + dir.getPath() + "不存在");
			}
			File preFile = new File(dir, "DEPD0020_"+jobDate+".txt");
			File newFile = new File(dir, "DEPD0020_"+jobDate+"_proc.txt");
			if (newFile.exists()) {
				XxlJobLogger.log("INFO: 文件已存在，删除后再转换：" + newFile.getPath(), jobId + "");
				newFile.delete();
			}
			PrintWriter pw;
			pw = new PrintWriter(new OutputStreamWriter(new FileOutputStream(newFile), "GBK"));
			if (preFile.length()==0) {
				pw.print("|||");
				pw.flush();
				pw.close();
				XxlJobLogger.log("INFO: 源文件不存在，生成转换文件：" + preFile.getPath() + " >> " + newFile.getPath(), jobId + "");
				return ReturnT.SUCCESS;
			}
			BufferedReader br = new BufferedReader(new InputStreamReader(new FileInputStream(preFile), "GBK"));
			
			String line = "";
			String siteNo = "";
			String siteName = "";
			String currencyType="";
			String occurDate = "";
			String btLine = "";
			String[] strArr = null;
			List list = new ArrayList();
			try {
				a:while ((line = br.readLine()) != null) {
					if(line.contains("1                 ") && line.contains("-DEPD0020")){
						siteNo = line.substring(line.indexOf("( ")+"( ".length(),line.indexOf("-DEPD0020"));
						continue;
					} 
					if(line.contains("机构号 :") && line.contains("日期 :")){
						siteNo = line.substring(line.indexOf("机构号 : ")+"机构号 : ".length(),line.indexOf("机构 :")).trim();
						siteName = line.substring(line.indexOf("机构 :  ")+"机构 :  ".length(),line.indexOf("日期 :")).trim();
						occurDate = line.substring(line.indexOf("日期 : ")+"日期 : ".length(),line.indexOf("日期 : ")+"日期 : ".length()+10).trim();
						occurDate = new SimpleDateFormat("yyyyMMdd").format(new SimpleDateFormat("yyyy/MM/dd").parse(occurDate));
						currencyType = line.substring(line.indexOf("货币 : ")+"货币 : ".length(),line.indexOf("页码 :")).trim();
						continue;
					}
					if(line.contains("开户机构") && line.contains("子账户类别") && line.contains("合同类型")){
						btLine = line;
						strArr = new String[]{
								btLine.substring(0,btLine.indexOf("开户机构 ")),	
								btLine.substring(btLine.indexOf("开户机构 "),btLine.indexOf("账号  ")),	
								btLine.substring(btLine.indexOf("账号  "),btLine.indexOf("子账户类别  ")),	
								btLine.substring(btLine.indexOf("子账户类别  "),btLine.indexOf("册号  ")),	
								btLine.substring(btLine.indexOf("册号  "),btLine.indexOf("序号 ")),	
								btLine.substring(btLine.indexOf("序号 "),btLine.indexOf("客户类型 ")),
								btLine.substring(btLine.indexOf("客户类型 "),btLine.indexOf("合同类型  ")),	
								btLine.substring(btLine.indexOf("合同类型  "),btLine.indexOf("产品类型 ")),	
								btLine.substring(btLine.indexOf("产品类型 "),btLine.indexOf("产品子类 ")),	
								btLine.substring(btLine.indexOf("产品子类 "),btLine.indexOf("产品名称   ")),	
								btLine.substring(btLine.indexOf("产品名称   "),btLine.indexOf("账户名称 ")),	
								//btLine.substring(btLine.indexOf("账户名称 "),btLine.indexOf("议价利率类型   ")),	
								//btLine.substring(btLine.indexOf("议价利率类型   "),btLine.indexOf("   议价利率值 ")),
								//btLine.substring(btLine.indexOf("   议价利率值 "),btLine.indexOf("交易码 ")),
								btLine.substring(btLine.indexOf("账户名称 "),btLine.indexOf("议价利率类型（旧）   ")),
								btLine.substring(btLine.indexOf("议价利率类型（旧）   "),btLine.indexOf("   议价利率值（旧） ")),
								btLine.substring(btLine.indexOf("   议价利率值（旧）"),btLine.indexOf("   议价利率类型（新） ")),
								btLine.substring(btLine.indexOf("   议价利率类型（新）"),btLine.indexOf("   议价利率值（新） ")),
								btLine.substring(btLine.indexOf("   议价利率值（新） "),btLine.indexOf("交易码 ")),
								btLine.substring(btLine.indexOf("交易码 "),btLine.indexOf("交易流水 ")),	
								btLine.substring(btLine.indexOf("交易流水 "),btLine.indexOf("交易时间 ")),	
								btLine.substring(btLine.indexOf("交易时间 "),btLine.indexOf("付息方式 ")),	
								btLine.substring(btLine.indexOf("付息方式 "),btLine.indexOf("转息账户 ")),	
								btLine.substring(btLine.indexOf("转息账户 "),btLine.indexOf("续存类型 ")),	
								btLine.substring(btLine.indexOf("续存类型 "),btLine.indexOf("经办柜员 ")),	
								btLine.substring(btLine.indexOf("经办柜员 "),btLine.indexOf("授权柜员")),	
								btLine.substring(btLine.indexOf("授权柜员"))};
						int index = 0;
						list = new ArrayList();
						for(String str:strArr){
							list.add(new int[]{index, str.getBytes("GBK").length});
							index += str.getBytes("GBK").length;
						}
						continue;
					}
					if("".equals(line.trim())||line.contains("================================================")||"中国银行账户层利率维护／变更交易清单（网点）".equals(line.trim())){
						continue;
					}
					int lineLength = line.getBytes().length;
					int btLength = btLine.getBytes().length;
					if(lineLength<btLength){
						line = String.format("%-"+btLength+"s", line);
					}
					StringBuffer sb = new StringBuffer(siteNo+"|"+siteName+"|"+occurDate+"|"+currencyType+"|");
					byte[] arr = line.getBytes("GBK");
					for(int i=1;i<list.size();i++){//这里等于0的时候是第一个字段前的空格位置，所以从1开始获取有内容的值，hwj20240528
						if(i<list.size()-1){
							int offset = ((int[])list.get(i))[0];
							int length = ((int[])list.get(i))[1];
							String tmp = new String(arr,offset,length,"GBK").trim();
							if (i==6) {
								if (StringUtils.isNotBlank(tmp) && tmp.indexOf("个人") >-1) {
											continue a ;
										}
							}
							
							
							sb.append(tmp).append("|");
						}else{
							int offset = ((int[])list.get(i))[0];
							String tmp = new String(arr,offset,7,"GBK");
							sb.append(tmp).append("|");
						}
						
					}
					pw.print(sb.toString()+System.getProperty("line.separator"));
					
				}
			} catch (IOException e) {
				e.printStackTrace();
			}finally{
				pw.flush();
				pw.close();
			}
			XxlJobLogger.log("INFO: 文件转换成功：" + newFile.getPath(), jobId + "");
			return ReturnT.SUCCESS;
		}
		return ReturnT.FAIL;
	}

	public static void main(String[] args) {
		FileFormat_DEPD0020 ff = new FileFormat_DEPD0020();
		try {
			ff.execute("9", "C:\\@");
		} catch (Exception e) {
			// TODO Auto-generated catch block
			e.printStackTrace();
		}
	}
}
