{"remainingRequest": "E:\\华南区项目\\中国银行\\开发区\\科技公司运营平台\\前端\\dopUnify\\node_modules\\babel-loader\\lib\\index.js!E:\\华南区项目\\中国银行\\开发区\\科技公司运营平台\\前端\\dopUnify\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!E:\\华南区项目\\中国银行\\开发区\\科技公司运营平台\\前端\\dopUnify\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\华南区项目\\中国银行\\开发区\\科技公司运营平台\\前端\\dopUnify\\src\\views\\home\\component\\important\\print\\index.vue?vue&type=script&lang=js&", "dependencies": [{"path": "E:\\华南区项目\\中国银行\\开发区\\科技公司运营平台\\前端\\dopUnify\\src\\views\\home\\component\\important\\print\\index.vue", "mtime": 1721631954662}, {"path": "E:\\华南区项目\\中国银行\\开发区\\科技公司运营平台\\前端\\dopUnify\\babel.config.js", "mtime": 1721631954662}, {"path": "E:\\华南区项目\\中国银行\\开发区\\科技公司运营平台\\前端\\dopUnify\\.babelrc", "mtime": 1721631954662}, {"path": "E:\\华南区项目\\中国银行\\开发区\\科技公司运营平台\\前端\\dopUnify\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "E:\\华南区项目\\中国银行\\开发区\\科技公司运营平台\\前端\\dopUnify\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "E:\\华南区项目\\中国银行\\开发区\\科技公司运营平台\\前端\\dopUnify\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "E:\\华南区项目\\中国银行\\开发区\\科技公司运营平台\\前端\\dopUnify\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1655715099000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KCmltcG9ydCB7IGRhdGVUaW1lRm9ybWF0LCBvcmdhbk5vRm9ybWF0IH0gZnJvbSAnQC9maWx0ZXJzJzsKLy8gaW1wb3J0IHN0b3JlIGZyb20gJ0Avc3RvcmUnCmV4cG9ydCBkZWZhdWx0IHsKICBuYW1lOiAnUHJpbnQnLAogIGZpbHRlcnM6IHsKICAgIG9yZ2FuTm9Gb3JtYXQ6IG9yZ2FuTm9Gb3JtYXQsCiAgICBkYXRlVGltZUZvcm1hdDogZGF0ZVRpbWVGb3JtYXQKICB9LAogIHByb3BzOiB7CiAgICBub3RpY2VDb25maWc6IHsKICAgICAgdHlwZTogT2JqZWN0LAogICAgICBkZWZhdWx0OiBmdW5jdGlvbiBfZGVmYXVsdCgpIHsKICAgICAgICByZXR1cm4gewogICAgICAgICAgbm90aWNlQ29uZmlnOiB7CiAgICAgICAgICAgIC8vIOa2iOaBr+WvueixoQogICAgICAgICAgICBpbWdTcmM6ICcnLAogICAgICAgICAgICAvLyDog4zmma/lm77niYfliqDovb0KICAgICAgICAgICAgdGl0bGU6ICfmoIfpopgnLAogICAgICAgICAgICAvLyDlhazlkYrmoIfpopgKICAgICAgICAgICAgaW5mbzogWyfmgLvooYzov5DokKXnrqHnkIbpg6gnLCAn6Zeo5oi36LaF57qn566h55CG5ZGYJywgJzIwMjItMDMtMjMgMTY6MTk6NDUnXSwKICAgICAgICAgICAgLy8g5Y+R5biD5py65p6E44CB5Y+R5biD6ICF5ZCN56ew44CB5Y+R5biD5pe26Ze0CiAgICAgICAgICAgIHJlYWROdW06ICcwJywKICAgICAgICAgICAgLy8g6ZiF6K+76YePCiAgICAgICAgICAgIGNvbnRlbnQ6ICfov5nmmK/kuIDmrrXmraPmlocnIC8vIOWFrOWRiuato+aWhwogICAgICAgICAgfQogICAgICAgIH07CiAgICAgIH0KICAgIH0KICB9LAogIGRhdGE6IGZ1bmN0aW9uIGRhdGEoKSB7CiAgICByZXR1cm4ge307CiAgfSwKICBiZWZvcmVNb3VudDogZnVuY3Rpb24gYmVmb3JlTW91bnQoKSB7fSwKICBiZWZvcmVEZXN0cm95OiBmdW5jdGlvbiBiZWZvcmVEZXN0cm95KCkge30sCiAgbW91bnRlZDogZnVuY3Rpb24gbW91bnRlZCgpIHt9LAogIG1ldGhvZHM6IHt9Cn07"}, {"version": 3, "mappings": ";;;;;;;;;;;;;;;;;;;AAmBA;AACA;AACA;EACAA;EACAC;IACAC;IACAC;EACA;EACAC;IACAC;MACAC;MACAC;QACA;UACAF;YACA;YACAG;YAAA;YACAC;YAAA;YACAC;YAAA;YACAC;YAAA;YACAC;UACA;QACA;MACA;IACA;EACA;EACAC;IACA,QACA;EACA;EACAC;EACAC;EACAC,6BACA;EACAC,UACA;AACA", "names": ["name", "filters", "organNoFormat", "dateTimeFormat", "props", "noticeConfig", "type", "default", "imgSrc", "title", "info", "readNum", "content", "data", "beforeMount", "<PERSON><PERSON><PERSON><PERSON>", "mounted", "methods"], "sourceRoot": "src/views/system/notice/release/component/print", "sources": ["index.vue"], "sourcesContent": ["<template>\r\n  <div class=\"notice\">\r\n    <div class=\"notice-top\">\r\n      <div class=\"notice-title\">{{ noticeConfig.title }}</div>\r\n      <div\r\n        v-for=\"(item, index) in noticeConfig.info\"\r\n        :key=\"index\"\r\n        class=\"notice-info\"\r\n      >\r\n        <span>{{ item.publish_organ | organNoFormat }}</span>\r\n        <span>{{ item.publish_username }}</span>\r\n        <span>{{ item.publish_time | dateTimeFormat }}</span>\r\n        <span>阅读量：{{ noticeConfig.readNum }}</span>\r\n      </div>\r\n    </div>\r\n    <div class=\"notice-content\" v-html=\"noticeConfig.content\" />\r\n  </div>\r\n</template>\r\n<script>\r\nimport { dateTimeFormat, organNoFormat } from '@/filters'\r\n// import store from '@/store'\r\nexport default {\r\n  name: 'Print',\r\n  filters: {\r\n    organNoFormat,\r\n    dateTimeFormat\r\n  },\r\n  props: {\r\n    noticeConfig: {\r\n      type: Object,\r\n      default: () => {\r\n        return {\r\n          noticeConfig: {\r\n            // 消息对象\r\n            imgSrc: '', // 背景图片加载\r\n            title: '标题', // 公告标题\r\n            info: ['总行运营管理部', '门户超级管理员', '2022-03-23 16:19:45'], // 发布机构、发布者名称、发布时间\r\n            readNum: '0', // 阅读量\r\n            content: '这是一段正文' // 公告正文\r\n          }\r\n        }\r\n      }\r\n    }\r\n  },\r\n  data() {\r\n    return {\r\n    }\r\n  },\r\n  beforeMount() {},\r\n  beforeDestroy() {},\r\n  mounted() {\r\n  },\r\n  methods: {\r\n  }\r\n}\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n@page {\r\n  margin-top: 0;\r\n  margin-bottom: 0\r\n}\r\n$color_border: #f2f2f2;\r\n$color: #606266;\r\n$font_title: 1.8rem;\r\n$font_info: 1.6rem;\r\n$font_content: 1.4rem;\r\n.notice {\r\n  height: 100%;\r\n  padding: 5rem 3rem 3rem 3rem;\r\n  font-size: $font_content;\r\n  color: $color;\r\n  .notice-top {\r\n    padding-bottom: 2rem;\r\n    border-bottom: 1px solid $color_border;\r\n    text-align: center;\r\n    .notice-title {\r\n      font-size: $font_title;\r\n      font-weight: bold;\r\n      padding: 1rem;\r\n    }\r\n    .notice-info {\r\n      font-size: $font_info;\r\n      opacity: 0.5;\r\n      ::v-deep {\r\n        span {\r\n          border-right: 2px solid #b4b5b7;\r\n          margin-right: 5px;\r\n          padding-right: 5px;\r\n          &:last-child {\r\n            border-right: none;\r\n          }\r\n        }\r\n      }\r\n    }\r\n  }\r\n  .notice-content {\r\n    padding: 2rem 1rem;\r\n    line-height: 3rem;\r\n    text-indent: 3rem;\r\n    text-align: left;\r\n  }\r\n}\r\n</style>\r\n"]}]}