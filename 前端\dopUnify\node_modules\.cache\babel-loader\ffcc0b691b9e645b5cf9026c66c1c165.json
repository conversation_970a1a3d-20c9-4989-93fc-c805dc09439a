{"remainingRequest": "D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\node_modules\\babel-loader\\lib\\index.js!D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\src\\layout\\components\\Navbar.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\src\\layout\\components\\Navbar.vue", "mtime": 1703583640629}, {"path": "D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\babel.config.js", "mtime": 1686019818359}, {"path": "D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\.babelrc", "mtime": 1705285078668}, {"path": "D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1655715099000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA4TA,SACAA,kBACAC,kBACAC,eACAC,qBACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA,IACAC,YAeAC,OAfAD;EACAE,aAcAD,OAdAC;EACAC,iBAaAF,OAbAE;EACAC,2BAYAH,OAZAG;EACAC,YAWAJ,OAXAI;EACAC,cAUAL,OAVAK;EACAC,cASAN,OATAM;EACAC,kBAQAP,OARAO;EACAC,gBAOAR,OAPAQ;EACAC,mBAMAT,OANAS;EACAC,aAKAV,OALAU;EACAC,sBAIAX,OAJAW;EACAC,oBAGAZ,OAHAY;EACAC,eAEAb,OAFAa;EACAC,cACAd,OADAc;AAEA;AACA;AACA;AACA;AACA;EACAC;IACAC;IACAC;IACAC;IACAC;IACAC;IACAC;IACAC;IACAC;IACA;EACA;EACAC;IACA;MACAC;MACAC;MAAA;MACAC;MAAA;MACAC;MACAC;MAAA;;MAEAC,YACA;MAAA;MACAC;MAAA;MACAC;MAAA;MACAC;MACAC;MAAA;MACAC;MAAA;MACAC;MACAC;MACAC;MACAC;MACAC;MAAA;MACAC;MACAC;MACAC,SACA;QAAAC;QAAAC;MAAA,GACA;QAAAD;QAAAC;MAAA,GACA;QAAAD;QAAAC;MAAA,GACA;QAAAD;QAAAC;MAAA,GACA;QAAAD;QAAAC;MAAA,GACA;QAAAD;QAAAC;MAAA,GACA;QAAAD;QAAAC;MAAA,EACA;MACAC;MACAC;MAAA;MACAC;MAAA;MACAC;QACA;QACAC;QACAC;UACA;UACAC;UACA;UACA;QACA;;QACAC;UACA;UACAC;UAAA;UACAC;UACAC;YACAC;YACAC;YACAC;YACAC;YACAC;UACA;QACA;MACA;MACAC;QACA;QACAZ;QACAC;UACA;UACAC;UACAW;QACA;;QACAV;UACA;UACAC;UAAA;UACAC;UACAC;YACAQ;UACA;QACA;MACA;MACAC;MAAA;MACAC;MAAA;MACAC;MAAA;MACAC;QACA;QACAlB;QACAmB;QACAjB;MACA;MACAkB;IACA;EACA;EACAC,4BACAC,sDACA;EACAC;IACA/C;MAAA;MACA;MACA;MACA;QACA;QACA;MACA;QACA;QACA;QACA;UACA;UACA;QACA;UACA;UACAgD;YACA;UACA;QACA;MACA;IACA;EACA;EACAC;IACA;EACA;EACAC;IAAA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;MACA;IACA;IACA;MACA;IACA;IACA;IACA;MACA;IACA;EACA;EACA;EACAC;IACA;IACA;IACA;IACAC;EACA;EACAC;IACA;AACA;IACAC;MAAA;MACA;QACAC;QACAC;UACAC;QACA;MACA;MACArE;QACA;QACA;MACA;IACA;IACA;AACA;IACAsE;MACA;IACA;IACA;AACA;IACAC;MAAA;MACAzF;QACA;UACA;QACA;MACA;IACA;IACA;AACA;IACA0F;MAAA;MAAA;QAAA;UAAA;YAAA;cAAA;gBAAA;gBAAA,OACA;cAAA;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IACA;IACA;AACA;IACAC;MACA;IACA;IACA;AACA;AACA;IACA1E;MAAA;MACA;QACAoE,gBACA;UACAO;QACA,EACA;QACAN;UACAO;UACAC;QACA;MACA;MACA7E;QACA;MACA;IACA;IACA;AACA;AACA;AACA;IACA8E;MACA;MACA;MACA;MACA;MACA;MACA;IACA;IACA;AACA;AACA;AACA;IACAC;MACA;IACA;IACA;AACA;IACAC;MACA;QACA;MACA;QACAhG;MACA;IACA;IACA;AACA;IACAiG;MAAA;MACA;MACA;QACA;MACA;IACA;IACA;AACA;IACAC;MAAA;MACA;MACA;QACA;MACA;IACA;IACA;AACA;IACAC;MACA;MACA;MACA;IACA;IACA;AACA;IACAC;MAAA;MACA;QACAhB;QACAQ;QACAS;QACAzC;QACA0C;QACAC;QACAC;QACAC;QACAC;QACAC;QACAC;MACA;MACAC;QACA;QACA,IACAC,YACAC,WADAD;QAEA;MACA;IACA;IACA;AACA;IACAE;MAAA;MACAjH;QACA;UACA;UACA;UACA;YACAqF;YACA6B;UACA;UACA1G;YACA;cACA;cACA;cACA,wBACA,8BACAwG,qBACA;cACA,wBACA,4BACAA,qBACA;cACAjH;YACA;UACA;QACA;MACA;IACA;IACA;AACA;IACAoH;MAAA;MACAnH;QACA;UACA;UACA;UACA;QACA;MACA;IACA;IACA;AACA;IACAoH;MAAA;MACA;QAAA/B;MAAA;MACA5E;QACA;QACA;QACA;UACA;UACA;YACA;UACA;UACA4G;QACA;QACA;QACAC;UACAC;QACA;QACA;QACAC;MACA;IACA;IACA;AACA;AACA;AACA;AACA;AACA;IACAC;MACA;MACA;QACA;MACA;MACA;IACA;IACA;AACA;IACAC;MAAA;MACA1H;QACA;UACA;YACAqF;UACA;UACA3E;YACA;YACAiH;cACAJ;YACA;YACA;YACAxH;UACA;QACA;MACA;IACA;IACA;AACA;IACA6H;MAAA;MACA5H;QACA;UACA;UACA;UACA;UACA;UACA;YACA;cACA6H;YACA;UACA;UACAA;UACA;YACAxC;YACAyC;YACAC;YAAA;YACAF;UACA;UACAlH;YACA;YAAA,2CACAqH;cAAA;YAAA;cAAA;gBAAA;gBACA;kBACAC;kBACAA;oBAAAC;kBAAA;gBACA;kBACAD;kBACAA;gBACA;cACA;YAAA;cAAA;YAAA;cAAA;YAAA;YACAA;YACA;YACAlI;UACA;QACA;MACA;IACA;IACAoI;MACA;MACA;MACA;MACA;MACA;QACA,wBACAC;UAAA;QAAA,IACA;QACAC;UACAC;UACAA;UACAA;UACAC;QACA;QACAF;UACA;UACA;YACAG;UACA;YACAC;UACA;QACA;QACAC;MACA;MACA;IACA;IACA;AACA;AACA;IACAC;MAAA;MACA;MACA;MACA;QACAtD;QACA;QACAxB;QACA4C;MACA;MACA;MACAtG;QACA;QACA;QACAyI;UACAC;YACA;cACAlG;YACA;UACA;UACAE;QACA;QACA;QACA;QACA;MACA;IACA;IACA;AACA;AACA;IACAiG;MACA;MACA;QAAAC;QAAAnH;MAAA;IACA;IACA;AACA;AACA;IACAoH;MACA;QAAAD;QAAAnH;MAAA;IACA;IACA;AACA;AACA;IACAqH;MAAA;MACA;QACA;QACA/I;QACA;MACA;MACA;QACAmF;QACA6B;QACArD;MACA;MACA9C,yBACAmI;QACA;QACA;UACAZ;UACAA;UACA;QACA;QACA;MACA,GACAa;QACAjJ;MACA;MACA;IACA;IACA;AACA;AACA;IACAkJ;MAAA;MACA;MACA;MACA;MACA;;MAEA;QACAC;MACA;MACA;MACA;MACA;QACA;UACAR;QACA;UACAA;QACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MAAA,4CACAA;QAAA;MAAA;QAAA;UAAA;UACA;YACAS;UACA;YACAA;UACA;QACA;QACA;QACA;QACA;QACA;MAAA;QAAA;MAAA;QAAA;MAAA;MAAA,4CACA;QAAA;MAAA;QAAA;UAAA;UACA;YACA;YACA;YACAC;YACA;UACA;YACA;cACAA;YACA;UACA;QACA;QACA;QACA;MAAA;QAAA;MAAA;QAAA;MAAA;MACA;MACA;QACAA;UACA;UACA;YACAC;cACAC;cACAC;cACAC;cACAC;cACAC;cACAC;YACA;UACA;QACA;MACA;MACA;MACA;MACA;MAAA,4CAEAjB;QAAA;MAAA;QAAA;UAAA;UACA;UAAA,4CACA;YAAA;UAAA;YAAA;cAAA;cACA;gBACArB;cACA;YACA;UAAA;YAAA;UAAA;YAAA;UAAA;UACA;YACAuC;UACA;QACA;MAAA;QAAA;MAAA;QAAA;MAAA;MAEA;QACA1E;QAAA;QACA;QACA2E;QAAA;QACAxC;MACA;MAEAnH;QACA;QACA;QACA;MACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;AACA;AACA;IACA4J;MACA;MACA;IACA;IACA;AACA;AACA;IACAC;MACA;MACA;MACA,uBACAC;IACA;IACAC;IACA;AACA;AACA;AACA;IACAC;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBACAC;kBACAjF;oBAAAxB;kBAAA;kBACAqD;gBACA;gBAAA;gBAAA,OACA5G;cAAA;gBAAA0G;gBACA;gBAAA,cACAA;gBACA;gBACA;gBACA;kBACAuD;kBACA;oBACAC;oBACAzB;kBACA;gBACA;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IACA;IACA;IACA0B;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA;gBAAA,OAOA;cAAA;gBACAC;gBACA;kBACAA;gBACA;gBAEA;kBACAA;gBACA;gBACA;gBACA;gBACAC;gBACA;kBACAC,wCACA;kBACA,gBACAC,+BACAC,SACA,gCACAF,oBACAA,gCACAA,wBACA,GACA,mBACAD;kBACA;kBACA;kBACA;gBACA;kBACA;gBACA;gBACA;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IACA;IACAI;MAAA;MACA/K;QACA;UACA;UACA;QACA;MACA;IACA;IACAgL;MACA;MACA;IACA;IACA;IACAC;MACA;QACA;MACA;QACA;MACA;IACA;IACA;IACAC;MACA;IACA;IACA;IACAC;MAAA;MACA;QACAC;QACA;QACAlE;QACAmE,uBACA,qDACA,4DACA;QACAC,uBACA,qDACA;MAEA;MACA/K;QACAR;QACA;QACA;MACA;IACA;IACA;IACAwL;MAAA;MACAvL;QACA;UACA;UACAoE;UACA;UACA;UACA;YACA;cACAoD;cACA;YACA;UACA;UACA;YACAnC;YACAxB;YACAO;YACAoD;UACA;;UACAxG,uBACAkI;YACAnJ;YACA;UACA,GACAoJ;YACAjJ;UACA;QACA;MACA;IACA;IACA;AACA;AACA;AACA;IACAsL;MAAA;MACA;MACA;;MAEA;MACA;MACA;QACA;QACA;UACAC;QACA;MACA;MACA;MACA;QACApG;QACA6B;QACAwE;QACAC;QACA9H;MACA;MACAhD;QACAd;MACA;IACA;IACA;AACA;AACA;IACA6L;MACA,YACArJ,kCACAA,iCACAA;MACA;MAEA;QACA;MACA;QACA;MACA;MACA;QACA;MACA;MACA;MACA;MACA;IACA;IACA;IACAsJ;MAAA;MACA;QACAxG,gBACA;UACAxB;QACA,EACA;QACAqD;MACA;MACApG;QACA;QACA;UACA,UACA+J,+BACAC,SACA,gCACAF,oBACAA,gCACAA,wBACA,GACA,mBACAA;UACA;UACA;QACA;UACA;QAAA;MAEA;IACA;IACAkB;MAAA;MACA;MACAC;MACAnL;QACA;QACA;;QAEA;QACA;UACA;UACA;YACA6K;UACA;QACA;QACA;QACA;UACApG;UACA6B;UACAwE;UACAC;UACA9H;QACA;QACAhD;UACAd;UACA;UACA;UACA;QACA;MACA;IACA;IACA;IACAiM;MACA;MACA;MACA;IACA;IACA;AACA;AACA;IACAC;MACA;MACA;MACA;MACA;MACA;QACA;QACA;QACA;MACA;QACA;QACA;QACA;MACA;IACA;EACA;AACA", "names": ["commonMsgSuccess", "commonMsgConfirm", "commonMsgInfo", "commonMsgWarn", "homeQuery", "Common", "homeModify", "userController", "changePasswordController", "organTree", "getAllRoles", "getAllUsers", "fieldController", "uploadUserImg", "uploadIconConfig", "showAvatar", "selectHomepageRoles", "syschroHomeToRole", "initSysLink", "getSysParam", "components", "ChangeColor", "<PERSON><PERSON>", "Screenfull", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SublicenseMan", "SublicenseLog", "Sublicense", "msgCard", "data", "menuDir", "msgNum", "isDot", "checked", "canEdit", "actiionUrl", "image", "imageUrl", "User", "uploadImg", "file", "dialogFormVisible", "dialogVisible2", "checkAll", "checkedItems", "menuList", "items", "isIndeterminate", "<PERSON><PERSON><PERSON>", "label", "name", "userInfo", "user_list", "role_list", "dialog3", "visible", "componentProps", "title", "form", "labelWidth", "config", "defaultForm", "user_no", "user_name", "j_userinfo_changepass_oldpass", "j_userinfo_changepass_newpass", "j_userinfo_changepass_confirmpass", "dialog4", "width", "roles", "isShow", "passwordClose", "sysLinkArr", "sysConfig", "srcUrl", "isShowName", "computed", "mapGetters", "watch", "timeout", "created", "mounted", "<PERSON><PERSON><PERSON><PERSON>", "clearTimeout", "methods", "userNameShow", "parameterList", "sysMap", "param_item", "toggleSideBar", "logout", "logoutSubmit", "changeColor", "is_lock", "currentPage", "pageNum", "sysLink", "sysDialogClose", "sublicense", "sublicenseMan", "sublicenseLog", "systemMsg", "msgTotalNum", "pageSize", "organ_no", "organ_level", "role_no", "msg_type", "classify", "msg_content", "deal_state", "queryMsg", "unReadNum", "res", "<PERSON><PERSON><PERSON><PERSON>", "oper_type", "ayncRole", "commitRole", "roleLevelData", "roleList", "role", "flag", "getLevelRole", "ayncUser", "userList", "ayncDict", "systemNo", "fieldTime", "operType", "systemNoArr", "localStorage", "UNIFY", "arrToTreeDictionary", "JSON", "arr<PERSON><PERSON><PERSON>", "item", "mapObj", "parent", "newArr", "newObj", "homePageSet", "realdata", "checkedArr", "setHomeEdit", "type", "homePageRefresh", "syschroToRole", "then", "catch", "save", "defaultFlag", "oldHomePage", "addHomePage", "checkedResults", "page_order", "page_id", "page_width", "page_height", "page_x", "page_y", "delHomePageID", "del_page", "handleCheckAllChange", "handleCheckedChange", "checkedCount", "closeDialog", "lastLoginTime", "msg", "lastTime", "message", "userInfoSet", "roleInfo", "user", "userImage", "process", "prefix", "saveHeaderImg", "editPassword", "changeVisible3", "changeVisible4", "dialogSumbit3", "userNo", "old<PERSON><PERSON>", "newPass", "dialogSumbit4", "handleAvatarSuccess", "file_url", "icon", "delete_path", "beforeAvatarUpload", "initTopHead", "updateUserImg", "formData", "beforeClose", "isInitialPassword"], "sourceRoot": "src/layout/components", "sources": ["Navbar.vue"], "sourcesContent": ["<!--top区-->\n<!-- :class=\"{ sidebar.opened?'is-active':''}\" -->\n<template>\n  <div class=\"navbar\">\n    <!--左则显示隐藏-->\n    <hamburger\n      v-if=\"menuDir === 'left'\"\n      id=\"hamburger-container\"\n      :is-active=\"sidebar.opened\"\n      class=\"hamburger-container\"\n      :class=\"sidebar.opened ? 'is-active' : ''\"\n      @toggleClick=\"toggleSideBar\"\n    />\n\n    <!--显示当前页面的路径-->\n    <!-- <breadcrumb id=\"breadcrumb-container\" class=\"breadcrumb-container\" /> -->\n    <!--  + '?imageView2/1/w/40/h/40' -->\n    <div class=\"right-menu\">\n      <template v-if=\"device !== 'mobile'\">\n        <!--用户名、头像 begin-->\n        <header-search\n          id=\"header-search\"\n          class=\"right-menu-item hover-effect\"\n        />\n\n        <img\n          :src=\"$store.getters.avatar ? $store.getters.avatar : User\"\n          class=\"user-avatar\"\n          @click=\"userInfoSet\"\n        >\n        <span v-if=\"isShowName\" class=\"user-name\" @click=\"userInfoSet\"> {{ userNo }}</span>\n        <!--用户名、头像 end-->\n        <div class=\"right-menu-item hover-effect\">\n          <sun-svg-icon\n            :class=\"{ cannotDrag: !canEdit }\"\n            class-name=\"homeEdit-icon\"\n            icon-class=\"homeEdit\"\n            @click=\"setHomeEdit\"\n          />\n        </div>\n\n        <!--菜单搜索-->\n        <screenfull\n          id=\"screenfull\"\n          class=\"right-menu-item hover-effect\"\n        /><!--全屏-->\n      </template>\n\n      <el-dropdown\n        class=\"avatar-container right-menu-item hover-effect\"\n        trigger=\"click\"\n      >\n        <div class=\"avatar-wrapper clearfix\">\n          <!-- <i class=\"el-icon-caret-bottom\" /> -->\n\n          <!-- <i class=\"el-icon-s-tools\" /> -->\n          <sun-svg-icon class-name=\"set-icon\" icon-class=\"set\" class=\"\" />\n          <el-badge\n            v-if=\"isDot\"\n            is-dot\n            class=\"item\"\n            style=\"right: 4px; top: -7px\"\n          />\n        </div>\n        <el-dropdown-menu slot=\"dropdown\" style=\"width: 113px\">\n          <!--  <router-link to=\"/\">\n            <el-dropdown-item>转到主页</el-dropdown-item>\n          </router-link> -->\n          <el-dropdown-item @click.native=\"changeColor\">\n            <span style=\"display: block\">换肤</span>\n          </el-dropdown-item>\n          <el-dropdown-item class=\"clearfix\" @click.native=\"systemMsg\">\n            <span>系统消息</span>\n            <el-badge v-if=\"msgNum !== 0\" :value=\"msgNum\" :max=\"99\" />\n          </el-dropdown-item>\n          <el-dropdown-item>\n            <!-- 二级菜单 -->\n            <el-dropdown\n              ref=\"subDropdown\"\n              trigger=\"hover\"\n              placement=\"right-start\"\n              :show-timeout=\"0\"\n            >\n              <!-- 手动控制hover显示，解决鼠标移入二级菜单时二级菜单消失问题 -->\n              <span style=\"display: inline-block\">数据同步</span>\n              <el-dropdown-menu slot=\"dropdown\">\n                <el-dropdown-item\n                  @click.native=\"ayncOrgan\"\n                >同步机构</el-dropdown-item>\n                <el-dropdown-item @click.native=\"ayncRole\">\n                  同步角色\n                </el-dropdown-item>\n                <el-dropdown-item\n                  @click.native=\"ayncUser\"\n                >同步用户</el-dropdown-item>\n                <el-dropdown-item @click.native=\"ayncDict\">\n                  同步字典\n                </el-dropdown-item>\n              </el-dropdown-menu>\n            </el-dropdown>\n          </el-dropdown-item>\n          <!-- 系统链接  begin-->\n          <el-dropdown-item>\n            <el-dropdown\n              ref=\"subDropdown2\"\n              trigger=\"hover\"\n              placement=\"right-start\"\n              :show-timeout=\"0\"\n            >\n              <!-- 手动控制hover显示，解决鼠标移入二级菜单时二级菜单消失问题 -->\n              <span style=\"display: inline-block\">系统链接</span>\n              <el-dropdown-menu slot=\"dropdown\">\n                <div v-for=\"(item, index) in sysLinkArr\" :key=\"index\">\n                  <el-dropdown-item\n                    v-if=\"item.open_type === '1'\"\n                    @click.native=\"sysLink(item)\"\n                  >{{ item.sys_name }}</el-dropdown-item>\n                  <el-dropdown-item\n                    v-if=\"item.open_type === '2'\"\n                    @click.native=\"sysLink(item)\"\n                  ><a target=\"_blank\" :href=\"item.url\">{{\n                    item.sys_name\n                  }}</a></el-dropdown-item>\n                </div>\n              </el-dropdown-menu>\n            </el-dropdown>\n          </el-dropdown-item>\n          <!-- 转授权  begin -->\n          <el-dropdown-item>\n            <el-dropdown\n              ref=\"subDropdown2\"\n              trigger=\"hover\"\n              placement=\"right-start\"\n              :show-timeout=\"0\"\n            >\n              <!-- 手动控制hover显示，解决鼠标移入二级菜单时二级菜单消失问题 -->\n              <span style=\"display: inline-block\">转授权</span>\n              <el-dropdown-menu slot=\"dropdown\">\n                <el-dropdown-item\n                  @click.native=\"sublicense\"\n                >转授权</el-dropdown-item>\n                <el-dropdown-item @click.native=\"sublicenseMan\">\n                  转授权管理\n                </el-dropdown-item>\n                <el-dropdown-item\n                  @click.native=\"sublicenseLog\"\n                >转授权日志</el-dropdown-item>\n              </el-dropdown-menu>\n            </el-dropdown>\n          </el-dropdown-item>\n          <el-dropdown-item>\n            <!-- 二级菜单 -->\n            <el-dropdown\n              ref=\"subDropdown3\"\n              trigger=\"hover\"\n              placement=\"right-start\"\n              :show-timeout=\"0\"\n            >\n              <!-- 手动控制hover显示，解决鼠标移入二级菜单时二级菜单消失问题 -->\n              <span style=\"display: inline-block\">主页设置</span>\n              <el-dropdown-menu slot=\"dropdown\">\n                <el-dropdown-item\n                  @click.native=\"homePageSet\"\n                >主页模块设置</el-dropdown-item>\n                <el-dropdown-item @click.native=\"syschroToRole\">\n                  主页设置同步\n                </el-dropdown-item>\n                <el-dropdown-item\n                  @click.native=\"homePageRefresh\"\n                >主页数据刷新</el-dropdown-item>\n              </el-dropdown-menu>\n            </el-dropdown>\n          </el-dropdown-item>\n          <el-dropdown-item divided>\n            <span style=\"display: block\" @click=\"logout\">退出系统</span>\n          </el-dropdown-item>\n        </el-dropdown-menu>\n      </el-dropdown>\n    </div>\n    <!-- 主换肤start -->\n    <change-color ref=\"dialogRef\" />\n    <!-- 主换肤end -->\n\n    <!-- 转授权start -->\n    <sublicense ref=\"sublicenseDialog\" />\n    <!-- 转授权end -->\n\n    <!-- 转授权管理start -->\n    <sublicense-man ref=\"sublicenseManDialog\" />\n    <!-- 转授权管理end -->\n\n    <!-- 转授权日志start -->\n    <sublicense-log ref=\"sublicenselogDialog\" />\n    <!-- 转授权日志end -->\n\n    <!-- 主页显示模块设置start -->\n    <el-dialog\n      title=\"主页设置-区域选择\"\n      class=\"homeDialog\"\n      :width=\"'500px'\"\n      :visible.sync=\"dialogFormVisible\"\n      :close-on-click-modal=\"false\"\n      @close=\"closeDialog\"\n    >\n      <el-checkbox\n        v-model=\"checkAll\"\n        :indeterminate=\"isIndeterminate\"\n        @change=\"handleCheckAllChange\"\n      >全选</el-checkbox>\n      <el-checkbox-group\n        v-if=\"items.length > 0\"\n        v-model=\"checkedItems\"\n        @change=\"handleCheckedChange\"\n      >\n        <el-row :gutter=\"10\">\n          <el-col v-for=\"item in items\" :key=\"item\" :span=\"12\">\n            <el-checkbox :label=\"item\">{{ item }}</el-checkbox>\n          </el-col>\n        </el-row>\n      </el-checkbox-group>\n\n      <div slot=\"footer\" class=\"dialog-footer\">\n        <el-button type=\"primary\" @click=\"save\">保存</el-button>\n        <!-- <el-button\n          type=\"primary\"\n          @click=\"restoreDefSet\"\n        >还原默认设置</el-button> -->\n      </div>\n    </el-dialog>\n    <!-- 主页设置end -->\n\n    <!-- 个人信息start -->\n    <el-dialog\n      title=\"个人信息\"\n      :visible.sync=\"dialogVisible2\"\n      :before-close=\"beforeClose\"\n    >\n      <div class=\"user-left\" title=\"点击修改头像\">\n        <el-upload\n          class=\"avatar-uploader\"\n          :action=\"actiionUrl\"\n          :show-file-list=\"false\"\n          :on-success=\"handleAvatarSuccess\"\n          :on-change=\"beforeAvatarUpload\"\n          :auto-upload=\"false\"\n        >\n          <img v-if=\"uploadImg\" :src=\"uploadImg\" class=\"avatar\">\n          <img v-else :src=\"User\" class=\"avatar\">\n          <!-- <i v-else class=\"el-icon-plus avatar-uploader-icon\" /> -->\n        </el-upload>\n        <!-- <img :src=\"User\" class=\"avatar\"> -->\n      </div>\n      <div class=\"user-right\">\n        <el-descriptions title=\"用户信息\" :column=\"1\">\n          <el-descriptions-item\n            v-for=\"item in desArr\"\n            :key=\"item.name\"\n            :label=\"item.label\"\n          >\n            <span\n              v-if=\"\n                item.name === 'last_login_time' ||\n                  item.name === 'last_modi_date'\n              \"\n            >\n              {{ userInfo[item.name] | dateTimeFormat }}\n            </span>\n            <span v-else-if=\"item.name === 'organ_no'\">\n              {{ `${userInfo[item.name]} - ${userInfo.organ_name}` }}\n            </span>\n            <span v-else-if=\"item.name === 'tellerlvl'\">\n              {{ userInfo[item.name] + '级' }}\n            </span>\n            <span v-else>\n              {{ userInfo[item.name] }}\n            </span>\n          </el-descriptions-item>\n        </el-descriptions>\n      </div>\n      <div slot=\"footer\" class=\"dialog-footer\">\n        <el-button @click=\"saveHeaderImg\">保存头像</el-button>\n        <el-button type=\"primary\" @click=\"editPassword\">修改密码</el-button>\n      </div>\n    </el-dialog>\n    <!-- 个人信息end -->\n    <!-- 修改密码start -->\n    <sun-form-dialog\n      :dialog-config=\"dialog3\"\n      @dialogClose=\"changeVisible3\"\n      @dialogSubmit=\"dialogSumbit3\"\n    />\n    <!-- 修改密码end -->\n    <!-- 消息通知弹框begin -->\n    <msg-card\n      v-if=\"isShow\"\n      ref=\"msgCard\"\n      class=\"msgcard\"\n      style=\"z-index: 2003\"\n    />\n    <!-- 消息通知弹框end -->\n    <!-- 主页设置同步start -->\n    <sun-form-dialog\n      :dialog-config=\"dialog4\"\n      @dialogClose=\"changeVisible4\"\n      @dialogSubmit=\"dialogSumbit4\"\n    />\n    <!-- 主页设置同步end -->\n    <!-- URL地址链接弹框 -->\n    <SunSysLinkDialog\n      :dialog-config=\"sysConfig\"\n      @dialogClose=\"sysDialogClose\"\n    />\n  </div>\n</template>\n\n<script>\nimport {\n  commonMsgSuccess,\n  commonMsgConfirm,\n  commonMsgInfo,\n  commonMsgWarn\n} from '@/utils/message.js' // 提示信息\nimport { dateTimeFormat } from '@/filters'\nimport { mapGetters } from 'vuex'\nimport { commonBlank } from '@/utils/common'\nimport ChangeColor from './ChangeColor' // 换肤\nimport Sublicense from './Sublicense' // 转授权\nimport SublicenseMan from './SublicenseMan' // 转授权管理\nimport SublicenseLog from './SublicenseLog' // 转授权日志\nimport msgCard from '@/views/system/config/message/msgCard' // 系统消息通知\nimport { Hamburger } from '@/components' // 左侧显示隐藏图标\nimport { Screenfull } from '@/components' // 全屏\nimport { HeaderSearch } from '@/components' // 菜单搜索\n\nimport User from '@/assets/img/main/user.png' // 默认头像\nimport { encryptResult } from '@/utils/crypto'\nimport { config, config4 } from './info' // 表头、表单配置\nimport { Common, system } from '@/api'\nimport { menuDir } from '@/settings'\nconst {\n  homeQuery,\n  homeModify,\n  userController,\n  changePasswordController,\n  organTree, // 同步机构\n  getAllRoles, // 同步角色\n  getAllUsers, // 同步用户\n  fieldController, // 同步字典\n  uploadUserImg,\n  uploadIconConfig,\n  showAvatar,\n  selectHomepageRoles,\n  syschroHomeToRole,\n  initSysLink,\n  getSysParam\n} = Common\nconst { queryMsg } = system.SysMes\nimport defaultSettings from '@/settings'\nconst prefix = defaultSettings.service.system // 前缀公共路由\nlet timeout\nexport default {\n  components: {\n    ChangeColor,\n    Hamburger,\n    Screenfull,\n    HeaderSearch,\n    SublicenseMan,\n    SublicenseLog,\n    Sublicense,\n    msgCard\n    // SunSysLinkDialog\n  },\n  data() {\n    return {\n      menuDir: menuDir,\n      msgNum: '', // 系统消息未处理条数\n      isDot: false, // 设置小红点显示隐藏\n      checked: true,\n      canEdit: false, // 设置主页是否可以拖拉\n\n      actiionUrl:\n        'http://127.0.0.1:9528/dev-api/system/fileController/fileUpload.do?filePath=userImageFilePath', // 上传地址\n      image: '', // 原本头像\n      imageUrl: '', // 原本头像地址\n      User: User,\n      uploadImg: '', // 上传框中的图片\n      file: {}, // 头像文件\n      dialogFormVisible: false,\n      dialogVisible2: false,\n      checkAll: false,\n      checkedItems: [],\n      menuList: [], // 全部主页模块\n      items: [],\n      isIndeterminate: true,\n      desArr: [\n        { label: '用户', name: 'user_no' },\n        { label: '姓名', name: 'user_name' },\n        { label: '所属机构', name: 'organ_no' },\n        { label: '用户等级', name: 'tellerlvl' },\n        { label: '角色', name: 'role' },\n        { label: '上次登录时间', name: 'last_login_time' },\n        { label: '最后修改时间', name: 'last_modi_date' }\n      ],\n      userInfo: {},\n      user_list: [], // 全局变量  用户信息 -最后登录时间\n      role_list: [], // 全局变量  用户信息-角色信息\n      dialog3: {\n        // 修改密码弹框\n        visible: false,\n        componentProps: {\n          // 弹出框配置属性\n          title: '密码修改'\n          // closeOnClickModal: false, // 禁止通过点击空白处关闭弹窗\n          // closeOnPressEscape: true // 禁止通过Esc键关闭弹窗\n        },\n        form: {\n          // 表单属性配置\n          labelWidth: '12rem', // 当前表单标签宽度配置\n          config: config(this),\n          defaultForm: {\n            user_no: this.$store.getters.userNo,\n            user_name: this.$store.getters.userName,\n            j_userinfo_changepass_oldpass: '',\n            j_userinfo_changepass_newpass: '',\n            j_userinfo_changepass_confirmpass: ''\n          }\n        }\n      },\n      dialog4: {\n        // 主页设置同步弹框\n        visible: false,\n        componentProps: {\n          // 弹出框配置属性\n          title: '同步当前主页至指定角色',\n          width: '50rem' // 当前弹出框宽度\n        },\n        form: {\n          // 表单属性配置\n          labelWidth: '12rem', // 当前表单标签宽度配置\n          config: config4(this),\n          defaultForm: {\n            roles: ''\n          }\n        }\n      },\n      isShow: false, // 系统消息弹框为true  显示\n      passwordClose: true, // 是否可以关闭修改密码弹窗:默认可以关闭修改密码弹窗\n      sysLinkArr: [], // 系统链接\n      sysConfig: {\n        // 系统内链接\n        visible: false,\n        srcUrl: '',\n        title: ''\n      },\n      isShowName: false\n    }\n  },\n  computed: {\n    ...mapGetters(['sidebar', 'avatar', 'device', 'userNo'])\n  },\n  watch: {\n    msgNum(val) {\n      // val 未处理消息总数\n      // 如果为0   设置不显示小红点和系统消息弹窗\n      if (val === 0) {\n        this.isDot = false // 小红点不显示\n        this.isShow = false // 系统消息弹框不显示\n      } else {\n        this.isDot = true // 小红点显示\n        // 未处理消息总数不为0且数据字典加载完毕  系统消息弹框显示\n        if (!commonBlank(this.$store.getters.dictionaryLet.UNIFY)) {\n          // 当本地有数据字典时   不再发请求拿数据字典\n          this.isShow = true // 系统消息弹框显示\n        } else {\n          // 当本地有数据字典时   发请求拿数据字典，请求字典时间较慢，做延迟处理，拿到字典后再进行字典格式化处理\n          timeout = setTimeout(() => {\n            this.isShow = true // 系统消息弹框显示\n          }, 3000)\n        }\n      }\n    }\n  },\n  created() {\n    this.userNameShow()\n  },\n  mounted() {\n    // this.initTopHead()\n    this.msgTotalNum()\n    this.lastLoginTime() // 上一次登录时间提示框\n    this.isInitialPassword()\n    this.initSysLink() // 系统链接初始化\n    // 新增或处理流程后即使处理系统消息条数\n    this.$bus.$on('undealMsgNum', (data) => {\n      this.msgNum = data\n    })\n    this.$bus.$on('sysLinkChange', (data) => {\n      this.sysLinkArr = data // 系统链接数据更新\n    })\n    // 同步角色\n    this.$bus.$on('updateRole', (data) => {\n      this.commitRole()\n    })\n  },\n  // 销毁对应自定义事件\n  beforeDestroy() {\n    this.$bus.$off('undealMsgNum')\n    this.$bus.$off('sysLinkChange')\n    this.$bus.$off('updateRole')\n    clearTimeout(timeout)\n  },\n  methods: {\n    /**\n     * 显示隐藏用户名*/\n    userNameShow() {\n      const msg = {\n        parameterList: [],\n        sysMap: {\n          param_item: 'HOME_SHOW_USER'\n        }\n      }\n      getSysParam(msg).then((res) => {\n        const { retMap } = res\n        this.isShowName = retMap.sysParam === '1'\n      })\n    },\n    /**\n     * 显示隐藏左侧区域*/\n    toggleSideBar() {\n      this.$store.dispatch('app/toggleSideBar')\n    },\n    /**\n     * 系统退出*/\n    logout() {\n      commonMsgConfirm('是否确认退出当前系统？', this, (param) => {\n        if (param) {\n          this.logoutSubmit()\n        }\n      })\n    },\n    /**\n     * 系统退出: 确认*/\n    async logoutSubmit() {\n      await this.$store.dispatch('user/logout')\n      this.$router.push(`/login?redirect=${this.$route.fullPath}`)\n    },\n    /**\n     * 换肤*/\n    changeColor() {\n      this.$refs.dialogRef.dialogShow()\n    },\n    /**\n     * 初始化系统链接\n     */\n    initSysLink() {\n      const msg = {\n        parameterList: [\n          {\n            is_lock: this.$store.getters.userNo\n          }\n        ],\n        sysMap: {\n          currentPage: -1,\n          pageNum: 1\n        }\n      }\n      initSysLink(msg).then((res) => {\n        this.sysLinkArr = res.retMap.returnList\n      })\n    },\n    /**\n     *系统链接点击事件跳转\n     *@param {Object} row 当前点击链接信息\n     */\n    sysLink(row) {\n      // if (row.open_type === '1') {\n      // 系统内 iframe嵌套\n      this.sysDialogClose(true)\n      this.sysConfig.srcUrl = row.url\n      this.sysConfig.title = row.sys_name\n      // }\n    },\n    /**\n     * 系统内链接弹框关闭\n     * @param {Boolean} param 弹窗显示状态\n     */\n    sysDialogClose(param) {\n      this.sysConfig.visible = param\n    },\n    /**\n     * 转授权*/\n    sublicense() {\n      if (!commonBlank(this.$store.getters.roleNo)) {\n        this.$refs.sublicenseDialog.dialogShow()\n      } else {\n        commonMsgInfo('当前用户无可转授权信息', this)\n      }\n    },\n    /**\n     * 转授权管理*/\n    sublicenseMan() {\n      this.$refs.sublicenseManDialog.dialogShow()\n      this.$nextTick(() => {\n        this.$refs.sublicenseManDialog.queryList()\n      })\n    },\n    /**\n     * 转授权日志*/\n    sublicenseLog() {\n      this.$refs.sublicenselogDialog.dialogShow()\n      this.$nextTick(() => {\n        this.$refs.sublicenselogDialog.queryList()\n      })\n    },\n    /**\n     *系统消息 */\n    systemMsg() {\n      //   解决相同路径跳转报错问题 路由重定向\n      //  this.$router.push({ path: 'systems' })\n      this.$router.push(`/systems?redirect=${this.$route.fullPath}`)\n    },\n    /**\n     *系统消息总条数  */\n    msgTotalNum() {\n      const msg = {\n        parameterList: [{}],\n        currentPage: 1,\n        pageSize: this.$store.getters.pageSize,\n        user_no: this.$store.getters.userNo,\n        organ_no: this.$store.getters.organNo,\n        organ_level: this.$store.getters.organLevel,\n        role_no: this.$store.getters.roleNo,\n        msg_type: '',\n        classify: 'DEAL_STATUS',\n        msg_content: '',\n        deal_state: ''\n      }\n      queryMsg(msg).then((res) => {\n        // 'readNum':0,//已读消息条数\n        const {\n          unReadNum // 未处理消息总数\n        } = res.retMap\n        this.msgNum = unReadNum // 未处理消息总数\n      })\n    },\n    /**\n     *同步机构 */\n    ayncOrgan() {\n      commonMsgConfirm('确认同步机构？', this, (param) => {\n        if (param) {\n          this.$store.dispatch('common/getRoleData')\n          // 点击确认后先从本地获取\n          const msg = {\n            parameterList: [''],\n            oper_type: 0\n          }\n          organTree(msg).then((res) => {\n            if (res.retMap.organFlag) {\n              // console.log(this.$store.getters.organList, res.retMap.organList)\n              this.$store.commit('common/SET_ORGAN_LIST', res.retMap.organList) // 将机构树数组存在store中\n              this.$store.dispatch(\n                'common/objectDataTranslate',\n                res.retMap.organList\n              )\n              this.$store.dispatch(\n                'common/treeDataTranslate',\n                res.retMap.organList\n              )\n              commonMsgSuccess('机构同步操作成功！', this)\n            }\n          })\n        }\n      })\n    },\n    /**\n     *同步角色 */\n    ayncRole() {\n      commonMsgConfirm('确认同步角色？', this, (param) => {\n        if (param) {\n          // this.$store.dispatch('common/getRoleData')\n          // commonMsgSuccess('角色同步操作成功！', this)\n          this.commitRole(true)\n        }\n      })\n    },\n    /**\n     * 确认-同步角色 */\n    commitRole(flag) {\n      const msg = { parameterList: [] }\n      getAllRoles(msg).then((res) => {\n        const roleList = res.retMap.roleList\n        const roleLevelData = {}\n        for (let level = 1; ; level++) {\n          const levelRole = this.getLevelRole(roleList, level)\n          if (!levelRole || levelRole.length <= 0) {\n            break\n          }\n          roleLevelData['level' + level] = levelRole\n        }\n        this.$store.commit('common/SET_ROLE_DATA', roleLevelData) // 将角色数据对象存在store中\n        roleList.map((role) => {\n          role.label = role.name\n        })\n        this.$store.commit('common/SET_ROLE_LIST', roleList) // 将角色数组存在store中\n        flag ? commonMsgSuccess('角色同步操作成功！', this) : ''\n      })\n    },\n    /**\n     * 获取某个级别的角色\n     * @param {Array} roleList 全部角色\n     * @param {Number} level 角色等级\n     * @returns {Object} 返回相应角色等级的角色对象\n     */\n    getLevelRole(roleList, level) {\n      const levelString = level.toString()\n      const roleLevelData = roleList.filter(function(item, index) {\n        return item.role_level === levelString\n      })\n      return roleLevelData\n    },\n    /**\n     *同步用户 */\n    ayncUser() {\n      commonMsgConfirm('确认同步用户？', this, (param) => {\n        if (param) {\n          const msg = {\n            parameterList: []\n          }\n          getAllUsers(msg).then((res) => {\n            const { userList } = res.retMap\n            userList.map((role) => {\n              role.label = role.name\n            })\n            this.$store.commit('common/SET_USER_LIST', userList) // 将用户信息数组存在store中\n            commonMsgSuccess('角色用户操作成功！', this)\n          })\n        }\n      })\n    },\n    /**\n     *同步字典 */\n    ayncDict() {\n      commonMsgConfirm('确认同步字典？', this, (param) => {\n        if (param) {\n          // 点击确认后先从本地获取数据字典---发请求----请求完毕存到本地  res不为空,更新成功，否则更新失败\n          // this.$store.dispatch('common/getFieldData')\n          const dictionaryLet = this.$store.getters.dictionaryLet\n          let systemNo = ''\n          for (const key in dictionaryLet) {\n            if (key !== 'dictionary' && key !== 'fieldTime') {\n              systemNo = key + ',' + systemNo\n            }\n          }\n          systemNo = systemNo.substring(0, systemNo.length - 1)\n          const msg = {\n            parameterList: [''],\n            fieldTime: '',\n            operType: '1', // 门户操作标识\n            systemNo: systemNo\n          }\n          fieldController(msg).then((res) => {\n            const systemNoArr = systemNo.split(',')\n            for (const item of systemNoArr) {\n              if (item === 'UNIFY') {\n                localStorage.setItem(item, JSON.stringify(res.retMap[item]))\n                localStorage.setItem('UNIFYdictionary', JSON.stringify({ UNIFY: res.retMap.dictionary[item] }))\n              } else {\n                localStorage.setItem(item, JSON.stringify(res.retMap[item]))\n                localStorage.setItem(item + 'dictionary', JSON.stringify({ [item]: res.retMap.dictionary[item] }))\n              }\n            }\n            localStorage.setItem('fieldTime', res.retMap.fieldTime) // 存储数据字典获取时间\n            this.$store.commit('common/ADD_DICTIONARYLET_SUBKEY', res.retMap)\n            commonMsgSuccess('数据字典同步操作成功！', this)\n          })\n        }\n      })\n    },\n    arrToTreeDictionary(obj) {\n      // 这个数据有一个问题，就时根节点的field_id 和  parent_field 一样 都为000000\n      const newObj = {}\n      const newArr = []\n      const mapObj = {}\n      for (const item in obj) {\n        const arrObj = JSON.parse(\n          JSON.stringify(obj[item].filter((item) => item.field_id !== '000000'))\n        )\n        arrObj.forEach((item) => {\n          item.children = []\n          item.id = item.field_id\n          item.label = item.field_id + '-' + item.field_name\n          mapObj[item.field_id] = item\n        })\n        arrObj.forEach((item) => {\n          const parent = mapObj[item.parent_field]\n          if (parent) {\n            parent.children.push(item)\n          } else {\n            newArr.push(item)\n          }\n        })\n        newObj[item] = newArr\n      }\n      return newObj\n    },\n    /**\n     * 主页设置: 查询\n     */\n    homePageSet() {\n      let items = []\n      let checkedItems = []\n      const msg = {\n        parameterList: [{}],\n        // oper_type: 'selectMenu',\n        user_no: this.$store.getters.userNo,\n        role_no: this.$store.getters.roleNo\n      }\n      const checkedArr = this.$store.getters.homeMoudlesChecked\n      homeQuery(msg).then((res) => {\n        const realdata = res.retMap.menuList\n        this.menuList = realdata\n        realdata.forEach((item) => {\n          checkedArr.forEach((item1) => {\n            if (item1.page_id === item.menu_id) {\n              checkedItems = [...checkedItems, item.menu_name]\n            }\n          })\n          items = [...items, item.menu_name]\n        })\n        this.items = items\n        this.checkedItems = checkedItems\n        this.dialogFormVisible = true\n      })\n    },\n    /**\n     * 设置主页是否可以拖拉\n     */\n    setHomeEdit() {\n      this.canEdit = !this.canEdit\n      this.$bus.$emit('homePage', { type: 'canEdit', data: this.canEdit })\n    },\n    /**\n     * 主页设置: 主页数据刷新\n     */\n    homePageRefresh() {\n      this.$bus.$emit('homePage', { type: 'refresh', data: null })\n    },\n    /**\n     * 主页设置: 主页设置同步\n     */\n    syschroToRole() {\n      if (this.$store.getters.userNo === 'admin') {\n        // 管理员不能操作此功能\n        commonMsgWarn('管理员无权限操作此功能', this)\n        return\n      }\n      const msg = {\n        parameterList: [],\n        oper_type: 'SelectRoles',\n        user_no: this.$store.getters.userNo\n      }\n      selectHomepageRoles(msg)\n        .then((response) => {\n          const { roles } = response.retMap\n          const roles2 = roles.map((item) => {\n            item.label = item.role_no + '' + item.role_name\n            item.value = item.role_no\n            return item\n          })\n          this.dialog4.form.config.roles.options = roles2\n        })\n        .catch(() => {\n          commonMsgWarn('初始化角色异常', this)\n        })\n      this.dialog4.visible = true\n    },\n    /**\n     * 主页模块设置: 保存\n     */\n    save() {\n      const checkedResults = []\n      let delHomePageID = [] // 移除原始选中状态的数组\n      let defaultFlag = false\n      let checkedArr = this.$store.getters.homeMoudlesChecked // 原本已选中\n\n      if (commonBlank(this.$store.state.common.homeMoudlesDefaultChecked)) {\n        defaultFlag = true\n      }\n      const addHomePage = [] // 新增项\n      const oldHomePage = [] // 原本已选中的page_name数组\n      if (commonBlank(this.$store.state.common.homeMoudlesDefaultChecked)) {\n        if (commonBlank(this.$store.state.common.homeMoudlesChecked)) {\n          checkedArr = this.menuList\n        } else {\n          checkedArr = this.menuList\n        }\n      }\n      // 各模块的右下角坐标\n      // const moduleXY = checkedArr.map((item) => {\n      //   return {\n      //     r_b_x: item.page_x * 1 + item.page_width * 1,\n      //     r_b_y: item.page_y * 1 + item.page_height * 1\n      //   }\n      // })\n      // 获取各模块中最大的Y值\n      // console.log(checkedArr)\n      // const maxY = Math.max(...moduleXY.map((item) => item.r_b_y))\n      for (const oldItem of checkedArr) {\n        if (commonBlank(this.$store.state.common.homeMoudlesChecked)) {\n          oldHomePage.push(oldItem.menu_name)\n        } else {\n          oldHomePage.push(oldItem.page_name)\n        }\n      }\n      // 循环判断新增的项\n      // console.log(checkedArr)\n      // console.log(this.checkedItems)\n      // console.log(oldHomePage)\n      for (const newItem of this.checkedItems) {\n        if (commonBlank(this.$store.state.common.homeMoudlesDefaultChecked)) {\n          // console.log(oldHomePage)\n          // if (oldHomePage.indexOf(newItem) === -1) {\n          addHomePage.push(newItem)\n          // }\n        } else {\n          if (oldHomePage.indexOf(newItem) === -1) {\n            addHomePage.push(newItem)\n          }\n        }\n      }\n      // console.log(addHomePage)\n      // 通过已选的模块构造参数\n      const length = oldHomePage.length\n      this.menuList.forEach((item) => {\n        addHomePage.forEach((checkItem, index) => {\n          // console.log(checkItem)\n          if (checkItem === item.menu_name) {\n            checkedResults.push({\n              page_order: length + index + 1,\n              page_id: item.menu_id,\n              page_width: 36,\n              page_height: 8,\n              page_x: 0,\n              page_y: index * 8\n            })\n          }\n        })\n      })\n      // console.log(this.menuList)\n      // console.log(addHomePage)\n      // console.log(checkedResults)\n\n      for (const key of checkedArr) {\n        let flag = '1'\n        for (const keys of this.checkedItems) {\n          if (keys === key.page_name) {\n            flag = '2'\n          }\n        }\n        if (flag === '1') {\n          delHomePageID = [...delHomePageID, key.page_id]\n        }\n      }\n\n      const msg = {\n        parameterList: checkedResults, // 新增的模块\n        // oper_type: dictionaryGet('OPERATE_MODIFY'),\n        del_page: delHomePageID.toString(), // 删除的模块\n        flag: defaultFlag\n      }\n\n      homeModify(msg).then((res) => {\n        this.dialogFormVisible = false\n        // 调用主页getBoxInit方法\n        this.$bus.$emit('getBoxInit')\n      })\n    },\n    // restoreDefSet() {\n    //   const msg = {\n    //     parameterList: [],\n    //     oper_type: dictionaryGet('OPERATE_MODIFY')\n    //   }\n    //   sysDialogController(msg).then((res) => {\n    //     console.log('res', res)\n    //     this.dialogFormVisible = false\n    //     // 调用主页getBoxInit方法\n    //     this.$bus.$emit('getBoxInit', 'reset')\n    //   })\n    // },\n    /**\n     * 主页设置：全选\n     */\n    handleCheckAllChange(val) {\n      this.checkedItems = val ? this.items : []\n      this.isIndeterminate = false\n    },\n    /**\n     * 主页设置：选中\n     */\n    handleCheckedChange(value) {\n      const checkedCount = value.length\n      this.checkAll = checkedCount === this.items.length\n      this.isIndeterminate =\n        checkedCount > 0 && checkedCount < this.items.length\n    },\n    closeDialog() {},\n    /**\n     * 上次登录时间提示\n     * @param {Boolean} userMsg 是否只是查询用户信息\n     */\n    async lastLoginTime(userMsg) {\n      const msg = {\n        parameterList: [{ user_no: this.$store.getters.userNo }],\n        oper_type: 'findUserInfo'\n      }\n      const res = await userController(msg)\n      // userController(msg).then((res) => {\n      const { userList, roleList } = res.retMap\n      this.user_list = userList\n      this.role_list = roleList\n      if (!userMsg) {\n        const lastTime = dateTimeFormat(userList[0].last_login_time)\n        this.$message({\n          message: `上一次登录时间:${lastTime}`,\n          type: 'success'\n        })\n      }\n      // })\n    },\n    // 设置用户信息弹框\n    async userInfoSet() {\n      // const msg = {\n      //   parameterList: [{ user_no: this.$store.getters.userNo }],\n      //   oper_type: 'findUserInfo'\n      // }\n      // userController(msg).then((res) => {\n      // const { userList, roleList } = res.retMap\n      await this.lastLoginTime(true)\n      let roleInfo = ''\n      this.role_list.forEach((item) => {\n        roleInfo += ',' + item.role_no + '-' + item.role_name\n      })\n\n      if (!commonBlank(roleInfo)) {\n        roleInfo = roleInfo.substring(1)\n      }\n      this.user_list[0].role = roleInfo\n      this.userInfo = this.user_list[0]\n      const user = this.user_list[0]\n      if (!commonBlank(user.image)) {\n        const userImage = this.user_list[0].image\n        // 拿到地址上的头像图片\n        this.image =\n          process.env.VUE_APP_BASE_API +\n          prefix +\n          '/download/file.do?fileName=' +\n          userImage.substring(\n            userImage.lastIndexOf('/') + 1,\n            userImage.indexOf('##')\n          ) +\n          '&saveFileName=' +\n          user.image.split('##')[0]\n        this.uploadImg = this.image\n        // 保存用户头像信息\n        this.imageUrl = this.user_list[0].image\n      } else {\n        this.image = User // 初始化页面时将后台保存图片传入\n      }\n      // })\n      this.dialogVisible2 = true\n    },\n    saveHeaderImg() {\n      commonMsgConfirm('确认更新头像？', this, (param) => {\n        if (param) {\n          this.updateUserImg() // 上传头像\n          this.dialogVisible2 = false\n        }\n      })\n    },\n    editPassword() {\n      this.passwordClose = true // 可关闭修改密码弹窗\n      this.dialog3.visible = true\n    },\n    // 修改密码弹窗-关闭\n    changeVisible3() {\n      if (this.passwordClose) {\n        this.dialog3.visible = false\n      } else {\n        return\n      }\n    },\n    // 主页同步设置弹窗-关闭\n    changeVisible4() {\n      this.dialog4.visible = false\n    },\n    // 确认修改密码\n    dialogSumbit3() {\n      const msg = {\n        userNo: this.$store.getters.userNo,\n        // oper_type: this.passwordClose ? 'reset' : 'update', // 强制修改密码传'update',主动修改密码传'reset'\n        oper_type: 'update',\n        oldPass: encryptResult(\n          this.$store.getters.initParams.enSecMap.encryptType,\n          this.dialog3.form.defaultForm.j_userinfo_changepass_oldpass\n        ),\n        newPass: encryptResult(\n          this.$store.getters.initParams.enSecMap.encryptType,\n          this.dialog3.form.defaultForm.j_userinfo_changepass_newpass\n        )\n      }\n      changePasswordController(msg).then((res) => {\n        commonMsgSuccess(res.retMsg, this)\n        this.changeVisible3()\n        this.logoutSubmit()\n      })\n    },\n    // 主页设置-主页设置同步\n    dialogSumbit4() {\n      commonMsgConfirm('确认提交数据？', this, (param) => {\n        if (param) {\n          let roles = this.dialog4.form.defaultForm.roles\n          roles = ',' + roles + ','\n          let flag = '0'\n          const currRoleList = this.$store.getters.userNo.split(',')\n          for (let i = 0; i < currRoleList.length; i++) {\n            if (roles.indexOf(currRoleList[i]) !== -1) {\n              flag = '1'\n              break\n            }\n          }\n          const msg = {\n            parameterList: [{}],\n            user_no: this.$store.getters.userNo,\n            roles: roles,\n            flag: flag // 要同步角色和当前用户角色是否存在相同角色\n          }\n          syschroHomeToRole(msg)\n            .then((response) => {\n              commonMsgSuccess(response.retMsg, this)\n              this.changeVisible4()\n            })\n            .catch(() => {\n              commonMsgWarn('同步异常', this)\n            })\n        }\n      })\n    },\n    /**\n     * 回调函数-上传头像成功时\n     * @param {Object} res 上传成功时的相应\n     * @param {Object} file 当前上传文件 */\n    handleAvatarSuccess(res, file) {\n      const upLoadData = res\n      // this.imageUrl = URL.createObjectURL(file.raw)\n\n      // 上传成功\n      let file_url = ''\n      if (!commonBlank(upLoadData.retMap)) {\n        const files = upLoadData.retMap.uploadFileList\n        for (var int = 0; int < files.length; int++) {\n          file_url = files[int].saveFileName\n        }\n      }\n      // 表单赋值\n      const msg = {\n        parameterList: [],\n        oper_type: 'uploadIconConfig',\n        icon: file_url,\n        delete_path: this.imageUrl,\n        user_no: this.$store.getters.userNogrid\n      } // 将base64的图片保存至数据库\n      uploadIconConfig(msg).then((res) => {\n        commonMsgSuccess('上传成功', this)\n      })\n    },\n    /**\n     * 回调函数-上传头像前\n     * @param {Object} file 当前上传文件 */\n    beforeAvatarUpload(file) {\n      const isJPG =\n        file.raw.type === 'image/jpeg' ||\n        file.raw.type === 'image/png' ||\n        file.raw.type === 'image/gif'\n      const isLt2M = file.raw.size / 1024 / 1024 < 2\n\n      if (!isJPG) {\n        this.$message.error('上传头像图片只能是 jpg/gif/png 格式!')\n      } else if (!isLt2M) {\n        this.$message.error('上传头像图片大小不能超过 2MB!')\n      }\n      if (!isJPG || !isLt2M) {\n        return false\n      }\n      this.uploadImg = URL.createObjectURL(file.raw) // 暂时将选择的图片作为头像，此时并没有上传至服务器\n      this.file = { ...file } // 暂时保存上传文件 深拷贝\n      return isJPG && isLt2M\n    },\n    // 展示用户头像信息\n    initTopHead() {\n      const msg = {\n        parameterList: [\n          {\n            user_no: this.$store.getters.userNo\n          }\n        ],\n        oper_type: 'initTopHead'\n      }\n      showAvatar(msg).then((res) => {\n        const userImage = res.retMap.image\n        if (!commonBlank(userImage)) {\n          const url =\n            process.env.VUE_APP_BASE_API +\n            prefix +\n            '/download/file.do?fileName=' +\n            userImage.substring(\n              userImage.lastIndexOf('/') + 1,\n              userImage.indexOf('##')\n            ) +\n            '&saveFileName=' +\n            userImage.split('##')[0]\n          this.image = url\n          this.$store.commit('user/SET_AVATAR', url)\n        } else {\n          // this.icon = 'static/img/subPage/exam/user_photo.png' // 初始化页面时将后台保存图片传入\n        }\n      })\n    },\n    updateUserImg() {\n      const formData = new FormData() //  用FormData存放上传文件\n      formData.append('file', this.file.raw)\n      uploadUserImg(formData).then((response) => {\n        // 执行成功，以列表形式返回上传成功的文件信息，包括每个文件的 文件名称、实际存储名称（带路径）\n        // 成功上传之后，获取上传地址\n\n        let file_url = ''\n        if (!commonBlank(response.retMap)) {\n          const { uploadFileList } = response.retMap\n          for (let int = 0; int < uploadFileList.length; int++) {\n            file_url = uploadFileList[int].saveFileName\n          }\n        }\n        // 表单赋值\n        const msg = {\n          parameterList: [],\n          oper_type: 'uploadIconConfig',\n          icon: file_url,\n          delete_path: this.imageUrl,\n          user_no: this.$store.getters.userNo\n        } // 将base64的图片保存至数据库\n        uploadIconConfig(msg).then((res) => {\n          commonMsgSuccess('上传成功', this)\n          this.initTopHead() // 展示用户头像信息\n          // this.userInfoSet()\n          // this.image =\n        })\n      })\n    },\n    // 关闭 个人信息 弹窗时的回调\n    beforeClose() {\n      // this.image = this.$store.getters.avatar\n      // this.userInfoSet()\n      this.dialogVisible2 = false\n    },\n    /**\n     * 判断当前登录用户是否为初始密码，是则强制修改\n     */\n    isInitialPassword() {\n      // 获取初始密码标识、过期密码标识\n      const isFirstLogin = this.$store.getters.isFirstLogin // 初始密码标识\n      const isOverFlag = this.$store.getters.isOverFlag // 过期密码标识\n      // 检查是否为初始密码或过期。如果是，弹出框强制修改\n      if (isFirstLogin === '1') {\n        this.dialog3.componentProps.title = '初始密码修改'\n        this.passwordClose = false // 不可关闭修改密码弹窗\n        this.dialog3.visible = true\n      } else if (isOverFlag === '1') {\n        this.dialog3.componentProps.title = '密码到期修改'\n        this.passwordClose = false // 不可关闭修改密码弹窗\n        this.dialog3.visible = true\n      }\n    }\n  }\n}\n</script>\n\n<style lang=\"scss\" scoped>\n$color-icon: #fff; // 图标颜色\n@import '~@/assets/scss/common/index';\n.el-checkbox {\n  margin: 1rem 2rem;\n}\n.cannotDrag {\n  opacity: 0.4;\n}\n\n.navbar {\n  height: $topHeight;\n  overflow: hidden;\n  position: relative;\n  background: $pro-bgc;\n  box-shadow: 0 0.1rem 0.4rem rgba(0, 21, 41, 0.08);\n  .hamburger-container {\n    position: fixed;\n    line-height: 4.6rem;\n    top: 3rem;\n    left: 0.7rem;\n    z-index: 32;\n    cursor: pointer;\n    transition: background 0.3s;\n    -webkit-tap-highlight-color: transparent;\n    &.is-active {\n      left: 17rem;\n    }\n  }\n\n  .breadcrumb-container {\n    float: left;\n  }\n\n  .errLog-container {\n    display: inline-block;\n    vertical-align: top;\n  }\n\n  .right-menu {\n    right: 0;\n    top: 0;\n    display: flex;\n    align-items: center;\n    position: fixed;\n    z-index: 66;\n    padding-left: 1rem;\n    height: 5rem;\n\n    &:focus {\n      outline: none;\n    }\n    span {\n      color: $pro_iconColor;\n    }\n    #screenfull {\n      &:before {\n        position: absolute;\n        content: '';\n        display: block;\n        width: 1px;\n        height: 15px;\n        background-color: #ccc;\n        top: 1px;\n        left: -13px;\n      }\n    }\n    .right-menu-item {\n      display: inline-block;\n      margin-right: 2.5rem;\n      position: relative;\n      &.hover-effect {\n        cursor: pointer;\n        &:hover {\n          svg {\n            fill: $primary;\n          }\n        }\n      }\n    }\n    //头像\n    .avatar-container {\n      margin-right: 1rem;\n      .avatar-wrapper {\n        margin-top: 0.2rem;\n        position: relative;\n        &:before {\n          position: absolute;\n          content: '';\n          display: block;\n          width: 1px;\n          height: 15px;\n          background-color: #ccc;\n          top: 2px;\n          left: -12px;\n        }\n\n        .el-icon-caret-bottom {\n          cursor: pointer;\n          position: absolute;\n          right: -2rem;\n          top: 2.5rem;\n          font-size: 1.2rem;\n        }\n\n        .el-badge__content.is-fixed {\n          position: absolute;\n          top: 13px;\n          right: 0px;\n        }\n      }\n    }\n  }\n}\n//头像\n.user-avatar {\n  cursor: pointer;\n  width: 2.1rem;\n  height: 2.1rem;\n  border-radius: 50%;\n  margin-right: 2rem;\n}\n// 用户名\n.user-name {\n  cursor: pointer;\n  margin-right: 2rem;\n  color: $menuText !important;\n}\n// 右上角工具：设置\n\n.sun-svg-icon {\n  &.set-icon {\n    fill: $pro_iconColor;\n    width: 1.8rem;\n    height: 1.8rem;\n  }\n  &.homeEdit-icon {\n    fill: $pro_iconColor;\n    width: 1.6rem;\n    height: 1.6rem;\n  }\n}\n.el-dropdown-menu--medium .el-dropdown-menu__item {\n  padding: 0 10px;\n}\n.el-dropdown-menu--medium\n  .el-dropdown-menu__item.el-dropdown-menu__item--divided:before {\n  height: 6px;\n  margin: 0 -10px;\n}\n.user-left {\n  width: 40%;\n  height: 40vh;\n  float: left;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  .avatar-uploader .el-upload {\n    border: 1px dashed #d9d9d9;\n    border-radius: 6px;\n    cursor: pointer;\n    position: relative;\n    overflow: hidden;\n  }\n  .avatar-uploader .el-upload:hover {\n    border-color: #409eff;\n  }\n  .avatar-uploader-icon {\n    font-size: 28px;\n    color: #8c939d;\n    width: 178px;\n    height: 178px;\n    line-height: 178px;\n    text-align: center;\n  }\n  .avatar {\n    width: 178px;\n    height: 178px;\n    display: block;\n  }\n}\n.user-right {\n  width: 60%;\n  height: 40vh;\n  float: left;\n  overflow: auto;\n}\n//系统消息弹窗\n::v-deep .msgcard {\n  position: fixed;\n  bottom: 0;\n  right: 0;\n  .el-card {\n    max-height: 30rem;\n  }\n}\n// 主页配置-弹框\n.homeDialog {\n  ::v-deep {\n    .el-dialog__body {\n      height: 30rem;\n      overflow: auto;\n    }\n  }\n}\n</style>\n"]}]}