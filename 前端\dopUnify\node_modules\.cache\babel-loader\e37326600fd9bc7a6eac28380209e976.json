{"remainingRequest": "D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\node_modules\\babel-loader\\lib\\index.js!D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\node_modules\\eslint-loader\\index.js??ref--13-0!D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\src\\views\\system\\dailyManage\\timing\\info.js", "dependencies": [{"path": "D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\src\\views\\system\\dailyManage\\timing\\info.js", "mtime": 1686019808513}, {"path": "D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\babel.config.js", "mtime": 1686019818359}, {"path": "D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\.babelrc", "mtime": 1705285078668}, {"path": "D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\node_modules\\eslint-loader\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["dictionaryFieds", "config", "that", "job_key", "component", "label", "colSpan", "name", "rules", "min", "max", "message", "componentProps", "placeholder", "clearable", "job_server", "filterable", "options", "job_result", "job_name", "service_module"], "sources": ["D:/1_Project/XYD_Project/dop-4.0/dop-4.1-qianduan-unify/dopUnify/src/views/system/dailyManage/timing/info.js"], "sourcesContent": ["import { dictionaryFieds } from '@/utils/dictionary' // 字典\r\n// 表单\r\nexport const config = (that) => ({\r\n  job_key: {\r\n    component: 'input',\r\n    label: '定时服务id',\r\n    colSpan: 8,\r\n    name: 'job_key',\r\n    config: {\r\n      // form-item 配置\r\n      rules: [\r\n        { min: 0, max: 20, message: '请最多填写20个字符' }\r\n      ]\r\n    },\r\n    componentProps: {\r\n      // input组件配置\r\n      placeholder: '支持按定时服务id模糊查询',\r\n      clearable: true\r\n    }\r\n  },\r\n  job_server: {\r\n    component: 'select',\r\n    label: '应用服务器',\r\n    colSpan: 8,\r\n    name: 'job_server',\r\n    config: {},\r\n    componentProps: {\r\n      placeholder: '请选择',\r\n      filterable: true,\r\n      clearable: true\r\n    },\r\n    options: []\r\n  },\r\n  job_result: {\r\n    component: 'select',\r\n    label: '执行结果',\r\n    colSpan: 8,\r\n    name: 'job_result',\r\n    config: {},\r\n    componentProps: {\r\n      placeholder: '请选择',\r\n      filterable: true,\r\n      clearable: true\r\n    },\r\n    options: dictionaryFieds('SCHEDULE_JOB_RESULT')\r\n  },\r\n  job_name: {\r\n    component: 'input',\r\n    label: '服务名称',\r\n    colSpan: 8,\r\n    name: 'job_name',\r\n    config: {},\r\n    componentProps: {\r\n      placeholder: '支持按服务名称模糊查询',\r\n      filterable: true,\r\n      clearable: true\r\n    }\r\n  },\r\n  service_module: {\r\n    component: 'select',\r\n    label: '服务模块',\r\n    colSpan: 8,\r\n    name: 'service_module',\r\n    config: {},\r\n    componentProps: {\r\n      placeholder: '请选择',\r\n      filterable: true,\r\n      clearable: true\r\n    },\r\n    options: dictionaryFieds('SERVICE_MODULE')\r\n  }\r\n})\r\n"], "mappings": "AAAA,SAASA,eAAe,QAAQ,oBAAoB,EAAC;AACrD;AACA,OAAO,IAAMC,MAAM,GAAG,SAATA,MAAM,CAAIC,IAAI;EAAA,OAAM;IAC/BC,OAAO,EAAE;MACPC,SAAS,EAAE,OAAO;MAClBC,KAAK,EAAE,QAAQ;MACfC,OAAO,EAAE,CAAC;MACVC,IAAI,EAAE,SAAS;MACfN,MAAM,EAAE;QACN;QACAO,KAAK,EAAE,CACL;UAAEC,GAAG,EAAE,CAAC;UAAEC,GAAG,EAAE,EAAE;UAAEC,OAAO,EAAE;QAAa,CAAC;MAE9C,CAAC;MACDC,cAAc,EAAE;QACd;QACAC,WAAW,EAAE,eAAe;QAC5BC,SAAS,EAAE;MACb;IACF,CAAC;IACDC,UAAU,EAAE;MACVX,SAAS,EAAE,QAAQ;MACnBC,KAAK,EAAE,OAAO;MACdC,OAAO,EAAE,CAAC;MACVC,IAAI,EAAE,YAAY;MAClBN,MAAM,EAAE,CAAC,CAAC;MACVW,cAAc,EAAE;QACdC,WAAW,EAAE,KAAK;QAClBG,UAAU,EAAE,IAAI;QAChBF,SAAS,EAAE;MACb,CAAC;MACDG,OAAO,EAAE;IACX,CAAC;IACDC,UAAU,EAAE;MACVd,SAAS,EAAE,QAAQ;MACnBC,KAAK,EAAE,MAAM;MACbC,OAAO,EAAE,CAAC;MACVC,IAAI,EAAE,YAAY;MAClBN,MAAM,EAAE,CAAC,CAAC;MACVW,cAAc,EAAE;QACdC,WAAW,EAAE,KAAK;QAClBG,UAAU,EAAE,IAAI;QAChBF,SAAS,EAAE;MACb,CAAC;MACDG,OAAO,EAAEjB,eAAe,CAAC,qBAAqB;IAChD,CAAC;IACDmB,QAAQ,EAAE;MACRf,SAAS,EAAE,OAAO;MAClBC,KAAK,EAAE,MAAM;MACbC,OAAO,EAAE,CAAC;MACVC,IAAI,EAAE,UAAU;MAChBN,MAAM,EAAE,CAAC,CAAC;MACVW,cAAc,EAAE;QACdC,WAAW,EAAE,aAAa;QAC1BG,UAAU,EAAE,IAAI;QAChBF,SAAS,EAAE;MACb;IACF,CAAC;IACDM,cAAc,EAAE;MACdhB,SAAS,EAAE,QAAQ;MACnBC,KAAK,EAAE,MAAM;MACbC,OAAO,EAAE,CAAC;MACVC,IAAI,EAAE,gBAAgB;MACtBN,MAAM,EAAE,CAAC,CAAC;MACVW,cAAc,EAAE;QACdC,WAAW,EAAE,KAAK;QAClBG,UAAU,EAAE,IAAI;QAChBF,SAAS,EAAE;MACb,CAAC;MACDG,OAAO,EAAEjB,eAAe,CAAC,gBAAgB;IAC3C;EACF,CAAC;AAAA,CAAC"}]}