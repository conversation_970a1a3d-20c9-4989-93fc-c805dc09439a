{"remainingRequest": "D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\node_modules\\babel-loader\\lib\\index.js!D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\src\\layout\\components\\SublicenseMan\\index.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\src\\layout\\components\\SublicenseMan\\index.vue", "mtime": 1703583640612}, {"path": "D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\babel.config.js", "mtime": 1686019818359}, {"path": "D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\.babelrc", "mtime": 1705285078668}, {"path": "D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1655715099000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:aW1wb3J0IF9yZWdlbmVyYXRvclJ1bnRpbWUgZnJvbSAiRDovMV9Qcm9qZWN0L1hZRF9Qcm9qZWN0L2RvcC00LjAvZG9wLTQuMS1xaWFuZHVhbi11bmlmeS9kb3BVbmlmeS9ub2RlX21vZHVsZXMvQGJhYmVsL3J1bnRpbWUvaGVscGVycy9lc20vcmVnZW5lcmF0b3JSdW50aW1lLmpzIjsKaW1wb3J0IF9hc3luY1RvR2VuZXJhdG9yIGZyb20gIkQ6LzFfUHJvamVjdC9YWURfUHJvamVjdC9kb3AtNC4wL2RvcC00LjEtcWlhbmR1YW4tdW5pZnkvZG9wVW5pZnkvbm9kZV9tb2R1bGVzL0BiYWJlbC9ydW50aW1lL2hlbHBlcnMvZXNtL2FzeW5jVG9HZW5lcmF0b3IuanMiOwppbXBvcnQgImNvcmUtanMvbW9kdWxlcy9lcy5vYmplY3QudG8tc3RyaW5nLmpzIjsKaW1wb3J0ICJjb3JlLWpzL21vZHVsZXMvd2ViLmRvbS1jb2xsZWN0aW9ucy5mb3ItZWFjaC5qcyI7CmltcG9ydCAiY29yZS1qcy9tb2R1bGVzL2VzLmFycmF5Lm1hcC5qcyI7CmltcG9ydCAiY29yZS1qcy9tb2R1bGVzL2VzLmFycmF5LmpvaW4uanMiOwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwoKaW1wb3J0IHsgY29uZmlnLCBjb25maWdUYWJsZSwgY29uZmlnRGV0YWlsVGFibGUsIGNvbmZpZ1BzZCB9IGZyb20gJy4vaW5mbyc7IC8vIOihqOWktOOAgeihqOWNlemFjee9rgppbXBvcnQgeyBlbmNyeXB0UmVzdWx0IH0gZnJvbSAnQC91dGlscy9jcnlwdG8nOyAvLyDliqDlr4YKaW1wb3J0IHsgZGljdGlvbmFyeUZpZWRzIH0gZnJvbSAnQC91dGlscy9kaWN0aW9uYXJ5LmpzJzsgLy8g5a2X5YW45bi46YePCmltcG9ydCB7IGRhdGU4Rm9ybWF0IH0gZnJvbSAnQC9maWx0ZXJzJzsKaW1wb3J0IHsgY29tbW9uQmxhbmsgfSBmcm9tICdAL3V0aWxzL2NvbW1vbic7CmltcG9ydCB7IGNvbW1vbk1zZ1N1Y2Nlc3MsIGNvbW1vbk1zZ1dhcm4sIGNvbW1vbk1zZ0Vycm9yLCBjb21tb25Nc2dDb25maXJtIH0gZnJvbSAnQC91dGlscy9tZXNzYWdlLmpzJzsgLy8g5o+Q56S65L+h5oGvCmltcG9ydCB7IENvbW1vbiB9IGZyb20gJ0AvYXBpJzsKdmFyIHN1YmxpY2Vuc2VNYW4gPSBDb21tb24uc3VibGljZW5zZU1hbiwKICByZXZva2UgPSBDb21tb24ucmV2b2tlLAogIHF1ZXJ5U3ViTGljZW5zZSA9IENvbW1vbi5xdWVyeVN1YkxpY2Vuc2UsCiAgcHNkVmFsaWQgPSBDb21tb24ucHNkVmFsaWQsCiAgY2FsbGluID0gQ29tbW9uLmNhbGxpbiwKICBzZWxlY3RGbG93RGV0YWlsRGF0YSA9IENvbW1vbi5zZWxlY3RGbG93RGV0YWlsRGF0YTsKZXhwb3J0IGRlZmF1bHQgewogIG5hbWU6ICdTdWJsaWNlbnNlTWFuJywKICBkYXRhOiBmdW5jdGlvbiBkYXRhKCkgewogICAgcmV0dXJuIHsKICAgICAgZGlhbG9nVmlzaWJsZTogZmFsc2UsCiAgICAgIGJ0bkFsbDogewogICAgICAgIC8vIOW9k+WJjemhtemcgOimgemFjee9ruadg+mZkOeahOaMiemSriAg5p2D6ZmQ6I635Y+WCiAgICAgICAgYnRuUXVlcnk6IHRydWUKICAgICAgfSwKICAgICAgcmVkQ29sb3I6IHsKICAgICAgICBjb2xvcjogJ3JlZCcKICAgICAgfSwKICAgICAgYmx1ZUNvbG9yOiB7CiAgICAgICAgY29sb3I6ICdibHVlJwogICAgICB9LAogICAgICBncmVlbkNvbG9yOiB7CiAgICAgICAgY29sb3I6ICcjMDA4MDAwJwogICAgICB9LAogICAgICBidG5EYXRhczogewogICAgICAgIC8vIOaMiemSrumFjee9rgogICAgICAgIGJ0blJldm9rZTogewogICAgICAgICAgc2hvdzogdHJ1ZSAvLyDmkqTplIAKICAgICAgICB9LAoKICAgICAgICBidG5DYWxsaW46IHsKICAgICAgICAgIHNob3c6IHRydWUgLy8g5pS25ZueCiAgICAgICAgfQogICAgICB9LAoKICAgICAgY29uZmlnOiBjb25maWcodGhpcyksCiAgICAgIGRlZmF1bHRGb3JtOiB7CiAgICAgICAgcXVlcnlfdHlwZTogJzAnLAogICAgICAgIGFwcGx5X3VzZXI6ICcnLAogICAgICAgIHRhcmdldF91c2VyOiAnJywKICAgICAgICByZWFzb246ICcnLAogICAgICAgIGlzX3JldGFrZTogJycsCiAgICAgICAgc3RhcnRfYXBwbHlfdGltZTogJycsCiAgICAgICAgZW5kX2FwcGx5X3RpbWU6ICcnCiAgICAgIH0sCiAgICAgIHRhYmxlOiB7CiAgICAgICAgLy8g6KGo5qC86YWN572uCiAgICAgICAgdGFibGVDb2x1bW5zOiBjb25maWdUYWJsZSgpLAogICAgICAgIC8vIOihqOWktOmFjee9rgogICAgICAgIHJlZjogJ3RhYmxlUmVmJywKICAgICAgICBzZWxlY3Rpb246IGZhbHNlLAogICAgICAgIC8vIOWkjemAiQogICAgICAgIGluZGV4TnVtYmVyOiB0cnVlLAogICAgICAgIC8vIOW6j+WPtwogICAgICAgIGxvYWRpbmc6IGZhbHNlLAogICAgICAgIGNvbXBvbmVudFByb3BzOiB7CiAgICAgICAgICBkYXRhOiBbXSwKICAgICAgICAgIC8vIOihqOagvOaVsOaNrgogICAgICAgICAgaGVpZ2h0OiAnMjEwcHgnLAogICAgICAgICAgZm9ybVJvdzogMyAvLyDooajljZXooYzmlbAKICAgICAgICB9LAoKICAgICAgICBwYWdlTGlzdDogewogICAgICAgICAgdG90YWxOdW06IDAsCiAgICAgICAgICBjdXJyZW50UGFnZTogMSwKICAgICAgICAgIC8vIOW9k+WJjemhtQogICAgICAgICAgcGFnZVNpemU6IHRoaXMuJHN0b3JlLmdldHRlcnMucGFnZVNpemUgLy8g5b2T5YmN6aG15pi+56S65p2h5pWwCiAgICAgICAgfSwKCiAgICAgICAgY3VycmVudFJvdzogW10gLy8g6YCJ5Lit6KGMCiAgICAgIH0sCgogICAgICBkaWFsb2c6IHsKICAgICAgICAvLyDnm67moIfnlKjmiLflr4bnoIHlvLnmoYYKICAgICAgICBjb21wb25lbnRQcm9wczogewogICAgICAgICAgLy8g5by55Ye65qGG6YWN572u5bGe5oCnCiAgICAgICAgICB3aWR0aDogJzUwcmVtJywKICAgICAgICAgIHRpdGxlOiAn5a+G56CB5qCh6aqMJwogICAgICAgIH0sCiAgICAgICAgdmlzaWJsZTogZmFsc2UsCiAgICAgICAgZm9ybTogewogICAgICAgICAgY29uZmlnOiBjb25maWdQc2QodGhpcyksCiAgICAgICAgICBsYWJlbFdpZHRoOiAnMTRyZW0nLAogICAgICAgICAgLy8g5b2T5YmN6KGo5Y2V5qCH562+5a695bqm6YWN572uCiAgICAgICAgICBkZWZhdWx0Rm9ybTogewogICAgICAgICAgICBwYXNzd29yZDogdGhpcy4kc3RvcmUuZ2V0dGVycy5lbXB0eVBzd2QgLy8g55uu5qCH55So5oi35a+G56CBCiAgICAgICAgICB9CiAgICAgICAgfQogICAgICB9LAoKICAgICAgZGlhbG9nRGV0YWlsOiB7CiAgICAgICAgLy8g5rWB5rC05Y+35a6h5om56K+m5oOFCiAgICAgICAgdmlzaWJsZTogZmFsc2UsCiAgICAgICAgLy8g5pi+56S66ZqQ6JeP6YWN572uCiAgICAgICAgYnRuU3VibWl0OiBmYWxzZSwKICAgICAgICAvLyDnoa7lrprmjInpkq4KICAgICAgICBidG5DYW5jbGU6IGZhbHNlLAogICAgICAgIC8vIOWPlua2iOaMiemSrgogICAgICAgIGNvbXBvbmVudFByb3BzOiB7CiAgICAgICAgICAvLyDlvLnlh7rmoYblsZ7mgKcKICAgICAgICAgIHRpdGxlOiAn5p+l55yL6K+m5oOFJywKICAgICAgICAgIC8vIOW8ueWHuuahhuagh+mimAogICAgICAgICAgd2lkdGg6ICc4MCUnIC8vIOW9k+WJjeW8ueWHuuahhuWuveW6piDpu5jorqQ4MCUKICAgICAgICB9LAoKICAgICAgICB0YWJsZUNvbmZpZzogewogICAgICAgICAgLy8g6KGo5qC85bGe5oCnCiAgICAgICAgICByZWY6ICd0YWJsZVJlZicsCiAgICAgICAgICBjb2x1bW5zOiBjb25maWdEZXRhaWxUYWJsZSh0aGlzKSwKICAgICAgICAgIC8vIOihqOWktAogICAgICAgICAgc2VsZWN0aW9uOiBmYWxzZSwKICAgICAgICAgIC8vIOWkjemAiQogICAgICAgICAgaW5kZXhOdW1iZXI6IGZhbHNlLAogICAgICAgICAgLy8g5bqP5Y+3CiAgICAgICAgICBsb2FkaW5nOiBmYWxzZSwKICAgICAgICAgIC8vIOetieW+heWKoOi9veS4rQogICAgICAgICAgcGFnZUxpc3Q6IHsKICAgICAgICAgICAgLy8g6aG156CBCiAgICAgICAgICAgIHRvdGFsTnVtOiAwLAogICAgICAgICAgICAvLyDmgLvpobXmlbAKICAgICAgICAgICAgY3VycmVudFBhZ2U6IDEsCiAgICAgICAgICAgIC8vIOW9k+WJjemhtQogICAgICAgICAgICBwYWdlU2l6ZTogdGhpcy4kc3RvcmUuZ2V0dGVycy5wYWdlU2l6ZSAvLyDlvZPliY3pobXmmL7npLrmnaHmlbAKICAgICAgICAgIH0sCgogICAgICAgICAgY29tcG9uZW50UHJvcHM6IHsKICAgICAgICAgICAgLy8g6KGo5qC85bGe5oCn6YWN572uCiAgICAgICAgICAgIGRhdGE6IFtdIC8vIOihqOaVsOaNrgogICAgICAgICAgfQogICAgICAgIH0KICAgICAgfSwKCiAgICAgIFN1Y2Nlc3NkaWFsb2c6IGZhbHNlLAogICAgICAvLyDovazmjojmnYPmiJDlip/mj5DnpLrlvLnnqpcKICAgICAgU2VuY29uZDogNSAvLyDorr7nva7liJ3lp4vlgJLorqHml7YKICAgIH07CiAgfSwKCiAgY29tcHV0ZWQ6IHt9LAogIHdhdGNoOiB7CiAgICBkaWFsb2dWaXNpYmxlOiB7CiAgICAgIGhhbmRsZXI6IGZ1bmN0aW9uIGhhbmRsZXIobmV3TmFtZSwgb2xkTmFtZSkgewogICAgICAgIC8vIOW8ueahhuWFs+mXrea4heepuuaJgOacieafpeivouadoeS7tiDop6PlhrPlvLnnqpflhbPpl63kuIvmrKHmiZPlvIDmn6Xor6LmnaHku7bov5jlnKjnmoTpl67popgKICAgICAgICB0aGlzLmRlZmF1bHRGb3JtID0gewogICAgICAgICAgcXVlcnlfdHlwZTogJzAnLAogICAgICAgICAgYXBwbHlfdXNlcjogJycsCiAgICAgICAgICB0YXJnZXRfdXNlcjogJycsCiAgICAgICAgICByZWFzb246ICcnLAogICAgICAgICAgaXNfcmV0YWtlOiAnJywKICAgICAgICAgIHN0YXJ0X2FwcGx5X3RpbWU6ICcnLAogICAgICAgICAgZW5kX2FwcGx5X3RpbWU6ICcnCiAgICAgICAgfTsKICAgICAgfSwKICAgICAgLy8gaW1tZWRpYXRlOiB0cnVlCiAgICAgIGRlZXA6IHRydWUKICAgIH0KICB9LAogIGNyZWF0ZWQ6IGZ1bmN0aW9uIGNyZWF0ZWQoKSB7fSwKICBtb3VudGVkOiBmdW5jdGlvbiBtb3VudGVkKCkge30sCiAgbWV0aG9kczogewogICAgZGlhbG9nU2hvdzogZnVuY3Rpb24gZGlhbG9nU2hvdygpIHsKICAgICAgdmFyIF90aGlzID0gdGhpczsKICAgICAgdGhpcy5kaWFsb2dWaXNpYmxlID0gdHJ1ZTsKICAgICAgdGhpcy4kbmV4dFRpY2soZnVuY3Rpb24gKCkgewogICAgICAgIF90aGlzLmNvbmZpZy5xdWVyeV90eXBlLm9wdGlvbnMgPSBkaWN0aW9uYXJ5RmllZHMoJ1NNX1FVRVJZX1RZUEUnKTsgLy8g5p+l6K+i57G75Z6LCiAgICAgICAgX3RoaXMuY29uZmlnLnJlYXNvbi5vcHRpb25zID0gZGljdGlvbmFyeUZpZWRzKCdTVUJMSUNFTlNFX1JFQVNPTicpOyAvLyDmjojmnYPljp/lm6AKICAgICAgICBfdGhpcy5jb25maWcuaXNfcmV0YWtlLm9wdGlvbnMgPSBkaWN0aW9uYXJ5RmllZHMoJ1NVQkxJQ0VOU0VfSVNfUkVUQUtFJyk7IC8vIOaOiOadg+eKtuaAgQogICAgICB9KTsKICAgIH0sCiAgICAvKioNCiAgICAgKiDmjInpkq4gLeafpeivoiovCiAgICBxdWVyeUxpc3Q6IGZ1bmN0aW9uIHF1ZXJ5TGlzdCgpIHsKICAgICAgdmFyIF90aGlzMiA9IHRoaXM7CiAgICAgIHZhciBtc2cgPSB7CiAgICAgICAgcGFyYW1ldGVyTGlzdDogW10sCiAgICAgICAgY3VycmVudFBhZ2U6IHRoaXMudGFibGUucGFnZUxpc3QuY3VycmVudFBhZ2UsCiAgICAgICAgcGFnZU51bTogdGhpcy50YWJsZS5wYWdlTGlzdC5wYWdlU2l6ZSwKICAgICAgICAvLyDmn6Xor6LnmoTmnaHku7YKICAgICAgICBxdWVyeV90eXBlOiB0aGlzLmRlZmF1bHRGb3JtLnF1ZXJ5X3R5cGUgPT09ICcnID8gJzAnIDogdGhpcy5kZWZhdWx0Rm9ybS5xdWVyeV90eXBlLAogICAgICAgIHRhcmdldF91c2VyOiB0aGlzLmRlZmF1bHRGb3JtLnRhcmdldF91c2VyLAogICAgICAgIHJlYXNvbjogdGhpcy5kZWZhdWx0Rm9ybS5yZWFzb24sCiAgICAgICAgYXBwbHlfdXNlcjogdGhpcy5kZWZhdWx0Rm9ybS5hcHBseV91c2VyLAogICAgICAgIHN0YXJ0X2FwcGx5X3RpbWU6IHRoaXMuZGVmYXVsdEZvcm0uc3RhcnRfYXBwbHlfdGltZSA/ICIiLmNvbmNhdChkYXRlOEZvcm1hdCh0aGlzLmRlZmF1bHRGb3JtLnN0YXJ0X2FwcGx5X3RpbWUpLCAiMDAwMDAwIikgOiAnJywKICAgICAgICBlbmRfYXBwbHlfdGltZTogdGhpcy5kZWZhdWx0Rm9ybS5lbmRfYXBwbHlfdGltZSA/ICIiLmNvbmNhdChkYXRlOEZvcm1hdCh0aGlzLmRlZmF1bHRGb3JtLmVuZF9hcHBseV90aW1lKSwgIjI0MDAwMCIpIDogJycsCiAgICAgICAgaW5zdF9pZDogJycsCiAgICAgICAgaXNfcmV0YWtlOiB0aGlzLmRlZmF1bHRGb3JtLmlzX3JldGFrZQogICAgICB9OwogICAgICBzdWJsaWNlbnNlTWFuKG1zZykudGhlbihmdW5jdGlvbiAocmVzKSB7CiAgICAgICAgdmFyIF9yZXMkcmV0TWFwID0gcmVzLnJldE1hcCwKICAgICAgICAgIGxpc3QgPSBfcmVzJHJldE1hcC5saXN0LAogICAgICAgICAgdG90YWxOdW0gPSBfcmVzJHJldE1hcC50b3RhbE51bSwKICAgICAgICAgIGN1cnJlbnRQYWdlID0gX3JlcyRyZXRNYXAuY3VycmVudFBhZ2U7CiAgICAgICAgX3RoaXMyLnRhYmxlLmNvbXBvbmVudFByb3BzLmRhdGEgPSBsaXN0OwogICAgICAgIF90aGlzMi50YWJsZS5wYWdlTGlzdC50b3RhbE51bSA9IHRvdGFsTnVtOwogICAgICAgIF90aGlzMi50YWJsZS5wYWdlTGlzdC5jdXJyZW50UGFnZSA9IGN1cnJlbnRQYWdlOwogICAgICAgIC8vIOaSpOmUgOaMiemSruaYvumakOS6i+S7tiDkuI3kuLrnqbrmiJbkuI3kuLowICDmjInpkq7pmpDol48KICAgICAgICBpZiAobXNnLnF1ZXJ5X3R5cGUgIT09ICcwJykgewogICAgICAgICAgX3RoaXMyLmJ0bkRhdGFzLmJ0blJldm9rZS5zaG93ID0gZmFsc2U7IC8vIOaMiemSrumakOiXjwogICAgICAgICAgX3RoaXMyLmJ0bkRhdGFzLmJ0bkNhbGxpbi5zaG93ID0gZmFsc2U7IC8vIOaMiemSrumakOiXjwogICAgICAgIH0gZWxzZSB7CiAgICAgICAgICBfdGhpczIuYnRuRGF0YXMuYnRuUmV2b2tlLnNob3cgPSB0cnVlOyAvLyDmjInpkq7mmL7npLoKICAgICAgICAgIF90aGlzMi5idG5EYXRhcy5idG5DYWxsaW4uc2hvdyA9IHRydWU7IC8vIOaMiemSruaYvuekugogICAgICAgIH0KICAgICAgfSk7CiAgICB9LAogICAgLyoqDQogICAgICrpgInmi6nooYwgKi8KICAgIGN1cnJlbnRDaGFuZ2U6IGZ1bmN0aW9uIGN1cnJlbnRDaGFuZ2Uocm93KSB7CiAgICAgIHRoaXMudGFibGUuY3VycmVudFJvdyA9IHJvdzsKICAgIH0sCiAgICAvKioNCiAgICAgKuaMiemSriAg5pKk6ZSAICovCiAgICBoYW5kbGVSZXZva2U6IGZ1bmN0aW9uIGhhbmRsZVJldm9rZSgpIHsKICAgICAgdmFyIF90aGlzMyA9IHRoaXM7CiAgICAgIHZhciByb3dzID0gdGhpcy50YWJsZS5jdXJyZW50Um93Lmxlbmd0aDsKICAgICAgaWYgKHJvd3MgPT09IDApIHsKICAgICAgICBjb21tb25Nc2dXYXJuKCfor7fpgInkuK3kuIDmnaHmlbDmja4nLCB0aGlzKTsKICAgICAgICByZXR1cm47CiAgICAgIH0gZWxzZSB7CiAgICAgICAgaWYgKHRoaXMudGFibGUuY3VycmVudFJvdy5pc19yZXRha2UgPT09ICczJyB8fCB0aGlzLnRhYmxlLmN1cnJlbnRSb3cuaXNfcmV0YWtlID09PSAnNCcgfHwgdGhpcy50YWJsZS5jdXJyZW50Um93LmlzX3JldGFrZSA9PT0gJzUnKSB7CiAgICAgICAgICBjb21tb25Nc2dXYXJuKCflj6rmnInlvoXlrqHmibnlkozlvoXmjojmnYPnmoTovazmjojmnYPlj6/ku6Xov5vooYzmkqTplIDmk43kvZwnLCB0aGlzKTsKICAgICAgICB9IGVsc2UgewogICAgICAgICAgdmFyIG1zZyA9IHsKICAgICAgICAgICAgcGFyYW1ldGVyTGlzdDogW10sCiAgICAgICAgICAgIHN5c01hcDogewogICAgICAgICAgICAgIGluc3RfaWQ6IHRoaXMudGFibGUuY3VycmVudFJvdy5zdWJsaWNlbnNlX2lkLAogICAgICAgICAgICAgIGlzX3JldGFrZTogdGhpcy50YWJsZS5jdXJyZW50Um93LmlzX3JldGFrZQogICAgICAgICAgICB9CiAgICAgICAgICB9OwogICAgICAgICAgcmV2b2tlKG1zZykudGhlbihmdW5jdGlvbiAocmVzKSB7CiAgICAgICAgICAgIGNvbW1vbk1zZ1N1Y2Nlc3MoJ+aSpOmUgOaIkOWKnycsIF90aGlzMyk7CiAgICAgICAgICAgIF90aGlzMy5xdWVyeUxpc3QoKTsKICAgICAgICAgIH0pLmNhdGNoKGZ1bmN0aW9uICgpIHsKICAgICAgICAgICAgY29tbW9uTXNnRXJyb3IoJ+aSpOmUgOWksei0pScsIF90aGlzMyk7CiAgICAgICAgICB9KTsKICAgICAgICB9CiAgICAgIH0KICAgIH0sCiAgICAvKioNCiAgICAgKuaMiemSriAg5pS25Zue5o6I5p2DICovCiAgICBoYW5kbGVDYWxsaW46IGZ1bmN0aW9uIGhhbmRsZUNhbGxpbigpIHsKICAgICAgdmFyIF90aGlzNCA9IHRoaXM7CiAgICAgIHZhciByb3dzID0gdGhpcy50YWJsZS5jdXJyZW50Um93Lmxlbmd0aDsKICAgICAgaWYgKHJvd3MgPT09IDApIHsKICAgICAgICBjb21tb25Nc2dXYXJuKCfor7fpgInkuK3kuIDmnaHmlbDmja4nLCB0aGlzKTsKICAgICAgICByZXR1cm47CiAgICAgIH0gZWxzZSB7CiAgICAgICAgaWYgKHRoaXMudGFibGUuY3VycmVudFJvdy5pc19yZXRha2UgPT09ICczJykgewogICAgICAgICAgLy8g5Yik5pat55So5oi35piv5ZCm5pyJ5Y+v5pS25Zue6L2s5o6I5p2D5L+h5oGvCiAgICAgICAgICB2YXIgbXNnID0gewogICAgICAgICAgICBwYXJhbWV0ZXJMaXN0OiBbXSwKICAgICAgICAgICAgdXNlcl9ubzogdGhpcy4kc3RvcmUuZ2V0dGVycy51c2VyTm8KICAgICAgICAgIH07CiAgICAgICAgICBxdWVyeVN1YkxpY2Vuc2UobXNnKS50aGVuKGZ1bmN0aW9uIChyZXMpIHsKICAgICAgICAgICAgX3RoaXM0LmRpYWxvZy52aXNpYmxlID0gdHJ1ZTsgLy8g5a+G56CB5qOA6aqM5qGG5pi+56S6CiAgICAgICAgICB9KTsKICAgICAgICB9IGVsc2UgewogICAgICAgICAgY29tbW9uTXNnV2Fybign5Y+q5pyJ5bey5o6I5p2D55qE6L2s5o6I5p2D5Y+v5Lul6L+b6KGM5pS25Zue5o6I5p2D5pON5L2cJywgdGhpcyk7CiAgICAgICAgfQogICAgICB9CiAgICB9LAogICAgLyoqDQogICAgICrmtYHmsLTlj7ct5a6h5om56K+m5oOFDQogICAgICpAcGFyYW0gcm93IOW9k+WJjeihjOaVsOaNriovCiAgICBhcHByb3ZlRGV0YWlsOiBmdW5jdGlvbiBhcHByb3ZlRGV0YWlsKHJvdykgewogICAgICB2YXIgX3RoaXM1ID0gdGhpczsKICAgICAgdGhpcy5jaGFuZ2VWaXNpYmxlKHRydWUpOwogICAgICB0aGlzLiRuZXh0VGljayhmdW5jdGlvbiAoKSB7CiAgICAgICAgdmFyIG1zZyA9IHsKICAgICAgICAgIHBhcmFtZXRlckxpc3Q6IFtdLAogICAgICAgICAgc3lzTWFwOiB7CiAgICAgICAgICAgIGluc3RfaWQ6IHJvdy5zdWJsaWNlbnNlX2lkCiAgICAgICAgICB9CiAgICAgICAgfTsKICAgICAgICBzZWxlY3RGbG93RGV0YWlsRGF0YShtc2cpLnRoZW4oZnVuY3Rpb24gKHJlcykgewogICAgICAgICAgX3RoaXM1LmRpYWxvZ0RldGFpbC50YWJsZUNvbmZpZy5jb21wb25lbnRQcm9wcy5kYXRhID0gcmVzLnJldE1hcC5saXN0OwogICAgICAgICAgX3RoaXM1LmRpYWxvZ0RldGFpbC50YWJsZUNvbmZpZy5jb21wb25lbnRQcm9wcy5kYXRhLmZvckVhY2goZnVuY3Rpb24gKGl0ZW0pIHsKICAgICAgICAgICAgLy8g5b6F5a6h5om56KeS6Imy5qC85byP5YyWCiAgICAgICAgICAgIHZhciByb2xlQXJyID0gW107CiAgICAgICAgICAgIF90aGlzNS4kc3RvcmUuZ2V0dGVycy5yb2xlTGlzdC5tYXAoZnVuY3Rpb24gKHZhbCkgewogICAgICAgICAgICAgIGlmICghY29tbW9uQmxhbmsoaXRlbS5yb2xlX25vKSkgewogICAgICAgICAgICAgICAgaXRlbS5yb2xlX25vLnNwbGl0KCcsJykubWFwKGZ1bmN0aW9uIChpKSB7CiAgICAgICAgICAgICAgICAgIGlmIChpID09PSB2YWwudmFsdWUpIHsKICAgICAgICAgICAgICAgICAgICByb2xlQXJyLnB1c2godmFsLmxhYmVsKTsKICAgICAgICAgICAgICAgICAgfQogICAgICAgICAgICAgICAgICByZXR1cm4gcm9sZUFycjsKICAgICAgICAgICAgICAgIH0pOwogICAgICAgICAgICAgIH0KICAgICAgICAgICAgfSk7CiAgICAgICAgICAgIGl0ZW0ucm9sZV9ubyA9IHJvbGVBcnIuam9pbigpOwogICAgICAgICAgfSk7CiAgICAgICAgfSk7CiAgICAgIH0pOwogICAgfSwKICAgIC8qKg0KICAgICAqIOa1geawtOWPt+ivpuaDheWFs+mXreaYvuekug0KICAgICAqLwogICAgY2hhbmdlVmlzaWJsZTogZnVuY3Rpb24gY2hhbmdlVmlzaWJsZShwYXJhbSkgewogICAgICB0aGlzLmRpYWxvZ0RldGFpbC52aXNpYmxlID0gcGFyYW07CiAgICB9LAogICAgLyoqDQogICAgICog5pS25Zue5o6I5p2D5a+G56CB5qGG5by55Ye65qGGIC0g5YWz6ZetKi8KICAgIGRpYWxvZ0Nsb3NlOiBmdW5jdGlvbiBkaWFsb2dDbG9zZSgpIHsKICAgICAgdGhpcy5kaWFsb2cudmlzaWJsZSA9IGZhbHNlOwogICAgfSwKICAgIC8qKg0KICAgICAqIOaUtuWbnuaOiOadg+WvhueggeahhuW8ueWHuuahhiAtIOaPkOS6pCovCiAgICBkaWFsb2dTdWJtaXQ6IGZ1bmN0aW9uIGRpYWxvZ1N1Ym1pdCgpIHsKICAgICAgdmFyIF90aGlzNiA9IHRoaXM7CiAgICAgIGNvbW1vbk1zZ0NvbmZpcm0oJ+aYr+WQpuehruiupOaUtuWbnuaOiOadgycsIHRoaXMsIGZ1bmN0aW9uIChwYXJhbSkgewogICAgICAgIGlmIChwYXJhbSkgewogICAgICAgICAgLy8g5YWI6L+b6KGM5a+G56CB5qCh6aqMIOWGjeWPkei1t+aUtuWbnuaOiOadg+ivt+axggogICAgICAgICAgdmFyIG1zZyA9IHsKICAgICAgICAgICAgcGFyYW1ldGVyTGlzdDogW10sCiAgICAgICAgICAgIG9wZXJfdHlwZTogJ3F1ZXJ5VXNlcicsCiAgICAgICAgICAgIHVzZXJfbm86IF90aGlzNi50YWJsZS5jdXJyZW50Um93LnRhcmdldF91c2VyX25vLAogICAgICAgICAgICBwYXNzV29yZDogZW5jcnlwdFJlc3VsdChfdGhpczYuJHN0b3JlLmdldHRlcnMuaW5pdFBhcmFtcy5lblNlY01hcC5lbmNyeXB0VHlwZSwgX3RoaXM2LmRpYWxvZy5mb3JtLmRlZmF1bHRGb3JtLnBhc3N3b3JkKQogICAgICAgICAgfTsKICAgICAgICAgIC8vIOWvhueggeagoemqjAogICAgICAgICAgcHNkVmFsaWQobXNnKS50aGVuKGZ1bmN0aW9uIChyZXMpIHsKICAgICAgICAgICAgaWYgKHJlcy5yZXRDb2RlID09PSAnMjAwJykgewogICAgICAgICAgICAgIC8vIOWPkei1t+aUtuWbnuaOiOadg+ivt+axggogICAgICAgICAgICAgIHZhciBtc2cyID0gewogICAgICAgICAgICAgICAgcGFyYW1ldGVyTGlzdDogW10sCiAgICAgICAgICAgICAgICB1c2VyX25vOiBfdGhpczYuJHN0b3JlLmdldHRlcnMudXNlck5vCiAgICAgICAgICAgICAgfTsKICAgICAgICAgICAgICBjYWxsaW4obXNnMikudGhlbihmdW5jdGlvbiAocmVzMikgewogICAgICAgICAgICAgICAgLy8g6Lez6L2s5Yiw6YeN5paw55m75b2V6aG16Z2iCiAgICAgICAgICAgICAgICBfdGhpczYuZGlhbG9nVmlzaWJsZSA9IGZhbHNlOyAvLyDmjojmnYPnrqHnkIbpobXpnaLlhbPpl60KICAgICAgICAgICAgICAgIF90aGlzNi5kaWFsb2cudmlzaWJsZSA9IGZhbHNlOyAvLyDlr4bnoIHmoYblhbPpl60KICAgICAgICAgICAgICAgIF90aGlzNi5TdWNjZXNzZGlhbG9nID0gdHJ1ZTsKICAgICAgICAgICAgICAgIHZhciBpbnRlcnZhbCA9IHNldEludGVydmFsKGZ1bmN0aW9uICgpIHsKICAgICAgICAgICAgICAgICAgX3RoaXM2LlNlbmNvbmQtLTsKICAgICAgICAgICAgICAgICAgaWYgKF90aGlzNi5TZW5jb25kID09PSAwKSB7CiAgICAgICAgICAgICAgICAgICAgd2luZG93LmNsZWFySW50ZXJ2YWwoaW50ZXJ2YWwpOwogICAgICAgICAgICAgICAgICAgIF90aGlzNi5sb2dvdXQoKTsgLy8g5YCS6K6h5pe257uT5p2f5pe26Lez6L2s55m75b2V6aG16Z2iCiAgICAgICAgICAgICAgICAgIH0KICAgICAgICAgICAgICAgIH0sIDEwMDApOwogICAgICAgICAgICAgIH0pLmNhdGNoKGZ1bmN0aW9uIChlKSB7CiAgICAgICAgICAgICAgICAvLyBjb25zb2xlLmxvZyhlKQogICAgICAgICAgICAgIH0pOwogICAgICAgICAgICB9CiAgICAgICAgICB9KS5jYXRjaChmdW5jdGlvbiAoKSB7CiAgICAgICAgICAgIGNvbW1vbk1zZ1dhcm4oJ+WvhueggemUmeivrycsIF90aGlzNik7CiAgICAgICAgICB9KTsKICAgICAgICB9CiAgICAgIH0pOwogICAgfSwKICAgIC8qKg0KICAgICAqIOi3s+i9rOeZu+W9lemhtemdouaWueazlSovCiAgICBsb2dvdXQ6IGZ1bmN0aW9uIGxvZ291dCgpIHsKICAgICAgdmFyIF90aGlzNyA9IHRoaXM7CiAgICAgIHJldHVybiBfYXN5bmNUb0dlbmVyYXRvciggLyojX19QVVJFX18qL19yZWdlbmVyYXRvclJ1bnRpbWUoKS5tYXJrKGZ1bmN0aW9uIF9jYWxsZWUoKSB7CiAgICAgICAgcmV0dXJuIF9yZWdlbmVyYXRvclJ1bnRpbWUoKS53cmFwKGZ1bmN0aW9uIF9jYWxsZWUkKF9jb250ZXh0KSB7CiAgICAgICAgICB3aGlsZSAoMSkgewogICAgICAgICAgICBzd2l0Y2ggKF9jb250ZXh0LnByZXYgPSBfY29udGV4dC5uZXh0KSB7CiAgICAgICAgICAgICAgY2FzZSAwOgogICAgICAgICAgICAgICAgX2NvbnRleHQubmV4dCA9IDI7CiAgICAgICAgICAgICAgICByZXR1cm4gX3RoaXM3LiRzdG9yZS5kaXNwYXRjaCgndXNlci9sb2dvdXQnKTsKICAgICAgICAgICAgICBjYXNlIDI6CiAgICAgICAgICAgICAgICBfdGhpczcuJHJvdXRlci5wdXNoKCIvbG9naW4/cmVkaXJlY3Q9Ii5jb25jYXQoX3RoaXM3LiRyb3V0ZS5mdWxsUGF0aCkpOwogICAgICAgICAgICAgIGNhc2UgMzoKICAgICAgICAgICAgICBjYXNlICJlbmQiOgogICAgICAgICAgICAgICAgcmV0dXJuIF9jb250ZXh0LnN0b3AoKTsKICAgICAgICAgICAgfQogICAgICAgICAgfQogICAgICAgIH0sIF9jYWxsZWUpOwogICAgICB9KSkoKTsKICAgIH0sCiAgICAvKioNCiAgICAgKumhteeggeabtOaWsCAqLwogICAgZ2V0TGlzdDogZnVuY3Rpb24gZ2V0TGlzdChwYWdlUGFyYW0pIHsKICAgICAgdmFyIGN1cnJlbnRQYWdlID0gcGFnZVBhcmFtLmN1cnJlbnRQYWdlLAogICAgICAgIHBhZ2VTaXplID0gcGFnZVBhcmFtLnBhZ2VTaXplOwogICAgICB0aGlzLnRhYmxlLnBhZ2VMaXN0LnBhZ2VTaXplID0gcGFnZVNpemU7CiAgICAgIHRoaXMudGFibGUucGFnZUxpc3QuY3VycmVudFBhZ2UgPSBjdXJyZW50UGFnZTsKICAgICAgdGhpcy5xdWVyeUxpc3QoKTsKICAgIH0KICB9Cn07"}, {"version": 3, "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAqHA;AACA;AACA;AACA;AACA;AACA,SACAA,kBACAC,eACAC,gBACAC,wBACA;AACA;AACA,IACAC,gBAMAC,OANAD;EACAE,SAKAD,OALAC;EACAC,kBAIAF,OAJAE;EACAC,WAGAH,OAHAG;EACAC,SAEAJ,OAFAI;EACAC,uBACAL,OADAK;AAEA;EACAC;EACAC;IACA;MACAC;MACAC;QACA;QACAC;MACA;MACAC;QACAC;MACA;MACAC;QACAD;MACA;MACAE;QACAF;MACA;MACAG;QACA;QACAC;UACAC;QACA;;QACAC;UACAD;QACA;MACA;;MACAE;MACAC;QACAC;QACAC;QACAC;QACAC;QACAC;QACAC;QACAC;MACA;MACAC;QACA;QACAC;QAAA;QACAC;QACAC;QAAA;QACAC;QAAA;QACAC;QACAC;UACA3B;UAAA;UACA4B;UACAC;QACA;;QACAC;UACAC;UACAC;UAAA;UACAC;QACA;;QACAC;MACA;;MACAC;QACA;QACAR;UACA;UACAS;UACAC;QACA;QACAC;QACAC;UACA3B;UACA4B;UAAA;UACA3B;YACA4B;UACA;QACA;MACA;;MACAC;QACA;QACAJ;QAAA;QACAK;QAAA;QACAC;QAAA;QACAjB;UACA;UACAU;UAAA;UACAD;QACA;;QACAS;UACA;UACAtB;UACAuB;UAAA;UACAtB;UAAA;UACAC;UAAA;UACAC;UAAA;UACAI;YACA;YACAC;YAAA;YACAC;YAAA;YACAC;UACA;;UACAN;YACA;YACA3B;UACA;QACA;MACA;;MACA+C;MAAA;MACAC;IACA;EACA;;EACAC;EACAC;IACAjD;MACAkD;QACA;QACA;UACArC;UACAC;UACAC;UACAC;UACAC;UACAC;UACAC;QACA;MACA;MACA;MACAgC;IACA;EACA;EACAC;EACAC;EAEAC;IACAC;MAAA;MACA;MACA;QACA;QACA;QACA;MACA;IACA;IACA;AACA;IACAC;MAAA;MACA;QACAC;QACA1B;QACA2B;QACA;QACA7C,YACA,qCACA,MACA;QACAE;QACAC;QACAF;QACAI,gEACAyC,4DACA;QACAxC,4DACAwC,0DACA;QACAC;QACA3C;MACA;MACA1B;QACA;UAAAsE;UAAA/B;UAAAC;QACA;QACA;QACA;QACA;QACA;UACA;UACA;QACA;UACA;UACA;QACA;MACA;IACA;IACA;AACA;IACA+B;MACA;IACA;IACA;AACA;IACAC;MAAA;MACA;MACA;QACA3E;QACA;MACA;QACA,IACA,2CACA,2CACA,yCACA;UACAA;QACA;UACA;YACAqE;YACAO;cACAJ;cACA3C;YACA;UACA;UACAxB,YACAwE;YACA9E;YACA;UACA,GACA+E;YACA7E;UACA;QACA;MACA;IACA;IACA;AACA;IACA8E;MAAA;MACA;MACA;QACA/E;QACA;MACA;QACA;UACA;UACA;YACAqE;YACAW;UACA;UACA1E;YACA;UACA;QACA;UACAN;QACA;MACA;IACA;IACA;AACA;AACA;IACAiF;MAAA;MACA;MACA;QACA;UACAZ;UACAO;YACAJ;UACA;QACA;QACA/D;UACA;UACA;YACA;YACA;YACA;cACA;gBACAyE;kBACA;oBACAC;kBACA;kBACA;gBACA;cACA;YACA;YACAD;UACA;QACA;MACA;IACA;IACA;AACA;AACA;IACAE;MACA;IACA;IACA;AACA;IACAC;MACA;IACA;IACA;AACA;IACAC;MAAA;MACApF;QACA;UACA;UACA;YACAmE;YACAkB;YACAP;YACAQ,wBACA,uDACA;UAEA;UACA;UACAjF,cACAsE;YACA;cACA;cACA;gBACAR;gBACAW;cACA;cACAxE,aACAqE;gBACA;gBACA;gBACA;gBACA;gBACA;kBACA;kBACA;oBACAY;oBACA;kBACA;gBACA;cACA,GACAX;gBACA;cAAA,CACA;YACA;UACA,GACAA;YACA9E;UACA;QACA;MACA;IACA;IACA;AACA;IACA0F;MAAA;MAAA;QAAA;UAAA;YAAA;cAAA;gBAAA;gBAAA,OACA;cAAA;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IACA;IACA;AACA;IACAC;MACA;QAAA/C;MACA;MACA;MACA;IACA;EACA;AACA", "names": ["commonMsgSuccess", "commonMsgWarn", "commonMsgError", "commonMsgConfirm", "sublicenseMan", "Common", "revoke", "querySubLicense", "psdValid", "callin", "selectFlowDetailData", "name", "data", "dialogVisible", "btnAll", "btnQuery", "redColor", "color", "blueColor", "greenColor", "btnDatas", "btnRevoke", "show", "btnCallin", "config", "defaultForm", "query_type", "apply_user", "target_user", "reason", "is_retake", "start_apply_time", "end_apply_time", "table", "tableColumns", "ref", "selection", "indexNumber", "loading", "componentProps", "height", "formRow", "pageList", "totalNum", "currentPage", "pageSize", "currentRow", "dialog", "width", "title", "visible", "form", "labelWidth", "password", "dialogDetail", "btnSubmit", "btnCancle", "tableConfig", "columns", "Successdialog", "Sencond", "computed", "watch", "handler", "deep", "created", "mounted", "methods", "dialogShow", "queryList", "parameterList", "pageNum", "date8Format", "inst_id", "list", "currentChange", "handleRevoke", "sysMap", "then", "catch", "handleCallin", "user_no", "approveDetail", "item", "roleArr", "changeVisible", "dialogClose", "dialogSubmit", "oper_type", "passWord", "window", "logout", "getList"], "sourceRoot": "src/layout/components/SublicenseMan", "sources": ["index.vue"], "sourcesContent": ["/* * *转授权管理 */\r\n<template>\r\n  <div>\r\n    <el-dialog :visible.sync=\"dialogVisible\" title=\"转授权管理\" width=\"80%\">\r\n      <sun-form\r\n        :config=\"config\"\r\n        :default-form=\"defaultForm\"\r\n        :query=\"btnAll.btnQuery\"\r\n        :reset=\"true\"\r\n        label-width=\"15rem\"\r\n        @query=\"queryList\"\r\n      />\r\n      <sun-table\r\n        :table-config=\"table\"\r\n        @current-change=\"currentChange\"\r\n        @pagination=\"getList\"\r\n      >\r\n        <template slot=\"tableColumn\">\r\n          <el-table-column\r\n            v-for=\"item in table.tableColumns\"\r\n            :key=\"item.id\"\r\n            :prop=\"item.name\"\r\n            :label=\"item.label\"\r\n            :width=\"item.width\"\r\n          >\r\n            <div slot-scope=\"{ row }\">\r\n              <span\r\n                v-if=\"\r\n                  item.name === 'sublicense_id' && row['author_type'] === '2'\r\n                \"\r\n                style=\"color: blue\"\r\n                @click=\"approveDetail(row)\"\r\n              >{{ row[item.name] }}</span>\r\n              <span v-else-if=\"item.name === 'sublicense_reason'\">{{\r\n                row[item.name] | commonFormatValue('SUBLICENSE_REASON')\r\n              }}</span>\r\n              <span v-else-if=\"item.name === 'is_retake'\">{{\r\n                row[item.name] | commonFormatValue('SUBLICENSE_IS_RETAKE')\r\n              }}</span>\r\n              <span v-else-if=\"item.name === 'start_date'\">{{\r\n                row[item.name] | date10Format\r\n              }}</span>\r\n              <span v-else-if=\"item.name === 'end_date'\">{{\r\n                row[item.name] | date10Format\r\n              }}</span>\r\n              <span v-else>{{ row[item.name] }}</span>\r\n            </div>\r\n          </el-table-column>\r\n        </template>\r\n        <template slot=\"customButton\">\r\n          <sun-button\r\n            :btn-datas=\"btnDatas\"\r\n            @handleRevoke=\"handleRevoke\"\r\n            @handleCallin=\"handleCallin\"\r\n          />\r\n          <!--按钮配置-->\r\n        </template>\r\n      </sun-table></el-dialog>\r\n    <!-- 流水号审批详情弹出框 begin-->\r\n    <sun-table-dialog\r\n      :dialog-config=\"dialogDetail\"\r\n      @dialogClose=\"changeVisible\"\r\n    >\r\n      <div slot-scope=\"{ item, row }\" class=\"appr-status\">\r\n        <span v-if=\"item.name === 'organ_no'\">{{\r\n          row[item.name] | organNameFormat\r\n        }}</span>\r\n        <span\r\n          v-else-if=\"item.name === 'role_no'\"\r\n          class=\"textOverflow\"\r\n          :title=\"row[item.name]\"\r\n        >{{ row[item.name] }}</span>\r\n        <span v-else-if=\"item.name === 'deal_time'\">{{\r\n          row[item.name] | dateTimeFormat\r\n        }}</span>\r\n        <span v-else-if=\"item.name === 'deal_result'\">{{\r\n          row[item.name] | commonFormatValue('DEAL_RESULT')\r\n        }}</span>\r\n        <span\r\n          v-else-if=\"item.name === 'deal_state'\"\r\n          :style=\"\r\n            row[item.name] === '2'\r\n              ? blueColor\r\n              : row[item.name] === '4'\r\n                ? greenColor\r\n                : redColor\r\n          \"\r\n        >{{ row[item.name] | commonFormatValue('DEAL_STATE') }}</span>\r\n\r\n        <span v-else>{{ row[item.name] }}</span>\r\n      </div></sun-table-dialog>\r\n    <!--收回授权密码弹出框begin-->\r\n    <sun-form-dialog\r\n      :dialog-config=\"dialog\"\r\n      @dialogClose=\"dialogClose\"\r\n      @dialogSubmit=\"dialogSubmit\"\r\n    />\r\n    <!-- 转授权成功弹窗 begin-->\r\n    <el-dialog\r\n      title=\"重新登陆\"\r\n      top=\"15%\"\r\n      :visible.sync=\"Successdialog\"\r\n      :show-close=\"false\"\r\n      :close-on-press-escape=\"false\"\r\n      :close-on-click-modal=\"false\"\r\n      width=\"30%\"\r\n    >\r\n      <span style=\"font-size: 14px\">\r\n        <el-button type=\"success\" icon=\"el-icon-check\" circle /><span\r\n          style=\"color: teal\"\r\n        >\r\n          转授权成功 </span><span>请等待<span style=\"font-size: 20px; color: red\"> {{ Sencond }} </span>秒，将自动跳转到登录页面</span></span>\r\n    </el-dialog>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport { config, configTable, configDetailTable, configPsd } from './info' // 表头、表单配置\r\nimport { encryptResult } from '@/utils/crypto' // 加密\r\nimport { dictionaryFieds } from '@/utils/dictionary.js' // 字典常量\r\nimport { date8Format } from '@/filters'\r\nimport { commonBlank } from '@/utils/common'\r\nimport {\r\n  commonMsgSuccess,\r\n  commonMsgWarn,\r\n  commonMsgError,\r\n  commonMsgConfirm\r\n} from '@/utils/message.js' // 提示信息\r\nimport { Common } from '@/api'\r\nconst {\r\n  sublicenseMan,\r\n  revoke,\r\n  querySubLicense, // 判断用户是否有可收回信息\r\n  psdValid, // 密码校验\r\n  callin,\r\n  selectFlowDetailData\r\n} = Common\r\nexport default {\r\n  name: 'SublicenseMan',\r\n  data() {\r\n    return {\r\n      dialogVisible: false,\r\n      btnAll: {\r\n        // 当前页需要配置权限的按钮  权限获取\r\n        btnQuery: true\r\n      },\r\n      redColor: {\r\n        color: 'red'\r\n      },\r\n      blueColor: {\r\n        color: 'blue'\r\n      },\r\n      greenColor: {\r\n        color: '#008000'\r\n      },\r\n      btnDatas: {\r\n        // 按钮配置\r\n        btnRevoke: {\r\n          show: true // 撤销\r\n        },\r\n        btnCallin: {\r\n          show: true // 收回\r\n        }\r\n      },\r\n      config: config(this),\r\n      defaultForm: {\r\n        query_type: '0',\r\n        apply_user: '',\r\n        target_user: '',\r\n        reason: '',\r\n        is_retake: '',\r\n        start_apply_time: '',\r\n        end_apply_time: ''\r\n      },\r\n      table: {\r\n        // 表格配置\r\n        tableColumns: configTable(), // 表头配置\r\n        ref: 'tableRef',\r\n        selection: false, // 复选\r\n        indexNumber: true, // 序号\r\n        loading: false,\r\n        componentProps: {\r\n          data: [], // 表格数据\r\n          height: '210px',\r\n          formRow: 3 // 表单行数\r\n        },\r\n        pageList: {\r\n          totalNum: 0,\r\n          currentPage: 1, // 当前页\r\n          pageSize: this.$store.getters.pageSize // 当前页显示条数\r\n        },\r\n        currentRow: [] // 选中行\r\n      },\r\n      dialog: {\r\n        // 目标用户密码弹框\r\n        componentProps: {\r\n          // 弹出框配置属性\r\n          width: '50rem',\r\n          title: '密码校验'\r\n        },\r\n        visible: false,\r\n        form: {\r\n          config: configPsd(this),\r\n          labelWidth: '14rem', // 当前表单标签宽度配置\r\n          defaultForm: {\r\n            password: this.$store.getters.emptyPswd // 目标用户密码\r\n          }\r\n        }\r\n      },\r\n      dialogDetail: {\r\n        // 流水号审批详情\r\n        visible: false, // 显示隐藏配置\r\n        btnSubmit: false, // 确定按钮\r\n        btnCancle: false, // 取消按钮\r\n        componentProps: {\r\n          // 弹出框属性\r\n          title: '查看详情', // 弹出框标题\r\n          width: '80%' // 当前弹出框宽度 默认80%\r\n        },\r\n        tableConfig: {\r\n          // 表格属性\r\n          ref: 'tableRef',\r\n          columns: configDetailTable(this), // 表头\r\n          selection: false, // 复选\r\n          indexNumber: false, // 序号\r\n          loading: false, // 等待加载中\r\n          pageList: {\r\n            // 页码\r\n            totalNum: 0, // 总页数\r\n            currentPage: 1, // 当前页\r\n            pageSize: this.$store.getters.pageSize // 当前页显示条数\r\n          },\r\n          componentProps: {\r\n            // 表格属性配置\r\n            data: [] // 表数据\r\n          }\r\n        }\r\n      },\r\n      Successdialog: false, // 转授权成功提示弹窗\r\n      Sencond: 5 // 设置初始倒计时\r\n    }\r\n  },\r\n  computed: {},\r\n  watch: {\r\n    dialogVisible: {\r\n      handler(newName, oldName) {\r\n        // 弹框关闭清空所有查询条件 解决弹窗关闭下次打开查询条件还在的问题\r\n        this.defaultForm = {\r\n          query_type: '0',\r\n          apply_user: '',\r\n          target_user: '',\r\n          reason: '',\r\n          is_retake: '',\r\n          start_apply_time: '',\r\n          end_apply_time: ''\r\n        }\r\n      },\r\n      // immediate: true\r\n      deep: true\r\n    }\r\n  },\r\n  created() {},\r\n  mounted() {},\r\n\r\n  methods: {\r\n    dialogShow() {\r\n      this.dialogVisible = true\r\n      this.$nextTick(() => {\r\n        this.config.query_type.options = dictionaryFieds('SM_QUERY_TYPE') // 查询类型\r\n        this.config.reason.options = dictionaryFieds('SUBLICENSE_REASON') // 授权原因\r\n        this.config.is_retake.options = dictionaryFieds('SUBLICENSE_IS_RETAKE') // 授权状态\r\n      })\r\n    },\r\n    /**\r\n     * 按钮 -查询*/\r\n    queryList() {\r\n      const msg = {\r\n        parameterList: [],\r\n        currentPage: this.table.pageList.currentPage,\r\n        pageNum: this.table.pageList.pageSize,\r\n        // 查询的条件\r\n        query_type:\r\n          this.defaultForm.query_type === ''\r\n            ? '0'\r\n            : this.defaultForm.query_type,\r\n        target_user: this.defaultForm.target_user,\r\n        reason: this.defaultForm.reason,\r\n        apply_user: this.defaultForm.apply_user,\r\n        start_apply_time: this.defaultForm.start_apply_time\r\n          ? `${date8Format(this.defaultForm.start_apply_time)}000000`\r\n          : '',\r\n        end_apply_time: this.defaultForm.end_apply_time\r\n          ? `${date8Format(this.defaultForm.end_apply_time)}240000`\r\n          : '',\r\n        inst_id: '',\r\n        is_retake: this.defaultForm.is_retake\r\n      }\r\n      sublicenseMan(msg).then((res) => {\r\n        const { list, totalNum, currentPage } = res.retMap\r\n        this.table.componentProps.data = list\r\n        this.table.pageList.totalNum = totalNum\r\n        this.table.pageList.currentPage = currentPage\r\n        // 撤销按钮显隐事件 不为空或不为0  按钮隐藏\r\n        if (msg.query_type !== '0') {\r\n          this.btnDatas.btnRevoke.show = false // 按钮隐藏\r\n          this.btnDatas.btnCallin.show = false // 按钮隐藏\r\n        } else {\r\n          this.btnDatas.btnRevoke.show = true // 按钮显示\r\n          this.btnDatas.btnCallin.show = true // 按钮显示\r\n        }\r\n      })\r\n    },\r\n    /**\r\n     *选择行 */\r\n    currentChange(row) {\r\n      this.table.currentRow = row\r\n    },\r\n    /**\r\n     *按钮  撤销 */\r\n    handleRevoke() {\r\n      const rows = this.table.currentRow.length\r\n      if (rows === 0) {\r\n        commonMsgWarn('请选中一条数据', this)\r\n        return\r\n      } else {\r\n        if (\r\n          this.table.currentRow.is_retake === '3' ||\r\n          this.table.currentRow.is_retake === '4' ||\r\n          this.table.currentRow.is_retake === '5'\r\n        ) {\r\n          commonMsgWarn('只有待审批和待授权的转授权可以进行撤销操作', this)\r\n        } else {\r\n          const msg = {\r\n            parameterList: [],\r\n            sysMap: {\r\n              inst_id: this.table.currentRow.sublicense_id,\r\n              is_retake: this.table.currentRow.is_retake\r\n            }\r\n          }\r\n          revoke(msg)\r\n            .then((res) => {\r\n              commonMsgSuccess('撤销成功', this)\r\n              this.queryList()\r\n            })\r\n            .catch(() => {\r\n              commonMsgError('撤销失败', this)\r\n            })\r\n        }\r\n      }\r\n    },\r\n    /**\r\n     *按钮  收回授权 */\r\n    handleCallin() {\r\n      const rows = this.table.currentRow.length\r\n      if (rows === 0) {\r\n        commonMsgWarn('请选中一条数据', this)\r\n        return\r\n      } else {\r\n        if (this.table.currentRow.is_retake === '3') {\r\n          // 判断用户是否有可收回转授权信息\r\n          const msg = {\r\n            parameterList: [],\r\n            user_no: this.$store.getters.userNo\r\n          }\r\n          querySubLicense(msg).then((res) => {\r\n            this.dialog.visible = true // 密码检验框显示\r\n          })\r\n        } else {\r\n          commonMsgWarn('只有已授权的转授权可以进行收回授权操作', this)\r\n        }\r\n      }\r\n    },\r\n    /**\r\n     *流水号-审批详情\r\n     *@param row 当前行数据*/\r\n    approveDetail(row) {\r\n      this.changeVisible(true)\r\n      this.$nextTick(() => {\r\n        const msg = {\r\n          parameterList: [],\r\n          sysMap: {\r\n            inst_id: row.sublicense_id\r\n          }\r\n        }\r\n        selectFlowDetailData(msg).then((res) => {\r\n          this.dialogDetail.tableConfig.componentProps.data = res.retMap.list\r\n          this.dialogDetail.tableConfig.componentProps.data.forEach((item) => {\r\n            // 待审批角色格式化\r\n            const roleArr = []\r\n            this.$store.getters.roleList.map((val) => {\r\n              if (!commonBlank(item.role_no)) {\r\n                item.role_no.split(',').map((i) => {\r\n                  if (i === val.value) {\r\n                    roleArr.push(val.label)\r\n                  }\r\n                  return roleArr\r\n                })\r\n              }\r\n            })\r\n            item.role_no = roleArr.join()\r\n          })\r\n        })\r\n      })\r\n    },\r\n    /**\r\n     * 流水号详情关闭显示\r\n     */\r\n    changeVisible(param) {\r\n      this.dialogDetail.visible = param\r\n    },\r\n    /**\r\n     * 收回授权密码框弹出框 - 关闭*/\r\n    dialogClose() {\r\n      this.dialog.visible = false\r\n    },\r\n    /**\r\n     * 收回授权密码框弹出框 - 提交*/\r\n    dialogSubmit() {\r\n      commonMsgConfirm('是否确认收回授权', this, (param) => {\r\n        if (param) {\r\n          // 先进行密码校验 再发起收回授权请求\r\n          const msg = {\r\n            parameterList: [],\r\n            oper_type: 'queryUser',\r\n            user_no: this.table.currentRow.target_user_no,\r\n            passWord: encryptResult(\r\n              this.$store.getters.initParams.enSecMap.encryptType,\r\n              this.dialog.form.defaultForm.password\r\n            )\r\n          }\r\n          // 密码校验\r\n          psdValid(msg)\r\n            .then((res) => {\r\n              if (res.retCode === '200') {\r\n                // 发起收回授权请求\r\n                const msg2 = {\r\n                  parameterList: [],\r\n                  user_no: this.$store.getters.userNo\r\n                }\r\n                callin(msg2)\r\n                  .then((res2) => {\r\n                    // 跳转到重新登录页面\r\n                    this.dialogVisible = false // 授权管理页面关闭\r\n                    this.dialog.visible = false // 密码框关闭\r\n                    this.Successdialog = true\r\n                    const interval = setInterval(() => {\r\n                      this.Sencond--\r\n                      if (this.Sencond === 0) {\r\n                        window.clearInterval(interval)\r\n                        this.logout() // 倒计时结束时跳转登录页面\r\n                      }\r\n                    }, 1000)\r\n                  })\r\n                  .catch((e) => {\r\n                    // console.log(e)\r\n                  })\r\n              }\r\n            })\r\n            .catch(() => {\r\n              commonMsgWarn('密码错误', this)\r\n            })\r\n        }\r\n      })\r\n    },\r\n    /**\r\n     * 跳转登录页面方法*/\r\n    async logout() {\r\n      await this.$store.dispatch('user/logout')\r\n      this.$router.push(`/login?redirect=${this.$route.fullPath}`)\r\n    },\r\n    /**\r\n     *页码更新 */\r\n    getList(pageParam) {\r\n      const { currentPage, pageSize } = pageParam\r\n      this.table.pageList.pageSize = pageSize\r\n      this.table.pageList.currentPage = currentPage\r\n      this.queryList()\r\n    }\r\n  }\r\n}\r\n</script>\r\n<style lang=\"scss\" scoped>\r\n.el-button.is-circle {\r\n  padding: 10px !important;\r\n}\r\n</style>\r\n"]}]}