{"remainingRequest": "D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\node_modules\\babel-loader\\lib\\index.js!D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\src\\views\\extend\\index.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\src\\views\\extend\\index.vue", "mtime": 1702370267380}, {"path": "D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\babel.config.js", "mtime": 1686019818359}, {"path": "D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\.babelrc", "mtime": 1705285078668}, {"path": "D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1655715099000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:aW1wb3J0ICJjb3JlLWpzL21vZHVsZXMvZXMuYXJyYXkuY29uY2F0LmpzIjsKaW1wb3J0ICJjb3JlLWpzL21vZHVsZXMvZXMuanNvbi5zdHJpbmdpZnkuanMiOwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwoKaW1wb3J0IHN0b3JlIGZyb20gJ0Avc3RvcmUnOwppbXBvcnQgeyBjb21tb25CbGFuayB9IGZyb20gJ0AvdXRpbHMvY29tbW9uJzsKZXhwb3J0IGRlZmF1bHQgewogIG5hbWU6ICdFeHRlbmQnLAogIGRhdGE6IGZ1bmN0aW9uIGRhdGEoKSB7CiAgICByZXR1cm4gewogICAgICBpZnJhbWVVcmw6ICcnLAogICAgICBleHRlbmRSZWY6ICdleHRlbmRSZWYnICsgbmV3IERhdGUoKS5nZXRUaW1lKCksCiAgICAgIGlzQ2hpbGRyZW5SZWFkeTogZmFsc2UKICAgIH07CiAgfSwKICB3YXRjaDogewogICAgaXNDaGlsZHJlblJlYWR5OiBmdW5jdGlvbiBpc0NoaWxkcmVuUmVhZHkodmFsKSB7CiAgICAgIGlmICh2YWwpIHsKICAgICAgICBjb25zb2xlLmxvZygnaXNDaGlsZHJlblJlYWR5JywgdmFsKTsKICAgICAgICB0aGlzLnBvc3RNZXNzKCk7CiAgICAgIH0KICAgIH0KICB9LAogIG1vdW50ZWQ6IGZ1bmN0aW9uIG1vdW50ZWQoKSB7CiAgICB2YXIgX3RoaXMgPSB0aGlzOwogICAgLy8gdGhpcy5wb3N0TWVzcygpCiAgICB0aGlzLiRuZXh0VGljaygpLnRoZW4oZnVuY3Rpb24gKCkgewogICAgICB2YXIgcGF0aEluZGV4ID0gX3RoaXMuJHJvdXRlLm1hdGNoZWQubGVuZ3RoIC0gMTsKICAgICAgdmFyIHJvdXRlTWVzc2FnZSA9IF90aGlzLiRyb3V0ZS5tYXRjaGVkW3BhdGhJbmRleF0ucHJvcHMuZGVmYXVsdDsKICAgICAgdmFyIHBhdGggPSByb3V0ZU1lc3NhZ2UucGF0aDsKICAgICAgLy8gY29uc29sZS5sb2coJ3JvdXRlJywgdGhpcy4kcm91dGUpCiAgICAgIC8vIGNvbnNvbGUubG9nKCdwYXRoJywgcGF0aCkKCiAgICAgIC8vIGh0dHA6Ly8xNzIuMS4xMS41MTo5NTI4L3JlZGlyZWN0L3JlcG9ydC8jL3JlcG9ydC9kYXRhU2V0CgogICAgICAvLyBodHRwOi8vMTcyLjEuMS4yMS9kb3BVbmlmeS9yZWRpcmVjdC9yZXBvcnQvIy9yZXBvcnQvZGF0YVNldAoKICAgICAgaWYgKCFjb21tb25CbGFuayhwYXRoKSkgewogICAgICAgIHN0b3JlLmNvbW1pdCgndXNlci9TRVRfUk9VVEVfTScsIHJvdXRlTWVzc2FnZSk7CiAgICAgICAgdmFyIHBhdGhVcmwgPSBwYXRoLnNwbGl0KCc6Jyk7IC8vIHJlZGlyZWN0Oi9yZXBvcnQvcmVwb3J0MQogICAgICAgIGlmIChwYXRoVXJsWzBdID09PSAncmVkaXJlY3QnKSB7CiAgICAgICAgICBpZiAocGF0aFVybFsxXS5pbmRleE9mKCdzdGF0aWMvaHRtbCcpICE9PSAtMSkgewogICAgICAgICAgICAvLyDlhbzlrrnogIHln7rnur9zdW5hb3MKICAgICAgICAgICAgX3RoaXMuaWZyYW1lVXJsID0gIiIuY29uY2F0KHdpbmRvdy5sb2NhdGlvbi5vcmlnaW4pLmNvbmNhdCh3aW5kb3cubG9jYXRpb24ucGF0aG5hbWUsICJyZWRpcmVjdC8iKS5jb25jYXQocGF0aFVybFsxXS5zdWJzdHJpbmcoMSkuc3BsaXQoJy8nKVswXSwgIi9pZnJhbWUuaHRtbCIpOwogICAgICAgICAgfSBlbHNlIHsKICAgICAgICAgICAgX3RoaXMuaWZyYW1lVXJsID0gIiIuY29uY2F0KHdpbmRvdy5sb2NhdGlvbi5vcmlnaW4pLmNvbmNhdCh3aW5kb3cubG9jYXRpb24ucGF0aG5hbWUsICJyZWRpcmVjdC8iKS5jb25jYXQocGF0aFVybFsxXS5zdWJzdHJpbmcoMSkuc3BsaXQoJy8nKVswXSwgIi8jLyIpLmNvbmNhdChwYXRoVXJsWzFdLnN1YnN0cmluZygxKSk7CiAgICAgICAgICB9CiAgICAgICAgfSBlbHNlIHsKICAgICAgICAgIF90aGlzLmlmcmFtZVVybCA9IHBhdGg7CiAgICAgICAgfQogICAgICAgIC8vIHRoaXMuZXh0ZW5kUmVmID0gdGhpcy5leHRlbmRSZWYgKyB0aGlzLiRyb3V0ZS5wYXRoCiAgICAgIH0KICAgICAgLy8gY29uc29sZS5sb2coJ3RoaXMuaWZyYW1lVXJsJywgdGhpcy5pZnJhbWVVcmwpCiAgICAgIF90aGlzLnBvc3RQYXJhbSgpOwogICAgfSk7CiAgfSwKICBtZXRob2RzOiB7CiAgICBwb3N0TWVzczogZnVuY3Rpb24gcG9zdE1lc3MoKSB7CiAgICAgIHZhciBtYXBGcmFtZSA9IHRoaXMuJHJlZnNbdGhpcy5leHRlbmRSZWZdOwogICAgICB2YXIgaWZyYW1lV2luID0gbWFwRnJhbWUuY29udGVudFdpbmRvdzsKICAgICAgdmFyIG1zZyA9IHsKICAgICAgICB1c2VyOiB0aGlzLiRzdG9yZS5zdGF0ZS51c2VyLAogICAgICAgIGNvbW1vbjogdGhpcy4kc3RvcmUuc3RhdGUuY29tbW9uLAogICAgICAgIHR5cGU6ICdzdW5SZXBvcnQnCiAgICAgIH07CiAgICAgIC8vIGNvbnNvbGUubG9nKCfniLbkvKDlrZDnmoTmlbDmja4nLCBKU09OLnN0cmluZ2lmeShtc2cpKQogICAgICBpZnJhbWVXaW4ucG9zdE1lc3NhZ2UoCiAgICAgIC8vIOWQkeWtkOW3peeoi+WPkemAgQogICAgICBKU09OLnBhcnNlKEpTT04uc3RyaW5naWZ5KG1zZykpLCBwcm9jZXNzLmVudi5OT0RFX0VOViA9PT0gJ2RldmVsb3BtZW50JyA/ICcqJyA6IHdpbmRvdy5sb2NhdGlvbi5vcmlnaW4pOwogICAgfSwKICAgIC8qKgogICAgICog5ZCRaWZyYW1l5a2Q6aG16Z2i5Y+R6YCB5raI5oGvCiAgICAgKi8KICAgIHBvc3RQYXJhbTogZnVuY3Rpb24gcG9zdFBhcmFtKCkgewogICAgICB2YXIgX3RoaXMyID0gdGhpczsKICAgICAgd2luZG93LmFkZEV2ZW50TGlzdGVuZXIoJ21lc3NhZ2UnLCBmdW5jdGlvbiAoZXZ0KSB7CiAgICAgICAgLy8g5o6l5pS25a2Q5bel56iL5pWw5o2uCiAgICAgICAgLy8g6Iul5a2Q6aG16Z2i5bey57uP5Yqg6L295aW96YCa55+l54i25bel56iL5L+u5pS55LqGaXNDaGlsZHJlblJlYWR555qE54q25oCBCiAgICAgICAgY29uc29sZS5sb2coJ+aOpeaUtuWtkOW3peeoi+aVsOaNricsIGV2dCk7CiAgICAgICAgaWYgKGV2dC5kYXRhLnR5cGUgPT09ICdzdW5SZXBvcnQnKSB7CiAgICAgICAgICBfdGhpczIuaXNDaGlsZHJlblJlYWR5ID0gdHJ1ZTsKICAgICAgICB9IGVsc2UgaWYgKGV2dC5kYXRhLnR5cGUgPT09ICdzYXZlRGF0YScpIHsKICAgICAgICAgIC8vIOWtkOW3peeoi+eahOaVsOaNruWtmOWCqAogICAgICAgICAgX3RoaXMyLiRzdG9yZS5jb21taXQoJ2NvbW1vbi9TRVRfU0FWRV9EQVRBJywgewogICAgICAgICAgICBrZXk6IGV2dC5kYXRhLnByb2plY3ROYW1lLAogICAgICAgICAgICBkYXRhOiBldnQuZGF0YS5kYXRhCiAgICAgICAgICB9KTsKCiAgICAgICAgICAvLyB0aGlzLiRwYXJlbnQuZGlhbG9nRmxvd0Nsb3NlKCkKICAgICAgICAgIC8vIHRoaXMuJHBhcmVudC5xdWVyeU1lbnUoKQogICAgICAgIH0KICAgICAgfSk7CiAgICB9CiAgfQp9Ow=="}, {"version": 3, "mappings": ";;;;;;;;;;;;;;;AAaA;AACA;AACA;EACAA;EACAC;IACA;MACAC;MACAC;MACAC;IACA;EACA;EACAC;IACAD;MACA;QACAE;QACA;MACA;IACA;EACA;EACAC;IAAA;IACA;IACA;MACA;MACA;MACA;MACA;MACA;;MAEA;;MAEA;;MAEA;QACAC;QACA;QACA;UACA;YACA;YACA,2DACAC,8CACAC;UACA;YACA,2DACAD,8CAEAC,qDACAA;UACA;QACA;UACA;QACA;QACA;MACA;MACA;MACA;IACA;EACA;EACAC;IACAC;MACA;MACA;MACA;QACAC;QACAC;QACAC;MACA;MACA;MACAC;MACA;MACAC,iCACAC,sEACA;IACA;IACA;AACA;AACA;IACAC;MAAA;MACAV;QACA;QACA;QACAH;QACA;UACA;QACA;UACA;UACA;YACAc;YACAnB;UACA;;UAEA;UACA;QACA;MACA;IACA;EACA;AACA", "names": ["name", "data", "iframeUrl", "extendRef", "is<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "watch", "console", "mounted", "store", "window", "pathUrl", "methods", "postMess", "user", "common", "type", "iframeWin", "JSON", "process", "postParam", "key"], "sourceRoot": "src/views/extend", "sources": ["index.vue"], "sourcesContent": ["<!--\n* 外系统代码入口\n-->\n<template>\n  <iframe\n    :ref=\"extendRef\"\n    class=\"extendIframe\"\n    :src=\"iframeUrl\"\n    frameborder=\"0\"\n  />\n</template>\n\n<script>\nimport store from '@/store'\nimport { commonBlank } from '@/utils/common'\nexport default {\n  name: 'Extend',\n  data() {\n    return {\n      iframeUrl: '',\n      extendRef: 'extendRef' + new Date().getTime(),\n      isChildrenReady: false\n    }\n  },\n  watch: {\n    isChildrenReady(val) {\n      if (val) {\n        console.log('isChildrenReady', val)\n        this.postMess()\n      }\n    }\n  },\n  mounted() {\n    // this.postMess()\n    this.$nextTick().then(() => {\n      const pathIndex = this.$route.matched.length - 1\n      const routeMessage = this.$route.matched[pathIndex].props.default\n      const path = routeMessage.path\n      // console.log('route', this.$route)\n      // console.log('path', path)\n\n      // http://***********:9528/redirect/report/#/report/dataSet\n\n      // http://**********/dopUnify/redirect/report/#/report/dataSet\n\n      if (!commonBlank(path)) {\n        store.commit('user/SET_ROUTE_M', routeMessage)\n        const pathUrl = path.split(':') // redirect:/report/report1\n        if (pathUrl[0] === 'redirect') {\n          if (pathUrl[1].indexOf('static/html') !== -1) {\n            // 兼容老基线sunaos\n            this.iframeUrl = `${window.location.origin}${\n              window.location.pathname\n            }redirect/${pathUrl[1].substring(1).split('/')[0]}/iframe.html`\n          } else {\n            this.iframeUrl = `${window.location.origin}${\n              window.location.pathname\n            }redirect/${\n              pathUrl[1].substring(1).split('/')[0]\n            }/#/${pathUrl[1].substring(1)}`\n          }\n        } else {\n          this.iframeUrl = path\n        }\n        // this.extendRef = this.extendRef + this.$route.path\n      }\n      // console.log('this.iframeUrl', this.iframeUrl)\n      this.postParam()\n    })\n  },\n  methods: {\n    postMess() {\n      const mapFrame = this.$refs[this.extendRef]\n      const iframeWin = mapFrame.contentWindow\n      const msg = {\n        user: this.$store.state.user,\n        common: this.$store.state.common,\n        type: 'sunReport'\n      }\n      // console.log('父传子的数据', JSON.stringify(msg))\n      iframeWin.postMessage(\n        // 向子工程发送\n        JSON.parse(JSON.stringify(msg)),\n        process.env.NODE_ENV === 'development' ? '*' : window.location.origin\n      )\n    },\n    /**\n     * 向iframe子页面发送消息\n     */\n    postParam() {\n      window.addEventListener('message', (evt) => {\n        // 接收子工程数据\n        // 若子页面已经加载好通知父工程修改了isChildrenReady的状态\n        console.log('接收子工程数据', evt)\n        if (evt.data.type === 'sunReport') {\n          this.isChildrenReady = true\n        } else if (evt.data.type === 'saveData') {\n          // 子工程的数据存储\n          this.$store.commit('common/SET_SAVE_DATA', {\n            key: evt.data.projectName,\n            data: evt.data.data\n          })\n\n          // this.$parent.dialogFlowClose()\n          // this.$parent.queryMenu()\n        }\n      })\n    }\n  }\n}\n</script>\n\n<style lang=\"scss\" scoped>\n.extendIframe {\n  width: 100%;\n  height: 100%;\n  position: absolute;\n  top: 0;\n  bottom: 0;\n  overflow: hidden;\n}\n</style>\n"]}]}