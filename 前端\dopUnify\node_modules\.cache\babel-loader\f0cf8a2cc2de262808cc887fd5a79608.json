{"remainingRequest": "D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\node_modules\\thread-loader\\dist\\cjs.js!D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\node_modules\\babel-loader\\lib\\index.js!D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\node_modules\\sunui\\srcobf\\directive\\el-drag-dialog\\drag.js", "dependencies": [{"path": "D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\node_modules\\sunui\\srcobf\\directive\\el-drag-dialog\\drag.js", "mtime": 499162500000}, {"path": "D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\babel.config.js", "mtime": 1686019818359}, {"path": "D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\node_modules\\thread-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, null]}