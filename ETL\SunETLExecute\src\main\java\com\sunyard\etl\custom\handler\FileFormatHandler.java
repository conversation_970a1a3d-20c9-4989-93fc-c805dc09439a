package com.sunyard.etl.custom.handler;

import org.springframework.stereotype.Service;

import com.sunyard.etl.custom.service.FileFormatService;
import com.sunyard.etl.system.dao.DataDateDAO;
import com.sunyard.etl.system.dao.impl.DataDateDAOImpl;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.IJobHandler;
import com.xxl.job.core.handler.annotation.JobHandler;
import com.xxl.job.core.log.XxlJobLogger;


@JobHandler(value="fileFormat",name = "流水文件格式化")
@Service
public class FileFormatHandler extends IJobHandler{
	
	private static final long serialVersionUID = 1L;
	
	private static DataDateDAO dateDao = new DataDateDAOImpl();
	private static FileFormatService fileFormatService = new FileFormatService();

	
	@Override
	public ReturnT<String> execute(String jobId, String... arg1) throws Exception {
		XxlJobLogger.log("开始流水文件格式化...");
		String jobDate = dateDao.getDataDate();
		if (null != arg1[0]) {
			String dirPath = arg1[0].toString().replace("@", jobDate);
			fileFormatService.fileNameFormat(dirPath);// 文件名格式化
			return ReturnT.SUCCESS;
		}
		return ReturnT.FAIL;
	}
}
