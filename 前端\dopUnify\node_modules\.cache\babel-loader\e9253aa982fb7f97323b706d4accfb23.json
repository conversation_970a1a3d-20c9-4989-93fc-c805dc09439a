{"remainingRequest": "E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1\\数字运营平台-统一门户工程\\dopUnify\\node_modules\\babel-loader\\lib\\index.js!E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1\\数字运营平台-统一门户工程\\dopUnify\\node_modules\\eslint-loader\\index.js??ref--13-0!E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1\\数字运营平台-统一门户工程\\dopUnify\\src\\api\\views\\home\\component\\notice.js", "dependencies": [{"path": "E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1\\数字运营平台-统一门户工程\\dopUnify\\src\\api\\views\\home\\component\\notice.js", "mtime": 1716875179858}, {"path": "E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1\\数字运营平台-统一门户工程\\dopUnify\\babel.config.js", "mtime": 1716875191548}, {"path": "E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1\\数字运营平台-统一门户工程\\dopUnify\\.babelrc", "mtime": 1716875191502}, {"path": "E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1\\数字运营平台-统一门户工程\\dopUnify\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1\\数字运营平台-统一门户工程\\dopUnify\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1\\数字运营平台-统一门户工程\\dopUnify\\node_modules\\eslint-loader\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:aW1wb3J0IHJlcXVlc3QgZnJvbSAnQC91dGlscy9yZXF1ZXN0JzsKaW1wb3J0IGRlZmF1bHRTZXR0aW5ncyBmcm9tICdAL3NldHRpbmdzJzsKdmFyIHByZWZpeCA9IGRlZmF1bHRTZXR0aW5ncy5zZXJ2aWNlLnN5c3RlbTsgLy8g5YmN57yA5YWs5YWx6Lev55SxCgpleHBvcnQgdmFyIE5vdGljZSA9IHsKICAvKioNCiAgICog5Li76aG15p+l6K+iDQogICAqLwogIE5vdGljZVF1ZXJ5OiBmdW5jdGlvbiBOb3RpY2VRdWVyeShkYXRhKSB7CiAgICByZXR1cm4gcmVxdWVzdCh7CiAgICAgIHVybDogcHJlZml4ICsgJy9ub3RpY2UvaW5pdEluZGV4UGFnZS5kbycsCiAgICAgIG1ldGhvZDogJ2dldCcsCiAgICAgIHBhcmFtczogewogICAgICAgIG1lc3NhZ2U6IGRhdGEKICAgICAgfQogICAgfSk7CiAgfSwKICAvLyDmn6Xor6LpmIXor7vph48KICByZWFkTnVtOiBmdW5jdGlvbiByZWFkTnVtKGRhdGEpIHsKICAgIHJldHVybiByZXF1ZXN0KHsKICAgICAgdXJsOiBwcmVmaXggKyAnL25vdGljZS9yZWFkTnVtLmRvJywKICAgICAgbWV0aG9kOiAnZ2V0JywKICAgICAgcGFyYW1zOiB7CiAgICAgICAgbWVzc2FnZTogZGF0YQogICAgICB9CiAgICB9KTsKICB9LAogIG5vdGljZU1vZGlmeTogZnVuY3Rpb24gbm90aWNlTW9kaWZ5KGRhdGEpIHsKICAgIC8vIOS/ruaUueWFrOWRiumYheivu+eKtuaAgQogICAgcmV0dXJuIHJlcXVlc3QoewogICAgICB1cmw6IHByZWZpeCArICcvbm90aWNlL21vZGlmeS5kbycsCiAgICAgIG1ldGhvZDogJ3Bvc3QnLAogICAgICBkYXRhOiBkYXRhCiAgICB9KTsKICB9Cn07"}, {"version": 3, "names": ["request", "defaultSettings", "prefix", "service", "system", "Notice", "NoticeQuery", "data", "url", "method", "params", "message", "readNum", "noticeModify"], "sources": ["E:/1_Project/XYD_Project/dop-4.0/dop-4.1/数字运营平台-统一门户工程/dopUnify/src/api/views/home/<USER>/notice.js"], "sourcesContent": ["import request from '@/utils/request'\r\nimport defaultSettings from '@/settings'\r\nconst prefix = defaultSettings.service.system // 前缀公共路由\r\n\r\nexport const Notice = {\r\n  /**\r\n   * 主页查询\r\n   */\r\n  NoticeQuery(data) {\r\n    return request({\r\n      url: prefix + '/notice/initIndexPage.do',\r\n      method: 'get',\r\n      params: { message: data }\r\n    })\r\n  },\r\n  // 查询阅读量\r\n  readNum(data) {\r\n    return request({\r\n      url: prefix + '/notice/readNum.do',\r\n      method: 'get',\r\n      params: { message: data }\r\n    })\r\n  },\r\n  noticeModify(data) {\r\n    // 修改公告阅读状态\r\n    return request({\r\n      url: prefix + '/notice/modify.do',\r\n      method: 'post',\r\n      data\r\n    })\r\n  }\r\n}\r\n"], "mappings": "AAAA,OAAOA,OAAO,MAAM,iBAAiB;AACrC,OAAOC,eAAe,MAAM,YAAY;AACxC,IAAMC,MAAM,GAAGD,eAAe,CAACE,OAAO,CAACC,MAAM,EAAC;;AAE9C,OAAO,IAAMC,MAAM,GAAG;EACpB;AACF;AACA;EACEC,WAAW,uBAACC,IAAI,EAAE;IAChB,OAAOP,OAAO,CAAC;MACbQ,GAAG,EAAEN,MAAM,GAAG,0BAA0B;MACxCO,MAAM,EAAE,KAAK;MACbC,MAAM,EAAE;QAAEC,OAAO,EAAEJ;MAAK;IAC1B,CAAC,CAAC;EACJ,CAAC;EACD;EACAK,OAAO,mBAACL,IAAI,EAAE;IACZ,OAAOP,OAAO,CAAC;MACbQ,GAAG,EAAEN,MAAM,GAAG,oBAAoB;MAClCO,MAAM,EAAE,KAAK;MACbC,MAAM,EAAE;QAAEC,OAAO,EAAEJ;MAAK;IAC1B,CAAC,CAAC;EACJ,CAAC;EACDM,YAAY,wBAACN,IAAI,EAAE;IACjB;IACA,OAAOP,OAAO,CAAC;MACbQ,GAAG,EAAEN,MAAM,GAAG,mBAAmB;MACjCO,MAAM,EAAE,MAAM;MACdF,IAAI,EAAJA;IACF,CAAC,CAAC;EACJ;AACF,CAAC"}]}