{"remainingRequest": "D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\node_modules\\babel-loader\\lib\\index.js!D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\src\\views\\system\\notice\\query\\index.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\src\\views\\system\\notice\\query\\index.vue", "mtime": 1686019808654}, {"path": "D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\babel.config.js", "mtime": 1686019818359}, {"path": "D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\.babelrc", "mtime": 1705285078668}, {"path": "D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1655715099000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "mappings": ";;;;;;;;;;;;;;;;;;;;;;;AAuBA;AACA;AACA;;AAEA;EACAA;EACAC;IAAAC;EAAA;EACAC;IACA;MACAC;MACAC;QACAC;MACA;;MACAC;QACA;QACAC;MACA;MACAC;IACA;EACA;EACAC;IACA;EACA;EACAC;IAAA;IACA;IACA;MACA;QACA;MACA;IACA;EACA;EACAC;IACA;AACA;IACAC;MACA;IACA;IACA;AACA;AACA;IACAC;MACA;QACA;MACA;QACA;MACA;IACA;IACA;AACA;IACAC;MACA;IACA;IACA;IACAC;MACA,sBACA;MACA;IACA;EACA;AACA", "names": ["name", "components", "TableList", "data", "config", "defaultForm", "key<PERSON>ord", "btnAll", "btnQuery", "boxSecondHeight", "created", "mounted", "methods", "btnPermissions", "validateForm", "queryList", "getHeight"], "sourceRoot": "src/views/system/notice/query", "sources": ["index.vue"], "sourcesContent": ["<template>\r\n  <div ref=\"boxRef\" class=\"app-container\">\r\n    <div ref=\"boxFirstRef\" class=\"sun-content\">\r\n      <div class=\"filter-container\">\r\n        <sun-form\r\n          ref=\"formRef\"\r\n          :config=\"config\"\r\n          :default-form=\"defaultForm\"\r\n          :query=\"btnAll.btnQuery\"\r\n          @query=\"queryList\"\r\n          @validateForm=\"validateForm\"\r\n        />\r\n      </div>\r\n    </div>\r\n    <table-list\r\n      ref=\"tableListRef\"\r\n      :default-form=\"defaultForm\"\r\n      :btn-all=\"btnAll\"\r\n      :height=\"boxSecondHeight\"\r\n    />\r\n  </div>\r\n</template>\r\n<script>\r\nimport { config } from './info' // 表单配置\r\nimport TableList from './component/table' // 表格\r\nimport { permissionsBtn } from '@/utils/permissions' // 权限配置\r\n\r\nexport default {\r\n  name: 'Query',\r\n  components: { TableList },\r\n  data() {\r\n    return {\r\n      config: config(this),\r\n      defaultForm: {\r\n        keyWord: '' // 公告关键字\r\n      },\r\n      btnAll: {\r\n        // 当前页需要配置权限的按钮  权限获取\r\n        btnQuery: false\r\n      },\r\n      boxSecondHeight: 0 // 下方容器高度\r\n    }\r\n  },\r\n  created() {\r\n    this.btnPermissions()\r\n  },\r\n  mounted() {\r\n    this.getHeight()\r\n    this.$nextTick().then(() => {\r\n      if (this.btnAll.btnQuery) {\r\n        this.queryList()\r\n      }\r\n    })\r\n  },\r\n  methods: {\r\n    /**\r\n     * 按钮权限配置*/\r\n    btnPermissions() {\r\n      this.btnAll = permissionsBtn(this.$attrs.button_id, this.btnAll)\r\n    },\r\n    /**\r\n     * 表单校验\r\n     * @param {Boolean}valid 校验返回值*/\r\n    validateForm(valid) {\r\n      if (valid) {\r\n        this.$refs.tableListRef.queryList(1)\r\n      } else {\r\n        return false\r\n      }\r\n    },\r\n    /**\r\n     * 按钮：查询*/\r\n    queryList() {\r\n      this.$refs['formRef'].validateForm()\r\n    },\r\n    // 获取容器高度\r\n    getHeight() {\r\n      const boxSecondHeight =\r\n        this.$refs.boxRef.clientHeight - this.$refs.boxFirstRef.clientHeight\r\n      this.boxSecondHeight = boxSecondHeight\r\n    }\r\n  }\r\n}\r\n</script>\r\n<style scoped>\r\n.app-container {\r\n  width: 100%;\r\n  height: 100%;\r\n}\r\n</style>\r\n"]}]}