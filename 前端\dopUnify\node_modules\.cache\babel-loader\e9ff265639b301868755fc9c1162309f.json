{"remainingRequest": "D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\node_modules\\babel-loader\\lib\\index.js!D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\node_modules\\eslint-loader\\index.js??ref--13-0!D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\src\\views\\system\\dataAuditing\\info.js", "dependencies": [{"path": "D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\src\\views\\system\\dataAuditing\\info.js", "mtime": 1686019808310}, {"path": "D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\babel.config.js", "mtime": 1686019818359}, {"path": "D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\node_modules\\eslint-loader\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["dictionaryFieds", "config", "that", "query_type", "component", "label", "colSpan", "name", "componentProps", "placeholder", "options", "methods", "change", "val", "defaultForm", "approve_type", "approve_agree", "clearable", "inst_id", "oper_type_flag", "apply_user", "disabled", "date_two", "type", "rangeSeparator", "startPlaceholder", "endPlaceholder", "valueFormat", "editable"], "sources": ["D:/1_Project/XYD_Project/dop-4.0/dop-4.1-qianduan-unify/dopUnify/src/views/system/dataAuditing/info.js"], "sourcesContent": ["import { dictionaryFieds } from '@/utils/dictionary' // 字典配置\r\n\r\n// 表单\r\nexport const config = (that) => ({\r\n  query_type: {\r\n    component: 'select',\r\n    label: '查询类型',\r\n    colSpan: 8,\r\n    name: 'query_type',\r\n    componentProps: {\r\n      // input组件配置\r\n      placeholder: '请选择'\r\n    },\r\n    options: dictionaryFieds('SM_QUERY_TYPE'),\r\n    methods: {\r\n      change(val) {\r\n        that.defaultForm.query_type = val\r\n      }\r\n    }\r\n  },\r\n  approve_type: {\r\n    component: 'select',\r\n    label: '终审状态',\r\n    colSpan: 8,\r\n    name: 'approve_type',\r\n    componentProps: {\r\n      placeholder: '请选择'\r\n    },\r\n    options: dictionaryFieds('SM_APPROVE_TYPEII'),\r\n    methods: {\r\n      change(val) {\r\n        that.defaultForm.approve_type = val\r\n      }\r\n    }\r\n  },\r\n  approve_agree: {\r\n    component: 'select',\r\n    label: '终审结果',\r\n    colSpan: 8,\r\n    name: 'approve_agree',\r\n    componentProps: {\r\n      placeholder: '请选择',\r\n      clearable: true\r\n    },\r\n    options: dictionaryFieds('SM_APPROVE_AGREE'),\r\n    methods: {\r\n      change(val) {\r\n        that.defaultForm.approve_agree = val\r\n      }\r\n    }\r\n  },\r\n  inst_id: {\r\n    component: 'input',\r\n    label: '申请流水号',\r\n    colSpan: 8,\r\n    name: 'inst_id',\r\n    componentProps: {\r\n      clearable: true,\r\n      placeholder: '支持申请流水号模糊查询'\r\n    }\r\n  },\r\n  oper_type_flag: {\r\n    component: 'input',\r\n    label: '申请操作',\r\n    colSpan: 8,\r\n    name: 'oper_type_flag',\r\n    componentProps: {\r\n      placeholder: '支持申请操作模糊查询',\r\n      clearable: true\r\n    }\r\n    // options: dictionaryFieds('SM_OPERATION_TYPE')\r\n  },\r\n  apply_user: {\r\n    component: 'input',\r\n    label: '申请用户',\r\n    colSpan: 8,\r\n    name: 'apply_user',\r\n    componentProps: {\r\n      placeholder: '支持申请用户模糊查询',\r\n      clearable: true,\r\n      disabled: false\r\n    },\r\n    options: dictionaryFieds('SM_OPERATION_TYPE')\r\n  },\r\n  date_two: {\r\n    component: 'date-picker',\r\n    label: '申请日期',\r\n    colSpan: 8,\r\n    name: 'date_two',\r\n    // config: {},\r\n    componentProps: {\r\n      type: 'daterange',\r\n      rangeSeparator: '-',\r\n      startPlaceholder: '开始日期',\r\n      endPlaceholder: '结束日期',\r\n      valueFormat: 'yyyyMMdd',\r\n      editable: false\r\n      // type: 'year'\r\n    }\r\n  }\r\n})\r\n"], "mappings": "AAAA,SAASA,eAAe,QAAQ,oBAAoB,EAAC;;AAErD;AACA,OAAO,IAAMC,MAAM,GAAG,SAATA,MAAM,CAAIC,IAAI;EAAA,OAAM;IAC/BC,UAAU,EAAE;MACVC,SAAS,EAAE,QAAQ;MACnBC,KAAK,EAAE,MAAM;MACbC,OAAO,EAAE,CAAC;MACVC,IAAI,EAAE,YAAY;MAClBC,cAAc,EAAE;QACd;QACAC,WAAW,EAAE;MACf,CAAC;MACDC,OAAO,EAAEV,eAAe,CAAC,eAAe,CAAC;MACzCW,OAAO,EAAE;QACPC,MAAM,kBAACC,GAAG,EAAE;UACVX,IAAI,CAACY,WAAW,CAACX,UAAU,GAAGU,GAAG;QACnC;MACF;IACF,CAAC;IACDE,YAAY,EAAE;MACZX,SAAS,EAAE,QAAQ;MACnBC,KAAK,EAAE,MAAM;MACbC,OAAO,EAAE,CAAC;MACVC,IAAI,EAAE,cAAc;MACpBC,cAAc,EAAE;QACdC,WAAW,EAAE;MACf,CAAC;MACDC,OAAO,EAAEV,eAAe,CAAC,mBAAmB,CAAC;MAC7CW,OAAO,EAAE;QACPC,MAAM,kBAACC,GAAG,EAAE;UACVX,IAAI,CAACY,WAAW,CAACC,YAAY,GAAGF,GAAG;QACrC;MACF;IACF,CAAC;IACDG,aAAa,EAAE;MACbZ,SAAS,EAAE,QAAQ;MACnBC,KAAK,EAAE,MAAM;MACbC,OAAO,EAAE,CAAC;MACVC,IAAI,EAAE,eAAe;MACrBC,cAAc,EAAE;QACdC,WAAW,EAAE,KAAK;QAClBQ,SAAS,EAAE;MACb,CAAC;MACDP,OAAO,EAAEV,eAAe,CAAC,kBAAkB,CAAC;MAC5CW,OAAO,EAAE;QACPC,MAAM,kBAACC,GAAG,EAAE;UACVX,IAAI,CAACY,WAAW,CAACE,aAAa,GAAGH,GAAG;QACtC;MACF;IACF,CAAC;IACDK,OAAO,EAAE;MACPd,SAAS,EAAE,OAAO;MAClBC,KAAK,EAAE,OAAO;MACdC,OAAO,EAAE,CAAC;MACVC,IAAI,EAAE,SAAS;MACfC,cAAc,EAAE;QACdS,SAAS,EAAE,IAAI;QACfR,WAAW,EAAE;MACf;IACF,CAAC;IACDU,cAAc,EAAE;MACdf,SAAS,EAAE,OAAO;MAClBC,KAAK,EAAE,MAAM;MACbC,OAAO,EAAE,CAAC;MACVC,IAAI,EAAE,gBAAgB;MACtBC,cAAc,EAAE;QACdC,WAAW,EAAE,YAAY;QACzBQ,SAAS,EAAE;MACb;MACA;IACF,CAAC;;IACDG,UAAU,EAAE;MACVhB,SAAS,EAAE,OAAO;MAClBC,KAAK,EAAE,MAAM;MACbC,OAAO,EAAE,CAAC;MACVC,IAAI,EAAE,YAAY;MAClBC,cAAc,EAAE;QACdC,WAAW,EAAE,YAAY;QACzBQ,SAAS,EAAE,IAAI;QACfI,QAAQ,EAAE;MACZ,CAAC;MACDX,OAAO,EAAEV,eAAe,CAAC,mBAAmB;IAC9C,CAAC;IACDsB,QAAQ,EAAE;MACRlB,SAAS,EAAE,aAAa;MACxBC,KAAK,EAAE,MAAM;MACbC,OAAO,EAAE,CAAC;MACVC,IAAI,EAAE,UAAU;MAChB;MACAC,cAAc,EAAE;QACde,IAAI,EAAE,WAAW;QACjBC,cAAc,EAAE,GAAG;QACnBC,gBAAgB,EAAE,MAAM;QACxBC,cAAc,EAAE,MAAM;QACtBC,WAAW,EAAE,UAAU;QACvBC,QAAQ,EAAE;QACV;MACF;IACF;EACF,CAAC;AAAA,CAAC"}]}