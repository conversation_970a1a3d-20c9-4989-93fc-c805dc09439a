{"remainingRequest": "D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\node_modules\\thread-loader\\dist\\cjs.js!D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\node_modules\\babel-loader\\lib\\index.js!D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\src\\layout\\components\\SublicenseMan\\index.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\src\\layout\\components\\SublicenseMan\\index.vue", "mtime": 1703583640612}, {"path": "D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\babel.config.js", "mtime": 1686019818359}, {"path": "D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\.babelrc", "mtime": 1705285078668}, {"path": "D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\node_modules\\thread-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1655715099000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:aW1wb3J0IF9yZWdlbmVyYXRvclJ1bnRpbWUgZnJvbSAiRDovMV9Qcm9qZWN0L1hZRF9Qcm9qZWN0L2RvcC00LjAvZG9wLTQuMS1xaWFuZHVhbi11bmlmeS9kb3BVbmlmeS9ub2RlX21vZHVsZXMvQGJhYmVsL3J1bnRpbWUvaGVscGVycy9lc20vcmVnZW5lcmF0b3JSdW50aW1lLmpzIjsKaW1wb3J0IF9hc3luY1RvR2VuZXJhdG9yIGZyb20gIkQ6LzFfUHJvamVjdC9YWURfUHJvamVjdC9kb3AtNC4wL2RvcC00LjEtcWlhbmR1YW4tdW5pZnkvZG9wVW5pZnkvbm9kZV9tb2R1bGVzL0BiYWJlbC9ydW50aW1lL2hlbHBlcnMvZXNtL2FzeW5jVG9HZW5lcmF0b3IuanMiOwppbXBvcnQgImNvcmUtanMvbW9kdWxlcy9lcy5vYmplY3QudG8tc3RyaW5nLmpzIjsKaW1wb3J0ICJjb3JlLWpzL21vZHVsZXMvd2ViLmRvbS1jb2xsZWN0aW9ucy5mb3ItZWFjaC5qcyI7CmltcG9ydCAiY29yZS1qcy9tb2R1bGVzL2VzLmFycmF5Lm1hcC5qcyI7CmltcG9ydCAiY29yZS1qcy9tb2R1bGVzL2VzLmFycmF5LmpvaW4uanMiOwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwoKaW1wb3J0IHsgY29uZmlnLCBjb25maWdUYWJsZSwgY29uZmlnRGV0YWlsVGFibGUsIGNvbmZpZ1BzZCB9IGZyb20gJy4vaW5mbyc7IC8vIOihqOWktOOAgeihqOWNlemFjee9rgppbXBvcnQgeyBlbmNyeXB0UmVzdWx0IH0gZnJvbSAnQC91dGlscy9jcnlwdG8nOyAvLyDliqDlr4YKaW1wb3J0IHsgZGljdGlvbmFyeUZpZWRzIH0gZnJvbSAnQC91dGlscy9kaWN0aW9uYXJ5LmpzJzsgLy8g5a2X5YW45bi46YePCmltcG9ydCB7IGRhdGU4Rm9ybWF0IH0gZnJvbSAnQC9maWx0ZXJzJzsKaW1wb3J0IHsgY29tbW9uQmxhbmsgfSBmcm9tICdAL3V0aWxzL2NvbW1vbic7CmltcG9ydCB7IGNvbW1vbk1zZ1N1Y2Nlc3MsIGNvbW1vbk1zZ1dhcm4sIGNvbW1vbk1zZ0Vycm9yLCBjb21tb25Nc2dDb25maXJtIH0gZnJvbSAnQC91dGlscy9tZXNzYWdlLmpzJzsgLy8g5o+Q56S65L+h5oGvCmltcG9ydCB7IENvbW1vbiB9IGZyb20gJ0AvYXBpJzsKdmFyIHN1YmxpY2Vuc2VNYW4gPSBDb21tb24uc3VibGljZW5zZU1hbiwKICByZXZva2UgPSBDb21tb24ucmV2b2tlLAogIHF1ZXJ5U3ViTGljZW5zZSA9IENvbW1vbi5xdWVyeVN1YkxpY2Vuc2UsCiAgcHNkVmFsaWQgPSBDb21tb24ucHNkVmFsaWQsCiAgY2FsbGluID0gQ29tbW9uLmNhbGxpbiwKICBzZWxlY3RGbG93RGV0YWlsRGF0YSA9IENvbW1vbi5zZWxlY3RGbG93RGV0YWlsRGF0YTsKZXhwb3J0IGRlZmF1bHQgewogIG5hbWU6ICdTdWJsaWNlbnNlTWFuJywKICBkYXRhOiBmdW5jdGlvbiBkYXRhKCkgewogICAgcmV0dXJuIHsKICAgICAgZGlhbG9nVmlzaWJsZTogZmFsc2UsCiAgICAgIGJ0bkFsbDogewogICAgICAgIC8vIOW9k+WJjemhtemcgOimgemFjee9ruadg+mZkOeahOaMiemSriAg5p2D6ZmQ6I635Y+WCiAgICAgICAgYnRuUXVlcnk6IHRydWUKICAgICAgfSwKICAgICAgcmVkQ29sb3I6IHsKICAgICAgICBjb2xvcjogJ3JlZCcKICAgICAgfSwKICAgICAgYmx1ZUNvbG9yOiB7CiAgICAgICAgY29sb3I6ICdibHVlJwogICAgICB9LAogICAgICBncmVlbkNvbG9yOiB7CiAgICAgICAgY29sb3I6ICcjMDA4MDAwJwogICAgICB9LAogICAgICBidG5EYXRhczogewogICAgICAgIC8vIOaMiemSrumFjee9rgogICAgICAgIGJ0blJldm9rZTogewogICAgICAgICAgc2hvdzogdHJ1ZSAvLyDmkqTplIAKICAgICAgICB9LAoKICAgICAgICBidG5DYWxsaW46IHsKICAgICAgICAgIHNob3c6IHRydWUgLy8g5pS25ZueCiAgICAgICAgfQogICAgICB9LAoKICAgICAgY29uZmlnOiBjb25maWcodGhpcyksCiAgICAgIGRlZmF1bHRGb3JtOiB7CiAgICAgICAgcXVlcnlfdHlwZTogJzAnLAogICAgICAgIGFwcGx5X3VzZXI6ICcnLAogICAgICAgIHRhcmdldF91c2VyOiAnJywKICAgICAgICByZWFzb246ICcnLAogICAgICAgIGlzX3JldGFrZTogJycsCiAgICAgICAgc3RhcnRfYXBwbHlfdGltZTogJycsCiAgICAgICAgZW5kX2FwcGx5X3RpbWU6ICcnCiAgICAgIH0sCiAgICAgIHRhYmxlOiB7CiAgICAgICAgLy8g6KGo5qC86YWN572uCiAgICAgICAgdGFibGVDb2x1bW5zOiBjb25maWdUYWJsZSgpLAogICAgICAgIC8vIOihqOWktOmFjee9rgogICAgICAgIHJlZjogJ3RhYmxlUmVmJywKICAgICAgICBzZWxlY3Rpb246IGZhbHNlLAogICAgICAgIC8vIOWkjemAiQogICAgICAgIGluZGV4TnVtYmVyOiB0cnVlLAogICAgICAgIC8vIOW6j+WPtwogICAgICAgIGxvYWRpbmc6IGZhbHNlLAogICAgICAgIGNvbXBvbmVudFByb3BzOiB7CiAgICAgICAgICBkYXRhOiBbXSwKICAgICAgICAgIC8vIOihqOagvOaVsOaNrgogICAgICAgICAgaGVpZ2h0OiAnMjEwcHgnLAogICAgICAgICAgZm9ybVJvdzogMyAvLyDooajljZXooYzmlbAKICAgICAgICB9LAoKICAgICAgICBwYWdlTGlzdDogewogICAgICAgICAgdG90YWxOdW06IDAsCiAgICAgICAgICBjdXJyZW50UGFnZTogMSwKICAgICAgICAgIC8vIOW9k+WJjemhtQogICAgICAgICAgcGFnZVNpemU6IHRoaXMuJHN0b3JlLmdldHRlcnMucGFnZVNpemUgLy8g5b2T5YmN6aG15pi+56S65p2h5pWwCiAgICAgICAgfSwKCiAgICAgICAgY3VycmVudFJvdzogW10gLy8g6YCJ5Lit6KGMCiAgICAgIH0sCgogICAgICBkaWFsb2c6IHsKICAgICAgICAvLyDnm67moIfnlKjmiLflr4bnoIHlvLnmoYYKICAgICAgICBjb21wb25lbnRQcm9wczogewogICAgICAgICAgLy8g5by55Ye65qGG6YWN572u5bGe5oCnCiAgICAgICAgICB3aWR0aDogJzUwcmVtJywKICAgICAgICAgIHRpdGxlOiAn5a+G56CB5qCh6aqMJwogICAgICAgIH0sCiAgICAgICAgdmlzaWJsZTogZmFsc2UsCiAgICAgICAgZm9ybTogewogICAgICAgICAgY29uZmlnOiBjb25maWdQc2QodGhpcyksCiAgICAgICAgICBsYWJlbFdpZHRoOiAnMTRyZW0nLAogICAgICAgICAgLy8g5b2T5YmN6KGo5Y2V5qCH562+5a695bqm6YWN572uCiAgICAgICAgICBkZWZhdWx0Rm9ybTogewogICAgICAgICAgICBwYXNzd29yZDogdGhpcy4kc3RvcmUuZ2V0dGVycy5lbXB0eVBzd2QgLy8g55uu5qCH55So5oi35a+G56CBCiAgICAgICAgICB9CiAgICAgICAgfQogICAgICB9LAoKICAgICAgZGlhbG9nRGV0YWlsOiB7CiAgICAgICAgLy8g5rWB5rC05Y+35a6h5om56K+m5oOFCiAgICAgICAgdmlzaWJsZTogZmFsc2UsCiAgICAgICAgLy8g5pi+56S66ZqQ6JeP6YWN572uCiAgICAgICAgYnRuU3VibWl0OiBmYWxzZSwKICAgICAgICAvLyDnoa7lrprmjInpkq4KICAgICAgICBidG5DYW5jbGU6IGZhbHNlLAogICAgICAgIC8vIOWPlua2iOaMiemSrgogICAgICAgIGNvbXBvbmVudFByb3BzOiB7CiAgICAgICAgICAvLyDlvLnlh7rmoYblsZ7mgKcKICAgICAgICAgIHRpdGxlOiAn5p+l55yL6K+m5oOFJywKICAgICAgICAgIC8vIOW8ueWHuuahhuagh+mimAogICAgICAgICAgd2lkdGg6ICc4MCUnIC8vIOW9k+WJjeW8ueWHuuahhuWuveW6piDpu5jorqQ4MCUKICAgICAgICB9LAoKICAgICAgICB0YWJsZUNvbmZpZzogewogICAgICAgICAgLy8g6KGo5qC85bGe5oCnCiAgICAgICAgICByZWY6ICd0YWJsZVJlZicsCiAgICAgICAgICBjb2x1bW5zOiBjb25maWdEZXRhaWxUYWJsZSh0aGlzKSwKICAgICAgICAgIC8vIOihqOWktAogICAgICAgICAgc2VsZWN0aW9uOiBmYWxzZSwKICAgICAgICAgIC8vIOWkjemAiQogICAgICAgICAgaW5kZXhOdW1iZXI6IGZhbHNlLAogICAgICAgICAgLy8g5bqP5Y+3CiAgICAgICAgICBsb2FkaW5nOiBmYWxzZSwKICAgICAgICAgIC8vIOetieW+heWKoOi9veS4rQogICAgICAgICAgcGFnZUxpc3Q6IHsKICAgICAgICAgICAgLy8g6aG156CBCiAgICAgICAgICAgIHRvdGFsTnVtOiAwLAogICAgICAgICAgICAvLyDmgLvpobXmlbAKICAgICAgICAgICAgY3VycmVudFBhZ2U6IDEsCiAgICAgICAgICAgIC8vIOW9k+WJjemhtQogICAgICAgICAgICBwYWdlU2l6ZTogdGhpcy4kc3RvcmUuZ2V0dGVycy5wYWdlU2l6ZSAvLyDlvZPliY3pobXmmL7npLrmnaHmlbAKICAgICAgICAgIH0sCgogICAgICAgICAgY29tcG9uZW50UHJvcHM6IHsKICAgICAgICAgICAgLy8g6KGo5qC85bGe5oCn6YWN572uCiAgICAgICAgICAgIGRhdGE6IFtdIC8vIOihqOaVsOaNrgogICAgICAgICAgfQogICAgICAgIH0KICAgICAgfSwKCiAgICAgIFN1Y2Nlc3NkaWFsb2c6IGZhbHNlLAogICAgICAvLyDovazmjojmnYPmiJDlip/mj5DnpLrlvLnnqpcKICAgICAgU2VuY29uZDogNSAvLyDorr7nva7liJ3lp4vlgJLorqHml7YKICAgIH07CiAgfSwKCiAgY29tcHV0ZWQ6IHt9LAogIHdhdGNoOiB7CiAgICBkaWFsb2dWaXNpYmxlOiB7CiAgICAgIGhhbmRsZXI6IGZ1bmN0aW9uIGhhbmRsZXIobmV3TmFtZSwgb2xkTmFtZSkgewogICAgICAgIC8vIOW8ueahhuWFs+mXrea4heepuuaJgOacieafpeivouadoeS7tiDop6PlhrPlvLnnqpflhbPpl63kuIvmrKHmiZPlvIDmn6Xor6LmnaHku7bov5jlnKjnmoTpl67popgKICAgICAgICB0aGlzLmRlZmF1bHRGb3JtID0gewogICAgICAgICAgcXVlcnlfdHlwZTogJzAnLAogICAgICAgICAgYXBwbHlfdXNlcjogJycsCiAgICAgICAgICB0YXJnZXRfdXNlcjogJycsCiAgICAgICAgICByZWFzb246ICcnLAogICAgICAgICAgaXNfcmV0YWtlOiAnJywKICAgICAgICAgIHN0YXJ0X2FwcGx5X3RpbWU6ICcnLAogICAgICAgICAgZW5kX2FwcGx5X3RpbWU6ICcnCiAgICAgICAgfTsKICAgICAgfSwKICAgICAgLy8gaW1tZWRpYXRlOiB0cnVlCiAgICAgIGRlZXA6IHRydWUKICAgIH0KICB9LAogIGNyZWF0ZWQ6IGZ1bmN0aW9uIGNyZWF0ZWQoKSB7fSwKICBtb3VudGVkOiBmdW5jdGlvbiBtb3VudGVkKCkge30sCiAgbWV0aG9kczogewogICAgZGlhbG9nU2hvdzogZnVuY3Rpb24gZGlhbG9nU2hvdygpIHsKICAgICAgdmFyIF90aGlzID0gdGhpczsKICAgICAgdGhpcy5kaWFsb2dWaXNpYmxlID0gdHJ1ZTsKICAgICAgdGhpcy4kbmV4dFRpY2soZnVuY3Rpb24gKCkgewogICAgICAgIF90aGlzLmNvbmZpZy5xdWVyeV90eXBlLm9wdGlvbnMgPSBkaWN0aW9uYXJ5RmllZHMoJ1NNX1FVRVJZX1RZUEUnKTsgLy8g5p+l6K+i57G75Z6LCiAgICAgICAgX3RoaXMuY29uZmlnLnJlYXNvbi5vcHRpb25zID0gZGljdGlvbmFyeUZpZWRzKCdTVUJMSUNFTlNFX1JFQVNPTicpOyAvLyDmjojmnYPljp/lm6AKICAgICAgICBfdGhpcy5jb25maWcuaXNfcmV0YWtlLm9wdGlvbnMgPSBkaWN0aW9uYXJ5RmllZHMoJ1NVQkxJQ0VOU0VfSVNfUkVUQUtFJyk7IC8vIOaOiOadg+eKtuaAgQogICAgICB9KTsKICAgIH0sCiAgICAvKioNCiAgICAgKiDmjInpkq4gLeafpeivoiovCiAgICBxdWVyeUxpc3Q6IGZ1bmN0aW9uIHF1ZXJ5TGlzdCgpIHsKICAgICAgdmFyIF90aGlzMiA9IHRoaXM7CiAgICAgIHZhciBtc2cgPSB7CiAgICAgICAgcGFyYW1ldGVyTGlzdDogW10sCiAgICAgICAgY3VycmVudFBhZ2U6IHRoaXMudGFibGUucGFnZUxpc3QuY3VycmVudFBhZ2UsCiAgICAgICAgcGFnZU51bTogdGhpcy50YWJsZS5wYWdlTGlzdC5wYWdlU2l6ZSwKICAgICAgICAvLyDmn6Xor6LnmoTmnaHku7YKICAgICAgICBxdWVyeV90eXBlOiB0aGlzLmRlZmF1bHRGb3JtLnF1ZXJ5X3R5cGUgPT09ICcnID8gJzAnIDogdGhpcy5kZWZhdWx0Rm9ybS5xdWVyeV90eXBlLAogICAgICAgIHRhcmdldF91c2VyOiB0aGlzLmRlZmF1bHRGb3JtLnRhcmdldF91c2VyLAogICAgICAgIHJlYXNvbjogdGhpcy5kZWZhdWx0Rm9ybS5yZWFzb24sCiAgICAgICAgYXBwbHlfdXNlcjogdGhpcy5kZWZhdWx0Rm9ybS5hcHBseV91c2VyLAogICAgICAgIHN0YXJ0X2FwcGx5X3RpbWU6IHRoaXMuZGVmYXVsdEZvcm0uc3RhcnRfYXBwbHlfdGltZSA/ICIiLmNvbmNhdChkYXRlOEZvcm1hdCh0aGlzLmRlZmF1bHRGb3JtLnN0YXJ0X2FwcGx5X3RpbWUpLCAiMDAwMDAwIikgOiAnJywKICAgICAgICBlbmRfYXBwbHlfdGltZTogdGhpcy5kZWZhdWx0Rm9ybS5lbmRfYXBwbHlfdGltZSA/ICIiLmNvbmNhdChkYXRlOEZvcm1hdCh0aGlzLmRlZmF1bHRGb3JtLmVuZF9hcHBseV90aW1lKSwgIjI0MDAwMCIpIDogJycsCiAgICAgICAgaW5zdF9pZDogJycsCiAgICAgICAgaXNfcmV0YWtlOiB0aGlzLmRlZmF1bHRGb3JtLmlzX3JldGFrZQogICAgICB9OwogICAgICBzdWJsaWNlbnNlTWFuKG1zZykudGhlbihmdW5jdGlvbiAocmVzKSB7CiAgICAgICAgdmFyIF9yZXMkcmV0TWFwID0gcmVzLnJldE1hcCwKICAgICAgICAgIGxpc3QgPSBfcmVzJHJldE1hcC5saXN0LAogICAgICAgICAgdG90YWxOdW0gPSBfcmVzJHJldE1hcC50b3RhbE51bSwKICAgICAgICAgIGN1cnJlbnRQYWdlID0gX3JlcyRyZXRNYXAuY3VycmVudFBhZ2U7CiAgICAgICAgX3RoaXMyLnRhYmxlLmNvbXBvbmVudFByb3BzLmRhdGEgPSBsaXN0OwogICAgICAgIF90aGlzMi50YWJsZS5wYWdlTGlzdC50b3RhbE51bSA9IHRvdGFsTnVtOwogICAgICAgIF90aGlzMi50YWJsZS5wYWdlTGlzdC5jdXJyZW50UGFnZSA9IGN1cnJlbnRQYWdlOwogICAgICAgIC8vIOaSpOmUgOaMiemSruaYvumakOS6i+S7tiDkuI3kuLrnqbrmiJbkuI3kuLowICDmjInpkq7pmpDol48KICAgICAgICBpZiAobXNnLnF1ZXJ5X3R5cGUgIT09ICcwJykgewogICAgICAgICAgX3RoaXMyLmJ0bkRhdGFzLmJ0blJldm9rZS5zaG93ID0gZmFsc2U7IC8vIOaMiemSrumakOiXjwogICAgICAgICAgX3RoaXMyLmJ0bkRhdGFzLmJ0bkNhbGxpbi5zaG93ID0gZmFsc2U7IC8vIOaMiemSrumakOiXjwogICAgICAgIH0gZWxzZSB7CiAgICAgICAgICBfdGhpczIuYnRuRGF0YXMuYnRuUmV2b2tlLnNob3cgPSB0cnVlOyAvLyDmjInpkq7mmL7npLoKICAgICAgICAgIF90aGlzMi5idG5EYXRhcy5idG5DYWxsaW4uc2hvdyA9IHRydWU7IC8vIOaMiemSruaYvuekugogICAgICAgIH0KICAgICAgfSk7CiAgICB9LAogICAgLyoqDQogICAgICrpgInmi6nooYwgKi8KICAgIGN1cnJlbnRDaGFuZ2U6IGZ1bmN0aW9uIGN1cnJlbnRDaGFuZ2Uocm93KSB7CiAgICAgIHRoaXMudGFibGUuY3VycmVudFJvdyA9IHJvdzsKICAgIH0sCiAgICAvKioNCiAgICAgKuaMiemSriAg5pKk6ZSAICovCiAgICBoYW5kbGVSZXZva2U6IGZ1bmN0aW9uIGhhbmRsZVJldm9rZSgpIHsKICAgICAgdmFyIF90aGlzMyA9IHRoaXM7CiAgICAgIHZhciByb3dzID0gdGhpcy50YWJsZS5jdXJyZW50Um93Lmxlbmd0aDsKICAgICAgaWYgKHJvd3MgPT09IDApIHsKICAgICAgICBjb21tb25Nc2dXYXJuKCfor7fpgInkuK3kuIDmnaHmlbDmja4nLCB0aGlzKTsKICAgICAgICByZXR1cm47CiAgICAgIH0gZWxzZSB7CiAgICAgICAgaWYgKHRoaXMudGFibGUuY3VycmVudFJvdy5pc19yZXRha2UgPT09ICczJyB8fCB0aGlzLnRhYmxlLmN1cnJlbnRSb3cuaXNfcmV0YWtlID09PSAnNCcgfHwgdGhpcy50YWJsZS5jdXJyZW50Um93LmlzX3JldGFrZSA9PT0gJzUnKSB7CiAgICAgICAgICBjb21tb25Nc2dXYXJuKCflj6rmnInlvoXlrqHmibnlkozlvoXmjojmnYPnmoTovazmjojmnYPlj6/ku6Xov5vooYzmkqTplIDmk43kvZwnLCB0aGlzKTsKICAgICAgICB9IGVsc2UgewogICAgICAgICAgdmFyIG1zZyA9IHsKICAgICAgICAgICAgcGFyYW1ldGVyTGlzdDogW10sCiAgICAgICAgICAgIHN5c01hcDogewogICAgICAgICAgICAgIGluc3RfaWQ6IHRoaXMudGFibGUuY3VycmVudFJvdy5zdWJsaWNlbnNlX2lkLAogICAgICAgICAgICAgIGlzX3JldGFrZTogdGhpcy50YWJsZS5jdXJyZW50Um93LmlzX3JldGFrZQogICAgICAgICAgICB9CiAgICAgICAgICB9OwogICAgICAgICAgcmV2b2tlKG1zZykudGhlbihmdW5jdGlvbiAocmVzKSB7CiAgICAgICAgICAgIGNvbW1vbk1zZ1N1Y2Nlc3MoJ+aSpOmUgOaIkOWKnycsIF90aGlzMyk7CiAgICAgICAgICAgIF90aGlzMy5xdWVyeUxpc3QoKTsKICAgICAgICAgIH0pLmNhdGNoKGZ1bmN0aW9uICgpIHsKICAgICAgICAgICAgY29tbW9uTXNnRXJyb3IoJ+aSpOmUgOWksei0pScsIF90aGlzMyk7CiAgICAgICAgICB9KTsKICAgICAgICB9CiAgICAgIH0KICAgIH0sCiAgICAvKioNCiAgICAgKuaMiemSriAg5pS25Zue5o6I5p2DICovCiAgICBoYW5kbGVDYWxsaW46IGZ1bmN0aW9uIGhhbmRsZUNhbGxpbigpIHsKICAgICAgdmFyIF90aGlzNCA9IHRoaXM7CiAgICAgIHZhciByb3dzID0gdGhpcy50YWJsZS5jdXJyZW50Um93Lmxlbmd0aDsKICAgICAgaWYgKHJvd3MgPT09IDApIHsKICAgICAgICBjb21tb25Nc2dXYXJuKCfor7fpgInkuK3kuIDmnaHmlbDmja4nLCB0aGlzKTsKICAgICAgICByZXR1cm47CiAgICAgIH0gZWxzZSB7CiAgICAgICAgaWYgKHRoaXMudGFibGUuY3VycmVudFJvdy5pc19yZXRha2UgPT09ICczJykgewogICAgICAgICAgLy8g5Yik5pat55So5oi35piv5ZCm5pyJ5Y+v5pS25Zue6L2s5o6I5p2D5L+h5oGvCiAgICAgICAgICB2YXIgbXNnID0gewogICAgICAgICAgICBwYXJhbWV0ZXJMaXN0OiBbXSwKICAgICAgICAgICAgdXNlcl9ubzogdGhpcy4kc3RvcmUuZ2V0dGVycy51c2VyTm8KICAgICAgICAgIH07CiAgICAgICAgICBxdWVyeVN1YkxpY2Vuc2UobXNnKS50aGVuKGZ1bmN0aW9uIChyZXMpIHsKICAgICAgICAgICAgX3RoaXM0LmRpYWxvZy52aXNpYmxlID0gdHJ1ZTsgLy8g5a+G56CB5qOA6aqM5qGG5pi+56S6CiAgICAgICAgICB9KTsKICAgICAgICB9IGVsc2UgewogICAgICAgICAgY29tbW9uTXNnV2Fybign5Y+q5pyJ5bey5o6I5p2D55qE6L2s5o6I5p2D5Y+v5Lul6L+b6KGM5pS25Zue5o6I5p2D5pON5L2cJywgdGhpcyk7CiAgICAgICAgfQogICAgICB9CiAgICB9LAogICAgLyoqDQogICAgICrmtYHmsLTlj7ct5a6h5om56K+m5oOFDQogICAgICpAcGFyYW0gcm93IOW9k+WJjeihjOaVsOaNriovCiAgICBhcHByb3ZlRGV0YWlsOiBmdW5jdGlvbiBhcHByb3ZlRGV0YWlsKHJvdykgewogICAgICB2YXIgX3RoaXM1ID0gdGhpczsKICAgICAgdGhpcy5jaGFuZ2VWaXNpYmxlKHRydWUpOwogICAgICB0aGlzLiRuZXh0VGljayhmdW5jdGlvbiAoKSB7CiAgICAgICAgdmFyIG1zZyA9IHsKICAgICAgICAgIHBhcmFtZXRlckxpc3Q6IFtdLAogICAgICAgICAgc3lzTWFwOiB7CiAgICAgICAgICAgIGluc3RfaWQ6IHJvdy5zdWJsaWNlbnNlX2lkCiAgICAgICAgICB9CiAgICAgICAgfTsKICAgICAgICBzZWxlY3RGbG93RGV0YWlsRGF0YShtc2cpLnRoZW4oZnVuY3Rpb24gKHJlcykgewogICAgICAgICAgX3RoaXM1LmRpYWxvZ0RldGFpbC50YWJsZUNvbmZpZy5jb21wb25lbnRQcm9wcy5kYXRhID0gcmVzLnJldE1hcC5saXN0OwogICAgICAgICAgX3RoaXM1LmRpYWxvZ0RldGFpbC50YWJsZUNvbmZpZy5jb21wb25lbnRQcm9wcy5kYXRhLmZvckVhY2goZnVuY3Rpb24gKGl0ZW0pIHsKICAgICAgICAgICAgLy8g5b6F5a6h5om56KeS6Imy5qC85byP5YyWCiAgICAgICAgICAgIHZhciByb2xlQXJyID0gW107CiAgICAgICAgICAgIF90aGlzNS4kc3RvcmUuZ2V0dGVycy5yb2xlTGlzdC5tYXAoZnVuY3Rpb24gKHZhbCkgewogICAgICAgICAgICAgIGlmICghY29tbW9uQmxhbmsoaXRlbS5yb2xlX25vKSkgewogICAgICAgICAgICAgICAgaXRlbS5yb2xlX25vLnNwbGl0KCcsJykubWFwKGZ1bmN0aW9uIChpKSB7CiAgICAgICAgICAgICAgICAgIGlmIChpID09PSB2YWwudmFsdWUpIHsKICAgICAgICAgICAgICAgICAgICByb2xlQXJyLnB1c2godmFsLmxhYmVsKTsKICAgICAgICAgICAgICAgICAgfQogICAgICAgICAgICAgICAgICByZXR1cm4gcm9sZUFycjsKICAgICAgICAgICAgICAgIH0pOwogICAgICAgICAgICAgIH0KICAgICAgICAgICAgfSk7CiAgICAgICAgICAgIGl0ZW0ucm9sZV9ubyA9IHJvbGVBcnIuam9pbigpOwogICAgICAgICAgfSk7CiAgICAgICAgfSk7CiAgICAgIH0pOwogICAgfSwKICAgIC8qKg0KICAgICAqIOa1geawtOWPt+ivpuaDheWFs+mXreaYvuekug0KICAgICAqLwogICAgY2hhbmdlVmlzaWJsZTogZnVuY3Rpb24gY2hhbmdlVmlzaWJsZShwYXJhbSkgewogICAgICB0aGlzLmRpYWxvZ0RldGFpbC52aXNpYmxlID0gcGFyYW07CiAgICB9LAogICAgLyoqDQogICAgICog5pS25Zue5o6I5p2D5a+G56CB5qGG5by55Ye65qGGIC0g5YWz6ZetKi8KICAgIGRpYWxvZ0Nsb3NlOiBmdW5jdGlvbiBkaWFsb2dDbG9zZSgpIHsKICAgICAgdGhpcy5kaWFsb2cudmlzaWJsZSA9IGZhbHNlOwogICAgfSwKICAgIC8qKg0KICAgICAqIOaUtuWbnuaOiOadg+WvhueggeahhuW8ueWHuuahhiAtIOaPkOS6pCovCiAgICBkaWFsb2dTdWJtaXQ6IGZ1bmN0aW9uIGRpYWxvZ1N1Ym1pdCgpIHsKICAgICAgdmFyIF90aGlzNiA9IHRoaXM7CiAgICAgIGNvbW1vbk1zZ0NvbmZpcm0oJ+aYr+WQpuehruiupOaUtuWbnuaOiOadgycsIHRoaXMsIGZ1bmN0aW9uIChwYXJhbSkgewogICAgICAgIGlmIChwYXJhbSkgewogICAgICAgICAgLy8g5YWI6L+b6KGM5a+G56CB5qCh6aqMIOWGjeWPkei1t+aUtuWbnuaOiOadg+ivt+axggogICAgICAgICAgdmFyIG1zZyA9IHsKICAgICAgICAgICAgcGFyYW1ldGVyTGlzdDogW10sCiAgICAgICAgICAgIG9wZXJfdHlwZTogJ3F1ZXJ5VXNlcicsCiAgICAgICAgICAgIHVzZXJfbm86IF90aGlzNi50YWJsZS5jdXJyZW50Um93LnRhcmdldF91c2VyX25vLAogICAgICAgICAgICBwYXNzV29yZDogZW5jcnlwdFJlc3VsdChfdGhpczYuJHN0b3JlLmdldHRlcnMuaW5pdFBhcmFtcy5lblNlY01hcC5lbmNyeXB0VHlwZSwgX3RoaXM2LmRpYWxvZy5mb3JtLmRlZmF1bHRGb3JtLnBhc3N3b3JkKQogICAgICAgICAgfTsKICAgICAgICAgIC8vIOWvhueggeagoemqjAogICAgICAgICAgcHNkVmFsaWQobXNnKS50aGVuKGZ1bmN0aW9uIChyZXMpIHsKICAgICAgICAgICAgaWYgKHJlcy5yZXRDb2RlID09PSAnMjAwJykgewogICAgICAgICAgICAgIC8vIOWPkei1t+aUtuWbnuaOiOadg+ivt+axggogICAgICAgICAgICAgIHZhciBtc2cyID0gewogICAgICAgICAgICAgICAgcGFyYW1ldGVyTGlzdDogW10sCiAgICAgICAgICAgICAgICB1c2VyX25vOiBfdGhpczYuJHN0b3JlLmdldHRlcnMudXNlck5vCiAgICAgICAgICAgICAgfTsKICAgICAgICAgICAgICBjYWxsaW4obXNnMikudGhlbihmdW5jdGlvbiAocmVzMikgewogICAgICAgICAgICAgICAgLy8g6Lez6L2s5Yiw6YeN5paw55m75b2V6aG16Z2iCiAgICAgICAgICAgICAgICBfdGhpczYuZGlhbG9nVmlzaWJsZSA9IGZhbHNlOyAvLyDmjojmnYPnrqHnkIbpobXpnaLlhbPpl60KICAgICAgICAgICAgICAgIF90aGlzNi5kaWFsb2cudmlzaWJsZSA9IGZhbHNlOyAvLyDlr4bnoIHmoYblhbPpl60KICAgICAgICAgICAgICAgIF90aGlzNi5TdWNjZXNzZGlhbG9nID0gdHJ1ZTsKICAgICAgICAgICAgICAgIHZhciBpbnRlcnZhbCA9IHNldEludGVydmFsKGZ1bmN0aW9uICgpIHsKICAgICAgICAgICAgICAgICAgX3RoaXM2LlNlbmNvbmQtLTsKICAgICAgICAgICAgICAgICAgaWYgKF90aGlzNi5TZW5jb25kID09PSAwKSB7CiAgICAgICAgICAgICAgICAgICAgd2luZG93LmNsZWFySW50ZXJ2YWwoaW50ZXJ2YWwpOwogICAgICAgICAgICAgICAgICAgIF90aGlzNi5sb2dvdXQoKTsgLy8g5YCS6K6h5pe257uT5p2f5pe26Lez6L2s55m75b2V6aG16Z2iCiAgICAgICAgICAgICAgICAgIH0KICAgICAgICAgICAgICAgIH0sIDEwMDApOwogICAgICAgICAgICAgIH0pLmNhdGNoKGZ1bmN0aW9uIChlKSB7CiAgICAgICAgICAgICAgICAvLyBjb25zb2xlLmxvZyhlKQogICAgICAgICAgICAgIH0pOwogICAgICAgICAgICB9CiAgICAgICAgICB9KS5jYXRjaChmdW5jdGlvbiAoKSB7CiAgICAgICAgICAgIGNvbW1vbk1zZ1dhcm4oJ+WvhueggemUmeivrycsIF90aGlzNik7CiAgICAgICAgICB9KTsKICAgICAgICB9CiAgICAgIH0pOwogICAgfSwKICAgIC8qKg0KICAgICAqIOi3s+i9rOeZu+W9lemhtemdouaWueazlSovCiAgICBsb2dvdXQ6IGZ1bmN0aW9uIGxvZ291dCgpIHsKICAgICAgdmFyIF90aGlzNyA9IHRoaXM7CiAgICAgIHJldHVybiBfYXN5bmNUb0dlbmVyYXRvciggLyojX19QVVJFX18qL19yZWdlbmVyYXRvclJ1bnRpbWUoKS5tYXJrKGZ1bmN0aW9uIF9jYWxsZWUoKSB7CiAgICAgICAgcmV0dXJuIF9yZWdlbmVyYXRvclJ1bnRpbWUoKS53cmFwKGZ1bmN0aW9uIF9jYWxsZWUkKF9jb250ZXh0KSB7CiAgICAgICAgICB3aGlsZSAoMSkgewogICAgICAgICAgICBzd2l0Y2ggKF9jb250ZXh0LnByZXYgPSBfY29udGV4dC5uZXh0KSB7CiAgICAgICAgICAgICAgY2FzZSAwOgogICAgICAgICAgICAgICAgX2NvbnRleHQubmV4dCA9IDI7CiAgICAgICAgICAgICAgICByZXR1cm4gX3RoaXM3LiRzdG9yZS5kaXNwYXRjaCgndXNlci9sb2dvdXQnKTsKICAgICAgICAgICAgICBjYXNlIDI6CiAgICAgICAgICAgICAgICBfdGhpczcuJHJvdXRlci5wdXNoKCIvbG9naW4/cmVkaXJlY3Q9Ii5jb25jYXQoX3RoaXM3LiRyb3V0ZS5mdWxsUGF0aCkpOwogICAgICAgICAgICAgIGNhc2UgMzoKICAgICAgICAgICAgICBjYXNlICJlbmQiOgogICAgICAgICAgICAgICAgcmV0dXJuIF9jb250ZXh0LnN0b3AoKTsKICAgICAgICAgICAgfQogICAgICAgICAgfQogICAgICAgIH0sIF9jYWxsZWUpOwogICAgICB9KSkoKTsKICAgIH0sCiAgICAvKioNCiAgICAgKumhteeggeabtOaWsCAqLwogICAgZ2V0TGlzdDogZnVuY3Rpb24gZ2V0TGlzdChwYWdlUGFyYW0pIHsKICAgICAgdmFyIGN1cnJlbnRQYWdlID0gcGFnZVBhcmFtLmN1cnJlbnRQYWdlLAogICAgICAgIHBhZ2VTaXplID0gcGFnZVBhcmFtLnBhZ2VTaXplOwogICAgICB0aGlzLnRhYmxlLnBhZ2VMaXN0LnBhZ2VTaXplID0gcGFnZVNpemU7CiAgICAgIHRoaXMudGFibGUucGFnZUxpc3QuY3VycmVudFBhZ2UgPSBjdXJyZW50UGFnZTsKICAgICAgdGhpcy5xdWVyeUxpc3QoKTsKICAgIH0KICB9Cn07"}, null]}