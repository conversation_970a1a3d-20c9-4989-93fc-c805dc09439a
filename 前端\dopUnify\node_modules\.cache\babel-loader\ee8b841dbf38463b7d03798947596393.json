{"remainingRequest": "E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-q<PERSON>duan\\数字运营平台-统一门户工程\\dopUnify\\node_modules\\thread-loader\\dist\\cjs.js!E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan\\数字运营平台-统一门户工程\\dopUnify\\node_modules\\babel-loader\\lib\\index.js!E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan\\数字运营平台-统一门户工程\\dopUnify\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan\\数字运营平台-统一门户工程\\dopUnify\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-q<PERSON>duan\\数字运营平台-统一门户工程\\dopUnify\\src\\App.vue?vue&type=script&lang=js&", "dependencies": [{"path": "E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-<PERSON><PERSON><PERSON>n\\数字运营平台-统一门户工程\\dopUnify\\src\\App.vue", "mtime": 1712050468638}, {"path": "E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-<PERSON><PERSON><PERSON>n\\数字运营平台-统一门户工程\\dopUnify\\babel.config.js", "mtime": 1686019818359}, {"path": "E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-<PERSON><PERSON><PERSON>n\\数字运营平台-统一门户工程\\dopUnify\\.babelrc", "mtime": 1705285078668}, {"path": "E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-<PERSON><PERSON><PERSON>n\\数字运营平台-统一门户工程\\dopUnify\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-<PERSON><PERSON><PERSON>n\\数字运营平台-统一门户工程\\dopUnify\\node_modules\\thread-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-<PERSON><PERSON><PERSON>n\\数字运营平台-统一门户工程\\dopUnify\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-<PERSON><PERSON><PERSON>n\\数字运营平台-统一门户工程\\dopUnify\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-<PERSON><PERSON><PERSON>n\\数字运营平台-统一门户工程\\dopUnify\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1655715099000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, null]}