{"remainingRequest": "D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\node_modules\\babel-loader\\lib\\index.js!D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\src\\views\\system\\config\\audit\\component\\table\\index.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\src\\views\\system\\config\\audit\\component\\table\\index.vue", "mtime": 1686019807872}, {"path": "D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\babel.config.js", "mtime": 1686019818359}, {"path": "D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1655715099000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA2HA,SACAA,kBACAC,eACAC,kBACAC,qBACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA,uBACAC;EADAC;EAAAC;EAAAC;EAAAC;EAAAC;EAAAC;EAAAC;AAEA;AACA;EACAC;EACAC;IAAAC;IAAAC;EAAA;EACAC;EACAC;IACAC;MACAC;MACAC;QACA;MACA;IACA;IACAC;MACAF;MACAC;QACA;MACA;IACA;EACA;EACAE;IACA;MACAC;MACAC;QACA;QACAC;QAAA;QACAC;QACAC;QAAA;QACAC;QAAA;QACAC;QACAC;UACAR;UAAA;UACAS;UACAC;QACA;;QACAC;UACAC;UACAC;UAAA;UACAC;QACA;;QACAC;MACA;;MACAC;QACA;QACAC;UACAC;QACA;QACAC;UACAD;QACA;QACAE;UACAF;QACA;MACA;MACAG;MACAC;MAAA;MACAC;QACAnB;QACAI;UACA;UACAgB;UACAC;QACA;QACAC;QACAC;UACAC;UACAC;UAAA;UACAjC;YACAkC;YACAC;YACAC;YACAC;YACAC;YACAC;YACAC;YACAC;UACA;QACA;MACA;MACAC;QACAb;QAAA;QACAC;QACAa;QACAC;QAAA;QACAC;QAAA;QACAC;QAAA;QACAC;QAAA;QACAC;QAAA;QACAC;UACAC,UACA;YACAC;YACAC;UACA,GACA;YACAD;YACAC;UACA,EACA;UAAA;UACAC,WACA;YAAAF;YAAAC;UAAA,GACA;YAAAD;YAAAC;UAAA,EACA;UAAA;UACAE;UAAA;UACAC;UAAA;UACAtD;UAAA;UACAuD,mBACA;YAAAL;YAAAC;UAAA,GACA;YAAAD;YAAAC;UAAA,EACA;UACAK,kBACA;YAAAN;YAAAC;UAAA,GACA;YAAAD;YAAAC;UAAA,EACA;UACAM;UAAA;UACAC,YACA;YAAAR;YAAAC;UAAA,GACA;YAAAD;YAAAC;UAAA,EACA;QACA;;QAAA;QACAQ;UACAV;UAAA;UACAG;UAAA;UACAC;UAAA;UACAC;UAAA;UACAC;UAAA;UACAC;UAAA;UACAC;UAAA;UACAC;QACA;MACA;;MACAE;MAAA;MACAC;MAAA;MACAC;MAAA;MACAC;MAAA;MACAC;MAAA;MACAC;QACA;QACApC;QACAlB;UACA;UACAiB;UAAA;UACAsC;UACAvC;QACA;;QACAwC;MACA;IACA;EACA;EACAC;IACAC;MACA;IACA;EACA;EACAC;IACA5D;MACA;IACA;IACA2D;MACA;IACA;IACA;MACAE;QACA;UACA;UACA;QACA;UACA;UACA;QACA;UACA;UACA;QACA;MACA;MACAC;IACA;IACA;MACAD;QACA;MAAA,CACA;MACAC;IACA;EACA;EACAC;IACA;EACA;EACAC;IAAA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;MACA;QACA;MACA;IACA;EACA;EACA;EACAC;IACA;EACA;EACAC;IACA;AACA;IAAA;IACAC;MAAA;MACA;MACA;QACAC;QACA9D;QACA+D;MAAA,GACA,iBACA;MACA;MACA7F;QACA;UAAA8F;UAAAjE;UAAAC;QACA;QACA;QACA;QACA;QACA;QACA;QACA;UACA;YACA;YACA;YACA;cACAiE;gBACA;kBACAC;gBACA;gBACA;cACA;YACA;YACAD;UACA;YACA;YACAA,oEACA;cACA;gBACA;cACA;YACA,EACA;UACA;UACA;UACA;YACAA;UACA;UACA;UACA;YACAA;UACA;QACA;QACA;MACA;IACA;IACA;IACAE;MACA,mDACA;MACA;QACA;QACA;QACA;QACA;QACA,IACA,yDACA,0BACA;UACA;UACA;UACA;QACA,WACA,0DACA,0BACA;UACA;UACA;UACA;QACA;QACA;MACA;QACArG;MACA;IACA;IACA;AACA;AACA;AACA;IACAsG;MACA;QACAC;UACA3C;UACAI;QACA;QACA;QACA;QACA;QACA;QAAA,2CACAwC;UAAA;QAAA;UAAA;YAAA;YACA;YACAC;YACAC;YACA;UACA;QAAA;UAAA;QAAA;UAAA;QAAA;QACA;QAAA,4CACAA;UAAA;QAAA;UAAA;YAAA;cAAAC;cAAAC;YACA;YACA;cACAhD;YACA;cACAA;YACA;cACAA;YACA;cACA;cACAA;YACA;cACA;gBACA;gBACAA;gBACAA;cACA;gBACA;gBACAA;gBACAA;cACA;gBACA;gBACA;kBACA;kBACA,UACAA,UACA,kCACAgD,iBACA;gBACA;kBACA;kBACA;kBACA,UACAhD,UACA,iCACAgD,iBACA;gBACA;cACA;YACA;cACA;gBACAhD;cACA;gBACAA;cACA;YACA;;YACA;cACA;YACA;UACA;UACA;QAAA;UAAA;QAAA;UAAA;QAAA;QACA;QACA;UACAiD;YACA;UACA;;UACA;UAAA,4CACAA;YAAA;UAAA;YAAA;cAAA;cACA;cACAjD;cACA;YACA;UAAA;YAAA;UAAA;YAAA;UAAA;QACA;MACA;IACA;IACA;AACA;AACA;IACAkD;MAAA;MACA;MACA;MACA;MACA;QAAA9C;QAAAJ;MACA;MACA;MACA;MACA;MACA;QACAmD;QACAC;MACA;MACAD;QACA;MACA;MACA;QACA;QACA;UACA;YACA;YACA;YACA;UACA;QACA;MACA;IACA;IACA;AACA;AACA;IACAE;MAAA;MACA;QAAAjB;QAAAkB;MAAA;MACAzG;QACA;QACA;QACA;UACA;YACA2D;YACAzD;YACAwG;YACAC;YACAC;YACAC;UACA;QACA;MACA;IACA;IACA;AACA;AACA;IACAC;MACA;MACA,2DACA,+DACA;QAAAZ;MAAA,IACA;MACA;MACA,4DACA,4BACA,4CACA;MACA;IACA;IACA;AACA;AACA;AACA;IACAa;MACA;MACA;QACA;MACA;MACA;QACA;MACA;IACA;IACA;AACA;AACA;IACAC;MACA;MACA;IACA;IACA;AACA;AACA;AACA;IACAC;MAAA;MACA,yDACAC,mDACAA,mDACAA,oDACAA,2DACAA,0DACAA,iDACAA,oDACAA;IAEA;IACA;AACA;AACA;AACA;IACAC;MAAA;MACA;QACA;MACA;;MACA,IACAC,0BACAA,kCACAA,oBACAA,oBACAA,kBACA;QACA;MACA;;MACA,IACAA,iCACAA,0BACAA,kCACAA,oBACAA,oBACAA,kBACA;QACA;QACA;UACA;YACA,iEACA1B,mBACA,SACA,KACA;UACA;QACA;MACA;MACA;QACA;UACA;UACA;QACA;UACA;UACA;UACA;UACA;QACA;UACA;UACA;UACA;UACA;QACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;QACA;MACA;IACA;IACA;IACA2B;MACA;MACA;MACA;MACA;MACA;IACA;IACA;IACAC;MAAA;MACA;QACA;UACA;UACA;YAAAjE;YAAAe;UACA;UACA;UACA;YACA;cACAmD;YACA;UACA;UACA;UACA;UACA;UACAA;YACA;cACA;cACA;gBACA;gBACA;kBACA;kBACAC;gBACA;kBACA;kBACA,IACAC,2BACAA,4BACAA,gBACA;oBACA;oBACA;sBACA,IACA,uCACA3B,qCACAzC,cACAoE,QACA,IACAA,mBACA;wBACA;wBACA;wBACA;wBACA;wBACA;wBACAD;wBACAE;sBACA;oBACA;sBACA;wBACAF;sBACA;wBACAA;sBACA;sBACAE;oBACA;kBACA;oBACA;oBACA,IACA5B,qCACAzC,cACAoE,QACA,EACA;sBACA,IACApE,sDACA;wBACA;wBACAmE;wBACAnE,8CACAA;wBAAA,CACA;sBACA;wBACAmE;sBACA;oBACA;kBACA;gBACA;cACA;YACA;UACA;UACA;UACA;UACA;UACAxB;YACA;cACA2B;YACA;UACA;UACA;UACA;YACAlI;YACA;UACA;UACA;UACA;UACA;YACA;YACA;UACA;YACA;YACA;UACA;UACA;QACA;MACA;IACA;IACA;IACAmI;MACA;MACA;QACAjG;UACA;QACA;MACA;;MACA;IACA;IAEA;AACA;AACA;IACAkG;MAAA;MACA;MACA;MACA;MACA;MACA;MACAC;QACA;QACA;UACA;YAAA;cACA;cACA;gBACAC;cACA;gBACAA;cACA;cACA;cACA;cACA;cACA;cACA;gBACA;gBACA;kBACAC;gBACA;kBACAA;gBACA;kBACAA;gBACA;cACA;gBACA;gBACA;gBACA;kBACAA;gBACA;kBACA;kBACAA;gBACA,WACA,qCACA,mCACA;kBACA;kBACA,IACA,wCACA,wCACA,sCACA;oBACAC,gBACA,8BACA,SACA,KACA;sBACA;wBACAD;sBACA;oBACA;kBACA,WACA,wCACA,sCACA;oBACAA;kBACA;gBACA;cACA;cACAlG,QACAiG,eACAA,eACA,+BACAA,eACAC,cACAD;YAAA;UACA;QACA;MACA;MACA;IACA;IACA;AACA;AACA;IACAG;MACA;IACA;IACA;AACA;IACAC;MACA;MACA;MACA;MACA;MACA;IACA;IACA;AACA;IACAC;MAAA;MACA;MACA;MACA;QACA7I;QACA;MACA;QACAA;QACA;MACA;MACA,6DACA;QACA8C;QACAgG;MAAA,EACA;MACA;MACA;QACA;QACA;MACA;MACA;QACA;QACA;MACA;MACA;QACA;QACA;QACAC;UACA;YACA;YACA,uCACA;UACA;YACA;YACA,uCACA;UACA;YACA;YACA,uCACA;UACA;UACA;UACA5C,uBACA,+CACA;YACA;YACA;cACAO;YACA;YACA;UACA,WACAP,yBACA,+CACA;YACA;YACA;cACA;gBACA;cACA;YACA;UACA;YACA;UACA;QACA;MACA;IACA;IACA;AACA;AACA;IACA6C;MAAA;MACA;QACA;QACA;UACAC;UACAC;QACA;QACAC;UACA;UACA;UACA;QACA;MACA;IACA;IACA;AACA;AACA;IACAC;MAAA;MACA;QACA;UACApD;QACA;QACAtF;UACA;UACA;QACA;MACA;IACA;IACA;AACA;AACA;AACA;IACA2I;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;QACA;UACA;YACAhF;YACAiF;YACAC;UAAA,GACAlI;YACAmI;UAAA,EACA;UACAC;QACA;MACA;;MACA;MACA;QACAC;MACA;MACA;MACA;IACA;IACA;AACA;AACA;IACAF;MACA;QACA;MACA;MACA;QACA;MACA;MACA;QACA;UACA;YACA;cACA;gBACAnF;gBACAiF;gBACAC;gBACAC;cAAA,GACAnI,QACA;cACAsI;YACA;UACA;UACA;QACA;MACA;MACA;IACA;IACA;AACA;AACA;IACAC;MACA;QACA;QACA;QACArJ;UACA;UACA2F;YACA;cACA2D;YACA;cACA;cACAC;cACAD;YACA;UACA;QACA;QACA;MACA;IACA;IACA,gBACAE;MACA;MACA;MACA;QACAC;UACA;YACAV;YACAjF;YACAD;UACA;UACA6F;QACA;QACA;QACA;UACA;QACA;UACA;QACA;MACA;IACA;IACA;AACA;AACA;IACAzJ;MAAA;MACA;QACAwF;QACAkE;MACA;MACA1J;QACA;QACA;QACAa;UACA;YACAgD;YACAD;UACA;UACA+F;QACA;QACA;QACA;MACA;IACA;IACA;AACA;IACAC;MACA;MACA;QACA;MACA;QACA;MACA;IACA;IACA;AACA;IACAC;MAAA;MACA;MACApK;QACA;UACA;YACA+F;YACAsE;YACAnH;YAAA;YACAC;YACAC;YACAC;YACAC;YAAA;YACAC;YACA+G,mDACA,KACAC;YAAA;YACAC;YAAA;YACAC;UACA;UACArK,SACAsK;YACA5K;YACA;YACA;YACA;YACA;YACA;UACA,GACA6K;YACA;UACA;QACA;MACA;IACA;IACA;AACA;IACAC;MAAA;MACA;MACA5K;QACA;UACA;UACA;YACA;YACA+F;YACA7C;YACAC;YACAC;YACAC;YACAC;YACAC;YACA+G,mDACA,KACAC;YACAC,uDACA,KACAD;YACAE;YACAJ,wCACA;cACAjH,YACA;cACAC,aACA;YAAA;UAEA;UACAhD;YACA;YACA;YACAP;YACA;YACA;YACA;YACA;YACA;UACA;QACA;MACA;IACA;IACA;AACA;AACA;AACA;IACA+K;MAAA;MACA;MACA;MAAA,2BACAC;QACA;UAAA,6BACAC;YACA;YACAR;cACA;gBACAS;kBACA5H;kBACAD;kBACAG;kBAAA;kBACAG,YACA8G,+BACA,KACAA,+BACAA,4BACAA;kBAAA;kBACAlH;kBACAH;kBACAK;gBACA;cACA;YACA;UAAA;UAnBA;YAAA;UAoBA;UACA;QACA;MAAA;MAxBA;QAAA;MAyBA;IACA;IACA;AACA;AACA;AACA;IACA0H;MAAA;MACA;MACA;QACA;UACA;QACA;MACA;;MACA;IACA;IACA;IACAC;MACA;IACA;IACA;AACA;IACAC;MACA;QAAAjJ;MACA;MACA;MACA;IACA;IACA;AACA;IACAkJ;MACA;IACA;EACA;AACA", "names": ["commonMsgSuccess", "commonMsgWarn", "commonMsgConfirm", "commonMsgInfo", "Common", "query", "add", "modify", "btnname", "<PERSON><PERSON>le", "dataSource", "aduitMenuTree", "name", "components", "Dynamic", "FlowDetailDialog", "mixins", "props", "defaultForm", "type", "default", "btnAll", "data", "listLoading", "table", "tableColumns", "ref", "selection", "indexNumber", "loading", "componentProps", "height", "formRow", "pageList", "totalNum", "currentPage", "pageSize", "currentRow", "btnDatas", "btnAdd", "show", "btnModify", "btnDelete", "downloadLoading", "currentIndex", "dialog", "width", "title", "visible", "form", "config", "labelWidth", "menu_id", "button_id", "allow_rule", "forbid_rule", "check_flag", "export_file", "ser_port", "check_rule", "dialog2", "enterObj", "allEnterConfig", "allEnterData", "enterNum", "enterNums", "conditionNum", "enterConfig", "operSym", "value", "label", "leftBrak", "typeData", "condition", "valueOrganSelect", "valueUserSelect", "values", "rightBrak", "enterData", "elems", "elemsVal", "isAllowRule", "menuMap", "firstModifyFlag", "flowConfig", "top", "module_id", "computed", "menuChange", "watch", "handler", "deep", "created", "mounted", "<PERSON><PERSON><PERSON><PERSON>", "methods", "queryList", "parameterList", "operationRequestConfig_pageNum", "list", "item", "roleArr", "dialogShow", "reback<PERSON><PERSON>", "Object", "enterArr", "arr2", "arr", "index", "val", "conditions", "initData", "numArr", "nums", "initDataSource", "sysMap", "show_type", "sources_type", "data_sources", "time_format_store", "addEnter", "delEnter", "initConfig", "createEnterData", "num", "enterChange", "param", "dialog2Close", "dialog2Submit", "indexArr", "str", "i", "flag", "arr3", "handleSelectionChange", "initElems", "lineArray", "condArray", "formatValue", "dictionaryFieds", "dialogClose", "handleAdd", "handleModify", "oprate", "keys", "roleNo", "oper_type", "role_level", "roleNoTree", "menuTree", "treeDataTranslate", "id", "pid", "children", "parents", "datas", "parent", "initButtonMap", "singleMap", "singleSet", "changeButtonName", "buttonArr", "btnArray", "role_no", "checkArray", "dialogSubmit", "dialogAddSubmit", "oldMsg", "check_rule_role", "formData1", "check_rule_module", "last_modi_date", "then", "catch", "dialogEditSubmit", "commitMenu", "key", "k", "menuAuditData", "ckeckPath", "dialogFlowClose", "getList", "showLoading"], "sourceRoot": "src/views/system/config/audit/component/table", "sources": ["index.vue"], "sourcesContent": ["<!-- 菜单审核配置-表格部分 -->\r\n<template>\r\n  <div class=\"sun-content\">\r\n    <sun-table\r\n      :table-config=\"table\"\r\n      @selection-change=\"handleSelectionChange\"\r\n      @pagination=\"getList\"\r\n    >\r\n      <template slot=\"tableColumn\">\r\n        <el-table-column\r\n          v-for=\"item in table.tableColumns\"\r\n          :key=\"item.id\"\r\n          :prop=\"item.name\"\r\n          :label=\"item.label\"\r\n          :width=\"item.width\"\r\n        >\r\n          <div slot-scope=\"{ row }\">\r\n            <span\r\n              v-if=\"item.name === 'allow_rule'\"\r\n              class=\"textOverflow\"\r\n              :title=\"row[item.name]\"\r\n            >{{ row[item.name] }}</span>\r\n            <span\r\n              v-else-if=\"item.name === 'forbid_rule'\"\r\n              class=\"textOverflow\"\r\n              :title=\"row[item.name]\"\r\n            >{{ row[item.name] }}</span>\r\n            <span\r\n              v-else-if=\"item.name === 'button_name'\"\r\n              class=\"textOverflow\"\r\n              :title=\"row[item.name]\"\r\n            >{{ row[item.name] }}</span>\r\n            <span v-else-if=\"item.name === 'check_flag'\">{{\r\n              row[item.name] | commonFormatValue('SM_HOME_SUBMIT_TYPE_FLAG')\r\n            }}</span>\r\n\r\n            <span\r\n              v-else-if=\"row['check_flag'] === '1' && item.name === 'ser_port'\"\r\n              :title=\"row['check_rule']\"\r\n              class=\"textOverflow\"\r\n            >{{ row['check_rule'] }}</span>\r\n            <span\r\n              v-else-if=\"\r\n                row['check_flag'] === '2' && item.name === 'check_rule'\r\n              \"\r\n              class=\"checkRule\"\r\n              @click=\"ckeckPath(row)\"\r\n            >{{ row['check_rule'] }}</span>\r\n            <span\r\n              v-else-if=\"\r\n                row['check_flag'] === '1' && item.name === 'check_rule'\r\n              \"\r\n            /><!-- 防止走v-else -->\r\n            <span v-else>{{ row[item.name] }}</span>\r\n          </div>\r\n        </el-table-column> </template><!-- 表格数据 -->\r\n      <template slot=\"customButton\">\r\n        <sun-button\r\n          :btn-datas=\"btnDatas\"\r\n          @handleAdd=\"handleAdd\"\r\n          @handleModify=\"handleModify\"\r\n        />\r\n        <!--按钮配置-->\r\n      </template>\r\n    </sun-table>\r\n    <!--新增、修改弹出框begin-->\r\n    <sun-form-dialog\r\n      :dialog-config=\"dialog\"\r\n      @dialogClose=\"dialogClose\"\r\n      @dialogSubmit=\"dialogSubmit\"\r\n    />\r\n    <!--准入条件、禁止条件弹框begin-->\r\n    <el-dialog\r\n      ref=\"refDialog\"\r\n      width=\"105rem\"\r\n      :title=\"dialog2.title\"\r\n      :visible.sync=\"dialog2.visible\"\r\n      @close=\"dialog2Close\"\r\n    >\r\n      <div class=\"content\">\r\n        <div class=\"titles\">\r\n          输入配置<span class=\"addBtns\" @click=\"addEnter\">\r\n            <el-button\r\n              type=\"success\"\r\n              icon=\"el-icon-plus\"\r\n            /></span>\r\n        </div>\r\n        <el-form ref=\"enterForm\" :model=\"dialog2.allEnterData\">\r\n          <el-row\r\n            v-for=\"(item, index) in dialog2.allEnterConfig\"\r\n            :key=\"item.index\"\r\n            :gutter=\"10\"\r\n            style=\"margin-bottom: 1rem\"\r\n          >\r\n            <Dynamic\r\n              :data=\"item\"\r\n              :pos-index=\"index\"\r\n              :end-index=\"item.index\"\r\n              :form-value=\"dialog2.allEnterData\"\r\n              @delBtn=\"delEnter\"\r\n              @enterData=\"enterChange\"\r\n            />\r\n          </el-row>\r\n        </el-form>\r\n      </div>\r\n      <div class=\"el_bottom\">\r\n        <span>禁止条件优先级高于准入条件。</span>\r\n        <div>\r\n          <el-button @click=\"dialog2Close\">取消</el-button>\r\n          <el-button type=\"primary\" @click=\"dialog2Submit\">确定</el-button>\r\n        </div>\r\n      </div>\r\n    </el-dialog>\r\n    <!-- 流程详情弹框 -->\r\n    <flow-detail-dialog\r\n      v-if=\"flowConfig.visible\"\r\n      ref=\"flowDetailDialog\"\r\n      :dialog-config=\"flowConfig\"\r\n      @dialogClose=\"dialogFlowClose\"\r\n    />\r\n  </div>\r\n</template>\r\n<script>\r\nimport {\r\n  commonMsgSuccess,\r\n  commonMsgWarn,\r\n  commonMsgConfirm,\r\n  commonMsgInfo\r\n} from '@/utils/message.js' // 提示信息\r\nimport { configTable, config } from './info' // 表格表头、弹出框表单配置\\\r\nimport { dictionaryFieds } from '@/utils/dictionary' // 字典配置\r\nimport FlowDetailDialog from '../others/flowDetailDialog.vue' // 流程详情弹窗\r\nimport Dynamic from '../others/dynamic.vue' // 输入配置组件\r\nimport { commonBlank } from '@/utils/common'\r\nimport { treeDataTranslate } from '@/utils/dictionary.js' // 字典常量\r\nimport { dateNowFormat } from '@/utils/date.js'\r\nimport { dateTimeFormat, organNameFormat } from '@/filters'\r\nimport ResizeMixin from '@/utils/ResizeHandler' // 整体页面是否根据总高配置\r\n\r\nimport { Common, system } from '@/api'\r\nconst { query, add, modify, btnname, checkrule, dataSource, aduitMenuTree } =\r\n  Common.SysAudit\r\nconst { roleNoTree } = system.SysUser // 角色获取\r\nexport default {\r\n  name: 'TableList',\r\n  components: { Dynamic, FlowDetailDialog },\r\n  mixins: [ResizeMixin],\r\n  props: {\r\n    defaultForm: {\r\n      type: Object,\r\n      default: function() {\r\n        return {}\r\n      }\r\n    },\r\n    btnAll: {\r\n      type: Object,\r\n      default: function() {\r\n        return {}\r\n      }\r\n    }\r\n  },\r\n  data() {\r\n    return {\r\n      listLoading: false,\r\n      table: {\r\n        // 表格配置\r\n        tableColumns: configTable(), // 表头配置\r\n        ref: 'tableRef',\r\n        selection: true, // 复选\r\n        indexNumber: true, // 序号\r\n        loading: false,\r\n        componentProps: {\r\n          data: [], // 表格数据\r\n          height: '100px',\r\n          formRow: 1 // 表单行数\r\n        },\r\n        pageList: {\r\n          totalNum: 0,\r\n          currentPage: 1, // 当前页\r\n          pageSize: this.$store.getters.pageSize // 当前页显示条数\r\n        },\r\n        currentRow: [] // 选中行\r\n      },\r\n      btnDatas: {\r\n        // 按钮配置\r\n        btnAdd: {\r\n          show: this.btnAll.btnAdd\r\n        },\r\n        btnModify: {\r\n          show: this.btnAll.btnModify\r\n        },\r\n        btnDelete: {\r\n          show: this.btnAll.btnDelete\r\n        }\r\n      },\r\n      downloadLoading: false,\r\n      currentIndex: 0, // 当前行索引\r\n      dialog: {\r\n        ref: 'dialogForm',\r\n        componentProps: {\r\n          // 弹出框配置属性\r\n          width: '60rem',\r\n          title: ''\r\n        },\r\n        visible: false,\r\n        form: {\r\n          config: config(this),\r\n          labelWidth: '15rem', // 当前表单标签宽度配置\r\n          defaultForm: {\r\n            menu_id: '',\r\n            button_id: [],\r\n            allow_rule: '',\r\n            forbid_rule: '',\r\n            check_flag: '',\r\n            export_file: '',\r\n            ser_port: '',\r\n            check_rule: ''\r\n          }\r\n        }\r\n      },\r\n      dialog2: {\r\n        title: '', // 准入条件配置   禁止条件配置\r\n        visible: false,\r\n        enterObj: {},\r\n        allEnterConfig: [], // 所有的输入配置(数组对象)\r\n        allEnterData: {}, // 所有的输入配置数据(数组对象)\r\n        enterNum: 0, // 输入配置个数\r\n        enterNums: 0, // 输入配置个数\r\n        conditionNum: 1, // 条件与和或后面的数字\r\n        enterConfig: {\r\n          operSym: [\r\n            {\r\n              value: '&&',\r\n              label: '与'\r\n            },\r\n            {\r\n              value: '||',\r\n              label: '或'\r\n            }\r\n          ], // 且或等操作符\r\n          leftBrak: [\r\n            { value: '', label: '' },\r\n            { value: '(', label: '(' }\r\n          ], // 前括号\r\n          typeData: [], // 类型下拉框数据\r\n          condition: [], // 条件\r\n          type: 'select', // 判断value框是select/input/input+select\r\n          valueOrganSelect: [\r\n            { value: '0', label: '当前机构' },\r\n            { value: '2', label: '自定义机构' }\r\n          ],\r\n          valueUserSelect: [\r\n            { value: '1', label: '当前用户' },\r\n            { value: '2', label: '自定义用户' }\r\n          ],\r\n          values: [], // 选择和输入的值\r\n          rightBrak: [\r\n            { value: '', label: '' },\r\n            { value: ')', label: ')' }\r\n          ] // 后括号\r\n        }, // 输入配置下拉框值\r\n        enterData: {\r\n          operSym: '', // 且或等操作符\r\n          leftBrak: '', // 前括号\r\n          typeData: '', // 类型下拉框数据\r\n          condition: '', // 条件\r\n          valueOrganSelect: '', // 维护机构\r\n          valueUserSelect: '', // 最后修改用户\r\n          values: '', // 选择和输入的值\r\n          rightBrak: '' // 后括号\r\n        }\r\n      },\r\n      elems: [], // 要素\r\n      elemsVal: [], // table未格式化前的准入条件和禁止条件\r\n      isAllowRule: '1', // 1 准入条件配置按钮  2 禁止条件配置按钮  用来区别是准入条件和禁止条件\r\n      menuMap: '', // 菜单集合\r\n      firstModifyFlag: 0, // 是否第一次编辑  清空按钮名称标识\r\n      flowConfig: {\r\n        // 流程模板调用弹窗\r\n        visible: false,\r\n        componentProps: {\r\n          // 弹出框属性\r\n          title: '模板关联流程图', // 弹出框标题\r\n          top: '0px',\r\n          width: '100%' // 当前弹出框宽度 默认80%\r\n        },\r\n        module_id: ''\r\n      }\r\n    }\r\n  },\r\n  computed: {\r\n    menuChange() {\r\n      return this.dialog.form.defaultForm.menu_id\r\n    }\r\n  },\r\n  watch: {\r\n    loading(value) {\r\n      this.listLoading = this.loading\r\n    },\r\n    menuChange(menuId) {\r\n      this.changeButtonName(menuId)\r\n    },\r\n    'dialog.form.defaultForm.check_flag': {\r\n      handler(val) {\r\n        if (val === '1') {\r\n          this.dialog.form.config.ser_port.hidden = false\r\n          this.dialog.form.config.check_rule.hidden = true\r\n        } else if (val === '2') {\r\n          this.dialog.form.config.ser_port.hidden = true\r\n          this.dialog.form.config.check_rule.hidden = false\r\n        } else {\r\n          this.dialog.form.config.ser_port.hidden = true\r\n          this.dialog.form.config.check_rule.hidden = true\r\n        }\r\n      },\r\n      deep: true\r\n    },\r\n    'dialog.form.defaultForm': {\r\n      handler(newName, oldName) {\r\n        // console.log('表单值', newName)\r\n      },\r\n      deep: true\r\n    }\r\n  },\r\n  created() {\r\n    this.listLoading = this.loading\r\n  },\r\n  mounted() {\r\n    this.initButtonMap()\r\n    this.roleNo()\r\n    this.menuTree()\r\n    this.checkrule()\r\n    this.queryList()\r\n    this.initDataSource() // 初始化数据源\r\n    this.initConfig() // 初试化准入条件、禁止条件下拉框\r\n    // 操作流程处理完毕 更新未处理列表\r\n    this.$bus.$on('operApproveUpdate', (data) => {\r\n      if (data) {\r\n        this.queryList()\r\n      }\r\n    })\r\n  },\r\n  // 销毁对应自定义事件\r\n  beforeDestroy() {\r\n    this.$bus.$off('operApproveUpdate')\r\n  },\r\n  methods: {\r\n    /**\r\n     * 按钮：查询后台数据*/ // 查询数据(此界面没有查询按钮)\r\n    queryList(currentPage) {\r\n      this.showLoading()\r\n      const msg = {\r\n        parameterList: [],\r\n        currentPage: currentPage || this.table.pageList.currentPage,\r\n        operationRequestConfig_pageNum: this.table.pageList.pageSize,\r\n        ...this.defaultForm\r\n      }\r\n      // 查询请求\r\n      query(msg).then((res) => {\r\n        const { list, totalNum, currentPage } = res.retMap\r\n        this.table.componentProps.data = list\r\n        this.table.pageList.totalNum = totalNum\r\n        this.table.pageList.currentPage = currentPage\r\n        // 深拷贝一份res   修改准入条件和禁止条件使用\r\n        this.elemsVal = JSON.parse(JSON.stringify(list))\r\n        // 根据返回的结果 本地审核或者远程 来判断check_rule字段代表角色或模板\r\n        this.table.componentProps.data.forEach((item) => {\r\n          if (item.check_flag === '1' && !commonBlank(item.check_rule)) {\r\n            // 本地审核  check_rule代表角色\r\n            const roleArr = []\r\n            this.$store.getters.roleList.map((val) => {\r\n              item.check_rule.split(',').map((i) => {\r\n                if (i === val.value) {\r\n                  roleArr.push(val.label)\r\n                }\r\n                return roleArr\r\n              })\r\n            })\r\n            item.check_rule = roleArr.join()\r\n          } else if (item.check_flag === '2' && !commonBlank(item.check_rule)) {\r\n            // 远程审核  check_rule代表模板 菜单审核模板格式化\r\n            item.check_rule = this.dialog.form.config.check_rule.options.find(\r\n              (val) => {\r\n                if (item.check_rule === val.value) {\r\n                  return val.name\r\n                }\r\n              }\r\n            ).name\r\n          }\r\n          // table准入条件格式化\r\n          if (item.allow_rule) {\r\n            item.allow_rule = this.initElems(item.allow_rule)\r\n          }\r\n          // table禁止条件格式化\r\n          if (item.forbid_rule) {\r\n            item.forbid_rule = this.initElems(item.forbid_rule)\r\n          }\r\n        })\r\n        this.showLoading()\r\n      })\r\n    },\r\n    // 准入条件、禁止条件弹框显示\r\n    dialogShow(type) {\r\n      if (type === 'enable') this.isAllowRule = '1'\r\n      else this.isAllowRule = '2'\r\n      if (!commonBlank(this.dialog.form.defaultForm.menu_id)) {\r\n        this.dialog2.visible = true\r\n        // this.addEnter() // 初试化第一行表单\r\n        // 如果是修改 调用准入条件、禁止条件数据回显方法\r\n        // if (this.dialog.componentProps.oprate === 'edit') {\r\n        if (\r\n          !commonBlank(this.dialog.form.defaultForm.allow_rule) &&\r\n          this.isAllowRule === '1'\r\n        ) {\r\n          // 准入条件数据回显方法\r\n          this.rebackVal(this.dialog.form.defaultForm.allow_rule)\r\n          this.initData()\r\n        } else if (\r\n          !commonBlank(this.dialog.form.defaultForm.forbid_rule) &&\r\n          this.isAllowRule === '2'\r\n        ) {\r\n          // 禁止条件数据回显方法\r\n          this.rebackVal(this.dialog.form.defaultForm.forbid_rule)\r\n          this.initData()\r\n        }\r\n        // }\r\n      } else {\r\n        commonMsgWarn('请先选择需要配置的菜单', this)\r\n      }\r\n    },\r\n    /**\r\n     * 修改回显输入输出配置值\r\n     * @param {Object} param  选中行的数据\r\n     */\r\n    rebackVal(param) {\r\n      if (param) {\r\n        Object.assign(this.dialog2, {\r\n          enterObj: {},\r\n          enterNums: 0\r\n        })\r\n        // 设置输入配置值(对象数组)\r\n        const enterArr = param.split('#&#')\r\n        let arr = []\r\n        // 设置输入配置值(对象数组)\r\n        for (const item of enterArr) {\r\n          const arr2 = item.split('#@#')\r\n          arr2.shift()\r\n          arr = [...arr, ...arr2]\r\n          // console.log('回显arr', arr)\r\n        }\r\n        const { enterObj } = this.dialog2\r\n        for (const [index, val] of arr.entries()) {\r\n          const { enterNums } = this.dialog2\r\n          if (index % 5 === 0) {\r\n            enterObj['leftBrak' + enterNums] = val\r\n          } else if (index % 5 === 1) {\r\n            enterObj['typeData' + enterNums] = val\r\n          } else if (index % 5 === 2) {\r\n            enterObj['condition' + enterNums] = val\r\n          } else if (index % 5 === 3 && val.indexOf('#!#') < 0) {\r\n            // 除了  select+input 外的values数据\r\n            enterObj['values' + enterNums] = val\r\n          } else if (index % 5 === 3 && val.indexOf('#!#') > -1) {\r\n            if (val.slice(0, 1) === '0') {\r\n              // 当前机构\r\n              enterObj['valueOrganSelect' + enterNums] = val.slice(0, 1)\r\n              enterObj['values' + enterNums] = val.slice(4)\r\n            } else if (val.slice(0, 1) === '1') {\r\n              // 当前用户\r\n              enterObj['valueUserSelect' + enterNums] = val.slice(0, 1)\r\n              enterObj['values' + enterNums] = val.slice(4)\r\n            } else if (val.slice(0, 1) === '2') {\r\n              // 自定义机构\r\n              if (enterObj['typeData' + enterNums] === 'last_modi_organ') {\r\n                this.$set(enterObj, ['values' + enterNums], val.slice(4))\r\n                this.$set(\r\n                  enterObj,\r\n                  ['valueOrganSelect' + enterNums],\r\n                  val.slice(0, 1)\r\n                )\r\n              } else {\r\n                // 自定义用户\r\n                this.$set(enterObj, ['values' + enterNums], val.slice(4))\r\n                this.$set(\r\n                  enterObj,\r\n                  ['valueUserSelect' + enterNums],\r\n                  val.slice(0, 1)\r\n                )\r\n              }\r\n            }\r\n          } else {\r\n            if (val.startsWith('#&#')) {\r\n              enterObj['rightBrak' + enterNums] = '' // 右 ）为空''\r\n            } else {\r\n              enterObj['rightBrak' + enterNums] = val // 右 ）有值\r\n            }\r\n          }\r\n          if (index % 5 === 4) {\r\n            this.dialog2.enterNums++\r\n          }\r\n        }\r\n        // 设置条件(&&和||)\r\n        let conditions = param.match(/#&#(.+?)\\#@#/g) // #&#&&#@#   #&#||#@#\r\n        if (!commonBlank(conditions)) {\r\n          conditions = conditions.map((item) => {\r\n            return item.substring(3, 5) // 条件&&和||\r\n          })\r\n          this.dialog2.conditionNum = 1\r\n          for (const val of conditions) {\r\n            const { conditionNum } = this.dialog2\r\n            enterObj['operSym' + conditionNum] = val\r\n            this.dialog2.conditionNum++\r\n          }\r\n        }\r\n      }\r\n    },\r\n    /**\r\n     * 修改反显表单配置-(监听是新增还是修改)\r\n     */\r\n    initData() {\r\n      // '#@#'; // 公式行内分隔符\r\n      // '#&#'; // 公式行间分隔符\r\n      // '#!#'; // 组件select_group 下拉框/输入框值分隔符\r\n      const { enterNums, enterObj } = this.dialog2\r\n      this.dialog2.allEnterConfig = []\r\n      this.dialog2.enterNum = 0 // 输入配置的个数\r\n      const numArr = []\r\n      let nums = 0\r\n      while (nums < enterNums) {\r\n        numArr.push(nums)\r\n        nums++\r\n      }\r\n      numArr.forEach((item) => {\r\n        this.addEnter()\r\n      })\r\n      this.$nextTick(() => {\r\n        this.dialog2.allEnterData = { ...enterObj }\r\n        for (const item in this.dialog2.allEnterData) {\r\n          if (item.includes('typeData')) {\r\n            const num = parseInt(item.slice(-1))\r\n            const val = this.dialog2.allEnterData[item]\r\n            this.enterChange([val, num]) // 选择的值和后面表单的类型 回显输入框类型判断\r\n          }\r\n        }\r\n      })\r\n    },\r\n    /**\r\n     * 准入、禁止条件初始化数据源\r\n     */\r\n    initDataSource() {\r\n      const msg = { parameterList: [{}], sysMap: {}}\r\n      dataSource(msg).then((res) => {\r\n        this.elems = res.retMap.elems // 深拷贝\r\n        // 初始化  类型   下拉框数据(  (、后的下拉框)\r\n        this.elems.forEach((item) => {\r\n          this.dialog2.enterConfig.typeData.push({\r\n            value: item.elem_english,\r\n            name: item.elem_chinese,\r\n            show_type: item.show_type,\r\n            sources_type: item.sources_type,\r\n            data_sources: item.data_sources,\r\n            time_format_store: item.time_format_store\r\n          })\r\n        })\r\n      })\r\n    },\r\n    /**\r\n     * 添加输入配置规则\r\n     */\r\n    addEnter() {\r\n      // 所有的输入配置(数组对象)\r\n      this.dialog2.allEnterConfig = [\r\n        ...this.dialog2.allEnterConfig,\r\n        { ...this.dialog2.enterConfig, index: this.dialog2.enterNum }\r\n      ]\r\n      // 所有的输入配置数据(数组对象)\r\n      this.dialog2.allEnterData = {\r\n        ...this.dialog2.allEnterData,\r\n        ...this.createEnterData(this.dialog2.enterNum)\r\n      }\r\n      this.dialog2.enterNum++\r\n    },\r\n    /**\r\n     * 删除输入配置规则\r\n     * @param {Array} param // 删除的第几行和删除对应的数据\r\n     */\r\n    delEnter(param) {\r\n      this.dialog2.allEnterConfig.splice(param[1], 1)\r\n      for (const i in this.dialog2.enterData) {\r\n        delete this.dialog2.allEnterData[i + param[0]]\r\n      }\r\n      if (commonBlank(this.dialog2.allEnterConfig)) {\r\n        this.dialog2.enterNum = 0\r\n      }\r\n    },\r\n    /**\r\n     * 初始化输入配置下拉框值\r\n     */\r\n    initConfig() {\r\n      // 条件框 初始化\r\n      this.dialog2.enterConfig.condition = dictionaryFieds('J_SYMBOL')\r\n    },\r\n    /**\r\n     * 获取输入配置规则字段数据\r\n     * @param {Number} num // 数据后面的数字\r\n     */\r\n    createEnterData(num) {\r\n      return {\r\n        [`operSym${num}`]: '', // 或  与\r\n        [`leftBrak${num}`]: '', // (\r\n        [`typeData${num}`]: '', //  机构、时间、用户、开启\r\n        [`condition${num}`]: '', // 条件 大于、小于、等于...\r\n        [`valueOrganSelect${num}`]: '', // 自定义/当前机构\r\n        [`valueUserSelect${num}`]: '', // 自定义/当前用户\r\n        [`values${num}`]: '', // 值\r\n        [`rightBrak${num}`]: '' // ）\r\n      }\r\n    },\r\n    /**\r\n     * 切换typeData 设置value\r\n     * @param {Array} param // 选择的值和后面表单的类型\r\n     */\r\n    enterChange(param) {\r\n      if (param[0] === 'last_modi_date') {\r\n        this.dialog2.allEnterConfig[param[1]].type = 'datetime' // 日期框\r\n      }\r\n      if (\r\n        param[0] === 'is_lock' ||\r\n        param[0] === 'last_modi_organ' ||\r\n        param[0] === '0' ||\r\n        param[0] === '1' ||\r\n        param[0] === '2'\r\n      ) {\r\n        this.dialog2.allEnterConfig[param[1]].type = 'select_input' // select+input组合框\r\n      }\r\n      if (\r\n        param[0] !== 'last_modi_date' &&\r\n        param[0] !== 'is_lock' &&\r\n        param[0] !== 'last_modi_organ' &&\r\n        param[0] !== '0' &&\r\n        param[0] !== '1' &&\r\n        param[0] !== '2'\r\n      ) {\r\n        this.dialog2.allEnterConfig[param[1]].type = 'select' // select下拉框 // 格式化不同来源的数据\r\n        this.dialog2.allEnterConfig[param[1]].typeData.forEach((item) => {\r\n          if (param[0] === item.value) {\r\n            this.dialog2.allEnterConfig[param[1]].values = dictionaryFieds(\r\n              item.data_sources,\r\n              'UNIFY',\r\n              true\r\n            )\r\n          }\r\n        })\r\n      }\r\n      if (!commonBlank(param[2])) {\r\n        if (param[0] === '0' || param[0] === '1' || param[0] === '2') {\r\n          // 切换valueOrganSelect或者valueUserSelect的下拉框值时 只清空values的值  不清空valueOrganSelect和valueUserSelect的值\r\n          this.dialog2.allEnterData['values' + param[1]] = ''\r\n        } else if (param[0] === 'is_lock') {\r\n          // 切换typeData时清空values的值  如果是typeDate是最后修改用户  valueUserSelect的默认值是1\r\n          delete this.dialog2.allEnterData['valueOrganSelect' + param[1]] // 删除删除valueUserSelect属性属性\r\n          this.dialog2.allEnterData['values' + param[1]] = ''\r\n          this.dialog2.allEnterData['valueUserSelect' + param[1]] = '1'\r\n        } else if (param[0] === 'last_modi_organ') {\r\n          // 切换typeData时清空values的值  如果是typeDate是维护机构  valueOrganSelect的默认值是0\r\n          delete this.dialog2.allEnterData['valueUserSelect' + param[1]] // 删除valueUserSelect属性\r\n          this.dialog2.allEnterData['values' + param[1]] = ''\r\n          this.dialog2.allEnterData['valueOrganSelect' + param[1]] = '0'\r\n        } else {\r\n          this.$refs.enterForm.clearValidate() // 切换typeData时清空valueOrganSelect和valueUserSelect的表单校验\r\n          // 1. 切换typeData时清空values的值   2.如果是typeDate切换,同时清空valueOrganSelect和valueUserSelect的值\r\n          this.dialog2.allEnterData['values' + param[1]] = '' // 1\r\n          this.dialog2.allEnterData['valueOrganSelect' + param[1]] = '' // 2\r\n          this.dialog2.allEnterData['valueUserSelect' + param[1]] = '' // 2\r\n          // 删除valueUserSelect、valueOrganSelect属性\r\n          delete this.dialog2.allEnterData['valueOrganSelect' + param[1]]\r\n          delete this.dialog2.allEnterData['valueUserSelect' + param[1]]\r\n        }\r\n      }\r\n    },\r\n    // 准入条件、禁止条件弹窗关闭\r\n    dialog2Close() {\r\n      this.dialog2.visible = false\r\n      this.dialog2.allEnterConfig = []\r\n      this.dialog2.allEnterData = {}\r\n      this.dialog2.enterConfig.type = 'select' // 重新定义表单values的类型\r\n      this.dialog2.enterNum = 0 // 由于每次弹窗打开都会调用this.addEnter()方法 所以弹窗关闭时将enterNum重置为0\r\n    },\r\n    // 准入条件 禁止条件提交\r\n    dialog2Submit() {\r\n      this.$refs.enterForm.validate((valid, obj) => {\r\n        if (valid) {\r\n          // 提交表单操作\r\n          const { allEnterData, enterData } = this.dialog2\r\n          // 拿到allEnterData（例如condition0 condition2  condition3  的0 2 3  形成新的数组循环）\r\n          const indexArr = []\r\n          for (const item in allEnterData) {\r\n            if (item.indexOf('condition') > -1) {\r\n              indexArr.push(item.slice(-1))\r\n            }\r\n          }\r\n          // 拼接表单数据\r\n          let str = ''\r\n          let flag = false\r\n          indexArr.forEach((key) => {\r\n            for (const i in enterData) {\r\n              // console.log('i', i) // operSym leftBrak typeData condition valueOrganSelect valueUserSelect values rightBrak\r\n              if (!(key === '0' && i === 'operSym')) {\r\n                // console.log(key, i)\r\n                if (i === 'operSym' && key !== '0') {\r\n                  // || 和  &&的拼接\r\n                  str += '#&#' + allEnterData[i + key]\r\n                } else {\r\n                  // 判断有没有valueOrganSelect valueUserSelect存在\r\n                  if (\r\n                    i === 'valueUserSelect' ||\r\n                    i === 'valueOrganSelect' ||\r\n                    i === 'values'\r\n                  ) {\r\n                    // 包含属性valueUserSelect 'valueOrganSelect'且值不为空\r\n                    if (i === 'valueUserSelect' || i === 'valueOrganSelect') {\r\n                      if (\r\n                        !commonBlank(allEnterData[i + key]) &&\r\n                        Object.prototype.hasOwnProperty.call(\r\n                          allEnterData,\r\n                          i + key\r\n                        ) &&\r\n                        i + key.slice(-1)\r\n                      ) {\r\n                        // console.log(\r\n                        //   i + key,\r\n                        //   allEnterData[i + key],\r\n                        //   allEnterData\r\n                        // )\r\n                        str += '#@#' + allEnterData[i + key] + '#!#'\r\n                        flag = true\r\n                      }\r\n                    } else {\r\n                      if (flag) {\r\n                        str += allEnterData[i + key]\r\n                      } else {\r\n                        str += '#@#' + allEnterData[i + key]\r\n                      }\r\n                      flag = false\r\n                    }\r\n                  } else {\r\n                    // 没有valueOrganSelect valueUserSelect存在\r\n                    if (\r\n                      Object.prototype.hasOwnProperty.call(\r\n                        allEnterData,\r\n                        i + key\r\n                      )\r\n                    ) {\r\n                      if (\r\n                        allEnterData[i + key].indexOf('last_modi_date') > -1\r\n                      ) {\r\n                        // 如果values是日期进行格式化\r\n                        str += '#@#' + allEnterData[i + key] // typeData的值\r\n                        allEnterData['values' + key] = dateTimeFormat(\r\n                          allEnterData['values' + key] // 相对应日期框的值格式化\r\n                        )\r\n                      } else {\r\n                        str += '#@#' + allEnterData[i + key]\r\n                      }\r\n                    }\r\n                  }\r\n                }\r\n              }\r\n            }\r\n          })\r\n          // 检验判断括号是否闭合\r\n          const arr2 = str.split('#@#')\r\n          const arr3 = [] // 括号数组\r\n          arr2.forEach((item) => {\r\n            if (item.indexOf('(') !== -1 || item.indexOf(')') !== -1) {\r\n              arr3.push(item.slice(0, 1))\r\n            }\r\n          })\r\n          const bracketsArr = arr3.join('').replaceAll('()', '') // 对括号数据进行数据处理，把（）替换成''\r\n          if (bracketsArr.length) {\r\n            commonMsgInfo('配置条件有误', this)\r\n            return\r\n          }\r\n          // 左括号开头和有括号闭合，防止出现成对)(\r\n          // 赋值\r\n          if (this.isAllowRule === '1') {\r\n            // 准入条件提交\r\n            this.dialog.form.defaultForm.allow_rule = str\r\n          } else {\r\n            // 禁止条件提交\r\n            this.dialog.form.defaultForm.forbid_rule = str\r\n          }\r\n          this.dialog2.visible = false\r\n        }\r\n      })\r\n    },\r\n    // 表格选择单行、多行\r\n    handleSelectionChange(val) {\r\n      const currentRow = val\r\n      if (currentRow.length > 1) {\r\n        currentRow.sort(function(a, b) {\r\n          return a.index - b.index\r\n        }) // 选中行排序:升序\r\n      }\r\n      this.table.currentRow = val\r\n    },\r\n\r\n    /**\r\n     *  准入条件、禁止条件  table表格数据 格式化方法\r\n     * @param exp:需格式化的数据 */\r\n    initElems(exp) {\r\n      // '#@#'; // 公式行内分隔符\r\n      // '#&#'; // 公式行间分隔符\r\n      // '#!#'; // 组件select_group 下拉框/输入框值分隔符\r\n      let show = ''\r\n      const lineArray = exp.split('#&#') // 切割成几个条件数组\r\n      lineArray.forEach((item) => {\r\n        const condArray = item.split('#@#')\r\n        for (let i = 0; i < this.elems.length; i++) {\r\n          if (condArray[2] === this.elems[i].elem_english) {\r\n            // 处理 && ||\r\n            if (condArray[0] === '&&') {\r\n              condArray[0] = '且'\r\n            } else if (condArray[0] === '||') {\r\n              condArray[0] = '或'\r\n            }\r\n            // 处理values的数据\r\n            const getValue = condArray[4]\r\n            let formatValue = ''\r\n            // 如果是select+input组合框  最后修改用户  维护机构数据处理\r\n            if (getValue.indexOf('#!#') > -1) {\r\n              const arr = getValue.split('#!#')\r\n              if (arr[0] === '0') {\r\n                formatValue = '当前机构'\r\n              } else if (arr[0] === '1') {\r\n                formatValue = '当前用户'\r\n              } else if (arr[0] === '2') {\r\n                formatValue = arr[1]\r\n              }\r\n            } else {\r\n              // 数据源\r\n              // 展示类型##1-文本框 2-日期框 3-普通下拉框 4-树形下拉框\r\n              if (this.elems[i].show_type === '1') {\r\n                formatValue = getValue\r\n              } else if (this.elems[i].show_type === '2') {\r\n                // formatValue = dateTimeFormat(getValue)\r\n                formatValue = getValue\r\n              } else if (\r\n                this.elems[i].show_type === '3' ||\r\n                this.elems[i].show_type === '4'\r\n              ) {\r\n                // 数据来源##0-无 1-数据字典 2-上辖机构 3-下辖机构 4-外表 5-自定义\r\n                if (\r\n                  this.elems[i].sources_type === '1' ||\r\n                  this.elems[i].sources_type === '4' ||\r\n                  this.elems[i].sources_type === '5'\r\n                ) {\r\n                  dictionaryFieds(\r\n                    this.elems[i].data_sources,\r\n                    'UNIFY',\r\n                    true\r\n                  ).forEach((key) => {\r\n                    if (getValue === key.value) {\r\n                      formatValue = key.label\r\n                    }\r\n                  })\r\n                } else if (\r\n                  this.elems[i].sources_type === '2' ||\r\n                  this.elems[i].sources_type === '3'\r\n                ) {\r\n                  formatValue = organNameFormat(getValue)\r\n                }\r\n              }\r\n            }\r\n            show +=\r\n              condArray[0] +\r\n              condArray[1] +\r\n              this.elems[i].elem_chinese +\r\n              condArray[3] +\r\n              formatValue +\r\n              condArray[5]\r\n          }\r\n        }\r\n      })\r\n      return show\r\n    },\r\n    /**\r\n     * 新增、修改弹出框 - 关闭\r\n     * 弹出框显示隐藏配置*/\r\n    dialogClose() {\r\n      this.dialog.visible = false\r\n    },\r\n    /**\r\n     * btn - 新增*/\r\n    handleAdd() {\r\n      this.dialog.componentProps.title = '新增'\r\n      this.dialog.componentProps.oprate = 'add'\r\n      this.dialog.form.config.ser_port.hidden = true // 本地审核角色隐藏\r\n      this.dialog.form.config.check_rule.hidden = true // 远程审批模板隐藏\r\n      this.dialog.visible = true\r\n    },\r\n    /**\r\n     * btn - 编辑*/\r\n    handleModify() {\r\n      this.firstModifyFlag = 1\r\n      const rows = this.table.currentRow.length\r\n      if (rows === 0) {\r\n        commonMsgWarn('请选择要修改的行', this)\r\n        return\r\n      } else if (rows > 1) {\r\n        commonMsgWarn('仅支持单笔操作!', this)\r\n        return\r\n      }\r\n      this.dialog.componentProps = {\r\n        ...this.dialog.componentProps,\r\n        title: '编辑',\r\n        oprate: 'edit'\r\n      }\r\n      this.dialog.visible = true\r\n      if (this.table.currentRow[0].check_flag === '1') {\r\n        this.dialog.form.config.ser_port.hidden = false\r\n        this.dialog.form.config.check_rule.hidden = true\r\n      }\r\n      if (this.table.currentRow[0].check_flag === '2') {\r\n        this.dialog.form.config.ser_port.hidden = true\r\n        this.dialog.form.config.check_rule.hidden = false\r\n      }\r\n      this.$nextTick(() => {\r\n        // 弹出框加载完成后赋值、\r\n        const keys = Object.keys(this.dialog.form.defaultForm)\r\n        keys.forEach((item) => {\r\n          if (item === 'button_id') {\r\n            // 按钮名称赋值\r\n            this.dialog.form.defaultForm[item] =\r\n              this.table.currentRow[0][item].split(',')\r\n          } else if (item === 'allow_rule') {\r\n            // 准入条件赋值\r\n            this.dialog.form.defaultForm[item] =\r\n              this.elemsVal[this.table.currentRow[0].index].allow_rule\r\n          } else if (item === 'forbid_rule') {\r\n            // 禁止条件赋值\r\n            this.dialog.form.defaultForm[item] =\r\n              this.elemsVal[this.table.currentRow[0].index].forbid_rule\r\n          } else if (\r\n            // 审核方式为1-本地审核\r\n            item === 'ser_port' &&\r\n            this.table.currentRow[0].check_flag === '1'\r\n          ) {\r\n            const arr = []\r\n            this.table.currentRow[0]['check_rule'].split(',').forEach((val) => {\r\n              arr.push(val.split('-')[0])\r\n            })\r\n            this.dialog.form.defaultForm['ser_port'] = arr\r\n          } else if (\r\n            item === 'check_rule' &&\r\n            this.table.currentRow[0].check_flag === '2'\r\n          ) {\r\n            // 审核方式为2-远程审核\r\n            this.dialog.form.config.check_rule.options.forEach((val) => {\r\n              if (this.table.currentRow[0].check_rule === val.label) {\r\n                this.dialog.form.defaultForm['check_rule'] = val.value\r\n              }\r\n            })\r\n          } else {\r\n            this.dialog.form.defaultForm[item] = this.table.currentRow[0][item]\r\n          }\r\n        })\r\n      })\r\n    },\r\n    /**\r\n     * 角色获取\r\n     */\r\n    roleNo() {\r\n      if (this.dialog.form.config.ser_port.options.length < 1) {\r\n        const role_level = this.$store.getters.organLevel + '00'\r\n        const msg = {\r\n          oper_type: 'rolelist',\r\n          role_level: role_level\r\n        }\r\n        roleNoTree(msg).then((res) => {\r\n          const { rolelist } = res.retMap\r\n          this.rolelist = rolelist\r\n          this.dialog.form.config.ser_port.options = treeDataTranslate(rolelist)\r\n        })\r\n      }\r\n    },\r\n    /**\r\n     * 菜单获取\r\n     */\r\n    menuTree() {\r\n      if (this.dialog.form.config.menu_id.options.length < 1) {\r\n        const msg = {\r\n          parameterList: [{}]\r\n        }\r\n        aduitMenuTree(msg).then((res) => {\r\n          const { list } = res.retMap\r\n          this.dialog.form.config.menu_id.options = this.treeDataTranslate(list)\r\n        })\r\n      }\r\n    },\r\n    /**\r\n     * 树形数据转换: 菜单配置\r\n     * @param {*} data\r\n     */\r\n    treeDataTranslate(obj) {\r\n      const data = obj\r\n      const parentID = '#000000'\r\n      const id = 'menu_id'\r\n      const pid = 'parent_id'\r\n      const name = 'menu_name'\r\n      // 建立个树形结构,需要定义个最顶层的父节点，pId是1\r\n      let parents = []\r\n      for (let i = 0; i < data.length; i++) {\r\n        if (data[i][pid] === parentID) {\r\n          const obj = {\r\n            label: data[i][name],\r\n            id: data[i][id],\r\n            pid: data[i][pid],\r\n            ...data[i],\r\n            children: []\r\n          }\r\n          parents = [...parents, obj] // 数组加数组值\r\n        }\r\n      }\r\n      let datas = []\r\n      if (data.length > 0) {\r\n        datas = this.children(parents, data, id, pid, name)\r\n      }\r\n      // 调用子节点方法,参数为父节点的数组\r\n      return datas\r\n    },\r\n    /**\r\n     * 菜单结构初始化\r\n     */\r\n    children(parent, data, id, pid, name) {\r\n      if (data === undefined) {\r\n        return []\r\n      }\r\n      if (parent.length === 0) {\r\n        return\r\n      }\r\n      if (data.length !== 0) {\r\n        for (let i = 0; i < parent.length; i++) {\r\n          for (let j = 0; j < data.length; j++) {\r\n            if (parent[i].id === data[j][pid]) {\r\n              const obj = {\r\n                label: data[j][name],\r\n                id: data[j][id],\r\n                pid: data[i][pid],\r\n                children: [],\r\n                ...data[j]\r\n              }\r\n              parent[i].children.push(obj)\r\n            }\r\n          }\r\n          this.children(parent[i].children, data, id, pid, name)\r\n        }\r\n      }\r\n      return parent\r\n    },\r\n    /**\r\n     * 初始化按钮菜单映射集合\r\n     */\r\n    initButtonMap() {\r\n      if (!this.menuMap) {\r\n        const singleMap = new Map()\r\n        const msg = {}\r\n        btnname(msg).then((res) => {\r\n          const { list } = res.retMap\r\n          list.forEach((item) => {\r\n            if (singleMap.has(item.parent_id)) {\r\n              singleMap.get(item.parent_id).add(item)\r\n            } else {\r\n              const singleSet = new Set()\r\n              singleSet.add(item)\r\n              singleMap.set(item.parent_id, singleSet)\r\n            }\r\n          })\r\n        })\r\n        this.menuMap = singleMap\r\n      }\r\n    },\r\n    /* 菜单名称发生改变事件 */\r\n    changeButtonName(menuId) {\r\n      const buttonArr = this.menuMap.get(menuId)\r\n      const btnArray = []\r\n      if (buttonArr) {\r\n        buttonArr.forEach((tmp) => {\r\n          const valueS = Object.assign({}, tmp, {\r\n            id: tmp.button_id,\r\n            label: tmp.button_name,\r\n            value: tmp.button_id\r\n          })\r\n          btnArray.push(valueS)\r\n        })\r\n        this.dialog.form.config.button_id.options = btnArray\r\n        if (this.firstModifyFlag === 1) {\r\n          this.firstModifyFlag = 0\r\n        } else {\r\n          this.dialog.form.defaultForm.button_id = []\r\n        }\r\n      }\r\n    },\r\n    /**\r\n     * 远程审批模板\r\n     */\r\n    checkrule() {\r\n      const msg = {\r\n        parameterList: [],\r\n        role_no: this.$store.getters.roleNo\r\n      }\r\n      checkrule(msg).then((res) => {\r\n        const { data } = res.retMap\r\n        const checkArray = []\r\n        data.map((item) => {\r\n          const valueS = Object.assign({}, item, {\r\n            label: item.name,\r\n            value: item.value\r\n          })\r\n          checkArray.push(valueS)\r\n        })\r\n        // 获取外部字典\r\n        this.dialog.form.config.check_rule.options = checkArray\r\n      })\r\n    },\r\n    /**\r\n     * 弹出框 - 确认弹框类型 */\r\n    dialogSubmit() {\r\n      const param = this.dialog.componentProps.oprate\r\n      if (param === 'add') {\r\n        this.dialogAddSubmit()\r\n      } else {\r\n        this.dialogEditSubmit()\r\n      }\r\n    },\r\n    /**\r\n     * 弹出框 - 确认 → 新增*/\r\n    dialogAddSubmit() {\r\n      const formData1 = Object.assign({}, this.dialog.form.defaultForm)\r\n      commonMsgConfirm('是否确认提交当前数据', this, (param) => {\r\n        if (param) {\r\n          const msg = {\r\n            parameterList: [],\r\n            oldMsg: {},\r\n            menu_id: formData1.menu_id, // 菜单id\r\n            button_id: formData1.button_id.join(),\r\n            allow_rule: formData1.allow_rule,\r\n            forbid_rule: formData1.forbid_rule,\r\n            check_flag: formData1.check_flag, // 审核方式\r\n            export_file: formData1.export_file,\r\n            check_rule_role: commonBlank(formData1.ser_port)\r\n              ? ''\r\n              : formData1.ser_port.join(), // 审批角色\r\n            check_rule_module: formData1.check_rule, // 审批模板\r\n            last_modi_date: dateNowFormat()\r\n          }\r\n          add(msg)\r\n            .then((res) => {\r\n              commonMsgSuccess('新增成功！', this)\r\n              this.dialog.visible = false\r\n              // 将新增的菜单配置更新到vuex里\r\n              this.commitMenu(formData1)\r\n              this.queryList(1) // 重新查询\r\n              // this.showLoading()\r\n            })\r\n            .catch(() => {\r\n              this.showLoading()\r\n            })\r\n        }\r\n      })\r\n    },\r\n    /**\r\n     * 弹出框 - 确认 - 编辑*/\r\n    dialogEditSubmit() {\r\n      const formData1 = Object.assign({}, this.dialog.form.defaultForm)\r\n      commonMsgConfirm('是否确认提交当前数据？', this, (param) => {\r\n        if (param) {\r\n          this.showLoading()\r\n          const msg = {\r\n            // 请求参数\r\n            parameterList: [],\r\n            menu_id: formData1.menu_id,\r\n            button_id: formData1.button_id.join(),\r\n            allow_rule: formData1.allow_rule,\r\n            forbid_rule: formData1.forbid_rule,\r\n            check_flag: formData1.check_flag,\r\n            export_file: formData1.export_file,\r\n            check_rule_role: commonBlank(formData1.ser_port)\r\n              ? ''\r\n              : formData1.ser_port.join(),\r\n            check_rule_module: commonBlank(formData1.check_rule)\r\n              ? ''\r\n              : formData1.check_rule,\r\n            last_modi_date: dateNowFormat(),\r\n            oldMsg: {\r\n              ...this.table.currentRow[0],\r\n              allow_rule:\r\n                this.elemsVal[this.table.currentRow[0].index].allow_rule,\r\n              forbid_rule:\r\n                this.elemsVal[this.table.currentRow[0].index].forbid_rule\r\n            }\r\n          }\r\n          modify(msg).then((res) => {\r\n            const index = this.table.currentRow[0].index\r\n            this.table.componentProps.data.splice(index, 1, formData1)\r\n            commonMsgSuccess(res.retMsg, this)\r\n            this.queryList(1) // 重新查询\r\n            this.showLoading()\r\n            this.dialog.visible = false\r\n            // 将新增的菜单审核方式更新到vuex\r\n            this.commitMenu(formData1)\r\n          })\r\n        }\r\n      })\r\n    },\r\n    /**\r\n     * 新增和修改的菜单配置更新到vuex里\r\n     * @param formData1新增能修改弹窗的\r\n     */\r\n    commitMenu(formData1) {\r\n      // 先循环menu_id  再去循环button_id  替换修改的button_id\r\n      const menuAuditData = this.$store.getters.menuAuditData\r\n      for (const key in menuAuditData) {\r\n        if (key === formData1.menu_id) {\r\n          for (const k in menuAuditData[key]) {\r\n            // formData1.button_id.join()是新增能的两个按钮id\r\n            formData1.button_id.forEach((item) => {\r\n              if (item === k) {\r\n                menuAuditData[key][item] = {\r\n                  allow_rule: formData1.allow_rule,\r\n                  button_id: formData1.button_id.join(),\r\n                  check_flag: formData1.check_flag, // 审核方式\r\n                  check_rule:\r\n                    formData1.check_flag === '0'\r\n                      ? ''\r\n                      : formData1.check_flag === '1'\r\n                        ? formData1.ser_port.join()\r\n                        : formData1.check_rule, // '' 或 审批角色 或 审批模板\r\n                  forbid_rule: formData1.forbid_rule,\r\n                  menu_id: formData1.menu_id,\r\n                  export_file: formData1.export_file\r\n                }\r\n              }\r\n            })\r\n          }\r\n          this.$store.commit('user/MENU_AUDIT_DATA', menuAuditData)\r\n        }\r\n      }\r\n    },\r\n    /**\r\n     * 调用基线审核流程详情\r\n     * @param {Object} row 当前行数据\r\n     */\r\n    ckeckPath(row) {\r\n      this.table.currentRow = row\r\n      this.dialog.form.config.check_rule.options.forEach((item) => {\r\n        if (item.label === row.check_rule) {\r\n          this.flowConfig.module_id = item.value // 参数传递\r\n        }\r\n      })\r\n      this.flowConfig.visible = true\r\n    },\r\n    // 流程弹窗关闭\r\n    dialogFlowClose() {\r\n      this.flowConfig.visible = false\r\n    },\r\n    /**\r\n     *页码更新 */\r\n    getList(pageParam) {\r\n      const { currentPage, pageSize } = pageParam\r\n      this.table.pageList.pageSize = pageSize\r\n      this.table.pageList.currentPage = currentPage\r\n      this.queryList()\r\n    },\r\n    /**\r\n     * 加载中动画配置*/\r\n    showLoading() {\r\n      this.listLoading = !this.listLoading\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style scoped lang=\"scss\">\r\n.checkRule {\r\n  display: inline-block;\r\n  color: rgba(0, 0, 255, 0.662);\r\n  cursor: pointer;\r\n  &:hover {\r\n    color: #e9313e;\r\n  }\r\n}\r\n.el_bottom {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  span {\r\n    color: #008000;\r\n    font-size: 13px;\r\n    margin-top: 10px;\r\n  }\r\n}\r\n.content {\r\n  height: 40rem;\r\n  overflow-y: auto;\r\n  overflow-x: hidden;\r\n  .titles {\r\n    font-size: 16px;\r\n    line-height: 30px;\r\n    padding-bottom: 20px;\r\n    text-align: left;\r\n    .addBtns {\r\n      padding-left: 10px;\r\n      cursor: pointer;\r\n      // &:hover {\r\n      //   color: red;\r\n      // }\r\n    }\r\n  }\r\n}\r\n</style>\r\n"]}]}