package com.sunyard.etl.custom.handler;

import java.io.BufferedReader;
import java.io.File;
import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.InputStreamReader;
import java.io.OutputStreamWriter;
import java.io.PrintWriter;
import java.text.SimpleDateFormat;
import com.sunyard.etl.system.common.Constants;
import org.springframework.stereotype.Service;

import com.sunyard.etl.system.dao.DataDateDAO;
import com.sunyard.etl.system.dao.impl.DataDateDAOImpl;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.enums.JobStartEnum;
import com.xxl.job.core.handler.IJobHandler;
import com.xxl.job.core.handler.annotation.JobHandler;
import com.xxl.job.core.log.XxlJobLogger;

@JobHandler(value = "FileFormat_CORD0030", name = "CORD0030文件格式化")
@Service
public class FileFormat_CORD0030 extends IJobHandler {

	private static final long serialVersionUID = 1L;

	private static DataDateDAO dateDao = new DataDateDAOImpl();

	@Override
	public ReturnT<String> execute(String jobId, String... arg1) throws Exception {
		XxlJobLogger.log("开始CORD0030文件格式化...");
		String jobDate = dateDao.getDataDate();
		if (null != arg1[0]) {
			String dirPath = arg1[0].toString().replace("@", jobDate);
			File dir = new File(dirPath);
			if (!dir.isDirectory()) {
				XxlJobLogger.log("INFO: 资源不足，目录不存在：" + dir, jobId + "");
				return new ReturnT<String>(ReturnT.FAIL_CODE, JobStartEnum.TASK_STATE_NO_RESOURCE.getCode(),
						"文件目录" + dir.getPath() + "不存在");
			}
			File preFile = new File(dir, "CORD0030_"+jobDate+".txt");
			File file = new File(dir, "CORD0030_"+jobDate+"_proc.txt");
			if (file.exists()) {
				XxlJobLogger.log("INFO: 文件已存在，先删除后再转换：" + file.getPath(), jobId + "");
				file.delete();
			}
			PrintWriter pw;
			pw = new PrintWriter(new OutputStreamWriter(new FileOutputStream(file), "GBK"));
			if (preFile.length()==0) {
				pw.print("|||");
				pw.flush();
				pw.close();
				XxlJobLogger.log("INFO: 源文件不存在，生成转换文件：" + preFile.getPath() + " >> " + file.getPath(), jobId + "");
				return ReturnT.SUCCESS;
			}
			BufferedReader br = new BufferedReader(new InputStreamReader(new FileInputStream(preFile), "GBK"));
			
			String line = null;
			String siteNo = "";
			String siteName = "";
			String occurDate = "";
			try {
				while ((line = br.readLine()) != null) {
					if(line.contains("1                 ") && line.contains("-CORD0030")){
						siteNo = line.substring(line.indexOf("( ")+"( ".length(),line.indexOf("-CORD0030"));
						continue;
					} 
					if(line.contains("机构 :") && line.contains("日期 :")){
						siteName = line.substring(line.indexOf("机构 :  ")+"机构 :  ".length(),line.indexOf("日期 :")).trim().replace("　", "");
						occurDate = line.substring(line.indexOf("日期 : ")+"日期 : ".length(),line.indexOf("日期 : ")+"日期 : ".length()+10).trim();
						occurDate = new SimpleDateFormat("yyyyMMdd").format(new SimpleDateFormat("yyyy/MM/dd").parse(occurDate));
						continue;
					}
					if("".equals(line.trim())||(line.contains("交易柜员号") && line.contains("交易流水号") && line.contains("授权柜员网点号"))||line.contains("============")||"核准清单".equals(line.trim())){
						continue;
					}
					String newLine = siteNo+"|"+siteName+"|"+occurDate+"|"
							+line.substring(2,9)+"|"	//交易柜员号
							+line.substring(14,23)+"|"	//交易流水号
							+line.substring(34,42)+"|"	//交易时间
							+line.substring(44,50)+"|"	//交易代码
							+line.substring(54,61)+"|"	//授权柜员号
							+line.substring(66,71)+"|"	//授权柜员网点号
							+line.substring(83);	//授权原因
					pw.print(newLine+System.getProperty("line.separator"));
				}
			} catch (IOException e) {
				e.printStackTrace();
			}finally{
				pw.flush();
				pw.close();
			}
			XxlJobLogger.log("INFO: 文件转换成功：" + file.getPath(), jobId + "");
			return ReturnT.SUCCESS;
		}
		return ReturnT.FAIL;
	}

	public static void main(String[] args) {
		FileFormat_CORD0030 ff = new FileFormat_CORD0030();
		try {
			ff.execute("9", "C:\\@");
		} catch (Exception e) {
			// TODO Auto-generated catch block
			e.printStackTrace();
		}
	}
}
