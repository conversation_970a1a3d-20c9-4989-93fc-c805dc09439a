{"remainingRequest": "E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\node_modules\\babel-loader\\lib\\index.js!E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\node_modules\\sunui\\src\\components\\Dialog\\SunSysLinkDialog\\index.vue?vue&type=script&lang=js&", "dependencies": [{"path": "E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\node_modules\\sunui\\src\\components\\Dialog\\SunSysLinkDialog\\index.vue", "mtime": 499162500000}, {"path": "E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\babel.config.js", "mtime": 1686019818359}, {"path": "E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1655715099000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KCmV4cG9ydCBkZWZhdWx0IHsKICBuYW1lOiAnU3VuU3lzTGlua0RpYWxvZycsCiAgY29tcG9uZW50czoge30sCiAgcHJvcHM6IHsKICAgIGRpYWxvZ0NvbmZpZzogewogICAgICB0eXBlOiBPYmplY3QsCiAgICAgIGRlZmF1bHQ6IGZ1bmN0aW9uIF9kZWZhdWx0KCkgewogICAgICAgIHJldHVybiB7CiAgICAgICAgICB0aXRsZTogJycsCiAgICAgICAgICAvLyDlvLnmoYbmoIfpopgKICAgICAgICAgIHZpc2libGU6ICcnLAogICAgICAgICAgLy8g5pi+56S66ZqQ6JePCiAgICAgICAgICBzcmNVcmw6ICcnIC8vIOmTvuaOpeWcsOWdgAogICAgICAgIH07CiAgICAgIH0KICAgIH0KICB9LAogIGRhdGE6IGZ1bmN0aW9uIGRhdGEoKSB7CiAgICByZXR1cm4gewogICAgICAvLyBkaWFsb2dGdWxsOiBmYWxzZSAvL+aYr+WQpuWFqOWxjwogICAgfTsKICB9LAogIGNyZWF0ZWQ6IGZ1bmN0aW9uIGNyZWF0ZWQoKSB7fSwKICBtb3VudGVkOiBmdW5jdGlvbiBtb3VudGVkKCkge30sCiAgbWV0aG9kczogewogICAgLyoqDQogICAgICog5by55qGG5YWz6ZetDQogICAgICovCiAgICBkaWFsb2dDbG9zZTogZnVuY3Rpb24gZGlhbG9nQ2xvc2UoKSB7CiAgICAgIHZhciBfdGhpcyA9IHRoaXM7CiAgICAgIHRoaXMuJG5leHRUaWNrKGZ1bmN0aW9uICgpIHsKICAgICAgICBfdGhpcy5kaWFsb2dGdWxsID0gZmFsc2U7CiAgICAgICAgX3RoaXMuJGVtaXQoJ2RpYWxvZ0Nsb3NlJywgZmFsc2UpOwogICAgICB9KTsKICAgIH0KICAgIC8qKg0KICAgICAqIOW8ueeql+aUvuWkpw0KICAgICAqLwogICAgLy8gZGlhbG9nZnVsbCgpIHsKICAgIC8vIHRoaXMuZGlhbG9nRnVsbCA9IHRydWUKICAgIC8vIH0KICB9Cn07"}, {"version": 3, "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAgDA;EACAA;EACAC;EACAC;IACAC;MACAC;MACAC;QACA;UACAC;UAAA;UACAC;UAAA;UACAC;QACA;MACA;IACA;EACA;EACAC;IACA;MACA;IAAA,CACA;EACA;EACAC;EACAC;EAEAC;IACA;AACA;AACA;IACAC;MAAA;MACA;QACA;QACA;MACA;IACA;IACA;AACA;AACA;IACA;IACA;IACA;EACA;AACA", "names": ["name", "components", "props", "dialogConfig", "type", "default", "title", "visible", "srcUrl", "data", "created", "mounted", "methods", "dialogClose"], "sourceRoot": "node_modules/sunui/src/components/Dialog/SunSysLinkDialog", "sources": ["index.vue"], "sourcesContent": ["<template>\r\n  <div class=\"urlDialog\">\r\n    <el-dialog\r\n      v-dialogDrag\r\n      top=\"1rem\"\r\n      :title=\"dialogConfig.title\"\r\n      :visible.sync=\"dialogConfig.visible\"\r\n      :close-on-click-modal=\"false\"\r\n      width=\"95%\"\r\n      height=\"95%\"\r\n      :before-close=\"dialogClose\"\r\n      v-bind=\"dialogConfig\"\r\n    >\r\n      <template slot=\"title\">\r\n        <div class=\"avue-crud__dialog__header\">\r\n          <span class=\"el-dialog__title\">\r\n            <span\r\n              style=\"\r\n                display: inline-block;\r\n                width: 3px;\r\n                height: 20px;\r\n                margin-right: 5px;\r\n                float: left;\r\n                margin-top: 2px;\r\n              \"\r\n            />\r\n            {{ dialogConfig.title }}\r\n          </span>\r\n          <div class=\"avue-crud__dialog__menu\">\r\n            <!--  <i v-if=\"dialogFull\" class=\"el-icon-minus\" /> -->\r\n            <i class=\"el-icon-full-screen\" />\r\n          </div>\r\n        </div>\r\n      </template>\r\n\r\n      <div>\r\n        <iframe\r\n          :src=\"dialogConfig.srcUrl\"\r\n          frameborder=\"0\"\r\n          width=\"100%\"\r\n          height=\"600px\"\r\n        />\r\n      </div>\r\n    </el-dialog>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nexport default {\r\n  name: 'SunSysLinkDialog',\r\n  components: {},\r\n  props: {\r\n    dialogConfig: {\r\n      type: Object,\r\n      default: () => {\r\n        return {\r\n          title: '', // 弹框标题\r\n          visible: '', // 显示隐藏\r\n          srcUrl: '' // 链接地址\r\n        }\r\n      }\r\n    }\r\n  },\r\n  data() {\r\n    return {\r\n      // dialogFull: false //是否全屏\r\n    }\r\n  },\r\n  created() {},\r\n  mounted() {},\r\n\r\n  methods: {\r\n    /**\r\n     * 弹框关闭\r\n     */\r\n    dialogClose() {\r\n      this.$nextTick(() => {\r\n        this.dialogFull = false\r\n        this.$emit('dialogClose', false)\r\n      })\r\n    }\r\n    /**\r\n     * 弹窗放大\r\n     */\r\n    // dialogfull() {\r\n    // this.dialogFull = true\r\n    // }\r\n  }\r\n}\r\n</script>\r\n<style lang=\"scss\" scoped>\r\n::v-deep {\r\n  .el-dialog__header {\r\n    padding: 15px 20px 15px;\r\n  }\r\n  .el-dialog__headerbtn {\r\n    top: 15px;\r\n  }\r\n  .avue-crud__dialog__header {\r\n    display: flex;\r\n    align-items: center;\r\n    justify-content: space-between;\r\n  }\r\n  .el-dialog__headerbtn .el-dialog__close {\r\n    margin-top: 3px;\r\n  }\r\n  .el-dialog__title {\r\n    color: rgba(0, 0, 0, 0.85);\r\n    font-weight: 500;\r\n    word-wrap: break-word;\r\n  }\r\n  .avue-crud__dialog__menu {\r\n    margin-right: 15px;\r\n    float: left;\r\n  }\r\n  .avue-crud__dialog__menu i {\r\n    color: #909399;\r\n    font-size: 15px;\r\n    padding-right: 15px;\r\n  }\r\n  .el-icon-full-screen,\r\n  .el-icon-minus {\r\n    cursor: pointer;\r\n    &:hover {\r\n      color: #1890ff;\r\n    }\r\n  }\r\n  .el-icon-full-screen:before {\r\n    content: '\\e719';\r\n  }\r\n}\r\n</style>\r\n"]}]}