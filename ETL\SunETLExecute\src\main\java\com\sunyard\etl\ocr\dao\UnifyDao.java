package com.sunyard.etl.ocr.dao;

import com.sunyard.etl.ocr.bean.DataSource;
import com.sunyard.etl.ocr.common.OCRConstants;
import org.sunyard.util.dbutil.DBHandler;
import javax.sql.rowset.CachedRowSet;
import java.sql.SQLException;

public class UnifyDao {
    private String unifyDsId;

    public UnifyDao(DataSource unifyDs) {
        this.unifyDsId = unifyDs.getName();
    }

    public String getParentOrgan(String organNo) throws SQLException {
        StringBuilder sb = new StringBuilder();
        DBHandler dbHandler = new DBHandler(unifyDsId);
        String sq = "select parent_organ from sm_organ_parent_tb where organ_no = ?";
        CachedRowSet rs = dbHandler.queryRs(sq, new Object[]{organNo});

        while(rs.next()) {
            sb.append(",'" + rs.getString("parent_organ") + "'");
        }

        return sb.length() > 0 ? sb.substring(1) : null;
    }

}
