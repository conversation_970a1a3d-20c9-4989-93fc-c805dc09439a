{"remainingRequest": "E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1\\数字运营平台-统一门户工程\\dopUnify\\node_modules\\babel-loader\\lib\\index.js!E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1\\数字运营平台-统一门户工程\\dopUnify\\node_modules\\@babel\\runtime\\helpers\\iterableToArrayLimit.js", "dependencies": [{"path": "E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1\\数字运营平台-统一门户工程\\dopUnify\\node_modules\\@babel\\runtime\\helpers\\iterableToArrayLimit.js", "mtime": 456789000000}, {"path": "E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1\\数字运营平台-统一门户工程\\dopUnify\\babel.config.js", "mtime": 1716875191548}, {"path": "E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1\\数字运营平台-统一门户工程\\dopUnify\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1\\数字运营平台-统一门户工程\\dopUnify\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:cmVxdWlyZSgiY29yZS1qcy9tb2R1bGVzL2VzLnN5bWJvbC5qcyIpOwpyZXF1aXJlKCJjb3JlLWpzL21vZHVsZXMvZXMuc3ltYm9sLmRlc2NyaXB0aW9uLmpzIik7CnJlcXVpcmUoImNvcmUtanMvbW9kdWxlcy9lcy5vYmplY3QudG8tc3RyaW5nLmpzIik7CnJlcXVpcmUoImNvcmUtanMvbW9kdWxlcy9lcy5zeW1ib2wuaXRlcmF0b3IuanMiKTsKcmVxdWlyZSgiY29yZS1qcy9tb2R1bGVzL2VzLnN0cmluZy5pdGVyYXRvci5qcyIpOwpyZXF1aXJlKCJjb3JlLWpzL21vZHVsZXMvd2ViLmRvbS1jb2xsZWN0aW9ucy5pdGVyYXRvci5qcyIpOwpmdW5jdGlvbiBfaXRlcmFibGVUb0FycmF5TGltaXQoYXJyLCBpKSB7CiAgdmFyIF9pID0gYXJyID09IG51bGwgPyBudWxsIDogdHlwZW9mIFN5bWJvbCAhPT0gInVuZGVmaW5lZCIgJiYgYXJyW1N5bWJvbC5pdGVyYXRvcl0gfHwgYXJyWyJAQGl0ZXJhdG9yIl07CiAgaWYgKF9pID09IG51bGwpIHJldHVybjsKICB2YXIgX2FyciA9IFtdOwogIHZhciBfbiA9IHRydWU7CiAgdmFyIF9kID0gZmFsc2U7CiAgdmFyIF9zLCBfZTsKICB0cnkgewogICAgZm9yIChfaSA9IF9pLmNhbGwoYXJyKTsgIShfbiA9IChfcyA9IF9pLm5leHQoKSkuZG9uZSk7IF9uID0gdHJ1ZSkgewogICAgICBfYXJyLnB1c2goX3MudmFsdWUpOwogICAgICBpZiAoaSAmJiBfYXJyLmxlbmd0aCA9PT0gaSkgYnJlYWs7CiAgICB9CiAgfSBjYXRjaCAoZXJyKSB7CiAgICBfZCA9IHRydWU7CiAgICBfZSA9IGVycjsKICB9IGZpbmFsbHkgewogICAgdHJ5IHsKICAgICAgaWYgKCFfbiAmJiBfaVsicmV0dXJuIl0gIT0gbnVsbCkgX2lbInJldHVybiJdKCk7CiAgICB9IGZpbmFsbHkgewogICAgICBpZiAoX2QpIHRocm93IF9lOwogICAgfQogIH0KICByZXR1cm4gX2FycjsKfQptb2R1bGUuZXhwb3J0cyA9IF9pdGVyYWJsZVRvQXJyYXlMaW1pdCwgbW9kdWxlLmV4cG9ydHMuX19lc01vZHVsZSA9IHRydWUsIG1vZHVsZS5leHBvcnRzWyJkZWZhdWx0Il0gPSBtb2R1bGUuZXhwb3J0czs="}, {"version": 3, "names": ["_iterableToArrayLimit", "arr", "i", "_i", "Symbol", "iterator", "_arr", "_n", "_d", "_s", "_e", "call", "next", "done", "push", "value", "length", "err", "module", "exports", "__esModule"], "sources": ["E:/1_Project/XYD_Project/dop-4.0/dop-4.1/数字运营平台-统一门户工程/dopUnify/node_modules/@babel/runtime/helpers/iterableToArrayLimit.js"], "sourcesContent": ["function _iterableToArrayLimit(arr, i) {\n  var _i = arr == null ? null : typeof Symbol !== \"undefined\" && arr[Symbol.iterator] || arr[\"@@iterator\"];\n  if (_i == null) return;\n  var _arr = [];\n  var _n = true;\n  var _d = false;\n  var _s, _e;\n  try {\n    for (_i = _i.call(arr); !(_n = (_s = _i.next()).done); _n = true) {\n      _arr.push(_s.value);\n      if (i && _arr.length === i) break;\n    }\n  } catch (err) {\n    _d = true;\n    _e = err;\n  } finally {\n    try {\n      if (!_n && _i[\"return\"] != null) _i[\"return\"]();\n    } finally {\n      if (_d) throw _e;\n    }\n  }\n  return _arr;\n}\nmodule.exports = _iterableToArrayLimit, module.exports.__esModule = true, module.exports[\"default\"] = module.exports;"], "mappings": ";;;;;;AAAA,SAASA,qBAAqB,CAACC,GAAG,EAAEC,CAAC,EAAE;EACrC,IAAIC,EAAE,GAAGF,GAAG,IAAI,IAAI,GAAG,IAAI,GAAG,OAAOG,MAAM,KAAK,WAAW,IAAIH,GAAG,CAACG,MAAM,CAACC,QAAQ,CAAC,IAAIJ,GAAG,CAAC,YAAY,CAAC;EACxG,IAAIE,EAAE,IAAI,IAAI,EAAE;EAChB,IAAIG,IAAI,GAAG,EAAE;EACb,IAAIC,EAAE,GAAG,IAAI;EACb,IAAIC,EAAE,GAAG,KAAK;EACd,IAAIC,EAAE,EAAEC,EAAE;EACV,IAAI;IACF,KAAKP,EAAE,GAAGA,EAAE,CAACQ,IAAI,CAACV,GAAG,CAAC,EAAE,EAAEM,EAAE,GAAG,CAACE,EAAE,GAAGN,EAAE,CAACS,IAAI,EAAE,EAAEC,IAAI,CAAC,EAAEN,EAAE,GAAG,IAAI,EAAE;MAChED,IAAI,CAACQ,IAAI,CAACL,EAAE,CAACM,KAAK,CAAC;MACnB,IAAIb,CAAC,IAAII,IAAI,CAACU,MAAM,KAAKd,CAAC,EAAE;IAC9B;EACF,CAAC,CAAC,OAAOe,GAAG,EAAE;IACZT,EAAE,GAAG,IAAI;IACTE,EAAE,GAAGO,GAAG;EACV,CAAC,SAAS;IACR,IAAI;MACF,IAAI,CAACV,EAAE,IAAIJ,EAAE,CAAC,QAAQ,CAAC,IAAI,IAAI,EAAEA,EAAE,CAAC,QAAQ,CAAC,EAAE;IACjD,CAAC,SAAS;MACR,IAAIK,EAAE,EAAE,MAAME,EAAE;IAClB;EACF;EACA,OAAOJ,IAAI;AACb;AACAY,MAAM,CAACC,OAAO,GAAGnB,qBAAqB,EAAEkB,MAAM,CAACC,OAAO,CAACC,UAAU,GAAG,IAAI,EAAEF,MAAM,CAACC,OAAO,CAAC,SAAS,CAAC,GAAGD,MAAM,CAACC,OAAO"}]}