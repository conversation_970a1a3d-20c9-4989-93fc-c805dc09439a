{"remainingRequest": "E:\\华南区项目\\中国银行\\开发区\\科技公司运营平台\\前端\\dopUnify\\node_modules\\babel-loader\\lib\\index.js!E:\\华南区项目\\中国银行\\开发区\\科技公司运营平台\\前端\\dopUnify\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!E:\\华南区项目\\中国银行\\开发区\\科技公司运营平台\\前端\\dopUnify\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\华南区项目\\中国银行\\开发区\\科技公司运营平台\\前端\\dopUnify\\src\\views\\home\\page\\components\\defaultTable.vue?vue&type=script&lang=js&", "dependencies": [{"path": "E:\\华南区项目\\中国银行\\开发区\\科技公司运营平台\\前端\\dopUnify\\src\\views\\home\\page\\components\\defaultTable.vue", "mtime": 1721631954662}, {"path": "E:\\华南区项目\\中国银行\\开发区\\科技公司运营平台\\前端\\dopUnify\\babel.config.js", "mtime": 1721631954662}, {"path": "E:\\华南区项目\\中国银行\\开发区\\科技公司运营平台\\前端\\dopUnify\\.babelrc", "mtime": 1721631954662}, {"path": "E:\\华南区项目\\中国银行\\开发区\\科技公司运营平台\\前端\\dopUnify\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "E:\\华南区项目\\中国银行\\开发区\\科技公司运营平台\\前端\\dopUnify\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "E:\\华南区项目\\中国银行\\开发区\\科技公司运营平台\\前端\\dopUnify\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "E:\\华南区项目\\中国银行\\开发区\\科技公司运营平台\\前端\\dopUnify\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1655715099000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KCmltcG9ydCB7IEhvbWUgfSBmcm9tICdAL2FwaSc7CnZhciBhY0hvbWVQYWdlSW5pdCA9IEhvbWUuYWNIb21lUGFnZUluaXQ7CmV4cG9ydCBkZWZhdWx0IHsKICBuYW1lOiAnRGVmYXVsdFRhYmxlJywKICBkYXRhOiBmdW5jdGlvbiBkYXRhKCkgewogICAgcmV0dXJuIHsKICAgICAgdGFibGVEYXRhOiBbXQogICAgfTsKICB9LAogIGNyZWF0ZWQ6IGZ1bmN0aW9uIGNyZWF0ZWQoKSB7CiAgICB2YXIgX3RoaXMgPSB0aGlzOwogICAgdmFyIG1zZyA9IHsKICAgICAgcGFyYW1ldGVyTGlzdDogW10sCiAgICAgIG9wZXJfdHlwZTogJ3NlbGVjdFdhcm5OdW0nCiAgICB9OwogICAgYWNIb21lUGFnZUluaXQobXNnKS50aGVuKGZ1bmN0aW9uIChyZXMpIHsKICAgICAgdmFyIGxpc3QgPSByZXMucmV0TWFwLmxpc3Q7CiAgICAgIF90aGlzLnRhYmxlRGF0YSA9IGxpc3Q7CiAgICB9KTsKICB9Cn07"}, {"version": 3, "mappings": ";;;;;;;;;;;;;AAaA;AACA;AACA;EACAA;EACAC;IACA;MACAC;IACA;EACA;EACAC;IAAA;IACA;MAAAC;MAAAC;IAAA;IACAC;MACA,IACAC,OACAC,IADAC;MAEA;IACA;EACA;AACA", "names": ["name", "data", "tableData", "created", "parameterList", "oper_type", "acHomePageInit", "list", "res", "retMap"], "sourceRoot": "src/views/home/<USER>/components", "sources": ["defaultTable.vue"], "sourcesContent": ["<template>\n  <div class=\"default-table\">\n    <div class=\"table-title\">预警数量展示</div>\n    <div class=\"table-body\">\n      <el-table :data=\"tableData\">\n        <el-table-column prop=\"warn_type\" label=\"预警类型\" />\n        <el-table-column prop=\"warn_num\" label=\"预警数量\" />\n      </el-table>\n    </div>\n  </div>\n</template>\n\n<script>\nimport { Home } from '@/api'\nconst { acHomePageInit } = Home\nexport default {\n  name: 'DefaultTable',\n  data() {\n    return {\n      tableData: []\n    }\n  },\n  created() {\n    const msg = { parameterList: [], oper_type: 'selectWarnNum' }\n    acHomePageInit(msg).then((res) => {\n      const {\n        retMap: { list }\n      } = res\n      this.tableData = list\n    })\n  }\n}\n</script>\n<style scoped lang=\"scss\">\n.default-table {\n  width: 100%;\n  height: 100%;\n  padding: 1rem;\n  border-radius: 1rem;\n  background-color: #fff;\n  .table-title {\n    height: 50px;\n    line-height: 50px;\n    font-size: 1.6rem;\n    font-weight: bold;\n    padding-left: 1rem;\n  }\n  .table-body {\n    height: calc(100% - 50px - 2rem);\n    overflow: auto;\n  }\n}\n</style>\n"]}]}