package com.sunyard.etl.custom.service;


import java.io.File;
import java.util.regex.Pattern;
import com.xxl.job.core.log.XxlJobLogger;

 
public class FileFormatService {

	
	// 文件名格式化
	public void fileNameFormat(String path) {
		XxlJobLogger.log("开始文件名格式化...");
		File file = new File(path);
		String[] list = file.list();
		if (list != null && list.length > 0) {
			for (String oldName : list) {
				File oldFile = new File(path, oldName);
				if (!oldFile.isDirectory()) {//如果原始的类型为文件
					String fileName = oldName.toString().substring(oldName.toString().lastIndexOf(".") + 1);
					Pattern pattern = Pattern.compile("^[-\\+]?[\\d]{9}$");
					if (pattern.matcher(fileName).matches()) {
						//0100006D.d01.gz.202104200
                        String[] names = oldName.split("\\.");
						//0100006D.d01.20210420.gz
                        String newoldName = names[0]+"."+names[1]+"."+names[3].substring(0,8)+"."+names[2];
						File newFile = new File(path, newoldName);

						// String datas = fileName.substring(0, 8);
						// XxlJobLogger.log(datas);
						// String newoldName = oldName.substring(0, oldName.lastIndexOf("gz"))	+ datas + ".gz";
						// File newFile = new File(path, newoldName);
						
						if(newFile.exists()){
							XxlJobLogger.log("相同名字的文件已经存在，先删除 ：");
							newFile.delete();
						}
						
						boolean flag = oldFile.renameTo(newFile);
						XxlJobLogger.log("重命名 ：" + flag);
					}
				}
			}
		}
	}
	
	public static void main(String[] args) {
		FileFormatService a = new FileFormatService();
		a.fileNameFormat("C:/20200115");
	}
}

