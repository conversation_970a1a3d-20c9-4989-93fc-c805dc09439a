{"remainingRequest": "E:\\华南区项目\\中国银行\\开发区\\科技公司运营平台\\前端\\dopUnify\\node_modules\\babel-loader\\lib\\index.js!E:\\华南区项目\\中国银行\\开发区\\科技公司运营平台\\前端\\dopUnify\\node_modules\\eslint-loader\\index.js??ref--13-0!E:\\华南区项目\\中国银行\\开发区\\科技公司运营平台\\前端\\dopUnify\\src\\views\\login\\info.js", "dependencies": [{"path": "E:\\华南区项目\\中国银行\\开发区\\科技公司运营平台\\前端\\dopUnify\\src\\views\\login\\info.js", "mtime": 1721631954662}, {"path": "E:\\华南区项目\\中国银行\\开发区\\科技公司运营平台\\前端\\dopUnify\\babel.config.js", "mtime": 1721631954662}, {"path": "E:\\华南区项目\\中国银行\\开发区\\科技公司运营平台\\前端\\dopUnify\\.babelrc", "mtime": 1721631954662}, {"path": "E:\\华南区项目\\中国银行\\开发区\\科技公司运营平台\\前端\\dopUnify\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "E:\\华南区项目\\中国银行\\开发区\\科技公司运营平台\\前端\\dopUnify\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "E:\\华南区项目\\中国银行\\开发区\\科技公司运营平台\\前端\\dopUnify\\node_modules\\eslint-loader\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["config", "that", "user_no", "component", "label", "colSpan", "inlineForm", "name", "componentProps", "clearable", "placeholder", "rules", "required", "message", "slotObj", "text", "click", "currBtnIndex", "$refs", "formRef", "validateForm", "phone", "pattern", "code", "modifyConfig", "readonly", "newPassword", "type", "RegExp", "$store", "getters", "initParams", "passRegular", "passTips", "confirmPassword"], "sources": ["E:/华南区项目/中国银行/开发区/科技公司运营平台/前端/dopUnify/src/views/login/info.js"], "sourcesContent": ["// 找回密码 验证弹窗表单\r\nexport const config = (that) => ({\r\n  user_no: {\r\n    component: 'input',\r\n    label: '账号',\r\n    colSpan: 19,\r\n    inlineForm: true,\r\n    name: 'user_no',\r\n    componentProps: {\r\n      // input组件配置\r\n      clearable: true,\r\n      placeholder: '请输入忘记密码的账号'\r\n    },\r\n    config: {\r\n      // form-item 配置\r\n      rules: [{ required: true, message: '账号名为必输' }]\r\n    },\r\n    slotObj: {\r\n      text: '获取验证码',\r\n      click() {\r\n        that.currBtnIndex = '0'\r\n        that.$refs.formRef.validateForm()\r\n      }\r\n    }\r\n  },\r\n  phone: {\r\n    component: 'input',\r\n    label: '手机号码',\r\n    colSpan: 19,\r\n    inlineForm: true,\r\n    name: 'phone',\r\n    componentProps: {\r\n      // input组件配置\r\n      clearable: true,\r\n      placeholder: '请输入手机号码'\r\n    },\r\n    config: {\r\n      // form-item 配置\r\n      rules: [\r\n        { required: true, message: '手机号码为必输' },\r\n        {\r\n          pattern: /^1[3456789]\\d{9}$/,\r\n          message: '手机号码不合法，请重新输入'\r\n        }\r\n      ]\r\n    }\r\n  },\r\n  code: {\r\n    component: 'input',\r\n    label: '验证码',\r\n    colSpan: 19,\r\n    inlineForm: true,\r\n    name: 'code',\r\n    componentProps: {\r\n      // input组件配置\r\n      clearable: true,\r\n      placeholder: '请输入验证码'\r\n    },\r\n    config: {\r\n      // form-item 配置\r\n      rules: [{ pattern: /^[\\S]{1,6}$/, message: '最多可输6个非空格字符' }]\r\n    }\r\n  }\r\n})\r\n\r\n// 修改密码弹窗表单\r\nexport const modifyConfig = (that) => ({\r\n  user_no: {\r\n    component: 'input',\r\n    label: '账号',\r\n    colSpan: 19,\r\n    inlineForm: true,\r\n    name: 'user_no',\r\n    componentProps: {\r\n      // input组件配置\r\n      readonly: true\r\n    },\r\n    config: {\r\n      // form-item 配置\r\n      rules: [{ required: true, message: '账号名为必输' }]\r\n    }\r\n  },\r\n  newPassword: {\r\n    component: 'input',\r\n    label: '新密码',\r\n    colSpan: 19,\r\n    inlineForm: true,\r\n    name: 'newPassword',\r\n    componentProps: {\r\n      // input组件配置\r\n      clearable: true,\r\n      placeholder: '请输入密码',\r\n      type: 'password'\r\n    },\r\n    config: {\r\n      // form-item 配置\r\n      rules: [\r\n        { required: true, message: '新密码为必输' },\r\n        {\r\n          pattern: new RegExp(that.$store.getters.initParams.passRegular),\r\n          message: `${that.$store.getters.initParams.passTips}`\r\n        }\r\n      ]\r\n    }\r\n  },\r\n  confirmPassword: {\r\n    component: 'input',\r\n    label: '确认密码',\r\n    colSpan: 19,\r\n    inlineForm: true,\r\n    name: 'confirmPassword',\r\n    componentProps: {\r\n      // input组件配置\r\n      clearable: true,\r\n      placeholder: '请再次输入密码',\r\n      type: 'password'\r\n    },\r\n    config: {\r\n      // form-item 配置\r\n      rules: [\r\n        { required: true, message: '确认密码为必输' },\r\n        {\r\n          pattern: new RegExp(that.$store.getters.initParams.passRegular),\r\n          message: `${that.$store.getters.initParams.passTips}`\r\n        }\r\n      ]\r\n    }\r\n  }\r\n})\r\n"], "mappings": ";;;;AAAA;AACA,OAAO,IAAMA,MAAM,GAAG,SAATA,MAAM,CAAIC,IAAI;EAAA,OAAM;IAC/BC,OAAO,EAAE;MACPC,SAAS,EAAE,OAAO;MAClBC,KAAK,EAAE,IAAI;MACXC,OAAO,EAAE,EAAE;MACXC,UAAU,EAAE,IAAI;MAChBC,IAAI,EAAE,SAAS;MACfC,cAAc,EAAE;QACd;QACAC,SAAS,EAAE,IAAI;QACfC,WAAW,EAAE;MACf,CAAC;MACDV,MAAM,EAAE;QACN;QACAW,KAAK,EAAE,CAAC;UAAEC,QAAQ,EAAE,IAAI;UAAEC,OAAO,EAAE;QAAS,CAAC;MAC/C,CAAC;MACDC,OAAO,EAAE;QACPC,IAAI,EAAE,OAAO;QACbC,KAAK,mBAAG;UACNf,IAAI,CAACgB,YAAY,GAAG,GAAG;UACvBhB,IAAI,CAACiB,KAAK,CAACC,OAAO,CAACC,YAAY,EAAE;QACnC;MACF;IACF,CAAC;IACDC,KAAK,EAAE;MACLlB,SAAS,EAAE,OAAO;MAClBC,KAAK,EAAE,MAAM;MACbC,OAAO,EAAE,EAAE;MACXC,UAAU,EAAE,IAAI;MAChBC,IAAI,EAAE,OAAO;MACbC,cAAc,EAAE;QACd;QACAC,SAAS,EAAE,IAAI;QACfC,WAAW,EAAE;MACf,CAAC;MACDV,MAAM,EAAE;QACN;QACAW,KAAK,EAAE,CACL;UAAEC,QAAQ,EAAE,IAAI;UAAEC,OAAO,EAAE;QAAU,CAAC,EACtC;UACES,OAAO,EAAE,mBAAmB;UAC5BT,OAAO,EAAE;QACX,CAAC;MAEL;IACF,CAAC;IACDU,IAAI,EAAE;MACJpB,SAAS,EAAE,OAAO;MAClBC,KAAK,EAAE,KAAK;MACZC,OAAO,EAAE,EAAE;MACXC,UAAU,EAAE,IAAI;MAChBC,IAAI,EAAE,MAAM;MACZC,cAAc,EAAE;QACd;QACAC,SAAS,EAAE,IAAI;QACfC,WAAW,EAAE;MACf,CAAC;MACDV,MAAM,EAAE;QACN;QACAW,KAAK,EAAE,CAAC;UAAEW,OAAO,EAAE,aAAa;UAAET,OAAO,EAAE;QAAc,CAAC;MAC5D;IACF;EACF,CAAC;AAAA,CAAC;;AAEF;AACA,OAAO,IAAMW,YAAY,GAAG,SAAfA,YAAY,CAAIvB,IAAI;EAAA,OAAM;IACrCC,OAAO,EAAE;MACPC,SAAS,EAAE,OAAO;MAClBC,KAAK,EAAE,IAAI;MACXC,OAAO,EAAE,EAAE;MACXC,UAAU,EAAE,IAAI;MAChBC,IAAI,EAAE,SAAS;MACfC,cAAc,EAAE;QACd;QACAiB,QAAQ,EAAE;MACZ,CAAC;MACDzB,MAAM,EAAE;QACN;QACAW,KAAK,EAAE,CAAC;UAAEC,QAAQ,EAAE,IAAI;UAAEC,OAAO,EAAE;QAAS,CAAC;MAC/C;IACF,CAAC;IACDa,WAAW,EAAE;MACXvB,SAAS,EAAE,OAAO;MAClBC,KAAK,EAAE,KAAK;MACZC,OAAO,EAAE,EAAE;MACXC,UAAU,EAAE,IAAI;MAChBC,IAAI,EAAE,aAAa;MACnBC,cAAc,EAAE;QACd;QACAC,SAAS,EAAE,IAAI;QACfC,WAAW,EAAE,OAAO;QACpBiB,IAAI,EAAE;MACR,CAAC;MACD3B,MAAM,EAAE;QACN;QACAW,KAAK,EAAE,CACL;UAAEC,QAAQ,EAAE,IAAI;UAAEC,OAAO,EAAE;QAAS,CAAC,EACrC;UACES,OAAO,EAAE,IAAIM,MAAM,CAAC3B,IAAI,CAAC4B,MAAM,CAACC,OAAO,CAACC,UAAU,CAACC,WAAW,CAAC;UAC/DnB,OAAO,YAAKZ,IAAI,CAAC4B,MAAM,CAACC,OAAO,CAACC,UAAU,CAACE,QAAQ;QACrD,CAAC;MAEL;IACF,CAAC;IACDC,eAAe,EAAE;MACf/B,SAAS,EAAE,OAAO;MAClBC,KAAK,EAAE,MAAM;MACbC,OAAO,EAAE,EAAE;MACXC,UAAU,EAAE,IAAI;MAChBC,IAAI,EAAE,iBAAiB;MACvBC,cAAc,EAAE;QACd;QACAC,SAAS,EAAE,IAAI;QACfC,WAAW,EAAE,SAAS;QACtBiB,IAAI,EAAE;MACR,CAAC;MACD3B,MAAM,EAAE;QACN;QACAW,KAAK,EAAE,CACL;UAAEC,QAAQ,EAAE,IAAI;UAAEC,OAAO,EAAE;QAAU,CAAC,EACtC;UACES,OAAO,EAAE,IAAIM,MAAM,CAAC3B,IAAI,CAAC4B,MAAM,CAACC,OAAO,CAACC,UAAU,CAACC,WAAW,CAAC;UAC/DnB,OAAO,YAAKZ,IAAI,CAAC4B,MAAM,CAACC,OAAO,CAACC,UAAU,CAACE,QAAQ;QACrD,CAAC;MAEL;IACF;EACF,CAAC;AAAA,CAAC"}]}