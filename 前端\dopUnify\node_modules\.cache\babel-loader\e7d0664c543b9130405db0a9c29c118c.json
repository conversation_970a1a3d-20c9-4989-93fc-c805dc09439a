{"remainingRequest": "E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-q<PERSON><PERSON>n\\数字运营平台-统一门户工程\\dopUnify\\node_modules\\babel-loader\\lib\\index.js!E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan\\数字运营平台-统一门户工程\\dopUnify\\node_modules\\eslint-loader\\index.js??ref--13-0!E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan\\数字运营平台-统一门户工程\\dopUnify\\src\\store\\modules\\permission.js", "dependencies": [{"path": "E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-<PERSON><PERSON><PERSON>n\\数字运营平台-统一门户工程\\dopUnify\\src\\store\\modules\\permission.js", "mtime": 1712806340320}, {"path": "E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-<PERSON><PERSON><PERSON>n\\数字运营平台-统一门户工程\\dopUnify\\babel.config.js", "mtime": 1686019818359}, {"path": "E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-<PERSON><PERSON><PERSON>n\\数字运营平台-统一门户工程\\dopUnify\\.babelrc", "mtime": 1705285078668}, {"path": "E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-<PERSON><PERSON><PERSON>n\\数字运营平台-统一门户工程\\dopUnify\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-<PERSON><PERSON><PERSON>n\\数字运营平台-统一门户工程\\dopUnify\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-<PERSON><PERSON><PERSON>n\\数字运营平台-统一门户工程\\dopUnify\\node_modules\\eslint-loader\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["constantRoutes", "Common", "commonBlank", "routesAll", "Layout", "commonMenu", "count", "filterAsyncRoutes", "routes", "type", "res", "route", "menu_url", "id", "menu_id", "label", "meta", "title", "newItem", "home_show", "path", "menu_attr", "indexOf", "children", "menuEntry", "randNum", "pathLen", "split", "length", "component", "name", "icon", "module_no", "menu_level", "createCustomComponent", "resolve", "require", "push", "data", "created", "Promise", "module", "render", "h", "state", "addRoutes", "addRoutesArray", "mutations", "SET_ROUTES", "concat", "SET_ROUTES_ARRAY", "SET_MENU_OBJ", "menuObj", "actions", "generateRoutes", "param", "commit", "dispatch", "reject", "accessedRoutes", "msg1", "oper_type", "isPC", "then", "response", "retMap", "menuList", "usualMenuList", "for<PERSON>ach", "item", "menu_name", "root", "formatMenuList", "returnListData", "index", "catch", "error", "refreshData", "namespaced"], "sources": ["E:/1_Project/XYD_Project/dop-4.0/dop-4.1-qianduan/数字运营平台-统一门户工程/dopUnify/src/store/modules/permission.js"], "sourcesContent": ["import { constantRoutes } from '@/router'\nimport { Common } from '@/api'\nimport { commonBlank } from '@/utils/common'\nimport { routesAll } from '@/router/routers' // 获取路由组件信息\nimport Layout from '@/layout'\n\nconst { commonMenu } = Common\n\n/**\n * 通过递归过滤异步路由表\n * @param routes asyncRoutes\n * @param roles\n * @param type 菜单类型 'all'-所有菜单，'parent'-父级菜单\n */\nlet count = 0\nexport function filterAsyncRoutes(routes, type) {\n  if (!type) type = 'all'\n  const res = []\n  for (let route of routes) {\n    route['path'] = route.menu_url // 添加path 属性\n    // route['deplName'] = route.deplName // 添加部署工程属性\n    if (type === 'parent') {\n      route = { ...route, id: route.menu_id, label: route.meta.title }\n    }\n    const newItem = { ...route }\n    newItem['props'] = { ...route } // 配置路由参数\n\n    if (newItem.home_show === '2' || commonBlank(route.path)) {\n      // route.path 为空，不往下执行\n      // 仅在首页显示，不初始化在路由中\n      continue\n    }\n    // if (newItem.index) {\n    //   // 一级菜单 component配置\n    //   if (newItem.index === '0') {\n    //     route.component = '/Layout'\n    //     newItem.component = '/Layout'\n    //     if (newItem.alwaysshow) {\n    //       delete newItem.alwaysshow\n    //       newItem.alwaysShow = true\n    //     }\n    //   }    // } else {\n    count++\n    if (route.path === 'testPath') {\n      route.path = 'menu'\n    }\n    if (route.menu_attr === '2' || route.menu_attr === '3') {\n      if (\n        route.path.indexOf('common-index.html') !== -1 ||\n        route.path.indexOf('arms.html') !== -1\n      ) {\n        // 若子工程配置下级菜单为common-index.html\n        route.children = []\n        newItem.children = []\n      }\n      // 外系统菜单\n      // const randNum = Math.random(0, 1)\n      const menuEntry = route.menu_attr === '2' ? 'extend' : 'qiankun'\n      let randNum = menuEntry + count\n      if (route.path.indexOf('redirect:') !== -1) {\n        const pathLen = route.path.split(':')\n        if (pathLen.length > 1) {\n          randNum = pathLen[1].indexOf('report/preview/index' !== -1)\n            ? pathLen[1] + count\n            : pathLen[1]\n        } else {\n          randNum = pathLen[0].indexOf('report/preview/index' !== -1)\n            ? pathLen[0] + count\n            : pathLen[0]\n        }\n      }\n\n      if (routesAll[menuEntry]) {\n        route.component = routesAll[menuEntry].component // 通过path获取路由信息，赋值component\n        route.name = routesAll[menuEntry].name + count // name 赋值\n        newItem.component = routesAll[menuEntry].component // 通过path获取路由信息，赋值component\n        newItem.name = routesAll[menuEntry].name + count // name 赋值\n        newItem.path = randNum // path 赋值\n        route.path = randNum // path 赋值\n      }\n    } else if (routesAll[route.path]) {\n      // 本系统菜单\n      // let paramNum = ''\n      // if (route.path === '/parents' || route.path === 'parentMenu') { // 公共的一级节点、父级节点\n      //   paramNum = Math.random(0, 1)\n      // }\n      route.component = routesAll[route.path].component // 通过path获取路由信息，赋值component\n      route.name = routesAll[route.path].name // name 赋值\n\n      newItem.component = routesAll[route.path].component // 通过path获取路由信息，赋值component\n      newItem.name = routesAll[route.path].name // name 赋值\n    } else {\n      continue\n    }\n    /* 动态父级配置 begin*/\n    count++\n    if (newItem.path === '/parents') {\n      // 通用父级\n      if (routesAll[route.path]) {\n        newItem.name = routesAll[route.path].name + count // name 赋值\n        route.name = routesAll[route.path].name + count // name 赋值\n        newItem.path = '/parents' + count // path 赋值\n        route.path = '/parents' + count // path 赋值\n      }\n    } else if (newItem.path === 'parentMenu') {\n      // 通用二级及以下父级\n      newItem.name = routesAll[route.path].name + count // name 赋值\n      route.name = routesAll[route.path].name + count // name 赋值\n      newItem.path = '/parentMenu' + count // path 赋值\n      route.path = '/parentMenu' + count // path 赋值\n    }\n    /* 动态父级配置 end*/\n    // }\n    if (route.meta.icon === '') {\n      route.meta.icon = ' '\n    }\n    if (!route.meta.module_no) {\n      route.meta.module_no = route.menu_id\n    }\n    if (route.path) {\n      if (\n        route.menu_level === '1' &&\n        newItem.path.indexOf('parentMenu') === -1\n      ) {\n        newItem.component = Layout\n      } else {\n        if (newItem?.name) {\n          if (newItem.name.indexOf('Qiankun') !== -1) {\n            // qiankun 配置\n            newItem.component = () =>\n              createCustomComponent(\n                newItem.name,\n                // Promise.resolve(require(`@/views/${route.component}`).default)\n                (resolve) => require([`@/views/${route.component}`], resolve)\n              )\n          } else {\n            // newItem.component = () =>\n            //   Promise.resolve(require(`@/views/${route.component}`).default)\n            newItem.component = (resolve) => require([`@/views/${route.component}`], resolve)\n          }\n        } else {\n          // newItem.component = () =>\n          //   Promise.resolve(require(`@/views/${route.component}`).default)\n          newItem.component = (resolve) => require([`@/views/${route.component}`], resolve)\n        }\n      }\n    } else {\n      continue\n    }\n\n    if (type === 'all') {\n      // 获取所有的父节点\n      if (newItem.children && newItem.children.length) {\n        newItem['alwaysShow'] = true // 配置当前父节点\n        newItem.children = filterAsyncRoutes(route.children)\n      }\n      res.push(newItem)\n    } else {\n      if (newItem.children && newItem.children.length) {\n        newItem['alwaysShow'] = true // 配置当前父节点\n        newItem.children = filterAsyncRoutes(route.children, 'parent')\n        res.push(newItem)\n      }\n    }\n  }\n\n  return res\n}\n\n/**\n * 将指定组件设置自定义名称\n * 动态设置vue 实例中的name值\n * @param {String} name 组件自定义名称\n * @param {Component | Promise<Component>} component\n * @return {Component}\n */\nasync function createCustomComponent(name, component) {\n  return {\n    name,\n    data() {\n      return { component: null }\n    },\n    async created() {\n      if (component instanceof Promise) {\n        try {\n          const module = await component\n          this.component = module\n        } catch (error) {\n          // console.error(`can not resolve component ${name}, error:`, error)\n        }\n        return\n      }\n      this.component = component\n    },\n    render(h) {\n      return this.component ? h(this.component) : null\n    }\n  }\n}\n\nconst state = {\n  routes: [],\n  addRoutes: [],\n  addRoutesArray: []\n}\n\nconst mutations = {\n  SET_ROUTES: (state, routes) => {\n    state.addRoutes = routes\n    state.routes = constantRoutes.concat(routes)\n  },\n  SET_ROUTES_ARRAY: (state, routes) => {\n    state.addRoutesArray = routes\n  },\n  // 设置菜单格式化-对象\n  SET_MENU_OBJ: (state, data) => {\n    state.menuObj = data\n  }\n}\n\nconst actions = {\n  generateRoutes({ commit, dispatch, state }, param) {\n    return new Promise((resolve, reject) => {\n      // 获取左侧菜单\n      let accessedRoutes = []\n      // 判断当前路由是否已初始化，通常用于页面刷新时，不重新获取菜单\n      if (commonBlank(state.addRoutes)) {\n        // const msg = {\n        //   oper_type: '0',\n        //   isPC: true,\n        //   menu_type: '2'\n        // }\n        // commonMenu(msg)\n        //   .then((response) => {\n        //   // 捕获错误 begin\n        //     if (response === undefined) {\n        //       return\n        //     }\n        //     // 捕获错误 end\n        //     // 菜单请求\n        //     let { menuList } = response.retMap\n        //     if (commonBlank(menuList)) {\n        //       menuList = []\n        //     }\n        //     // let returnListData = [] // 所有菜单\n        //     // for (const item of menuList) {\n        //     //   returnListData = [...returnListData, { index: '0', ...item }]\n        //     // }\n        //     // console.log('returnListData', returnListData)\n        //     // return\n        //     accessedRoutes = filterAsyncRoutes(menuList) // 配置动态路由\n        //     commit('SET_ROUTES', accessedRoutes)\n        //     resolve(accessedRoutes)\n        //   })\n        //   .catch((error) => {\n        //     reject(error)\n        //   })\n        // 获取所有菜单（单个菜单） 优化两个接口合为一个\n        const msg1 = {\n          oper_type: '0',\n          isPC: true\n        }\n        commonMenu(msg1).then((response) => {\n          const { menuList, usualMenuList } = response.retMap\n          const menuObj = {}\n          menuList.forEach((item) => {\n            menuObj[item.menu_id] = item.menu_name\n          })\n          commit('common/SET_MENU_ARR', menuList, { root: true })\n          commit('common/SET_MENU_OBJ', menuObj, { root: true })\n          commit('SET_ROUTES_ARRAY', usualMenuList)\n          let { formatMenuList } = response.retMap\n          if (commonBlank(formatMenuList)) {\n            formatMenuList = []\n          }\n\n          let returnListData = [] // 所有菜单\n          for (const item of formatMenuList) {\n            returnListData = [...returnListData, { index: '0', ...item }]\n          }\n          accessedRoutes = filterAsyncRoutes(returnListData) // 配置动态路由\n          commit('SET_ROUTES', accessedRoutes)\n          resolve(accessedRoutes)\n        }).catch((error) => {\n          reject(error)\n        })\n      } else {\n        let refreshData = []\n        refreshData = filterAsyncRoutes(state.addRoutes) // 配置动态路由\n        commit('SET_ROUTES', refreshData)\n        resolve(refreshData)\n      }\n    })\n  }\n}\n\nexport default {\n  namespaced: true,\n  state,\n  mutations,\n  actions\n}\n"], "mappings": ";;;;;;;;;AAAA,SAASA,cAAc,QAAQ,UAAU;AACzC,SAASC,MAAM,QAAQ,OAAO;AAC9B,SAASC,WAAW,QAAQ,gBAAgB;AAC5C,SAASC,SAAS,QAAQ,kBAAkB,EAAC;AAC7C,OAAOC,MAAM,MAAM,UAAU;AAE7B,IAAQC,UAAU,GAAKJ,MAAM,CAArBI,UAAU;;AAElB;AACA;AACA;AACA;AACA;AACA;AACA,IAAIC,KAAK,GAAG,CAAC;AACb,OAAO,SAASC,iBAAiB,CAACC,MAAM,EAAEC,IAAI,EAAE;EAC9C,IAAI,CAACA,IAAI,EAAEA,IAAI,GAAG,KAAK;EACvB,IAAMC,GAAG,GAAG,EAAE;EAAA,2CACIF,MAAM;IAAA;EAAA;IAAA;MAAA,IAAfG,KAAK;MACZA,KAAK,CAAC,MAAM,CAAC,GAAGA,KAAK,CAACC,QAAQ,EAAC;MAC/B;MACA,IAAIH,IAAI,KAAK,QAAQ,EAAE;QACrBE,KAAK,mCAAQA,KAAK;UAAEE,EAAE,EAAEF,KAAK,CAACG,OAAO;UAAEC,KAAK,EAAEJ,KAAK,CAACK,IAAI,CAACC;QAAK,EAAE;MAClE;MACA,IAAMC,OAAO,qBAAQP,KAAK,CAAE;MAC5BO,OAAO,CAAC,OAAO,CAAC,qBAAQP,KAAK,CAAE,EAAC;;MAEhC,IAAIO,OAAO,CAACC,SAAS,KAAK,GAAG,IAAIjB,WAAW,CAACS,KAAK,CAACS,IAAI,CAAC,EAAE;QACxD;QACA;QACA;MACF;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACAd,KAAK,EAAE;MACP,IAAIK,KAAK,CAACS,IAAI,KAAK,UAAU,EAAE;QAC7BT,KAAK,CAACS,IAAI,GAAG,MAAM;MACrB;MACA,IAAIT,KAAK,CAACU,SAAS,KAAK,GAAG,IAAIV,KAAK,CAACU,SAAS,KAAK,GAAG,EAAE;QACtD,IACEV,KAAK,CAACS,IAAI,CAACE,OAAO,CAAC,mBAAmB,CAAC,KAAK,CAAC,CAAC,IAC9CX,KAAK,CAACS,IAAI,CAACE,OAAO,CAAC,WAAW,CAAC,KAAK,CAAC,CAAC,EACtC;UACA;UACAX,KAAK,CAACY,QAAQ,GAAG,EAAE;UACnBL,OAAO,CAACK,QAAQ,GAAG,EAAE;QACvB;QACA;QACA;QACA,IAAMC,SAAS,GAAGb,KAAK,CAACU,SAAS,KAAK,GAAG,GAAG,QAAQ,GAAG,SAAS;QAChE,IAAII,OAAO,GAAGD,SAAS,GAAGlB,KAAK;QAC/B,IAAIK,KAAK,CAACS,IAAI,CAACE,OAAO,CAAC,WAAW,CAAC,KAAK,CAAC,CAAC,EAAE;UAC1C,IAAMI,OAAO,GAAGf,KAAK,CAACS,IAAI,CAACO,KAAK,CAAC,GAAG,CAAC;UACrC,IAAID,OAAO,CAACE,MAAM,GAAG,CAAC,EAAE;YACtBH,OAAO,GAAGC,OAAO,CAAC,CAAC,CAAC,CAACJ,OAAO,CAAC,sBAAsB,KAAK,CAAC,CAAC,CAAC,GACvDI,OAAO,CAAC,CAAC,CAAC,GAAGpB,KAAK,GAClBoB,OAAO,CAAC,CAAC,CAAC;UAChB,CAAC,MAAM;YACLD,OAAO,GAAGC,OAAO,CAAC,CAAC,CAAC,CAACJ,OAAO,CAAC,sBAAsB,KAAK,CAAC,CAAC,CAAC,GACvDI,OAAO,CAAC,CAAC,CAAC,GAAGpB,KAAK,GAClBoB,OAAO,CAAC,CAAC,CAAC;UAChB;QACF;QAEA,IAAIvB,SAAS,CAACqB,SAAS,CAAC,EAAE;UACxBb,KAAK,CAACkB,SAAS,GAAG1B,SAAS,CAACqB,SAAS,CAAC,CAACK,SAAS,EAAC;UACjDlB,KAAK,CAACmB,IAAI,GAAG3B,SAAS,CAACqB,SAAS,CAAC,CAACM,IAAI,GAAGxB,KAAK,EAAC;UAC/CY,OAAO,CAACW,SAAS,GAAG1B,SAAS,CAACqB,SAAS,CAAC,CAACK,SAAS,EAAC;UACnDX,OAAO,CAACY,IAAI,GAAG3B,SAAS,CAACqB,SAAS,CAAC,CAACM,IAAI,GAAGxB,KAAK,EAAC;UACjDY,OAAO,CAACE,IAAI,GAAGK,OAAO,EAAC;UACvBd,KAAK,CAACS,IAAI,GAAGK,OAAO,EAAC;QACvB;MACF,CAAC,MAAM,IAAItB,SAAS,CAACQ,KAAK,CAACS,IAAI,CAAC,EAAE;QAChC;QACA;QACA;QACA;QACA;QACAT,KAAK,CAACkB,SAAS,GAAG1B,SAAS,CAACQ,KAAK,CAACS,IAAI,CAAC,CAACS,SAAS,EAAC;QAClDlB,KAAK,CAACmB,IAAI,GAAG3B,SAAS,CAACQ,KAAK,CAACS,IAAI,CAAC,CAACU,IAAI,EAAC;;QAExCZ,OAAO,CAACW,SAAS,GAAG1B,SAAS,CAACQ,KAAK,CAACS,IAAI,CAAC,CAACS,SAAS,EAAC;QACpDX,OAAO,CAACY,IAAI,GAAG3B,SAAS,CAACQ,KAAK,CAACS,IAAI,CAAC,CAACU,IAAI,EAAC;MAC5C,CAAC,MAAM;QACL;MACF;MACA;MACAxB,KAAK,EAAE;MACP,IAAIY,OAAO,CAACE,IAAI,KAAK,UAAU,EAAE;QAC/B;QACA,IAAIjB,SAAS,CAACQ,KAAK,CAACS,IAAI,CAAC,EAAE;UACzBF,OAAO,CAACY,IAAI,GAAG3B,SAAS,CAACQ,KAAK,CAACS,IAAI,CAAC,CAACU,IAAI,GAAGxB,KAAK,EAAC;UAClDK,KAAK,CAACmB,IAAI,GAAG3B,SAAS,CAACQ,KAAK,CAACS,IAAI,CAAC,CAACU,IAAI,GAAGxB,KAAK,EAAC;UAChDY,OAAO,CAACE,IAAI,GAAG,UAAU,GAAGd,KAAK,EAAC;UAClCK,KAAK,CAACS,IAAI,GAAG,UAAU,GAAGd,KAAK,EAAC;QAClC;MACF,CAAC,MAAM,IAAIY,OAAO,CAACE,IAAI,KAAK,YAAY,EAAE;QACxC;QACAF,OAAO,CAACY,IAAI,GAAG3B,SAAS,CAACQ,KAAK,CAACS,IAAI,CAAC,CAACU,IAAI,GAAGxB,KAAK,EAAC;QAClDK,KAAK,CAACmB,IAAI,GAAG3B,SAAS,CAACQ,KAAK,CAACS,IAAI,CAAC,CAACU,IAAI,GAAGxB,KAAK,EAAC;QAChDY,OAAO,CAACE,IAAI,GAAG,aAAa,GAAGd,KAAK,EAAC;QACrCK,KAAK,CAACS,IAAI,GAAG,aAAa,GAAGd,KAAK,EAAC;MACrC;MACA;MACA;MACA,IAAIK,KAAK,CAACK,IAAI,CAACe,IAAI,KAAK,EAAE,EAAE;QAC1BpB,KAAK,CAACK,IAAI,CAACe,IAAI,GAAG,GAAG;MACvB;MACA,IAAI,CAACpB,KAAK,CAACK,IAAI,CAACgB,SAAS,EAAE;QACzBrB,KAAK,CAACK,IAAI,CAACgB,SAAS,GAAGrB,KAAK,CAACG,OAAO;MACtC;MACA,IAAIH,KAAK,CAACS,IAAI,EAAE;QACd,IACET,KAAK,CAACsB,UAAU,KAAK,GAAG,IACxBf,OAAO,CAACE,IAAI,CAACE,OAAO,CAAC,YAAY,CAAC,KAAK,CAAC,CAAC,EACzC;UACAJ,OAAO,CAACW,SAAS,GAAGzB,MAAM;QAC5B,CAAC,MAAM;UACL,IAAIc,OAAO,aAAPA,OAAO,eAAPA,OAAO,CAAEY,IAAI,EAAE;YACjB,IAAIZ,OAAO,CAACY,IAAI,CAACR,OAAO,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC,EAAE;cAC1C;cACAJ,OAAO,CAACW,SAAS,GAAG;gBAAA,OAClBK,qBAAqB,CACnBhB,OAAO,CAACY,IAAI;gBACZ;gBACA,UAACK,OAAO;kBAAA,OAAKC,OAAO,CAAC,mBAAYzB,KAAK,CAACkB,SAAS,EAAG,EAAEM,OAAO,CAAC;gBAAA,EAC9D;cAAA;YACL,CAAC,MAAM;cACL;cACA;cACAjB,OAAO,CAACW,SAAS,GAAG,UAACM,OAAO;gBAAA,OAAKC,OAAO,CAAC,mBAAYzB,KAAK,CAACkB,SAAS,EAAG,EAAEM,OAAO,CAAC;cAAA;YACnF;UACF,CAAC,MAAM;YACL;YACA;YACAjB,OAAO,CAACW,SAAS,GAAG,UAACM,OAAO;cAAA,OAAKC,OAAO,CAAC,mBAAYzB,KAAK,CAACkB,SAAS,EAAG,EAAEM,OAAO,CAAC;YAAA;UACnF;QACF;MACF,CAAC,MAAM;QACL;MACF;MAEA,IAAI1B,IAAI,KAAK,KAAK,EAAE;QAClB;QACA,IAAIS,OAAO,CAACK,QAAQ,IAAIL,OAAO,CAACK,QAAQ,CAACK,MAAM,EAAE;UAC/CV,OAAO,CAAC,YAAY,CAAC,GAAG,IAAI,EAAC;UAC7BA,OAAO,CAACK,QAAQ,GAAGhB,iBAAiB,CAACI,KAAK,CAACY,QAAQ,CAAC;QACtD;QACAb,GAAG,CAAC2B,IAAI,CAACnB,OAAO,CAAC;MACnB,CAAC,MAAM;QACL,IAAIA,OAAO,CAACK,QAAQ,IAAIL,OAAO,CAACK,QAAQ,CAACK,MAAM,EAAE;UAC/CV,OAAO,CAAC,YAAY,CAAC,GAAG,IAAI,EAAC;UAC7BA,OAAO,CAACK,QAAQ,GAAGhB,iBAAiB,CAACI,KAAK,CAACY,QAAQ,EAAE,QAAQ,CAAC;UAC9Db,GAAG,CAAC2B,IAAI,CAACnB,OAAO,CAAC;QACnB;MACF;IAAC;IAjJH,oDAA0B;MAAA;MAAA,yBAiItB;IAiBJ;EAAC;IAAA;EAAA;IAAA;EAAA;EAED,OAAOR,GAAG;AACZ;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AANA,SAOewB,qBAAqB;EAAA;AAAA;AAAA;EAAA,oFAApC,kBAAqCJ,IAAI,EAAED,SAAS;IAAA;MAAA;QAAA;UAAA;YAAA,kCAC3C;cACLC,IAAI,EAAJA,IAAI;cACJQ,IAAI,kBAAG;gBACL,OAAO;kBAAET,SAAS,EAAE;gBAAK,CAAC;cAC5B,CAAC;cACKU,OAAO,qBAAG;gBAAA;gBAAA;kBAAA;kBAAA;oBAAA;sBAAA;wBAAA;0BAAA,MACVV,SAAS,YAAYW,OAAO;4BAAA;4BAAA;0BAAA;0BAAA;0BAAA;0BAAA,OAEPX,SAAS;wBAAA;0BAAxBY,MAAM;0BACZ,KAAI,CAACZ,SAAS,GAAGY,MAAM;0BAAA;0BAAA;wBAAA;0BAAA;0BAAA;wBAAA;0BAAA;wBAAA;0BAM3B,KAAI,CAACZ,SAAS,GAAGA,SAAS;wBAAA;wBAAA;0BAAA;sBAAA;oBAAA;kBAAA;gBAAA;cAC5B,CAAC;cACDa,MAAM,kBAACC,CAAC,EAAE;gBACR,OAAO,IAAI,CAACd,SAAS,GAAGc,CAAC,CAAC,IAAI,CAACd,SAAS,CAAC,GAAG,IAAI;cAClD;YACF,CAAC;UAAA;UAAA;YAAA;QAAA;MAAA;IAAA;EAAA,CACF;EAAA;AAAA;AAED,IAAMe,KAAK,GAAG;EACZpC,MAAM,EAAE,EAAE;EACVqC,SAAS,EAAE,EAAE;EACbC,cAAc,EAAE;AAClB,CAAC;AAED,IAAMC,SAAS,GAAG;EAChBC,UAAU,EAAE,oBAACJ,KAAK,EAAEpC,MAAM,EAAK;IAC7BoC,KAAK,CAACC,SAAS,GAAGrC,MAAM;IACxBoC,KAAK,CAACpC,MAAM,GAAGR,cAAc,CAACiD,MAAM,CAACzC,MAAM,CAAC;EAC9C,CAAC;EACD0C,gBAAgB,EAAE,0BAACN,KAAK,EAAEpC,MAAM,EAAK;IACnCoC,KAAK,CAACE,cAAc,GAAGtC,MAAM;EAC/B,CAAC;EACD;EACA2C,YAAY,EAAE,sBAACP,KAAK,EAAEN,IAAI,EAAK;IAC7BM,KAAK,CAACQ,OAAO,GAAGd,IAAI;EACtB;AACF,CAAC;AAED,IAAMe,OAAO,GAAG;EACdC,cAAc,gCAA8BC,KAAK,EAAE;IAAA,IAAlCC,MAAM,QAANA,MAAM;MAAEC,QAAQ,QAARA,QAAQ;MAAEb,KAAK,QAALA,KAAK;IACtC,OAAO,IAAIJ,OAAO,CAAC,UAACL,OAAO,EAAEuB,MAAM,EAAK;MACtC;MACA,IAAIC,cAAc,GAAG,EAAE;MACvB;MACA,IAAIzD,WAAW,CAAC0C,KAAK,CAACC,SAAS,CAAC,EAAE;QAChC;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA,IAAMe,IAAI,GAAG;UACXC,SAAS,EAAE,GAAG;UACdC,IAAI,EAAE;QACR,CAAC;QACDzD,UAAU,CAACuD,IAAI,CAAC,CAACG,IAAI,CAAC,UAACC,QAAQ,EAAK;UAClC,uBAAoCA,QAAQ,CAACC,MAAM;YAA3CC,QAAQ,oBAARA,QAAQ;YAAEC,aAAa,oBAAbA,aAAa;UAC/B,IAAMf,OAAO,GAAG,CAAC,CAAC;UAClBc,QAAQ,CAACE,OAAO,CAAC,UAACC,IAAI,EAAK;YACzBjB,OAAO,CAACiB,IAAI,CAACvD,OAAO,CAAC,GAAGuD,IAAI,CAACC,SAAS;UACxC,CAAC,CAAC;UACFd,MAAM,CAAC,qBAAqB,EAAEU,QAAQ,EAAE;YAAEK,IAAI,EAAE;UAAK,CAAC,CAAC;UACvDf,MAAM,CAAC,qBAAqB,EAAEJ,OAAO,EAAE;YAAEmB,IAAI,EAAE;UAAK,CAAC,CAAC;UACtDf,MAAM,CAAC,kBAAkB,EAAEW,aAAa,CAAC;UACzC,IAAMK,cAAc,GAAKR,QAAQ,CAACC,MAAM,CAAlCO,cAAc;UACpB,IAAItE,WAAW,CAACsE,cAAc,CAAC,EAAE;YAC/BA,cAAc,GAAG,EAAE;UACrB;UAEA,IAAIC,cAAc,GAAG,EAAE,EAAC;UAAA,4CACLD,cAAc;YAAA;UAAA;YAAjC,uDAAmC;cAAA,IAAxBH,IAAI;cACbI,cAAc,gCAAOA,cAAc;gBAAIC,KAAK,EAAE;cAAG,GAAKL,IAAI,GAAG;YAC/D;UAAC;YAAA;UAAA;YAAA;UAAA;UACDV,cAAc,GAAGpD,iBAAiB,CAACkE,cAAc,CAAC,EAAC;UACnDjB,MAAM,CAAC,YAAY,EAAEG,cAAc,CAAC;UACpCxB,OAAO,CAACwB,cAAc,CAAC;QACzB,CAAC,CAAC,CAACgB,KAAK,CAAC,UAACC,KAAK,EAAK;UAClBlB,MAAM,CAACkB,KAAK,CAAC;QACf,CAAC,CAAC;MACJ,CAAC,MAAM;QACL,IAAIC,WAAW,GAAG,EAAE;QACpBA,WAAW,GAAGtE,iBAAiB,CAACqC,KAAK,CAACC,SAAS,CAAC,EAAC;QACjDW,MAAM,CAAC,YAAY,EAAEqB,WAAW,CAAC;QACjC1C,OAAO,CAAC0C,WAAW,CAAC;MACtB;IACF,CAAC,CAAC;EACJ;AACF,CAAC;AAED,eAAe;EACbC,UAAU,EAAE,IAAI;EAChBlC,KAAK,EAALA,KAAK;EACLG,SAAS,EAATA,SAAS;EACTM,OAAO,EAAPA;AACF,CAAC"}]}