package com.sunyard.etl.nps.orm;

import java.sql.ResultSet;
import java.sql.SQLException;

import com.sunyard.etl.nps.model.NpOutputDef;
import com.sunyard.etl.system.orm.Orm;

public class NpOutputDefOrm implements Orm<NpOutputDef>{

	@Override
	public NpOutputDef orm(ResultSet rs) throws SQLException {
		NpOutputDef npOutputDef = new NpOutputDef();
		try {
			npOutputDef.setNopaperId(rs.getString("NOPAPER_ID"));
			npOutputDef.setBusinessId(rs.getInt("BUSINESS_ID"));
			npOutputDef.setDataSourceId(rs.getString("DATA_SOURCE_ID"));
		} catch (SQLException e) {
			e.printStackTrace();
		}
		return npOutputDef;
	}

}
