package com.sunyard.etl.nps.dao;

import java.sql.SQLException;
import java.util.ArrayList;
import java.util.List;
import java.util.UUID;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.sunyard.util.dbutil.DBHandler;

import com.sun.rowset.CachedRowSetImpl;
import com.sunyard.etl.nps.model.BpTmpbatchTb;
import com.sunyard.etl.nps.model.BpVoucherChkTb;
import com.sunyard.etl.nps.model.FlFlowTb;
import com.sunyard.etl.nps.model.NpBusinessData;
import com.sunyard.etl.nps.model.BpTmpdata1Tb;
import com.sunyard.etl.nps.orm.BpTmpbatchOrm;
import com.sunyard.etl.nps.orm.BpTmpdata1Orm;
import com.sunyard.etl.nps.orm.FlFlowTbOrm;
import com.sunyard.etl.system.common.Constants;
import com.sunyard.etl.system.orm.Orm;

public class ADMS5Dao {
	protected final Logger log = LoggerFactory.getLogger(getClass());
	private Orm<BpTmpdata1Tb> BpTmpdata1Orm = new BpTmpdata1Orm();
	private Orm<BpTmpbatchTb> BpTmpbatchOrm = new BpTmpbatchOrm();

	/**
	 * 
	 * @Title cleanBatch
	 * @Description 作废批次：将批次设为无效批次
	 * <AUTHOR>
	 * 2017年7月20日
	 * @return
	 * @throws SQLException 
	 */
	public void asideBatch(String batchId) throws SQLException{
		DBHandler dbHandler = new DBHandler(Constants.CONN_POOL_NAME);
		String sql = "UPDATE BP_TMPBATCH_TB T  SET T.IS_INVALID  = 0 WHERE T.BATCH_ID = ?";
		dbHandler.execute(sql, batchId);
	}
	
	
	
	
	
	/**
	 * 
	 * @Title checkFlow
	 * @Description
	 * <AUTHOR>
	 * 2017年7月20日
	 * @param serialNo
	 * @param flowId
	 * @return
	 * @throws SQLException
	 */
	public boolean checkFlowStep1(String occurDate, String siteNo, String operatorNo, String flowId , String serialNo ) throws SQLException {
		DBHandler dbHandler = new DBHandler(Constants.CONN_POOL_NAME);
		String sql = "UPDATE FL_FLOW_TB T SET T.CHECK_FLAG = '1' , T.LSERIAL_NO =? WHERE T.OCCUR_DATE =?  AND T.SITE_NO =?  AND T.OPERATOR_NO =?  AND  T.FLOW_ID =?" ;
		int i = dbHandler.execute(sql, serialNo,occurDate,siteNo,operatorNo,flowId);
		if (i <= 0) {
			return false;
		}
		return true;
	}
	
	public boolean checkFlowStep2(String serialNo) throws SQLException {
		DBHandler dbHandler = new DBHandler(Constants.CONN_POOL_NAME);
		String sql = "UPDATE BP_TMPDATA_1_TB T SET T.CHECK_FLAG = '1'  WHERE T.SERIAL_NO = ?";
		int i = dbHandler.execute(sql, serialNo);
		if (i <= 0) {
			return false;
		}
		return true;
	}
	
	// 释放流水
	public boolean checkFlowStep3(String serialNo) throws SQLException {
		DBHandler dbHandler = new DBHandler(Constants.CONN_POOL_NAME);
		String sql = "UPDATE BP_TMPDATA_1_TB T SET T.FLOW_ID = '',T.CHECK_FLAG = '-1'  WHERE T.SERIAL_NO = ?";
		int i = dbHandler.execute(sql, serialNo);
		if (i <= 0) {
			return false;
		}
		return true;
	}
	
	
	/**
	 * 
	 * @Title checkFlow
	 * @Description 勾兑流水
	 * <AUTHOR>
	 * 2017年7月12日
	 * @param businessNo
	 * @return
	 * @throws SQLException
	 */
	public boolean checkFlow(String flowId) throws SQLException {
		DBHandler dbHandler = new DBHandler(Constants.CONN_POOL_NAME);
		String sql1 = "UPDATE  FL_FLOW_TB FL SET FL.LSERIAL_NO = (SELECT TMP1.SERIAL_NO FROM BP_TMPDATA_1_TB TMP1  WHERE TMP1.FLOW_ID = '"+flowId+"' AND TMP1.PS_LEVEL = 1 ),FL.CHECK_FLAG = 1 WHERE FL.FLOW_ID = '"+flowId+"'";
		String sql2 = "UPDATE  BP_TMPDATA_1_TB TMP1 SET TMP1.CHECK_FLAG = '1' WHERE TMP1.FLOW_ID = '"+flowId+"' AND TMP1.PS_LEVEL = '1'";
		String sql3 = "UPDATE  BP_TMPDATA_1_TB TMP1 SET TMP1.CHECK_FLAG = '0' WHERE TMP1.FLOW_ID = '"+flowId+"' AND TMP1.PS_LEVEL = '0'";
		int result[] = dbHandler.executeAsBatch(sql1, sql2, sql3);
		for (int i : result) {
			if (i == 0) return false;
		}
		return true;
	}
	
	
	
	/**
	 * 
	 * @Title cleanBatch
	 * @Description 清理批次表
	 * <AUTHOR>
	 * 2017年7月20日
	 * @return
	 * @throws SQLException 
	 */
	public void cleanTmpBatch(String batchId) throws SQLException{
		DBHandler dbHandler = new DBHandler(Constants.CONN_POOL_NAME);
		String sql = "DELETE FROM BP_TMPBATCH_TB T  WHERE T.BATCH_ID = ?";
		dbHandler.execute(sql, batchId);
	}
	
	
	
	/**
	 * 
	 * @Title cleanTmpData
	 * @Description 清理数据表
	 * <AUTHOR>
	 * 2017年7月20日
	 * @return
	 * @throws SQLException 
	 */
	public void cleanTmpData(String batchId) throws SQLException{
		DBHandler dbHandler = new DBHandler(Constants.CONN_POOL_NAME);
		String sql = "DELETE FROM BP_TMPDATA_1_TB T  WHERE T.BATCH_ID = ?";
		dbHandler.execute(sql, batchId);
	}
	
	
	
	
	
	
	/**
	 * 
	 * @Title getSeqId
	 * @Description 勾兑流水——查询获取流水序号集合
	 * <AUTHOR>
	 * 2017年8月16日
	 * @param occurDate
	 * @param siteNo
	 * @param operatorNo
	 * @param flowId
	 * @return
	 * @throws SQLException
	 */
	public List<FlFlowTb> querySeqId(String occurDate, String siteNo, String operatorNo, String flowId) throws SQLException {
		FlFlowTbOrm orm = new FlFlowTbOrm();
		List<FlFlowTb> flowList = new ArrayList<FlFlowTb>();
		DBHandler dbHandler = new DBHandler(Constants.CONN_POOL_NAME);
		CachedRowSetImpl rs = null;
		String sql = "SELECT * FROM FL_FLOW_TB T WHERE T.OCCUR_DATE = ? AND  T.SITE_NO = ? AND T.OPERATOR_NO = ? AND T.FLOW_ID = ?";
		rs = dbHandler.queryRs(sql, occurDate, siteNo, operatorNo, flowId);
		while (rs.next()) {
			FlFlowTb flow = orm.orm(rs);
			flowList.add(flow);
		}
		return flowList;
	}
	
	
//	public List<String> releaseFlow(String seqId) throws SQLException {
// 		DBHandler dbHandler = new DBHandler(Constants.CONN_POOL_NAME);
//		CachedRowSetImpl rs = null;
//		String sql = ""
//		rs = dbHandler.queryRs(sql, occurDate, siteNo, operatorNo, flowId);
//		while (rs.next()) {
//			seqId.add(rs.getString("SEQ_ID"));
//		}
//		return seqId;
//	}
	
	
	
	/**
	 * 
	 * @Title getSerialNo
	 * @Description 根据批次号和流水号找到主件的图像序号
	 * <AUTHOR>
	 * 2017年7月20日
	 * @param batchId
	 * @param flowId
	 * @return
	 * @throws SQLException
	 */
	public String querySerialNo(String batchId, String flowId) throws SQLException {
		String serialNo = "";
		DBHandler dbHandler = new DBHandler(Constants.CONN_POOL_NAME);
		CachedRowSetImpl rs = null;
		String sql = "SELECT T.SERIAL_NO FROM BP_TMPDATA_1_TB T  WHERE T.BATCH_ID = ? AND T.FLOW_ID = ? AND T.PS_LEVEL = 1";
		rs = dbHandler.queryRs(sql, batchId, flowId);
		while (rs.next()) {
			serialNo = rs.getString(1);
		}
		return serialNo;
	}
	
	
	
	/**
	 * 
	 * @Title queryBasiData
	 * @Description 查询原业务图像信息
	 * <AUTHOR>
	 * 2017年8月9日
	 * @param busiData
	 * @return
	 * @throws SQLException
	 */
	public List<BpTmpdata1Tb> queryBasiData(NpBusinessData busiData) throws SQLException{
		List<BpTmpdata1Tb> list = new ArrayList<BpTmpdata1Tb>();
		DBHandler dbHandler = new DBHandler(Constants.CONN_POOL_NAME);
		CachedRowSetImpl rs = null;
		String sql = "SELECT * FROM BP_TMPBATCH_TB A, BP_TMPDATA_1_TB B WHERE A.BATCH_ID = B.BATCH_ID AND A.IS_INVALID = 1 AND A.OCCUR_DATE = ? AND A.SITE_NO = ? AND A.OPERATOR_NO = ? AND B.FLOW_ID = ?";
		log.info("根据批次信息从无纸化业务表中取得业务信息:" + sql);
		rs = dbHandler.queryRs(sql, busiData.getOccurDate(),busiData.getSiteNo(),busiData.getOperatorNo(),busiData.getFlowId());
		while (rs.next()) {
			BpTmpdata1Tb bpTmpdata1Tb = BpTmpdata1Orm.orm(rs);
			list.add(bpTmpdata1Tb);
		}
		return list;
	}
	
	
	/**
	 * 
	 * @Title queryMixedBpTmpbatch
	 * @Description  查询混合无纸化批次
	 * <AUTHOR>
	 * 2017年8月11日
	 * @param occurData
	 * @param siteNo
	 * @param operatorNo
	 * @return
	 * @throws SQLException
	 */
	public List<BpTmpbatchTb> queryMixedBpTmpbatch(String occurDate, String siteNo, String operatorNo) throws SQLException{
		List<BpTmpbatchTb> list = new ArrayList<BpTmpbatchTb>();
		DBHandler dbHandler = new DBHandler(Constants.CONN_POOL_NAME);
		CachedRowSetImpl rs = null;
		String sql = "SELECT * FROM BP_VOUCHER_CHK_TB T WHERE T.OCCUR_DATE = ? AND T.SITE_NO = ? AND T.OPERATOR_NO = ? AND T.MIXED_NP_FLAG = 1";
		log.info("寻找混合无纸化批次:" + sql);
		rs = dbHandler.queryRs(sql,occurDate,siteNo,operatorNo);
		while (rs.next()) {
			BpTmpbatchTb bpTmpbatchTb = new BpTmpbatchTb();
			bpTmpbatchTb.setBatchId(rs.getString("BATCHID"));
			list.add(bpTmpbatchTb);
		}
		return list;
	}
	
	
	/**
	 * 
	 * @Title queryBpTmpbatch
	 * @Description 根据批次号寻找批次
	 * <AUTHOR>
	 * 2017年8月16日
	 * @param batchId
	 * @return
	 * @throws SQLException
	 */
	public BpTmpbatchTb queryBpTmpbatch(String batchId) throws SQLException{
		BpTmpbatchTb bpTmpbatchTb = new BpTmpbatchTb();
		DBHandler dbHandler = new DBHandler(Constants.CONN_POOL_NAME);
		CachedRowSetImpl rs = null;
		String sql = "SELECT * FROM BP_TMPBATCH_TB WHERE BATCH_ID = ? ";
		log.info("根据批次号寻找批次:" + sql);
		rs = dbHandler.queryRs(sql,batchId);
		while (rs.next()) {
			bpTmpbatchTb = BpTmpbatchOrm.orm(rs);
		}
		return bpTmpbatchTb;
	}
	
	
	
	
	/**
	 * 
	 * @Title insertBpBatchTb
	 * @Description 插入批次表
	 * <AUTHOR> 2017年7月19日
	 * @param bpTmpdata1TbList
	 * @return
	 * @throws SQLException
	 */
	public boolean insertBpBatchTb(BpTmpbatchTb b) {
		List<Object[]> params = new ArrayList<Object[]>();
		Object[] param = { b.getBatchId(), b.getBatchTotalPage(),
				b.getBusinessId(), b.getBatchCommit(), b.getInputDate(),
				b.getTempDataTable(), b.getInputWorker(), b.getMachineId(),
				b.getNeedProcess(), b.getOccurDate(), b.getOperatorNo(),
				b.getProgressFlag(), b.getSiteNo(), b.getInputTime(),
				b.getImageStatus(), b.getIsInvalid() };
		params.add(param);
		DBHandler dbHandler = new DBHandler(Constants.CONN_POOL_NAME);
		String sql = "INSERT INTO BP_TMPBATCH_TB(BATCH_ID,BATCH_TOTAL_PAGE,BUSINESS_ID,"
				+ "BATCH_COMMIT,INPUT_DATE,TEMP_DATA_TABLE,INPUT_WORKER,MACHINE_ID,"
				+ "NEED_PROCESS,OCCUR_DATE,OPERATOR_NO,PROGRESS_FLAG,SITE_NO,"
				+ "INPUT_TIME,IMAGE_STATUS,IS_INVALID) VALUES(?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)";
		try {
			int[] ii = dbHandler.executeAsBatch(sql, params);
			for (int i : ii) {
				if (i < 0 && i != -2) {
					return false;
				}
			}
		} catch (SQLException e) {
			e.printStackTrace();
			return false;
		}
		return true;
	}
	
	
	
	
	/**
	 * 
	 * @Title insertBpDataTb
	 * @Description 插入数据表
	 * <AUTHOR>
	 * 2017年7月19日
	 * @param bpTmpdata1TbList
	 * @return
	 * @throws SQLException
	 */
	public boolean insertBpDataTb(List<BpTmpdata1Tb> bpTmpdata1TbList) {
		List<Object[]> params = new ArrayList<Object[]>();
		for (BpTmpdata1Tb data : bpTmpdata1TbList) {
			String uuid = UUID.randomUUID().toString().replaceAll("-", "").substring(0,25);
			Object[] param = { uuid, data.getBatchId(), data.getCheckFlag(),
					data.getFlowId(), data.getFormName(),
					data.getInccodeinBatch(), data.getProcessState(),
					data.getPsLevel(), data.getIsFrontPage(),
					data.getFileName(), data.getBackFileName(),
					data.getContentId(), data.getPrimaryInccodein(), data.getDataSourceId() };
			params.add(param);
		}
		DBHandler dbHandler = new DBHandler(Constants.CONN_POOL_NAME);
		String sql = "INSERT INTO BP_TMPDATA_1_TB(SERIAL_NO,BATCH_ID,CHECK_FLAG,FLOW_ID,FORM_NAME,"
				+ "INCCODEIN_BATCH,PROCESS_STATE,PS_LEVEL,IS_FRONT_PAGE,FILE_NAME,"
				+ "BACK_FILE_NAME,CONTENT_ID,PRIMARY_INCCODEIN, DATA_SOURCE_ID) VALUES (?,?,?,?,?,?,?,?,?,?,?,?,?,?)";

		try {
			int[] ii = dbHandler.executeAsBatch(sql, params);
			for (int i : ii) {
				if (i < 0 && i != -2)
					return false;
			}
			return true;
		} catch (SQLException e) {
			return false;
		}

	}
	
	
	
	
	/**
	 * 
	 * @Title insertBpTmpdata1
	 * @Description 往BpTmpdata1表中插入数据
	 * <AUTHOR>
	 * 2017年8月9日
	 * @param bpTmpdata1TbList
	 * @return
	 */
	public boolean insertBpTmpdata1(List<BpTmpdata1Tb> bpTmpdata1TbList){
		List<Object[]> params = new ArrayList<Object[]>();
		for (BpTmpdata1Tb data : bpTmpdata1TbList) {
			Object[] param = { data.getBatchId(), data.getCheckFlag(),
					data.getFlowId(), data.getFormName(),
					data.getInccodeinBatch(), data.getProcessState(),
					data.getPsLevel(), data.getIsFrontPage(),
					data.getFileName(), data.getBackFileName(),
					data.getContentId(), data.getPrimaryInccodein() };
			params.add(param);
		}
		DBHandler dbHandler = new DBHandler(Constants.CONN_POOL_NAME);
		String sql = "INSERT INTO BP_TMPDATA_1_TB(BATCH_ID,CHECK_FLAG,FLOW_ID,FORM_NAME,"
				+ "INCCODEIN_BATCH,PROCESS_STATE,PS_LEVEL,IS_FRONT_PAGE,FILE_NAME,"
				+ "BACK_FILE_NAME,CONTENT_ID,PRIMARY_INCCODEIN) VALUES (?,?,?,?,?,?,?,?,?,?,?,?)";
		try {
			int[] ii = dbHandler.executeAsBatch(sql, params);
			for (int i : ii) {
				if (i < 0 && i != -2)
					return false;
			}
		} catch (SQLException e) {
			return false;
		}
		return true;
	}
	
	
	
	/**
	 * 
	 * @Title 
	 * @Description
	 * <AUTHOR>
	 * 2017年8月10日
	 * @param bpTmpdata1TbList
	 * @return
	 */
	public boolean insertBpVoucherChk(BpVoucherChkTb bpVoucherChkTb){	
		DBHandler dbHandler = new DBHandler(Constants.CONN_POOL_NAME);
		
		int businessId = bpVoucherChkTb.getBusinessId();
		String occurDate = bpVoucherChkTb.getOccurDate();
		String siteNo = bpVoucherChkTb.getSiteNo();
		String operatorNo = bpVoucherChkTb.getOperatorNo();
		String inputWorker = bpVoucherChkTb.getInputWorker();
		String batchid = bpVoucherChkTb.getBatchId();
		String regDate = bpVoucherChkTb.getRegDate();
		String regTime = bpVoucherChkTb.getRegTime();
		String mixedNPFlag = bpVoucherChkTb.getMixedNPFlag();
		String regVoucherNum = bpVoucherChkTb.getRegVoucherNum();
		String scanVoucherNum = bpVoucherChkTb.getScanVoucherNum();
		String batchCommit = bpVoucherChkTb.getBatchCommit();
		String sql = "INSERT INTO BP_VOUCHER_CHK_TB(ID, BUSINESS_ID,OCCUR_DATE,SITE_NO,"
				+ "OPERATOR_NO,INPUT_WORKER, BATCHID,REG_DATE,REG_TIME,MIXED_NP_FLAG,REG_VOUCHER_NUM,SCAN_VOUCHER_NUM, BATCH_COMMIT) "
				+ "values((select max(id) +1 from bp_voucher_chk_tb ),?,?,?,?,?,?,?,?,?,?,?,?)";
		try {
			int i = dbHandler.execute(sql, businessId, occurDate, siteNo,
					operatorNo, inputWorker, batchid, regDate, regTime,
					mixedNPFlag, regVoucherNum, scanVoucherNum, batchCommit);
			if(i < 1){
				return false;
			}
		} catch (SQLException e) {
			e.printStackTrace();
			return false;
		}
		return true;
	}
	
	
	
	
	
	/**
	 * 
	 * @Title updateBpTmpBatch
	 * @Description 更新批次表
	 * <AUTHOR>
	 * 2017年8月9日
	 * @param batchId
	 * @param batchTotalPage
	 * @return
	 */
	public boolean updateBpTmpBatch(String batchId){
		DBHandler dbHandler = new DBHandler(Constants.CONN_POOL_NAME);
		String sql = "UPDATE BP_TMPBATCH_TB T SET  T.BATCH_TOTAL_PAGE = BATCH_TOTAL_PAGE + 1 WHERE T.BATCH_ID = ?";
		try {
			dbHandler.execute(sql,batchId);
		} catch (SQLException e) {
			e.printStackTrace();
			return false;
		}
		return true;
	}
	
	public boolean updateBpTmpBatch(String batchId,String batchTotalPage){
		DBHandler dbHandler = new DBHandler(Constants.CONN_POOL_NAME);
		String sql = "UPDATE BP_TMPBATCH_TB T SET  T.BATCH_TOTAL_PAGE = ? WHERE T.BATCH_ID = ?";
		try {
			dbHandler.execute(sql, batchTotalPage, batchId);
		} catch (SQLException e) {
			e.printStackTrace();
			return false;
		}
		return true;
	}
	
	
	public boolean updateBpTmpdata1(String batchId,int inccodeinBatch){
		DBHandler dbHandler = new DBHandler(Constants.CONN_POOL_NAME);
		String sql = "UPDATE BP_TMPDATA_1_TB T SET T.INCCODEIN_BATCH = INCCODEIN_BATCH + 1 WHERE  T.BATCH_ID = ?  AND T.INCCODEIN_BATCH >= ?";
		try {
			dbHandler.execute(sql,batchId,inccodeinBatch);
		} catch (SQLException e) {
			e.printStackTrace();
			return false;
		}
		return true;
	}
	
	
	
	public boolean updateBpVoucherChk(String batchId, String regVoucherNum, String scanVoucherNum){
		DBHandler dbHandler = new DBHandler(Constants.CONN_POOL_NAME);
		String sql = "UPDATE BP_VOUCHER_CHK_TB  T SET T.REG_VOUCHER_NUM = ?,T.SCAN_VOUCHER_NUM = ? WHERE T.BATCHID = ?";
		try {
			dbHandler.execute(sql,regVoucherNum,scanVoucherNum, batchId);
		} catch (SQLException e) {
			e.printStackTrace();
			return false;
		}
		return true;
	}
	
	
	
	
	public boolean deleteBpTmpBatch(String batchId,int inccodeinBatch){
		DBHandler dbHandler = new DBHandler(Constants.CONN_POOL_NAME);
		String sql = "DELETE BP_TMPBATCH_TB T WHERE T.BATCH_ID = ? AND T.INCCODEIN_BATCH = ?";
		try {
			dbHandler.execute(sql,batchId,inccodeinBatch);
		} catch (SQLException e) {
			e.printStackTrace();
			return false;
		}
		return true;
	}
	
	
	
	
	
	
	public boolean deleteBpVoucherChk(String occurDate, String siteNo, String operatorNo){
		return false;
		
	}
	

//	public boolean updateBpTmpBatch(String batchId, String batchTotalPage, String progressFlag){
//		DBHandler dbHandler = new DBHandler(Constants.CONN_POOL_NAME);
//		List<String> param = new ArrayList<>();
//		String sql = "UPDATE BP_TMPBATCH_TB T SET 1 = 1  ";
//		if(batchTotalPage != ""){
//			sql = sql +",T.BATCH_TOTAL_PAGE = ? ";
//			param.add(batchTotalPage);
//		}
//		sql = sql + "WHERE T.BATCH_ID = ?";
//		param.add(batchId);
//		String params = param.toString();
//		try {
//			dbHandler.execute(sql,params.substring(1,params.length()-1));
//		} catch (SQLException e) {
//			e.printStackTrace();
//		}
//		return true;
//	}
	
	
}
