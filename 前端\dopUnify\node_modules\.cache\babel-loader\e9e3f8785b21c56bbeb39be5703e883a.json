{"remainingRequest": "D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\node_modules\\babel-loader\\lib\\index.js!D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\node_modules\\@babel\\runtime\\helpers\\esm\\assertThisInitialized.js", "dependencies": [{"path": "D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\node_modules\\@babel\\runtime\\helpers\\esm\\assertThisInitialized.js", "mtime": 456789000000}, {"path": "D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\babel.config.js", "mtime": 1686019818359}, {"path": "D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:ZXhwb3J0IGRlZmF1bHQgZnVuY3Rpb24gX2Fzc2VydFRoaXNJbml0aWFsaXplZChzZWxmKSB7CiAgaWYgKHNlbGYgPT09IHZvaWQgMCkgewogICAgdGhyb3cgbmV3IFJlZmVyZW5jZUVycm9yKCJ0aGlzIGhhc24ndCBiZWVuIGluaXRpYWxpc2VkIC0gc3VwZXIoKSBoYXNuJ3QgYmVlbiBjYWxsZWQiKTsKICB9CiAgcmV0dXJuIHNlbGY7Cn0="}, {"version": 3, "names": ["_assertThisInitialized", "self", "ReferenceError"], "sources": ["D:/1_Project/XYD_Project/dop-4.0/dop-4.1-qianduan-unify/dopUnify/node_modules/@babel/runtime/helpers/esm/assertThisInitialized.js"], "sourcesContent": ["export default function _assertThisInitialized(self) {\n  if (self === void 0) {\n    throw new ReferenceError(\"this hasn't been initialised - super() hasn't been called\");\n  }\n  return self;\n}"], "mappings": "AAAA,eAAe,SAASA,sBAAsB,CAACC,IAAI,EAAE;EACnD,IAAIA,IAAI,KAAK,KAAK,CAAC,EAAE;IACnB,MAAM,IAAIC,cAAc,CAAC,2DAA2D,CAAC;EACvF;EACA,OAAOD,IAAI;AACb"}]}