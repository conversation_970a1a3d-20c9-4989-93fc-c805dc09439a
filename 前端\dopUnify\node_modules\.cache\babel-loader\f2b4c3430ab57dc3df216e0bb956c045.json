{"remainingRequest": "D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\node_modules\\babel-loader\\lib\\index.js!D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\node_modules\\eslint-loader\\index.js??ref--13-0!D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\src\\utils\\date.js", "dependencies": [{"path": "D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\src\\utils\\date.js", "mtime": 1686019809841}, {"path": "D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\babel.config.js", "mtime": 1686019818359}, {"path": "D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\.babelrc", "mtime": 1705285078668}, {"path": "D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\node_modules\\eslint-loader\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["Moment", "dateNowFormat", "Date", "format", "dateNowFormat10", "formatTime", "date", "fmt", "o", "getMonth", "getDate", "getHours", "getMinutes", "getSeconds", "test", "replace", "RegExp", "$1", "getFullYear", "substr", "length", "k", "getPreMonth", "year", "month", "parseInt", "getPreYear", "console", "log", "getNextMonth", "t2", "getNextYear"], "sources": ["D:/1_Project/XYD_Project/dop-4.0/dop-4.1-qianduan-unify/dopUnify/src/utils/date.js"], "sourcesContent": ["// 日期格式化\n\nimport Moment from 'moment'\n/**\n * 获取当前日期\n */\nexport function dateNowFormat() {\n  return Moment(new Date()).format('YYYYMMDDHHmmss')\n}\n/**\n * 获取当前日期 十位格式\n */\nexport function dateNowFormat10() {\n  return Moment(new Date()).format('YYYY-MM-DD')\n}\n\nexport function formatTime(date, fmt = 'yyyy-MM-dd hh:mm:ss') {\n  var o = {\n    'M+': date.getMonth() + 1, // 月份\n    'd+': date.getDate(), // 日\n    'h+': date.getHours(), // 小时\n    'm+': date.getMinutes(), // 分\n    's+': date.getSeconds() // 秒\n  }\n  if (/(y+)/.test(fmt)) { fmt = fmt.replace(RegExp.$1, (date.getFullYear() + '').substr(4 - RegExp.$1.length)) }\n  for (var k in o) {\n    if (new RegExp('(' + k + ')').test(fmt)) { fmt = fmt.replace(RegExp.$1, (RegExp.$1.length === 1) ? (o[k]) : (('00' + o[k]).substr(('' + o[k]).length))) }\n  }\n  return fmt\n}\nexport function getPreMonth(date) {\n  let year = date.getFullYear() // 获取当前日期的年份\n  let month = date.getMonth() // 获取当前日期的月份\n  if (month === 0) {\n    year = parseInt(year) - 1\n    month = 12\n  }\n  if (month < 10) {\n    month = '0' + month\n  }\n  return year + '-' + month\n}\nexport function getPreYear(date) {\n  let year = date.getFullYear() // 获取当前日期的年份\n  let month = date.getMonth() + 1 // 获取当前日期的月份\n  year = parseInt(year) - 1\n  if (month < 10) {\n    month = '0' + month\n  }\n  console.log(year + '-' + month)\n  return year + '-' + month\n}\n/**\n * 获取下一个月\n *\n * @date 格式为yyyy-mm-dd的日期，如：2014-01-25\n */\nexport function getNextMonth(date) {\n  let year = date.getFullYear() // 获取当前日期的年份\n  let month = date.getMonth() + 2 // 获取当前日期的月份\n  if (month === 13) {\n    year = parseInt(year) + 1\n    month = 1\n  }\n  if (month < 10) {\n    month = '0' + month\n  }\n\n  var t2 = year + '-' + month\n  return t2\n}\nexport function getNextYear(date) {\n  let year = date.getFullYear() // 获取当前日期的年份\n  let month = date.getMonth() + 1 // 获取当前日期的月份\n  year = parseInt(year) + 1\n  if (month < 10) {\n    month = '0' + month\n  }\n  return year + '-' + month\n}\n"], "mappings": ";;;;;;AAAA;;AAEA,OAAOA,MAAM,MAAM,QAAQ;AAC3B;AACA;AACA;AACA,OAAO,SAASC,aAAa,GAAG;EAC9B,OAAOD,MAAM,CAAC,IAAIE,IAAI,EAAE,CAAC,CAACC,MAAM,CAAC,gBAAgB,CAAC;AACpD;AACA;AACA;AACA;AACA,OAAO,SAASC,eAAe,GAAG;EAChC,OAAOJ,MAAM,CAAC,IAAIE,IAAI,EAAE,CAAC,CAACC,MAAM,CAAC,YAAY,CAAC;AAChD;AAEA,OAAO,SAASE,UAAU,CAACC,IAAI,EAA+B;EAAA,IAA7BC,GAAG,uEAAG,qBAAqB;EAC1D,IAAIC,CAAC,GAAG;IACN,IAAI,EAAEF,IAAI,CAACG,QAAQ,EAAE,GAAG,CAAC;IAAE;IAC3B,IAAI,EAAEH,IAAI,CAACI,OAAO,EAAE;IAAE;IACtB,IAAI,EAAEJ,IAAI,CAACK,QAAQ,EAAE;IAAE;IACvB,IAAI,EAAEL,IAAI,CAACM,UAAU,EAAE;IAAE;IACzB,IAAI,EAAEN,IAAI,CAACO,UAAU,EAAE,CAAC;EAC1B,CAAC;;EACD,IAAI,MAAM,CAACC,IAAI,CAACP,GAAG,CAAC,EAAE;IAAEA,GAAG,GAAGA,GAAG,CAACQ,OAAO,CAACC,MAAM,CAACC,EAAE,EAAE,CAACX,IAAI,CAACY,WAAW,EAAE,GAAG,EAAE,EAAEC,MAAM,CAAC,CAAC,GAAGH,MAAM,CAACC,EAAE,CAACG,MAAM,CAAC,CAAC;EAAC;EAC7G,KAAK,IAAIC,CAAC,IAAIb,CAAC,EAAE;IACf,IAAI,IAAIQ,MAAM,CAAC,GAAG,GAAGK,CAAC,GAAG,GAAG,CAAC,CAACP,IAAI,CAACP,GAAG,CAAC,EAAE;MAAEA,GAAG,GAAGA,GAAG,CAACQ,OAAO,CAACC,MAAM,CAACC,EAAE,EAAGD,MAAM,CAACC,EAAE,CAACG,MAAM,KAAK,CAAC,GAAKZ,CAAC,CAACa,CAAC,CAAC,GAAK,CAAC,IAAI,GAAGb,CAAC,CAACa,CAAC,CAAC,EAAEF,MAAM,CAAC,CAAC,EAAE,GAAGX,CAAC,CAACa,CAAC,CAAC,EAAED,MAAM,CAAE,CAAC;IAAC;EAC1J;EACA,OAAOb,GAAG;AACZ;AACA,OAAO,SAASe,WAAW,CAAChB,IAAI,EAAE;EAChC,IAAIiB,IAAI,GAAGjB,IAAI,CAACY,WAAW,EAAE,EAAC;EAC9B,IAAIM,KAAK,GAAGlB,IAAI,CAACG,QAAQ,EAAE,EAAC;EAC5B,IAAIe,KAAK,KAAK,CAAC,EAAE;IACfD,IAAI,GAAGE,QAAQ,CAACF,IAAI,CAAC,GAAG,CAAC;IACzBC,KAAK,GAAG,EAAE;EACZ;EACA,IAAIA,KAAK,GAAG,EAAE,EAAE;IACdA,KAAK,GAAG,GAAG,GAAGA,KAAK;EACrB;EACA,OAAOD,IAAI,GAAG,GAAG,GAAGC,KAAK;AAC3B;AACA,OAAO,SAASE,UAAU,CAACpB,IAAI,EAAE;EAC/B,IAAIiB,IAAI,GAAGjB,IAAI,CAACY,WAAW,EAAE,EAAC;EAC9B,IAAIM,KAAK,GAAGlB,IAAI,CAACG,QAAQ,EAAE,GAAG,CAAC,EAAC;EAChCc,IAAI,GAAGE,QAAQ,CAACF,IAAI,CAAC,GAAG,CAAC;EACzB,IAAIC,KAAK,GAAG,EAAE,EAAE;IACdA,KAAK,GAAG,GAAG,GAAGA,KAAK;EACrB;EACAG,OAAO,CAACC,GAAG,CAACL,IAAI,GAAG,GAAG,GAAGC,KAAK,CAAC;EAC/B,OAAOD,IAAI,GAAG,GAAG,GAAGC,KAAK;AAC3B;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASK,YAAY,CAACvB,IAAI,EAAE;EACjC,IAAIiB,IAAI,GAAGjB,IAAI,CAACY,WAAW,EAAE,EAAC;EAC9B,IAAIM,KAAK,GAAGlB,IAAI,CAACG,QAAQ,EAAE,GAAG,CAAC,EAAC;EAChC,IAAIe,KAAK,KAAK,EAAE,EAAE;IAChBD,IAAI,GAAGE,QAAQ,CAACF,IAAI,CAAC,GAAG,CAAC;IACzBC,KAAK,GAAG,CAAC;EACX;EACA,IAAIA,KAAK,GAAG,EAAE,EAAE;IACdA,KAAK,GAAG,GAAG,GAAGA,KAAK;EACrB;EAEA,IAAIM,EAAE,GAAGP,IAAI,GAAG,GAAG,GAAGC,KAAK;EAC3B,OAAOM,EAAE;AACX;AACA,OAAO,SAASC,WAAW,CAACzB,IAAI,EAAE;EAChC,IAAIiB,IAAI,GAAGjB,IAAI,CAACY,WAAW,EAAE,EAAC;EAC9B,IAAIM,KAAK,GAAGlB,IAAI,CAACG,QAAQ,EAAE,GAAG,CAAC,EAAC;EAChCc,IAAI,GAAGE,QAAQ,CAACF,IAAI,CAAC,GAAG,CAAC;EACzB,IAAIC,KAAK,GAAG,EAAE,EAAE;IACdA,KAAK,GAAG,GAAG,GAAGA,KAAK;EACrB;EACA,OAAOD,IAAI,GAAG,GAAG,GAAGC,KAAK;AAC3B"}]}