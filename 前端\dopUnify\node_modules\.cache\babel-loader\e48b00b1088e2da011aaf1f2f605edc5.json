{"remainingRequest": "D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\node_modules\\thread-loader\\dist\\cjs.js!D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\node_modules\\babel-loader\\lib\\index.js!D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\src\\views\\system\\config\\audit\\component\\table\\index.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\src\\views\\system\\config\\audit\\component\\table\\index.vue", "mtime": 1686019807872}, {"path": "D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\babel.config.js", "mtime": 1686019818359}, {"path": "D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\node_modules\\thread-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1655715099000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, null]}