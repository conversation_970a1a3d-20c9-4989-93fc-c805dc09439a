package com.sunyard.etl.nps.service.base;

import java.io.File;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Vector;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.sunyard.util.file.FileUtil;
import org.sunyard.util.fileService.FileServerHeader;
import org.sunyard.util.fileService.SocketTransClient;

import com.sunyard.etl.nps.dao.NpFtpDataDateDao;
import com.xxl.job.core.log.XxlJobLogger;



public class FileService {
	protected final Logger log = LoggerFactory.getLogger(getClass());
	private FileUtil fileUtil = new FileUtil();
	private NpFtpDataDateDao npFtpDataDateDao = new NpFtpDataDateDao();

	
	public int addToFileServer(String serverIp, String port, String IMGPath, String JPGPath) {
		SocketTransClient client = new SocketTransClient();
		boolean flag =false;
		File file = new File(JPGPath);
		//建立连接
		int ret = client.InitConnect(serverIp, Integer.parseInt(port));
		if (ret != 1) {
			//连接失败
			log.info("建立连接失败");
			return 0;
		} else {
			log.info("地址：" + serverIp + "端口：" + port +  "建立连接成功");
		}
		// 第一次发送：发送数据包包头
		log.info("准备包头：" + IMGPath);
		FileServerHeader fileServerHeader = new FileServerHeader(IMGPath);
		log.info("第一次发送报文内容：");
		flag = client.sendMessageToFileServer(fileServerHeader);
		if (flag = false)return 0;
		
		// 第二次发送：发送数据包包头，包头包含要发送文件的大小
		FileServerHeader fileServerHeaderWithData = new FileServerHeader(JPGPath,(int)fileUtil.size(file));
		log.info("第三次发送报文内容： ");
		flag = client.sendMessageToFileServer(fileServerHeaderWithData);
		if (flag = false)return 0;
		
		// 第三次发送：发送文件
		log.info("第四次发送报文内容： "+ JPGPath);
		flag = client.sendFile(JPGPath);
		if (flag = false)return 0;
		return 1;
	}
	
	
	
	public List<File> getDirs(String path){
		List<File> list = new ArrayList<File>();
		File file = new File(path);
		File[] array = file.listFiles();
		if (null != array && array.length > 0) {
			for (File i : array) {
				if (i.isDirectory()) {
					list.add(i);
				}
			}
		}
		return list;
	}

	
	public void check(List<File> list) {
		/*
		 * 过滤掉非批次路径，过滤规则：过滤路径下没有图像文件的路径
		 */
		for (File i : list) {
			if (!FileUtil.existFileType(i, "JPG")) {
				list.remove(i);
			}
		}
	}
	

	public List<File> getBatchFileList(String path) throws SQLException {
		List<File> batchFileList = new ArrayList<File>();
		List<File> occurDateList = getDirs(path);
		if (null == occurDateList || occurDateList.size() == 0){
			return null;
		}
		for (File i : occurDateList) {
			String filename = i.getName();
			if (npFtpDataDateDao.isFinish(filename)) {
				continue;
			} else {
				List<File> organFileList = getDirs(i.getPath());
				for (File j : organFileList) {
					if (j.isDirectory()) {
						List<File> operatorFileList = getDirs(j.getPath());
						for (File k : operatorFileList) {
							if (k.isDirectory()) {
								batchFileList.add(k);
							}
						}
					}
				}
			}

		}
		return batchFileList;
	}
	

	/**
	 * 
	 * @Title getImgMap
	 * @Description 获取一个目录下的所有图片 imgMap<流水号,图片编号1+图片编号2...>
	 *              path是一个批次所有图片的目录 
	 *              返回这个批次的所有流水信息
	 * <AUTHOR> 2017年7月14日
	 * @param path
	 * @param imgMap
	 */
	public Map<String, List<String>> getImgMap(String path) {
		Map<String, List<String>> imgMap = new HashMap<String, List<String>>();
		File file = new File(path);
		File[] files = file.listFiles();
		for (File img : files) {
			if (img.isDirectory() || img.getName().indexOf("xml") > 1)continue;
//			String occurDate = img.getParentFile().getParentFile().getParentFile().getName();
//			String siteNO = img.getParentFile().getParentFile().getName();
//			String operatorNo = img.getParentFile().getName();
			String flowId = img.getName().substring(0,img.getName().indexOf('-'));
			String num = img.getName().substring(img.getName().indexOf('-') + 1, img.getName().indexOf('.'));
			String fileFormat = img.getName().substring(img.getName().lastIndexOf(".")+1);
			if (imgMap != null && imgMap.containsKey(flowId)) {
				imgMap.get(flowId).add(num); // 一条流水多张图片，将图片序号追加到List后面
			} else {
				List<String> list = new ArrayList<String>();// 将流水信息存到list中
//				list.add(occurDate);
//				list.add(siteNO);
//				list.add(operatorNo);
				list.add(fileFormat);
				list.add(num);
				imgMap.put(flowId, list);
			}
		}
		return imgMap;
	}
}


/*
// 发送文件名长度
log.info("第二次发送报文内容： "+ IMGPath);
System.out.println("第二次发送报文内容： "+ IMGPath);
client.sendMessageToFileServer(IMGPath);
*/
//result = client.recieveMessage();// 接收返回
//log.info("第一次接收的返回信息： "+ result);
//System.out.println("第一次接收的返回信息： "+ result);
//
// 发送报头

//result = client.recieveMessage();// 接收返回
//log.info("第二次接收的返回信息： "+ result);
//System.out.println("第二次接收的返回信息： "+ result);


//int ret2 = client.InitConnect(serverIp, Integer.parseInt(port));
//if (ret2 != 1) {
//	//连接失败
//	log.info("建立连接失败");
//	System.out.println("建立连接失败");
//	return 0;
//} else {
//	System.out.println("建立连接成功");
//}

//================================================================
//
//long			nVersion;	//版本号						0x1002
//long      		nTxCode;	//交易码						2003
//long			lParam;		//交易参数1;						0
//long			wParam;		//交易参数2;						0
//long			bClose;		//是否关闭连接;						0
//long			nRetCode;	//返回码						0
//long			nCheckIndex;//校验序号							0
//long	        	nLen;    	//数据包内容长度(字节数)，如包文是字符串，包括结束符；	lpszServerFile（文件服务的文件名长度）
//long			nLenBuf;	//数据包头缓冲区数据长度				0
//char			szBuf[960];	//数据包头缓冲区数据长度				
//
//
//
//char*			pData									lpszServerFile （文件服务的文件名）
//
//
//
//
//
//ReadData
//
//long			nVersion;	//版本号						0x1002
//long      		nTxCode;	//交易码						4001
//long			lParam;		//交易参数1;						0或者1
//long			wParam;		//交易参数2;						0
//long			bClose;		//是否关闭连接;						0
//long			nRetCode;	//返回码						0
//long			nCheckIndex;//校验序号							0
//long	        	nLen;    	//数据包内容长度(字节数)，如包文是字符串，包括结束符；	文件长度或者512000	
//long			nLenBuf;	//数据包头缓冲区数据长度				0
//char			szBuf[960];	//数据包头缓冲区数据长度
//
//char*			pData									文件内容
//
//
//ReadData