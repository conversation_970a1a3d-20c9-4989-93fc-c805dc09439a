{"remainingRequest": "E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\node_modules\\babel-loader\\lib\\index.js!E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\node_modules\\eslint-loader\\index.js??ref--13-0!E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\src\\api\\views\\home\\index.js", "dependencies": [{"path": "E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\src\\api\\views\\home\\index.js", "mtime": 1686019810920}, {"path": "E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\babel.config.js", "mtime": 1686019818359}, {"path": "E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\.babelrc", "mtime": 1705285078668}, {"path": "E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\node_modules\\eslint-loader\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:aW1wb3J0IF9vYmplY3RTcHJlYWQgZnJvbSAiRTovMV9Qcm9qZWN0L1hZRF9Qcm9qZWN0L2RvcC00LjAvZG9wLTQuMS1xaWFuZHVhbi11bmlmeS9kb3BVbmlmeS9ub2RlX21vZHVsZXMvQGJhYmVsL3J1bnRpbWUvaGVscGVycy9lc20vb2JqZWN0U3ByZWFkMi5qcyI7CmltcG9ydCByZXF1ZXN0IGZyb20gJ0AvdXRpbHMvcmVxdWVzdCc7CmltcG9ydCBkZWZhdWx0U2V0dGluZ3MgZnJvbSAnQC9zZXR0aW5ncyc7CnZhciBzeXN0ZW0gPSBkZWZhdWx0U2V0dGluZ3Muc2VydmljZS5zeXN0ZW07Ci8vIOS4u+mhteaooeWdl2JlZ2luCmltcG9ydCB7IE5vdGljZSB9IGZyb20gJy4vY29tcG9uZW50L25vdGljZSc7IC8vIOWFrOWRiuS/oeaBrwppbXBvcnQgeyB0b2RvTGlzdCB9IGZyb20gJy4vY29tcG9uZW50L3RvZG9MaXN0JzsgLy8g5b6F5Yqe5LqL6aG5CmltcG9ydCB7IHVzdWFsTWVudSB9IGZyb20gJy4vY29tcG9uZW50L3VzdWFsTWVudSc7IC8vIOW4uOeUqOWKn+iDvQppbXBvcnQgeyBCdXNpbmVzcyB9IGZyb20gJy4vY29tcG9uZW50L2J1c2luZXNzJzsgLy8g5Lia5Yqh6YePCmltcG9ydCB7IEltcG9ydGFudCB9IGZyb20gJy4vY29tcG9uZW50L2ltcG9ydGFudCc7IC8vIOmHjeimgeS/oeaBrwppbXBvcnQgeyBDYWxlbmRhciB9IGZyb20gJy4vY29tcG9uZW50L2NhbGVuZGFyJzsgLy8g5L6/562+5pel5Y6GCmltcG9ydCB7IHNob3J0Y3V0TGlzdCB9IGZyb20gJy4vY29tcG9uZW50L3Nob3J0Y3V0TWVudSc7IC8vIOW/q+aNt+iPnOWNlQppbXBvcnQgeyB3b3JrYmVuY2ggfSBmcm9tICcuL2NvbXBvbmVudC93b3JrYmVuY2gnOyAvLyDmiJHnmoTlt6XkvZzlj7AKLy8g5Li76aG15qih5Z2XZW5kCgpleHBvcnQgdmFyIEhvbWUgPSBfb2JqZWN0U3ByZWFkKF9vYmplY3RTcHJlYWQoX29iamVjdFNwcmVhZChfb2JqZWN0U3ByZWFkKF9vYmplY3RTcHJlYWQoX29iamVjdFNwcmVhZChfb2JqZWN0U3ByZWFkKF9vYmplY3RTcHJlYWQoewogIC8qKgogICAqIOS4u+mhteafpeivogogICAqLwogIHF1ZXJ5OiBmdW5jdGlvbiBxdWVyeShkYXRhKSB7CiAgICByZXR1cm4gcmVxdWVzdCh7CiAgICAgIHVybDogc3lzdGVtICsgJy9zeXNEaWFsb2cvcXVlcnkuZG8nLAogICAgICBtZXRob2Q6ICdnZXQnLAogICAgICBwYXJhbXM6IHsKICAgICAgICBtZXNzYWdlOiBkYXRhCiAgICAgIH0KICAgIH0pOwogIH0sCiAgYWNIb21lUGFnZUluaXQ6IGZ1bmN0aW9uIGFjSG9tZVBhZ2VJbml0KGRhdGEpIHsKICAgIHJldHVybiByZXF1ZXN0KHsKICAgICAgdXJsOiAnL2FjSG9tZVBhZ2VJbml0LmRvJywKICAgICAgbWV0aG9kOiAncG9zdCcsCiAgICAgIGRhdGE6IGRhdGEKICAgIH0pOwogIH0KfSwgTm90aWNlKSwgdG9kb0xpc3QpLCB1c3VhbE1lbnUpLCBCdXNpbmVzcyksIEltcG9ydGFudCksIENhbGVuZGFyKSwgc2hvcnRjdXRMaXN0KSwgd29ya2JlbmNoKTs="}, {"version": 3, "names": ["request", "defaultSettings", "system", "service", "Notice", "todoList", "usualMenu", "Business", "Important", "Calendar", "shortcutList", "workbench", "Home", "query", "data", "url", "method", "params", "message", "acHomePageInit"], "sources": ["E:/1_Project/XYD_Project/dop-4.0/dop-4.1-qianduan-unify/dopUnify/src/api/views/home/<USER>"], "sourcesContent": ["import request from '@/utils/request'\nimport defaultSettings from '@/settings'\nconst system = defaultSettings.service.system\n// 主页模块begin\nimport { Notice } from './component/notice' // 公告信息\nimport { todoList } from './component/todoList' // 待办事项\nimport { usualMenu } from './component/usualMenu' // 常用功能\nimport { Business } from './component/business' // 业务量\nimport { Important } from './component/important' // 重要信息\nimport { Calendar } from './component/calendar' // 便签日历\nimport { shortcutList } from './component/shortcutMenu' // 快捷菜单\nimport { workbench } from './component/workbench' // 我的工作台\n// 主页模块end\n\nexport const Home = {\n  /**\n   * 主页查询\n   */\n  query(data) {\n    return request({\n      url: system + '/sysDialog/query.do',\n      method: 'get',\n      params: { message: data }\n    })\n  },\n  acHomePageInit(data) {\n    return request({\n      url: '/acHomePageInit.do',\n      method: 'post',\n      data\n    })\n  },\n  ...Notice,\n  ...todoList,\n  ...usualMenu,\n  ...Business,\n  ...Important,\n  ...Calendar,\n  ...shortcutList,\n  ...workbench\n}\n"], "mappings": ";AAAA,OAAOA,OAAO,MAAM,iBAAiB;AACrC,OAAOC,eAAe,MAAM,YAAY;AACxC,IAAMC,MAAM,GAAGD,eAAe,CAACE,OAAO,CAACD,MAAM;AAC7C;AACA,SAASE,MAAM,QAAQ,oBAAoB,EAAC;AAC5C,SAASC,QAAQ,QAAQ,sBAAsB,EAAC;AAChD,SAASC,SAAS,QAAQ,uBAAuB,EAAC;AAClD,SAASC,QAAQ,QAAQ,sBAAsB,EAAC;AAChD,SAASC,SAAS,QAAQ,uBAAuB,EAAC;AAClD,SAASC,QAAQ,QAAQ,sBAAsB,EAAC;AAChD,SAASC,YAAY,QAAQ,0BAA0B,EAAC;AACxD,SAASC,SAAS,QAAQ,uBAAuB,EAAC;AAClD;;AAEA,OAAO,IAAMC,IAAI;EACf;AACF;AACA;EACEC,KAAK,iBAACC,IAAI,EAAE;IACV,OAAOd,OAAO,CAAC;MACbe,GAAG,EAAEb,MAAM,GAAG,qBAAqB;MACnCc,MAAM,EAAE,KAAK;MACbC,MAAM,EAAE;QAAEC,OAAO,EAAEJ;MAAK;IAC1B,CAAC,CAAC;EACJ,CAAC;EACDK,cAAc,0BAACL,IAAI,EAAE;IACnB,OAAOd,OAAO,CAAC;MACbe,GAAG,EAAE,oBAAoB;MACzBC,MAAM,EAAE,MAAM;MACdF,IAAI,EAAJA;IACF,CAAC,CAAC;EACJ;AAAC,GACEV,MAAM,GACNC,QAAQ,GACRC,SAAS,GACTC,QAAQ,GACRC,SAAS,GACTC,QAAQ,GACRC,YAAY,GACZC,SAAS,CACb"}]}