{"remainingRequest": "D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\node_modules\\babel-loader\\lib\\index.js!D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\node_modules\\qiankun\\es\\errorHandler.js", "dependencies": [{"path": "D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\node_modules\\qiankun\\es\\errorHandler.js", "mtime": 1667130453000}, {"path": "D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\babel.config.js", "mtime": 1686019818359}, {"path": "D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:LyoqCiAqIEBhdXRob3IgS3VpdG9zCiAqIEBzaW5jZSAyMDIwLTAyLTIxCiAqLwpleHBvcnQgeyBhZGRFcnJvckhhbmRsZXIsIHJlbW92ZUVycm9ySGFuZGxlciB9IGZyb20gJ3NpbmdsZS1zcGEnOwpleHBvcnQgZnVuY3Rpb24gYWRkR2xvYmFsVW5jYXVnaHRFcnJvckhhbmRsZXIoZXJyb3JIYW5kbGVyKSB7CiAgd2luZG93LmFkZEV2ZW50TGlzdGVuZXIoJ2Vycm9yJywgZXJyb3JIYW5kbGVyKTsKICB3aW5kb3cuYWRkRXZlbnRMaXN0ZW5lcigndW5oYW5kbGVkcmVqZWN0aW9uJywgZXJyb3JIYW5kbGVyKTsKfQpleHBvcnQgZnVuY3Rpb24gcmVtb3ZlR2xvYmFsVW5jYXVnaHRFcnJvckhhbmRsZXIoZXJyb3JIYW5kbGVyKSB7CiAgd2luZG93LnJlbW92ZUV2ZW50TGlzdGVuZXIoJ2Vycm9yJywgZXJyb3JIYW5kbGVyKTsKICB3aW5kb3cucmVtb3ZlRXZlbnRMaXN0ZW5lcigndW5oYW5kbGVkcmVqZWN0aW9uJywgZXJyb3JIYW5kbGVyKTsKfQ=="}, {"version": 3, "names": ["addError<PERSON><PERSON>ler", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "addGlobalUncaughtErrorHandler", "<PERSON><PERSON><PERSON><PERSON>", "window", "addEventListener", "removeGlobalUncaughtErrorHandler", "removeEventListener"], "sources": ["D:/1_Project/XYD_Project/dop-4.0/dop-4.1-qianduan-unify/dopUnify/node_modules/qiankun/es/errorHandler.js"], "sourcesContent": ["/**\n * <AUTHOR>\n * @since 2020-02-21\n */\nexport { addErrorHandler, removeErrorHand<PERSON> } from 'single-spa';\nexport function addGlobalUncaughtErrorHandler(errorHandler) {\n  window.addEventListener('error', errorHandler);\n  window.addEventListener('unhandledrejection', errorHandler);\n}\nexport function removeGlobalUncaughtError<PERSON>andler(errorHandler) {\n  window.removeEventListener('error', errorHandler);\n  window.removeEventListener('unhandledrejection', errorHandler);\n}"], "mappings": "AAAA;AACA;AACA;AACA;AACA,SAASA,eAAe,EAAEC,kBAAkB,QAAQ,YAAY;AAChE,OAAO,SAASC,6BAA6B,CAACC,YAAY,EAAE;EAC1DC,MAAM,CAACC,gBAAgB,CAAC,OAAO,EAAEF,YAAY,CAAC;EAC9CC,MAAM,CAACC,gBAAgB,CAAC,oBAAoB,EAAEF,YAAY,CAAC;AAC7D;AACA,OAAO,SAASG,gCAAgC,CAACH,YAAY,EAAE;EAC7DC,MAAM,CAACG,mBAAmB,CAAC,OAAO,EAAEJ,YAAY,CAAC;EACjDC,MAAM,CAACG,mBAAmB,CAAC,oBAAoB,EAAEJ,YAAY,CAAC;AAChE"}]}