package com.sunyard.etl.custom.handler;

import java.io.BufferedReader;
import java.io.File;
import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.InputStreamReader;
import java.io.OutputStreamWriter;
import java.io.PrintWriter;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.List;

import com.sunyard.etl.system.common.Constants;
import org.springframework.stereotype.Service;

import com.sunyard.etl.system.dao.DataDateDAO;
import com.sunyard.etl.system.dao.impl.DataDateDAOImpl;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.enums.JobStartEnum;
import com.xxl.job.core.handler.IJobHandler;
import com.xxl.job.core.handler.annotation.JobHandler;
import com.xxl.job.core.log.XxlJobLogger;

@JobHandler(value = "FileFormat_CRDD1020", name = "CRDD1020文件格式化")
@Service
public class FileFormat_CRDD1020 extends IJobHandler {

	private static final long serialVersionUID = 1L;

	private static DataDateDAO dateDao = new DataDateDAOImpl();

	@Override
	public ReturnT<String> execute(String jobId, String... arg1) throws Exception {
		XxlJobLogger.log("开始CRDD1020文件格式化...");
		String jobDate = dateDao.getDataDate();
		if (null != arg1[0]) {
			String dirPath = arg1[0].toString().replace("@", jobDate);
			File dir = new File(dirPath);
			if (!dir.isDirectory()) {
				XxlJobLogger.log("INFO: 资源不足，目录不存在：" + dir, jobId + "");
				return new ReturnT<String>(ReturnT.FAIL_CODE, JobStartEnum.TASK_STATE_NO_RESOURCE.getCode(),
						"文件目录" + dir.getPath() + "不存在");
			}
			File preFile = new File(dir, "CRDD1020_"+jobDate+".txt");
			File file = new File(dir, "CRDD1020_"+jobDate+"_proc.txt");
			if (file.exists()) {
				XxlJobLogger.log("INFO: 文件已存在，先删除后再转换：" + file.getPath(), jobId + "");
				file.delete();
			}
			PrintWriter pw;
			pw = new PrintWriter(new OutputStreamWriter(new FileOutputStream(file), "GBK"));
			if (preFile.length()==0) {
				pw.print("|||");
				pw.flush();
				pw.close();
				XxlJobLogger.log("INFO: 源文件不存在，生成转换文件：" + preFile.getPath() + " >> " + file.getPath(), jobId + "");
				return ReturnT.SUCCESS;
			}
			BufferedReader br = new BufferedReader(new InputStreamReader(new FileInputStream(preFile), "GBK"));
			
			String line = null;
			String siteNo = "";
			String siteName = "";
			String occurDate = "";
			int index = -1;
			String cardNo = "";
			String accountName = "";
			String cardType = "";
			String tradeType="";
			String preContent="";
			String occurTime ="";
			String tellerNo = "";
			String authTellerNo = "";
			String subContent = "";
			String btLine1 = "";
			String[] strArr1 = null;
			List list1 = new ArrayList();
			String btLine2 = "";
			String[] strArr2 = null;
			List list2 = new ArrayList();
			try {
				while ((line = br.readLine()) != null || index ==1 || index ==3) {
					if(line.contains("1              ") && line.contains("-CRDD1020")){
						siteNo = line.substring(line.indexOf("( ")+"( ".length(),line.indexOf("-CRDD1020"));
						index = -1;
						continue;
					} 
					if(line.contains("机构 :") && line.contains("日期 :")){
						siteName = line.substring(line.indexOf("机构 :  ")+"机构 :  ".length(),line.indexOf("日期 :")).trim().replace("　", "");
						occurDate = line.substring(line.indexOf("日期 : ")+"日期 : ".length(),line.indexOf("日期 : ")+"日期 : ".length()+10).trim();
						occurDate = new SimpleDateFormat("yyyyMMdd").format(new SimpleDateFormat("yyyy/MM/dd").parse(occurDate));
						index = -1;
						continue;
					}
					if(line.contains("机构号") && line.contains("机构名称") && line.contains("卡号") ){
						btLine1 = line;
						strArr1 = new String[]{
								btLine1.substring(0,btLine1.indexOf("机构号 ")),	
								btLine1.substring(btLine1.indexOf("机构号 "),btLine1.indexOf("机构名称 ")),	
								btLine1.substring(btLine1.indexOf("机构名称 "),btLine1.indexOf("卡号 ")),	
								btLine1.substring(btLine1.indexOf("卡号 "),btLine1.indexOf("户名")),	
								btLine1.substring(btLine1.indexOf("户名"))};
						int idx = 0;
						list1 = new ArrayList();
						for(String str:strArr1){
							list1.add(new int[]{idx, str.getBytes("GBK").length});
							idx += str.getBytes("GBK").length;
						}
						index = -1;
						continue;
					}
					if(line.contains("交易类型") && line.contains("变更前内容") && line.contains("交易时间")){
						btLine2 = line;
						strArr2 = new String[]{
								btLine2.substring(0,btLine2.indexOf("交易类型 ")),	
								btLine2.substring(btLine2.indexOf("交易类型 "),btLine2.indexOf("变更前内容 ")),	
								btLine2.substring(btLine2.indexOf("变更前内容 "),btLine2.indexOf("交易日期 ")),	
								btLine2.substring(btLine2.indexOf("交易日期 "),btLine2.indexOf("交易时间 ")),	
								btLine2.substring(btLine2.indexOf("交易时间 "),btLine2.indexOf("交易柜员 ")),	
								btLine2.substring(btLine2.indexOf("交易柜员 "),btLine2.indexOf("授权柜员")),
								btLine2.substring(btLine2.indexOf("授权柜员"))};
						int idx = 0;
						list2 = new ArrayList();
						for(String str:strArr2){
							list2.add(new int[]{idx, str.getBytes("GBK").length});
							idx += str.getBytes("GBK").length;
						}
						index = -1;
						continue;
					}
					if(("".equals(line.trim()) && index !=1 && index !=3)||line.trim().equals("卡类型")||line.contains("==========================")||"卡资料／状态变更清单".equals(line.trim())){
						index = -1;
						continue;
					}
					if(line.trim().equals("变更后内容")){
						index = 0;
						continue;
					}
					if(index == 0){
						int lineLength = line.getBytes().length;
						int btLength = btLine1.getBytes().length;
						if(lineLength<btLength){
							line = String.format("%-"+btLength+"s", line);
						}
						byte[] arr = line.getBytes("GBK");
						siteNo = new String(arr,((int[])list1.get(1))[0],((int[])list1.get(1))[1],"GBK").trim();//机构号
						siteName = new String(arr,((int[])list1.get(2))[0],((int[])list1.get(2))[1],"GBK").trim().replace("　", "");//机构名称
						cardNo = new String(arr,((int[])list1.get(3))[0],((int[])list1.get(3))[1],"GBK").trim();//卡号
						accountName = new String(arr,((int[])list1.get(4))[0],line.getBytes("GBK").length-((int[])list1.get(4))[0],"GBK").trim();//户名
						index = 1;
						continue;
					}
					if(index == 1){
						cardType = line.trim();//卡类型
						index = 2;
						continue;
					}
					if(index == 2){
						int lineLength = line.getBytes().length;
						int btLength = btLine2.getBytes().length;
						if(lineLength<btLength){
							line = String.format("%-"+btLength+"s", line);
						}
						byte[] arr = line.getBytes("GBK");
						tradeType = new String(arr,((int[])list2.get(1))[0],((int[])list2.get(1))[1],"GBK").trim();//机构号
						preContent = new String(arr,((int[])list2.get(2))[0],((int[])list2.get(2))[1],"GBK").trim();//机构名称
						occurDate = new String(arr,((int[])list2.get(3))[0],((int[])list2.get(3))[1],"GBK").trim();//卡号
						occurDate = new SimpleDateFormat("yyyyMMdd").format(new SimpleDateFormat("yyyy-MM-dd").parse(occurDate)).trim();
						occurTime = new String(arr,((int[])list2.get(4))[0],((int[])list2.get(4))[1],"GBK").trim();//机构名称
						tellerNo = new String(arr,((int[])list2.get(5))[0],((int[])list2.get(5))[1],"GBK").trim();//卡号
						authTellerNo = new String(arr,((int[])list2.get(6))[0],line.getBytes("GBK").length-((int[])list2.get(6))[0],"GBK").trim();//户名

						index = 3;
						continue;
					}
					if(index == 3){
						subContent=line.trim();//变更后内容
						String newLine = siteNo+"|"+siteName+"|"+cardNo+"|"
								+accountName+"|"	//交易柜员号
								+cardType+"|"	//交易流水号
								+tradeType+"|"	//交易时间
								+preContent+"|"	//交易代码
								+occurDate+"|"	//授权柜员号
								+occurTime+"|"	//授权柜员网点号
								+tellerNo+"|"	//授权原因
								+authTellerNo+"|"	//授权原因
								+subContent;	//授权原因
						pw.print(newLine+System.getProperty("line.separator"));
						index = 0;
						continue;
					}
				}
			} catch (IOException e) {
				e.printStackTrace();
			}finally{
				pw.flush();
				pw.close();
			}
			XxlJobLogger.log("INFO: 文件转换成功：" + file.getPath(), jobId + "");
			return ReturnT.SUCCESS;
		}
		return ReturnT.FAIL;
	}

	public static void main(String[] args) {
		FileFormat_CRDD1020 ff = new FileFormat_CRDD1020();
		try {
			ff.execute("9", "C:\\@");
		} catch (Exception e) {
			// TODO Auto-generated catch block
			e.printStackTrace();
		}
	}
}
