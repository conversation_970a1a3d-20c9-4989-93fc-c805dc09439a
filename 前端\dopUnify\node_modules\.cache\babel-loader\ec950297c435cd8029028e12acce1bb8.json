{"remainingRequest": "E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1\\数字运营平台-统一门户工程\\dopUnify\\node_modules\\babel-loader\\lib\\index.js!E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1\\数字运营平台-统一门户工程\\dopUnify\\node_modules\\eslint-loader\\index.js??ref--13-0!E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1\\数字运营平台-统一门户工程\\dopUnify\\src\\store\\modules\\flowPath.js", "dependencies": [{"path": "E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1\\数字运营平台-统一门户工程\\dopUnify\\src\\store\\modules\\flowPath.js", "mtime": 1716875180499}, {"path": "E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1\\数字运营平台-统一门户工程\\dopUnify\\babel.config.js", "mtime": 1716875191548}, {"path": "E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1\\数字运营平台-统一门户工程\\dopUnify\\.babelrc", "mtime": 1716875191502}, {"path": "E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1\\数字运营平台-统一门户工程\\dopUnify\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1\\数字运营平台-统一门户工程\\dopUnify\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1\\数字运营平台-统一门户工程\\dopUnify\\node_modules\\eslint-loader\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["state", "releaseTaskFlag", "Boolean", "releaseTaskId", "ismClassList", "ismDateList", "ismEmpList", "ismOrganList", "ismPostList", "menuCount", "menuExist", "pageName", "extendList", "mutations", "RELEASE_TASK_FLAG", "RELEASE_TASK_ID", "STORAGE_ISM_CLASS", "STORAGE_ISM_DATE", "STORAGE_ISM_EMP", "STORAGE_ISM_ORGAN", "STORAGE_ISM_POST", "GET_MENU_COUNT", "GET_MENU_EXIST", "SET_NAME", "names", "SET_EXTEND_LIST", "extendObj", "push", "DELECT_EXTEND_LIST", "title", "for<PERSON>ach", "item", "index", "splice", "actions", "releaseFlag", "commit", "releaseId", "namespaced"], "sources": ["E:/1_Project/XYD_Project/dop-4.0/dop-4.1/数字运营平台-统一门户工程/dopUnify/src/store/modules/flowPath.js"], "sourcesContent": ["/**\r\n * 流程相关\r\n */\r\nconst state = {\r\n  releaseTaskFlag: Boolean, // 任务释放标志  需要释放 true   不需要释放 false\r\n  releaseTaskId: '', // 释放任务的id\r\n  // 智能排班-排班方案管理 begin\r\n  ismClassList: {}, // 排班班次\r\n  ismDateList: {}, // 排班日期\r\n  ismEmpList: {}, // 排班人员\r\n  ismOrganList: {}, // 排班机构\r\n  ismPostList: [], // 排班岗位\r\n  // 智能排班-排班方案管理 end\r\n  // 菜单点击量存储 begin\r\n  menuCount: {}, // 点击的菜单次数配置\r\n  menuExist: [], // 存菜单名称\r\n  // 菜单点击量存储 end\r\n  // 合同管理  begin\r\n  pageName: '', // 维护/总价页面\r\n  extendList: [] // 流程页面信息\r\n  // 合同管理  begin\r\n}\r\n\r\nconst mutations = {\r\n  RELEASE_TASK_FLAG: (state, releaseTaskFlag) => {\r\n    state.releaseTaskFlag = releaseTaskFlag\r\n  },\r\n  RELEASE_TASK_ID: (state, releaseTaskId) => {\r\n    state.releaseTaskId = releaseTaskId\r\n  },\r\n  STORAGE_ISM_CLASS: (state, ismClassList) => {\r\n    state.ismClassList = ismClassList\r\n  },\r\n  STORAGE_ISM_DATE: (state, ismDateList) => {\r\n    state.ismDateList = ismDateList\r\n  },\r\n  STORAGE_ISM_EMP: (state, ismEmpList) => {\r\n    state.ismEmpList = ismEmpList\r\n  },\r\n  STORAGE_ISM_ORGAN: (state, ismOrganList) => {\r\n    state.ismOrganList = ismOrganList\r\n  },\r\n  STORAGE_ISM_POST: (state, ismPostList) => {\r\n    state.ismPostList = ismPostList\r\n  },\r\n  GET_MENU_COUNT: (state, menuCount) => {\r\n    state.menuCount = menuCount\r\n  },\r\n  GET_MENU_EXIST: (state, menuExist) => {\r\n    state.menuExist = menuExist\r\n  },\r\n  SET_NAME: (state, names) => {\r\n    state.pageName = names\r\n  },\r\n  SET_EXTEND_LIST: (state, extendObj) => {\r\n    state.extendList.push(extendObj)\r\n  },\r\n  DELECT_EXTEND_LIST: (state, title) => {\r\n    state.extendList.forEach((item, index) => {\r\n      if (item.title === title) {\r\n        state.extendList.splice(index, 1)\r\n      }\r\n    })\r\n  }\r\n}\r\n\r\nconst actions = {\r\n  releaseFlag({ commit }, releaseTaskFlag) {\r\n    commit('RELEASE_TASK_FLAG', releaseTaskFlag)\r\n  },\r\n  releaseId({ commit }, releaseTaskId) {\r\n    commit('RELEASE_TASK_ID', releaseTaskId)\r\n  }\r\n}\r\n\r\nexport default {\r\n  namespaced: true,\r\n  state,\r\n  mutations,\r\n  actions\r\n}\r\n"], "mappings": ";;;AAAA;AACA;AACA;AACA,IAAMA,KAAK,GAAG;EACZC,eAAe,EAAEC,OAAO;EAAE;EAC1BC,aAAa,EAAE,EAAE;EAAE;EACnB;EACAC,YAAY,EAAE,CAAC,CAAC;EAAE;EAClBC,WAAW,EAAE,CAAC,CAAC;EAAE;EACjBC,UAAU,EAAE,CAAC,CAAC;EAAE;EAChBC,YAAY,EAAE,CAAC,CAAC;EAAE;EAClBC,WAAW,EAAE,EAAE;EAAE;EACjB;EACA;EACAC,SAAS,EAAE,CAAC,CAAC;EAAE;EACfC,SAAS,EAAE,EAAE;EAAE;EACf;EACA;EACAC,QAAQ,EAAE,EAAE;EAAE;EACdC,UAAU,EAAE,EAAE,CAAC;EACf;AACF,CAAC;;AAED,IAAMC,SAAS,GAAG;EAChBC,iBAAiB,EAAE,2BAACd,KAAK,EAAEC,eAAe,EAAK;IAC7CD,KAAK,CAACC,eAAe,GAAGA,eAAe;EACzC,CAAC;EACDc,eAAe,EAAE,yBAACf,KAAK,EAAEG,aAAa,EAAK;IACzCH,KAAK,CAACG,aAAa,GAAGA,aAAa;EACrC,CAAC;EACDa,iBAAiB,EAAE,2BAAChB,KAAK,EAAEI,YAAY,EAAK;IAC1CJ,KAAK,CAACI,YAAY,GAAGA,YAAY;EACnC,CAAC;EACDa,gBAAgB,EAAE,0BAACjB,KAAK,EAAEK,WAAW,EAAK;IACxCL,KAAK,CAACK,WAAW,GAAGA,WAAW;EACjC,CAAC;EACDa,eAAe,EAAE,yBAAClB,KAAK,EAAEM,UAAU,EAAK;IACtCN,KAAK,CAACM,UAAU,GAAGA,UAAU;EAC/B,CAAC;EACDa,iBAAiB,EAAE,2BAACnB,KAAK,EAAEO,YAAY,EAAK;IAC1CP,KAAK,CAACO,YAAY,GAAGA,YAAY;EACnC,CAAC;EACDa,gBAAgB,EAAE,0BAACpB,KAAK,EAAEQ,WAAW,EAAK;IACxCR,KAAK,CAACQ,WAAW,GAAGA,WAAW;EACjC,CAAC;EACDa,cAAc,EAAE,wBAACrB,KAAK,EAAES,SAAS,EAAK;IACpCT,KAAK,CAACS,SAAS,GAAGA,SAAS;EAC7B,CAAC;EACDa,cAAc,EAAE,wBAACtB,KAAK,EAAEU,SAAS,EAAK;IACpCV,KAAK,CAACU,SAAS,GAAGA,SAAS;EAC7B,CAAC;EACDa,QAAQ,EAAE,kBAACvB,KAAK,EAAEwB,KAAK,EAAK;IAC1BxB,KAAK,CAACW,QAAQ,GAAGa,KAAK;EACxB,CAAC;EACDC,eAAe,EAAE,yBAACzB,KAAK,EAAE0B,SAAS,EAAK;IACrC1B,KAAK,CAACY,UAAU,CAACe,IAAI,CAACD,SAAS,CAAC;EAClC,CAAC;EACDE,kBAAkB,EAAE,4BAAC5B,KAAK,EAAE6B,KAAK,EAAK;IACpC7B,KAAK,CAACY,UAAU,CAACkB,OAAO,CAAC,UAACC,IAAI,EAAEC,KAAK,EAAK;MACxC,IAAID,IAAI,CAACF,KAAK,KAAKA,KAAK,EAAE;QACxB7B,KAAK,CAACY,UAAU,CAACqB,MAAM,CAACD,KAAK,EAAE,CAAC,CAAC;MACnC;IACF,CAAC,CAAC;EACJ;AACF,CAAC;AAED,IAAME,OAAO,GAAG;EACdC,WAAW,6BAAalC,eAAe,EAAE;IAAA,IAA3BmC,MAAM,QAANA,MAAM;IAClBA,MAAM,CAAC,mBAAmB,EAAEnC,eAAe,CAAC;EAC9C,CAAC;EACDoC,SAAS,4BAAalC,aAAa,EAAE;IAAA,IAAzBiC,MAAM,SAANA,MAAM;IAChBA,MAAM,CAAC,iBAAiB,EAAEjC,aAAa,CAAC;EAC1C;AACF,CAAC;AAED,eAAe;EACbmC,UAAU,EAAE,IAAI;EAChBtC,KAAK,EAALA,KAAK;EACLa,SAAS,EAATA,SAAS;EACTqB,OAAO,EAAPA;AACF,CAAC"}]}