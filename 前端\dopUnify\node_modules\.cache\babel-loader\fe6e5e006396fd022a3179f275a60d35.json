{"remainingRequest": "E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1\\数字运营平台-统一门户工程\\dopUnify\\node_modules\\babel-loader\\lib\\index.js!E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1\\数字运营平台-统一门户工程\\dopUnify\\node_modules\\qiankun\\es\\sandbox\\patchers\\css.js", "dependencies": [{"path": "E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1\\数字运营平台-统一门户工程\\dopUnify\\node_modules\\qiankun\\es\\sandbox\\patchers\\css.js", "mtime": 1667130453000}, {"path": "E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1\\数字运营平台-统一门户工程\\dopUnify\\babel.config.js", "mtime": 1716875191548}, {"path": "E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1\\数字运营平台-统一门户工程\\dopUnify\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1\\数字运营平台-统一门户工程\\dopUnify\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["_classCallCheck", "_createClass", "RuleType", "arrayify", "list", "slice", "call", "rawDocumentBodyAppend", "HTMLBodyElement", "prototype", "append<PERSON><PERSON><PERSON>", "ScopedCSS", "sheet", "swapNode", "styleNode", "document", "createElement", "body", "disabled", "key", "value", "process", "_this", "prefix", "arguments", "length", "undefined", "ModifiedTag", "textContent", "_sheet$cssRules", "textNode", "createTextNode", "rules", "cssRules", "css", "rewrite", "<PERSON><PERSON><PERSON><PERSON>", "mutator", "MutationObserver", "mutations", "i", "mutation", "type", "_sheet$cssRules2", "_sheet", "_rules", "_css", "observe", "childList", "_this2", "for<PERSON>ach", "rule", "STYLE", "ruleStyle", "MEDIA", "ruleMedia", "SUPPORTS", "ruleSupport", "concat", "cssText", "rootSelectorRE", "rootCombinationRE", "selector", "selectorText", "trim", "replace", "test", "siblingSelectorRE", "selectors", "item", "p", "s", "m", "whitePrevChars", "includes", "conditionText", "media", "mediaText", "split", "processor", "QiankunCSSRewriteAttr", "appWrapper", "stylesheetElement", "appName", "tagName", "console", "warn", "mountDOM", "tag", "toLowerCase"], "sources": ["E:/1_Project/XYD_Project/dop-4.0/dop-4.1/数字运营平台-统一门户工程/dopUnify/node_modules/qiankun/es/sandbox/patchers/css.js"], "sourcesContent": ["import _classCallCheck from \"@babel/runtime/helpers/esm/classCallCheck\";\nimport _createClass from \"@babel/runtime/helpers/esm/createClass\";\n/**\n * <AUTHOR>\n * @since 2020-4-19\n */\n// https://developer.mozilla.org/en-US/docs/Web/API/CSSRule\nvar RuleType;\n(function (RuleType) {\n  // type: rule will be rewrote\n  RuleType[RuleType[\"STYLE\"] = 1] = \"STYLE\";\n  RuleType[RuleType[\"MEDIA\"] = 4] = \"MEDIA\";\n  RuleType[RuleType[\"SUPPORTS\"] = 12] = \"SUPPORTS\";\n  // type: value will be kept\n  RuleType[RuleType[\"IMPORT\"] = 3] = \"IMPORT\";\n  RuleType[RuleType[\"FONT_FACE\"] = 5] = \"FONT_FACE\";\n  RuleType[RuleType[\"PAGE\"] = 6] = \"PAGE\";\n  RuleType[RuleType[\"KEYFRAMES\"] = 7] = \"KEYFRAMES\";\n  RuleType[RuleType[\"KEYFRAME\"] = 8] = \"KEYFRAME\";\n})(RuleType || (RuleType = {}));\nvar arrayify = function arrayify(list) {\n  return [].slice.call(list, 0);\n};\nvar rawDocumentBodyAppend = HTMLBodyElement.prototype.appendChild;\nexport var ScopedCSS = /*#__PURE__*/function () {\n  function ScopedCSS() {\n    _classCallCheck(this, ScopedCSS);\n    this.sheet = void 0;\n    this.swapNode = void 0;\n    var styleNode = document.createElement('style');\n    rawDocumentBodyAppend.call(document.body, styleNode);\n    this.swapNode = styleNode;\n    this.sheet = styleNode.sheet;\n    this.sheet.disabled = true;\n  }\n  _createClass(ScopedCSS, [{\n    key: \"process\",\n    value: function process(styleNode) {\n      var _this = this;\n      var prefix = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : '';\n      if (ScopedCSS.ModifiedTag in styleNode) {\n        return;\n      }\n      if (styleNode.textContent !== '') {\n        var _sheet$cssRules;\n        var textNode = document.createTextNode(styleNode.textContent || '');\n        this.swapNode.appendChild(textNode);\n        var sheet = this.swapNode.sheet; // type is missing\n        var rules = arrayify((_sheet$cssRules = sheet === null || sheet === void 0 ? void 0 : sheet.cssRules) !== null && _sheet$cssRules !== void 0 ? _sheet$cssRules : []);\n        var css = this.rewrite(rules, prefix);\n        // eslint-disable-next-line no-param-reassign\n        styleNode.textContent = css;\n        // cleanup\n        this.swapNode.removeChild(textNode);\n        styleNode[ScopedCSS.ModifiedTag] = true;\n        return;\n      }\n      var mutator = new MutationObserver(function (mutations) {\n        for (var i = 0; i < mutations.length; i += 1) {\n          var mutation = mutations[i];\n          if (ScopedCSS.ModifiedTag in styleNode) {\n            return;\n          }\n          if (mutation.type === 'childList') {\n            var _sheet$cssRules2;\n            var _sheet = styleNode.sheet;\n            var _rules = arrayify((_sheet$cssRules2 = _sheet === null || _sheet === void 0 ? void 0 : _sheet.cssRules) !== null && _sheet$cssRules2 !== void 0 ? _sheet$cssRules2 : []);\n            var _css = _this.rewrite(_rules, prefix);\n            // eslint-disable-next-line no-param-reassign\n            styleNode.textContent = _css;\n            // eslint-disable-next-line no-param-reassign\n            styleNode[ScopedCSS.ModifiedTag] = true;\n          }\n        }\n      });\n      // since observer will be deleted when node be removed\n      // we dont need create a cleanup function manually\n      // see https://developer.mozilla.org/en-US/docs/Web/API/MutationObserver/disconnect\n      mutator.observe(styleNode, {\n        childList: true\n      });\n    }\n  }, {\n    key: \"rewrite\",\n    value: function rewrite(rules) {\n      var _this2 = this;\n      var prefix = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : '';\n      var css = '';\n      rules.forEach(function (rule) {\n        switch (rule.type) {\n          case RuleType.STYLE:\n            css += _this2.ruleStyle(rule, prefix);\n            break;\n          case RuleType.MEDIA:\n            css += _this2.ruleMedia(rule, prefix);\n            break;\n          case RuleType.SUPPORTS:\n            css += _this2.ruleSupport(rule, prefix);\n            break;\n          default:\n            css += \"\".concat(rule.cssText);\n            break;\n        }\n      });\n      return css;\n    }\n    // handle case:\n    // .app-main {}\n    // html, body {}\n    // eslint-disable-next-line class-methods-use-this\n  }, {\n    key: \"ruleStyle\",\n    value: function ruleStyle(rule, prefix) {\n      var rootSelectorRE = /((?:[^\\w\\-.#]|^)(body|html|:root))/gm;\n      var rootCombinationRE = /(html[^\\w{[]+)/gm;\n      var selector = rule.selectorText.trim();\n      var cssText = rule.cssText;\n      // handle html { ... }\n      // handle body { ... }\n      // handle :root { ... }\n      if (selector === 'html' || selector === 'body' || selector === ':root') {\n        return cssText.replace(rootSelectorRE, prefix);\n      }\n      // handle html body { ... }\n      // handle html > body { ... }\n      if (rootCombinationRE.test(rule.selectorText)) {\n        var siblingSelectorRE = /(html[^\\w{]+)(\\+|~)/gm;\n        // since html + body is a non-standard rule for html\n        // transformer will ignore it\n        if (!siblingSelectorRE.test(rule.selectorText)) {\n          cssText = cssText.replace(rootCombinationRE, '');\n        }\n      }\n      // handle grouping selector, a,span,p,div { ... }\n      cssText = cssText.replace(/^[\\s\\S]+{/, function (selectors) {\n        return selectors.replace(/(^|,\\n?)([^,]+)/g, function (item, p, s) {\n          // handle div,body,span { ... }\n          if (rootSelectorRE.test(item)) {\n            return item.replace(rootSelectorRE, function (m) {\n              // do not discard valid previous character, such as body,html or *:not(:root)\n              var whitePrevChars = [',', '('];\n              if (m && whitePrevChars.includes(m[0])) {\n                return \"\".concat(m[0]).concat(prefix);\n              }\n              // replace root selector with prefix\n              return prefix;\n            });\n          }\n          return \"\".concat(p).concat(prefix, \" \").concat(s.replace(/^ */, ''));\n        });\n      });\n      return cssText;\n    }\n    // handle case:\n    // @media screen and (max-width: 300px) {}\n  }, {\n    key: \"ruleMedia\",\n    value: function ruleMedia(rule, prefix) {\n      var css = this.rewrite(arrayify(rule.cssRules), prefix);\n      return \"@media \".concat(rule.conditionText || rule.media.mediaText, \" {\").concat(css, \"}\");\n    }\n    // handle case:\n    // @supports (display: grid) {}\n  }, {\n    key: \"ruleSupport\",\n    value: function ruleSupport(rule, prefix) {\n      var css = this.rewrite(arrayify(rule.cssRules), prefix);\n      return \"@supports \".concat(rule.conditionText || rule.cssText.split('{')[0], \" {\").concat(css, \"}\");\n    }\n  }]);\n  return ScopedCSS;\n}();\nScopedCSS.ModifiedTag = 'Symbol(style-modified-qiankun)';\nvar processor;\nexport var QiankunCSSRewriteAttr = 'data-qiankun';\nexport var process = function process(appWrapper, stylesheetElement, appName) {\n  // lazy singleton pattern\n  if (!processor) {\n    processor = new ScopedCSS();\n  }\n  if (stylesheetElement.tagName === 'LINK') {\n    console.warn('Feature: sandbox.experimentalStyleIsolation is not support for link element yet.');\n  }\n  var mountDOM = appWrapper;\n  if (!mountDOM) {\n    return;\n  }\n  var tag = (mountDOM.tagName || '').toLowerCase();\n  if (tag && stylesheetElement.tagName === 'STYLE') {\n    var prefix = \"\".concat(tag, \"[\").concat(QiankunCSSRewriteAttr, \"=\\\"\").concat(appName, \"\\\"]\");\n    processor.process(stylesheetElement, prefix);\n  }\n};"], "mappings": ";;;;;;;;;AAAA,OAAOA,eAAe,MAAM,2CAA2C;AACvE,OAAOC,YAAY,MAAM,wCAAwC;AACjE;AACA;AACA;AACA;AACA;AACA,IAAIC,QAAQ;AACZ,CAAC,UAAUA,QAAQ,EAAE;EACnB;EACAA,QAAQ,CAACA,QAAQ,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC,GAAG,OAAO;EACzCA,QAAQ,CAACA,QAAQ,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC,GAAG,OAAO;EACzCA,QAAQ,CAACA,QAAQ,CAAC,UAAU,CAAC,GAAG,EAAE,CAAC,GAAG,UAAU;EAChD;EACAA,QAAQ,CAACA,QAAQ,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC,GAAG,QAAQ;EAC3CA,QAAQ,CAACA,QAAQ,CAAC,WAAW,CAAC,GAAG,CAAC,CAAC,GAAG,WAAW;EACjDA,QAAQ,CAACA,QAAQ,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,GAAG,MAAM;EACvCA,QAAQ,CAACA,QAAQ,CAAC,WAAW,CAAC,GAAG,CAAC,CAAC,GAAG,WAAW;EACjDA,QAAQ,CAACA,QAAQ,CAAC,UAAU,CAAC,GAAG,CAAC,CAAC,GAAG,UAAU;AACjD,CAAC,EAAEA,QAAQ,KAAKA,QAAQ,GAAG,CAAC,CAAC,CAAC,CAAC;AAC/B,IAAIC,QAAQ,GAAG,SAASA,QAAQ,CAACC,IAAI,EAAE;EACrC,OAAO,EAAE,CAACC,KAAK,CAACC,IAAI,CAACF,IAAI,EAAE,CAAC,CAAC;AAC/B,CAAC;AACD,IAAIG,qBAAqB,GAAGC,eAAe,CAACC,SAAS,CAACC,WAAW;AACjE,OAAO,IAAIC,SAAS,GAAG,aAAa,YAAY;EAC9C,SAASA,SAAS,GAAG;IACnBX,eAAe,CAAC,IAAI,EAAEW,SAAS,CAAC;IAChC,IAAI,CAACC,KAAK,GAAG,KAAK,CAAC;IACnB,IAAI,CAACC,QAAQ,GAAG,KAAK,CAAC;IACtB,IAAIC,SAAS,GAAGC,QAAQ,CAACC,aAAa,CAAC,OAAO,CAAC;IAC/CT,qBAAqB,CAACD,IAAI,CAACS,QAAQ,CAACE,IAAI,EAAEH,SAAS,CAAC;IACpD,IAAI,CAACD,QAAQ,GAAGC,SAAS;IACzB,IAAI,CAACF,KAAK,GAAGE,SAAS,CAACF,KAAK;IAC5B,IAAI,CAACA,KAAK,CAACM,QAAQ,GAAG,IAAI;EAC5B;EACAjB,YAAY,CAACU,SAAS,EAAE,CAAC;IACvBQ,GAAG,EAAE,SAAS;IACdC,KAAK,EAAE,SAASC,OAAO,CAACP,SAAS,EAAE;MACjC,IAAIQ,KAAK,GAAG,IAAI;MAChB,IAAIC,MAAM,GAAGC,SAAS,CAACC,MAAM,GAAG,CAAC,IAAID,SAAS,CAAC,CAAC,CAAC,KAAKE,SAAS,GAAGF,SAAS,CAAC,CAAC,CAAC,GAAG,EAAE;MACnF,IAAIb,SAAS,CAACgB,WAAW,IAAIb,SAAS,EAAE;QACtC;MACF;MACA,IAAIA,SAAS,CAACc,WAAW,KAAK,EAAE,EAAE;QAChC,IAAIC,eAAe;QACnB,IAAIC,QAAQ,GAAGf,QAAQ,CAACgB,cAAc,CAACjB,SAAS,CAACc,WAAW,IAAI,EAAE,CAAC;QACnE,IAAI,CAACf,QAAQ,CAACH,WAAW,CAACoB,QAAQ,CAAC;QACnC,IAAIlB,KAAK,GAAG,IAAI,CAACC,QAAQ,CAACD,KAAK,CAAC,CAAC;QACjC,IAAIoB,KAAK,GAAG7B,QAAQ,CAAC,CAAC0B,eAAe,GAAGjB,KAAK,KAAK,IAAI,IAAIA,KAAK,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,KAAK,CAACqB,QAAQ,MAAM,IAAI,IAAIJ,eAAe,KAAK,KAAK,CAAC,GAAGA,eAAe,GAAG,EAAE,CAAC;QACpK,IAAIK,GAAG,GAAG,IAAI,CAACC,OAAO,CAACH,KAAK,EAAET,MAAM,CAAC;QACrC;QACAT,SAAS,CAACc,WAAW,GAAGM,GAAG;QAC3B;QACA,IAAI,CAACrB,QAAQ,CAACuB,WAAW,CAACN,QAAQ,CAAC;QACnChB,SAAS,CAACH,SAAS,CAACgB,WAAW,CAAC,GAAG,IAAI;QACvC;MACF;MACA,IAAIU,OAAO,GAAG,IAAIC,gBAAgB,CAAC,UAAUC,SAAS,EAAE;QACtD,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGD,SAAS,CAACd,MAAM,EAAEe,CAAC,IAAI,CAAC,EAAE;UAC5C,IAAIC,QAAQ,GAAGF,SAAS,CAACC,CAAC,CAAC;UAC3B,IAAI7B,SAAS,CAACgB,WAAW,IAAIb,SAAS,EAAE;YACtC;UACF;UACA,IAAI2B,QAAQ,CAACC,IAAI,KAAK,WAAW,EAAE;YACjC,IAAIC,gBAAgB;YACpB,IAAIC,MAAM,GAAG9B,SAAS,CAACF,KAAK;YAC5B,IAAIiC,MAAM,GAAG1C,QAAQ,CAAC,CAACwC,gBAAgB,GAAGC,MAAM,KAAK,IAAI,IAAIA,MAAM,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,MAAM,CAACX,QAAQ,MAAM,IAAI,IAAIU,gBAAgB,KAAK,KAAK,CAAC,GAAGA,gBAAgB,GAAG,EAAE,CAAC;YAC3K,IAAIG,IAAI,GAAGxB,KAAK,CAACa,OAAO,CAACU,MAAM,EAAEtB,MAAM,CAAC;YACxC;YACAT,SAAS,CAACc,WAAW,GAAGkB,IAAI;YAC5B;YACAhC,SAAS,CAACH,SAAS,CAACgB,WAAW,CAAC,GAAG,IAAI;UACzC;QACF;MACF,CAAC,CAAC;MACF;MACA;MACA;MACAU,OAAO,CAACU,OAAO,CAACjC,SAAS,EAAE;QACzBkC,SAAS,EAAE;MACb,CAAC,CAAC;IACJ;EACF,CAAC,EAAE;IACD7B,GAAG,EAAE,SAAS;IACdC,KAAK,EAAE,SAASe,OAAO,CAACH,KAAK,EAAE;MAC7B,IAAIiB,MAAM,GAAG,IAAI;MACjB,IAAI1B,MAAM,GAAGC,SAAS,CAACC,MAAM,GAAG,CAAC,IAAID,SAAS,CAAC,CAAC,CAAC,KAAKE,SAAS,GAAGF,SAAS,CAAC,CAAC,CAAC,GAAG,EAAE;MACnF,IAAIU,GAAG,GAAG,EAAE;MACZF,KAAK,CAACkB,OAAO,CAAC,UAAUC,IAAI,EAAE;QAC5B,QAAQA,IAAI,CAACT,IAAI;UACf,KAAKxC,QAAQ,CAACkD,KAAK;YACjBlB,GAAG,IAAIe,MAAM,CAACI,SAAS,CAACF,IAAI,EAAE5B,MAAM,CAAC;YACrC;UACF,KAAKrB,QAAQ,CAACoD,KAAK;YACjBpB,GAAG,IAAIe,MAAM,CAACM,SAAS,CAACJ,IAAI,EAAE5B,MAAM,CAAC;YACrC;UACF,KAAKrB,QAAQ,CAACsD,QAAQ;YACpBtB,GAAG,IAAIe,MAAM,CAACQ,WAAW,CAACN,IAAI,EAAE5B,MAAM,CAAC;YACvC;UACF;YACEW,GAAG,IAAI,EAAE,CAACwB,MAAM,CAACP,IAAI,CAACQ,OAAO,CAAC;YAC9B;QAAM;MAEZ,CAAC,CAAC;MACF,OAAOzB,GAAG;IACZ;IACA;IACA;IACA;IACA;EACF,CAAC,EAAE;IACDf,GAAG,EAAE,WAAW;IAChBC,KAAK,EAAE,SAASiC,SAAS,CAACF,IAAI,EAAE5B,MAAM,EAAE;MACtC,IAAIqC,cAAc,GAAG,sCAAsC;MAC3D,IAAIC,iBAAiB,GAAG,kBAAkB;MAC1C,IAAIC,QAAQ,GAAGX,IAAI,CAACY,YAAY,CAACC,IAAI,EAAE;MACvC,IAAIL,OAAO,GAAGR,IAAI,CAACQ,OAAO;MAC1B;MACA;MACA;MACA,IAAIG,QAAQ,KAAK,MAAM,IAAIA,QAAQ,KAAK,MAAM,IAAIA,QAAQ,KAAK,OAAO,EAAE;QACtE,OAAOH,OAAO,CAACM,OAAO,CAACL,cAAc,EAAErC,MAAM,CAAC;MAChD;MACA;MACA;MACA,IAAIsC,iBAAiB,CAACK,IAAI,CAACf,IAAI,CAACY,YAAY,CAAC,EAAE;QAC7C,IAAII,iBAAiB,GAAG,uBAAuB;QAC/C;QACA;QACA,IAAI,CAACA,iBAAiB,CAACD,IAAI,CAACf,IAAI,CAACY,YAAY,CAAC,EAAE;UAC9CJ,OAAO,GAAGA,OAAO,CAACM,OAAO,CAACJ,iBAAiB,EAAE,EAAE,CAAC;QAClD;MACF;MACA;MACAF,OAAO,GAAGA,OAAO,CAACM,OAAO,CAAC,WAAW,EAAE,UAAUG,SAAS,EAAE;QAC1D,OAAOA,SAAS,CAACH,OAAO,CAAC,kBAAkB,EAAE,UAAUI,IAAI,EAAEC,CAAC,EAAEC,CAAC,EAAE;UACjE;UACA,IAAIX,cAAc,CAACM,IAAI,CAACG,IAAI,CAAC,EAAE;YAC7B,OAAOA,IAAI,CAACJ,OAAO,CAACL,cAAc,EAAE,UAAUY,CAAC,EAAE;cAC/C;cACA,IAAIC,cAAc,GAAG,CAAC,GAAG,EAAE,GAAG,CAAC;cAC/B,IAAID,CAAC,IAAIC,cAAc,CAACC,QAAQ,CAACF,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;gBACtC,OAAO,EAAE,CAACd,MAAM,CAACc,CAAC,CAAC,CAAC,CAAC,CAAC,CAACd,MAAM,CAACnC,MAAM,CAAC;cACvC;cACA;cACA,OAAOA,MAAM;YACf,CAAC,CAAC;UACJ;UACA,OAAO,EAAE,CAACmC,MAAM,CAACY,CAAC,CAAC,CAACZ,MAAM,CAACnC,MAAM,EAAE,GAAG,CAAC,CAACmC,MAAM,CAACa,CAAC,CAACN,OAAO,CAAC,KAAK,EAAE,EAAE,CAAC,CAAC;QACtE,CAAC,CAAC;MACJ,CAAC,CAAC;MACF,OAAON,OAAO;IAChB;IACA;IACA;EACF,CAAC,EAAE;IACDxC,GAAG,EAAE,WAAW;IAChBC,KAAK,EAAE,SAASmC,SAAS,CAACJ,IAAI,EAAE5B,MAAM,EAAE;MACtC,IAAIW,GAAG,GAAG,IAAI,CAACC,OAAO,CAAChC,QAAQ,CAACgD,IAAI,CAAClB,QAAQ,CAAC,EAAEV,MAAM,CAAC;MACvD,OAAO,SAAS,CAACmC,MAAM,CAACP,IAAI,CAACwB,aAAa,IAAIxB,IAAI,CAACyB,KAAK,CAACC,SAAS,EAAE,IAAI,CAAC,CAACnB,MAAM,CAACxB,GAAG,EAAE,GAAG,CAAC;IAC5F;IACA;IACA;EACF,CAAC,EAAE;IACDf,GAAG,EAAE,aAAa;IAClBC,KAAK,EAAE,SAASqC,WAAW,CAACN,IAAI,EAAE5B,MAAM,EAAE;MACxC,IAAIW,GAAG,GAAG,IAAI,CAACC,OAAO,CAAChC,QAAQ,CAACgD,IAAI,CAAClB,QAAQ,CAAC,EAAEV,MAAM,CAAC;MACvD,OAAO,YAAY,CAACmC,MAAM,CAACP,IAAI,CAACwB,aAAa,IAAIxB,IAAI,CAACQ,OAAO,CAACmB,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,EAAE,IAAI,CAAC,CAACpB,MAAM,CAACxB,GAAG,EAAE,GAAG,CAAC;IACrG;EACF,CAAC,CAAC,CAAC;EACH,OAAOvB,SAAS;AAClB,CAAC,EAAE;AACHA,SAAS,CAACgB,WAAW,GAAG,gCAAgC;AACxD,IAAIoD,SAAS;AACb,OAAO,IAAIC,qBAAqB,GAAG,cAAc;AACjD,OAAO,IAAI3D,OAAO,GAAG,SAASA,OAAO,CAAC4D,UAAU,EAAEC,iBAAiB,EAAEC,OAAO,EAAE;EAC5E;EACA,IAAI,CAACJ,SAAS,EAAE;IACdA,SAAS,GAAG,IAAIpE,SAAS,EAAE;EAC7B;EACA,IAAIuE,iBAAiB,CAACE,OAAO,KAAK,MAAM,EAAE;IACxCC,OAAO,CAACC,IAAI,CAAC,kFAAkF,CAAC;EAClG;EACA,IAAIC,QAAQ,GAAGN,UAAU;EACzB,IAAI,CAACM,QAAQ,EAAE;IACb;EACF;EACA,IAAIC,GAAG,GAAG,CAACD,QAAQ,CAACH,OAAO,IAAI,EAAE,EAAEK,WAAW,EAAE;EAChD,IAAID,GAAG,IAAIN,iBAAiB,CAACE,OAAO,KAAK,OAAO,EAAE;IAChD,IAAI7D,MAAM,GAAG,EAAE,CAACmC,MAAM,CAAC8B,GAAG,EAAE,GAAG,CAAC,CAAC9B,MAAM,CAACsB,qBAAqB,EAAE,KAAK,CAAC,CAACtB,MAAM,CAACyB,OAAO,EAAE,KAAK,CAAC;IAC5FJ,SAAS,CAAC1D,OAAO,CAAC6D,iBAAiB,EAAE3D,MAAM,CAAC;EAC9C;AACF,CAAC"}]}