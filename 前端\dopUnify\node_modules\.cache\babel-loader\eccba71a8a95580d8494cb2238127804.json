{"remainingRequest": "E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\node_modules\\babel-loader\\lib\\index.js!E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\src\\components\\Layout\\ThemePicker\\index.vue?vue&type=script&lang=js&", "dependencies": [{"path": "E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\src\\components\\Layout\\ThemePicker\\index.vue", "mtime": 1686019810044}, {"path": "E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\babel.config.js", "mtime": 1686019818359}, {"path": "E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\.babelrc", "mtime": 1705285078668}, {"path": "E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1655715099000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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<PERSON>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"}, {"version": 3, "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;AAUA;AACA;AACA;;AAEA;EACAA;IACA;MACAC;MAAA;MACAC;IACA;EACA;EACAC;IACAC;MACA;IACA;EACA;EACAC;IACAD;MACAE;QACA;MACA;MACAC;IACA;IACAL;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBACAM;gBAAA,MACA;kBAAA;kBAAA;gBAAA;gBAAA;cAAA;gBACAC;gBACAC,kEACA;gBAEAC;kBACAC;kBACAC;kBACAC;kBACAC;kBACAC;gBACA;gBAEAC;kBACA;oBACA;oBACA;oBAEA;oBACA;sBACAC;sBACAA;sBACAC;oBACA;oBACAD;kBACA;gBACA,GAEA;gBACA;gBACA;gBACA;gBACA;gBAAA,CACAE;gBAEAC;gBAEAA;gBAEAC,2DACAC;kBACA;kBACA;gBACA;gBACAD;kBACA;kBACA;kBACAE;gBACA;gBAEA;gBAEAb;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IACA;EACA;EACAc;IAAA;IACA;MACA;IACA;EACA;EACAC;IACAC;MACA;MACAC;QACAC;MACA;MACA;IACA;IAEAC;MAAA;MACA;QACA;QACAC;UACA;YACA;YACAC;UACA;QACA;QACAD;QACAA;MACA;IACA;IAEAE;MACA;QACA;QACA;QACA;QAEA;UAAA;UACA;QACA;UACAC;UACAC;UACAC;UAEAF;UACAC;UACAC;UAEA;QACA;MACA;MAEA;QACA;QACA;QACA;QAEAF;QACAC;QACAC;QAEAF;QACAC;QACAC;QAEA;MACA;MAEA;MACA;QACAC;MACA;MACAA;MACA;IACA;EACA;AACA", "names": ["data", "chalk", "theme", "computed", "defaultTheme", "watch", "handler", "immediate", "oldVal", "themeCluster", "originalCluster", "$message", "message", "customClass", "type", "duration", "iconClass", "<PERSON><PERSON><PERSON><PERSON>", "styleTag", "document", "replace", "<PERSON><PERSON><PERSON><PERSON>", "styles", "filter", "style", "mounted", "methods", "updateStyle", "oldCluster", "newStyle", "getCSSString", "xhr", "resolve", "getThemeCluster", "red", "green", "blue", "clusters"], "sourceRoot": "src/components/Layout/ThemePicker", "sources": ["index.vue"], "sourcesContent": ["<template>\n  <el-color-picker\n    v-model=\"theme\"\n    :predefine=\"['#409EFF', '#1890ff', '#304156','#212121','#11a983', '#13c2c2', '#6959CD', '#f5222d', ]\"\n    class=\"theme-picker\"\n    popper-class=\"theme-picker-dropdown\"\n  />\n</template>\n\n<script>\nimport ElementUiCSS from './ElementUiCSS.js' // 就是上文说的默认样式 index.css; (字符串)\n// const version = require('element-ui/package.json').version // element-ui version from node_modules\nconst ORIGINAL_THEME = '#409EFF' // default color\n\nexport default {\n  data() {\n    return {\n      chalk: '', // content of theme-chalk css\n      theme: ''\n    }\n  },\n  computed: {\n    defaultTheme() {\n      return this.$store.state.settings.theme\n    }\n  },\n  watch: {\n    defaultTheme: {\n      handler: function(val, oldVal) {\n        this.theme = val\n      },\n      immediate: true\n    },\n    async theme(val) {\n      const oldVal = this.chalk ? this.theme : this.$store.state.settings.theme\n      if (typeof val !== 'string') return\n      const themeCluster = this.getThemeCluster(val.replace('#', ''))\n      const originalCluster = this.getThemeCluster(oldVal.replace('#', ''))\n      // console.log(themeCluster, originalCluster)\n\n      const $message = this.$message({\n        message: '  Compiling the theme',\n        customClass: 'theme-message',\n        type: 'success',\n        duration: 0,\n        iconClass: 'el-icon-loading'\n      })\n\n      const getHandler = (variable, id) => {\n        return () => {\n          const originalCluster = this.getThemeCluster(ORIGINAL_THEME.replace('#', ''))\n          const newStyle = this.updateStyle(this[variable], originalCluster, themeCluster)\n\n          let styleTag = document.getElementById(id)\n          if (!styleTag) {\n            styleTag = document.createElement('style')\n            styleTag.setAttribute('id', id)\n            document.head.appendChild(styleTag)\n          }\n          styleTag.innerText = newStyle\n        }\n      }\n\n      // if (!this.chalk) {\n      //   const url = `https://unpkg.com/element-ui@${version}/lib/theme-chalk/index.css`\n      //   await this.getCSSString(url, 'chalk')\n      // }\n      this.chalk = ElementUiCSS.replace(/@font-face{[^}]+}/g, '') // 去掉字体样式\n        .replace(/.el-icon-[a-zA-Z0-9-:^]+before{content:\"[^}]+}/g, '') // 去掉图标样式\n\n      const chalkHandler = getHandler('chalk', 'chalk-style')\n\n      chalkHandler()\n\n      const styles = [].slice.call(document.querySelectorAll('style'))\n        .filter(style => {\n          const text = style.innerText\n          return new RegExp(oldVal, 'i').test(text) && !/Chalk Variables/.test(text)\n        })\n      styles.forEach(style => {\n        const { innerText } = style\n        if (typeof innerText !== 'string') return\n        style.innerText = this.updateStyle(innerText, originalCluster, themeCluster)\n      })\n\n      this.$emit('change', val)\n\n      $message.close()\n    }\n  },\n  mounted() {\n    this.$nextTick(() => {\n      this.theme = this.$store.state.settings.theme\n    })\n  },\n  methods: {\n    updateStyle(style, oldCluster, newCluster) {\n      let newStyle = style\n      oldCluster.forEach((color, index) => {\n        newStyle = newStyle.replace(new RegExp(color, 'ig'), newCluster[index])\n      })\n      return newStyle\n    },\n\n    getCSSString(url, variable) {\n      return new Promise(resolve => {\n        const xhr = new XMLHttpRequest()\n        xhr.onreadystatechange = () => {\n          if (xhr.readyState === 4 && xhr.status === 200) {\n            this[variable] = xhr.responseText.replace(/@font-face{[^}]+}/, '')\n            resolve()\n          }\n        }\n        xhr.open('GET', url)\n        xhr.send()\n      })\n    },\n\n    getThemeCluster(theme) {\n      const tintColor = (color, tint) => {\n        let red = parseInt(color.slice(0, 2), 16)\n        let green = parseInt(color.slice(2, 4), 16)\n        let blue = parseInt(color.slice(4, 6), 16)\n\n        if (tint === 0) { // when primary color is in its rgb space\n          return [red, green, blue].join(',')\n        } else {\n          red += Math.round(tint * (255 - red))\n          green += Math.round(tint * (255 - green))\n          blue += Math.round(tint * (255 - blue))\n\n          red = red.toString(16)\n          green = green.toString(16)\n          blue = blue.toString(16)\n\n          return `#${red}${green}${blue}`\n        }\n      }\n\n      const shadeColor = (color, shade) => {\n        let red = parseInt(color.slice(0, 2), 16)\n        let green = parseInt(color.slice(2, 4), 16)\n        let blue = parseInt(color.slice(4, 6), 16)\n\n        red = Math.round((1 - shade) * red)\n        green = Math.round((1 - shade) * green)\n        blue = Math.round((1 - shade) * blue)\n\n        red = red.toString(16)\n        green = green.toString(16)\n        blue = blue.toString(16)\n\n        return `#${red}${green}${blue}`\n      }\n\n      const clusters = [theme]\n      for (let i = 0; i <= 9; i++) {\n        clusters.push(tintColor(theme, Number((i / 10).toFixed(2))))\n      }\n      clusters.push(shadeColor(theme, 0.1))\n      return clusters\n    }\n  }\n}\n</script>\n\n<style>\n.theme-message,\n.theme-picker-dropdown {\n  z-index: 99999 !important;\n}\n\n.theme-picker .el-color-picker__trigger {\n  height: 26px !important;\n  width: 26px !important;\n  padding: 2px;\n}\n\n.theme-picker-dropdown .el-color-dropdown__link-btn {\n  display: none;\n}\n</style>\n"]}]}