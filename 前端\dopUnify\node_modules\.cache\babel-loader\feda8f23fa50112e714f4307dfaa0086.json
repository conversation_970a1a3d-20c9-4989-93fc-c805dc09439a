{"remainingRequest": "E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1\\数字运营平台-统一门户工程\\dopUnify\\node_modules\\babel-loader\\lib\\index.js!E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1\\数字运营平台-统一门户工程\\dopUnify\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1\\数字运营平台-统一门户工程\\dopUnify\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1\\数字运营平台-统一门户工程\\dopUnify\\src\\views\\system\\user\\component\\table\\index.vue?vue&type=script&lang=js&", "dependencies": [{"path": "E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1\\数字运营平台-统一门户工程\\dopUnify\\src\\views\\system\\user\\component\\table\\index.vue", "mtime": 1718072531887}, {"path": "E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1\\数字运营平台-统一门户工程\\dopUnify\\babel.config.js", "mtime": 1716875191548}, {"path": "E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1\\数字运营平台-统一门户工程\\dopUnify\\.babelrc", "mtime": 1716875191502}, {"path": "E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1\\数字运营平台-统一门户工程\\dopUnify\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1\\数字运营平台-统一门户工程\\dopUnify\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1\\数字运营平台-统一门户工程\\dopUnify\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1\\数字运营平台-统一门户工程\\dopUnify\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1655715099000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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**************************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"}, null]}