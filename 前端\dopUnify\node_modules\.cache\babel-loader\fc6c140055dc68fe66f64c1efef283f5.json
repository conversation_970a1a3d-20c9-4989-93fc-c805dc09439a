{"remainingRequest": "D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\node_modules\\thread-loader\\dist\\cjs.js!D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\node_modules\\babel-loader\\lib\\index.js!D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\node_modules\\sunui\\src\\components\\Vendor\\Export2Excel.js", "dependencies": [{"path": "D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\node_modules\\sunui\\src\\components\\Vendor\\Export2Excel.js", "mtime": 499162500000}, {"path": "D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\babel.config.js", "mtime": 1686019818359}, {"path": "D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\node_modules\\thread-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, null]}