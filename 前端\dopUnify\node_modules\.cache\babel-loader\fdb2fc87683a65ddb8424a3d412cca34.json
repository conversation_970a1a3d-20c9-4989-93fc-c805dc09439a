{"remainingRequest": "E:\\华南区项目\\中国银行\\开发区\\科技公司运营平台\\前端\\dopUnify\\node_modules\\thread-loader\\dist\\cjs.js!E:\\华南区项目\\中国银行\\开发区\\科技公司运营平台\\前端\\dopUnify\\node_modules\\babel-loader\\lib\\index.js!E:\\华南区项目\\中国银行\\开发区\\科技公司运营平台\\前端\\dopUnify\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!E:\\华南区项目\\中国银行\\开发区\\科技公司运营平台\\前端\\dopUnify\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\华南区项目\\中国银行\\开发区\\科技公司运营平台\\前端\\dopUnify\\src\\views\\system\\config\\timingService\\component\\table\\index.vue?vue&type=script&lang=js&", "dependencies": [{"path": "E:\\华南区项目\\中国银行\\开发区\\科技公司运营平台\\前端\\dopUnify\\src\\views\\system\\config\\timingService\\component\\table\\index.vue", "mtime": 1721631954662}, {"path": "E:\\华南区项目\\中国银行\\开发区\\科技公司运营平台\\前端\\dopUnify\\babel.config.js", "mtime": 1721631954662}, {"path": "E:\\华南区项目\\中国银行\\开发区\\科技公司运营平台\\前端\\dopUnify\\.babelrc", "mtime": 1721631954662}, {"path": "E:\\华南区项目\\中国银行\\开发区\\科技公司运营平台\\前端\\dopUnify\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "E:\\华南区项目\\中国银行\\开发区\\科技公司运营平台\\前端\\dopUnify\\node_modules\\thread-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "E:\\华南区项目\\中国银行\\开发区\\科技公司运营平台\\前端\\dopUnify\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "E:\\华南区项目\\中国银行\\开发区\\科技公司运营平台\\前端\\dopUnify\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "E:\\华南区项目\\中国银行\\开发区\\科技公司运营平台\\前端\\dopUnify\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1655715099000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, null]}