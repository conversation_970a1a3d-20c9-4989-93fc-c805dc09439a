{"remainingRequest": "D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\node_modules\\babel-loader\\lib\\index.js!D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\node_modules\\eslint-loader\\index.js??ref--13-0!D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\src\\views\\system\\config\\message\\messageClass\\info.js", "dependencies": [{"path": "D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\src\\views\\system\\config\\message\\messageClass\\info.js", "mtime": 1686019807591}, {"path": "D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\babel.config.js", "mtime": 1686019818359}, {"path": "D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\.babelrc", "mtime": 1705285078668}, {"path": "D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\node_modules\\eslint-loader\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["dictionaryFieds", "config", "that", "classify_field", "component", "label", "colSpan", "name", "componentProps", "placeholder", "filterable", "clearable", "options", "value", "module_type", "map", "item", "homepage_is_open", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "is_open"], "sources": ["D:/1_Project/XYD_Project/dop-4.0/dop-4.1-qian<PERSON>n-unify/dopUnify/src/views/system/config/message/messageClass/info.js"], "sourcesContent": ["import { dictionaryFieds } from '@/utils/dictionary.js' // 字典常量\r\n// 表单\r\nexport const config = (that) => ({\r\n  classify_field: {\r\n    component: 'select',\r\n    label: '分类字段',\r\n    colSpan: 8,\r\n    name: 'classify_field',\r\n    config: {},\r\n    componentProps: {\r\n      placeholder: '请选择',\r\n      filterable: true,\r\n      clearable: true\r\n    },\r\n    options: [\r\n      { value: 'DEAL_STATUS', label: '消息状态' },\r\n      { value: 'WORK_TYPE', label: '消息类型' },\r\n      { value: 'DEAL_PERIOD', label: '任务周期' }\r\n    ]\r\n  },\r\n  module_type: {\r\n    component: 'select',\r\n    label: '所属模块',\r\n    colSpan: 8,\r\n    name: 'module_type',\r\n    config: {},\r\n    componentProps: {\r\n      placeholder: '请选择',\r\n      filterable: true,\r\n      clearable: true\r\n    },\r\n    options: dictionaryFieds('SERVICE_MODULE').map((item) => {\r\n      return {\r\n        value: item.value,\r\n        label: `${item.value}-${item.label}`\r\n      }\r\n    })\r\n  },\r\n  homepage_is_open: {\r\n    component: 'select',\r\n    lableWidth: '12rem',\r\n    label: '首页是否展示',\r\n    colSpan: 8,\r\n    name: 'homepage_is_open',\r\n    config: {},\r\n    componentProps: {\r\n      clearable: true\r\n    },\r\n    options: [\r\n      { value: '0', label: '否' },\r\n      { value: '1', label: '是' }\r\n    ]\r\n  },\r\n  is_open: {\r\n    component: 'select',\r\n    label: '启用标志',\r\n    colSpan: 8,\r\n    name: 'is_open',\r\n    config: {},\r\n    componentProps: {\r\n      placeholder: '请选择',\r\n      filterable: true,\r\n      clearable: true\r\n    },\r\n    options: [\r\n      { value: '0', label: '未启用' },\r\n      { value: '1', label: '启用' }\r\n    ]\r\n  }\r\n})\r\n"], "mappings": ";;AAAA,SAASA,eAAe,QAAQ,uBAAuB,EAAC;AACxD;AACA,OAAO,IAAMC,MAAM,GAAG,SAATA,MAAM,CAAIC,IAAI;EAAA,OAAM;IAC/BC,cAAc,EAAE;MACdC,SAAS,EAAE,QAAQ;MACnBC,KAAK,EAAE,MAAM;MACbC,OAAO,EAAE,CAAC;MACVC,IAAI,EAAE,gBAAgB;MACtBN,MAAM,EAAE,CAAC,CAAC;MACVO,cAAc,EAAE;QACdC,WAAW,EAAE,KAAK;QAClBC,UAAU,EAAE,IAAI;QAChBC,SAAS,EAAE;MACb,CAAC;MACDC,OAAO,EAAE,CACP;QAAEC,KAAK,EAAE,aAAa;QAAER,KAAK,EAAE;MAAO,CAAC,EACvC;QAAEQ,KAAK,EAAE,WAAW;QAAER,KAAK,EAAE;MAAO,CAAC,EACrC;QAAEQ,KAAK,EAAE,aAAa;QAAER,KAAK,EAAE;MAAO,CAAC;IAE3C,CAAC;IACDS,WAAW,EAAE;MACXV,SAAS,EAAE,QAAQ;MACnBC,KAAK,EAAE,MAAM;MACbC,OAAO,EAAE,CAAC;MACVC,IAAI,EAAE,aAAa;MACnBN,MAAM,EAAE,CAAC,CAAC;MACVO,cAAc,EAAE;QACdC,WAAW,EAAE,KAAK;QAClBC,UAAU,EAAE,IAAI;QAChBC,SAAS,EAAE;MACb,CAAC;MACDC,OAAO,EAAEZ,eAAe,CAAC,gBAAgB,CAAC,CAACe,GAAG,CAAC,UAACC,IAAI,EAAK;QACvD,OAAO;UACLH,KAAK,EAAEG,IAAI,CAACH,KAAK;UACjBR,KAAK,YAAKW,IAAI,CAACH,KAAK,cAAIG,IAAI,CAACX,KAAK;QACpC,CAAC;MACH,CAAC;IACH,CAAC;IACDY,gBAAgB,EAAE;MAChBb,SAAS,EAAE,QAAQ;MACnBc,UAAU,EAAE,OAAO;MACnBb,KAAK,EAAE,QAAQ;MACfC,OAAO,EAAE,CAAC;MACVC,IAAI,EAAE,kBAAkB;MACxBN,MAAM,EAAE,CAAC,CAAC;MACVO,cAAc,EAAE;QACdG,SAAS,EAAE;MACb,CAAC;MACDC,OAAO,EAAE,CACP;QAAEC,KAAK,EAAE,GAAG;QAAER,KAAK,EAAE;MAAI,CAAC,EAC1B;QAAEQ,KAAK,EAAE,GAAG;QAAER,KAAK,EAAE;MAAI,CAAC;IAE9B,CAAC;IACDc,OAAO,EAAE;MACPf,SAAS,EAAE,QAAQ;MACnBC,KAAK,EAAE,MAAM;MACbC,OAAO,EAAE,CAAC;MACVC,IAAI,EAAE,SAAS;MACfN,MAAM,EAAE,CAAC,CAAC;MACVO,cAAc,EAAE;QACdC,WAAW,EAAE,KAAK;QAClBC,UAAU,EAAE,IAAI;QAChBC,SAAS,EAAE;MACb,CAAC;MACDC,OAAO,EAAE,CACP;QAAEC,KAAK,EAAE,GAAG;QAAER,KAAK,EAAE;MAAM,CAAC,EAC5B;QAAEQ,KAAK,EAAE,GAAG;QAAER,KAAK,EAAE;MAAK,CAAC;IAE/B;EACF,CAAC;AAAA,CAAC"}]}