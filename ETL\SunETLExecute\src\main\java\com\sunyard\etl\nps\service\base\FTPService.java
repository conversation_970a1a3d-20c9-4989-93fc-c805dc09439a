package com.sunyard.etl.nps.service.base;

import java.io.File;
import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.sql.SQLException;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.sunyard.util.date.DateUtil;
import org.sunyard.util.file.FileUtil;
import org.sunyard.util.ftp.FTPFileUtil;
import com.xxl.job.core.log.XxlJobLogger;


import com.sunyard.etl.nps.dao.InputNopaperDao;
import com.sunyard.etl.system.model.JobParam;

public class FTPService {

	protected final Logger log = LoggerFactory.getLogger(getClass());
	
	private String ip;
	private int port;
	private String username;
	private String password;
	
	private static InputNopaperDao InputNopaperDao = new InputNopaperDao();
	private String remoteThisPath; // 远程文件路径
	private String flagName;// 标志文件名
	private String localPath;// 本地保存路径
	private String tableName;

	
	public FTPService() {

	}
	
	public FTPService(String ip, int port, String username, String password) {
		this.setIp(ip);
		this.setPort(port);
		this.setUsername(username);
		this.setPassword(password);
	}
	
	public FTPService(String remoteThisPath, String flagName, String localPath,
			String tableName) {
		this.remoteThisPath = remoteThisPath;
		this.flagName = flagName;
		this.localPath = localPath;
		this.tableName = tableName;
	}

	@Override  
    public Object clone() throws CloneNotSupportedException {  
        return (FTPService)super.clone();  
    }  
	
	
	
	/**
	 * 返回目录下所有图片的MAP<流水号,图片编号字符串>
	 * 
	 * <AUTHOR> 2017年7月7日
	 * @param dir
	 * @return
	 */
	public Map<String, List<String>> getImgMap(File dir) {
		Map<String, List<String>> map = new HashMap<String, List<String>>();
		File[] siteArray = dir.listFiles();
		for (File site : siteArray) {
			File[] operatorArray = site.listFiles();
			for (File operator : operatorArray) {
				File[] imgArray = operator.listFiles();
				for (File img : imgArray) {
					String imgName = img.getName();
					String flowId = imgName.substring(0, imgName.indexOf("."));
					String number = imgName.substring(imgName.indexOf("-"),
							imgName.indexOf("."));
					if (map.containsKey(flowId)) {
						map.get(flowId).add(number);
					} else {
						List<String> info = new ArrayList<String>();
						info.add(dir.getName());
						info.add(site.getName());
						info.add(operator.getName());
						info.add(flowId);
						info.add(number);
						map.put(flowId, info);
					}
				}
			}
		}
		return map;
	}

	/**
	 * 批量更新FTP导入日期
	 * 
	 * @Title
	 * @Description
	 * <AUTHOR> 2017年7月18日
	 * @param
	 * @throws ParseException
	 * @throws SQLException 
	 * @throws SQLException
	 */
	public boolean updateNpFtpDataDate(String nopaperId, JobParam jobParam) throws ParseException, SQLException {
		String startDate = jobParam.getStartDate();
		String today = DateUtil.getNow();
		// 获取任务开始日期到昨天的日期集合
		SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMdd");
		Date dateA = sdf.parse(startDate);
		Date dateB = sdf.parse(today);
		List<String> a = DateUtil.getDatesBetweenTwoDate(dateA, dateB);
		// 获取表中所有的日期信息
		List<String> b = InputNopaperDao.getAllFTPDataDate(nopaperId, startDate);
		a.removeAll(b);// 两个集合相减
		InputNopaperDao.insertFTPDataDate(a, nopaperId);// 插入数据
		return true;
	}

	
	
	public List<String> download(FTPFileUtil ftp) {
		XxlJobLogger.log("开始FTP下载.......", tableName);
		XxlJobLogger.log("远程文件路径:" + remoteThisPath, tableName);
		XxlJobLogger.log("标志文件名称:" + flagName, tableName);
		XxlJobLogger.log("本地保存路径:" + localPath, tableName);
		List<String> pathList = new ArrayList<String>();
		try {
			/*
			 * 检查标志文件
			 */
			if (!ftp.isExist(remoteThisPath, flagName)) {
				XxlJobLogger.log("标志文件" + flagName + "不存在，FTP文件未准备完毕:", tableName);
				return pathList;
			}
			/*
			 * 获取FTP上当前目录下的所有文件路径
			 */
			ftp.getAllPath(remoteThisPath, pathList);
			/*
			 * 清空本地临时文件
			 */
			for (String path : pathList) {
				path = localPath + File.separator + path;
				File dir = new File(path);
				FileUtil.fullyDelete(dir);
			}
			/*
			 * 下载到本地
			 */
			ftp.downloadAllPath(pathList, localPath);
		} catch (UnsupportedEncodingException e) {
			e.printStackTrace();
			return pathList;
		} catch (IOException e) {
			e.printStackTrace();
			return pathList;
		}
		return pathList;
	}

	public String getIp() {
		return ip;
	}

	public void setIp(String ip) {
		this.ip = ip;
	}

	public int getPort() {
		return port;
	}

	public void setPort(int port) {
		this.port = port;
	}

	public String getUsername() {
		return username;
	}

	public void setUsername(String username) {
		this.username = username;
	}

	public String getPassword() {
		return password;
	}

	public void setPassword(String password) {
		this.password = password;
	}
}
