{"remainingRequest": "E:\\华南区项目\\中国银行\\开发区\\科技公司运营平台\\前端\\dopUnify\\node_modules\\babel-loader\\lib\\index.js!E:\\华南区项目\\中国银行\\开发区\\科技公司运营平台\\前端\\dopUnify\\node_modules\\eslint-loader\\index.js??ref--13-0!E:\\华南区项目\\中国银行\\开发区\\科技公司运营平台\\前端\\dopUnify\\src\\utils\\requestCf.js", "dependencies": [{"path": "E:\\华南区项目\\中国银行\\开发区\\科技公司运营平台\\前端\\dopUnify\\src\\utils\\requestCf.js", "mtime": 1721631954662}, {"path": "E:\\华南区项目\\中国银行\\开发区\\科技公司运营平台\\前端\\dopUnify\\babel.config.js", "mtime": 1721631954662}, {"path": "E:\\华南区项目\\中国银行\\开发区\\科技公司运营平台\\前端\\dopUnify\\.babelrc", "mtime": 1721631954662}, {"path": "E:\\华南区项目\\中国银行\\开发区\\科技公司运营平台\\前端\\dopUnify\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "E:\\华南区项目\\中国银行\\开发区\\科技公司运营平台\\前端\\dopUnify\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "E:\\华南区项目\\中国银行\\开发区\\科技公司运营平台\\前端\\dopUnify\\node_modules\\eslint-loader\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["axios", "MessageBox", "Notification", "commonBlank", "store", "getToken", "getXToken", "setToken", "encryptResult", "decryptResult", "defaultSettings", "system", "service", "create", "baseURL", "process", "env", "VUE_APP_BASE_API", "timeout", "headers", "interceptors", "request", "use", "config", "enSecMap", "getters", "initParams", "url", "token", "userNo", "includes", "xToken", "method", "params", "isEncrypt", "encodeURIComponent", "JSON", "stringify", "message", "keys", "Object", "key", "substring", "length", "data", "error", "Promise", "reject", "response", "status", "statusText", "authorization", "commit", "cfitCode", "msgCode", "code", "cfitMsg", "msg", "Error", "confirm", "confirmButtonText", "showClose", "showCancelButton", "type", "then", "dispatch", "location", "reload", "console", "log"], "sources": ["E:/华南区项目/中国银行/开发区/科技公司运营平台/前端/dopUnify/src/utils/requestCf.js"], "sourcesContent": ["import axios from 'axios'\nimport { MessageBox, Notification } from 'element-ui'\nimport { commonBlank } from '@/utils/common'\nimport store from '@/store'\nimport { getToken, getXToken, setToken } from '@/utils/auth'\nimport { encryptResult, decryptResult } from '@/utils/crypto'\nimport defaultSettings from '@/settings'\nconst system = defaultSettings.service.system\n// import qs from 'qs'\n\n// 新建一个 axios 实例\nconst service = axios.create({\n  // `baseURL` 将自动加在 `url` 前面，可以通过设置一个 `baseURL` 便于为 axios 实例的方法传递相对 URL\n  baseURL: process.env.VUE_APP_BASE_API, // url = base url + request url\n  timeout: 50000, // 请求超时(毫秒)\n  // withCredentials: true, // 当跨域请求时发送cookie\n  headers: { 'If-Modified-Since': '0', 'Cache-Control': 'no-cache' } // 解决ie浏览器get请求会被缓存，导致结果页面无法正常显示的问题\n})\n\n// 请求拦截器\nservice.interceptors.request.use(\n  (config) => {\n    const enSecMap = store.getters.initParams.enSecMap\n    let url = config.url\n\n    if (store.getters.token && store.getters.userNo) {\n      // 让每个请求携带 token\n      // ['X-Token'] 是一个自定义头键\n      // 请根据实际情况进行修改\n      if (!url.includes('/cfit/')) {\n        config.headers['Authorization'] = getToken()\n          ? getToken()\n          : store.getters.token\n      }\n      config.headers['X-TOKEN'] = getToken()\n        ? getXToken()\n        : store.getters.xToken\n    }\n    // 请求头配置\n    // config.headers.Accept = 'application/x-www-form-urlencoded'\n    // 参数传递格式化\n    // config.transformRequest = [\n    //   function(data) {\n    //     return qs.stringify(data, { arrayFormat: 'BRACKETS' })\n    //   }\n    // ]\n\n    // get参数编码\n    if (config.method === 'get' && config.params) {\n      url += '?'\n      if (!commonBlank(enSecMap) && enSecMap.isEncrypt === '1') {\n        // 需要加密\n        url += `message=${encodeURIComponent(\n          encryptResult('SM4', JSON.stringify(config.params.message))\n        )}&`\n      } else {\n        const keys = Object.keys(config.params)\n        for (const key of keys) {\n          url += `${key}=${encodeURIComponent(\n            JSON.stringify(config.params[key])\n          )}&`\n        }\n      }\n      url = url.substring(0, url.length - 1)\n      config.params = {}\n    }\n    if (\n      !commonBlank(config.data) &&\n      !commonBlank(enSecMap) && // 第一个接口中必定不存在加密参数属性Map\n      enSecMap.isEncrypt === '1' && // 是否进行加密\n      url !== system + '/checkInit.do' // 是否为第一个获取加密参数的接口\n    ) {\n      config.data = {\n        message: encryptResult('SM4', JSON.stringify(config.data))\n      }\n    }\n    config.url = url\n    return config\n  },\n  (error) => {\n    // 对请求错误做些什么\n    return Promise.reject(error)\n  }\n)\n\n// 响应拦截器\nservice.interceptors.response.use(\n  /**\n   * 通过自定义代码确定请求状态\n   * 可以通过HTTP状态码来判断状态\n   */\n  (response) => {\n    const enSecMap = store.getters.initParams.enSecMap\n    // 对响应数据做点什么\n    if (commonBlank(response)) {\n      response = {}\n    }\n    const { status, statusText, headers } = response\n    //\n    if (!commonBlank(headers.authorization)) {\n      // console.log(2, headers.authorization)\n      setToken(headers.authorization)\n      store.commit('user/SET_TOKEN', headers.authorization)\n    }\n    if (status === 200) {\n      // 成方返回报文结构\n      const cfitCode = response.data.msgCode || response.data.code\n      const cfitMsg = response.data.message || response.data.msg || ''\n      if (!commonBlank(cfitCode)) {\n        if (cfitCode === 500) {\n          // Notification.error({\n          //   title: '错误',\n          //   message: '后台报错：' + cfitCode + ' - ' + cfitMsg || 'Error',\n          //   type: 'warning'\n          // })\n          return Promise.reject(new Error(cfitMsg || 'Error'))\n        } else {\n          return response.data\n        }\n      }\n    } else {\n      // 重新登录提示\n      MessageBox.confirm(`${statusText}，请重新登录`, '确认注销', {\n        confirmButtonText: '重新登录',\n        showClose: false,\n        showCancelButton: false,\n        type: 'warning'\n      }).then(() => {\n        store.dispatch('user/resetToken').then(() => {\n          location.reload()\n        })\n      })\n    }\n  },\n  (error) => {\n    // 对响应错误做点什么\n    const { status, statusText } = error.response\n\n    console.log(`后台报错：${status} - ${statusText}`)\n\n    // Message({\n    //   message: `后台报错：${status} - ${statusText}`,\n    //   type: 'error',\n    //   duration: 5 * 1000\n    // })\n\n    return Promise.reject(error) // 返回一个带有拒绝原因的Promise对象,error表示Promise被拒绝的原因。\n  }\n)\n\nexport default service\n"], "mappings": ";;;;;;AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,UAAU,EAAEC,YAAY,QAAQ,YAAY;AACrD,SAASC,WAAW,QAAQ,gBAAgB;AAC5C,OAAOC,KAAK,MAAM,SAAS;AAC3B,SAASC,QAAQ,EAAEC,SAAS,EAAEC,QAAQ,QAAQ,cAAc;AAC5D,SAASC,aAAa,EAAEC,aAAa,QAAQ,gBAAgB;AAC7D,OAAOC,eAAe,MAAM,YAAY;AACxC,IAAMC,MAAM,GAAGD,eAAe,CAACE,OAAO,CAACD,MAAM;AAC7C;;AAEA;AACA,IAAMC,OAAO,GAAGZ,KAAK,CAACa,MAAM,CAAC;EAC3B;EACAC,OAAO,EAAEC,OAAO,CAACC,GAAG,CAACC,gBAAgB;EAAE;EACvCC,OAAO,EAAE,KAAK;EAAE;EAChB;EACAC,OAAO,EAAE;IAAE,mBAAmB,EAAE,GAAG;IAAE,eAAe,EAAE;EAAW,CAAC,CAAC;AACrE,CAAC,CAAC;;AAEF;AACAP,OAAO,CAACQ,YAAY,CAACC,OAAO,CAACC,GAAG,CAC9B,UAACC,MAAM,EAAK;EACV,IAAMC,QAAQ,GAAGpB,KAAK,CAACqB,OAAO,CAACC,UAAU,CAACF,QAAQ;EAClD,IAAIG,GAAG,GAAGJ,MAAM,CAACI,GAAG;EAEpB,IAAIvB,KAAK,CAACqB,OAAO,CAACG,KAAK,IAAIxB,KAAK,CAACqB,OAAO,CAACI,MAAM,EAAE;IAC/C;IACA;IACA;IACA,IAAI,CAACF,GAAG,CAACG,QAAQ,CAAC,QAAQ,CAAC,EAAE;MAC3BP,MAAM,CAACJ,OAAO,CAAC,eAAe,CAAC,GAAGd,QAAQ,EAAE,GACxCA,QAAQ,EAAE,GACVD,KAAK,CAACqB,OAAO,CAACG,KAAK;IACzB;IACAL,MAAM,CAACJ,OAAO,CAAC,SAAS,CAAC,GAAGd,QAAQ,EAAE,GAClCC,SAAS,EAAE,GACXF,KAAK,CAACqB,OAAO,CAACM,MAAM;EAC1B;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;EAEA;EACA,IAAIR,MAAM,CAACS,MAAM,KAAK,KAAK,IAAIT,MAAM,CAACU,MAAM,EAAE;IAC5CN,GAAG,IAAI,GAAG;IACV,IAAI,CAACxB,WAAW,CAACqB,QAAQ,CAAC,IAAIA,QAAQ,CAACU,SAAS,KAAK,GAAG,EAAE;MACxD;MACAP,GAAG,sBAAeQ,kBAAkB,CAClC3B,aAAa,CAAC,KAAK,EAAE4B,IAAI,CAACC,SAAS,CAACd,MAAM,CAACU,MAAM,CAACK,OAAO,CAAC,CAAC,CAC5D,MAAG;IACN,CAAC,MAAM;MACL,IAAMC,IAAI,GAAGC,MAAM,CAACD,IAAI,CAAChB,MAAM,CAACU,MAAM,CAAC;MACvC,yBAAkBM,IAAI,2BAAE;QAAnB,IAAME,GAAG;QACZd,GAAG,cAAOc,GAAG,cAAIN,kBAAkB,CACjCC,IAAI,CAACC,SAAS,CAACd,MAAM,CAACU,MAAM,CAACQ,GAAG,CAAC,CAAC,CACnC,MAAG;MACN;IACF;IACAd,GAAG,GAAGA,GAAG,CAACe,SAAS,CAAC,CAAC,EAAEf,GAAG,CAACgB,MAAM,GAAG,CAAC,CAAC;IACtCpB,MAAM,CAACU,MAAM,GAAG,CAAC,CAAC;EACpB;EACA,IACE,CAAC9B,WAAW,CAACoB,MAAM,CAACqB,IAAI,CAAC,IACzB,CAACzC,WAAW,CAACqB,QAAQ,CAAC;EAAI;EAC1BA,QAAQ,CAACU,SAAS,KAAK,GAAG;EAAI;EAC9BP,GAAG,KAAKhB,MAAM,GAAG,eAAe,CAAC;EAAA,EACjC;IACAY,MAAM,CAACqB,IAAI,GAAG;MACZN,OAAO,EAAE9B,aAAa,CAAC,KAAK,EAAE4B,IAAI,CAACC,SAAS,CAACd,MAAM,CAACqB,IAAI,CAAC;IAC3D,CAAC;EACH;EACArB,MAAM,CAACI,GAAG,GAAGA,GAAG;EAChB,OAAOJ,MAAM;AACf,CAAC,EACD,UAACsB,KAAK,EAAK;EACT;EACA,OAAOC,OAAO,CAACC,MAAM,CAACF,KAAK,CAAC;AAC9B,CAAC,CACF;;AAED;AACAjC,OAAO,CAACQ,YAAY,CAAC4B,QAAQ,CAAC1B,GAAG;AAC/B;AACF;AACA;AACA;AACE,UAAC0B,QAAQ,EAAK;EACZ,IAAMxB,QAAQ,GAAGpB,KAAK,CAACqB,OAAO,CAACC,UAAU,CAACF,QAAQ;EAClD;EACA,IAAIrB,WAAW,CAAC6C,QAAQ,CAAC,EAAE;IACzBA,QAAQ,GAAG,CAAC,CAAC;EACf;EACA,gBAAwCA,QAAQ;IAAxCC,MAAM,aAANA,MAAM;IAAEC,UAAU,aAAVA,UAAU;IAAE/B,OAAO,aAAPA,OAAO;EACnC;EACA,IAAI,CAAChB,WAAW,CAACgB,OAAO,CAACgC,aAAa,CAAC,EAAE;IACvC;IACA5C,QAAQ,CAACY,OAAO,CAACgC,aAAa,CAAC;IAC/B/C,KAAK,CAACgD,MAAM,CAAC,gBAAgB,EAAEjC,OAAO,CAACgC,aAAa,CAAC;EACvD;EACA,IAAIF,MAAM,KAAK,GAAG,EAAE;IAClB;IACA,IAAMI,QAAQ,GAAGL,QAAQ,CAACJ,IAAI,CAACU,OAAO,IAAIN,QAAQ,CAACJ,IAAI,CAACW,IAAI;IAC5D,IAAMC,OAAO,GAAGR,QAAQ,CAACJ,IAAI,CAACN,OAAO,IAAIU,QAAQ,CAACJ,IAAI,CAACa,GAAG,IAAI,EAAE;IAChE,IAAI,CAACtD,WAAW,CAACkD,QAAQ,CAAC,EAAE;MAC1B,IAAIA,QAAQ,KAAK,GAAG,EAAE;QACpB;QACA;QACA;QACA;QACA;QACA,OAAOP,OAAO,CAACC,MAAM,CAAC,IAAIW,KAAK,CAACF,OAAO,IAAI,OAAO,CAAC,CAAC;MACtD,CAAC,MAAM;QACL,OAAOR,QAAQ,CAACJ,IAAI;MACtB;IACF;EACF,CAAC,MAAM;IACL;IACA3C,UAAU,CAAC0D,OAAO,WAAIT,UAAU,2CAAU,MAAM,EAAE;MAChDU,iBAAiB,EAAE,MAAM;MACzBC,SAAS,EAAE,KAAK;MAChBC,gBAAgB,EAAE,KAAK;MACvBC,IAAI,EAAE;IACR,CAAC,CAAC,CAACC,IAAI,CAAC,YAAM;MACZ5D,KAAK,CAAC6D,QAAQ,CAAC,iBAAiB,CAAC,CAACD,IAAI,CAAC,YAAM;QAC3CE,QAAQ,CAACC,MAAM,EAAE;MACnB,CAAC,CAAC;IACJ,CAAC,CAAC;EACJ;AACF,CAAC,EACD,UAACtB,KAAK,EAAK;EACT;EACA,sBAA+BA,KAAK,CAACG,QAAQ;IAArCC,MAAM,mBAANA,MAAM;IAAEC,UAAU,mBAAVA,UAAU;EAE1BkB,OAAO,CAACC,GAAG,yCAASpB,MAAM,gBAAMC,UAAU,EAAG;;EAE7C;EACA;EACA;EACA;EACA;;EAEA,OAAOJ,OAAO,CAACC,MAAM,CAACF,KAAK,CAAC,EAAC;AAC/B,CAAC,CACF;;AAED,eAAejC,OAAO"}]}