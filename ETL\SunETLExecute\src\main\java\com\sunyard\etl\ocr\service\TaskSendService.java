package com.sunyard.etl.ocr.service;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.json.JsonSanitizer;
import com.sunyard.etl.ocr.bean.DataSource;
import com.sunyard.etl.ocr.bean.TmpBatch;
import com.sunyard.etl.ocr.common.OCRConstants;
import com.sunyard.etl.ocr.common.OcrRequestClient;
import com.sunyard.etl.ocr.dao.SmOrganOcrUrlDao;
import com.sunyard.etl.ocr.dao.TmpBatchMapper;
import com.sunyard.etl.ocr.dao.UnifyDao;
import com.xxl.job.core.log.XxlJobLogger;

import java.net.URLEncoder;
import java.sql.SQLException;
import java.text.SimpleDateFormat;
import java.util.*;

public class TaskSendService {

	/**
	 * 批次状态未等待OCR识别
	 */
	private final String BATCH_TYPE_WATI_OCR = "10";

	/**
	 * OCR识别状态-未处理
	 */
	private final String BATCH_OCR_STATE_NO = "0";

	/**
	 * OCR识别状态-在队列中
	 */
	private final String BATCH_OCR_STATE_DOING = "1";

	/**
	 * 批次状态-有效
	 */
	private final String BATCH_STATE_VALID = "1";

	/**
	 * if平台添加操作
	 */
	private final String IF_OPTION_ADD = "OP001";

	private TmpBatchMapper tmpBatchMapper;

	private SmOrganOcrUrlDao smOrganOcrUrlDao;

	private UnifyDao unifyDao;

	public TaskSendService(DataSource ds, DataSource unifyDs){
		tmpBatchMapper = new TmpBatchMapper(ds.getName());
		smOrganOcrUrlDao = new SmOrganOcrUrlDao(ds.getName());
		unifyDao = new UnifyDao(unifyDs);
	}

	/**
	 * 获取批次表中有效的状态为等待OCR识别且还未进入识别队列的批次。
	 * @return
	 * @throws SQLException
	 */
	public List<TmpBatch> getSendBatch() throws SQLException {
		TmpBatch tmpBatch = new TmpBatch();
		tmpBatch.setIsInvalid(BATCH_STATE_VALID);
		tmpBatch.setProgressFlag(BATCH_TYPE_WATI_OCR);
		tmpBatch.setOcrFactorFlag(BATCH_OCR_STATE_NO);
		List<TmpBatch> list = tmpBatchMapper.selectByCondition(tmpBatch);
		return list;
	}

	/**
	 * 发送批次识别任务
	 * @param batchList
	 * @return
	 * @throws Exception
	 */
	public Map<String,String> sendBatchList(List<TmpBatch> batchList, String channelIdStr,String ocrUrlStr,String platForm) throws Exception {
		Map<String, String> retMap = new HashMap<String, String>();
		if(batchList == null || batchList.isEmpty()) {
			retMap.put("retCode", OCRConstants.HANDLE_SUCCESS);
			retMap.put("retMsg", "没有需要发送的识别任务！");
			retMap.put("sendCount", "0");
		}else {
			int sendCountSuccess = 0;
			int sendCountFail = 0;
			if ("SunFA".equals(platForm)){
				for(TmpBatch batch : batchList) {
					boolean sendFlag = sendBatchToSunFA(batch, channelIdStr,ocrUrlStr);
					if(sendFlag) {
						updateBatchOcrState(batch);
						sendCountSuccess++;
					}else {
						sendCountFail++;
					}
				}
			}else {
				for(TmpBatch batch : batchList) {
					boolean sendFlag = sendBatch(batch, channelIdStr,ocrUrlStr);
					if(sendFlag) {
						updateBatchOcrState(batch);
						sendCountSuccess++;
					}else {
						sendCountFail++;
					}
				}
			}

			retMap.put("retCode", OCRConstants.HANDLE_SUCCESS);
			retMap.put("retMsg", "识别任务发送成功！");
			retMap.put("sendCount", String.valueOf(batchList.size()));
			retMap.put("sendCountSuccess", String.valueOf(sendCountSuccess));
			retMap.put("sendCountFail", String.valueOf(sendCountFail));
		}
		return retMap;
	}
	/**
	 * 发送单笔识别任务到SunFA识别平台
	 * @param batch
	 * @return
	 * @throws Exception
	 */
	@SuppressWarnings({ "rawtypes", "unchecked" })
	private boolean sendBatchToSunFA(TmpBatch batch, String rightChannelStr,String ocrUrlStr) throws Exception {
		Map<String, Object> sysMap = new HashMap<String, Object>();
		sysMap.put("chanId", rightChannelStr); //渠道号
		sysMap.put("taskType", "1"); //是否回调 0-否 ，1-是
		sysMap.put("docId", batch.getBatchId());//ecm影像批次号.后督没存，随便写个，不能为空。
		sysMap.put("organNo", batch.getSiteNo()); //机构号
		SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMddHHmmssSSS");
		Date today = new Date();
		String taskId = sdf.format(today);
		sysMap.put("taskId", taskId); //机构号
		sysMap.put("ecmDate",batch.getInputDate()); //机构号
		ObjectMapper objectMapper = new ObjectMapper();
		String message = objectMapper.writeValueAsString(sysMap);
		String parentOrgans = unifyDao.getParentOrgan(batch.getSiteNo());
		String ocrUrl = smOrganOcrUrlDao.getOrganOcrUrl(parentOrgans);
		//优先表中配置，页面配置再默认配置
		if(ocrUrl == null || ocrUrl.isEmpty()) {
			ocrUrl = ocrUrlStr.trim();
		}
		message = JsonSanitizer.sanitize(message);
		String backMessge = OcrRequestClient.sendPostToSunFA(ocrUrl,message);
		XxlJobLogger.log("发送url:"+ocrUrl + "  " + "message="+ message);
		backMessge = JsonSanitizer.sanitize(backMessge);
		Map backMap = objectMapper.readValue(backMessge, Map.class);
		if("FA0000".equals(backMap.get("retCode"))) {
			return true;
		}else {
			return false;
		}
	}
	/**
	 * 发送单笔识别任务
	 * @param batch
	 * @return
	 * @throws Exception
	 */
	@SuppressWarnings({ "rawtypes", "unchecked" })
	private boolean sendBatch(TmpBatch batch, String rightChannelStr,String ocrUrlStr) throws Exception {
		List parameterList = new ArrayList();
		Map<String, Object> batchInfo = new HashMap<String, Object>();
		//批次信息
		batchInfo.put("batchId", batch.getBatchId());//后督批次号
		batchInfo.put("inputDate", batch.getInputDate());//扫描日期
		batchInfo.put("siteNO", batch.getSiteNo());//交易机构
		batchInfo.put("contentId", batch.getContentId());//ecm批次号
		batchInfo.put("channelId", rightChannelStr);
		parameterList.add(batchInfo); //建议只放1个map,识别平台只存了第一个。

		// 按序号识别
		Map<String, Object> sysMap = new HashMap<String, Object>();
		sysMap.put("chan_id", rightChannelStr); //渠道号
		sysMap.put("task_type", "1"); //是否回调 0-否 ，1-是
		sysMap.put("doc_id", batch.getBatchId());//ecm影像批次号.后督没存，随便写个，不能为空。
		sysMap.put("organ_no", batch.getSiteNo()); //机构号
		sysMap.put("oper_type", IF_OPTION_ADD); //操作类型OP001-添加识别任务

		Map<String, Object> msgMap = new HashMap<String, Object>();
		msgMap.put("parameterList", parameterList);
		msgMap.put("sysMap", sysMap);

		ObjectMapper objectMapper = new ObjectMapper();
		String message = objectMapper.writeValueAsString(msgMap);
		String parentOrgans = unifyDao.getParentOrgan(batch.getSiteNo());
		String ocrUrl = smOrganOcrUrlDao.getOrganOcrUrl(parentOrgans);
		//优先表中配置，页面配置再默认配置
		if(ocrUrl == null || ocrUrl.isEmpty()) {
			ocrUrl = ocrUrlStr.trim();
		}
		message = JsonSanitizer.sanitize(message);
		String backMessge = OcrRequestClient.sendPost(ocrUrl, "message="+ URLEncoder.encode(message, OCRConstants.MESSAGE_ENCODING));
		XxlJobLogger.log("发送url:"+ocrUrl + "  " + "message="+ message);
		backMessge = JsonSanitizer.sanitize(backMessge);
		Map backMap = objectMapper.readValue(backMessge, Map.class);
		if(OCRConstants.HANDLE_SUCCESS.equals(backMap.get("retCode"))) {
			return true;
		}else {
			return false;
		}
	}

	/**
	 * 将ocr识别状态改为在队列中。
	 * @param batch
	 * @return
	 * @throws SQLException
	 */
	private int updateBatchOcrState(TmpBatch batch) throws SQLException {
		batch.setOcrFactorFlag(BATCH_OCR_STATE_DOING);
		int i = tmpBatchMapper.updateByPrimaryKey(batch);
		return i;
	}


}
