{"remainingRequest": "E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\node_modules\\babel-loader\\lib\\index.js!E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\node_modules\\qiankun\\es\\globalState.js", "dependencies": [{"path": "E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\node_modules\\qiankun\\es\\globalState.js", "mtime": 1667130453000}, {"path": "E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\babel.config.js", "mtime": 1686019818359}, {"path": "E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["_defineProperty", "_cloneDeep", "globalState", "deps", "emitGlobal", "state", "prevState", "Object", "keys", "for<PERSON>ach", "id", "Function", "initGlobalState", "arguments", "length", "undefined", "process", "env", "NODE_ENV", "console", "warn", "prevGlobalState", "getMicroAppStateActions", "concat", "Date", "isMaster", "onGlobalStateChange", "callback", "fireImmediately", "error", "cloneState", "setGlobalState", "changeKeys", "reduce", "_globalState", "change<PERSON>ey", "hasOwnProperty", "push", "assign", "offGlobalStateChange"], "sources": ["E:/1_Project/XYD_Project/dop-4.0/dop-4.1-qianduan-unify/dopUnify/node_modules/qiankun/es/globalState.js"], "sourcesContent": ["import _defineProperty from \"@babel/runtime/helpers/esm/defineProperty\";\nimport _cloneDeep from \"lodash/cloneDeep\";\nvar globalState = {};\nvar deps = {};\n// 触发全局监听\nfunction emitGlobal(state, prevState) {\n  Object.keys(deps).forEach(function (id) {\n    if (deps[id] instanceof Function) {\n      deps[id](_cloneDeep(state), _cloneDeep(prevState));\n    }\n  });\n}\nexport function initGlobalState() {\n  var state = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : {};\n  if (process.env.NODE_ENV === 'development') {\n    console.warn(\"[qiankun] globalState tools will be removed in 3.0, pls don't use it!\");\n  }\n  if (state === globalState) {\n    console.warn('[qiankun] state has not changed！');\n  } else {\n    var prevGlobalState = _cloneDeep(globalState);\n    globalState = _cloneDeep(state);\n    emitGlobal(globalState, prevGlobalState);\n  }\n  return getMicroAppStateActions(\"global-\".concat(+new Date()), true);\n}\nexport function getMicroAppStateActions(id, isMaster) {\n  return {\n    /**\n     * onGlobalStateChange 全局依赖监听\n     *\n     * 收集 setState 时所需要触发的依赖\n     *\n     * 限制条件：每个子应用只有一个激活状态的全局监听，新监听覆盖旧监听，若只是监听部分属性，请使用 onGlobalStateChange\n     *\n     * 这么设计是为了减少全局监听滥用导致的内存爆炸\n     *\n     * 依赖数据结构为：\n     * {\n     *   {id}: callback\n     * }\n     *\n     * @param callback\n     * @param fireImmediately\n     */\n    onGlobalStateChange: function onGlobalStateChange(callback, fireImmediately) {\n      if (!(callback instanceof Function)) {\n        console.error('[qiankun] callback must be function!');\n        return;\n      }\n      if (deps[id]) {\n        console.warn(\"[qiankun] '\".concat(id, \"' global listener already exists before this, new listener will overwrite it.\"));\n      }\n      deps[id] = callback;\n      if (fireImmediately) {\n        var cloneState = _cloneDeep(globalState);\n        callback(cloneState, cloneState);\n      }\n    },\n    /**\n     * setGlobalState 更新 store 数据\n     *\n     * 1. 对输入 state 的第一层属性做校验，只有初始化时声明过的第一层（bucket）属性才会被更改\n     * 2. 修改 store 并触发全局监听\n     *\n     * @param state\n     */\n    setGlobalState: function setGlobalState() {\n      var state = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : {};\n      if (state === globalState) {\n        console.warn('[qiankun] state has not changed！');\n        return false;\n      }\n      var changeKeys = [];\n      var prevGlobalState = _cloneDeep(globalState);\n      globalState = _cloneDeep(Object.keys(state).reduce(function (_globalState, changeKey) {\n        if (isMaster || _globalState.hasOwnProperty(changeKey)) {\n          changeKeys.push(changeKey);\n          return Object.assign(_globalState, _defineProperty({}, changeKey, state[changeKey]));\n        }\n        console.warn(\"[qiankun] '\".concat(changeKey, \"' not declared when init state\\uFF01\"));\n        return _globalState;\n      }, globalState));\n      if (changeKeys.length === 0) {\n        console.warn('[qiankun] state has not changed！');\n        return false;\n      }\n      emitGlobal(globalState, prevGlobalState);\n      return true;\n    },\n    // 注销该应用下的依赖\n    offGlobalStateChange: function offGlobalStateChange() {\n      delete deps[id];\n      return true;\n    }\n  };\n}"], "mappings": ";;;;AAAA,OAAOA,eAAe,MAAM,2CAA2C;AACvE,OAAOC,UAAU,MAAM,kBAAkB;AACzC,IAAIC,WAAW,GAAG,CAAC,CAAC;AACpB,IAAIC,IAAI,GAAG,CAAC,CAAC;AACb;AACA,SAASC,UAAU,CAACC,KAAK,EAAEC,SAAS,EAAE;EACpCC,MAAM,CAACC,IAAI,CAACL,IAAI,CAAC,CAACM,OAAO,CAAC,UAAUC,EAAE,EAAE;IACtC,IAAIP,IAAI,CAACO,EAAE,CAAC,YAAYC,QAAQ,EAAE;MAChCR,IAAI,CAACO,EAAE,CAAC,CAACT,UAAU,CAACI,KAAK,CAAC,EAAEJ,UAAU,CAACK,SAAS,CAAC,CAAC;IACpD;EACF,CAAC,CAAC;AACJ;AACA,OAAO,SAASM,eAAe,GAAG;EAChC,IAAIP,KAAK,GAAGQ,SAAS,CAACC,MAAM,GAAG,CAAC,IAAID,SAAS,CAAC,CAAC,CAAC,KAAKE,SAAS,GAAGF,SAAS,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;EAClF,IAAIG,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,aAAa,EAAE;IAC1CC,OAAO,CAACC,IAAI,CAAC,uEAAuE,CAAC;EACvF;EACA,IAAIf,KAAK,KAAKH,WAAW,EAAE;IACzBiB,OAAO,CAACC,IAAI,CAAC,kCAAkC,CAAC;EAClD,CAAC,MAAM;IACL,IAAIC,eAAe,GAAGpB,UAAU,CAACC,WAAW,CAAC;IAC7CA,WAAW,GAAGD,UAAU,CAACI,KAAK,CAAC;IAC/BD,UAAU,CAACF,WAAW,EAAEmB,eAAe,CAAC;EAC1C;EACA,OAAOC,uBAAuB,CAAC,SAAS,CAACC,MAAM,CAAC,CAAC,IAAIC,IAAI,EAAE,CAAC,EAAE,IAAI,CAAC;AACrE;AACA,OAAO,SAASF,uBAAuB,CAACZ,EAAE,EAAEe,QAAQ,EAAE;EACpD,OAAO;IACL;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;IACIC,mBAAmB,EAAE,SAASA,mBAAmB,CAACC,QAAQ,EAAEC,eAAe,EAAE;MAC3E,IAAI,EAAED,QAAQ,YAAYhB,QAAQ,CAAC,EAAE;QACnCQ,OAAO,CAACU,KAAK,CAAC,sCAAsC,CAAC;QACrD;MACF;MACA,IAAI1B,IAAI,CAACO,EAAE,CAAC,EAAE;QACZS,OAAO,CAACC,IAAI,CAAC,aAAa,CAACG,MAAM,CAACb,EAAE,EAAE,+EAA+E,CAAC,CAAC;MACzH;MACAP,IAAI,CAACO,EAAE,CAAC,GAAGiB,QAAQ;MACnB,IAAIC,eAAe,EAAE;QACnB,IAAIE,UAAU,GAAG7B,UAAU,CAACC,WAAW,CAAC;QACxCyB,QAAQ,CAACG,UAAU,EAAEA,UAAU,CAAC;MAClC;IACF,CAAC;IACD;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;IACIC,cAAc,EAAE,SAASA,cAAc,GAAG;MACxC,IAAI1B,KAAK,GAAGQ,SAAS,CAACC,MAAM,GAAG,CAAC,IAAID,SAAS,CAAC,CAAC,CAAC,KAAKE,SAAS,GAAGF,SAAS,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;MAClF,IAAIR,KAAK,KAAKH,WAAW,EAAE;QACzBiB,OAAO,CAACC,IAAI,CAAC,kCAAkC,CAAC;QAChD,OAAO,KAAK;MACd;MACA,IAAIY,UAAU,GAAG,EAAE;MACnB,IAAIX,eAAe,GAAGpB,UAAU,CAACC,WAAW,CAAC;MAC7CA,WAAW,GAAGD,UAAU,CAACM,MAAM,CAACC,IAAI,CAACH,KAAK,CAAC,CAAC4B,MAAM,CAAC,UAAUC,YAAY,EAAEC,SAAS,EAAE;QACpF,IAAIV,QAAQ,IAAIS,YAAY,CAACE,cAAc,CAACD,SAAS,CAAC,EAAE;UACtDH,UAAU,CAACK,IAAI,CAACF,SAAS,CAAC;UAC1B,OAAO5B,MAAM,CAAC+B,MAAM,CAACJ,YAAY,EAAElC,eAAe,CAAC,CAAC,CAAC,EAAEmC,SAAS,EAAE9B,KAAK,CAAC8B,SAAS,CAAC,CAAC,CAAC;QACtF;QACAhB,OAAO,CAACC,IAAI,CAAC,aAAa,CAACG,MAAM,CAACY,SAAS,EAAE,sCAAsC,CAAC,CAAC;QACrF,OAAOD,YAAY;MACrB,CAAC,EAAEhC,WAAW,CAAC,CAAC;MAChB,IAAI8B,UAAU,CAAClB,MAAM,KAAK,CAAC,EAAE;QAC3BK,OAAO,CAACC,IAAI,CAAC,kCAAkC,CAAC;QAChD,OAAO,KAAK;MACd;MACAhB,UAAU,CAACF,WAAW,EAAEmB,eAAe,CAAC;MACxC,OAAO,IAAI;IACb,CAAC;IACD;IACAkB,oBAAoB,EAAE,SAASA,oBAAoB,GAAG;MACpD,OAAOpC,IAAI,CAACO,EAAE,CAAC;MACf,OAAO,IAAI;IACb;EACF,CAAC;AACH"}]}