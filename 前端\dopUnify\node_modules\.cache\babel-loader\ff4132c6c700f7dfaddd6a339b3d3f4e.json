{"remainingRequest": "D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\node_modules\\thread-loader\\dist\\cjs.js!D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\node_modules\\babel-loader\\lib\\index.js!D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\src\\layout\\components\\Sublicense\\index.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\src\\layout\\components\\Sublicense\\index.vue", "mtime": 1703583640602}, {"path": "D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\babel.config.js", "mtime": 1686019818359}, {"path": "D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\.babelrc", "mtime": 1705285078668}, {"path": "D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\node_modules\\thread-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1655715099000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:aW1wb3J0IF9yZWdlbmVyYXRvclJ1bnRpbWUgZnJvbSAiRDovMV9Qcm9qZWN0L1hZRF9Qcm9qZWN0L2RvcC00LjAvZG9wLTQuMS1xaWFuZHVhbi11bmlmeS9kb3BVbmlmeS9ub2RlX21vZHVsZXMvQGJhYmVsL3J1bnRpbWUvaGVscGVycy9lc20vcmVnZW5lcmF0b3JSdW50aW1lLmpzIjsKaW1wb3J0IF9hc3luY1RvR2VuZXJhdG9yIGZyb20gIkQ6LzFfUHJvamVjdC9YWURfUHJvamVjdC9kb3AtNC4wL2RvcC00LjEtcWlhbmR1YW4tdW5pZnkvZG9wVW5pZnkvbm9kZV9tb2R1bGVzL0BiYWJlbC9ydW50aW1lL2hlbHBlcnMvZXNtL2FzeW5jVG9HZW5lcmF0b3IuanMiOwppbXBvcnQgImNvcmUtanMvbW9kdWxlcy9lcy5vYmplY3QudG8tc3RyaW5nLmpzIjsKaW1wb3J0ICJjb3JlLWpzL21vZHVsZXMvd2ViLmRvbS1jb2xsZWN0aW9ucy5mb3ItZWFjaC5qcyI7CmltcG9ydCAiY29yZS1qcy9tb2R1bGVzL2VzLmFycmF5LmpvaW4uanMiOwppbXBvcnQgImNvcmUtanMvbW9kdWxlcy9lcy5hcnJheS5jb25jYXQuanMiOwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwoKaW1wb3J0IHsgZGljdGlvbmFyeUZpZWRzIH0gZnJvbSAnQC91dGlscy9kaWN0aW9uYXJ5LmpzJzsgLy8g5a2X5YW45bi46YePCmltcG9ydCB7IGNvbmZpZywgY29uZmlnUHNkIH0gZnJvbSAnLi9pbmZvJzsgLy8g6KGo5aS044CB6KGo5Y2V6YWN572uCgppbXBvcnQgeyBjb21tb25Nc2dXYXJuLCBjb21tb25Nc2dDb25maXJtLCBjb21tb25Nc2dTdWNjZXNzIH0gZnJvbSAnQC91dGlscy9tZXNzYWdlLmpzJzsgLy8g5o+Q56S65L+h5oGvCmltcG9ydCB7IGVuY3J5cHRSZXN1bHQgfSBmcm9tICdAL3V0aWxzL2NyeXB0byc7IC8vIOWKoOWvhgppbXBvcnQgeyBmb3JtYXRUaW1lIH0gZnJvbSAnQC91dGlscy9kYXRlLmpzJzsKaW1wb3J0IHsgQ29tbW9uIH0gZnJvbSAnQC9hcGknOwp2YXIgZ2V0VXNlckluZm8gPSBDb21tb24uZ2V0VXNlckluZm8sCiAgcHNkVmFsaWQgPSBDb21tb24ucHNkVmFsaWQsCiAgbm9hcHByb3ZlZCA9IENvbW1vbi5ub2FwcHJvdmVkLAogIHN1YkxpY2Vuc2UgPSBDb21tb24uc3ViTGljZW5zZTsKZXhwb3J0IGRlZmF1bHQgewogIG5hbWU6ICdTdWJsaWNlbnNlJywKICBkYXRhOiBmdW5jdGlvbiBkYXRhKCkgewogICAgcmV0dXJuIHsKICAgICAgYnRuQWxsOiB7CiAgICAgICAgLy8g5b2T5YmN6aG16ZyA6KaB6YWN572u5p2D6ZmQ55qE5oyJ6ZKuICDmnYPpmZDojrflj5YKICAgICAgfSwKICAgICAgYnRuRGF0YXM6IHsKICAgICAgICAvLyDmjInpkq7phY3nva4KICAgICAgICBidG5TYXZlOiB7CiAgICAgICAgICBzaG93OiB0cnVlCiAgICAgICAgfSwKICAgICAgICBidG5DYW5jbGU6IHsKICAgICAgICAgIHNob3c6IHRydWUKICAgICAgICB9CiAgICAgIH0sCiAgICAgIGRpYWxvZzogewogICAgICAgIGNvbXBvbmVudFByb3BzOiB7CiAgICAgICAgICAvLyDlvLnlh7rmoYbphY3nva7lsZ7mgKcKICAgICAgICAgIHdpZHRoOiAnNTByZW0nLAogICAgICAgICAgdGl0bGU6ICfovazmjojmnYMnCiAgICAgICAgfSwKICAgICAgICB2aXNpYmxlOiBmYWxzZSwKICAgICAgICBmb3JtOiB7CiAgICAgICAgICBjb25maWc6IGNvbmZpZyh0aGlzKSwKICAgICAgICAgIGxhYmVsV2lkdGg6ICcxMHJlbScsCiAgICAgICAgICAvLyDlvZPliY3ooajljZXmoIfnrb7lrr3luqbphY3nva4KICAgICAgICAgIGRlZmF1bHRGb3JtOiB7CiAgICAgICAgICAgIHVzZXI6IHRoaXMuJHN0b3JlLmdldHRlcnMudXNlck5vLAogICAgICAgICAgICAvLyDmjojmnYPnlKjmiLcKICAgICAgICAgICAgcHNkOiAnJywKICAgICAgICAgICAgLy8g55So5oi35a+G56CBCiAgICAgICAgICAgIHRhcmdldDogJycsCiAgICAgICAgICAgIC8vIOebruagh+eUqOaItwogICAgICAgICAgICByZWFzb246ICcnLAogICAgICAgICAgICAvLyDmjojmnYPljp/lm6AKICAgICAgICAgICAgYXV0aG9yX3R5cGU6ICcnLAogICAgICAgICAgICAvLyDmjojmnYPmlrnlvI8KICAgICAgICAgICAgZGF0ZTogJycsCiAgICAgICAgICAgIC8vIOaXpeacn+WMuumXtAogICAgICAgICAgICByZW1hcms6ICcnIC8vIOWkh+azqAogICAgICAgICAgfQogICAgICAgIH0KICAgICAgfSwKCiAgICAgIGRpYWxvZ1Zpc2libGU6IGZhbHNlLAogICAgICAvLyDnm67moIfkurrlkZjpgInmi6nlvLnmoYYKICAgICAgdHJlZURhdGE6IFtdLAogICAgICAvLyDlt6bkvqfmoJEKICAgICAgZGVmYXVsdFByb3BzOiB7CiAgICAgICAgY2hpbGRyZW46ICdjaGlsZHJlbicsCiAgICAgICAgbGFiZWw6ICdsYWJlbCcKICAgICAgfSwKICAgICAgZGVmYXVsdElkOiB0aGlzLiRzdG9yZS5nZXR0ZXJzLm9yZ2FuTm8sCiAgICAgIC8vIOm7mOiupOmAieS4reiKgueCuQogICAgICB0aXRsZXM6IFsn5Y+v6YCJ5Lq65ZGYJywgJ+mAieS4reS6uuWRmCddLAogICAgICBzZWFyY2hWYWw6ICcnLAogICAgICAvLyDmkJzntKLmoYbnmoTlgLwKICAgICAgcm9sZURhdGE6IFtdLAogICAgICAvLyDnqb/moq3moYblt6bovrnnmoTlgLwKICAgICAgcm9sZVZhbHVlOiBbXSwKICAgICAgLy8g56m/5qKt5qGG5Y+z6L6555qE5YC8CiAgICAgIGRpYWxvZ1BzZDogewogICAgICAgIC8vIOebruagh+eUqOaIt+WvhueggeW8ueahhgogICAgICAgIGNvbXBvbmVudFByb3BzOiB7CiAgICAgICAgICAvLyDlvLnlh7rmoYbphY3nva7lsZ7mgKcKICAgICAgICAgIHdpZHRoOiAnNTByZW0nLAogICAgICAgICAgdGl0bGU6ICflr4bnoIHmoKHpqownCiAgICAgICAgfSwKICAgICAgICB2aXNpYmxlOiBmYWxzZSwKICAgICAgICBmb3JtOiB7CiAgICAgICAgICBjb25maWc6IGNvbmZpZ1BzZCh0aGlzKSwKICAgICAgICAgIGxhYmVsV2lkdGg6ICcxNHJlbScsCiAgICAgICAgICAvLyDlvZPliY3ooajljZXmoIfnrb7lrr3luqbphY3nva4KICAgICAgICAgIGRlZmF1bHRGb3JtOiB7CiAgICAgICAgICAgIHBhc3N3b3JkOiB0aGlzLiRzdG9yZS5nZXR0ZXJzLmVtcHR5UHN3ZCAvLyDnm67moIfnlKjmiLflr4bnoIEKICAgICAgICAgIH0KICAgICAgICB9CiAgICAgIH0sCgogICAgICBTdWNjZXNzZGlhbG9nOiBmYWxzZSwKICAgICAgLy8g6L2s5o6I5p2D5oiQ5Yqf5o+Q56S65by556qXCiAgICAgIFNlbmNvbmQ6IDUgLy8g6K6+572u5Yid5aeL5YCS6K6h5pe2CiAgICB9OwogIH0sCgogIGNvbXB1dGVkOiB7fSwKICB3YXRjaDogewogICAgLy8gZGlhbG9nVmlzaWJsZSh2YWwpIHsKICAgIC8vICAgLy8g5by55qGG5pi+56S6LOa4heepuui9rOaOiOadg+W8ueahhuWPs+S+p+WSjOi+k+WFpeahhueahOWAvAogICAgLy8gICBpZiAodmFsKSB7CiAgICAvLyAgICAgdGhpcy5zZWFyY2hWYWwgPSAnJwogICAgLy8gICAgIHRoaXMucm9sZVZhbHVlID0gW10KICAgIC8vICAgfQogICAgLy8gfSwKICAgIHJvbGVWYWx1ZTogZnVuY3Rpb24gcm9sZVZhbHVlKHZhbCkgewogICAgICBpZiAodmFsLmxlbmd0aCA+IDEpIHsKICAgICAgICB2YWwuc2hpZnQoKTsKICAgICAgfQogICAgfQogIH0sCiAgY3JlYXRlZDogZnVuY3Rpb24gY3JlYXRlZCgpIHt9LAogIG1vdW50ZWQ6IGZ1bmN0aW9uIG1vdW50ZWQoKSB7fSwKICBtZXRob2RzOiB7CiAgICAvKioNCiAgICAgKiDovazmjojmnYPlvLnmoYbmmL7npLoqLwogICAgZGlhbG9nU2hvdzogZnVuY3Rpb24gZGlhbG9nU2hvdygpIHsKICAgICAgdmFyIF90aGlzID0gdGhpczsKICAgICAgdGhpcy5kaWFsb2cudmlzaWJsZSA9IHRydWU7CiAgICAgIHRoaXMuJG5leHRUaWNrKGZ1bmN0aW9uICgpIHsKICAgICAgICBfdGhpcy5kaWFsb2cuZm9ybS5jb25maWcucmVhc29uLm9wdGlvbnMgPSBkaWN0aW9uYXJ5RmllZHMoJ1NVQkxJQ0VOU0VfUkVBU09OJyk7IC8vIOaOiOadg+WOn+WboAogICAgICAgIF90aGlzLmRpYWxvZy5mb3JtLmNvbmZpZy5hdXRob3JfdHlwZS5vcHRpb25zID0gZGljdGlvbmFyeUZpZWRzKCdTVUJMSUNFTlNFX0FVVEhPUl9UWVBFJyk7IC8vIOaOiOadg+aWueW8jwogICAgICB9KTsKICAgIH0sCiAgICAvKioNCiAgICAgKiDovazmjojmnYPpgInmi6nlvLnmoYbmmL7npLoqLwogICAgZWxEaWFsb2dTaG93OiBmdW5jdGlvbiBlbERpYWxvZ1Nob3coKSB7CiAgICAgIHZhciBfdGhpczIgPSB0aGlzOwogICAgICB0aGlzLmRpYWxvZ1Zpc2libGUgPSB0cnVlOwogICAgICB0aGlzLm9yZ2FuVHJlZUdldCgpOyAvLyDliqDovb3mnLrmnoTmoJEKICAgICAgdGhpcy4kbmV4dFRpY2soZnVuY3Rpb24gKCkgewogICAgICAgIF90aGlzMi4kcmVmcy50cmVlLnNldEN1cnJlbnRLZXkoX3RoaXMyLiRzdG9yZS5nZXR0ZXJzLm9yZ2FuTm8pOyAvLyDpu5jorqTpgInkuK3oioLngrkKICAgICAgICBfdGhpczIuZ2V0Q3VycmVudE5vZGUoX3RoaXMyLiRzdG9yZS5nZXR0ZXJzLm9yZ2FuTm8pOyAvLyDpu5jorqTlsZXnpLrlvZPliY3nlKjmiLfmnLrmnoTlj7fljLnphY3liLDnmoTnlKjmiLcKICAgICAgfSk7CiAgICB9LAogICAgLyoqDQogICAgICog6YWN572u5py65p6E5qCRKi8KICAgIG9yZ2FuVHJlZUdldDogZnVuY3Rpb24gb3JnYW5UcmVlR2V0KCkgewogICAgICB0aGlzLnRyZWVEYXRhID0gdGhpcy4kc3RvcmUuZ2V0dGVycy5vcmdhblRyZWU7CiAgICB9LAogICAgLyoqDQogICAgICog5pCc57Si5qGGKi8KICAgIGhhbmRsZVNlYXJjaDogZnVuY3Rpb24gaGFuZGxlU2VhcmNoKCkgewogICAgICB2YXIgX3RoaXMzID0gdGhpczsKICAgICAgLy8g5qC55o2u55So5oi35Y+35oiW55So5oi35ZCN5p+l6K+i55So5oi35L+h5oGvCiAgICAgIHZhciBtc2cgPSB7CiAgICAgICAgcGFyYW1ldGVyTGlzdDogW3t9XSwKICAgICAgICBzeXNNYXA6IHsKICAgICAgICAgIHJvbGVfaWQ6ICcnLAogICAgICAgICAgb3JnX2lkOiAnJywKICAgICAgICAgIHVzZXJfbm86IHRoaXMuc2VhcmNoVmFsLAogICAgICAgICAgb3JnX3RwOiAnJwogICAgICAgIH0KICAgICAgfTsKICAgICAgZ2V0VXNlckluZm8obXNnKS50aGVuKGZ1bmN0aW9uIChyZXMpIHsKICAgICAgICB2YXIgdXNlcnMgPSByZXMucmV0TWFwLnVzZXJzOwogICAgICAgIF90aGlzMy5yb2xlRGF0YSA9IFtdOwogICAgICAgIHVzZXJzLmZvckVhY2goZnVuY3Rpb24gKGl0ZW0pIHsKICAgICAgICAgIF90aGlzMy5yb2xlRGF0YS5wdXNoKHsKICAgICAgICAgICAga2V5OiBpdGVtLnVzZXJfbm8sCiAgICAgICAgICAgIGxhYmVsOiBpdGVtLnVzZXJfbmFtZQogICAgICAgICAgfSk7CiAgICAgICAgfSk7CiAgICAgIH0pOwogICAgfSwKICAgIC8qKg0KICAgICAqIOmAieS4reiKgueCuQ0KICAgICAqIEBwYXJhbSB7T2JqZWN0fWRhdGEg6YCJ5Lit6KGM5pWw5o2uDQogICAgICovCiAgICBnZXRDdXJyZW50Tm9kZTogZnVuY3Rpb24gZ2V0Q3VycmVudE5vZGUoZGF0YSkgewogICAgICB2YXIgX3RoaXM0ID0gdGhpczsKICAgICAgLy8g5qC55o2u5py65p6E5Y+35p+l6K+i55So5oi35L+h5oGvCiAgICAgIHZhciBtc2cgPSB7CiAgICAgICAgcGFyYW1ldGVyTGlzdDogW3t9XSwKICAgICAgICBzeXNNYXA6IHsKICAgICAgICAgIHJvbGVfaWQ6ICcnLAogICAgICAgICAgb3JnX2lkOiBkYXRhLmlkLAogICAgICAgICAgdXNlcl9ubzogJycsCiAgICAgICAgICBvcmdfdHA6ICcnCiAgICAgICAgfQogICAgICB9OwogICAgICBnZXRVc2VySW5mbyhtc2cpLnRoZW4oZnVuY3Rpb24gKHJlcykgewogICAgICAgIHZhciB1c2VycyA9IHJlcy5yZXRNYXAudXNlcnM7CiAgICAgICAgX3RoaXM0LnJvbGVEYXRhID0gW107CiAgICAgICAgdXNlcnMuZm9yRWFjaChmdW5jdGlvbiAoaXRlbSkgewogICAgICAgICAgX3RoaXM0LnJvbGVEYXRhLnB1c2goewogICAgICAgICAgICBrZXk6IGl0ZW0udXNlcl9ubywKICAgICAgICAgICAgbGFiZWw6IGl0ZW0udXNlcl9uYW1lCiAgICAgICAgICB9KTsKICAgICAgICB9KTsKICAgICAgfSk7CiAgICB9LAogICAgLyoqDQogICAgICog56m/5qKt5qGG5bem5L6n6YCJ5Lit5LqL5Lu2DQogICAgICogQHBhcmFtIHtBcnJheX0gdmFsdWUg6YCJ5Lit5YC8DQogICAgICovCiAgICBsZWZ0QzogZnVuY3Rpb24gbGVmdEModmFsdWUpIHsKICAgICAgaWYgKHZhbHVlLmxlbmd0aCA+IDEpIHsKICAgICAgICB0aGlzLiRyZWZzLnRyYW5zZmVyLiRjaGlsZHJlblswXS5jaGVja2VkLnNoaWZ0KCk7CiAgICAgIH0KICAgIH0sCiAgICAvKioNCiAgICAgKijnm67moIfkurrlkZjlvLnmoYYpLeaMiemSri3kv53lrZgqLwogICAgaGFuZGxlU2F2ZTogZnVuY3Rpb24gaGFuZGxlU2F2ZSgpIHsKICAgICAgdGhpcy5kaWFsb2cuZm9ybS5kZWZhdWx0Rm9ybS50YXJnZXQgPSB0aGlzLnJvbGVWYWx1ZS5qb2luKCk7CiAgICAgIHRoaXMuZGlhbG9nVmlzaWJsZSA9IGZhbHNlOyAvLyDnm67moIfkurrlkZjlvLnmoYblhbPpl60KICAgIH0sCiAgICAvKioNCiAgICAgKijnm67moIfkurrlkZjlvLnmoYYpLeaMiemSri3lj5bmtogqLwogICAgaGFuZGxlQ2FuY2xlOiBmdW5jdGlvbiBoYW5kbGVDYW5jbGUoKSB7CiAgICAgIHRoaXMuZGlhbG9nVmlzaWJsZSA9IGZhbHNlOyAvLyDnm67moIfkurrlkZjlvLnmoYblhbPpl60KICAgIH0sCiAgICAvKioNCiAgICAgKiDovazmjojmnYPlvLnmoYYt5oyJ6ZKuLS3noa7lrpoqLwogICAgZGlhbG9nU3VtYml0OiBmdW5jdGlvbiBkaWFsb2dTdW1iaXQoKSB7CiAgICAgIHZhciBfdGhpczUgPSB0aGlzOwogICAgICB2YXIgX3RoaXMkZGlhbG9nJGZvcm0kZGVmID0gdGhpcy5kaWFsb2cuZm9ybS5kZWZhdWx0Rm9ybSwKICAgICAgICB0YXJnZXQgPSBfdGhpcyRkaWFsb2ckZm9ybSRkZWYudGFyZ2V0LAogICAgICAgIHVzZXIgPSBfdGhpcyRkaWFsb2ckZm9ybSRkZWYudXNlcjsKICAgICAgaWYgKHVzZXIgPT09IHRhcmdldCkgewogICAgICAgIGNvbW1vbk1zZ1dhcm4oJ+aOiOadg+eUqOaIt+WSjOaOiOadg+WvueixoeeUqOaIt+ebuOWQjO+8jOS4jeiDvei9rOaOiOadgycsIHRoaXMpOwogICAgICAgIHJldHVybjsKICAgICAgfQogICAgICAvLyDlhYjov5vmgKflr4bnoIHmo4DpqowKICAgICAgdmFyIG1zZyA9IHsKICAgICAgICBwYXJhbWV0ZXJMaXN0OiBbXSwKICAgICAgICBvcGVyX3R5cGU6ICdxdWVyeVVzZXInLAogICAgICAgIHVzZXJfbm86IHRoaXMuZGlhbG9nLmZvcm0uZGVmYXVsdEZvcm0udXNlciwKICAgICAgICBwYXNzV29yZDogZW5jcnlwdFJlc3VsdCh0aGlzLiRzdG9yZS5nZXR0ZXJzLmluaXRQYXJhbXMuZW5TZWNNYXAuZW5jcnlwdFR5cGUsIHRoaXMuZGlhbG9nLmZvcm0uZGVmYXVsdEZvcm0ucHNkKQogICAgICB9OwogICAgICBwc2RWYWxpZChtc2cpLnRoZW4oZnVuY3Rpb24gKHJlcykgewogICAgICAgIGlmIChyZXMucmV0Q29kZSA9PT0gJzIwMCcpIHsKICAgICAgICAgIC8vIOafpeivouW9k+WJjeeUqOaIt+aYr+WQpuacieW+heWuoeaJueS7u+WKoQogICAgICAgICAgdmFyIG1zZzIgPSB7CiAgICAgICAgICAgIHBhcmFtZXRlckxpc3Q6IFtdLAogICAgICAgICAgICB1c2VyX25vOiBfdGhpczUuJHN0b3JlLmdldHRlcnMudXNlck5vLAogICAgICAgICAgICBvcmdhbl9ubzogX3RoaXM1LiRzdG9yZS5nZXR0ZXJzLm9yZ2FuTm8KICAgICAgICAgIH07CiAgICAgICAgICBub2FwcHJvdmVkKG1zZzIpLnRoZW4oZnVuY3Rpb24gKHJlczIpIHsKICAgICAgICAgICAgLy8gaGF2ZVRhc2sg5Li6MeaXtuS7o+ihqOWtmOWcqOW+heWuoeaguOS7u+WKoe+8jOS4ujDliJnooajnpLrmsqHmnInlvoXlrqHmoLjku7vliqEKICAgICAgICAgICAgLy8g5pyJ5a6h5om55Lu75YqhICDmj5DnpLrlvZPliY3nlKjmiLfmnInlvoXlrqHmibnku7vliqHmmK/lkKbov5vooYzovazmjojmnYMgICDmjojmnYPor7fmsYIgIDEu5pys5Zyw5a6h5qC477yI5pys5Zyw6L6T5YWl5o6I5p2D55So5oi35a+G56CB77yJIDIu6L+c56iL5a6h5qC477yI5Y+R6LW35rWBIOeoi++8iQogICAgICAgICAgICBpZiAocmVzMi5yZXRNYXAuaGF2ZVRhc2sgPT09ICcxJykgewogICAgICAgICAgICAgIGNvbW1vbk1zZ0NvbmZpcm0oJ+W9k+WJjeeUqOaIt+acieW+heWuoeaJueS7u+WKoeaYr+WQpui/m+ihjOi9rOaOiOadgycsIF90aGlzNSwgZnVuY3Rpb24gKHBhcmFtKSB7CiAgICAgICAgICAgICAgICBpZiAocGFyYW0pIHsKICAgICAgICAgICAgICAgICAgX3RoaXM1LmFwcHJvdmVkKCk7CiAgICAgICAgICAgICAgICB9CiAgICAgICAgICAgICAgfSk7CiAgICAgICAgICAgIH0gZWxzZSB7CiAgICAgICAgICAgICAgLy8g5peg5a6h5om55Lu75YqhICAg5o6I5p2D6K+35rGCICAxLuacrOWcsOWuoeaguO+8iOacrOWcsOi+k+WFpeaOiOadg+eUqOaIt+Wvhuegge+8iSAyLui/nOeoi+WuoeaguO+8iOWPkei1t+a1geeoi++8iQogICAgICAgICAgICAgIF90aGlzNS5hcHByb3ZlZCgpOwogICAgICAgICAgICB9CiAgICAgICAgICB9KTsKICAgICAgICB9CiAgICAgIH0pOwogICAgfSwKICAgIC8vIOi9rOaOiOadgyDmnKzlnLDlrqHmoLjlkozov5znqIvlrqHmoLjmlrnms5UgICAxLuacrOWcsOWuoeaguO+8iOacrOWcsOi+k+WFpeaOiOadg+eUqOaIt+Wvhuegge+8iSAyLui/nOeoi+WuoeaguO+8iOWPkei1t+a1geeoi++8iQogICAgYXBwcm92ZWQ6IGZ1bmN0aW9uIGFwcHJvdmVkKCkgewogICAgICAvLyDmnKzlnLDlrqHmoLggICDlvLnlr4bmjojmnYPnlKjmiLfnoIHovpPlhaXmoYYKICAgICAgaWYgKHRoaXMuZGlhbG9nLmZvcm0uZGVmYXVsdEZvcm0uYXV0aG9yX3R5cGUgPT09ICcxJykgewogICAgICAgIHRoaXMuZGlhbG9nUHNkLnZpc2libGUgPSB0cnVlOwogICAgICB9IGVsc2UgewogICAgICAgIC8vIOi/nOeoi+aOiOadgwogICAgICAgIHRoaXMuZW1wb3dlcigpOwogICAgICB9CiAgICB9LAogICAgLyoqDQogICAgICog55uu5qCH55So5oi35a+G56CB5qCh6aqM5by55Ye65qGGIC0g56Gu5a6aKi8KICAgIGRpYWxvZ1N1bWJpdFBzZDogZnVuY3Rpb24gZGlhbG9nU3VtYml0UHNkKCkgewogICAgICB2YXIgX3RoaXM2ID0gdGhpczsKICAgICAgdmFyIG1zZyA9IHsKICAgICAgICBwYXJhbWV0ZXJMaXN0OiBbXSwKICAgICAgICB1c2VyX25vOiB0aGlzLmRpYWxvZy5mb3JtLmRlZmF1bHRGb3JtLnRhcmdldCwKICAgICAgICBwYXNzV29yZDogZW5jcnlwdFJlc3VsdCh0aGlzLiRzdG9yZS5nZXR0ZXJzLmluaXRQYXJhbXMuZW5TZWNNYXAuZW5jcnlwdFR5cGUsIHRoaXMuZGlhbG9nUHNkLmZvcm0uZGVmYXVsdEZvcm0ucGFzc3dvcmQpCiAgICAgIH07CiAgICAgIC8vIOWvhueggeagoemqjCAgIOWGjei/m+ihjOaOiOadg+ivt+axggogICAgICBwc2RWYWxpZChtc2cpLnRoZW4oZnVuY3Rpb24gKHJlcykgewogICAgICAgIGlmIChyZXMucmV0Q29kZSA9PT0gJzIwMCcpIHsKICAgICAgICAgIC8vIOWPkei1t+aOiOadg+ivt+axggogICAgICAgICAgX3RoaXM2LmVtcG93ZXIoKTsKICAgICAgICB9CiAgICAgIH0pOwogICAgfSwKICAgIC8vIOi9rOaOiOadg+ivt+axggogICAgZW1wb3dlcjogZnVuY3Rpb24gZW1wb3dlcigpIHsKICAgICAgdmFyIF90aGlzNyA9IHRoaXM7CiAgICAgIC8vIOagh+mimCDvvIjmqKHmnb/lkI3np7DvvIjml7bpl7TvvInvvIkKICAgICAgdmFyIHRpbWUgPSBmb3JtYXRUaW1lKG5ldyBEYXRlKCksICd5eXl5LU1NLWRkIGhoOm1tOnNzJyk7CiAgICAgIHZhciBpbnN0X25hbWUgPSAiXHU4RjZDXHU2Mzg4XHU2NzQzXHU2QTIxXHU2NzdGKCIuY29uY2F0KHRoaXMuJHN0b3JlLmdldHRlcnMudXNlck5hbWUsICIgIikuY29uY2F0KHRpbWUsICIpIik7CiAgICAgIHZhciBfdGhpcyRkaWFsb2ckZm9ybSRkZWYyID0gdGhpcy5kaWFsb2cuZm9ybS5kZWZhdWx0Rm9ybSwKICAgICAgICB1c2VyID0gX3RoaXMkZGlhbG9nJGZvcm0kZGVmMi51c2VyLAogICAgICAgIHRhcmdldCA9IF90aGlzJGRpYWxvZyRmb3JtJGRlZjIudGFyZ2V0LAogICAgICAgIHJlYXNvbiA9IF90aGlzJGRpYWxvZyRmb3JtJGRlZjIucmVhc29uLAogICAgICAgIGF1dGhvcl90eXBlID0gX3RoaXMkZGlhbG9nJGZvcm0kZGVmMi5hdXRob3JfdHlwZSwKICAgICAgICBkYXRlID0gX3RoaXMkZGlhbG9nJGZvcm0kZGVmMi5kYXRlLAogICAgICAgIHJlbWFyayA9IF90aGlzJGRpYWxvZyRmb3JtJGRlZjIucmVtYXJrOwogICAgICB2YXIgbXNnID0gdGhpcy5kaWFsb2cuZm9ybS5kZWZhdWx0Rm9ybS5hdXRob3JfdHlwZSA9PT0gJzEnID8gewogICAgICAgIHBhcmFtZXRlckxpc3Q6IFtdLAogICAgICAgIHVzZXJfbm86IHVzZXIsCiAgICAgICAgdGFyZ2V0X3VzZXJfbm86IHRhcmdldCwKICAgICAgICBzdGFydF9kYXRlOiBkYXRlWzBdLAogICAgICAgIGVuZF9kYXRlOiBkYXRlWzFdLAogICAgICAgIHN1YmxpY2Vuc2VfcmVhc29uOiByZWFzb24sCiAgICAgICAgYXV0aG9yX3R5cGU6IGF1dGhvcl90eXBlLAogICAgICAgIHJlbWFyazogcmVtYXJrCiAgICAgIH0gOiB7CiAgICAgICAgcGFyYW1ldGVyTGlzdDogW10sCiAgICAgICAgbW9kdWxlX2lkOiAnMjAyMDEyMjExMzQwNDYzMDgwMjknLAogICAgICAgIC8vIOaooeadv2lkIOi/nOeoi+WuoeaJueacieWPguaVsCAg5pys5Zyw5a6h5qC45pegCiAgICAgICAgaW5zdF9uYW1lOiBpbnN0X25hbWUsCiAgICAgICAgLy8g5qCH6aKYICDov5znqIvlrqHmibnmnInlj4LmlbAgIOacrOWcsOWuoeaguOaXoAogICAgICAgIHVzZXJfbm86IHVzZXIsCiAgICAgICAgdGFyZ2V0X3VzZXJfbm86IHRhcmdldCwKICAgICAgICBzdGFydF9kYXRlOiBkYXRlWzBdLAogICAgICAgIGVuZF9kYXRlOiBkYXRlWzFdLAogICAgICAgIHN1YmxpY2Vuc2VfcmVhc29uOiByZWFzb24sCiAgICAgICAgYXV0aG9yX3R5cGU6IGF1dGhvcl90eXBlLAogICAgICAgIHJlbWFyazogcmVtYXJrCiAgICAgIH07CiAgICAgIHN1YkxpY2Vuc2UobXNnKS50aGVuKGZ1bmN0aW9uIChyZXMpIHsKICAgICAgICBpZiAoX3RoaXM3LmRpYWxvZy5mb3JtLmRlZmF1bHRGb3JtLmF1dGhvcl90eXBlID09PSAnMScpIHsKICAgICAgICAgIC8vIOacrOWcsOaOiOadgyAg5by55qGG6Lez6L2s55m75b2V6aG1CiAgICAgICAgICBfdGhpczcuZGlhbG9nLnZpc2libGUgPSBmYWxzZTsKICAgICAgICAgIF90aGlzNy5kaWFsb2dQc2QudmlzaWJsZSA9IGZhbHNlOwogICAgICAgICAgX3RoaXM3LlN1Y2Nlc3NkaWFsb2cgPSB0cnVlOwogICAgICAgICAgdmFyIGludGVydmFsID0gc2V0SW50ZXJ2YWwoZnVuY3Rpb24gKCkgewogICAgICAgICAgICBfdGhpczcuU2VuY29uZC0tOwogICAgICAgICAgICBpZiAoX3RoaXM3LlNlbmNvbmQgPT09IDApIHsKICAgICAgICAgICAgICB3aW5kb3cuY2xlYXJJbnRlcnZhbChpbnRlcnZhbCk7CiAgICAgICAgICAgICAgX3RoaXM3LmxvZ291dCgpOyAvLyDlgJLorqHml7bnu5PmnZ/ml7bot7PovaznmbvlvZXpobXpnaIKICAgICAgICAgICAgfQogICAgICAgICAgfSwgMTAwMCk7CiAgICAgICAgfSBlbHNlIHsKICAgICAgICAgIC8vIOi/nOeoi+WuoeaguAogICAgICAgICAgX3RoaXM3LmRpYWxvZy52aXNpYmxlID0gZmFsc2U7CiAgICAgICAgICBfdGhpczcuZGlhbG9nUHNkLnZpc2libGUgPSBmYWxzZTsKICAgICAgICAgIGNvbW1vbk1zZ1N1Y2Nlc3MoJ+a1geeoi+WPkei1t+aIkOWKnycsIF90aGlzNyk7CiAgICAgICAgfQogICAgICB9KTsKICAgIH0sCiAgICAvKioNCiAgICAgKiDot7PovaznmbvlvZXpobXpnaLmlrnms5UqLwogICAgbG9nb3V0OiBmdW5jdGlvbiBsb2dvdXQoKSB7CiAgICAgIHZhciBfdGhpczggPSB0aGlzOwogICAgICByZXR1cm4gX2FzeW5jVG9HZW5lcmF0b3IoIC8qI19fUFVSRV9fKi9fcmVnZW5lcmF0b3JSdW50aW1lKCkubWFyayhmdW5jdGlvbiBfY2FsbGVlKCkgewogICAgICAgIHJldHVybiBfcmVnZW5lcmF0b3JSdW50aW1lKCkud3JhcChmdW5jdGlvbiBfY2FsbGVlJChfY29udGV4dCkgewogICAgICAgICAgd2hpbGUgKDEpIHsKICAgICAgICAgICAgc3dpdGNoIChfY29udGV4dC5wcmV2ID0gX2NvbnRleHQubmV4dCkgewogICAgICAgICAgICAgIGNhc2UgMDoKICAgICAgICAgICAgICAgIF9jb250ZXh0Lm5leHQgPSAyOwogICAgICAgICAgICAgICAgcmV0dXJuIF90aGlzOC4kc3RvcmUuZGlzcGF0Y2goJ3VzZXIvbG9nb3V0Jyk7CiAgICAgICAgICAgICAgY2FzZSAyOgogICAgICAgICAgICAgICAgX3RoaXM4LiRyb3V0ZXIucHVzaCgiL2xvZ2luP3JlZGlyZWN0PSIuY29uY2F0KF90aGlzOC4kcm91dGUuZnVsbFBhdGgpKTsKICAgICAgICAgICAgICBjYXNlIDM6CiAgICAgICAgICAgICAgY2FzZSAiZW5kIjoKICAgICAgICAgICAgICAgIHJldHVybiBfY29udGV4dC5zdG9wKCk7CiAgICAgICAgICAgIH0KICAgICAgICAgIH0KICAgICAgICB9LCBfY2FsbGVlKTsKICAgICAgfSkpKCk7CiAgICB9LAogICAgLyoqDQogICAgICog55uu5qCH55So5oi35a+G56CB5qCh6aqM5by55Ye65qGGIC0g5YWz6ZetKi8KICAgIGRpYWxvZ0Nsb3NlUHNkOiBmdW5jdGlvbiBkaWFsb2dDbG9zZVBzZCgpIHsKICAgICAgdGhpcy5kaWFsb2dQc2QudmlzaWJsZSA9IGZhbHNlOwogICAgfSwKICAgIC8qKg0KICAgICAqIOi9rOaOiOadg+W8ueWHuuahhiAtIOWFs+mXrSovCiAgICBkaWFsb2dDbG9zZTogZnVuY3Rpb24gZGlhbG9nQ2xvc2UoKSB7CiAgICAgIHRoaXMuZGlhbG9nLnZpc2libGUgPSBmYWxzZTsKICAgIH0KICB9Cn07"}, null]}