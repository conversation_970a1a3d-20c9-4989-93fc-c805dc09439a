{"remainingRequest": "D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\node_modules\\thread-loader\\dist\\cjs.js!D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\node_modules\\babel-loader\\lib\\index.js!D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\src\\views\\home\\index.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\src\\views\\home\\index.vue", "mtime": 1688371436381}, {"path": "D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\babel.config.js", "mtime": 1686019818359}, {"path": "D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\node_modules\\thread-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1655715099000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, null]}