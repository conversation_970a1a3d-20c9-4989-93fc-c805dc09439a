package com.sunyard.etl.ocr.common;

public class OCRConstants {

	/**
	 * HTTP请求参数编码格式
	 */
	public static String MESSAGE_ENCODING = "UTF-8";

	/** 执行成功标识 0 */
	public static final String HANDLE_SUCCESS = "0";

	/** 执行失败标识 1 */
	public static final String HANDLE_FAIL = "1";


	/**
	 * 定义sunars数据源，与/SunETLExecute/config/db_config.xml中配置<pool name="ecm"> 的name保持一致
	 *
	 */
	public static final String DATA_SOURCE_SUNARS = "ADMS";

	/**
	 * 定义sunaos数据源，与/SunETLExecute/config/db_config.xml中配置<pool name="sunaos"> 的name保持一致
	 *
	 */
	public static final String DATA_SOURCE_SUNAOS = "sunaos";

	/**
	 * 定义sunirp数据源，与/SunETLExecute/config/db_config.xml中配置<pool name="ecm"> 的name保持一致
	 */
	public static final String DATA_SOURCE_SUNIRP = "SUNIRP";

	/**
	 * 定义一次最多同步一百条数据
	 */
	public final static Integer PAGE_MAX = 100;

	/**
	 * 清空目标表语句定义
	 */
	public static final String DELETE_SUNARS_SQL = "DELETE FROM ";

	/**
	 * 获取目标数据源数据
	 */
	public static final String SELECT_SUNIRP_SQL = "SELECT * FROM ";

	/**
	 * 获取目标数据源数据总数
	 */
	public static final String SELECT_COUNT_SUNIRP_SQL = "SELECT COUNT(*) FROM ";

	/**
	 * 机构版面信息表(IM_ORGAN_FORM_TB)
	 */
	public static final String TABLE_NAME_IM_ORGAN_FORM_TB = "IM_ORGAN_FORM_TB";

	/**
	 * 版面信息表(SM_FORM_INFO_TB)
	 */
	public static final String TABLE_NAME_SM_FORM_INFO_TB = "SM_FORM_INFO_TB";

	/**
	 * 页容量信息表（预留）(SM_PAGE_BASE_TB)
	 */
	public static final String TABLE_NAME_SM_PAGE_BASE_TB = "SM_PAGE_BASE_TB";

	/**
	 * 凭证种类信息表(SM_VOUCHER_INFO_TB)
	 */
	public static final String TABLE_NAME_SM_VOUCHER_INFO_TB = "SM_VOUCHER_INFO_TB";

	/**
	 * 每次获取自动监督任务的数量
	 */
	public static int QD_OCR_TASK_NUM = 100;


	/**
	 * 每次获取自动分析的任务数量
	 */
	public static int QD_OCR_ANALYZE_NUM = 100;

	/** OCR识别成功 */
	public static final String OCR_SUCCESS = "1";
	/** OCR识别失败 */
	public static final String OCR_FAIL = "0";


	/**
	 * 监督通过
	 */
	public static final String QD_TASK_STATE_SUCCESS = "3";

	/**
	 * 监督未通过，置为未处理
	 */
	public static final String QD_TASK_STATE_UNDO = "1";

	/**
	 * 自动监督成功
	 */
	public static final String AUTO_QD_STATE_SUCCESS = "1";

	/**
	 * 自动监督失败
	 */
	public static final String AUTO_QD_STATE_FAIL = "2";

}
