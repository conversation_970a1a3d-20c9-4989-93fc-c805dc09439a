package com.sunyard.etl.nps.dao;

import java.sql.SQLException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.sunyard.util.dbutil.DBHandler;

import com.sun.rowset.CachedRowSetImpl;
import com.sunyard.etl.nps.model.BpTmpbatchTb;
import com.sunyard.etl.nps.model.NpBusinessData;
import com.sunyard.etl.nps.model.NpImageData;
import com.sunyard.etl.nps.model.NpInputDef;
import com.sunyard.etl.nps.model.NpOutputDef;
import com.sunyard.etl.nps.orm.NpBusinessDataOrm;
import com.sunyard.etl.nps.orm.NpInputDefOrm;
import com.sunyard.etl.nps.orm.NpOutputDefOrm;
import com.sunyard.etl.system.common.Constants;
import com.sunyard.etl.system.orm.Orm;

public class InputNopaperDao {

	protected final Logger log = LoggerFactory.getLogger(getClass());
	private Orm<NpInputDef> npInputDefOrm = new NpInputDefOrm();
	private Orm<NpOutputDef> npOutputDefOrm = new NpOutputDefOrm();
	private Orm<NpBusinessData> npBusinessDataOrm = new NpBusinessDataOrm();

	/**
	 * 从无纸化业务信息表中删除数据
	 * 
	 * <AUTHOR> 2017年7月7日
	 * @param busiDataNo
	 * @return
	 */
	public boolean cleanImageData(String busiDataNo) {
		DBHandler dbHandler = new DBHandler(Constants.CONN_POOL_NAME);
		try {
			String sql = "DELETE FROM NP_IMAGE_DATA_TB T WHERE T.BUSI_DATA_NO IN ('"
					+ busiDataNo + "') ";
			dbHandler.execute(sql);
		} catch (SQLException e) {
			e.printStackTrace();
			return false;
		}
		return true;
	}

	/**
	 * 从无纸化业务信息表中删除数据
	 * 
	 * <AUTHOR> 2017年7月7日
	 * @param cleanByBusiDataNo
	 * @return
	 */
	public boolean cleanByBusiDataNo(String busiDataNo) {
		DBHandler dbHandler = new DBHandler(Constants.CONN_POOL_NAME);
		try {
			String sql = "DELETE FROM NP_BUSINESS_DATA_TB T WHERE T.BUSI_DATA_NO IN ('"
					+ busiDataNo + "') ";
			String sql2 = "DELETE FROM NP_IMAGE_DATA_TB T WHERE T.BUSI_DATA_NO IN ('"
					+ busiDataNo + "') ";
			int[] ii = dbHandler.executeAsBatch(sql, sql2);
			for (int i : ii) {
				if (i < 0 && i != -2)
					return false;
			}
		} catch (SQLException e) {
			e.printStackTrace();
			return false;
		}
		return true;
	}

	/**
	 * 根据无纸化ID从无纸化业务数据表中提取需要处理的日期
	 * 
	 * <AUTHOR> 2017年7月6日
	 * @param nopaperId
	 * @return
	 * @throws SQLException
	 */

	public List<String> getPendingDateList(String nopaperId) {
		DBHandler dbHandler = new DBHandler(Constants.CONN_POOL_NAME);
		CachedRowSetImpl rs = null;
		List<String> pendingDateList = new ArrayList<String>();
		String sql = "SELECT DISTINCT  OCCUR_DATE  FROM NP_BUSINESS_DATA_TB T  WHERE T.NOPAPER_ID = "
				+ nopaperId
				+ " AND T.FLAG = '0' AND T.ERROR_FLAG = '0'  ORDER BY OCCUR_DATE";

		log.info("获取nopaperId = " + nopaperId + "的无纸化业务待处理日期:" + sql);
		try {
			rs = dbHandler.queryRs(sql);
			while (rs.next()) {
				pendingDateList.add(rs.getString(1));
			}
		} catch (SQLException e) {
			e.printStackTrace();
			return null;
		}
		return pendingDateList;
	}

	/**
	 * 根据日期+无纸化ID从无纸化业务数据表中提取批次
	 * 
	 * <AUTHOR> 2017年7月6日
	 * @param nopaperId
	 * @return
	 * @throws SQLException
	 */

	public List<BpTmpbatchTb> getPendingBatchList(String occurDate,
			String nopaperId) {
		DBHandler dbHandler = new DBHandler(Constants.CONN_POOL_NAME);
		CachedRowSetImpl rs = null;
		List<BpTmpbatchTb> pendingBatchList = new ArrayList<BpTmpbatchTb>();
		String sql = "SELECT DISTINCT OCCUR_DATE,T.SITE_NO,T.OPERATOR_NO "
				+ "FROM NP_BUSINESS_DATA_TB T  " + "WHERE T.NOPAPER_ID ="
				+ nopaperId + " AND OCCUR_DATE =" + occurDate
				+ " AND T.FLAG = '0' AND T.ERROR_FLAG = '0'  ORDER BY OCCUR_DATE";

		log.info("提取nopaperId = " + nopaperId + "当前待处理的批次:" + sql);
		try {
			rs = dbHandler.queryRs(sql);
			while (rs.next()) {
				BpTmpbatchTb batch = new BpTmpbatchTb();
				batch.setOccurDate(rs.getString("OCCUR_DATE"));
				batch.setSiteNo(rs.getString("SITE_NO"));
				batch.setOperatorNo(rs.getString("OPERATOR_NO"));
				pendingBatchList.add(batch);
			}
		} catch (SQLException e) {
			e.printStackTrace();
			return null;
		}
		return pendingBatchList;
	}

	/**
	 * 根据无纸化ID获取差错业务
	 * 
	 * <AUTHOR> 2017年7月6日
	 * @param batch
	 * @return
	 * @throws SQLException
	 */

	public List<NpBusinessData> getPendingErrorBusinessData(String nopaperId)
			throws SQLException {
		DBHandler dbHandler = new DBHandler(Constants.CONN_POOL_NAME);
		CachedRowSetImpl rs = null;
		List<NpBusinessData> npBusinessDataList = new ArrayList<NpBusinessData>();
		String sql = "SELECT * FROM NP_BUSINESS_DATA_TB T WHERE T.NOPAPER_ID = ? AND T.ERROR_FLAG = 1 AND T.FLAG = '1' AND T.ADMS5 = 0";
		log.info("根据批次信息从无纸化业务表中取得业务信息:" + sql);
		rs = dbHandler.queryRs(sql, nopaperId);
		while (rs.next()) {
			NpBusinessData npBusinessData = npBusinessDataOrm.orm(rs);
			npBusinessDataList.add(npBusinessData);
		}
		return npBusinessDataList;
	}

	/**
	 * 
	 * @Title getNpFtpDataDate
	 * @Description 根据无纸化ID获取待处理的日期
	 * <AUTHOR> 2017年7月18日
	 * @param nopaperId
	 * @return
	 * @throws SQLException
	 */

	public List<String> getNpFtpDataDate(String nopaperId) throws SQLException {
		List<String> list = new ArrayList<String>();
		DBHandler dbHandler = new DBHandler(Constants.CONN_POOL_NAME);
		CachedRowSetImpl rs = null;
		String sql = "SELECT T.NP_FTP_DATA_DATE  FROM  NP_FTP_DATA_DATE  T WHERE  T.FLAG = '0'  AND  T.NOPAPER_ID = ? ORDER BY NP_FTP_DATA_DATE";
		log.info("根据批次信息从无纸化业务表中取得业务信息:" + sql);
		rs = dbHandler.queryRs(sql, nopaperId);
		while (rs.next()) {
			list.add(rs.getString(1));
		}
		return list;
	}

	/**
	 * 根据日期和任务ID从无纸化业务表中获取流水号和BUSINESSNO的对应关系
	 * 
	 * <AUTHOR> 2017年7月10日
	 * @param occurDate
	 * @param nopaperId
	 * @return
	 * @throws SQLException
	 */
//	public Map<String, String> getBusiDataNoByDataList(List<NpBusinessData> list) throws SQLException {
//		Map<String, String> map = new HashMap<String, String>();
//		if (list.size() == 0 || list == null) {
//			return map;
//		}
//		String occurDate = list.get(0).getOccurDate();
//		String siteNo = list.get(0).getSiteNo();
//		String operatorNo = list.get(0).getOperatorNo();
//		String flowId = list.get(0).getFlowId();
//		String nopaperId = list.get(0).getNopaperId();
//		for (int i = 1; i < list.size(); i++) {
//			NpBusinessData data = list.get(i);
//			flowId = flowId + "','" + data.getFlowId();
//		}
//		DBHandler dbHandler = new DBHandler(Constants.CONN_POOL_NAME);
//		CachedRowSetImpl rs = null;
//		String sql = "SELECT T.FLOW_ID,T.BUSI_DATA_NO FROM NP_BUSINESS_DATA_TB  T WHERE "
//				+ " T.OCCUR_DATE = '"
//				+ occurDate
//				+ "' AND T.SITE_NO = '"
//				+ siteNo
//				+ "' AND T.OPERATOR_NO = '"
//				+ operatorNo
//				+ "'  AND T.NOPAPER_ID = '"
//				+ nopaperId
//				+ "' AND T.FLOW_ID IN ('" + flowId + "')";
//		log.info("查询BUSI_DATA_NO:" + sql);
//		rs = dbHandler.queryRs(sql);
//		while (rs.next()) {
//			map.put(rs.getString(1), rs.getString(2));
//		}
//		return map;
//	}

	/**
	 * 根据source从NP_INPUT_DEF_TB表中获取详细配置
	 * 
	 * <AUTHOR> 2017年7月6日
	 * @param nopaperId
	 * @return
	 * @throws SQLException
	 */
	public List<NpInputDef> getDefineWithSource(String source) {
		List<NpInputDef> list = new ArrayList<NpInputDef>();
		String sql = "SELECT * FROM NP_INPUT_DEF_TB T WHERE T.SOURCE ="
				+ source;
		DBHandler dbHandler = new DBHandler(Constants.CONN_POOL_NAME);
		CachedRowSetImpl rs = null;
		try {
			rs = dbHandler.queryRs(sql);
			while (rs.next()) {
				NpInputDef npInputDef = npInputDefOrm.orm(rs);
				list.add(npInputDef);
			}
		} catch (SQLException e) {
			e.printStackTrace();
			return null;
		}
		return list;
	}

	/**
	 * 根据source从NP_INPUT_DEF_TB表中获取详细配置
	 * 
	 * <AUTHOR> 2017年7月6日
	 * @param nopaperId
	 * @return
	 * @throws SQLException
	 */
	public NpInputDef getInputDefWithNopaperId(String nopaperId) {
		NpInputDef def = new NpInputDef();
		String sql = "SELECT * FROM NP_INPUT_DEF_TB T WHERE T.NOPAPER_ID ="
				+ nopaperId;
		DBHandler dbHandler = new DBHandler(Constants.CONN_POOL_NAME);
		CachedRowSetImpl rs = null;
		try {
			rs = dbHandler.queryRs(sql);
			while (rs.next()) {
				def = npInputDefOrm.orm(rs);
			}
		} catch (SQLException e) {
			e.printStackTrace();
			return null;
		}
		return def;
	}

	/**
	 * 根据nopaperId从NP_OUTPUT_DEF_TB表中获取详细配置
	 * 
	 * <AUTHOR> 2017年7月6日
	 * @param nopaperId
	 * @return
	 * @throws SQLException
	 */
	public NpOutputDef getOutputDefWithNopaperId(String nopaperId) {
		NpOutputDef def = new NpOutputDef();
		String sql = "SELECT * FROM NP_OUTPUT_DEF_TB T WHERE T.NOPAPER_ID ="
				+ nopaperId;
		DBHandler dbHandler = new DBHandler(Constants.CONN_POOL_NAME);
		CachedRowSetImpl rs = null;
		try {
			rs = dbHandler.queryRs(sql);
			while (rs.next()) {
				def = npOutputDefOrm.orm(rs);
			}
		} catch (SQLException e) {
			e.printStackTrace();
			return null;
		}
		return def;
	}

	/**
	 * 根据批次信息从无纸化业务表中取得业务信息（重构：根据nopaperId获取待处理的业务数据）
	 * 
	 * <AUTHOR> 2017年7月6日
	 * @param batch
	 * @return
	 * @throws SQLException
	 */
	public List<NpBusinessData> getPendingBusinessData(String nopaperId,
			String occurDate, String siteNo, String operatorNo) {
		DBHandler dbHandler = new DBHandler(Constants.CONN_POOL_NAME);
		CachedRowSetImpl rs = null;
		List<NpBusinessData> npBusinessDataList = new ArrayList<NpBusinessData>();
		String sql = "SELECT * FROM NP_BUSINESS_DATA_TB  T WHERE T.NOPAPER_ID = ?  "
				+ "AND T.FLAG = '0'  AND T.OCCUR_DATE = ? "
				+ "AND T.SITE_NO = ? AND T.OPERATOR_NO = ? ";
		log.info("根据批次信息从无纸化业务表中取得业务信息:" + sql);
		try {
			rs = dbHandler.queryRs(sql, nopaperId, occurDate, siteNo,
					operatorNo);
			while (rs.next()) {
				NpBusinessData busi = npBusinessDataOrm.orm(rs);
				npBusinessDataList.add(busi);
			}
		} catch (SQLException e) {
			e.printStackTrace();
			return null;
		}
		return npBusinessDataList;
	}

	/**
	 * 根据批次信息从无纸化业务表中取得业务信息（重构：根据nopaperId获取待处理的业务数据）
	 * 
	 * <AUTHOR> 2017年7月6日
	 * @param batch
	 * @return
	 * @throws SQLException
	 */
	public List<NpBusinessData> getPendingBusinessData(String nopaperId) {
		DBHandler dbHandler = new DBHandler(Constants.CONN_POOL_NAME);
		CachedRowSetImpl rs = null;
		List<NpBusinessData> npBusinessDataList = new ArrayList<NpBusinessData>();
		String sql = "SELECT * FROM NP_BUSINESS_DATA_TB  T WHERE T.NOPAPER_ID = ?  "
				+ "AND T.FLAG = '0'  ";
		log.info("根据批次信息从无纸化业务表中取得业务信息:" + sql);
		try {
			rs = dbHandler.queryRs(sql, nopaperId);
			while (rs.next()) {
				NpBusinessData busi = npBusinessDataOrm.orm(rs);
				npBusinessDataList.add(busi);
			}
		} catch (SQLException e) {
			e.printStackTrace();
			return null;
		}
		return npBusinessDataList;
	}

	/**
	 * 
	 * @Title getFTPDataDate
	 * @Description
	 * <AUTHOR> 2017年7月18日
	 * @param nopaperId
	 * @param startDate
	 * @return
	 * @throws SQLException
	 */
	public List<String> getAllFTPDataDate(String nopaperId, String startDate)
			throws SQLException {
		List<String> dateList = new ArrayList<String>();

		String sql = "SELECT T.NP_FTP_DATA_DATE FROM NP_FTP_DATA_DATE  T  WHERE NOPAPER_ID  = ?  AND T.NP_FTP_DATA_DATE >= ? ";
		DBHandler dbHandler = new DBHandler(Constants.CONN_POOL_NAME);
		CachedRowSetImpl rs = null;
		rs = dbHandler.queryRs(sql, nopaperId, startDate);
		while (rs.next()) {
			dateList.add(rs.getString(1));
		}
		return dateList;
	}

	/**
	 * 
	 * @Title insertFTPDataDate
	 * @Description 批量插入FTPDataDate
	 * <AUTHOR> 2017年7月18日
	 * @param FTPDataDate
	 * @param nopaperId
	 * @return
	 * @throws SQLException
	 */
	public void insertFTPDataDate(List<String> dateList, String nopaperId)
			throws SQLException {
		List<Object[]> params = new ArrayList<Object[]>();
		for (String i : dateList) {
			String[] param = { i, nopaperId, "0" };
			params.add(param);
		}
		String sql = "INSERT INTO NP_FTP_DATA_DATE VALUES (?,?,?)";
		DBHandler dbHandler = new DBHandler(Constants.CONN_POOL_NAME);
		dbHandler.executeAsBatch(sql, params);
	}

	/**
	 * 把一笔无纸化业务对应的图像的数据集合npImageDataList插入到NpImageData表中，同时更新该笔业务的处理状态
	 * 
	 * 
	 * <AUTHOR> 2017年7月7日
	 * @param npImageDataList
	 * @return
	 */
	public boolean insertNpImageData(List<NpImageData> npImageDataList) {
		DBHandler dbHandler = new DBHandler(Constants.CONN_POOL_NAME);
		List<String> sqlList = new ArrayList<String>();
		for (NpImageData i : npImageDataList) {
			String sql = "INSERT INTO NP_IMAGE_DATA_TB(BUSI_DATA_NO,FORM_NAME,FILE_NAME,BACK_FILE_NAME,IMAGE_SIZE,BACK_IMAGE_SIZE,ORDER_NUM, PS_LEVEL) "
					+ "VALUES ('"
					+ i.getBusiDataNo()
					+ "','"
					+ i.getFormName()
					+ "','"
					+ i.getFileName()
					+ "','"
					+ i.getBackFileName()
					+ "',"
					+ i.getImageSize()
					+ ","
					+ i.getBackImageSize()
					+ ",'" + i.getOrderNum() + "','" + i.getPsLevel() + "')";
			sqlList.add(sql);
			String updateSql = "UPDATE NP_BUSINESS_DATA_TB T  SET T.FLAG = '1'  WHERE T.BUSI_DATA_NO ="
					+ i.getBusiDataNo();
			sqlList.add(updateSql);
		}

		try {
			int[] i = dbHandler.executeAsBatch(sqlList);
			for (int flag : i) {
				if (flag < 0 && flag != -2)
					return false;
			}
		} catch (SQLException e) {
			e.printStackTrace();
			return false;
		}
		return true;
	}

	/**
	 * 把FTP业务信息插入到无纸化业务信息表中
	 * 
	 * <AUTHOR> 2017年7月7日
	 * @param npBusinessDataList
	 * @return
	 */
//	public boolean insertNpBusinessData(List<NpBusinessData> npBusinessDataList) {
//		DBHandler dbHandler = new DBHandler(Constants.CONN_POOL_NAME);
//		List<Object[]> paramsList = new ArrayList<Object[]>();
//		for (NpBusinessData i : npBusinessDataList) {
//			String[] params = { i.getOccurDate(), i.getSiteNo(),
//					i.getOperatorNo(), i.getFlowId(), i.getFormName(),
//					i.getContentId(), i.getNopaperId(), i.getBatchId() };
//			paramsList.add(params);
//		}
//		try {
//			String sql = "INSERT INTO NP_BUSINESS_DATA_TB(OCCUR_DATE,SITE_NO,OPERATOR_NO,FLOW_ID,FORM_NAME,CONTENT_ID,NOPAPER_ID,BATCH_ID) VALUES (?,?,?,?,?,?,?,?)";
//			int[] ii = dbHandler.executeAsBatch(sql, paramsList);
//			for (int i : ii) {
//				if (i < 0 && i != -2)
//					return false;
//			}
//		} catch (SQLException e) {
//			e.printStackTrace();
//			return false;
//		}
//		return true;
//	}

	/**
	 * 根据nopaperId从NP_INPUT_DEF_TB表中获取详细配置
	 * 
	 * <AUTHOR> 2017年7月6日
	 * @param nopaperId
	 * @return
	 * @throws SQLException
	 */
	public boolean updateFTPDataDate(String FTPDataDate, String nopaperId)
			throws SQLException {
		String sql = "UPDATE NP_FTP_DATA_DATE T SET T.FLAG = '1' WHERE T.NP_FTP_DATA_DATE = ? AND T.NOPAPER_ID = ?";
		DBHandler dbHandler = new DBHandler(Constants.CONN_POOL_NAME);
		int result = dbHandler.execute(sql, FTPDataDate, nopaperId);
		if (result < 0) {
			return false;
		} else {
			return true;
		}
	}

	public boolean updateBusiData(String batchId, String contentId,
			String indexValue, String busiDataNo) {
		DBHandler dbHandler = new DBHandler(Constants.CONN_POOL_NAME);
		String sql = "UPDATE NP_BUSINESS_DATA_TB T SET T.FLAG = '1',T.BATCH_ID = '"
				+ batchId
				+ "',T.CONTENT_ID_NEW ='"
				+ contentId
				+ "', T.INDEX_VALUE_NEW = '"
				+ indexValue
				+ "' WHERE T.BUSI_DATA_NO IN ('" + busiDataNo + "')";
		try {
			int i = dbHandler.execute(sql);
			if (i == 0)
				return false;
		} catch (SQLException e) {
			e.printStackTrace();
			return false;
		}
		return true;
	}

	/**
	 * 根据日期+任务号 检查TMS该条任务是否完成
	 * 
	 * <AUTHOR> 2017年7月6日
	 * @param occurDate
	 * @param id
	 * @return
	 * @throws SQLException
	 */
	public boolean TMSJobIsFinish(String occurDate, String jobId) {
		String sql = "SELECT * FROM ARMS_DL_JOBLOG_TB T WHERE T.JOB_ID ="
				+ jobId + "  AND T.JOB_STATE = '2'  AND T.OCCUR_DATE ="
				+ occurDate;
		DBHandler dbHandler = new DBHandler(Constants.CONN_POOL_NAME);
		CachedRowSetImpl rs = null;
		try {
			rs = dbHandler.queryRs(sql);
			while (rs.next()) {
				return true;
			}
		} catch (SQLException e) {
			e.printStackTrace();
			return false;
		}
		return false;
	}

}
