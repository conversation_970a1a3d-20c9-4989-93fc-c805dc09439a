{"remainingRequest": "D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\node_modules\\babel-loader\\lib\\index.js!D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\src\\views\\system\\config\\message\\systemMsg\\SunOperDetailDialog\\index.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\src\\views\\system\\config\\message\\systemMsg\\SunOperDetailDialog\\index.vue", "mtime": 1690352538878}, {"path": "D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\babel.config.js", "mtime": 1686019818359}, {"path": "D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\.babelrc", "mtime": 1705285078668}, {"path": "D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1655715099000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAwKA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SACAA,eACAC,eACAC,kBACAC,kBACAC,sBACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,2BACAC;EADAC;EAAAC;EAAAC;EAAAC;AAEA;EAAAC;EAAAC;AACA;EACAC;EACAC;IACAC;IACAC;EACA;EACAC;IAAAC;EAAA;EACAC;IACAC;MACAC;MACAC;QACA;UACA;UACAC;UACAC;YACA;YACAC;YAAA;YACAC;UACA;;UACAC;UAAA;UACAC;YACA;YACAC;UACA;UACAC;UAAA;UACAC;UAAA;UACAC;QACA;MACA;IACA;EACA;EACAC;IACA;MACAC;MAAA;MACAC;MACA;MACAC;QACA;QACAC;QAAA;QACAC;QACAC;QAAA;QACAC;QAAA;QACAC;QACAjB;UACAS;UAAA;UACAS;UACAC;QACA;;QACAC;UACA;UACAC;UACAC;UAAA;UACAC;QACA;;QACAlB;MACA;MACAmB;QAAAC;MAAA;MACAC;QACAD;MACA;MACAE;QACAF;MACA;MACAG;QACAH;MACA;MACAI;MAAA;MACAC;MAAA;MACA;MACAtB;MAAA;MACAuB;QACA;QACAhC;QACAC;UACA;UACAC;UACAC;UAAA;UACA8B;QACA;QACAC;UACAC;UACAC;UACAC;YACAC;YAAA;YACAC;UACA;QACA;MACA;;MACAC;MAAA;MACAC;MAAA;MACAC;MAAA;MACA;MACAC;QACA;QACA3C;QAAA;QACA4C;QAAA;QACAC;QAAA;QACA5C;UACA;UACAC;UAAA;UACAC;QACA;;QACA2C;UACA;UACA/B;UACAgC;UAAA;UACA9B;UAAA;UACAC;UAAA;UACAG;YACA;YACAC;YAAA;YACAC;YAAA;YACAC;UACA;;UACAvB;YACA;YACAS;UACA;QACA;MACA;;MACAsC;MACAC;MACAC;QACAlD;QACAE;QACAC;QACAgB;QACAP;QAAA;QACAd;MACA;;MACAqD;QACA;QACAnD;QACAC;UACA;UACAC;UAAA;UACAkD;UACAjD;QACA;;QACAkD;MACA;IACA;EACA;EACAC;EACAC;IACA;MACA;MACAC;QACA;UACA;UACA;UACA,IACA,uCACA,+BACA;YACAC;UACA;QACA;UACA;UACA;QACA;MACA;MACAC;IACA;EACA;EACAC;EACAC,6BACA;EACAC;IACAC;MACA;IACA;IACA;AACA;AACA;IACAC;MAAA;MACA;MACA,mEACAC,6CACAA,oBACA,oBACA,IACA;MACA;MACA;QACA;QACA,IACA,8BACAA,2DACA,qCACA;UACA;UACA;UACA;UACA;UACA;QACA;UACA;UACA;QACA;MACA;QACA;QACA,IACAA,8CACA,qCACA;UACA;UACA;UACA;UACA;UACA;QACA;UACA;UACA;QACA;MACA;MACA;MACA;MACA;MACA;QACA;QACA;QACA,iDACAC;QAAA,EACA;QACA,0CACAA,kCACA;QACA;QACA,0DACAC;QAAA,EACA;QACA;QACA,gEACAA;QAAA,EACA;QACA;QACA;UACA;YAAA;cACA;cACA;cACA;cAAA,2BACAC;gBACA;gBACA;gBACAC;kBACA;oBACA;sBACAC;sBACA/E;sBACAgF;oBACA;oBACA,mDACAC;oBACA,iDACA;kBACA;;kBACA;oBACA;sBAAA;wBACAC;wBACA,yCACA,KACAA;wBACA,yDACA;0BACA;4BACA;0BAAA,CACA,UACA,0BACAA,2BACAA,8BACA;4BACA;4BACAC;8BACA;gCACAC;8BACA;4BACA;0BACA;4BACA;0BAAA,CACA,UACAC,yBACAA,uBACA;4BACA;4BACAD;0BACA;4BACA;4BACA,WACA;4BAAA,2CACAhE;8BAAA;4BAAA;8BAAA;gCAAA;gCACA,IACAkE,iDACA;kCACAF;kCACA;gCACA;8BACA;4BAAA;8BAAA;4BAAA;8BAAA;4BAAA;0BACA;4BACA;4BACA;8BACA;gCACA;gCAAA,4CACAhE;kCAAA;gCAAA;kCAAA;oCAAA;oCACA;oCACA;sCACAgE;sCACA;oCACA;kCACA;gCAAA;kCAAA;gCAAA;kCAAA;gCAAA;8BACA;4BACA;0BACA;wBACA,EACA;sBAAA;oBACA;kBACA;gBACA;cAAA;cA3EA;gBAAA;cA4EA;YAAA;UACA,WACAV,kCACAA,gCACA;YAAA,6BAEAG;cACA;cACA;cACAC;gBACA;kBACA;oBACAC;oBACA/E;kBACA;kBACA;gBACA;;gBACA;kBACA;oBACAkF;kBACA;gBACA;gBACA,yCACA,KACAA;gBACA;kBACA;kBACA;gBAAA,CACA,UACA,0BACAA,2BACAA,8BACA;kBACA;kBACAC;oBACA;sBACA,2BACAI,KACA;oBACA;kBACA;gBACA;kBACA;gBAAA,CACA,UACAF,yBACAA,yBACAH,yBACA;kBACA;kBACA,mDACAM,oBACA;gBACA;kBACA;kBACA;kBAAA,4CACApE;oBAAA;kBAAA;oBAAA;sBAAA;sBACA;wBACA,2BACAmE,KACA;wBACA;sBACA;oBACA;kBAAA;oBAAA;kBAAA;oBAAA;kBAAA;gBACA;kBACA;kBACA;oBACA;sBACA;sBAAA,4CACAnE;wBAAA;sBAAA;wBAAA;0BAAA;0BACA;0BACA;4BACA,2BACAmE,KACA;4BACA;0BACA;wBACA;sBAAA;wBAAA;sBAAA;wBAAA;sBAAA;oBACA;kBACA;gBACA;kBACA;gBAAA;cAEA;YAAA;YA7EA;YACA;cAAA;YA6EA;UACA;YACA;YACA;cACAR;cACA/E;YACA;YACA;UACA;YACA;cACAZ;cACA;YACA;YACA;YACA;YACA;cACA2F;cACA/E;YACA;UACA;YACA;YACA;YACA;YACA;cACAyF;cACAvD;cACAD;cACAyD;YACA;YACA3F;cACA;gBAAA4F;gBAAA3D;cAAA,6BACA6C;gBACA;gBACA;gBACAC;kBACA;oBACA;sBACAC;sBACA/E;sBACAgF;oBACA;oBACA;oBACA;kBACA;;kBACA;oBACA;sBAAA;wBACAE;wBACA,yCACA,KACAA;wBACA,yDACA;0BACA;4BACA;0BAAA,CACA,UACA,0BACAA,2BACAA,8BACA;4BACA;4BACAC;8BACA;gCACAC;8BACA;4BACA;0BACA;4BACA;0BAAA,CACA,UACAC,yBACAA,uBACA;4BACA;4BACAD;0BACA;4BACA;4BACA,WACA;4BAAA,4CACAhE;8BAAA;4BAAA;8BAAA;gCAAA;gCACA,IACAkE,iDACA;kCACAF;kCACA;gCACA;8BACA;4BAAA;8BAAA;4BAAA;8BAAA;4BAAA;0BACA;4BACA;4BACA;8BACA;gCACA;gCAAA,4CACAhE;kCAAA;gCAAA;kCAAA;oCAAA;oCACA;oCACA;sCACAgE;sCACA;oCACA;kCACA;gCAAA;kCAAA;gCAAA;kCAAA;gCAAA;8BACA;4BACA;0BACA;wBACA,EACA;sBAAA;oBACA;kBACA;gBACA;cAAA;cAzEA;gBAAA;cA0EA;YACA;UACA;YACA/F;YACA;YACA;YACA,IACA,wCACA,gCACA;cACA8E;YACA;UACA;QACA;QACA;QACA;QACA;UACA;UACA;UACA;YACA;cACAoB;gBACA;kBACAK;gBACA;gBACA;cACA;YACA;UACA;UACAL;QACA;MACA;MACA;IACA;IACA;AACA;AACA;AACA;IACAM;MACA;MACA;IACA;IAAA;IACAC;MACA;IACA;IACA;AACA;AACA;AACA;IACAC;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;IACA;IACA;AACA;AACA;AACA;IACAC;MACA;MACA;MACA;MACA;MAAA,4CACAhD;QAAA;MAAA;QAAA;UAAA;UACA;YACAiD;UACA;QACA;MAAA;QAAA;MAAA;QAAA;MAAA;MACA;QAAA;QACA;QACA;MACA;IACA;IACA;AACA;AACA;IACAC;MACA;MACA;MACA;IACA;IACA;AACA;AACA;AACA;AACA;IACAC;MACA;MACA;MACA;MACA,yCACAC,cACA,qDACAC,SACA,CACA;MACA,sCACAD,cACA,qDACAE,uBACA,CACA;MACA;MACA;MACA;;MAEA,WACAC;MACA;;MAEA;MAEA,+GACAC,mBACA;;MAEA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;;MAEA;QACA;MACA;QACA;MACA;QACA;MACA;QACA;MACA;QACA;MACA;QACA;MACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IAEA;IACA;IACA;IAEA;IAEA;IACA;IACA;IAEA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IAEA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;AACA;AACA;AACA;IACAC;MACAnH;QACA;UACAoH;QACA;MACA;IACA;IACA;AACA;AACA;IACAC;MACA;IACA;IACA;AACA;AACA;AACA;IACAC;MAAA;MACAtH;QACA;UAAA;UACA;YACA;YACA;YACA;YACA;cACAmG;cACAoB;gBACAC;gBACAC;gBAAA;gBACAC;gBACAjD;gBAAA;gBACAkD;gBACAC;gBACAC;gBACAC;gBACAC;cACA;YACA;YACAvH;cACA;gBACA;gBACAP;gBACA;gBACA;kBACA;gBACA;cACA;gBACAC;cACA;YACA;UACA;YACA;YACA;YACA;UACA;QACA;MACA;IACA;IACA;AACA;IACA8H;MAAA;MACAhI;QACA;UAAA;UACA;YACA;YACA;YACA;cACAmG;cACAoB;gBACAC;gBACAC;gBACAC;gBACAjD;gBACAkD;gBACAC;gBACAC;gBACAC;gBACAC;cACA;YACA;YACAvH;cACA;gBACA;gBACA;kBACA;gBACA;;gBACAP;gBACA;cACA;gBACAC;cACA;YACA;UACA;YACA;YACA;YACA;UACA;QACA;MACA;IACA;IACA;AACA;IACA+H;MAAA;MACA;MACA;MACA;MACA;QACAC;MACA;;MACA9H;QACA;QACA;UACA;UACA;UAAA,4CACA+H;YAAA;UAAA;YAAA;cAAA;cACA;YACA;UAAA;YAAA;UAAA;YAAA;UAAA;UACA;YACAhC;YACAoB;cACAC;cACAC;cACAC;cAAA;cACAU;cACA3D;cACA4D;gBACAC;gBACAC;cACA;cACAC;gBACAC;gBACAC;cACA;YACA;UACA;UAEA;YACA;YACArI;cACA;cACA;gBACA;gBACA;gBACA;gBACA;kBACA;kBACA;kBACA;kBAEA;kBACA;kBACA;kBACA;oBACA;oBACA,IACA,sCACAsI,mCACA;sBACAC;wBACAzC;wBACAoB;0BACAoB;wBACA;sBACA;oBACA;sBACAC;wBACAzC;wBACAoB;0BACAoB;wBACA;sBACA;oBACA;oBAEAE;kBACA,WACAC,qCACAA,mCACA;oBACA;oBACA,IACA,sCACAH,mCACA;sBACA;sBACAA,kBACAA;oBACA;oBACAC;sBACAzC;sBACAoB;wBACAoB;sBACA;oBACA;kBACA;oBACA;oBACAC;sBACAzC;sBACAoB;wBACAY;wBACAY;wBACAC;wBACAC;wBACAC;wBACAC;wBACAC;wBACAC;wBACAC;wBACAzB;wBACAc;wBACAY;sBACA;oBACA;kBACA;oBACA;oBACA;oBACA;kBACA;oBACA;oBACAX;sBACAzC;sBACAoB;wBACAoB;sBACA;oBACA;kBACA;kBACA;kBACA,0DACA,iBACA,GACAA,iCACAa;kBACAC;;kBAEAnJ;oBACA;sBACA;sBACA;wBACA,IACA,uCACAoJ,qCACA;0BACA;4BACAvD;4BACAoB;8BACAoC;8BACAC;8BACApC;8BACAqC;8BACAC;4BACA;0BACA;0BACAvJ;wBACA;sBACA;sBACA;sBACA;wBACA;sBACA;;sBACAN;oBACA;sBACAC;sBACA;wBACAiG;wBACAoB;0BACAoC;0BACAC;0BACApC;0BACAqC;wBACA;sBACA;sBACAtJ;wBACAL;sBACA;oBACA;oBACAuJ;kBACA;gBACA;kBACA;kBACAxJ;gBACA;cACA;gBACA;gBACAC;cACA;cACA;cACA;YACA;UACA;YACAG;cACA;gBACA;gBACA;gBACA;kBACA;gBACA;;gBACAJ;gBACA;gBACA;cACA;gBACAC;cACA;YACA;UACA;QACA;MACA;IACA;IACA;AACA;AACA;IACA6J;MACA;IACA;IACA;AACA;AACA;IACAC;MACA;IACA;IACA;AACA;AACA;IACAC;MACA;QACAnK;MACA;IACA;IACA;AACA;IACAoK;MACA;IACA;IACA;AACA;AACA;IACAC;MAAA;MACA;QACA;MACA;IACA;EACA;AACA", "names": ["commonMsgWarn", "commonMsgInfo", "commonMsgConfirm", "commonMsgSuccess", "commonMsgError", "Common", "upload", "approvalResult", "operationApply", "errorOperation", "approveFlow", "instOther", "name", "components", "SunPreview", "FlowDetailDialog", "directives", "elDragDialog", "props", "dialogConfig", "type", "default", "visible", "componentProps", "title", "width", "currentRow", "tableData", "showPage", "desData", "defaultForm2", "btnId", "data", "preViewVis", "imageSrc", "tableDetail", "tableColumns", "ref", "selection", "indexNumber", "loading", "height", "formRow", "pageList", "totalNum", "currentPage", "pageSize", "redColor", "color", "blueBlack", "blueColor", "greenColor", "showAgreeBtn", "bindTaskId", "dialogFile", "appendToBody", "form", "labelWidth", "config", "defaultForm", "file_url", "postscript", "fileList", "delFile", "fileRow", "tableFileDialog", "btnSubmit", "btnCancle", "tableConfig", "columns", "extend", "fileData", "previewData", "flowConfig", "top", "module_id", "computed", "watch", "handler", "releaseFlowTask", "deep", "created", "mounted", "methods", "closePreview", "detailShow", "row", "JSON", "operDetailValTitle", "key", "show_content_sort_list", "label", "id", "deleteData_list", "key_type", "dictionaryFieds", "item2", "keyTypeArr", "key2", "item", "operDetailVal", "parameterList", "other_msg", "list", "roleArr", "openFlowDetail", "dialogFlowClose", "importFileDetail", "openFile", "rows", "preview", "fiePreview", "encryptResult", "fileName", "fileUrl", "href", "Base64", "downloadFile", "commonDownLoadFile", "fileDialogClose", "handleAgree", "sysMap", "inst_id", "item_id", "deal_result", "roles", "target_user_no", "user_no", "start_date", "end_date", "handleNotAgree", "dialogSubmit", "formData", "uploadFileList", "menu_id", "plug_in", "user_comment", "attachment", "flowParameter", "f_deal_result", "f_organ_level", "operation_value", "msg2", "applyType", "curRow", "headerRowNum", "module_no", "table_name", "pkList", "procedure_name", "columnInfoList", "queryList", "add_way", "instId", "undefined", "sessionStorage", "parseInt", "oper_type", "operation_type", "error_msg", "remarks", "delFileList", "changeFileList", "onExceed", "dialogFileClose", "dialogClose"], "sourceRoot": "src/views/system/config/message/systemMsg/SunOperDetailDialog", "sources": ["index.vue"], "sourcesContent": ["/*数据审核工作台：流水号- 操作详情弹框 */ /*系统消息-操作内容详情 */\n/*首页系统消息通知-消息内容详情 */\n<template>\n  <div>\n    <el-dialog\n      ref=\"detailDialog\"\n      class=\"detailDialog\"\n      top=\"3rem\"\n      :visible.sync=\"dialogConfig.visible\"\n      :close-on-click-modal=\"false\"\n      element-loading-text=\"数据加载中...\"\n      :before-close=\"dialogClose\"\n      :append-to-body=\"true\"\n      v-bind=\"dialogConfig.componentProps\"\n    >\n      <div v-if=\"dialogConfig.currentRow.operation_type\">\n        <!-- 表格形式---删除 begin -->\n        <div v-if=\"dialogConfig.currentRow.operation_type === 'OP002'\">\n          <div class=\"title\">操作请求详情</div>\n          <sun-table\n            :table-config=\"dialogConfig.tableData\"\n            :show-page=\"dialogConfig.tableData.showPage\"\n          >\n            <template slot=\"tableColumn\">\n              <el-table-column\n                v-for=\"item in dialogConfig.tableData.tableColumns\"\n                :key=\"item.id\"\n                :prop=\"item.name\"\n                :label=\"item.label\"\n              />\n            </template>\n          </sun-table>\n        </div>\n        <!-- 表单形式---新增/修改/导入/导出/批量操作 begin-->\n        <div v-else>\n          <el-descriptions :column=\"2\" title=\"操作请求详情\">\n            <el-descriptions-item\n              v-for=\"item in dialogConfig.defaultForm2\"\n              :key=\"item.name\"\n              :label=\"item.label\"\n            >\n              <span\n                v-if=\"item.name === 'flow_detail'\"\n                style=\"color: blue\"\n                @click=\"openFlowDetail\"\n              >查看流程详情</span>\n              <span\n                v-else-if=\"item.name === 'importFile'\"\n                style=\"color: blue\"\n                @click=\"importFileDetail\"\n              >查看</span>\n              <span\n                v-else-if=\"item.name === 'exportFile'\"\n                style=\"color: blue\"\n                @click=\"importFileDetail\"\n              >查看</span>\n              <span v-else>{{ dialogConfig.desData[item.name] }}</span>\n            </el-descriptions-item>\n          </el-descriptions>\n        </div>\n      </div>\n\n      <!-- 处理明细 begin -->\n      <div>\n        <div class=\"title\">处理明细</div>\n        <sun-table\n          :table-config=\"tableDetail\"\n          :show-page=\"tableDetail.showPage\"\n        >\n          <template slot=\"tableColumn\">\n            <el-table-column\n              v-for=\"item in tableDetail.tableColumns\"\n              :key=\"item.id\"\n              :prop=\"item.name\"\n              :label=\"item.label\"\n              :width=\"item.width\"\n            >\n              <div slot-scope=\"{ row }\">\n                <span v-if=\"item.name === 'organ_no'\">{{\n                  row[item.name] | organNameFormat\n                }}</span>\n                <span\n                  v-else-if=\"item.name === 'role_no'\"\n                  class=\"textOverflow\"\n                  :title=\"row[item.name]\"\n                >{{ row[item.name] }}</span>\n                <span v-else-if=\"item.name === 'deal_time'\">{{\n                  row[item.name] | dateTimeFormat\n                }}</span>\n                <span v-else-if=\"item.name === 'deal_result'\">{{\n                  row[item.name] | commonFormatValue('DEAL_RESULT')\n                }}</span>\n                <span\n                  v-else-if=\"item.name === 'deal_state'\"\n                  :style=\"\n                    row[item.name] === '2'\n                      ? blueColor\n                      : row[item.name] === '4'\n                        ? greenColor\n                        : redColor\n                  \"\n                >{{ row[item.name] }}-{{\n                  row[item.name] | commonFormatValue('DEAL_STATE')\n                }}</span>\n                <span\n                  v-else-if=\"item.name === 'attachment' && row[item.name]\"\n                  style=\"color: blue\"\n                  @click=\"openFile(row)\"\n                >查看\n                </span>\n                <span v-else>{{ row[item.name] }}</span>\n              </div>\n            </el-table-column>\n          </template>\n        </sun-table>\n      </div>\n\n      <!-- 同意、不同意按钮 -->\n      <div slot=\"footer\" class=\"dialog-footer\">\n        <div v-if=\"dialogConfig.showAgreeBtn\">\n          <el-button type=\"primary\" @click=\"handleAgree\">同意</el-button>\n          <el-button type=\"danger\" @click=\"handleNotAgree\">不同意</el-button>\n        </div>\n      </div>\n    </el-dialog>\n\n    <!-- 查看附件begin -->\n    <SunTableDialog\n      :dialog-config=\"tableFileDialog\"\n      @dialogClose=\"fileDialogClose\"\n    >\n      <template slot-scope=\"{ item, row }\">\n        <span\n          v-if=\"item.name === 'show'\"\n          style=\"color: blue\"\n          @click=\"preview(row)\"\n        >预览</span>\n        <span\n          v-else-if=\"item.name === 'download'\"\n          style=\"color: blue\"\n          @click=\"downloadFile(row)\"\n        >下载</span>\n        <span v-else>{{ row[item.name] }}</span>\n      </template>\n    </SunTableDialog>\n\n    <!-- 上传附件弹框-begin -->\n    <SunFormDialog\n      ref=\"refDialog\"\n      :dialog-config=\"dialogFile\"\n      @dialogSubmit=\"dialogSubmit\"\n      @dialogClose=\"dialogFileClose\"\n    />\n    <!-- 预览 -->\n\n    <sun-preview :dialog-config=\"previewData\" @dialogClose=\"closePreview\" />\n\n    <!-- 流程详情弹框 -->\n    <flow-detail-dialog\n      v-if=\"flowConfig.visible\"\n      ref=\"flowDetailDialog\"\n      :dialog-config=\"flowConfig\"\n      @dialogClose=\"dialogFlowClose\"\n    />\n  </div>\n</template>\n\n<script>\nimport * as Base64 from 'js-base64'\nimport { SunPreview } from '@/components'\nimport { configTableDetail, config, configTableFile } from './info' // 表头、表单配置\nimport { releaseFlowTask } from '@/utils/flowPath.js'\nimport elDragDialog from '@/directive/el-drag-dialog' // 弹出框可拖动\nimport { dateNowFormat10 } from '@/utils/date.js'\nimport { encryptResult } from '@/utils/crypto' // 加密解密密码\nimport { commonBlank, uploadFile, commonDownLoadFile } from '@/utils/common'\nimport {\n  commonMsgWarn,\n  commonMsgInfo,\n  commonMsgConfirm,\n  commonMsgSuccess,\n  commonMsgError\n} from '@/utils/message.js' // 提示信息\nimport FlowDetailDialog from '../../../audit/component/others/flowDetailDialog.vue' // 流程详情弹窗\nimport { dictionaryFieds } from '@/utils/dictionary.js' // 字典常量\nimport { v1 as uuidv1 } from 'uuid'\nimport { organNameFormat, commonFormatValue } from '@/filters'\nimport defaultSettings from '@/settings'\nconst prefix = defaultSettings.service.system // 前缀公共路由\nimport { Common } from '@/api'\nconst { upload, approvalResult, operationApply, errorOperation } =\n  Common.DataAuditing\nconst { approveFlow, instOther } = Common.FlowPath\nexport default {\n  name: 'SunOperDetailDialog',\n  components: {\n    SunPreview,\n    FlowDetailDialog\n  },\n  directives: { elDragDialog },\n  props: {\n    dialogConfig: {\n      type: Object,\n      default: () => {\n        return {\n          // 流水号-操作详情弹框\n          visible: false,\n          componentProps: {\n            // 弹出框属性\n            title: '', // 弹出框标题\n            width: '100%' // 当前弹出框宽度 默认80%\n          },\n          currentRow: [], // 选中行\n          tableData: {\n            // 操作详情弹窗---删除-表格\n            showPage: false\n          },\n          desData: {}, // 操作详情-自定义表单value\n          defaultForm2: [], // 操作详情-自定义表单key\n          btnId: '' // 上传文件提交时判断是同意操作或不同意操作标志\n        }\n      }\n    }\n  },\n  data() {\n    return {\n      preViewVis: false, // 预览组件\n      imageSrc: '',\n      // 操作详情相关参数\n      tableDetail: {\n        // 操作详情弹框-处理明细-表格配置\n        tableColumns: configTableDetail(), // 表头配置\n        ref: 'tableRef',\n        selection: false, // 复选\n        indexNumber: true, // 序号\n        loading: false,\n        componentProps: {\n          data: [], // 表格数据\n          height: '100px',\n          formRow: 2 // 表单行数\n        },\n        pageList: {\n          // show: false,\n          totalNum: 0,\n          currentPage: 1, // 当前页\n          pageSize: this.$store.getters.pageSize // 当前页显示条数\n        },\n        showPage: false\n      },\n      redColor: { color: 'red' },\n      blueBlack: {\n        color: '#15518c'\n      },\n      blueColor: {\n        color: 'blue'\n      },\n      greenColor: {\n        color: '#008000'\n      },\n      showAgreeBtn: false, // 同意不同意按钮不显示\n      bindTaskId: '', // 全局变量 绑定任务明细id\n      // 文件上传相关参数\n      btnId: '', // 上传文件提交时判断是同意操作或不同意操作标志\n      dialogFile: {\n        // 上传文件表单\n        visible: false,\n        componentProps: {\n          // 弹出框配置属性\n          title: '审核提交',\n          width: '60rem', // 当前弹出框宽度\n          appendToBody: true\n        },\n        form: {\n          labelWidth: '15rem',\n          config: config(this),\n          defaultForm: {\n            file_url: '', // 表单地址\n            postscript: '' // 附言\n          }\n        }\n      },\n      fileList: [], // 上传文件列表\n      delFile: [], // 删除文件后的文件列表\n      fileRow: [], // 下载附件当前row信息\n      // 操作详情查看附件相关参数\n      tableFileDialog: {\n        // 操作详情-附件- 查看附件弹框\n        visible: false, // 显示隐藏配置\n        btnSubmit: false, // 确定按钮\n        btnCancle: false, // 取消按钮\n        componentProps: {\n          // 弹出框属性\n          title: '附件列表', // 弹出框标题\n          width: '50%' // 当前弹出框宽度 默认80%\n        },\n        tableConfig: {\n          // 表格属性\n          ref: 'tableRef',\n          columns: configTableFile(), // 表头\n          indexNumber: true, // 序号\n          loading: false, // 等待加载中\n          pageList: {\n            // 页码\n            totalNum: 0, // 总页数\n            currentPage: 1, // 当前页\n            pageSize: this.$store.getters.pageSize // 当前页显示条数\n          },\n          componentProps: {\n            // 表格属性配置\n            data: [] // 表数据\n          }\n        }\n      },\n      extend: 'docx',\n      fileData: '',\n      previewData: {\n        visible: false,\n        title: '预览',\n        width: '100%',\n        height: '50%',\n        imageSrc: '', // 组件显示内容\n        type: '' // 预览组件类型(eg: pdf, xls)\n      },\n      flowConfig: {\n        // 流程模板调用弹窗\n        visible: false,\n        componentProps: {\n          // 弹出框属性\n          title: '模板关联流程图', // 弹出框标题\n          top: '0px',\n          width: '100%' // 当前弹出框宽度 默认80%\n        },\n        module_id: ''\n      }\n    }\n  },\n  computed: {},\n  watch: {\n    'dialogConfig.visible': {\n      // 如果浏览器正常关闭状态\n      handler(val) {\n        if (val === false) {\n          // console.log('任务释放')\n          // 监听流水号操作详情弹窗关闭状态   关闭时释放任务\n          if (\n            this.$store.getters.releaseTaskFlag &&\n            !commonBlank(this.bindTaskId)\n          ) {\n            releaseFlowTask(this.bindTaskId)\n          }\n        } else {\n          // 当操作详情弹框visible为true时调用详情展示方法\n          this.detailShow()\n        }\n      },\n      deep: true\n    }\n  },\n  created() {},\n  mounted() {\n  },\n  methods: {\n    closePreview(param) {\n      this.previewData.visible = param\n    },\n    /**\n     * 操作详情数据处理\n     */\n    detailShow() {\n      const row = this.dialogConfig.currentRow\n      this.dialogConfig.componentProps.title = row.menu_name\n        ? `${row.menu_name}-${commonFormatValue(\n          row.operation_type,\n          'SM_OPERATION_TYPE'\n        )}`\n        : '转授权审批' // 弹框标题\n      // 未审批时且不是本人申请 显示同意/不同意按钮\n      if (row.approve_agree || row.role_no) {\n        // 增删改导入导出操作申请\n        if (\n          (row.approve_agree === '2' ||\n            row.role_no.indexOf(this.$store.getters.userNo) !== -1) &&\n          this.dialogConfig.queryType === '1'\n        ) {\n          this.dialogConfig.showAgreeBtn = true // 同意不同意按钮显示\n          this.bindTaskId = row.item_id // 绑定任务明细id\n          // 释放任务标志和任务id存到本地\n          this.$store.dispatch('flowPath/releaseFlag', true) // ---需要释放任务标志 true\n          this.$store.dispatch('flowPath/releaseId', row.item_id) // ---需要释放任务的id\n        } else {\n          this.dialogConfig.showAgreeBtn = false // 同意不同意按钮隐藏\n          this.bindTaskId = ''\n        }\n      } else {\n        // 转授权判断\n        if (\n          row.user_no !== this.$store.getters.userNo &&\n          this.dialogConfig.queryType === '1'\n        ) {\n          this.dialogConfig.showAgreeBtn = true // 同意不同意按钮显示\n          this.bindTaskId = row.item_id // 绑定任务明细id\n          // 释放任务标志和任务id存到本地\n          this.$store.dispatch('flowPath/releaseFlag', true) // ---需要释放任务标志 true\n          this.$store.dispatch('flowPath/releaseId', row.item_id) // ---需要释放任务的id\n        } else {\n          this.dialogConfig.showAgreeBtn = false // 同意不同意按钮隐藏\n          this.bindTaskId = ''\n        }\n      }\n      this.dialogConfig.defaultForm2 = []\n      this.dialogConfig.tableData.componentProps.data = []\n      this.dialogConfig.tableData.tableColumns = []\n      this.$nextTick(() => {\n        // 弹出框加载完成后赋值\n        // 1- 处理弹框表单（表格）数据\n        const operDetailValTitle = row.show_content_sort\n          ? JSON.parse(row.show_content_sort) // 转授权没有row.show_content_sort\n          : ''\n        const operDetailVal = row.operation_value\n          ? JSON.parse(row.operation_value)\n          : ''\n        // 数据转译\n        const data_translation = !commonBlank(operDetailValTitle)\n          ? operDetailValTitle.data_translation // 兼容转授权\n          : ''\n        // 排序信息数组\n        const show_content_sort_list = !commonBlank(operDetailValTitle)\n          ? operDetailValTitle.content_sort // 兼容转授权\n          : ''\n        // 构造表单key--value\n        if (!commonBlank(row.operation_type)) {\n          if (row.operation_type === 'OP002') {\n            // 如果参数配置类型为  删除  //操作请求详情展示形式 表格+表格\n            // 删除参数\n            const deleteData_list = operDetailVal.deleteData_list\n            for (const key in operDetailValTitle) {\n              // 操作请求详情数据格式化\n              let key_type = ''\n              show_content_sort_list.forEach((item) => {\n                if (key === item) {\n                  this.dialogConfig.tableData.tableColumns.push({\n                    label: operDetailValTitle[key],\n                    name: item,\n                    id: uuidv1()\n                  })\n                  this.dialogConfig.tableData.componentProps.data =\n                    deleteData_list // value值\n                  this.dialogConfig.tableData.pageList.totalNum =\n                    this.dialogConfig.tableData.componentProps.data.length // 页码\n                }\n                for (const i in data_translation) {\n                  if (i === item) {\n                    key_type = data_translation[i]\n                    const keyTypeArr = commonBlank(key_type)\n                      ? []\n                      : key_type.split('@')\n                    this.dialogConfig.tableData.componentProps.data.forEach(\n                      (item2) => {\n                        if (commonBlank(key_type) || keyTypeArr[0] === '1') {\n                          // 正常文本   不做数据格式化直接赋值\n                        } else if (\n                          !commonBlank(key_type) &&\n                          key_type !== 'ORGAN_NO' &&\n                          key_type.indexOf('@') === -1\n                        ) {\n                          // 数据字典\n                          dictionaryFieds(key_type).forEach((key2) => {\n                            if (key2.value === item2[item]) {\n                              item2[item] = key2.label\n                            }\n                          })\n                        } else if (keyTypeArr[0] === '2') {\n                          // 自定义数据源(支持多个格式化)\n                        } else if (\n                          keyTypeArr[0] === '3' ||\n                          keyTypeArr[0] === '4'\n                        ) {\n                          // 数据源为机构\n                          item2[item] = organNameFormat(item2[item])\n                        } else if (keyTypeArr[0] === '5') {\n                          // 外表数据源\n                          const data =\n                            this.$store.getters.externalData[key_type]\n                          for (const key2 of data) {\n                            if (\n                              key2.value === this.dialogConfig.desData[item]\n                            ) {\n                              item2[item] = `${key2.value}-${key2.label}`\n                              break\n                            }\n                          }\n                        } else if (keyTypeArr[0] === '6') {\n                          // 自定义下拉数据\n                          if (!commonBlank(operDetailVal[item])) {\n                            if (operDetailVal[item].indexOf('||') !== -1) {\n                              const data = operDetailVal[item].split('||')\n                              for (const key2 of data) {\n                                const list = key2.split('-')\n                                if (list[0] === item2[item]) {\n                                  item2[item] = `${list[0]}-${list[1]}`\n                                  break\n                                }\n                              }\n                            }\n                          }\n                        }\n                      }\n                    )\n                  }\n                }\n              })\n            }\n          } else if (\n            row.operation_type === 'OP003' ||\n            row.operation_type === 'OP001'\n          ) {\n            // 如果参数配置类型为 新增或修改   列表+表格\n            for (const key in operDetailValTitle) {\n              // 操作请求详情数据格式化\n              let key_type = ''\n              show_content_sort_list.forEach((item) => {\n                if (key === item) {\n                  this.dialogConfig.defaultForm2.push({\n                    label: operDetailValTitle[key],\n                    name: item\n                  })\n                  this.dialogConfig.desData[item] = operDetailVal[item] // value值\n                }\n                for (const i in data_translation) {\n                  if (i === item) {\n                    key_type = data_translation[i]\n                  }\n                }\n                const keyTypeArr = commonBlank(key_type)\n                  ? []\n                  : key_type.split('@')\n                if (commonBlank(key_type) || keyTypeArr[0] === '1') {\n                  // 正常文本   不做数据格式化直接赋值\n                  // this.dialogConfig.desData[item] = operDetailVal[item] // value值\n                } else if (\n                  !commonBlank(key_type) &&\n                  key_type !== 'ORGAN_NO' &&\n                  key_type.indexOf('@') === -1\n                ) {\n                  // 数据字典(支持多个格式化)\n                  dictionaryFieds(key_type).forEach((key) => {\n                    if (key.value === this.dialogConfig.desData[item]) {\n                      this.dialogConfig.desData[\n                        item\n                      ] = `${key.value}-${key.label}`\n                    }\n                  })\n                } else if (keyTypeArr[0] === '2') {\n                  // 自定义数据源(支持多个格式化)\n                } else if (\n                  keyTypeArr[0] === '3' ||\n                  keyTypeArr[0] === '4' ||\n                  key_type === 'ORGAN_NO'\n                ) {\n                  // 机构(支持多个格式化)\n                  this.dialogConfig.desData[item] = organNameFormat(\n                    operDetailVal[item]\n                  )\n                } else if (keyTypeArr[0] === '5') {\n                  // 外表数据源\n                  const data = this.$store.getters.externalData[key_type]\n                  for (const key of data) {\n                    if (key.value === this.dialogConfig.desData[item]) {\n                      this.dialogConfig.desData[\n                        item\n                      ] = `${key.value}-${key.label}`\n                      break\n                    }\n                  }\n                } else if (keyTypeArr[0] === '6') {\n                  // 自定义下拉数据\n                  if (!commonBlank(operDetailVal[item])) {\n                    if (operDetailVal[item].indexOf('||') !== -1) {\n                      const data = operDetailVal[item].split('||')\n                      for (const key of data) {\n                        const list = key.split('-')\n                        if (list[0] === this.dialogConfig.desData[item]) {\n                          this.dialogConfig.desData[\n                            item\n                          ] = `${list[0]}-${list[1]}`\n                          break\n                        }\n                      }\n                    }\n                  }\n                } else if (keyTypeArr[0] === '7') {\n                  // console.log('自定义')\n                }\n              })\n            }\n          } else if (row.operation_type === 'OP997') {\n            // const path = row.file_path\n            this.dialogConfig.defaultForm2.push({\n              label: '导出文件',\n              name: 'exportFile'\n            })\n            // this.dialogConfig.desData['exportFile'] = '查看' // value值\n          } else if (row.operation_type === 'OP998') {\n            if (operDetailVal.sysMap.uploadFileList.length === 0) {\n              commonMsgWarn('上传文件为空！')\n              return\n            }\n            // const path2 =\n            // operDetailVal.sysMap.uploadFileList.length[0].saveFileName + ','\n            this.dialogConfig.defaultForm2.push({\n              label: '导入文件',\n              name: 'importFile'\n            })\n          } else if (row.operation_type === 'OP999') {\n            const other_oper_interface = operDetailValTitle.other_oper_interface\n            const other_oper_interface_msg = operDetailValTitle.other_oper_interface_msg\n            const other_oper_interface_msg_json = JSON.parse(other_oper_interface_msg)\n            const msg = {\n              parameterList: [],\n              pageSize: this.$store.getters.pageSize,\n              currentPage: 1,\n              other_msg: other_oper_interface_msg_json\n            }\n            instOther(other_oper_interface, msg).then((response) => {\n              const { list, totalNum } = response.retMap\n              for (const key in operDetailValTitle) {\n              // 操作请求详情数据格式化\n                let key_type = ''\n                show_content_sort_list.forEach((item) => {\n                  if (key === item) {\n                    this.dialogConfig.tableData.tableColumns.push({\n                      label: operDetailValTitle[key],\n                      name: item,\n                      id: uuidv1()\n                    })\n                    this.dialogConfig.tableData.componentProps.data = list // value值\n                    this.dialogConfig.tableData.pageList.totalNum = totalNum // 页码\n                  }\n                  for (const i in data_translation) {\n                    if (i === item) {\n                      key_type = data_translation[i]\n                      const keyTypeArr = commonBlank(key_type)\n                        ? []\n                        : key_type.split('@')\n                      this.dialogConfig.tableData.componentProps.data.forEach(\n                        (item2) => {\n                          if (commonBlank(key_type) || keyTypeArr[0] === '1') {\n                            // 正常文本   不做数据格式化直接赋值\n                          } else if (\n                            !commonBlank(key_type) &&\n                            key_type !== 'ORGAN_NO' &&\n                            key_type.indexOf('@') === -1\n                          ) {\n                            // 数据字典\n                            dictionaryFieds(key_type).forEach((key2) => {\n                              if (key2.value === item2[item]) {\n                                item2[item] = key2.label\n                              }\n                            })\n                          } else if (keyTypeArr[0] === '2') {\n                            // 自定义数据源(支持多个格式化)\n                          } else if (\n                            keyTypeArr[0] === '3' ||\n                            keyTypeArr[0] === '4'\n                          ) {\n                            // 数据源为机构\n                            item2[item] = organNameFormat(item2[item])\n                          } else if (keyTypeArr[0] === '5') {\n                            // 外表数据源\n                            const data =\n                              this.$store.getters.externalData[key_type]\n                            for (const key2 of data) {\n                              if (\n                                key2.value === this.dialogConfig.desData[item]\n                              ) {\n                                item2[item] = `${key2.value}-${key2.label}`\n                                break\n                              }\n                            }\n                          } else if (keyTypeArr[0] === '6') {\n                            // 自定义下拉数据\n                            if (!commonBlank(operDetailVal[item])) {\n                              if (operDetailVal[item].indexOf('||') !== -1) {\n                                const data = operDetailVal[item].split('||')\n                                for (const key2 of data) {\n                                  const list = key2.split('-')\n                                  if (list[0] === item2[item]) {\n                                    item2[item] = `${list[0]}-${list[1]}`\n                                    break\n                                  }\n                                }\n                              }\n                            }\n                          }\n                        }\n                      )\n                    }\n                  }\n                })\n              }\n            })\n          } else {\n            commonMsgInfo('暂不支持该种类型配置', this)\n            this.showAgreeBtn = false // 同意不同意按钮隐藏\n            // 审批任务时关闭释放任务\n            if (\n              this.$store.getters.releaseTaskFlag &&\n              !commonBlank(this.bindTaskId)\n            ) {\n              releaseFlowTask(this.bindTaskId)\n            }\n          }\n        }\n        // 2- 处理明细表格数据处理\n        this.tableDetail.componentProps.data = this.dialogConfig.tableDetailData\n        this.tableDetail.componentProps.data.forEach((item) => {\n          // 待审批角色格式化\n          const roleArr = []\n          this.$store.getters.roleList.map((val) => {\n            if (!commonBlank(item.role_no)) {\n              item.role_no.split(',').map((i) => {\n                if (i === val.value) {\n                  roleArr.push(val.label)\n                }\n                return roleArr\n              })\n            }\n          })\n          item.role_no = roleArr.join()\n        })\n      })\n      // }\n    },\n    /*\n     * 操作详情\n     * 新增/修改/导入/导出/批量操作-- 表单description形式--查看流程详情\n     */\n    openFlowDetail() {\n      this.flowConfig.module_id = this.dialogConfig.currentRow.module_id\n      this.flowConfig.visible = true\n    }, // 流程弹窗关闭\n    dialogFlowClose() {\n      this.flowConfig.visible = false\n    },\n    /*\n     * 操作详情-操作类型为导入/导出\n     * 导入/导出--表单description形式--查看（预览导入/导出文件详情）\n     */\n    importFileDetail() {\n      // 调用预览方法\n      this.preViewVis = true\n      const urlname = this.dialogConfig.currentRow.file_path.split('_')\n      const fileName = `${urlname[1]}_${urlname[2]}`\n      const fileUrl = this.dialogConfig.currentRow.file_path\n      this.fiePreview(fileName, fileUrl)\n      this.fiePreview(fileName, fileUrl)\n      this.previewData.visible = true\n      // alert('预览导入文件详情（预览功能未开发）')\n    },\n    /**\n     * 操作详情\n     * 处理明细-附件-查看\n     * @param row-当前行数据*/\n    openFile(row) {\n      this.tableFileDialog.visible = true\n      this.tableFileDialog.tableConfig.componentProps.data = []\n      const rows = []\n      const file_url = row.attachment.split(',')\n      for (const file of file_url) {\n        if (!commonBlank(file)) {\n          rows.push(file)\n        }\n      }\n      for (const item of rows) {\n        const file = uploadFile(item)\n        this.tableFileDialog.tableConfig.componentProps.data.push(file[0])\n      }\n    },\n    /**\n     *详情页面-table-按钮-预览\n     */\n    preview(row) {\n      this.preViewVis = true\n      this.fiePreview(row.name, row.url) // 文件预览方法\n      this.previewData.visible = true\n    },\n    /**\n     * 文件预览方法\n     * @param {String} fileName 文件名\n     * @param {String} fileUrl 文件路径\n     */\n    fiePreview(fileName, fileUrl) {\n      const loginKkIP = this.$store.getters.loginKkIP // kk ip地址\n      const loginKkport = this.$store.getters.loginKkport // kk 端口号\n      const type = fileName.split('.').reverse()[0] // 文件类型 xls pdf jpg\n      const encryptFileName = encodeURIComponent(\n        encryptResult(\n          this.$store.getters.initParams.enSecMap.encryptType,\n          fileName\n        )\n      ) // fileName '1111.pdf'\n      const encryptFName = encodeURIComponent(\n        encryptResult(\n          this.$store.getters.initParams.enSecMap.encryptType,\n          fileUrl.split('##')[0]\n        )\n      ) // fileUrl  \"/home/<USER>/TempFiles/uploadTemp/c3231d5e-359c-4bce-9540-a9ed35f6a4f8_1111.pdf\"\n      const fileNames = fileName.split('##')[0]\n      const watermark = dateNowFormat10() + ` ${this.$store.getters.userName}`\n      const href = window.location.href.split('#')[0].slice(0, -1) // 生产环境 http://**********/dopUnify/\n\n      const urls =\n        href + process.env.VUE_APP_BASE_API + prefix + '/download/file.do' // 生产环境地址\n      // const urls = 'http://127.0.0.1:5001/dev-api/unify/download/file.do' // 开发环境地址 /prod-api-------------------\n\n      const str = `${urls}?fileName=${encryptFileName}&saveFileName=${encryptFName}&isEncrypt=1&fullfilename=${fileNames}`\n\n      const aa = `http://${loginKkIP}:${loginKkport}/onlinePreview?url=${encodeURIComponent(\n        Base64.encode(str)\n      )}&watermarkTxt=${encodeURIComponent(watermark)}`// 开发环境\n\n      // const aa = `http://${this.$store.getters.loginKkIP}:${\n      //   this.$store.getters.loginKkport\n      // }/onlinePreview?url=${encodeURIComponent(\n      //   Base64.encode(str)\n      // )}&watermarkTxt=${encodeURIComponent(watermark)}`\n      // const aa = `http://***********:8018/onlinePreview?url=${encodeURIComponent(\n      //   Base64.encode(str)\n      // )}&watermarkTxt=${encodeURIComponent(watermark)}` // 生产环境地址\n\n      if (type === 'pdf') {\n        this.previewData.imageSrc = str\n      } else if (type === 'jpg') {\n        this.previewData.imageSrc = str\n      } else if (type === 'xls') {\n        this.previewData.imageSrc = aa\n      } else if (type === 'xlsx') {\n        this.previewData.imageSrc = aa\n      } else if (type === 'doc') {\n        this.previewData.imageSrc = aa\n      } else if (type === 'docx') {\n        this.previewData.imageSrc = aa\n      }\n    },\n    // fiePreview(fileName, fileUrl) {\n    //   const type = fileName.split('.').reverse()[0] // 文件类型 xls pdf jpg\n    //   const encryptFileName = encodeURIComponent(\n    //     encryptResult(\n    //       this.$store.getters.initParams.enSecMap.encryptType,\n    //       fileName\n    //     )\n    //   ) // fileName '1111.pdf'\n    //   const encryptFName = encodeURIComponent(\n    //     encryptResult(\n    //       this.$store.getters.initParams.enSecMap.encryptType,\n    //       fileUrl\n    //     )\n    //   ) // fileUrl  \"/home/<USER>/TempFiles/uploadTemp/c3231d5e-359c-4bce-9540-a9ed35f6a4f8_1111.pdf\"\n    //   const fileNames = fileName\n    //   const watermark = dateNowFormat10() + ` ${this.$store.getters.userName}`\n    //   const href = window.location.href.split('#')[0] // 生产环境 http://**********/dopUnify/\n\n    //   const urls =\n    //     href + process.env.VUE_APP_BASE_API + prefix + '/download/file.do' // 生产环境地址\n    //   // const urls = 'http://127.0.0.1:5001/dev-api/unify/download/file.do' // 开发环境地址 /prod-api-------------------\n\n    //   const str = `${urls}?fileName=${encryptFileName}&saveFileName=${encryptFName}&isEncrypt=1&fullfilename=${fileNames}`\n\n    //   // const aa = `http://************:8010/onlinePreview?url=${encodeURIComponent(\n    //   //   Base64.encode(str)\n    //   // )}&watermarkTxt=${encodeURIComponent(watermark)}`   // 开发环境\n\n    //   // const aa = `http://${this.$store.getters.loginKkIP}:${\n    //   //   this.$store.getters.loginKkport\n    //   // }/onlinePreview?url=${encodeURIComponent(\n    //   //   Base64.encode(str)\n    //   // )}&watermarkTxt=${encodeURIComponent(watermark)}`\n    //   const aa = `http://************:8010/onlinePreview?url=${encodeURIComponent(\n    //     Base64.encode(str)\n    //   )}&watermarkTxt=${encodeURIComponent(watermark)}` // 生产环境地址\n\n    //   if (type === 'pdf') {\n    //     this.previewData.imageSrc = str\n    //   } else if (type === 'jpg') {\n    //     this.previewData.imageSrc = str\n    //   } else if (type === 'xls') {\n    //     this.previewData.imageSrc = aa\n    //   } else if (type === 'xlsx') {\n    //     this.previewData.imageSrc = aa\n    //   }\n    // },\n    /**\n     * 详情页面-table-按钮-附件- 下载\n     * @param {String} fileName 文件名\n     * @param {String} saveFileName 文件路径*/\n    downloadFile(row) {\n      commonMsgConfirm('是否确认下载?', this, (param) => {\n        if (param) {\n          commonDownLoadFile(row.name, row.url)\n        }\n      })\n    },\n    /**\n     *详情页面-附件查看弹框关闭\n     */\n    fileDialogClose() {\n      this.tableFileDialog.visible = false\n    },\n    /**\n     * 操作详情\n     * 按钮-同意\n     */\n    handleAgree() {\n      commonMsgConfirm('是否确认通过该申请？', this, (param) => {\n        if (param) {\n          if (this.dialogConfig?.isSublicense) {\n            // 转授权同意操作\n            this.dialogConfig.isSublicense = false\n            const curRow = this.dialogConfig.currentRow\n            const msg = {\n              parameterList: [],\n              sysMap: {\n                inst_id: curRow.sublicense_id,\n                item_id: curRow.item_id, // --------------------\n                deal_result: '2',\n                module_id: curRow.module_id, // -----------------\n                roles: curRow.roles,\n                target_user_no: curRow.target_user_no,\n                user_no: curRow.user_no,\n                start_date: curRow.start_date,\n                end_date: curRow.end_date\n              }\n            }\n            approveFlow(msg).then((res) => {\n              if (res.retCode === '200') {\n                this.$store.dispatch('flowPath/releaseFlag', false)\n                commonMsgSuccess('审批成功', this)\n                this.dialogConfig.visible = false // 详情弹框关闭\n                this.$nextTick(() => {\n                  this.$bus.$emit('moudleSubUpdate', true) // true  // 更新系统消息弹窗的数据/更新系统消息右下角弹窗数据\n                })\n              } else {\n                commonMsgError('审批失败', this)\n              }\n            })\n          } else {\n            // isSublicense不存在标识不是转授权的同意操作\n            this.dialogFile.visible = true\n            this.btnId = 'agree'\n          }\n        }\n      })\n    },\n    /* 操作详情\n     *按钮-不同意  */\n    handleNotAgree() {\n      commonMsgConfirm('是否确认否决该申请？', this, (param) => {\n        if (param) {\n          if (this.dialogConfig?.isSublicense) {\n            // 转授权拒绝操作\n            const curRow = this.dialogConfig.currentRow\n            const msg = {\n              parameterList: [],\n              sysMap: {\n                inst_id: curRow.sublicense_id,\n                item_id: curRow.item_id,\n                deal_result: '3',\n                module_id: curRow.module_id,\n                roles: curRow.roles,\n                target_user_no: curRow.target_user_no,\n                user_no: curRow.user_no,\n                start_date: curRow.start_date,\n                end_date: curRow.end_date\n              }\n            }\n            approveFlow(msg).then((res) => {\n              if (res.retCode === '200') {\n                this.$store.dispatch('flowPath/releaseFlag', false) // 无需释放任务\n                this.$nextTick(() => {\n                  this.$bus.$emit('moudleSubUpdate', true) // true  // 更新系统消息弹窗的数据/更新系统消息右下角弹窗数据\n                })\n                commonMsgSuccess('审批成功', this)\n                this.dialogConfig.visible = false // 详情弹框关闭\n              } else {\n                commonMsgError('审批失败', this)\n              }\n            })\n          } else {\n            // 非转授权拒绝操作\n            this.dialogFile.visible = true\n            this.btnId = 'notAgree'\n          }\n        }\n      })\n    },\n    /**\n     *详情-按钮同意/不同意-上传文件提交 */\n    dialogSubmit() {\n      const curRow = this.dialogConfig.currentRow\n      // 上传文件请求\n      const formData = new FormData() //  用FormData存放上传文件\n      this.fileList.forEach((file) => {\n        formData.append('file', file.raw) // file.raw\n      })\n      upload(formData).then((response) => {\n        // 文件上传成功过后再发起同意请求\n        if (response.retCode === '200') {\n          // 处理上传文件url\n          const { uploadFileList } = response.retMap\n          for (const File of uploadFileList) {\n            this.dialogFile.form.defaultForm.file_url += File.saveFileName + ','\n          }\n          const msg = {\n            parameterList: [],\n            sysMap: {\n              inst_id: curRow.inst_id,\n              item_id: curRow.item_id,\n              deal_result: this.btnId === 'agree' ? '2' : '3', // 同意  \"2\"   不同意\"3\"\n              menu_id: curRow.operation_desc,\n              module_id: curRow.module_id,\n              plug_in: {\n                user_comment: this.dialogFile.form.defaultForm.postscript,\n                attachment: this.dialogFile.form.defaultForm.file_url\n              },\n              flowParameter: {\n                f_deal_result: this.btnId === 'agree' ? '2' : '3',\n                f_organ_level: this.$store.getters.organLevel\n              }\n            }\n          }\n\n          if (this.btnId === 'agree') {\n            // 同意操作请求\n            approvalResult(msg).then((res) => {\n              // 审批成功相关操作\n              if (res.retCode === '200') {\n                this.$store.dispatch('flowPath/releaseFlag', false) // 同意审批流程时不需要释放任务\n                // isEnd 流程是否完结标志\n                const { isEnd } = res.retMap\n                if (isEnd) {\n                  // 流程完结\n                  // 获取提交信息\n                  let operation_value = JSON.parse(curRow.operation_value)\n\n                  let msg2 = {} // 请求提交参数\n                  const url = curRow.oper_do // 请求地址\n                  let applyType = 'post' // 请求方式\n                  if (curRow.operation_type === 'OP002') {\n                    // 配置类型为删除 // 删除时将提交申请的操作参数变为删除参数\n                    if (\n                      !commonBlank(operation_value.flag) &&\n                      operation_value.flag === 'custom'\n                    ) {\n                      msg2 = {\n                        parameterList: [{}],\n                        sysMap: {\n                          operation_value: operation_value.mainDataMap\n                        }\n                      }\n                    } else {\n                      msg2 = {\n                        parameterList: [{}],\n                        sysMap: {\n                          operation_value: operation_value.deleteData_list\n                        }\n                      }\n                    }\n\n                    applyType = 'delete'\n                  } else if (\n                    curRow.operation_type === 'OP001' ||\n                    curRow.operation_type === 'OP003'\n                  ) {\n                    // 配置类型为新增或修改\n                    if (\n                      !commonBlank(operation_value.flag) &&\n                      operation_value.flag === 'custom'\n                    ) {\n                      // 区分自定义模块\n                      operation_value =\n                        operation_value.mainDataMap.parameterList[0]\n                    }\n                    msg2 = {\n                      parameterList: [operation_value],\n                      sysMap: {\n                        operation_value: operation_value\n                      }\n                    }\n                  } else if (curRow.operation_type === 'OP998') {\n                    // 配置类型为导入\n                    msg2 = {\n                      parameterList: [{}],\n                      sysMap: {\n                        uploadFileList: operation_value.sysMap.uploadFileList,\n                        headerRowNum: operation_value.sysMap.headerRowNum,\n                        module_no: operation_value.sysMap.module_no,\n                        table_name: operation_value.sysMap.table_name,\n                        pkList: operation_value.sysMap.pkList,\n                        procedure_name: operation_value.sysMap.procedure_name,\n                        columnInfoList: operation_value.sysMap.columnInfoList,\n                        queryList: operation_value.sysMap.queryList,\n                        add_way: operation_value.sysMap.add_way,\n                        user_no: operation_value.sysMap.user_no,\n                        operation_value: operation_value,\n                        instId: curRow.inst_id\n                      }\n                    }\n                  } else if (curRow.operation_type === 'OP997') {\n                    // 改为后台直接处理\n                    // 1.如果是系统消息页面 更新处理状态和时间   2.如果是其他数据审核工作台处理的数据强制更新其他页面的数据（重新查询列表queryList）\n                    return\n                  } else {\n                    // 除 新增、修改、删除、导入、导出之外的操作\n                    msg2 = {\n                      parameterList: [operation_value],\n                      sysMap: {\n                        operation_value: operation_value\n                      }\n                    }\n                  }\n                  // 同意操作\n                  const operRequestUser = operation_value.hasOwnProperty.call(\n                    'operation_user'\n                  )\n                    ? operation_value.operation_user\n                    : undefined\n                  sessionStorage.setItem('operRequestUser', operRequestUser) // 需要在request.js里添加operRequestUser的请求头\n\n                  operationApply(msg2, url, applyType).then((res2) => {\n                    if (res2.retCode === '200') {\n                      // 导入失败不为0  执行异常\n                      if (curRow.operation_type === 'OP997') {\n                        if (\n                          !commonBlank(res2.retMap.failCount) &&\n                          parseInt(res2.retMap.failCount) > 0\n                        ) {\n                          const msg3 = {\n                            parameterList: [],\n                            sysMap: {\n                              oper_type: 'errorOperation',\n                              operation_type: 'OP997',\n                              inst_id: curRow.inst_id,\n                              error_msg: res2.retMsg,\n                              remarks: JSON.stringify(res2.retMap)\n                            }\n                          }\n                          errorOperation(msg3).then((res3) => {})\n                        }\n                      }\n                      // 其他情况\n                      this.$nextTick(() => {\n                        this.$bus.$emit('operApproveUpdate', true) // true  // 更新系统消息弹窗的数据/数据审核工作台数据/更新系统消息右下角弹窗数据\n                      })\n                      commonMsgSuccess('操作处理成功!', this)\n                    } else {\n                      commonMsgError('操作处理失败：' + res2.retMsg, this)\n                      var msg3 = {\n                        parameterList: [],\n                        sysMap: {\n                          oper_type: 'errorOperation',\n                          operation_type: curRow.operation_type,\n                          inst_id: curRow.inst_id,\n                          error_msg: res2.retMsg\n                        }\n                      }\n                      errorOperation(msg3).then((res3) => {\n                        commonMsgError(res3.retMsg, this)\n                      })\n                    }\n                    sessionStorage.removeItem('operRequestUser') // 随存随删，不会占用内存\n                  })\n                } else {\n                  // 流程尚未结束\n                  commonMsgSuccess('审批成功', this)\n                }\n              } else {\n                // 审批失败\n                commonMsgError('审批失败', this)\n              }\n              this.dialogFile.visible = false // 上传文件弹框关闭\n              this.dialogConfig.visible = false // 详情弹框关闭\n            })\n          } else if (this.btnId === 'notAgree') {\n            approvalResult(msg).then((res) => {\n              if (res.retCode === '200') {\n                // 不同意操作请求\n                this.$store.dispatch('flowPath/releaseFlag', false) // 无需释放任务\n                this.$nextTick(() => {\n                  this.$bus.$emit('operApproveUpdate', true) // true  // 更新系统消息弹窗的数据/更新系统消息右下角弹窗数据\n                })\n                commonMsgSuccess('审批成功', this)\n                this.dialogFile.visible = false // 上传文件弹框关闭\n                this.dialogConfig.visible = false // 详情弹框关闭\n              } else {\n                commonMsgError('审批失败', this)\n              }\n            })\n          }\n        }\n      })\n    },\n    /**\n     * 删除上传文件列表时的回调函数\n     * @param {Array} fileList 文件列表*/\n    delFileList(fileList) {\n      this.delFile = fileList\n    },\n    /**\n     * 上传文件列表状态改变时的回调函数\n     * @param {Array} fileList 文件列表*/\n    changeFileList(fileList) {\n      this.fileList = fileList\n    },\n    /**\n     * 上传文件个数超出时的回调函数\n     * @param {Array} fileList 文件列表*/\n    onExceed(fileList) {\n      if (fileList.length === 3) {\n        commonMsgWarn('最大上传附件个数为3！', this)\n      }\n    },\n    /**\n     *详情-按钮同意（不同意）-上传文件-取消弹框关闭 */\n    dialogFileClose() {\n      this.dialogFile.visible = false\n    },\n    /**\n     * 弹出框关闭\n     */\n    dialogClose() {\n      this.$nextTick(() => {\n        this.$emit('dialogClose', false)\n      })\n    }\n  }\n}\n</script>\n<style lang=\"scss\" scoped>\n@import '@/assets/scss/main.scss';\n$ParamsBorder: 1px solid #f1f1f1; // 线框\n.detailDialog {\n  ::v-deep .el-dialog {\n    height: 90%;\n    margin-bottom: 0 !important;\n    .el-dialog__body {\n      .title {\n        font-size: 14px;\n        margin-bottom: 1rem;\n        font-weight: bold;\n      }\n      .el-descriptions {\n        border: $ParamsBorder;\n        border-radius: 1rem;\n        padding: 2rem 2rem 1rem 1rem;\n        margin-bottom: 2rem;\n        .el-descriptions__header {\n          margin-bottom: 1.5rem;\n          .el-descriptions__title {\n            font-size: 14px;\n            font-weight: bold;\n            color: #606266;\n          }\n        }\n        .el-descriptions__body {\n          margin-left: 4rem;\n        }\n      }\n      .el-table {\n        height: 170px !important;\n      }\n    }\n  }\n}\n\n.dialog-footer {\n  display: flex;\n  justify-content: flex-end;\n  .footerRightBtn {\n    margin-right: 10px;\n    .rightBtn {\n      margin: 0rem 20rem 0rem 2rem;\n      position: absolute;\n      right: 2rem;\n    }\n  }\n}\n</style>\n"]}]}