{"remainingRequest": "D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\node_modules\\thread-loader\\dist\\cjs.js!D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\node_modules\\babel-loader\\lib\\index.js!D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\src\\layout\\index.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\src\\layout\\index.vue", "mtime": 1703583640655}, {"path": "D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\babel.config.js", "mtime": 1686019818359}, {"path": "D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\node_modules\\thread-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1655715099000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, null]}