{"remainingRequest": "D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\node_modules\\babel-loader\\lib\\index.js!D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\node_modules\\qiankun\\es\\sandbox\\patchers\\dynamicAppend\\common.js", "dependencies": [{"path": "D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\node_modules\\qiankun\\es\\sandbox\\patchers\\dynamicAppend\\common.js", "mtime": 1667130453000}, {"path": "D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\babel.config.js", "mtime": 1686019818359}, {"path": "D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["_isFunction", "_slicedToArray", "execScripts", "frameworkConfiguration", "qiankunHeadTagName", "trustedGlobals", "css", "rawHeadAppendChild", "HTMLHeadElement", "prototype", "append<PERSON><PERSON><PERSON>", "rawHeadRemoveChild", "<PERSON><PERSON><PERSON><PERSON>", "rawBodyAppendChild", "HTMLBodyElement", "rawBodyRemoveChild", "rawHeadInsertBefore", "insertBefore", "rawRemoveChild", "HTMLElement", "SCRIPT_TAG_NAME", "LINK_TAG_NAME", "STYLE_TAG_NAME", "styleElementTargetSymbol", "Symbol", "getAppWrapperHeadElement", "appWrapper", "querySelector", "isExecutableScriptType", "script", "type", "indexOf", "isHijackingTag", "tagName", "toUpperCase", "isStyledComponentsLike", "element", "_element$sheet", "_getStyledElementCSSR", "textContent", "sheet", "cssRules", "length", "getStyledElementCSSRules", "appsCounterMap", "Map", "calcAppCount", "appName", "calcType", "status", "appCount", "get", "bootstrappingPatchCount", "mountingPatchCount", "concat", "set", "isAllAppsUnmounted", "Array", "from", "entries", "every", "_ref", "_ref2", "_ref2$", "bpc", "mpc", "patchCustomEvent", "e", "elementGetter", "Object", "defineProperties", "srcElement", "target", "manualInvokeElementOnLoad", "loadEvent", "CustomEvent", "patchedEvent", "onload", "dispatchEvent", "manualInvokeElementOnError", "errorEvent", "onerror", "convertLinkAsStyle", "postProcess", "fetchFn", "arguments", "undefined", "fetch", "styleElement", "document", "createElement", "href", "dataset", "qiankunHref", "then", "res", "text", "styleContext", "createTextNode", "catch", "styledComponentCSSRulesMap", "WeakMap", "dynamicScriptAttachedCommentMap", "dynamicLinkAttachedInlineStyleMap", "recordStyledComponentsCSSRules", "styleElements", "for<PERSON>ach", "HTMLStyleElement", "styledElement", "getOverwrittenAppendChildOrInsertBefore", "opts", "appendChildOrInsertBefore", "<PERSON><PERSON><PERSON><PERSON>", "refChild", "rawDOMAppendOrInsertBefore", "isInvokedByMicroApp", "containerConfigGetter", "_opts$target", "call", "containerConfig", "appWrapperGetter", "proxy", "strictGlobal", "speedySandbox", "dynamicStyleSheetElements", "scopedCSS", "excludeAssetFilter", "stylesheetElement", "_stylesheetElement", "defineProperty", "value", "writable", "configurable", "_element$tagName", "linkElementUsingStylesheet", "rel", "_frameworkConfigurati", "_fetch", "fn", "process", "mountDOM", "push", "referenceNode", "contains", "_element", "src", "_appWrapper", "_mountDOM", "_fetch2", "_referenceNode", "scopedGlobalVariables", "beforeExec", "isCurrentScriptConfigurable", "descriptor", "getOwnPropertyDescriptor", "success", "error", "dynamicScriptCommentElement", "createComment", "dynamicInlineScriptCommentElement", "getNewRemoveChild", "headOrBodyRemoveChild", "child", "attachedElement", "_containerConfigGette", "dynamicElementIndex", "splice", "container", "parentNode", "console", "warn", "patchHTMLDynamicAppendPrototypeFunctions", "unpatch", "rebuildCSSRules", "styleSheetElements", "reAppendElement", "appendSuccess", "i", "cssRule", "cssStyleSheetElement", "insertRule", "cssText"], "sources": ["D:/1_Project/XYD_Project/dop-4.0/dop-4.1-qianduan-unify/dopUnify/node_modules/qiankun/es/sandbox/patchers/dynamicAppend/common.js"], "sourcesContent": ["import _isFunction from \"lodash/isFunction\";\nimport _slicedToArray from \"@babel/runtime/helpers/esm/slicedToArray\";\n/**\n * <AUTHOR>\n * @since 2019-10-21\n */\nimport { execScripts } from 'import-html-entry';\nimport { frameworkConfiguration } from '../../../apis';\nimport { qiankunHeadTagName } from '../../../utils';\nimport { trustedGlobals } from '../../common';\nimport * as css from '../css';\nexport var rawHeadAppendChild = HTMLHeadElement.prototype.appendChild;\nvar rawHeadRemoveChild = HTMLHeadElement.prototype.removeChild;\nvar rawBodyAppendChild = HTMLBodyElement.prototype.appendChild;\nvar rawBodyRemoveChild = HTMLBodyElement.prototype.removeChild;\nvar rawHeadInsertBefore = HTMLHeadElement.prototype.insertBefore;\nvar rawRemoveChild = HTMLElement.prototype.removeChild;\nvar SCRIPT_TAG_NAME = 'SCRIPT';\nvar LINK_TAG_NAME = 'LINK';\nvar STYLE_TAG_NAME = 'STYLE';\nexport var styleElementTargetSymbol = Symbol('target');\nexport var getAppWrapperHeadElement = function getAppWrapperHeadElement(appWrapper) {\n  return appWrapper.querySelector(qiankunHeadTagName);\n};\nexport function isExecutableScriptType(script) {\n  return !script.type || ['text/javascript', 'module', 'application/javascript', 'text/ecmascript', 'application/ecmascript'].indexOf(script.type) !== -1;\n}\nexport function isHijackingTag(tagName) {\n  return (tagName === null || tagName === void 0 ? void 0 : tagName.toUpperCase()) === LINK_TAG_NAME || (tagName === null || tagName === void 0 ? void 0 : tagName.toUpperCase()) === STYLE_TAG_NAME || (tagName === null || tagName === void 0 ? void 0 : tagName.toUpperCase()) === SCRIPT_TAG_NAME;\n}\n/**\n * Check if a style element is a styled-component liked.\n * A styled-components liked element is which not have textContext but keep the rules in its styleSheet.cssRules.\n * Such as the style element generated by styled-components and emotion.\n * @param element\n */\nexport function isStyledComponentsLike(element) {\n  var _element$sheet, _getStyledElementCSSR;\n  return !element.textContent && (((_element$sheet = element.sheet) === null || _element$sheet === void 0 ? void 0 : _element$sheet.cssRules.length) || ((_getStyledElementCSSR = getStyledElementCSSRules(element)) === null || _getStyledElementCSSR === void 0 ? void 0 : _getStyledElementCSSR.length));\n}\nvar appsCounterMap = new Map();\nexport function calcAppCount(appName, calcType, status) {\n  var appCount = appsCounterMap.get(appName) || {\n    bootstrappingPatchCount: 0,\n    mountingPatchCount: 0\n  };\n  switch (calcType) {\n    case 'increase':\n      appCount[\"\".concat(status, \"PatchCount\")] += 1;\n      break;\n    case 'decrease':\n      // bootstrap patch just called once but its freer will be called multiple times\n      if (appCount[\"\".concat(status, \"PatchCount\")] > 0) {\n        appCount[\"\".concat(status, \"PatchCount\")] -= 1;\n      }\n      break;\n  }\n  appsCounterMap.set(appName, appCount);\n}\nexport function isAllAppsUnmounted() {\n  return Array.from(appsCounterMap.entries()).every(function (_ref) {\n    var _ref2 = _slicedToArray(_ref, 2),\n      _ref2$ = _ref2[1],\n      bpc = _ref2$.bootstrappingPatchCount,\n      mpc = _ref2$.mountingPatchCount;\n    return bpc === 0 && mpc === 0;\n  });\n}\nfunction patchCustomEvent(e, elementGetter) {\n  Object.defineProperties(e, {\n    srcElement: {\n      get: elementGetter\n    },\n    target: {\n      get: elementGetter\n    }\n  });\n  return e;\n}\nfunction manualInvokeElementOnLoad(element) {\n  // we need to invoke the onload event manually to notify the event listener that the script was completed\n  // here are the two typical ways of dynamic script loading\n  // 1. element.onload callback way, which webpack and loadjs used, see https://github.com/muicss/loadjs/blob/master/src/loadjs.js#L138\n  // 2. addEventListener way, which toast-loader used, see https://github.com/pyrsmk/toast/blob/master/src/Toast.ts#L64\n  var loadEvent = new CustomEvent('load');\n  var patchedEvent = patchCustomEvent(loadEvent, function () {\n    return element;\n  });\n  if (_isFunction(element.onload)) {\n    element.onload(patchedEvent);\n  } else {\n    element.dispatchEvent(patchedEvent);\n  }\n}\nfunction manualInvokeElementOnError(element) {\n  var errorEvent = new CustomEvent('error');\n  var patchedEvent = patchCustomEvent(errorEvent, function () {\n    return element;\n  });\n  if (_isFunction(element.onerror)) {\n    element.onerror(patchedEvent);\n  } else {\n    element.dispatchEvent(patchedEvent);\n  }\n}\nfunction convertLinkAsStyle(element, postProcess) {\n  var fetchFn = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : fetch;\n  var styleElement = document.createElement('style');\n  var href = element.href;\n  // add source link element href\n  styleElement.dataset.qiankunHref = href;\n  fetchFn(href).then(function (res) {\n    return res.text();\n  }).then(function (styleContext) {\n    styleElement.appendChild(document.createTextNode(styleContext));\n    postProcess(styleElement);\n    manualInvokeElementOnLoad(element);\n  }).catch(function () {\n    return manualInvokeElementOnError(element);\n  });\n  return styleElement;\n}\nvar styledComponentCSSRulesMap = new WeakMap();\nvar dynamicScriptAttachedCommentMap = new WeakMap();\nvar dynamicLinkAttachedInlineStyleMap = new WeakMap();\nexport function recordStyledComponentsCSSRules(styleElements) {\n  styleElements.forEach(function (styleElement) {\n    /*\n     With a styled-components generated style element, we need to record its cssRules for restore next re-mounting time.\n     We're doing this because the sheet of style element is going to be cleaned automatically by browser after the style element dom removed from document.\n     see https://www.w3.org/TR/cssom-1/#associated-css-style-sheet\n     */\n    if (styleElement instanceof HTMLStyleElement && isStyledComponentsLike(styleElement)) {\n      if (styleElement.sheet) {\n        // record the original css rules of the style element for restore\n        styledComponentCSSRulesMap.set(styleElement, styleElement.sheet.cssRules);\n      }\n    }\n  });\n}\nexport function getStyledElementCSSRules(styledElement) {\n  return styledComponentCSSRulesMap.get(styledElement);\n}\nfunction getOverwrittenAppendChildOrInsertBefore(opts) {\n  return function appendChildOrInsertBefore(newChild) {\n    var refChild = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : null;\n    var element = newChild;\n    var rawDOMAppendOrInsertBefore = opts.rawDOMAppendOrInsertBefore,\n      isInvokedByMicroApp = opts.isInvokedByMicroApp,\n      containerConfigGetter = opts.containerConfigGetter,\n      _opts$target = opts.target,\n      target = _opts$target === void 0 ? 'body' : _opts$target;\n    if (!isHijackingTag(element.tagName) || !isInvokedByMicroApp(element)) {\n      return rawDOMAppendOrInsertBefore.call(this, element, refChild);\n    }\n    if (element.tagName) {\n      var containerConfig = containerConfigGetter(element);\n      var appName = containerConfig.appName,\n        appWrapperGetter = containerConfig.appWrapperGetter,\n        proxy = containerConfig.proxy,\n        strictGlobal = containerConfig.strictGlobal,\n        speedySandbox = containerConfig.speedySandbox,\n        dynamicStyleSheetElements = containerConfig.dynamicStyleSheetElements,\n        scopedCSS = containerConfig.scopedCSS,\n        excludeAssetFilter = containerConfig.excludeAssetFilter;\n      switch (element.tagName) {\n        case LINK_TAG_NAME:\n        case STYLE_TAG_NAME:\n          {\n            var stylesheetElement = newChild;\n            var _stylesheetElement = stylesheetElement,\n              href = _stylesheetElement.href;\n            if (excludeAssetFilter && href && excludeAssetFilter(href)) {\n              return rawDOMAppendOrInsertBefore.call(this, element, refChild);\n            }\n            Object.defineProperty(stylesheetElement, styleElementTargetSymbol, {\n              value: target,\n              writable: true,\n              configurable: true\n            });\n            var appWrapper = appWrapperGetter();\n            if (scopedCSS) {\n              var _element$tagName;\n              // exclude link elements like <link rel=\"icon\" href=\"favicon.ico\">\n              var linkElementUsingStylesheet = ((_element$tagName = element.tagName) === null || _element$tagName === void 0 ? void 0 : _element$tagName.toUpperCase()) === LINK_TAG_NAME && element.rel === 'stylesheet' && element.href;\n              if (linkElementUsingStylesheet) {\n                var _frameworkConfigurati;\n                var _fetch = typeof frameworkConfiguration.fetch === 'function' ? frameworkConfiguration.fetch : (_frameworkConfigurati = frameworkConfiguration.fetch) === null || _frameworkConfigurati === void 0 ? void 0 : _frameworkConfigurati.fn;\n                stylesheetElement = convertLinkAsStyle(element, function (styleElement) {\n                  return css.process(appWrapper, styleElement, appName);\n                }, _fetch);\n                dynamicLinkAttachedInlineStyleMap.set(element, stylesheetElement);\n              } else {\n                css.process(appWrapper, stylesheetElement, appName);\n              }\n            }\n            var mountDOM = target === 'head' ? getAppWrapperHeadElement(appWrapper) : appWrapper;\n            dynamicStyleSheetElements.push(stylesheetElement);\n            var referenceNode = mountDOM.contains(refChild) ? refChild : null;\n            return rawDOMAppendOrInsertBefore.call(mountDOM, stylesheetElement, referenceNode);\n          }\n        case SCRIPT_TAG_NAME:\n          {\n            var _element = element,\n              src = _element.src,\n              text = _element.text;\n            // some script like jsonp maybe not support cors which should't use execScripts\n            if (excludeAssetFilter && src && excludeAssetFilter(src) || !isExecutableScriptType(element)) {\n              return rawDOMAppendOrInsertBefore.call(this, element, refChild);\n            }\n            var _appWrapper = appWrapperGetter();\n            var _mountDOM = target === 'head' ? getAppWrapperHeadElement(_appWrapper) : _appWrapper;\n            var _fetch2 = frameworkConfiguration.fetch;\n            var _referenceNode = _mountDOM.contains(refChild) ? refChild : null;\n            var scopedGlobalVariables = speedySandbox ? trustedGlobals : [];\n            if (src) {\n              execScripts(null, [src], proxy, {\n                fetch: _fetch2,\n                strictGlobal: strictGlobal,\n                scopedGlobalVariables: scopedGlobalVariables,\n                beforeExec: function beforeExec() {\n                  var isCurrentScriptConfigurable = function isCurrentScriptConfigurable() {\n                    var descriptor = Object.getOwnPropertyDescriptor(document, 'currentScript');\n                    return !descriptor || descriptor.configurable;\n                  };\n                  if (isCurrentScriptConfigurable()) {\n                    Object.defineProperty(document, 'currentScript', {\n                      get: function get() {\n                        return element;\n                      },\n                      configurable: true\n                    });\n                  }\n                },\n                success: function success() {\n                  manualInvokeElementOnLoad(element);\n                  element = null;\n                },\n                error: function error() {\n                  manualInvokeElementOnError(element);\n                  element = null;\n                }\n              });\n              var dynamicScriptCommentElement = document.createComment(\"dynamic script \".concat(src, \" replaced by qiankun\"));\n              dynamicScriptAttachedCommentMap.set(element, dynamicScriptCommentElement);\n              return rawDOMAppendOrInsertBefore.call(_mountDOM, dynamicScriptCommentElement, _referenceNode);\n            }\n            // inline script never trigger the onload and onerror event\n            execScripts(null, [\"<script>\".concat(text, \"</script>\")], proxy, {\n              strictGlobal: strictGlobal,\n              scopedGlobalVariables: scopedGlobalVariables\n            });\n            var dynamicInlineScriptCommentElement = document.createComment('dynamic inline script replaced by qiankun');\n            dynamicScriptAttachedCommentMap.set(element, dynamicInlineScriptCommentElement);\n            return rawDOMAppendOrInsertBefore.call(_mountDOM, dynamicInlineScriptCommentElement, _referenceNode);\n          }\n        default:\n          break;\n      }\n    }\n    return rawDOMAppendOrInsertBefore.call(this, element, refChild);\n  };\n}\nfunction getNewRemoveChild(headOrBodyRemoveChild, containerConfigGetter, target) {\n  return function removeChild(child) {\n    var tagName = child.tagName;\n    if (!isHijackingTag(tagName)) return headOrBodyRemoveChild.call(this, child);\n    try {\n      var attachedElement;\n      var _containerConfigGette = containerConfigGetter(child),\n        appWrapperGetter = _containerConfigGette.appWrapperGetter,\n        dynamicStyleSheetElements = _containerConfigGette.dynamicStyleSheetElements;\n      switch (tagName) {\n        case STYLE_TAG_NAME:\n        case LINK_TAG_NAME:\n          {\n            attachedElement = dynamicLinkAttachedInlineStyleMap.get(child) || child;\n            // try to remove the dynamic style sheet\n            var dynamicElementIndex = dynamicStyleSheetElements.indexOf(attachedElement);\n            if (dynamicElementIndex !== -1) {\n              dynamicStyleSheetElements.splice(dynamicElementIndex, 1);\n            }\n            break;\n          }\n        case SCRIPT_TAG_NAME:\n          {\n            attachedElement = dynamicScriptAttachedCommentMap.get(child) || child;\n            break;\n          }\n        default:\n          {\n            attachedElement = child;\n          }\n      }\n      var appWrapper = appWrapperGetter();\n      var container = target === 'head' ? getAppWrapperHeadElement(appWrapper) : appWrapper;\n      // container might have been removed while app unmounting if the removeChild action was async\n      if (container.contains(attachedElement)) {\n        return rawRemoveChild.call(attachedElement.parentNode, attachedElement);\n      }\n    } catch (e) {\n      console.warn(e);\n    }\n    return headOrBodyRemoveChild.call(this, child);\n  };\n}\nexport function patchHTMLDynamicAppendPrototypeFunctions(isInvokedByMicroApp, containerConfigGetter) {\n  // Just overwrite it while it have not been overwrite\n  if (HTMLHeadElement.prototype.appendChild === rawHeadAppendChild && HTMLBodyElement.prototype.appendChild === rawBodyAppendChild && HTMLHeadElement.prototype.insertBefore === rawHeadInsertBefore) {\n    HTMLHeadElement.prototype.appendChild = getOverwrittenAppendChildOrInsertBefore({\n      rawDOMAppendOrInsertBefore: rawHeadAppendChild,\n      containerConfigGetter: containerConfigGetter,\n      isInvokedByMicroApp: isInvokedByMicroApp,\n      target: 'head'\n    });\n    HTMLBodyElement.prototype.appendChild = getOverwrittenAppendChildOrInsertBefore({\n      rawDOMAppendOrInsertBefore: rawBodyAppendChild,\n      containerConfigGetter: containerConfigGetter,\n      isInvokedByMicroApp: isInvokedByMicroApp,\n      target: 'body'\n    });\n    HTMLHeadElement.prototype.insertBefore = getOverwrittenAppendChildOrInsertBefore({\n      rawDOMAppendOrInsertBefore: rawHeadInsertBefore,\n      containerConfigGetter: containerConfigGetter,\n      isInvokedByMicroApp: isInvokedByMicroApp,\n      target: 'head'\n    });\n  }\n  // Just overwrite it while it have not been overwrite\n  if (HTMLHeadElement.prototype.removeChild === rawHeadRemoveChild && HTMLBodyElement.prototype.removeChild === rawBodyRemoveChild) {\n    HTMLHeadElement.prototype.removeChild = getNewRemoveChild(rawHeadRemoveChild, containerConfigGetter, 'head');\n    HTMLBodyElement.prototype.removeChild = getNewRemoveChild(rawBodyRemoveChild, containerConfigGetter, 'body');\n  }\n  return function unpatch() {\n    HTMLHeadElement.prototype.appendChild = rawHeadAppendChild;\n    HTMLHeadElement.prototype.removeChild = rawHeadRemoveChild;\n    HTMLBodyElement.prototype.appendChild = rawBodyAppendChild;\n    HTMLBodyElement.prototype.removeChild = rawBodyRemoveChild;\n    HTMLHeadElement.prototype.insertBefore = rawHeadInsertBefore;\n  };\n}\nexport function rebuildCSSRules(styleSheetElements, reAppendElement) {\n  styleSheetElements.forEach(function (stylesheetElement) {\n    // re-append the dynamic stylesheet to sub-app container\n    var appendSuccess = reAppendElement(stylesheetElement);\n    if (appendSuccess) {\n      /*\n      get the stored css rules from styled-components generated element, and the re-insert rules for them.\n      note that we must do this after style element had been added to document, which stylesheet would be associated to the document automatically.\n      check the spec https://www.w3.org/TR/cssom-1/#associated-css-style-sheet\n       */\n      if (stylesheetElement instanceof HTMLStyleElement && isStyledComponentsLike(stylesheetElement)) {\n        var cssRules = getStyledElementCSSRules(stylesheetElement);\n        if (cssRules) {\n          // eslint-disable-next-line no-plusplus\n          for (var i = 0; i < cssRules.length; i++) {\n            var cssRule = cssRules[i];\n            var cssStyleSheetElement = stylesheetElement.sheet;\n            cssStyleSheetElement.insertRule(cssRule.cssText, cssStyleSheetElement.cssRules.length);\n          }\n        }\n      }\n    }\n  });\n}"], "mappings": ";;;;;;;;;;;AAAA,OAAOA,WAAW,MAAM,mBAAmB;AAC3C,OAAOC,cAAc,MAAM,0CAA0C;AACrE;AACA;AACA;AACA;AACA,SAASC,WAAW,QAAQ,mBAAmB;AAC/C,SAASC,sBAAsB,QAAQ,eAAe;AACtD,SAASC,kBAAkB,QAAQ,gBAAgB;AACnD,SAASC,cAAc,QAAQ,cAAc;AAC7C,OAAO,KAAKC,GAAG,MAAM,QAAQ;AAC7B,OAAO,IAAIC,kBAAkB,GAAGC,eAAe,CAACC,SAAS,CAACC,WAAW;AACrE,IAAIC,kBAAkB,GAAGH,eAAe,CAACC,SAAS,CAACG,WAAW;AAC9D,IAAIC,kBAAkB,GAAGC,eAAe,CAACL,SAAS,CAACC,WAAW;AAC9D,IAAIK,kBAAkB,GAAGD,eAAe,CAACL,SAAS,CAACG,WAAW;AAC9D,IAAII,mBAAmB,GAAGR,eAAe,CAACC,SAAS,CAACQ,YAAY;AAChE,IAAIC,cAAc,GAAGC,WAAW,CAACV,SAAS,CAACG,WAAW;AACtD,IAAIQ,eAAe,GAAG,QAAQ;AAC9B,IAAIC,aAAa,GAAG,MAAM;AAC1B,IAAIC,cAAc,GAAG,OAAO;AAC5B,OAAO,IAAIC,wBAAwB,GAAGC,MAAM,CAAC,QAAQ,CAAC;AACtD,OAAO,IAAIC,wBAAwB,GAAG,SAASA,wBAAwB,CAACC,UAAU,EAAE;EAClF,OAAOA,UAAU,CAACC,aAAa,CAACvB,kBAAkB,CAAC;AACrD,CAAC;AACD,OAAO,SAASwB,sBAAsB,CAACC,MAAM,EAAE;EAC7C,OAAO,CAACA,MAAM,CAACC,IAAI,IAAI,CAAC,iBAAiB,EAAE,QAAQ,EAAE,wBAAwB,EAAE,iBAAiB,EAAE,wBAAwB,CAAC,CAACC,OAAO,CAACF,MAAM,CAACC,IAAI,CAAC,KAAK,CAAC,CAAC;AACzJ;AACA,OAAO,SAASE,cAAc,CAACC,OAAO,EAAE;EACtC,OAAO,CAACA,OAAO,KAAK,IAAI,IAAIA,OAAO,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,OAAO,CAACC,WAAW,EAAE,MAAMb,aAAa,IAAI,CAACY,OAAO,KAAK,IAAI,IAAIA,OAAO,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,OAAO,CAACC,WAAW,EAAE,MAAMZ,cAAc,IAAI,CAACW,OAAO,KAAK,IAAI,IAAIA,OAAO,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,OAAO,CAACC,WAAW,EAAE,MAAMd,eAAe;AACrS;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASe,sBAAsB,CAACC,OAAO,EAAE;EAC9C,IAAIC,cAAc,EAAEC,qBAAqB;EACzC,OAAO,CAACF,OAAO,CAACG,WAAW,KAAK,CAAC,CAACF,cAAc,GAAGD,OAAO,CAACI,KAAK,MAAM,IAAI,IAAIH,cAAc,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,cAAc,CAACI,QAAQ,CAACC,MAAM,MAAM,CAACJ,qBAAqB,GAAGK,wBAAwB,CAACP,OAAO,CAAC,MAAM,IAAI,IAAIE,qBAAqB,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,qBAAqB,CAACI,MAAM,CAAC,CAAC;AAC3S;AACA,IAAIE,cAAc,GAAG,IAAIC,GAAG,EAAE;AAC9B,OAAO,SAASC,YAAY,CAACC,OAAO,EAAEC,QAAQ,EAAEC,MAAM,EAAE;EACtD,IAAIC,QAAQ,GAAGN,cAAc,CAACO,GAAG,CAACJ,OAAO,CAAC,IAAI;IAC5CK,uBAAuB,EAAE,CAAC;IAC1BC,kBAAkB,EAAE;EACtB,CAAC;EACD,QAAQL,QAAQ;IACd,KAAK,UAAU;MACbE,QAAQ,CAAC,EAAE,CAACI,MAAM,CAACL,MAAM,EAAE,YAAY,CAAC,CAAC,IAAI,CAAC;MAC9C;IACF,KAAK,UAAU;MACb;MACA,IAAIC,QAAQ,CAAC,EAAE,CAACI,MAAM,CAACL,MAAM,EAAE,YAAY,CAAC,CAAC,GAAG,CAAC,EAAE;QACjDC,QAAQ,CAAC,EAAE,CAACI,MAAM,CAACL,MAAM,EAAE,YAAY,CAAC,CAAC,IAAI,CAAC;MAChD;MACA;EAAM;EAEVL,cAAc,CAACW,GAAG,CAACR,OAAO,EAAEG,QAAQ,CAAC;AACvC;AACA,OAAO,SAASM,kBAAkB,GAAG;EACnC,OAAOC,KAAK,CAACC,IAAI,CAACd,cAAc,CAACe,OAAO,EAAE,CAAC,CAACC,KAAK,CAAC,UAAUC,IAAI,EAAE;IAChE,IAAIC,KAAK,GAAG7D,cAAc,CAAC4D,IAAI,EAAE,CAAC,CAAC;MACjCE,MAAM,GAAGD,KAAK,CAAC,CAAC,CAAC;MACjBE,GAAG,GAAGD,MAAM,CAACX,uBAAuB;MACpCa,GAAG,GAAGF,MAAM,CAACV,kBAAkB;IACjC,OAAOW,GAAG,KAAK,CAAC,IAAIC,GAAG,KAAK,CAAC;EAC/B,CAAC,CAAC;AACJ;AACA,SAASC,gBAAgB,CAACC,CAAC,EAAEC,aAAa,EAAE;EAC1CC,MAAM,CAACC,gBAAgB,CAACH,CAAC,EAAE;IACzBI,UAAU,EAAE;MACVpB,GAAG,EAAEiB;IACP,CAAC;IACDI,MAAM,EAAE;MACNrB,GAAG,EAAEiB;IACP;EACF,CAAC,CAAC;EACF,OAAOD,CAAC;AACV;AACA,SAASM,yBAAyB,CAACrC,OAAO,EAAE;EAC1C;EACA;EACA;EACA;EACA,IAAIsC,SAAS,GAAG,IAAIC,WAAW,CAAC,MAAM,CAAC;EACvC,IAAIC,YAAY,GAAGV,gBAAgB,CAACQ,SAAS,EAAE,YAAY;IACzD,OAAOtC,OAAO;EAChB,CAAC,CAAC;EACF,IAAIpC,WAAW,CAACoC,OAAO,CAACyC,MAAM,CAAC,EAAE;IAC/BzC,OAAO,CAACyC,MAAM,CAACD,YAAY,CAAC;EAC9B,CAAC,MAAM;IACLxC,OAAO,CAAC0C,aAAa,CAACF,YAAY,CAAC;EACrC;AACF;AACA,SAASG,0BAA0B,CAAC3C,OAAO,EAAE;EAC3C,IAAI4C,UAAU,GAAG,IAAIL,WAAW,CAAC,OAAO,CAAC;EACzC,IAAIC,YAAY,GAAGV,gBAAgB,CAACc,UAAU,EAAE,YAAY;IAC1D,OAAO5C,OAAO;EAChB,CAAC,CAAC;EACF,IAAIpC,WAAW,CAACoC,OAAO,CAAC6C,OAAO,CAAC,EAAE;IAChC7C,OAAO,CAAC6C,OAAO,CAACL,YAAY,CAAC;EAC/B,CAAC,MAAM;IACLxC,OAAO,CAAC0C,aAAa,CAACF,YAAY,CAAC;EACrC;AACF;AACA,SAASM,kBAAkB,CAAC9C,OAAO,EAAE+C,WAAW,EAAE;EAChD,IAAIC,OAAO,GAAGC,SAAS,CAAC3C,MAAM,GAAG,CAAC,IAAI2C,SAAS,CAAC,CAAC,CAAC,KAAKC,SAAS,GAAGD,SAAS,CAAC,CAAC,CAAC,GAAGE,KAAK;EACvF,IAAIC,YAAY,GAAGC,QAAQ,CAACC,aAAa,CAAC,OAAO,CAAC;EAClD,IAAIC,IAAI,GAAGvD,OAAO,CAACuD,IAAI;EACvB;EACAH,YAAY,CAACI,OAAO,CAACC,WAAW,GAAGF,IAAI;EACvCP,OAAO,CAACO,IAAI,CAAC,CAACG,IAAI,CAAC,UAAUC,GAAG,EAAE;IAChC,OAAOA,GAAG,CAACC,IAAI,EAAE;EACnB,CAAC,CAAC,CAACF,IAAI,CAAC,UAAUG,YAAY,EAAE;IAC9BT,YAAY,CAAC9E,WAAW,CAAC+E,QAAQ,CAACS,cAAc,CAACD,YAAY,CAAC,CAAC;IAC/Dd,WAAW,CAACK,YAAY,CAAC;IACzBf,yBAAyB,CAACrC,OAAO,CAAC;EACpC,CAAC,CAAC,CAAC+D,KAAK,CAAC,YAAY;IACnB,OAAOpB,0BAA0B,CAAC3C,OAAO,CAAC;EAC5C,CAAC,CAAC;EACF,OAAOoD,YAAY;AACrB;AACA,IAAIY,0BAA0B,GAAG,IAAIC,OAAO,EAAE;AAC9C,IAAIC,+BAA+B,GAAG,IAAID,OAAO,EAAE;AACnD,IAAIE,iCAAiC,GAAG,IAAIF,OAAO,EAAE;AACrD,OAAO,SAASG,8BAA8B,CAACC,aAAa,EAAE;EAC5DA,aAAa,CAACC,OAAO,CAAC,UAAUlB,YAAY,EAAE;IAC5C;AACJ;AACA;AACA;AACA;IACI,IAAIA,YAAY,YAAYmB,gBAAgB,IAAIxE,sBAAsB,CAACqD,YAAY,CAAC,EAAE;MACpF,IAAIA,YAAY,CAAChD,KAAK,EAAE;QACtB;QACA4D,0BAA0B,CAAC7C,GAAG,CAACiC,YAAY,EAAEA,YAAY,CAAChD,KAAK,CAACC,QAAQ,CAAC;MAC3E;IACF;EACF,CAAC,CAAC;AACJ;AACA,OAAO,SAASE,wBAAwB,CAACiE,aAAa,EAAE;EACtD,OAAOR,0BAA0B,CAACjD,GAAG,CAACyD,aAAa,CAAC;AACtD;AACA,SAASC,uCAAuC,CAACC,IAAI,EAAE;EACrD,OAAO,SAASC,yBAAyB,CAACC,QAAQ,EAAE;IAClD,IAAIC,QAAQ,GAAG5B,SAAS,CAAC3C,MAAM,GAAG,CAAC,IAAI2C,SAAS,CAAC,CAAC,CAAC,KAAKC,SAAS,GAAGD,SAAS,CAAC,CAAC,CAAC,GAAG,IAAI;IACvF,IAAIjD,OAAO,GAAG4E,QAAQ;IACtB,IAAIE,0BAA0B,GAAGJ,IAAI,CAACI,0BAA0B;MAC9DC,mBAAmB,GAAGL,IAAI,CAACK,mBAAmB;MAC9CC,qBAAqB,GAAGN,IAAI,CAACM,qBAAqB;MAClDC,YAAY,GAAGP,IAAI,CAACtC,MAAM;MAC1BA,MAAM,GAAG6C,YAAY,KAAK,KAAK,CAAC,GAAG,MAAM,GAAGA,YAAY;IAC1D,IAAI,CAACrF,cAAc,CAACI,OAAO,CAACH,OAAO,CAAC,IAAI,CAACkF,mBAAmB,CAAC/E,OAAO,CAAC,EAAE;MACrE,OAAO8E,0BAA0B,CAACI,IAAI,CAAC,IAAI,EAAElF,OAAO,EAAE6E,QAAQ,CAAC;IACjE;IACA,IAAI7E,OAAO,CAACH,OAAO,EAAE;MACnB,IAAIsF,eAAe,GAAGH,qBAAqB,CAAChF,OAAO,CAAC;MACpD,IAAIW,OAAO,GAAGwE,eAAe,CAACxE,OAAO;QACnCyE,gBAAgB,GAAGD,eAAe,CAACC,gBAAgB;QACnDC,KAAK,GAAGF,eAAe,CAACE,KAAK;QAC7BC,YAAY,GAAGH,eAAe,CAACG,YAAY;QAC3CC,aAAa,GAAGJ,eAAe,CAACI,aAAa;QAC7CC,yBAAyB,GAAGL,eAAe,CAACK,yBAAyB;QACrEC,SAAS,GAAGN,eAAe,CAACM,SAAS;QACrCC,kBAAkB,GAAGP,eAAe,CAACO,kBAAkB;MACzD,QAAQ1F,OAAO,CAACH,OAAO;QACrB,KAAKZ,aAAa;QAClB,KAAKC,cAAc;UACjB;YACE,IAAIyG,iBAAiB,GAAGf,QAAQ;YAChC,IAAIgB,kBAAkB,GAAGD,iBAAiB;cACxCpC,IAAI,GAAGqC,kBAAkB,CAACrC,IAAI;YAChC,IAAImC,kBAAkB,IAAInC,IAAI,IAAImC,kBAAkB,CAACnC,IAAI,CAAC,EAAE;cAC1D,OAAOuB,0BAA0B,CAACI,IAAI,CAAC,IAAI,EAAElF,OAAO,EAAE6E,QAAQ,CAAC;YACjE;YACA5C,MAAM,CAAC4D,cAAc,CAACF,iBAAiB,EAAExG,wBAAwB,EAAE;cACjE2G,KAAK,EAAE1D,MAAM;cACb2D,QAAQ,EAAE,IAAI;cACdC,YAAY,EAAE;YAChB,CAAC,CAAC;YACF,IAAI1G,UAAU,GAAG8F,gBAAgB,EAAE;YACnC,IAAIK,SAAS,EAAE;cACb,IAAIQ,gBAAgB;cACpB;cACA,IAAIC,0BAA0B,GAAG,CAAC,CAACD,gBAAgB,GAAGjG,OAAO,CAACH,OAAO,MAAM,IAAI,IAAIoG,gBAAgB,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,gBAAgB,CAACnG,WAAW,EAAE,MAAMb,aAAa,IAAIe,OAAO,CAACmG,GAAG,KAAK,YAAY,IAAInG,OAAO,CAACuD,IAAI;cAC3N,IAAI2C,0BAA0B,EAAE;gBAC9B,IAAIE,qBAAqB;gBACzB,IAAIC,MAAM,GAAG,OAAOtI,sBAAsB,CAACoF,KAAK,KAAK,UAAU,GAAGpF,sBAAsB,CAACoF,KAAK,GAAG,CAACiD,qBAAqB,GAAGrI,sBAAsB,CAACoF,KAAK,MAAM,IAAI,IAAIiD,qBAAqB,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,qBAAqB,CAACE,EAAE;gBACxOX,iBAAiB,GAAG7C,kBAAkB,CAAC9C,OAAO,EAAE,UAAUoD,YAAY,EAAE;kBACtE,OAAOlF,GAAG,CAACqI,OAAO,CAACjH,UAAU,EAAE8D,YAAY,EAAEzC,OAAO,CAAC;gBACvD,CAAC,EAAE0F,MAAM,CAAC;gBACVlC,iCAAiC,CAAChD,GAAG,CAACnB,OAAO,EAAE2F,iBAAiB,CAAC;cACnE,CAAC,MAAM;gBACLzH,GAAG,CAACqI,OAAO,CAACjH,UAAU,EAAEqG,iBAAiB,EAAEhF,OAAO,CAAC;cACrD;YACF;YACA,IAAI6F,QAAQ,GAAGpE,MAAM,KAAK,MAAM,GAAG/C,wBAAwB,CAACC,UAAU,CAAC,GAAGA,UAAU;YACpFkG,yBAAyB,CAACiB,IAAI,CAACd,iBAAiB,CAAC;YACjD,IAAIe,aAAa,GAAGF,QAAQ,CAACG,QAAQ,CAAC9B,QAAQ,CAAC,GAAGA,QAAQ,GAAG,IAAI;YACjE,OAAOC,0BAA0B,CAACI,IAAI,CAACsB,QAAQ,EAAEb,iBAAiB,EAAEe,aAAa,CAAC;UACpF;QACF,KAAK1H,eAAe;UAClB;YACE,IAAI4H,QAAQ,GAAG5G,OAAO;cACpB6G,GAAG,GAAGD,QAAQ,CAACC,GAAG;cAClBjD,IAAI,GAAGgD,QAAQ,CAAChD,IAAI;YACtB;YACA,IAAI8B,kBAAkB,IAAImB,GAAG,IAAInB,kBAAkB,CAACmB,GAAG,CAAC,IAAI,CAACrH,sBAAsB,CAACQ,OAAO,CAAC,EAAE;cAC5F,OAAO8E,0BAA0B,CAACI,IAAI,CAAC,IAAI,EAAElF,OAAO,EAAE6E,QAAQ,CAAC;YACjE;YACA,IAAIiC,WAAW,GAAG1B,gBAAgB,EAAE;YACpC,IAAI2B,SAAS,GAAG3E,MAAM,KAAK,MAAM,GAAG/C,wBAAwB,CAACyH,WAAW,CAAC,GAAGA,WAAW;YACvF,IAAIE,OAAO,GAAGjJ,sBAAsB,CAACoF,KAAK;YAC1C,IAAI8D,cAAc,GAAGF,SAAS,CAACJ,QAAQ,CAAC9B,QAAQ,CAAC,GAAGA,QAAQ,GAAG,IAAI;YACnE,IAAIqC,qBAAqB,GAAG3B,aAAa,GAAGtH,cAAc,GAAG,EAAE;YAC/D,IAAI4I,GAAG,EAAE;cACP/I,WAAW,CAAC,IAAI,EAAE,CAAC+I,GAAG,CAAC,EAAExB,KAAK,EAAE;gBAC9BlC,KAAK,EAAE6D,OAAO;gBACd1B,YAAY,EAAEA,YAAY;gBAC1B4B,qBAAqB,EAAEA,qBAAqB;gBAC5CC,UAAU,EAAE,SAASA,UAAU,GAAG;kBAChC,IAAIC,2BAA2B,GAAG,SAASA,2BAA2B,GAAG;oBACvE,IAAIC,UAAU,GAAGpF,MAAM,CAACqF,wBAAwB,CAACjE,QAAQ,EAAE,eAAe,CAAC;oBAC3E,OAAO,CAACgE,UAAU,IAAIA,UAAU,CAACrB,YAAY;kBAC/C,CAAC;kBACD,IAAIoB,2BAA2B,EAAE,EAAE;oBACjCnF,MAAM,CAAC4D,cAAc,CAACxC,QAAQ,EAAE,eAAe,EAAE;sBAC/CtC,GAAG,EAAE,SAASA,GAAG,GAAG;wBAClB,OAAOf,OAAO;sBAChB,CAAC;sBACDgG,YAAY,EAAE;oBAChB,CAAC,CAAC;kBACJ;gBACF,CAAC;gBACDuB,OAAO,EAAE,SAASA,OAAO,GAAG;kBAC1BlF,yBAAyB,CAACrC,OAAO,CAAC;kBAClCA,OAAO,GAAG,IAAI;gBAChB,CAAC;gBACDwH,KAAK,EAAE,SAASA,KAAK,GAAG;kBACtB7E,0BAA0B,CAAC3C,OAAO,CAAC;kBACnCA,OAAO,GAAG,IAAI;gBAChB;cACF,CAAC,CAAC;cACF,IAAIyH,2BAA2B,GAAGpE,QAAQ,CAACqE,aAAa,CAAC,iBAAiB,CAACxG,MAAM,CAAC2F,GAAG,EAAE,sBAAsB,CAAC,CAAC;cAC/G3C,+BAA+B,CAAC/C,GAAG,CAACnB,OAAO,EAAEyH,2BAA2B,CAAC;cACzE,OAAO3C,0BAA0B,CAACI,IAAI,CAAC6B,SAAS,EAAEU,2BAA2B,EAAER,cAAc,CAAC;YAChG;YACA;YACAnJ,WAAW,CAAC,IAAI,EAAE,CAAC,UAAU,CAACoD,MAAM,CAAC0C,IAAI,EAAE,WAAW,CAAC,CAAC,EAAEyB,KAAK,EAAE;cAC/DC,YAAY,EAAEA,YAAY;cAC1B4B,qBAAqB,EAAEA;YACzB,CAAC,CAAC;YACF,IAAIS,iCAAiC,GAAGtE,QAAQ,CAACqE,aAAa,CAAC,2CAA2C,CAAC;YAC3GxD,+BAA+B,CAAC/C,GAAG,CAACnB,OAAO,EAAE2H,iCAAiC,CAAC;YAC/E,OAAO7C,0BAA0B,CAACI,IAAI,CAAC6B,SAAS,EAAEY,iCAAiC,EAAEV,cAAc,CAAC;UACtG;QACF;UACE;MAAM;IAEZ;IACA,OAAOnC,0BAA0B,CAACI,IAAI,CAAC,IAAI,EAAElF,OAAO,EAAE6E,QAAQ,CAAC;EACjE,CAAC;AACH;AACA,SAAS+C,iBAAiB,CAACC,qBAAqB,EAAE7C,qBAAqB,EAAE5C,MAAM,EAAE;EAC/E,OAAO,SAAS5D,WAAW,CAACsJ,KAAK,EAAE;IACjC,IAAIjI,OAAO,GAAGiI,KAAK,CAACjI,OAAO;IAC3B,IAAI,CAACD,cAAc,CAACC,OAAO,CAAC,EAAE,OAAOgI,qBAAqB,CAAC3C,IAAI,CAAC,IAAI,EAAE4C,KAAK,CAAC;IAC5E,IAAI;MACF,IAAIC,eAAe;MACnB,IAAIC,qBAAqB,GAAGhD,qBAAqB,CAAC8C,KAAK,CAAC;QACtD1C,gBAAgB,GAAG4C,qBAAqB,CAAC5C,gBAAgB;QACzDI,yBAAyB,GAAGwC,qBAAqB,CAACxC,yBAAyB;MAC7E,QAAQ3F,OAAO;QACb,KAAKX,cAAc;QACnB,KAAKD,aAAa;UAChB;YACE8I,eAAe,GAAG5D,iCAAiC,CAACpD,GAAG,CAAC+G,KAAK,CAAC,IAAIA,KAAK;YACvE;YACA,IAAIG,mBAAmB,GAAGzC,yBAAyB,CAAC7F,OAAO,CAACoI,eAAe,CAAC;YAC5E,IAAIE,mBAAmB,KAAK,CAAC,CAAC,EAAE;cAC9BzC,yBAAyB,CAAC0C,MAAM,CAACD,mBAAmB,EAAE,CAAC,CAAC;YAC1D;YACA;UACF;QACF,KAAKjJ,eAAe;UAClB;YACE+I,eAAe,GAAG7D,+BAA+B,CAACnD,GAAG,CAAC+G,KAAK,CAAC,IAAIA,KAAK;YACrE;UACF;QACF;UACE;YACEC,eAAe,GAAGD,KAAK;UACzB;MAAC;MAEL,IAAIxI,UAAU,GAAG8F,gBAAgB,EAAE;MACnC,IAAI+C,SAAS,GAAG/F,MAAM,KAAK,MAAM,GAAG/C,wBAAwB,CAACC,UAAU,CAAC,GAAGA,UAAU;MACrF;MACA,IAAI6I,SAAS,CAACxB,QAAQ,CAACoB,eAAe,CAAC,EAAE;QACvC,OAAOjJ,cAAc,CAACoG,IAAI,CAAC6C,eAAe,CAACK,UAAU,EAAEL,eAAe,CAAC;MACzE;IACF,CAAC,CAAC,OAAOhG,CAAC,EAAE;MACVsG,OAAO,CAACC,IAAI,CAACvG,CAAC,CAAC;IACjB;IACA,OAAO8F,qBAAqB,CAAC3C,IAAI,CAAC,IAAI,EAAE4C,KAAK,CAAC;EAChD,CAAC;AACH;AACA,OAAO,SAASS,wCAAwC,CAACxD,mBAAmB,EAAEC,qBAAqB,EAAE;EACnG;EACA,IAAI5G,eAAe,CAACC,SAAS,CAACC,WAAW,KAAKH,kBAAkB,IAAIO,eAAe,CAACL,SAAS,CAACC,WAAW,KAAKG,kBAAkB,IAAIL,eAAe,CAACC,SAAS,CAACQ,YAAY,KAAKD,mBAAmB,EAAE;IAClMR,eAAe,CAACC,SAAS,CAACC,WAAW,GAAGmG,uCAAuC,CAAC;MAC9EK,0BAA0B,EAAE3G,kBAAkB;MAC9C6G,qBAAqB,EAAEA,qBAAqB;MAC5CD,mBAAmB,EAAEA,mBAAmB;MACxC3C,MAAM,EAAE;IACV,CAAC,CAAC;IACF1D,eAAe,CAACL,SAAS,CAACC,WAAW,GAAGmG,uCAAuC,CAAC;MAC9EK,0BAA0B,EAAErG,kBAAkB;MAC9CuG,qBAAqB,EAAEA,qBAAqB;MAC5CD,mBAAmB,EAAEA,mBAAmB;MACxC3C,MAAM,EAAE;IACV,CAAC,CAAC;IACFhE,eAAe,CAACC,SAAS,CAACQ,YAAY,GAAG4F,uCAAuC,CAAC;MAC/EK,0BAA0B,EAAElG,mBAAmB;MAC/CoG,qBAAqB,EAAEA,qBAAqB;MAC5CD,mBAAmB,EAAEA,mBAAmB;MACxC3C,MAAM,EAAE;IACV,CAAC,CAAC;EACJ;EACA;EACA,IAAIhE,eAAe,CAACC,SAAS,CAACG,WAAW,KAAKD,kBAAkB,IAAIG,eAAe,CAACL,SAAS,CAACG,WAAW,KAAKG,kBAAkB,EAAE;IAChIP,eAAe,CAACC,SAAS,CAACG,WAAW,GAAGoJ,iBAAiB,CAACrJ,kBAAkB,EAAEyG,qBAAqB,EAAE,MAAM,CAAC;IAC5GtG,eAAe,CAACL,SAAS,CAACG,WAAW,GAAGoJ,iBAAiB,CAACjJ,kBAAkB,EAAEqG,qBAAqB,EAAE,MAAM,CAAC;EAC9G;EACA,OAAO,SAASwD,OAAO,GAAG;IACxBpK,eAAe,CAACC,SAAS,CAACC,WAAW,GAAGH,kBAAkB;IAC1DC,eAAe,CAACC,SAAS,CAACG,WAAW,GAAGD,kBAAkB;IAC1DG,eAAe,CAACL,SAAS,CAACC,WAAW,GAAGG,kBAAkB;IAC1DC,eAAe,CAACL,SAAS,CAACG,WAAW,GAAGG,kBAAkB;IAC1DP,eAAe,CAACC,SAAS,CAACQ,YAAY,GAAGD,mBAAmB;EAC9D,CAAC;AACH;AACA,OAAO,SAAS6J,eAAe,CAACC,kBAAkB,EAAEC,eAAe,EAAE;EACnED,kBAAkB,CAACpE,OAAO,CAAC,UAAUqB,iBAAiB,EAAE;IACtD;IACA,IAAIiD,aAAa,GAAGD,eAAe,CAAChD,iBAAiB,CAAC;IACtD,IAAIiD,aAAa,EAAE;MACjB;AACN;AACA;AACA;AACA;MACM,IAAIjD,iBAAiB,YAAYpB,gBAAgB,IAAIxE,sBAAsB,CAAC4F,iBAAiB,CAAC,EAAE;QAC9F,IAAItF,QAAQ,GAAGE,wBAAwB,CAACoF,iBAAiB,CAAC;QAC1D,IAAItF,QAAQ,EAAE;UACZ;UACA,KAAK,IAAIwI,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGxI,QAAQ,CAACC,MAAM,EAAEuI,CAAC,EAAE,EAAE;YACxC,IAAIC,OAAO,GAAGzI,QAAQ,CAACwI,CAAC,CAAC;YACzB,IAAIE,oBAAoB,GAAGpD,iBAAiB,CAACvF,KAAK;YAClD2I,oBAAoB,CAACC,UAAU,CAACF,OAAO,CAACG,OAAO,EAAEF,oBAAoB,CAAC1I,QAAQ,CAACC,MAAM,CAAC;UACxF;QACF;MACF;IACF;EACF,CAAC,CAAC;AACJ"}]}