package com.sunyard.etl.custom.handler;

import java.io.BufferedReader;
import java.io.File;
import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.InputStreamReader;
import java.io.OutputStreamWriter;
import java.io.PrintWriter;
import java.text.SimpleDateFormat;
import com.sunyard.etl.system.common.Constants;
import org.springframework.stereotype.Service;

import com.sunyard.etl.system.dao.DataDateDAO;
import com.sunyard.etl.system.dao.impl.DataDateDAOImpl;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.enums.JobStartEnum;
import com.xxl.job.core.handler.IJobHandler;
import com.xxl.job.core.handler.annotation.JobHandler;
import com.xxl.job.core.log.XxlJobLogger;


@JobHandler(value = "FileFormat_DEPD9950", name = "DEPD9950文件格式化")
@Service
public class FileFormat_DEPD9950 extends IJobHandler {

	private static final long serialVersionUID = 1L;

	private static DataDateDAO dateDao = new DataDateDAOImpl();

	@Override
	public ReturnT<String> execute(String jobId, String... arg1) throws Exception {
		XxlJobLogger.log("开始DEPD9950文件格式化...");
		String jobDate = dateDao.getDataDate();
		if (null != arg1[0]) {
			String dirPath = arg1[0].toString().replace("@", jobDate);
			File dir = new File(dirPath);
			if (!dir.isDirectory()) {
				XxlJobLogger.log("INFO: 资源不足，目录不存在：" + dir, jobId + "");
				return new ReturnT<String>(ReturnT.FAIL_CODE, JobStartEnum.TASK_STATE_NO_RESOURCE.getCode(),
						"文件目录" + dir.getPath() + "不存在");
			}
			File preFile = new File(dir, "DEPD9950_"+jobDate+".txt");
			File file = new File(dir, "DEPD9950_"+jobDate+"_proc.txt");
			if (file.exists()) {
				XxlJobLogger.log("INFO: 文件已存在，先删除后再转换：" + file.getPath(), jobId + "");
				file.delete();
			}
			PrintWriter pw;
			pw = new PrintWriter(new OutputStreamWriter(new FileOutputStream(file), "GBK"));
			if (preFile.length()==0) {
				pw.print("|||");
				pw.flush();
				pw.close();
				XxlJobLogger.log("INFO: 源文件不存在，生成转换文件：" + preFile.getPath() + " >> " + file.getPath(), jobId + "");
				return ReturnT.SUCCESS;
			}
			BufferedReader br = new BufferedReader(new InputStreamReader(new FileInputStream(preFile), "GBK"));
			
			String line = null;
			String siteNo = "";
			String siteName = "";
			String occurDate = "";
			int hang = 0 ;
			try {
				while ((line = br.readLine()) != null) {
					hang++ ;
					if(line.contains("1                 ") && line.contains("-DEPD9950")){
						siteNo = line.substring(line.indexOf("( ")+"( ".length(),line.indexOf("-DEPD9950"));
						continue;
					} 
					if((line.contains("会计分录流水") || line.contains("=============="))){
						continue;
					}    
					if(line.contains("机构 :") && line.contains("日期 :")){
						siteName = line.substring(line.indexOf("机构 :  ")+"机构 :  ".length(),line.indexOf("日期 :")).trim().replace("　", "");
						occurDate = line.substring(line.indexOf("日期 : ")+"日期 : ".length(),line.indexOf("日期 : ")+"日期 : ".length()+10).trim();
						occurDate = new SimpleDateFormat("yyyyMMdd").format(new SimpleDateFormat("yyyy/MM/dd").parse(occurDate));
						continue;
					}
					if("".equals(line.trim())||(line.contains("机构") && line.contains("流水号") && line.contains("序号"))){
						continue;
					}     
					if("".equals(line.trim())||(line.contains("BRCH") && line.contains("JOURNAL-NBR") && line.contains("JOURNAL-ID"))){
						continue;
					}
					if (line.length() <158 ) {
						continue;
					}
					
					String newLine = null ;
					try {//机构，流水号，交易日期，账务日期，交易码，交易名称，货币，交易金额，账号，主账户
						newLine = siteName+" | "+occurDate+" | "
								+line.substring(2,7).trim() +" | "	//机构号BRCH
								+line.substring(9,21).trim() +" | "	//流水号JOURNAL-NBR
								+line.substring(34,45).trim() +" | "	//交易日期POST-DATE
								
								+line.substring(46,57).trim() +" | "	//账务日期TRAN-DATE
								+line.substring(58,84).trim() +" | "	//总账代码GL-CLASS-CODE
								+line.substring(84,94).trim() +" | "	//交易码TRAN-CODE    
								+line.substring(94,112).trim() +" | "	//交易名称 TRAN-NAME
								
								+line.substring(118,126).trim() +" | "	//货币 FCY-CODE
								+line.substring(126,158).trim() +" | "	//交易金额 AMT 
								+this.getNum(line.substring(158,200)).trim() +" | ";	//账号 ACCOUNT-NO
								if (line.length()<224) {
									newLine +="  | "	 +"  |  "	;
								}else {
									newLine +=line.substring(212,224).trim()  +" | ";	//柜员号TELLER-NO
									if (line.length()<236) {
										newLine +="  |  "	;
									}else {
										newLine +=this.getVC(line.substring(236)).trim()  +" | "	;//提示码 PROM-CODE,只提取vc其他返回空
										
										if(line.length()<249) {//避免最后没用主账号导致报错跳过。
											newLine +="  "	;
										}else {
											newLine += line.substring(249).trim() ;	//主账户MAST-ACCT
											
										}
									}
											
											
									
									
								}
								
						pw.print(newLine+System.getProperty("line.separator"));
					} catch (Exception e) {
					//	XxlJobLogger.log("INFO: 第"+hang+"出错，原因是："+e, jobId );
						continue ;
					}
				}
			} catch (IOException e) {
				e.printStackTrace();
			}finally{
				pw.flush();
				pw.close();
			}
			Thread.sleep(8000);
			XxlJobLogger.log("INFO: 文件转换成功：" + file.getPath()+ ",休眠完8秒钟", jobId );
			return ReturnT.SUCCESS;
		}
		return ReturnT.FAIL;
	}

	
	
	
	
	private String getVC(String str) {
		String str2 = " " ;
		if(str != null && !"".equals(str.trim())){
			str = str.trim() ;
			if (str.indexOf("VC")>-1 ||str.indexOf("vC")>-1 ||str.indexOf("Vc")>-1 ||str.indexOf("vc")>-1 ) {
				str2 = "VC" ;
			}
		}
		return str2 ;
	}





	private String getNum(String str) {
		
		String str2 = "" ;
		if(str != null && !"".equals(str.trim())){
			str = str.trim() ;
			for(int i=0;i<str.length();i++){
				if(str.charAt(i)>=48 && str.charAt(i)<=57){
					str2+=str.charAt(i);
				}
			}
		}
		return str2 ;
	}


	public static void main(String[] args) {
		FileFormat_DEPD9950 ff = new FileFormat_DEPD9950();
		try {
			ff.execute("9", "C:\\@");
		} catch (Exception e) {
			// TODO Auto-generated catch block
			e.printStackTrace();
		}
	}
}
