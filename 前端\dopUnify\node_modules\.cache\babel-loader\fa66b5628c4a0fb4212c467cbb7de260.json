{"remainingRequest": "E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-q<PERSON><PERSON>n\\数字运营平台-统一门户工程\\dopUnify\\node_modules\\babel-loader\\lib\\index.js!E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan\\数字运营平台-统一门户工程\\dopUnify\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan\\数字运营平台-统一门户工程\\dopUnify\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan\\数字运营平台-统一门户工程\\dopUnify\\src\\layout\\components\\SublicenseMan\\index.vue?vue&type=script&lang=js&", "dependencies": [{"path": "E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-<PERSON><PERSON><PERSON>n\\数字运营平台-统一门户工程\\dopUnify\\src\\layout\\components\\SublicenseMan\\index.vue", "mtime": 1703583640612}, {"path": "E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-<PERSON><PERSON><PERSON>n\\数字运营平台-统一门户工程\\dopUnify\\babel.config.js", "mtime": 1686019818359}, {"path": "E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-<PERSON><PERSON><PERSON>n\\数字运营平台-统一门户工程\\dopUnify\\.babelrc", "mtime": 1705285078668}, {"path": "E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-<PERSON><PERSON><PERSON>n\\数字运营平台-统一门户工程\\dopUnify\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-<PERSON><PERSON><PERSON>n\\数字运营平台-统一门户工程\\dopUnify\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-<PERSON><PERSON><PERSON>n\\数字运营平台-统一门户工程\\dopUnify\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-<PERSON><PERSON><PERSON>n\\数字运营平台-统一门户工程\\dopUnify\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1655715099000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:aW1wb3J0IF9yZWdlbmVyYXRvclJ1bnRpbWUgZnJvbSAiRTovMV9Qcm9qZWN0L1hZRF9Qcm9qZWN0L2RvcC00LjAvZG9wLTQuMS1xaWFuZHVhbi9cdTY1NzBcdTVCNTdcdThGRDBcdTg0MjVcdTVFNzNcdTUzRjAtXHU3RURGXHU0RTAwXHU5NUU4XHU2MjM3XHU1REU1XHU3QTBCL2RvcFVuaWZ5L25vZGVfbW9kdWxlcy9AYmFiZWwvcnVudGltZS9oZWxwZXJzL2VzbS9yZWdlbmVyYXRvclJ1bnRpbWUuanMiOwppbXBvcnQgX2FzeW5jVG9HZW5lcmF0b3IgZnJvbSAiRTovMV9Qcm9qZWN0L1hZRF9Qcm9qZWN0L2RvcC00LjAvZG9wLTQuMS1xaWFuZHVhbi9cdTY1NzBcdTVCNTdcdThGRDBcdTg0MjVcdTVFNzNcdTUzRjAtXHU3RURGXHU0RTAwXHU5NUU4XHU2MjM3XHU1REU1XHU3QTBCL2RvcFVuaWZ5L25vZGVfbW9kdWxlcy9AYmFiZWwvcnVudGltZS9oZWxwZXJzL2VzbS9hc3luY1RvR2VuZXJhdG9yLmpzIjsKaW1wb3J0ICJjb3JlLWpzL21vZHVsZXMvZXMub2JqZWN0LnRvLXN0cmluZy5qcyI7CmltcG9ydCAiY29yZS1qcy9tb2R1bGVzL3dlYi5kb20tY29sbGVjdGlvbnMuZm9yLWVhY2guanMiOwppbXBvcnQgImNvcmUtanMvbW9kdWxlcy9lcy5hcnJheS5tYXAuanMiOwppbXBvcnQgImNvcmUtanMvbW9kdWxlcy9lcy5hcnJheS5qb2luLmpzIjsKLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KCmltcG9ydCB7IGNvbmZpZywgY29uZmlnVGFibGUsIGNvbmZpZ0RldGFpbFRhYmxlLCBjb25maWdQc2QgfSBmcm9tICcuL2luZm8nOyAvLyDooajlpLTjgIHooajljZXphY3nva4KaW1wb3J0IHsgZW5jcnlwdFJlc3VsdCB9IGZyb20gJ0AvdXRpbHMvY3J5cHRvJzsgLy8g5Yqg5a+GCmltcG9ydCB7IGRpY3Rpb25hcnlGaWVkcyB9IGZyb20gJ0AvdXRpbHMvZGljdGlvbmFyeS5qcyc7IC8vIOWtl+WFuOW4uOmHjwppbXBvcnQgeyBkYXRlOEZvcm1hdCB9IGZyb20gJ0AvZmlsdGVycyc7CmltcG9ydCB7IGNvbW1vbkJsYW5rIH0gZnJvbSAnQC91dGlscy9jb21tb24nOwppbXBvcnQgeyBjb21tb25Nc2dTdWNjZXNzLCBjb21tb25Nc2dXYXJuLCBjb21tb25Nc2dFcnJvciwgY29tbW9uTXNnQ29uZmlybSB9IGZyb20gJ0AvdXRpbHMvbWVzc2FnZS5qcyc7IC8vIOaPkOekuuS/oeaBrwppbXBvcnQgeyBDb21tb24gfSBmcm9tICdAL2FwaSc7CnZhciBzdWJsaWNlbnNlTWFuID0gQ29tbW9uLnN1YmxpY2Vuc2VNYW4sCiAgcmV2b2tlID0gQ29tbW9uLnJldm9rZSwKICBxdWVyeVN1YkxpY2Vuc2UgPSBDb21tb24ucXVlcnlTdWJMaWNlbnNlLAogIHBzZFZhbGlkID0gQ29tbW9uLnBzZFZhbGlkLAogIGNhbGxpbiA9IENvbW1vbi5jYWxsaW4sCiAgc2VsZWN0Rmxvd0RldGFpbERhdGEgPSBDb21tb24uc2VsZWN0Rmxvd0RldGFpbERhdGE7CmV4cG9ydCBkZWZhdWx0IHsKICBuYW1lOiAnU3VibGljZW5zZU1hbicsCiAgZGF0YTogZnVuY3Rpb24gZGF0YSgpIHsKICAgIHJldHVybiB7CiAgICAgIGRpYWxvZ1Zpc2libGU6IGZhbHNlLAogICAgICBidG5BbGw6IHsKICAgICAgICAvLyDlvZPliY3pobXpnIDopoHphY3nva7mnYPpmZDnmoTmjInpkq4gIOadg+mZkOiOt+WPlgogICAgICAgIGJ0blF1ZXJ5OiB0cnVlCiAgICAgIH0sCiAgICAgIHJlZENvbG9yOiB7CiAgICAgICAgY29sb3I6ICdyZWQnCiAgICAgIH0sCiAgICAgIGJsdWVDb2xvcjogewogICAgICAgIGNvbG9yOiAnYmx1ZScKICAgICAgfSwKICAgICAgZ3JlZW5Db2xvcjogewogICAgICAgIGNvbG9yOiAnIzAwODAwMCcKICAgICAgfSwKICAgICAgYnRuRGF0YXM6IHsKICAgICAgICAvLyDmjInpkq7phY3nva4KICAgICAgICBidG5SZXZva2U6IHsKICAgICAgICAgIHNob3c6IHRydWUgLy8g5pKk6ZSACiAgICAgICAgfSwKCiAgICAgICAgYnRuQ2FsbGluOiB7CiAgICAgICAgICBzaG93OiB0cnVlIC8vIOaUtuWbngogICAgICAgIH0KICAgICAgfSwKCiAgICAgIGNvbmZpZzogY29uZmlnKHRoaXMpLAogICAgICBkZWZhdWx0Rm9ybTogewogICAgICAgIHF1ZXJ5X3R5cGU6ICcwJywKICAgICAgICBhcHBseV91c2VyOiAnJywKICAgICAgICB0YXJnZXRfdXNlcjogJycsCiAgICAgICAgcmVhc29uOiAnJywKICAgICAgICBpc19yZXRha2U6ICcnLAogICAgICAgIHN0YXJ0X2FwcGx5X3RpbWU6ICcnLAogICAgICAgIGVuZF9hcHBseV90aW1lOiAnJwogICAgICB9LAogICAgICB0YWJsZTogewogICAgICAgIC8vIOihqOagvOmFjee9rgogICAgICAgIHRhYmxlQ29sdW1uczogY29uZmlnVGFibGUoKSwKICAgICAgICAvLyDooajlpLTphY3nva4KICAgICAgICByZWY6ICd0YWJsZVJlZicsCiAgICAgICAgc2VsZWN0aW9uOiBmYWxzZSwKICAgICAgICAvLyDlpI3pgIkKICAgICAgICBpbmRleE51bWJlcjogdHJ1ZSwKICAgICAgICAvLyDluo/lj7cKICAgICAgICBsb2FkaW5nOiBmYWxzZSwKICAgICAgICBjb21wb25lbnRQcm9wczogewogICAgICAgICAgZGF0YTogW10sCiAgICAgICAgICAvLyDooajmoLzmlbDmja4KICAgICAgICAgIGhlaWdodDogJzIxMHB4JywKICAgICAgICAgIGZvcm1Sb3c6IDMgLy8g6KGo5Y2V6KGM5pWwCiAgICAgICAgfSwKCiAgICAgICAgcGFnZUxpc3Q6IHsKICAgICAgICAgIHRvdGFsTnVtOiAwLAogICAgICAgICAgY3VycmVudFBhZ2U6IDEsCiAgICAgICAgICAvLyDlvZPliY3pobUKICAgICAgICAgIHBhZ2VTaXplOiB0aGlzLiRzdG9yZS5nZXR0ZXJzLnBhZ2VTaXplIC8vIOW9k+WJjemhteaYvuekuuadoeaVsAogICAgICAgIH0sCgogICAgICAgIGN1cnJlbnRSb3c6IFtdIC8vIOmAieS4reihjAogICAgICB9LAoKICAgICAgZGlhbG9nOiB7CiAgICAgICAgLy8g55uu5qCH55So5oi35a+G56CB5by55qGGCiAgICAgICAgY29tcG9uZW50UHJvcHM6IHsKICAgICAgICAgIC8vIOW8ueWHuuahhumFjee9ruWxnuaApwogICAgICAgICAgd2lkdGg6ICc1MHJlbScsCiAgICAgICAgICB0aXRsZTogJ+WvhueggeagoemqjCcKICAgICAgICB9LAogICAgICAgIHZpc2libGU6IGZhbHNlLAogICAgICAgIGZvcm06IHsKICAgICAgICAgIGNvbmZpZzogY29uZmlnUHNkKHRoaXMpLAogICAgICAgICAgbGFiZWxXaWR0aDogJzE0cmVtJywKICAgICAgICAgIC8vIOW9k+WJjeihqOWNleagh+etvuWuveW6pumFjee9rgogICAgICAgICAgZGVmYXVsdEZvcm06IHsKICAgICAgICAgICAgcGFzc3dvcmQ6IHRoaXMuJHN0b3JlLmdldHRlcnMuZW1wdHlQc3dkIC8vIOebruagh+eUqOaIt+WvhueggQogICAgICAgICAgfQogICAgICAgIH0KICAgICAgfSwKCiAgICAgIGRpYWxvZ0RldGFpbDogewogICAgICAgIC8vIOa1geawtOWPt+WuoeaJueivpuaDhQogICAgICAgIHZpc2libGU6IGZhbHNlLAogICAgICAgIC8vIOaYvuekuumakOiXj+mFjee9rgogICAgICAgIGJ0blN1Ym1pdDogZmFsc2UsCiAgICAgICAgLy8g56Gu5a6a5oyJ6ZKuCiAgICAgICAgYnRuQ2FuY2xlOiBmYWxzZSwKICAgICAgICAvLyDlj5bmtojmjInpkq4KICAgICAgICBjb21wb25lbnRQcm9wczogewogICAgICAgICAgLy8g5by55Ye65qGG5bGe5oCnCiAgICAgICAgICB0aXRsZTogJ+afpeeci+ivpuaDhScsCiAgICAgICAgICAvLyDlvLnlh7rmoYbmoIfpopgKICAgICAgICAgIHdpZHRoOiAnODAlJyAvLyDlvZPliY3lvLnlh7rmoYblrr3luqYg6buY6K6kODAlCiAgICAgICAgfSwKCiAgICAgICAgdGFibGVDb25maWc6IHsKICAgICAgICAgIC8vIOihqOagvOWxnuaApwogICAgICAgICAgcmVmOiAndGFibGVSZWYnLAogICAgICAgICAgY29sdW1uczogY29uZmlnRGV0YWlsVGFibGUodGhpcyksCiAgICAgICAgICAvLyDooajlpLQKICAgICAgICAgIHNlbGVjdGlvbjogZmFsc2UsCiAgICAgICAgICAvLyDlpI3pgIkKICAgICAgICAgIGluZGV4TnVtYmVyOiBmYWxzZSwKICAgICAgICAgIC8vIOW6j+WPtwogICAgICAgICAgbG9hZGluZzogZmFsc2UsCiAgICAgICAgICAvLyDnrYnlvoXliqDovb3kuK0KICAgICAgICAgIHBhZ2VMaXN0OiB7CiAgICAgICAgICAgIC8vIOmhteeggQogICAgICAgICAgICB0b3RhbE51bTogMCwKICAgICAgICAgICAgLy8g5oC76aG15pWwCiAgICAgICAgICAgIGN1cnJlbnRQYWdlOiAxLAogICAgICAgICAgICAvLyDlvZPliY3pobUKICAgICAgICAgICAgcGFnZVNpemU6IHRoaXMuJHN0b3JlLmdldHRlcnMucGFnZVNpemUgLy8g5b2T5YmN6aG15pi+56S65p2h5pWwCiAgICAgICAgICB9LAoKICAgICAgICAgIGNvbXBvbmVudFByb3BzOiB7CiAgICAgICAgICAgIC8vIOihqOagvOWxnuaAp+mFjee9rgogICAgICAgICAgICBkYXRhOiBbXSAvLyDooajmlbDmja4KICAgICAgICAgIH0KICAgICAgICB9CiAgICAgIH0sCgogICAgICBTdWNjZXNzZGlhbG9nOiBmYWxzZSwKICAgICAgLy8g6L2s5o6I5p2D5oiQ5Yqf5o+Q56S65by556qXCiAgICAgIFNlbmNvbmQ6IDUgLy8g6K6+572u5Yid5aeL5YCS6K6h5pe2CiAgICB9OwogIH0sCgogIGNvbXB1dGVkOiB7fSwKICB3YXRjaDogewogICAgZGlhbG9nVmlzaWJsZTogewogICAgICBoYW5kbGVyOiBmdW5jdGlvbiBoYW5kbGVyKG5ld05hbWUsIG9sZE5hbWUpIHsKICAgICAgICAvLyDlvLnmoYblhbPpl63muIXnqbrmiYDmnInmn6Xor6LmnaHku7Yg6Kej5Yaz5by556qX5YWz6Zet5LiL5qyh5omT5byA5p+l6K+i5p2h5Lu26L+Y5Zyo55qE6Zeu6aKYCiAgICAgICAgdGhpcy5kZWZhdWx0Rm9ybSA9IHsKICAgICAgICAgIHF1ZXJ5X3R5cGU6ICcwJywKICAgICAgICAgIGFwcGx5X3VzZXI6ICcnLAogICAgICAgICAgdGFyZ2V0X3VzZXI6ICcnLAogICAgICAgICAgcmVhc29uOiAnJywKICAgICAgICAgIGlzX3JldGFrZTogJycsCiAgICAgICAgICBzdGFydF9hcHBseV90aW1lOiAnJywKICAgICAgICAgIGVuZF9hcHBseV90aW1lOiAnJwogICAgICAgIH07CiAgICAgIH0sCiAgICAgIC8vIGltbWVkaWF0ZTogdHJ1ZQogICAgICBkZWVwOiB0cnVlCiAgICB9CiAgfSwKICBjcmVhdGVkOiBmdW5jdGlvbiBjcmVhdGVkKCkge30sCiAgbW91bnRlZDogZnVuY3Rpb24gbW91bnRlZCgpIHt9LAogIG1ldGhvZHM6IHsKICAgIGRpYWxvZ1Nob3c6IGZ1bmN0aW9uIGRpYWxvZ1Nob3coKSB7CiAgICAgIHZhciBfdGhpcyA9IHRoaXM7CiAgICAgIHRoaXMuZGlhbG9nVmlzaWJsZSA9IHRydWU7CiAgICAgIHRoaXMuJG5leHRUaWNrKGZ1bmN0aW9uICgpIHsKICAgICAgICBfdGhpcy5jb25maWcucXVlcnlfdHlwZS5vcHRpb25zID0gZGljdGlvbmFyeUZpZWRzKCdTTV9RVUVSWV9UWVBFJyk7IC8vIOafpeivouexu+WeiwogICAgICAgIF90aGlzLmNvbmZpZy5yZWFzb24ub3B0aW9ucyA9IGRpY3Rpb25hcnlGaWVkcygnU1VCTElDRU5TRV9SRUFTT04nKTsgLy8g5o6I5p2D5Y6f5ZugCiAgICAgICAgX3RoaXMuY29uZmlnLmlzX3JldGFrZS5vcHRpb25zID0gZGljdGlvbmFyeUZpZWRzKCdTVUJMSUNFTlNFX0lTX1JFVEFLRScpOyAvLyDmjojmnYPnirbmgIEKICAgICAgfSk7CiAgICB9LAogICAgLyoqDQogICAgICog5oyJ6ZKuIC3mn6Xor6IqLwogICAgcXVlcnlMaXN0OiBmdW5jdGlvbiBxdWVyeUxpc3QoKSB7CiAgICAgIHZhciBfdGhpczIgPSB0aGlzOwogICAgICB2YXIgbXNnID0gewogICAgICAgIHBhcmFtZXRlckxpc3Q6IFtdLAogICAgICAgIGN1cnJlbnRQYWdlOiB0aGlzLnRhYmxlLnBhZ2VMaXN0LmN1cnJlbnRQYWdlLAogICAgICAgIHBhZ2VOdW06IHRoaXMudGFibGUucGFnZUxpc3QucGFnZVNpemUsCiAgICAgICAgLy8g5p+l6K+i55qE5p2h5Lu2CiAgICAgICAgcXVlcnlfdHlwZTogdGhpcy5kZWZhdWx0Rm9ybS5xdWVyeV90eXBlID09PSAnJyA/ICcwJyA6IHRoaXMuZGVmYXVsdEZvcm0ucXVlcnlfdHlwZSwKICAgICAgICB0YXJnZXRfdXNlcjogdGhpcy5kZWZhdWx0Rm9ybS50YXJnZXRfdXNlciwKICAgICAgICByZWFzb246IHRoaXMuZGVmYXVsdEZvcm0ucmVhc29uLAogICAgICAgIGFwcGx5X3VzZXI6IHRoaXMuZGVmYXVsdEZvcm0uYXBwbHlfdXNlciwKICAgICAgICBzdGFydF9hcHBseV90aW1lOiB0aGlzLmRlZmF1bHRGb3JtLnN0YXJ0X2FwcGx5X3RpbWUgPyAiIi5jb25jYXQoZGF0ZThGb3JtYXQodGhpcy5kZWZhdWx0Rm9ybS5zdGFydF9hcHBseV90aW1lKSwgIjAwMDAwMCIpIDogJycsCiAgICAgICAgZW5kX2FwcGx5X3RpbWU6IHRoaXMuZGVmYXVsdEZvcm0uZW5kX2FwcGx5X3RpbWUgPyAiIi5jb25jYXQoZGF0ZThGb3JtYXQodGhpcy5kZWZhdWx0Rm9ybS5lbmRfYXBwbHlfdGltZSksICIyNDAwMDAiKSA6ICcnLAogICAgICAgIGluc3RfaWQ6ICcnLAogICAgICAgIGlzX3JldGFrZTogdGhpcy5kZWZhdWx0Rm9ybS5pc19yZXRha2UKICAgICAgfTsKICAgICAgc3VibGljZW5zZU1hbihtc2cpLnRoZW4oZnVuY3Rpb24gKHJlcykgewogICAgICAgIHZhciBfcmVzJHJldE1hcCA9IHJlcy5yZXRNYXAsCiAgICAgICAgICBsaXN0ID0gX3JlcyRyZXRNYXAubGlzdCwKICAgICAgICAgIHRvdGFsTnVtID0gX3JlcyRyZXRNYXAudG90YWxOdW0sCiAgICAgICAgICBjdXJyZW50UGFnZSA9IF9yZXMkcmV0TWFwLmN1cnJlbnRQYWdlOwogICAgICAgIF90aGlzMi50YWJsZS5jb21wb25lbnRQcm9wcy5kYXRhID0gbGlzdDsKICAgICAgICBfdGhpczIudGFibGUucGFnZUxpc3QudG90YWxOdW0gPSB0b3RhbE51bTsKICAgICAgICBfdGhpczIudGFibGUucGFnZUxpc3QuY3VycmVudFBhZ2UgPSBjdXJyZW50UGFnZTsKICAgICAgICAvLyDmkqTplIDmjInpkq7mmL7pmpDkuovku7Yg5LiN5Li656m65oiW5LiN5Li6MCAg5oyJ6ZKu6ZqQ6JePCiAgICAgICAgaWYgKG1zZy5xdWVyeV90eXBlICE9PSAnMCcpIHsKICAgICAgICAgIF90aGlzMi5idG5EYXRhcy5idG5SZXZva2Uuc2hvdyA9IGZhbHNlOyAvLyDmjInpkq7pmpDol48KICAgICAgICAgIF90aGlzMi5idG5EYXRhcy5idG5DYWxsaW4uc2hvdyA9IGZhbHNlOyAvLyDmjInpkq7pmpDol48KICAgICAgICB9IGVsc2UgewogICAgICAgICAgX3RoaXMyLmJ0bkRhdGFzLmJ0blJldm9rZS5zaG93ID0gdHJ1ZTsgLy8g5oyJ6ZKu5pi+56S6CiAgICAgICAgICBfdGhpczIuYnRuRGF0YXMuYnRuQ2FsbGluLnNob3cgPSB0cnVlOyAvLyDmjInpkq7mmL7npLoKICAgICAgICB9CiAgICAgIH0pOwogICAgfSwKICAgIC8qKg0KICAgICAq6YCJ5oup6KGMICovCiAgICBjdXJyZW50Q2hhbmdlOiBmdW5jdGlvbiBjdXJyZW50Q2hhbmdlKHJvdykgewogICAgICB0aGlzLnRhYmxlLmN1cnJlbnRSb3cgPSByb3c7CiAgICB9LAogICAgLyoqDQogICAgICrmjInpkq4gIOaSpOmUgCAqLwogICAgaGFuZGxlUmV2b2tlOiBmdW5jdGlvbiBoYW5kbGVSZXZva2UoKSB7CiAgICAgIHZhciBfdGhpczMgPSB0aGlzOwogICAgICB2YXIgcm93cyA9IHRoaXMudGFibGUuY3VycmVudFJvdy5sZW5ndGg7CiAgICAgIGlmIChyb3dzID09PSAwKSB7CiAgICAgICAgY29tbW9uTXNnV2Fybign6K+36YCJ5Lit5LiA5p2h5pWw5o2uJywgdGhpcyk7CiAgICAgICAgcmV0dXJuOwogICAgICB9IGVsc2UgewogICAgICAgIGlmICh0aGlzLnRhYmxlLmN1cnJlbnRSb3cuaXNfcmV0YWtlID09PSAnMycgfHwgdGhpcy50YWJsZS5jdXJyZW50Um93LmlzX3JldGFrZSA9PT0gJzQnIHx8IHRoaXMudGFibGUuY3VycmVudFJvdy5pc19yZXRha2UgPT09ICc1JykgewogICAgICAgICAgY29tbW9uTXNnV2Fybign5Y+q5pyJ5b6F5a6h5om55ZKM5b6F5o6I5p2D55qE6L2s5o6I5p2D5Y+v5Lul6L+b6KGM5pKk6ZSA5pON5L2cJywgdGhpcyk7CiAgICAgICAgfSBlbHNlIHsKICAgICAgICAgIHZhciBtc2cgPSB7CiAgICAgICAgICAgIHBhcmFtZXRlckxpc3Q6IFtdLAogICAgICAgICAgICBzeXNNYXA6IHsKICAgICAgICAgICAgICBpbnN0X2lkOiB0aGlzLnRhYmxlLmN1cnJlbnRSb3cuc3VibGljZW5zZV9pZCwKICAgICAgICAgICAgICBpc19yZXRha2U6IHRoaXMudGFibGUuY3VycmVudFJvdy5pc19yZXRha2UKICAgICAgICAgICAgfQogICAgICAgICAgfTsKICAgICAgICAgIHJldm9rZShtc2cpLnRoZW4oZnVuY3Rpb24gKHJlcykgewogICAgICAgICAgICBjb21tb25Nc2dTdWNjZXNzKCfmkqTplIDmiJDlip8nLCBfdGhpczMpOwogICAgICAgICAgICBfdGhpczMucXVlcnlMaXN0KCk7CiAgICAgICAgICB9KS5jYXRjaChmdW5jdGlvbiAoKSB7CiAgICAgICAgICAgIGNvbW1vbk1zZ0Vycm9yKCfmkqTplIDlpLHotKUnLCBfdGhpczMpOwogICAgICAgICAgfSk7CiAgICAgICAgfQogICAgICB9CiAgICB9LAogICAgLyoqDQogICAgICrmjInpkq4gIOaUtuWbnuaOiOadgyAqLwogICAgaGFuZGxlQ2FsbGluOiBmdW5jdGlvbiBoYW5kbGVDYWxsaW4oKSB7CiAgICAgIHZhciBfdGhpczQgPSB0aGlzOwogICAgICB2YXIgcm93cyA9IHRoaXMudGFibGUuY3VycmVudFJvdy5sZW5ndGg7CiAgICAgIGlmIChyb3dzID09PSAwKSB7CiAgICAgICAgY29tbW9uTXNnV2Fybign6K+36YCJ5Lit5LiA5p2h5pWw5o2uJywgdGhpcyk7CiAgICAgICAgcmV0dXJuOwogICAgICB9IGVsc2UgewogICAgICAgIGlmICh0aGlzLnRhYmxlLmN1cnJlbnRSb3cuaXNfcmV0YWtlID09PSAnMycpIHsKICAgICAgICAgIC8vIOWIpOaWreeUqOaIt+aYr+WQpuacieWPr+aUtuWbnui9rOaOiOadg+S/oeaBrwogICAgICAgICAgdmFyIG1zZyA9IHsKICAgICAgICAgICAgcGFyYW1ldGVyTGlzdDogW10sCiAgICAgICAgICAgIHVzZXJfbm86IHRoaXMuJHN0b3JlLmdldHRlcnMudXNlck5vCiAgICAgICAgICB9OwogICAgICAgICAgcXVlcnlTdWJMaWNlbnNlKG1zZykudGhlbihmdW5jdGlvbiAocmVzKSB7CiAgICAgICAgICAgIF90aGlzNC5kaWFsb2cudmlzaWJsZSA9IHRydWU7IC8vIOWvhueggeajgOmqjOahhuaYvuekugogICAgICAgICAgfSk7CiAgICAgICAgfSBlbHNlIHsKICAgICAgICAgIGNvbW1vbk1zZ1dhcm4oJ+WPquacieW3suaOiOadg+eahOi9rOaOiOadg+WPr+S7pei/m+ihjOaUtuWbnuaOiOadg+aTjeS9nCcsIHRoaXMpOwogICAgICAgIH0KICAgICAgfQogICAgfSwKICAgIC8qKg0KICAgICAq5rWB5rC05Y+3LeWuoeaJueivpuaDhQ0KICAgICAqQHBhcmFtIHJvdyDlvZPliY3ooYzmlbDmja4qLwogICAgYXBwcm92ZURldGFpbDogZnVuY3Rpb24gYXBwcm92ZURldGFpbChyb3cpIHsKICAgICAgdmFyIF90aGlzNSA9IHRoaXM7CiAgICAgIHRoaXMuY2hhbmdlVmlzaWJsZSh0cnVlKTsKICAgICAgdGhpcy4kbmV4dFRpY2soZnVuY3Rpb24gKCkgewogICAgICAgIHZhciBtc2cgPSB7CiAgICAgICAgICBwYXJhbWV0ZXJMaXN0OiBbXSwKICAgICAgICAgIHN5c01hcDogewogICAgICAgICAgICBpbnN0X2lkOiByb3cuc3VibGljZW5zZV9pZAogICAgICAgICAgfQogICAgICAgIH07CiAgICAgICAgc2VsZWN0Rmxvd0RldGFpbERhdGEobXNnKS50aGVuKGZ1bmN0aW9uIChyZXMpIHsKICAgICAgICAgIF90aGlzNS5kaWFsb2dEZXRhaWwudGFibGVDb25maWcuY29tcG9uZW50UHJvcHMuZGF0YSA9IHJlcy5yZXRNYXAubGlzdDsKICAgICAgICAgIF90aGlzNS5kaWFsb2dEZXRhaWwudGFibGVDb25maWcuY29tcG9uZW50UHJvcHMuZGF0YS5mb3JFYWNoKGZ1bmN0aW9uIChpdGVtKSB7CiAgICAgICAgICAgIC8vIOW+heWuoeaJueinkuiJsuagvOW8j+WMlgogICAgICAgICAgICB2YXIgcm9sZUFyciA9IFtdOwogICAgICAgICAgICBfdGhpczUuJHN0b3JlLmdldHRlcnMucm9sZUxpc3QubWFwKGZ1bmN0aW9uICh2YWwpIHsKICAgICAgICAgICAgICBpZiAoIWNvbW1vbkJsYW5rKGl0ZW0ucm9sZV9ubykpIHsKICAgICAgICAgICAgICAgIGl0ZW0ucm9sZV9uby5zcGxpdCgnLCcpLm1hcChmdW5jdGlvbiAoaSkgewogICAgICAgICAgICAgICAgICBpZiAoaSA9PT0gdmFsLnZhbHVlKSB7CiAgICAgICAgICAgICAgICAgICAgcm9sZUFyci5wdXNoKHZhbC5sYWJlbCk7CiAgICAgICAgICAgICAgICAgIH0KICAgICAgICAgICAgICAgICAgcmV0dXJuIHJvbGVBcnI7CiAgICAgICAgICAgICAgICB9KTsKICAgICAgICAgICAgICB9CiAgICAgICAgICAgIH0pOwogICAgICAgICAgICBpdGVtLnJvbGVfbm8gPSByb2xlQXJyLmpvaW4oKTsKICAgICAgICAgIH0pOwogICAgICAgIH0pOwogICAgICB9KTsKICAgIH0sCiAgICAvKioNCiAgICAgKiDmtYHmsLTlj7for6bmg4XlhbPpl63mmL7npLoNCiAgICAgKi8KICAgIGNoYW5nZVZpc2libGU6IGZ1bmN0aW9uIGNoYW5nZVZpc2libGUocGFyYW0pIHsKICAgICAgdGhpcy5kaWFsb2dEZXRhaWwudmlzaWJsZSA9IHBhcmFtOwogICAgfSwKICAgIC8qKg0KICAgICAqIOaUtuWbnuaOiOadg+WvhueggeahhuW8ueWHuuahhiAtIOWFs+mXrSovCiAgICBkaWFsb2dDbG9zZTogZnVuY3Rpb24gZGlhbG9nQ2xvc2UoKSB7CiAgICAgIHRoaXMuZGlhbG9nLnZpc2libGUgPSBmYWxzZTsKICAgIH0sCiAgICAvKioNCiAgICAgKiDmlLblm57mjojmnYPlr4bnoIHmoYblvLnlh7rmoYYgLSDmj5DkuqQqLwogICAgZGlhbG9nU3VibWl0OiBmdW5jdGlvbiBkaWFsb2dTdWJtaXQoKSB7CiAgICAgIHZhciBfdGhpczYgPSB0aGlzOwogICAgICBjb21tb25Nc2dDb25maXJtKCfmmK/lkKbnoa7orqTmlLblm57mjojmnYMnLCB0aGlzLCBmdW5jdGlvbiAocGFyYW0pIHsKICAgICAgICBpZiAocGFyYW0pIHsKICAgICAgICAgIC8vIOWFiOi/m+ihjOWvhueggeagoemqjCDlho3lj5HotbfmlLblm57mjojmnYPor7fmsYIKICAgICAgICAgIHZhciBtc2cgPSB7CiAgICAgICAgICAgIHBhcmFtZXRlckxpc3Q6IFtdLAogICAgICAgICAgICBvcGVyX3R5cGU6ICdxdWVyeVVzZXInLAogICAgICAgICAgICB1c2VyX25vOiBfdGhpczYudGFibGUuY3VycmVudFJvdy50YXJnZXRfdXNlcl9ubywKICAgICAgICAgICAgcGFzc1dvcmQ6IGVuY3J5cHRSZXN1bHQoX3RoaXM2LiRzdG9yZS5nZXR0ZXJzLmluaXRQYXJhbXMuZW5TZWNNYXAuZW5jcnlwdFR5cGUsIF90aGlzNi5kaWFsb2cuZm9ybS5kZWZhdWx0Rm9ybS5wYXNzd29yZCkKICAgICAgICAgIH07CiAgICAgICAgICAvLyDlr4bnoIHmoKHpqowKICAgICAgICAgIHBzZFZhbGlkKG1zZykudGhlbihmdW5jdGlvbiAocmVzKSB7CiAgICAgICAgICAgIGlmIChyZXMucmV0Q29kZSA9PT0gJzIwMCcpIHsKICAgICAgICAgICAgICAvLyDlj5HotbfmlLblm57mjojmnYPor7fmsYIKICAgICAgICAgICAgICB2YXIgbXNnMiA9IHsKICAgICAgICAgICAgICAgIHBhcmFtZXRlckxpc3Q6IFtdLAogICAgICAgICAgICAgICAgdXNlcl9ubzogX3RoaXM2LiRzdG9yZS5nZXR0ZXJzLnVzZXJObwogICAgICAgICAgICAgIH07CiAgICAgICAgICAgICAgY2FsbGluKG1zZzIpLnRoZW4oZnVuY3Rpb24gKHJlczIpIHsKICAgICAgICAgICAgICAgIC8vIOi3s+i9rOWIsOmHjeaWsOeZu+W9lemhtemdogogICAgICAgICAgICAgICAgX3RoaXM2LmRpYWxvZ1Zpc2libGUgPSBmYWxzZTsgLy8g5o6I5p2D566h55CG6aG16Z2i5YWz6ZetCiAgICAgICAgICAgICAgICBfdGhpczYuZGlhbG9nLnZpc2libGUgPSBmYWxzZTsgLy8g5a+G56CB5qGG5YWz6ZetCiAgICAgICAgICAgICAgICBfdGhpczYuU3VjY2Vzc2RpYWxvZyA9IHRydWU7CiAgICAgICAgICAgICAgICB2YXIgaW50ZXJ2YWwgPSBzZXRJbnRlcnZhbChmdW5jdGlvbiAoKSB7CiAgICAgICAgICAgICAgICAgIF90aGlzNi5TZW5jb25kLS07CiAgICAgICAgICAgICAgICAgIGlmIChfdGhpczYuU2VuY29uZCA9PT0gMCkgewogICAgICAgICAgICAgICAgICAgIHdpbmRvdy5jbGVhckludGVydmFsKGludGVydmFsKTsKICAgICAgICAgICAgICAgICAgICBfdGhpczYubG9nb3V0KCk7IC8vIOWAkuiuoeaXtue7k+adn+aXtui3s+i9rOeZu+W9lemhtemdogogICAgICAgICAgICAgICAgICB9CiAgICAgICAgICAgICAgICB9LCAxMDAwKTsKICAgICAgICAgICAgICB9KS5jYXRjaChmdW5jdGlvbiAoZSkgewogICAgICAgICAgICAgICAgLy8gY29uc29sZS5sb2coZSkKICAgICAgICAgICAgICB9KTsKICAgICAgICAgICAgfQogICAgICAgICAgfSkuY2F0Y2goZnVuY3Rpb24gKCkgewogICAgICAgICAgICBjb21tb25Nc2dXYXJuKCflr4bnoIHplJnor68nLCBfdGhpczYpOwogICAgICAgICAgfSk7CiAgICAgICAgfQogICAgICB9KTsKICAgIH0sCiAgICAvKioNCiAgICAgKiDot7PovaznmbvlvZXpobXpnaLmlrnms5UqLwogICAgbG9nb3V0OiBmdW5jdGlvbiBsb2dvdXQoKSB7CiAgICAgIHZhciBfdGhpczcgPSB0aGlzOwogICAgICByZXR1cm4gX2FzeW5jVG9HZW5lcmF0b3IoIC8qI19fUFVSRV9fKi9fcmVnZW5lcmF0b3JSdW50aW1lKCkubWFyayhmdW5jdGlvbiBfY2FsbGVlKCkgewogICAgICAgIHJldHVybiBfcmVnZW5lcmF0b3JSdW50aW1lKCkud3JhcChmdW5jdGlvbiBfY2FsbGVlJChfY29udGV4dCkgewogICAgICAgICAgd2hpbGUgKDEpIHsKICAgICAgICAgICAgc3dpdGNoIChfY29udGV4dC5wcmV2ID0gX2NvbnRleHQubmV4dCkgewogICAgICAgICAgICAgIGNhc2UgMDoKICAgICAgICAgICAgICAgIF9jb250ZXh0Lm5leHQgPSAyOwogICAgICAgICAgICAgICAgcmV0dXJuIF90aGlzNy4kc3RvcmUuZGlzcGF0Y2goJ3VzZXIvbG9nb3V0Jyk7CiAgICAgICAgICAgICAgY2FzZSAyOgogICAgICAgICAgICAgICAgX3RoaXM3LiRyb3V0ZXIucHVzaCgiL2xvZ2luP3JlZGlyZWN0PSIuY29uY2F0KF90aGlzNy4kcm91dGUuZnVsbFBhdGgpKTsKICAgICAgICAgICAgICBjYXNlIDM6CiAgICAgICAgICAgICAgY2FzZSAiZW5kIjoKICAgICAgICAgICAgICAgIHJldHVybiBfY29udGV4dC5zdG9wKCk7CiAgICAgICAgICAgIH0KICAgICAgICAgIH0KICAgICAgICB9LCBfY2FsbGVlKTsKICAgICAgfSkpKCk7CiAgICB9LAogICAgLyoqDQogICAgICrpobXnoIHmm7TmlrAgKi8KICAgIGdldExpc3Q6IGZ1bmN0aW9uIGdldExpc3QocGFnZVBhcmFtKSB7CiAgICAgIHZhciBjdXJyZW50UGFnZSA9IHBhZ2VQYXJhbS5jdXJyZW50UGFnZSwKICAgICAgICBwYWdlU2l6ZSA9IHBhZ2VQYXJhbS5wYWdlU2l6ZTsKICAgICAgdGhpcy50YWJsZS5wYWdlTGlzdC5wYWdlU2l6ZSA9IHBhZ2VTaXplOwogICAgICB0aGlzLnRhYmxlLnBhZ2VMaXN0LmN1cnJlbnRQYWdlID0gY3VycmVudFBhZ2U7CiAgICAgIHRoaXMucXVlcnlMaXN0KCk7CiAgICB9CiAgfQp9Ow=="}, {"version": 3, "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAqHA;AACA;AACA;AACA;AACA;AACA,SACAA,kBACAC,eACAC,gBACAC,wBACA;AACA;AACA,IACAC,gBAMAC,OANAD;EACAE,SAKAD,OALAC;EACAC,kBAIAF,OAJAE;EACAC,WAGAH,OAHAG;EACAC,SAEAJ,OAFAI;EACAC,uBACAL,OADAK;AAEA;EACAC;EACAC;IACA;MACAC;MACAC;QACA;QACAC;MACA;MACAC;QACAC;MACA;MACAC;QACAD;MACA;MACAE;QACAF;MACA;MACAG;QACA;QACAC;UACAC;QACA;;QACAC;UACAD;QACA;MACA;;MACAE;MACAC;QACAC;QACAC;QACAC;QACAC;QACAC;QACAC;QACAC;MACA;MACAC;QACA;QACAC;QAAA;QACAC;QACAC;QAAA;QACAC;QAAA;QACAC;QACAC;UACA3B;UAAA;UACA4B;UACAC;QACA;;QACAC;UACAC;UACAC;UAAA;UACAC;QACA;;QACAC;MACA;;MACAC;QACA;QACAR;UACA;UACAS;UACAC;QACA;QACAC;QACAC;UACA3B;UACA4B;UAAA;UACA3B;YACA4B;UACA;QACA;MACA;;MACAC;QACA;QACAJ;QAAA;QACAK;QAAA;QACAC;QAAA;QACAjB;UACA;UACAU;UAAA;UACAD;QACA;;QACAS;UACA;UACAtB;UACAuB;UAAA;UACAtB;UAAA;UACAC;UAAA;UACAC;UAAA;UACAI;YACA;YACAC;YAAA;YACAC;YAAA;YACAC;UACA;;UACAN;YACA;YACA3B;UACA;QACA;MACA;;MACA+C;MAAA;MACAC;IACA;EACA;;EACAC;EACAC;IACAjD;MACAkD;QACA;QACA;UACArC;UACAC;UACAC;UACAC;UACAC;UACAC;UACAC;QACA;MACA;MACA;MACAgC;IACA;EACA;EACAC;EACAC;EAEAC;IACAC;MAAA;MACA;MACA;QACA;QACA;QACA;MACA;IACA;IACA;AACA;IACAC;MAAA;MACA;QACAC;QACA1B;QACA2B;QACA;QACA7C,YACA,qCACA,MACA;QACAE;QACAC;QACAF;QACAI,gEACAyC,4DACA;QACAxC,4DACAwC,0DACA;QACAC;QACA3C;MACA;MACA1B;QACA;UAAAsE;UAAA/B;UAAAC;QACA;QACA;QACA;QACA;QACA;UACA;UACA;QACA;UACA;UACA;QACA;MACA;IACA;IACA;AACA;IACA+B;MACA;IACA;IACA;AACA;IACAC;MAAA;MACA;MACA;QACA3E;QACA;MACA;QACA,IACA,2CACA,2CACA,yCACA;UACAA;QACA;UACA;YACAqE;YACAO;cACAJ;cACA3C;YACA;UACA;UACAxB,YACAwE;YACA9E;YACA;UACA,GACA+E;YACA7E;UACA;QACA;MACA;IACA;IACA;AACA;IACA8E;MAAA;MACA;MACA;QACA/E;QACA;MACA;QACA;UACA;UACA;YACAqE;YACAW;UACA;UACA1E;YACA;UACA;QACA;UACAN;QACA;MACA;IACA;IACA;AACA;AACA;IACAiF;MAAA;MACA;MACA;QACA;UACAZ;UACAO;YACAJ;UACA;QACA;QACA/D;UACA;UACA;YACA;YACA;YACA;cACA;gBACAyE;kBACA;oBACAC;kBACA;kBACA;gBACA;cACA;YACA;YACAD;UACA;QACA;MACA;IACA;IACA;AACA;AACA;IACAE;MACA;IACA;IACA;AACA;IACAC;MACA;IACA;IACA;AACA;IACAC;MAAA;MACApF;QACA;UACA;UACA;YACAmE;YACAkB;YACAP;YACAQ,wBACA,uDACA;UAEA;UACA;UACAjF,cACAsE;YACA;cACA;cACA;gBACAR;gBACAW;cACA;cACAxE,aACAqE;gBACA;gBACA;gBACA;gBACA;gBACA;kBACA;kBACA;oBACAY;oBACA;kBACA;gBACA;cACA,GACAX;gBACA;cAAA,CACA;YACA;UACA,GACAA;YACA9E;UACA;QACA;MACA;IACA;IACA;AACA;IACA0F;MAAA;MAAA;QAAA;UAAA;YAAA;cAAA;gBAAA;gBAAA,OACA;cAAA;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IACA;IACA;AACA;IACAC;MACA;QAAA/C;MACA;MACA;MACA;IACA;EACA;AACA", "names": ["commonMsgSuccess", "commonMsgWarn", "commonMsgError", "commonMsgConfirm", "sublicenseMan", "Common", "revoke", "querySubLicense", "psdValid", "callin", "selectFlowDetailData", "name", "data", "dialogVisible", "btnAll", "btnQuery", "redColor", "color", "blueColor", "greenColor", "btnDatas", "btnRevoke", "show", "btnCallin", "config", "defaultForm", "query_type", "apply_user", "target_user", "reason", "is_retake", "start_apply_time", "end_apply_time", "table", "tableColumns", "ref", "selection", "indexNumber", "loading", "componentProps", "height", "formRow", "pageList", "totalNum", "currentPage", "pageSize", "currentRow", "dialog", "width", "title", "visible", "form", "labelWidth", "password", "dialogDetail", "btnSubmit", "btnCancle", "tableConfig", "columns", "Successdialog", "Sencond", "computed", "watch", "handler", "deep", "created", "mounted", "methods", "dialogShow", "queryList", "parameterList", "pageNum", "date8Format", "inst_id", "list", "currentChange", "handleRevoke", "sysMap", "then", "catch", "handleCallin", "user_no", "approveDetail", "item", "roleArr", "changeVisible", "dialogClose", "dialogSubmit", "oper_type", "passWord", "window", "logout", "getList"], "sourceRoot": "src/layout/components/SublicenseMan", "sources": ["index.vue"], "sourcesContent": ["/* * *转授权管理 */\r\n<template>\r\n  <div>\r\n    <el-dialog :visible.sync=\"dialogVisible\" title=\"转授权管理\" width=\"80%\">\r\n      <sun-form\r\n        :config=\"config\"\r\n        :default-form=\"defaultForm\"\r\n        :query=\"btnAll.btnQuery\"\r\n        :reset=\"true\"\r\n        label-width=\"15rem\"\r\n        @query=\"queryList\"\r\n      />\r\n      <sun-table\r\n        :table-config=\"table\"\r\n        @current-change=\"currentChange\"\r\n        @pagination=\"getList\"\r\n      >\r\n        <template slot=\"tableColumn\">\r\n          <el-table-column\r\n            v-for=\"item in table.tableColumns\"\r\n            :key=\"item.id\"\r\n            :prop=\"item.name\"\r\n            :label=\"item.label\"\r\n            :width=\"item.width\"\r\n          >\r\n            <div slot-scope=\"{ row }\">\r\n              <span\r\n                v-if=\"\r\n                  item.name === 'sublicense_id' && row['author_type'] === '2'\r\n                \"\r\n                style=\"color: blue\"\r\n                @click=\"approveDetail(row)\"\r\n              >{{ row[item.name] }}</span>\r\n              <span v-else-if=\"item.name === 'sublicense_reason'\">{{\r\n                row[item.name] | commonFormatValue('SUBLICENSE_REASON')\r\n              }}</span>\r\n              <span v-else-if=\"item.name === 'is_retake'\">{{\r\n                row[item.name] | commonFormatValue('SUBLICENSE_IS_RETAKE')\r\n              }}</span>\r\n              <span v-else-if=\"item.name === 'start_date'\">{{\r\n                row[item.name] | date10Format\r\n              }}</span>\r\n              <span v-else-if=\"item.name === 'end_date'\">{{\r\n                row[item.name] | date10Format\r\n              }}</span>\r\n              <span v-else>{{ row[item.name] }}</span>\r\n            </div>\r\n          </el-table-column>\r\n        </template>\r\n        <template slot=\"customButton\">\r\n          <sun-button\r\n            :btn-datas=\"btnDatas\"\r\n            @handleRevoke=\"handleRevoke\"\r\n            @handleCallin=\"handleCallin\"\r\n          />\r\n          <!--按钮配置-->\r\n        </template>\r\n      </sun-table></el-dialog>\r\n    <!-- 流水号审批详情弹出框 begin-->\r\n    <sun-table-dialog\r\n      :dialog-config=\"dialogDetail\"\r\n      @dialogClose=\"changeVisible\"\r\n    >\r\n      <div slot-scope=\"{ item, row }\" class=\"appr-status\">\r\n        <span v-if=\"item.name === 'organ_no'\">{{\r\n          row[item.name] | organNameFormat\r\n        }}</span>\r\n        <span\r\n          v-else-if=\"item.name === 'role_no'\"\r\n          class=\"textOverflow\"\r\n          :title=\"row[item.name]\"\r\n        >{{ row[item.name] }}</span>\r\n        <span v-else-if=\"item.name === 'deal_time'\">{{\r\n          row[item.name] | dateTimeFormat\r\n        }}</span>\r\n        <span v-else-if=\"item.name === 'deal_result'\">{{\r\n          row[item.name] | commonFormatValue('DEAL_RESULT')\r\n        }}</span>\r\n        <span\r\n          v-else-if=\"item.name === 'deal_state'\"\r\n          :style=\"\r\n            row[item.name] === '2'\r\n              ? blueColor\r\n              : row[item.name] === '4'\r\n                ? greenColor\r\n                : redColor\r\n          \"\r\n        >{{ row[item.name] | commonFormatValue('DEAL_STATE') }}</span>\r\n\r\n        <span v-else>{{ row[item.name] }}</span>\r\n      </div></sun-table-dialog>\r\n    <!--收回授权密码弹出框begin-->\r\n    <sun-form-dialog\r\n      :dialog-config=\"dialog\"\r\n      @dialogClose=\"dialogClose\"\r\n      @dialogSubmit=\"dialogSubmit\"\r\n    />\r\n    <!-- 转授权成功弹窗 begin-->\r\n    <el-dialog\r\n      title=\"重新登陆\"\r\n      top=\"15%\"\r\n      :visible.sync=\"Successdialog\"\r\n      :show-close=\"false\"\r\n      :close-on-press-escape=\"false\"\r\n      :close-on-click-modal=\"false\"\r\n      width=\"30%\"\r\n    >\r\n      <span style=\"font-size: 14px\">\r\n        <el-button type=\"success\" icon=\"el-icon-check\" circle /><span\r\n          style=\"color: teal\"\r\n        >\r\n          转授权成功 </span><span>请等待<span style=\"font-size: 20px; color: red\"> {{ Sencond }} </span>秒，将自动跳转到登录页面</span></span>\r\n    </el-dialog>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport { config, configTable, configDetailTable, configPsd } from './info' // 表头、表单配置\r\nimport { encryptResult } from '@/utils/crypto' // 加密\r\nimport { dictionaryFieds } from '@/utils/dictionary.js' // 字典常量\r\nimport { date8Format } from '@/filters'\r\nimport { commonBlank } from '@/utils/common'\r\nimport {\r\n  commonMsgSuccess,\r\n  commonMsgWarn,\r\n  commonMsgError,\r\n  commonMsgConfirm\r\n} from '@/utils/message.js' // 提示信息\r\nimport { Common } from '@/api'\r\nconst {\r\n  sublicenseMan,\r\n  revoke,\r\n  querySubLicense, // 判断用户是否有可收回信息\r\n  psdValid, // 密码校验\r\n  callin,\r\n  selectFlowDetailData\r\n} = Common\r\nexport default {\r\n  name: 'SublicenseMan',\r\n  data() {\r\n    return {\r\n      dialogVisible: false,\r\n      btnAll: {\r\n        // 当前页需要配置权限的按钮  权限获取\r\n        btnQuery: true\r\n      },\r\n      redColor: {\r\n        color: 'red'\r\n      },\r\n      blueColor: {\r\n        color: 'blue'\r\n      },\r\n      greenColor: {\r\n        color: '#008000'\r\n      },\r\n      btnDatas: {\r\n        // 按钮配置\r\n        btnRevoke: {\r\n          show: true // 撤销\r\n        },\r\n        btnCallin: {\r\n          show: true // 收回\r\n        }\r\n      },\r\n      config: config(this),\r\n      defaultForm: {\r\n        query_type: '0',\r\n        apply_user: '',\r\n        target_user: '',\r\n        reason: '',\r\n        is_retake: '',\r\n        start_apply_time: '',\r\n        end_apply_time: ''\r\n      },\r\n      table: {\r\n        // 表格配置\r\n        tableColumns: configTable(), // 表头配置\r\n        ref: 'tableRef',\r\n        selection: false, // 复选\r\n        indexNumber: true, // 序号\r\n        loading: false,\r\n        componentProps: {\r\n          data: [], // 表格数据\r\n          height: '210px',\r\n          formRow: 3 // 表单行数\r\n        },\r\n        pageList: {\r\n          totalNum: 0,\r\n          currentPage: 1, // 当前页\r\n          pageSize: this.$store.getters.pageSize // 当前页显示条数\r\n        },\r\n        currentRow: [] // 选中行\r\n      },\r\n      dialog: {\r\n        // 目标用户密码弹框\r\n        componentProps: {\r\n          // 弹出框配置属性\r\n          width: '50rem',\r\n          title: '密码校验'\r\n        },\r\n        visible: false,\r\n        form: {\r\n          config: configPsd(this),\r\n          labelWidth: '14rem', // 当前表单标签宽度配置\r\n          defaultForm: {\r\n            password: this.$store.getters.emptyPswd // 目标用户密码\r\n          }\r\n        }\r\n      },\r\n      dialogDetail: {\r\n        // 流水号审批详情\r\n        visible: false, // 显示隐藏配置\r\n        btnSubmit: false, // 确定按钮\r\n        btnCancle: false, // 取消按钮\r\n        componentProps: {\r\n          // 弹出框属性\r\n          title: '查看详情', // 弹出框标题\r\n          width: '80%' // 当前弹出框宽度 默认80%\r\n        },\r\n        tableConfig: {\r\n          // 表格属性\r\n          ref: 'tableRef',\r\n          columns: configDetailTable(this), // 表头\r\n          selection: false, // 复选\r\n          indexNumber: false, // 序号\r\n          loading: false, // 等待加载中\r\n          pageList: {\r\n            // 页码\r\n            totalNum: 0, // 总页数\r\n            currentPage: 1, // 当前页\r\n            pageSize: this.$store.getters.pageSize // 当前页显示条数\r\n          },\r\n          componentProps: {\r\n            // 表格属性配置\r\n            data: [] // 表数据\r\n          }\r\n        }\r\n      },\r\n      Successdialog: false, // 转授权成功提示弹窗\r\n      Sencond: 5 // 设置初始倒计时\r\n    }\r\n  },\r\n  computed: {},\r\n  watch: {\r\n    dialogVisible: {\r\n      handler(newName, oldName) {\r\n        // 弹框关闭清空所有查询条件 解决弹窗关闭下次打开查询条件还在的问题\r\n        this.defaultForm = {\r\n          query_type: '0',\r\n          apply_user: '',\r\n          target_user: '',\r\n          reason: '',\r\n          is_retake: '',\r\n          start_apply_time: '',\r\n          end_apply_time: ''\r\n        }\r\n      },\r\n      // immediate: true\r\n      deep: true\r\n    }\r\n  },\r\n  created() {},\r\n  mounted() {},\r\n\r\n  methods: {\r\n    dialogShow() {\r\n      this.dialogVisible = true\r\n      this.$nextTick(() => {\r\n        this.config.query_type.options = dictionaryFieds('SM_QUERY_TYPE') // 查询类型\r\n        this.config.reason.options = dictionaryFieds('SUBLICENSE_REASON') // 授权原因\r\n        this.config.is_retake.options = dictionaryFieds('SUBLICENSE_IS_RETAKE') // 授权状态\r\n      })\r\n    },\r\n    /**\r\n     * 按钮 -查询*/\r\n    queryList() {\r\n      const msg = {\r\n        parameterList: [],\r\n        currentPage: this.table.pageList.currentPage,\r\n        pageNum: this.table.pageList.pageSize,\r\n        // 查询的条件\r\n        query_type:\r\n          this.defaultForm.query_type === ''\r\n            ? '0'\r\n            : this.defaultForm.query_type,\r\n        target_user: this.defaultForm.target_user,\r\n        reason: this.defaultForm.reason,\r\n        apply_user: this.defaultForm.apply_user,\r\n        start_apply_time: this.defaultForm.start_apply_time\r\n          ? `${date8Format(this.defaultForm.start_apply_time)}000000`\r\n          : '',\r\n        end_apply_time: this.defaultForm.end_apply_time\r\n          ? `${date8Format(this.defaultForm.end_apply_time)}240000`\r\n          : '',\r\n        inst_id: '',\r\n        is_retake: this.defaultForm.is_retake\r\n      }\r\n      sublicenseMan(msg).then((res) => {\r\n        const { list, totalNum, currentPage } = res.retMap\r\n        this.table.componentProps.data = list\r\n        this.table.pageList.totalNum = totalNum\r\n        this.table.pageList.currentPage = currentPage\r\n        // 撤销按钮显隐事件 不为空或不为0  按钮隐藏\r\n        if (msg.query_type !== '0') {\r\n          this.btnDatas.btnRevoke.show = false // 按钮隐藏\r\n          this.btnDatas.btnCallin.show = false // 按钮隐藏\r\n        } else {\r\n          this.btnDatas.btnRevoke.show = true // 按钮显示\r\n          this.btnDatas.btnCallin.show = true // 按钮显示\r\n        }\r\n      })\r\n    },\r\n    /**\r\n     *选择行 */\r\n    currentChange(row) {\r\n      this.table.currentRow = row\r\n    },\r\n    /**\r\n     *按钮  撤销 */\r\n    handleRevoke() {\r\n      const rows = this.table.currentRow.length\r\n      if (rows === 0) {\r\n        commonMsgWarn('请选中一条数据', this)\r\n        return\r\n      } else {\r\n        if (\r\n          this.table.currentRow.is_retake === '3' ||\r\n          this.table.currentRow.is_retake === '4' ||\r\n          this.table.currentRow.is_retake === '5'\r\n        ) {\r\n          commonMsgWarn('只有待审批和待授权的转授权可以进行撤销操作', this)\r\n        } else {\r\n          const msg = {\r\n            parameterList: [],\r\n            sysMap: {\r\n              inst_id: this.table.currentRow.sublicense_id,\r\n              is_retake: this.table.currentRow.is_retake\r\n            }\r\n          }\r\n          revoke(msg)\r\n            .then((res) => {\r\n              commonMsgSuccess('撤销成功', this)\r\n              this.queryList()\r\n            })\r\n            .catch(() => {\r\n              commonMsgError('撤销失败', this)\r\n            })\r\n        }\r\n      }\r\n    },\r\n    /**\r\n     *按钮  收回授权 */\r\n    handleCallin() {\r\n      const rows = this.table.currentRow.length\r\n      if (rows === 0) {\r\n        commonMsgWarn('请选中一条数据', this)\r\n        return\r\n      } else {\r\n        if (this.table.currentRow.is_retake === '3') {\r\n          // 判断用户是否有可收回转授权信息\r\n          const msg = {\r\n            parameterList: [],\r\n            user_no: this.$store.getters.userNo\r\n          }\r\n          querySubLicense(msg).then((res) => {\r\n            this.dialog.visible = true // 密码检验框显示\r\n          })\r\n        } else {\r\n          commonMsgWarn('只有已授权的转授权可以进行收回授权操作', this)\r\n        }\r\n      }\r\n    },\r\n    /**\r\n     *流水号-审批详情\r\n     *@param row 当前行数据*/\r\n    approveDetail(row) {\r\n      this.changeVisible(true)\r\n      this.$nextTick(() => {\r\n        const msg = {\r\n          parameterList: [],\r\n          sysMap: {\r\n            inst_id: row.sublicense_id\r\n          }\r\n        }\r\n        selectFlowDetailData(msg).then((res) => {\r\n          this.dialogDetail.tableConfig.componentProps.data = res.retMap.list\r\n          this.dialogDetail.tableConfig.componentProps.data.forEach((item) => {\r\n            // 待审批角色格式化\r\n            const roleArr = []\r\n            this.$store.getters.roleList.map((val) => {\r\n              if (!commonBlank(item.role_no)) {\r\n                item.role_no.split(',').map((i) => {\r\n                  if (i === val.value) {\r\n                    roleArr.push(val.label)\r\n                  }\r\n                  return roleArr\r\n                })\r\n              }\r\n            })\r\n            item.role_no = roleArr.join()\r\n          })\r\n        })\r\n      })\r\n    },\r\n    /**\r\n     * 流水号详情关闭显示\r\n     */\r\n    changeVisible(param) {\r\n      this.dialogDetail.visible = param\r\n    },\r\n    /**\r\n     * 收回授权密码框弹出框 - 关闭*/\r\n    dialogClose() {\r\n      this.dialog.visible = false\r\n    },\r\n    /**\r\n     * 收回授权密码框弹出框 - 提交*/\r\n    dialogSubmit() {\r\n      commonMsgConfirm('是否确认收回授权', this, (param) => {\r\n        if (param) {\r\n          // 先进行密码校验 再发起收回授权请求\r\n          const msg = {\r\n            parameterList: [],\r\n            oper_type: 'queryUser',\r\n            user_no: this.table.currentRow.target_user_no,\r\n            passWord: encryptResult(\r\n              this.$store.getters.initParams.enSecMap.encryptType,\r\n              this.dialog.form.defaultForm.password\r\n            )\r\n          }\r\n          // 密码校验\r\n          psdValid(msg)\r\n            .then((res) => {\r\n              if (res.retCode === '200') {\r\n                // 发起收回授权请求\r\n                const msg2 = {\r\n                  parameterList: [],\r\n                  user_no: this.$store.getters.userNo\r\n                }\r\n                callin(msg2)\r\n                  .then((res2) => {\r\n                    // 跳转到重新登录页面\r\n                    this.dialogVisible = false // 授权管理页面关闭\r\n                    this.dialog.visible = false // 密码框关闭\r\n                    this.Successdialog = true\r\n                    const interval = setInterval(() => {\r\n                      this.Sencond--\r\n                      if (this.Sencond === 0) {\r\n                        window.clearInterval(interval)\r\n                        this.logout() // 倒计时结束时跳转登录页面\r\n                      }\r\n                    }, 1000)\r\n                  })\r\n                  .catch((e) => {\r\n                    // console.log(e)\r\n                  })\r\n              }\r\n            })\r\n            .catch(() => {\r\n              commonMsgWarn('密码错误', this)\r\n            })\r\n        }\r\n      })\r\n    },\r\n    /**\r\n     * 跳转登录页面方法*/\r\n    async logout() {\r\n      await this.$store.dispatch('user/logout')\r\n      this.$router.push(`/login?redirect=${this.$route.fullPath}`)\r\n    },\r\n    /**\r\n     *页码更新 */\r\n    getList(pageParam) {\r\n      const { currentPage, pageSize } = pageParam\r\n      this.table.pageList.pageSize = pageSize\r\n      this.table.pageList.currentPage = currentPage\r\n      this.queryList()\r\n    }\r\n  }\r\n}\r\n</script>\r\n<style lang=\"scss\" scoped>\r\n.el-button.is-circle {\r\n  padding: 10px !important;\r\n}\r\n</style>\r\n"]}]}