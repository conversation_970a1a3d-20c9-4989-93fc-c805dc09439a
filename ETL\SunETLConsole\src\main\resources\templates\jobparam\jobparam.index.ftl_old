<!DOCTYPE html>
<html>
<head>
  	<title>任务调度中心</title>
  	<#import "/common/common.macro.ftl" as netCommon>
	<@netCommon.commonStyle />
	<!-- DataTables -->
  	<link rel="stylesheet" href="${request.contextPath}/static/adminlte/plugins/datatables/dataTables.bootstrap.css">

</head>
<body class="hold-transition skin-blue sidebar-mini <#if cookieMap?exists && "off" == cookieMap["xxljob_adminlte_settings"].value >sidebar-collapse</#if>">
<div class="wrapper">
	<!-- header -->
	<@netCommon.commonHeader />
	<!-- left -->
	<@netCommon.commonLeft "jobparam" />
	
	<!-- Content Wrapper. Contains page content -->
	<div class="content-wrapper">
		<!-- Content Header (Page header) -->
		<section class="content-header">
			<h1>参数管理<small>任务调度中心</small></h1>
		</section>
		
		<!-- Main content -->
	    <section class="content">
	    
	    	<div class="row">
	    		<div class="col-xs-2">
	              	<div class="input-group">
	                	<span class="input-group-addon">执行器</span>
                		<select class="form-control" id="jobGroup" >
                            <option value="0">全部</option>
						<#list JobGroupList as group>
                				<option value="${group.id}" <#if jobGroup==group.id>selected</#if> >${group.title}</option>
                			</#list>
	                  	</select>
	              	</div>
	            </div>
                <div class="col-xs-2">
                    <div class="input-group">
                        <span class="input-group-addon">任务编号</span>
                        <select class="form-control" id="jobGroup" >
                            <option value="0">全部</option>
						<#list jobInfos as job>
                            <option value="${job.id}">${job.id}-${job.jobDesc}</option>
						</#list>
                        </select>
					</div>
                </div>
                <div class="col-xs-4">
                    <div class="input-group">
                        <span class="input-group-addon">JobHandler</span>
                        <input type="text" class="form-control" id="executorHandler" autocomplete="on" >
                    </div>
                </div>
	            <div class="col-xs-2">
	            	<button class="btn btn-block btn-info" id="searchBtn">搜索</button>
	            </div>
	            <div class="col-xs-2">
	            	<button class="btn btn-block btn-success add" type="button">+新增参数</button>
	            </div>
          	</div>
	    	
			<div class="row">
				<div class="col-xs-12">
					<div class="box">
			            <div class="box-header hide">
			            	<h3 class="box-title">调度列表</h3>
			            </div>
			            <div class="box-body" >
			              	<table id="job_list" class="table table-bordered table-striped">
				                <thead>
					            	<tr>
					            		<th name="id" >参数序号</th>
					                	<th name="jobId" >任务ID</th>
                                        <th name="param_field" >参数名称</th>
					                  	<th name="param_value" >参数</th>
                                        <th name="param_field_ch" >参数解释</th>
					                  	<th>操作</th>
					                </tr>
				                </thead>
				                <tbody></tbody>
				                <tfoot></tfoot>
							</table>
						</div>
					</div>
				</div>
			</div>
	    </section>
	</div>
	
	<!-- footer -->
	<@netCommon.commonFooter />
</div>

<!-- job新增.模态框 -->
<div class="modal fade" id="addModal" tabindex="-1" role="dialog"  aria-hidden="true">
	<div class="modal-dialog modal-lg">
		<div class="modal-content">
			<div class="modal-header">
            	<h4 class="modal-title" >新增参数</h4>
         	</div>
         	<div class="modal-body">
				<form class="form-horizontal form" role="form" >
					<div class="form-group">
						<label for="firstname" class="col-sm-2 control-label">任务编号<font color="red">*</font></label>
						<div class="col-sm-4">
                            <select class="form-control" name="jobId" >
							<#list jobInfos as job>
                                <option value="${job.id}">${job.id}-${job.jobDesc}</option>
							</#list>
                            </select>
						</div>
                        <label for="lastname" class="col-sm-2 control-label">参数字段说明<font color="black">*</font></label>
                        <div class="col-sm-4"><input type="text" class="form-control" name="param_field_ch" placeholder="参数字段说明" maxlength="100" ></div>
					</div>
                    <div class="form-group">
                        <label for="firstname" class="col-sm-2 control-label">参数字段<font color="black">*</font></label>
                        <div class="col-sm-4"><input type="text" class="form-control" name="param_field" placeholder="请输入“参数字段”" maxlength="100" ></div>
                        <label for="firstname" class="col-sm-2 control-label">参数值<font color="black">*</font></label>
                        <div class="col-sm-4"><input type="text" class="form-control" name="param_value" placeholder="参数值”" maxlength="100" ></div>
                    </div>
                    <hr>
					<div class="form-group">
						<div class="col-sm-offset-3 col-sm-6">
							<button type="submit" class="btn btn-primary"  >保存</button>
							<button type="button" class="btn btn-default" data-dismiss="modal">取消</button>
						</div>
					</div>
				</form>
         	</div>
		</div>
	</div>
</div>

<!-- 更新.模态框 -->
<div class="modal fade" id="updateModal" tabindex="-1" role="dialog"  aria-hidden="true">
	<div class="modal-dialog modal-lg">
		<div class="modal-content">
			<div class="modal-header">
            	<h4 class="modal-title" >更新参数</h4>
         	</div>
         	<div class="modal-body">
                <form class="form-horizontal form" role="form" >
                    <div class="form-group">
                        <label for="firstname" class="col-sm-2 control-label">JobKey<font color="red">*</font></label>
                        <div class="col-sm-4">
                            <select class="form-control" name="jobId" >
							<#list jobInfos as job>
                                <option value="${job.id}">${job.id}-${job.jobDesc}</option>
							</#list>
                            </select>
                        </div>
                        <label for="firstname" class="col-sm-2 control-label">参数字段<font color="black">*</font></label>
                        <div class="col-sm-4"><input type="text" class="form-control" name="param_field" placeholder="请输入“参数字段”" maxlength="100" ></div>
                    </div>
                    <div class="form-group">
                        <label for="firstname" class="col-sm-2 control-label">参数值<font color="black">*</font></label>
                        <div class="col-sm-4"><input type="text" class="form-control" name="param_value" placeholder="参数值”" maxlength="100" ></div>
                        <label for="lastname" class="col-sm-2 control-label">参数字段说明<font color="black">*</font></label>
                        <div class="col-sm-4"><input type="text" class="form-control" name="param_field_ch" placeholder="参数字段说明" maxlength="100" ></div>
                    </div>
                    <hr>
                    <div class="form-group">
                        <div class="col-sm-offset-3 col-sm-6">
                            <button type="submit" class="btn btn-primary"  >保存</button>
                            <button type="button" class="btn btn-default" data-dismiss="modal">取消</button>
                            <input type="hidden" name="id" >
						</div>
                    </div>
                </form>
         	</div>
		</div>
	</div>
</div>

<@netCommon.commonScript />
<!-- DataTables -->
<script src="${request.contextPath}/static/adminlte/plugins/datatables/jquery.dataTables.min.js"></script>
<script src="${request.contextPath}/static/adminlte/plugins/datatables/dataTables.bootstrap.min.js"></script>
<script src="${request.contextPath}/static/plugins/jquery/jquery.validate.min.js"></script>
<!-- moment -->
<script src="${request.contextPath}/static/adminlte/plugins/daterangepicker/moment.min.js"></script>
<script src="${request.contextPath}/static/js/jobparam.index.1.js"></script>
</body>
</html>
