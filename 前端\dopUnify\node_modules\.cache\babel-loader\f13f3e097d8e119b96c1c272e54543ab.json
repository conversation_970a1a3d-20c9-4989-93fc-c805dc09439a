{"remainingRequest": "E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1\\数字运营平台-统一门户工程\\dopUnify\\node_modules\\babel-loader\\lib\\index.js!E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1\\数字运营平台-统一门户工程\\dopUnify\\node_modules\\eslint-loader\\index.js??ref--13-0!E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1\\数字运营平台-统一门户工程\\dopUnify\\src\\views\\home\\info.js", "dependencies": [{"path": "E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1\\数字运营平台-统一门户工程\\dopUnify\\src\\views\\home\\info.js", "mtime": 1716875179204}, {"path": "E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1\\数字运营平台-统一门户工程\\dopUnify\\babel.config.js", "mtime": 1716875191548}, {"path": "E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1\\数字运营平台-统一门户工程\\dopUnify\\.babelrc", "mtime": 1716875191502}, {"path": "E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1\\数字运营平台-统一门户工程\\dopUnify\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1\\数字运营平台-统一门户工程\\dopUnify\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1\\数字运营平台-统一门户工程\\dopUnify\\node_modules\\eslint-loader\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["config", "name", "label", "noteConfig", "that", "note_name", "component", "colSpan", "rules", "required", "message", "componentProps", "clearable", "options", "warn_time", "valueFormat", "format", "pickerOptions", "selectableRange", "warn_note", "hidden", "value", "methods", "change", "val", "noteDialog", "form", "warn_phone", "pattern", "note_info", "placeholder", "type", "rows"], "sources": ["E:/1_Project/XYD_Project/dop-4.0/dop-4.1/数字运营平台-统一门户工程/dopUnify/src/views/home/<USER>"], "sourcesContent": ["// 表单\r\nexport const config = () => [\r\n  {\r\n    name: 'last_modi_date',\r\n    label: '记录时间'\r\n  },\r\n  {\r\n    name: 'warn_time',\r\n    label: '提醒时间'\r\n  },\r\n  {\r\n    name: 'warn_note',\r\n    label: '短信提醒标识'\r\n  },\r\n  {\r\n    name: 'warn_phone',\r\n    label: '提醒手机号'\r\n  },\r\n  {\r\n    name: 'note_info',\r\n    label: '便签内容'\r\n  }\r\n]\r\n\r\nexport const noteConfig = (that) => ({\r\n  note_name: {\r\n    component: 'input',\r\n    label: '便签名称',\r\n    colSpan: 24,\r\n    name: 'note_name',\r\n    config: {\r\n      rules: [{ required: true, message: '便签名称为必输' }]\r\n    },\r\n    componentProps: {\r\n      clearable: true\r\n    },\r\n    options: []\r\n  },\r\n  warn_time: {\r\n    component: 'time-picker',\r\n    label: '提醒时间',\r\n    colSpan: 24,\r\n    name: 'warn_time',\r\n    config: {\r\n      rules: [{ required: true, message: '提醒时间为必输' }]\r\n    },\r\n    componentProps: {\r\n      clearable: true,\r\n      valueFormat: 'HHmmss',\r\n      format: 'HH:mm:ss',\r\n      pickerOptions: {\r\n        selectableRange: ''\r\n      }\r\n    },\r\n    options: []\r\n  },\r\n  warn_note: {\r\n    component: 'select',\r\n    label: '短信提醒标识',\r\n    colSpan: 24,\r\n    name: 'warn_note',\r\n    hidden: false,\r\n    componentProps: {\r\n      clearable: true\r\n    },\r\n    options: [\r\n      {\r\n        label: '是',\r\n        value: '1'\r\n      },\r\n      {\r\n        label: '否',\r\n        value: '0'\r\n      }\r\n    ],\r\n    methods: {\r\n      change(val) {\r\n        if (val === '0') that.noteDialog.form.config.warn_phone.hidden = true\r\n        else that.noteDialog.form.config.warn_phone.hidden = false\r\n      }\r\n    }\r\n  },\r\n  warn_phone: {\r\n    component: 'input',\r\n    label: '提醒手机号',\r\n    colSpan: 24,\r\n    name: 'warn_phone',\r\n    componentProps: {\r\n      clearable: true\r\n    },\r\n    config: {\r\n      // form-item 配置\r\n      rules: [\r\n        {\r\n          pattern: /^1[3456789]\\d{9}$/,\r\n          message: '手机号码不合法，请重新输入',\r\n          required: true\r\n        }\r\n      ]\r\n    },\r\n    options: []\r\n  },\r\n  note_info: {\r\n    component: 'input',\r\n    label: '便签内容',\r\n    colSpan: 24,\r\n    name: 'note_info',\r\n    config: {\r\n      rules: [{ required: true, message: '便签内容为必输' }]\r\n    },\r\n    componentProps: {\r\n      clearable: true,\r\n      placeholder: '',\r\n      type: 'textarea',\r\n      rows: 3\r\n    }\r\n  }\r\n})\r\n"], "mappings": "AAAA;AACA,OAAO,IAAMA,MAAM,GAAG,SAATA,MAAM;EAAA,OAAS,CAC1B;IACEC,IAAI,EAAE,gBAAgB;IACtBC,KAAK,EAAE;EACT,CAAC,EACD;IACED,IAAI,EAAE,WAAW;IACjBC,KAAK,EAAE;EACT,CAAC,EACD;IACED,IAAI,EAAE,WAAW;IACjBC,KAAK,EAAE;EACT,CAAC,EACD;IACED,IAAI,EAAE,YAAY;IAClBC,KAAK,EAAE;EACT,CAAC,EACD;IACED,IAAI,EAAE,WAAW;IACjBC,KAAK,EAAE;EACT,CAAC,CACF;AAAA;AAED,OAAO,IAAMC,UAAU,GAAG,SAAbA,UAAU,CAAIC,IAAI;EAAA,OAAM;IACnCC,SAAS,EAAE;MACTC,SAAS,EAAE,OAAO;MAClBJ,KAAK,EAAE,MAAM;MACbK,OAAO,EAAE,EAAE;MACXN,IAAI,EAAE,WAAW;MACjBD,MAAM,EAAE;QACNQ,KAAK,EAAE,CAAC;UAAEC,QAAQ,EAAE,IAAI;UAAEC,OAAO,EAAE;QAAU,CAAC;MAChD,CAAC;MACDC,cAAc,EAAE;QACdC,SAAS,EAAE;MACb,CAAC;MACDC,OAAO,EAAE;IACX,CAAC;IACDC,SAAS,EAAE;MACTR,SAAS,EAAE,aAAa;MACxBJ,KAAK,EAAE,MAAM;MACbK,OAAO,EAAE,EAAE;MACXN,IAAI,EAAE,WAAW;MACjBD,MAAM,EAAE;QACNQ,KAAK,EAAE,CAAC;UAAEC,QAAQ,EAAE,IAAI;UAAEC,OAAO,EAAE;QAAU,CAAC;MAChD,CAAC;MACDC,cAAc,EAAE;QACdC,SAAS,EAAE,IAAI;QACfG,WAAW,EAAE,QAAQ;QACrBC,MAAM,EAAE,UAAU;QAClBC,aAAa,EAAE;UACbC,eAAe,EAAE;QACnB;MACF,CAAC;MACDL,OAAO,EAAE;IACX,CAAC;IACDM,SAAS,EAAE;MACTb,SAAS,EAAE,QAAQ;MACnBJ,KAAK,EAAE,QAAQ;MACfK,OAAO,EAAE,EAAE;MACXN,IAAI,EAAE,WAAW;MACjBmB,MAAM,EAAE,KAAK;MACbT,cAAc,EAAE;QACdC,SAAS,EAAE;MACb,CAAC;MACDC,OAAO,EAAE,CACP;QACEX,KAAK,EAAE,GAAG;QACVmB,KAAK,EAAE;MACT,CAAC,EACD;QACEnB,KAAK,EAAE,GAAG;QACVmB,KAAK,EAAE;MACT,CAAC,CACF;MACDC,OAAO,EAAE;QACPC,MAAM,kBAACC,GAAG,EAAE;UACV,IAAIA,GAAG,KAAK,GAAG,EAAEpB,IAAI,CAACqB,UAAU,CAACC,IAAI,CAAC1B,MAAM,CAAC2B,UAAU,CAACP,MAAM,GAAG,IAAI,MAChEhB,IAAI,CAACqB,UAAU,CAACC,IAAI,CAAC1B,MAAM,CAAC2B,UAAU,CAACP,MAAM,GAAG,KAAK;QAC5D;MACF;IACF,CAAC;IACDO,UAAU,EAAE;MACVrB,SAAS,EAAE,OAAO;MAClBJ,KAAK,EAAE,OAAO;MACdK,OAAO,EAAE,EAAE;MACXN,IAAI,EAAE,YAAY;MAClBU,cAAc,EAAE;QACdC,SAAS,EAAE;MACb,CAAC;MACDZ,MAAM,EAAE;QACN;QACAQ,KAAK,EAAE,CACL;UACEoB,OAAO,EAAE,mBAAmB;UAC5BlB,OAAO,EAAE,eAAe;UACxBD,QAAQ,EAAE;QACZ,CAAC;MAEL,CAAC;MACDI,OAAO,EAAE;IACX,CAAC;IACDgB,SAAS,EAAE;MACTvB,SAAS,EAAE,OAAO;MAClBJ,KAAK,EAAE,MAAM;MACbK,OAAO,EAAE,EAAE;MACXN,IAAI,EAAE,WAAW;MACjBD,MAAM,EAAE;QACNQ,KAAK,EAAE,CAAC;UAAEC,QAAQ,EAAE,IAAI;UAAEC,OAAO,EAAE;QAAU,CAAC;MAChD,CAAC;MACDC,cAAc,EAAE;QACdC,SAAS,EAAE,IAAI;QACfkB,WAAW,EAAE,EAAE;QACfC,IAAI,EAAE,UAAU;QAChBC,IAAI,EAAE;MACR;IACF;EACF,CAAC;AAAA,CAAC"}]}