package com.sunyard.etl.nps.orm;

import java.sql.ResultSet;
import java.sql.SQLException;

import com.sunyard.etl.nps.model.BpTmpdata1Tb;
import com.sunyard.etl.system.orm.Orm;

public class BpTmpdata1Orm implements Orm<BpTmpdata1Tb> {

	public BpTmpdata1Tb orm(ResultSet rs) {
		BpTmpdata1Tb bpTmpdata1Tb = new BpTmpdata1Tb();
		try {
			bpTmpdata1Tb.setBatchId(rs.getString("BATCH_ID"));
			bpTmpdata1Tb.setInccodeinBatch(rs.getInt("INCCODEIN_BATCH"));
			bpTmpdata1Tb.setPsLevel(rs.getString("PS_LEVEL"));
			bpTmpdata1Tb.setFlowId(rs.getString("FLOW_ID"));
			bpTmpdata1Tb.setFileName(rs.getString("FILE_NAME"));
			bpTmpdata1Tb.setContentId(rs.getString("CONTENT_ID"));
		} catch (SQLException e) {
			e.printStackTrace();
		}
		return bpTmpdata1Tb;
	}
}
