{"remainingRequest": "D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\node_modules\\babel-loader\\lib\\index.js!D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\src\\views\\system\\config\\ecmConf\\component\\table\\index.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\src\\views\\system\\config\\ecmConf\\component\\table\\index.vue", "mtime": 1686019808154}, {"path": "D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\babel.config.js", "mtime": 1686019818359}, {"path": "D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1655715099000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA6CA,SACAA,kBACAC,eACAC,wBACA;AACA;AACA;AACA;;AAEA;AACA;EAAAC;EAAAC;EAAAC;EAAAC;AACA;EACAC;EACAC;IACA;EAAA,CACA;EACAC;EACAC;IACAC;MACAC;MACAC;QACA;MACA;IACA;IACAC;MACAF;MACAC;QACA;MACA;IACA;EACA;EACAE;IACA;MACAC;MACAC;QACA;QACAC;QAAA;QACAC;QACAC;QAAA;QACAC;QAAA;QACAC;QACAC;UACAR;UAAA;UACAS;QACA;QACAC;UACAC;UACAC;UAAA;UACAC;QACA;;QACAC;MACA;;MACAC;QACA;QACAC;UACAC;QACA;QACAC;UACAD;QACA;QACAE;UACAF;QACA;MACA;MACAG;MACAC;MAAA;MACAC;QACAd;UACA;UACAe;UACAC;QACA;QACAC;QACAC;UACAC;UACAC;UAAA;UACAhC;YACAiC;YACAC;YACAC;UACA;QACA;MACA;IACA;EACA;EACAC;EACAC;IACA1B;MACA;IACA;IACA;MACA2B;QACA;UACA;UACA;QACA;UACA;QACA;MACA;MACAC;IACA;EACA;EACAC;IACA;MACA;MACA;IACA;IACA;EACA;;EACAC;IACA;AACA;AACA;AACA;AACA;IACAC;MACA;MACA;QACA;QACA;UACA;UACAC;QACA;QACAC;MACA;MACA;IACA;IACA;IACAC;MACA;MACA;QACA3B;UACA;QACA;MACA;;MACA;IACA;IACA;AACA;IACA4B;MAAA;MACA;MACA;QACAC;QACA/B;QACAC;MACA;MACA;MACAzB;QACA;UAAAwD;UAAAjC;UAAAC;QACA;QACA;QACA;QACA;UACA;YACA;cACAiC;YACA;UACA;UACA;YACA;cACAA;YACA;UACA;QACA;;QAEA;QACA;MACA;IACA;IACA;AACA;AACA;IACAC;MACA;IACA;IACA;AACA;IACAC;MACA;MACA;MACAC;QACA;UACAC;QACA;QACAC;MACA;MACA;MACA;MACA;IACA;IACA;AACA;IACAC;MACA,6DACA;QACA3B;QACA4B;MAAA,EACA;MACA;MACA;IACA;IACA;AACA;IACAC;MAAA;MACA;MACA;QACAnE;QACA;MACA;QACAA;QACA;MACA;MACA,6DACA;QACAsC;QACA4B;MAAA,EACA;MACA;MACA;MACA;QACA;QACA;QACA;QACAE;UACAC;QACA;QACA;MACA;IACA;IAEA;AACA;IACAC;MACA;MACA;QACA;MACA;QACA;MACA;IACA;IACA;AACA;IACAC;MAAA;MACA;MACA;QACAd;UAEAe;QAAA,GACA,8BAEA;QACAC;QACAC;QACAC;QACAC;MACA;MACAzE,SACA0E;QACA9E;QACA;QACA;MACA,GACA+E;QACA;MACA;MACA;IACA;IAEA;AACA;IACAC;MAAA;MACA9E;QACA;UACA;UACA;YACAwD;cAEAe;YAAA,GAGA,gCAEA;YACAC,wCACA;cACA7B;cACAC;YAAA,EACA;YACA6B;YACAC,6CACA;cACA/B;cACAC;YAAA,EACA;YACA+B;UACA;UACA;UACAxE,YACAyE;YACA9E;YACA;YACA;UACA,GACA+E;YACA;UACA;UACA;QACA;MACA;IACA;IACA;AACA;IACAE;MAAA;MACA;MACA;QACAhF;QACA;MACA;MACAC;QACA;UACA,uCACA,gBACA,aACA,cACA,WACA;UACA;YACAwD;YACAwB;YACAL;UACA;UACAvE,SACAwE;YACA9E;YACA;UACA,GACA+E;YACA;UAAA,CACA;QACA;MACA;IACA;IACA;AACA;IACAI;MACA;QAAAvD;MACA;MACA;MACA;IACA;IACA;AACA;IACAwD;MACA;IACA;EACA;AACA", "names": ["commonMsgSuccess", "commonMsgWarn", "commonMsgConfirm", "query", "add", "modify", "del", "name", "filters", "mixins", "props", "defaultForm", "type", "default", "btnAll", "data", "listLoading", "table", "tableColumns", "ref", "selection", "indexNumber", "loading", "componentProps", "height", "pageList", "totalNum", "currentPage", "pageSize", "currentRow", "btnDatas", "btnAdd", "show", "btnModify", "btnDelete", "downloadLoading", "currentIndex", "dialog", "width", "title", "visible", "form", "config", "labelWidth", "organ_no", "first_ecm", "second_ecm", "computed", "watch", "handler", "deep", "created", "methods", "commonChoices", "jsonObj", "jsonArr", "handleSelectionChange", "queryList", "parameterList", "returnList", "item", "changeVisible", "ecmOptions", "arrayState", "label", "ecmmArray", "handleAdd", "oprate", "handleModify", "keys", "obbb", "dialogSumbit", "dialogAddSubmit", "ecmconfig_id", "oldMsg", "user_no", "before_data", "oper_type", "then", "catch", "dialogEditSubmit", "handleDelete", "operation_value", "getList", "showLoading"], "sourceRoot": "src/views/system/config/ecmConf/component/table", "sources": ["index.vue"], "sourcesContent": ["<template>\r\n  <div class=\"sun-content\">\r\n    <sun-table\r\n      :table-config=\"table\"\r\n      @selection-change=\"handleSelectionChange\"\r\n      @pagination=\"getList\"\r\n    >\r\n      <template slot=\"tableColumn\">\r\n        <el-table-column\r\n          v-for=\"item in table.tableColumns\"\r\n          :key=\"item.id\"\r\n          :prop=\"item.name\"\r\n          :label=\"item.label\"\r\n          :width=\"item.width\"\r\n        >\r\n          <div slot-scope=\"{ row }\">\r\n            <span v-if=\"item.name === 'organ_no'\">{{\r\n              row[item.name] | organNameFormat\r\n            }}</span>\r\n            <span v-else-if=\"item.name === 'last_modi_date'\">{{\r\n              row[item.name] | dateTimeFormat\r\n            }}</span>\r\n            <span v-else>{{ row[item.name] }}</span>\r\n          </div>\r\n        </el-table-column>\r\n      </template>\r\n      <template slot=\"customButton\">\r\n        <sun-button\r\n          :btn-datas=\"btnDatas\"\r\n          @handleAdd=\"handleAdd\"\r\n          @handleModify=\"handleModify\"\r\n          @handleDelete=\"handleDelete\"\r\n        />\r\n        <!--按钮配置-->\r\n      </template>\r\n    </sun-table>\r\n    <sun-form-dialog\r\n      :dialog-config=\"dialog\"\r\n      @dialogClose=\"changeVisible\"\r\n      @dialogSubmit=\"dialogSumbit\"\r\n    /><!--新增、修改弹出框-->\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport {\r\n  commonMsgSuccess,\r\n  commonMsgWarn,\r\n  commonMsgConfirm\r\n} from '@/utils/message.js' // 提示信息\r\nimport { dictionaryGet } from '@/utils/dictionary.js' // 字典常量\r\nimport { config, configTable } from './info' // 表头、表单配置\r\nimport ResizeMixin from '@/utils/ResizeHandler' // 整体页面是否根据总高配置\r\n\r\nimport { system } from '@/api'\r\nconst { query, add, modify, del } = system.SysEcmConf\r\nexport default {\r\n  name: 'TableList',\r\n  filters: {\r\n    // dateTimeFormat,\r\n  },\r\n  mixins: [ResizeMixin],\r\n  props: {\r\n    defaultForm: {\r\n      type: Object,\r\n      default: function() {\r\n        return {}\r\n      }\r\n    },\r\n    btnAll: {\r\n      type: Object,\r\n      default: function() {\r\n        return {}\r\n      }\r\n    }\r\n  },\r\n  data() {\r\n    return {\r\n      listLoading: false,\r\n      table: {\r\n        // 表格配置\r\n        tableColumns: configTable(), // 表头配置\r\n        ref: 'tableRef',\r\n        selection: true, // 复选\r\n        indexNumber: true, // 序号\r\n        loading: false,\r\n        componentProps: {\r\n          data: [], // 表格数据\r\n          height: '100px'\r\n        },\r\n        pageList: {\r\n          totalNum: 0,\r\n          currentPage: 1, // 当前页\r\n          pageSize: this.$store.getters.pageSize // 当前页显示条数\r\n        },\r\n        currentRow: [] // 选中行\r\n      },\r\n      btnDatas: {\r\n        // 按钮配置\r\n        btnAdd: {\r\n          show: this.btnAll.btnAdd\r\n        },\r\n        btnModify: {\r\n          show: this.btnAll.btnModify\r\n        },\r\n        btnDelete: {\r\n          show: this.btnAll.btnDelete\r\n        }\r\n      },\r\n      downloadLoading: false,\r\n      currentIndex: 0, // 当前行索引\r\n      dialog: {\r\n        componentProps: {\r\n          // 弹出框配置属性\r\n          width: '90rem',\r\n          title: ''\r\n        },\r\n        visible: false,\r\n        form: {\r\n          config: config(this),\r\n          labelWidth: '15rem', // 当前表单标签宽度配置\r\n          defaultForm: {\r\n            organ_no: '',\r\n            first_ecm: '',\r\n            second_ecm: ''\r\n          }\r\n        }\r\n      }\r\n    }\r\n  },\r\n  computed: {},\r\n  watch: {\r\n    loading(value) {\r\n      this.listLoading = this.loading\r\n    },\r\n    'dialog.componentProps.oprate': {\r\n      handler(val) {\r\n        if (val === 'edit') {\r\n          // 机构禁用\r\n          this.dialog.form.config.organ_no.componentProps.disabled = true\r\n        } else {\r\n          this.dialog.form.config.organ_no.componentProps.disabled = false\r\n        }\r\n      },\r\n      deep: true\r\n    }\r\n  },\r\n  created() {\r\n    if (!this.$store.getters.externalData.ECM_ID) {\r\n      // 影像服务\r\n      this.$store.dispatch('common/setExternalData', 'ECM_ID')\r\n    }\r\n    // this.queryList()\r\n  },\r\n  methods: {\r\n    /**\r\n     * 多行数据拼写报文的方法\r\n     * @param dataArr\t选择行的数组\r\n     * @param attrArr  放置的参数数组\r\n     */\r\n    commonChoices(dataArr, attrArr) {\r\n      const jsonArr = []\r\n      for (let i = 0; i < dataArr.length; i++) {\r\n        const jsonObj = {}\r\n        for (let j = 0; j < attrArr.length; j++) {\r\n          const name = attrArr[j]\r\n          jsonObj[name] = dataArr[i][name]\r\n        }\r\n        jsonArr.push(jsonObj)\r\n      }\r\n      return jsonArr\r\n    },\r\n    // 表格选择多行\r\n    handleSelectionChange(val) {\r\n      const currentRow = val\r\n      if (currentRow.length > 1) {\r\n        currentRow.sort(function(a, b) {\r\n          return a.rn - b.rn\r\n        }) // 选中行排序\r\n      }\r\n      this.table.currentRow = val\r\n    },\r\n    /**\r\n     * 按钮：查询*/\r\n    queryList(currentPage) {\r\n      this.showLoading(true)\r\n      const msg = {\r\n        parameterList: [{ ...this.defaultForm }],\r\n        currentPage: currentPage || this.table.pageList.currentPage,\r\n        pageSize: this.table.pageList.pageSize\r\n      }\r\n      // 查询\r\n      query(msg).then((res) => {\r\n        const { returnList, totalNum, currentPage } = res.retMap\r\n        this.table.componentProps.data = returnList\r\n        this.table.pageList.totalNum = totalNum\r\n        this.table.pageList.currentPage = currentPage\r\n        this.table.componentProps.data.forEach((item) => {\r\n          this.$store.getters.externalData.ECM_ID.forEach((item2) => {\r\n            if (item2.value === item.first_ecm) {\r\n              item.first_ecm = `${item2.value}-${item2.label}`\r\n            }\r\n          })\r\n          this.$store.getters.externalData.ECM_ID.forEach((item2) => {\r\n            if (item2.value === item.second_ecm) {\r\n              item.second_ecm = `${item2.value}-${item2.label}`\r\n            }\r\n          })\r\n        })\r\n\r\n        // this.$store.getters.externalData.ECM_ID\r\n        this.showLoading(false)\r\n      })\r\n    },\r\n    /**\r\n     * 弹出框 - 关闭\r\n     * @param {Boolean} param 弹出框显示隐藏配置*/\r\n    changeVisible(param) {\r\n      this.dialog.visible = param\r\n    },\r\n    /**\r\n     * 处理弹框前后台影像服务数据*/\r\n    ecmOptions() {\r\n      const arrayState = this.$store.getters.externalData.ECM_ID\r\n      const ecmmArray = []\r\n      arrayState.map(function(item) {\r\n        const valueS = Object.assign({}, item, {\r\n          label: item.value + '-' + item.label\r\n        })\r\n        ecmmArray.push(valueS)\r\n      })\r\n      // 获取外部字典\r\n      this.dialog.form.config.first_ecm.options = ecmmArray\r\n      this.dialog.form.config.second_ecm.options = ecmmArray\r\n    },\r\n    /**\r\n     * btn - 新增*/\r\n    handleAdd() {\r\n      this.dialog.componentProps = {\r\n        ...this.dialog.componentProps,\r\n        title: '新增',\r\n        oprate: 'add'\r\n      }\r\n      this.changeVisible(true)\r\n      this.ecmOptions() // 处理弹框前后台影像服务数据\r\n    },\r\n    /**\r\n     * btn - 编辑*/\r\n    handleModify() {\r\n      const rows = this.table.currentRow.length\r\n      if (rows === 0) {\r\n        commonMsgWarn('请选择要修改的行', this)\r\n        return\r\n      } else if (rows > 1) {\r\n        commonMsgWarn('请选择一行', this)\r\n        return\r\n      }\r\n      this.dialog.componentProps = {\r\n        ...this.dialog.componentProps,\r\n        title: '编辑',\r\n        oprate: 'edit'\r\n      } // 添加属性\r\n      this.changeVisible(true)\r\n      this.ecmOptions() // 处理弹框前后台影像服务数据\r\n      this.$nextTick(() => {\r\n        // 弹出框加载完成后赋值\r\n        const obbb = {}\r\n        const keys = Object.keys(this.dialog.form.defaultForm)\r\n        keys.forEach((item) => {\r\n          obbb[item] = this.table.currentRow[0][item]\r\n        })\r\n        this.dialog.form.defaultForm = obbb\r\n      })\r\n    },\r\n\r\n    /**\r\n     * 弹出框 - 确认弹框类型*/\r\n    dialogSumbit() {\r\n      const param = this.dialog.componentProps.oprate\r\n      if (param === 'add') {\r\n        this.dialogAddSubmit()\r\n      } else {\r\n        this.dialogEditSubmit()\r\n      }\r\n    },\r\n    /**\r\n     * 弹出框 - 确认 - 新增*/\r\n    dialogAddSubmit() {\r\n      this.showLoading(true)\r\n      const msg = {\r\n        parameterList: [\r\n          {\r\n            ecmconfig_id: '后台自动生成',\r\n            ...this.dialog.form.defaultForm\r\n          }\r\n        ],\r\n        oldMsg: {},\r\n        user_no: this.$store.getters.userNo,\r\n        before_data: {},\r\n        oper_type: dictionaryGet('OPERATE_ADD')\r\n      }\r\n      add(msg)\r\n        .then((res) => {\r\n          commonMsgSuccess('新增成功', this)\r\n          this.showLoading(false)\r\n          this.queryList(1)\r\n        })\r\n        .catch(() => {\r\n          this.showLoading(false)\r\n        })\r\n      this.changeVisible(false) // 弹出框关闭\r\n    },\r\n\r\n    /**\r\n     * 弹出框 - 确认 - 编辑*/\r\n    dialogEditSubmit() {\r\n      commonMsgConfirm('是否确认提交当前数据？', this, (param) => {\r\n        if (param) {\r\n          this.showLoading(true)\r\n          const msg = {\r\n            parameterList: [\r\n              {\r\n                ecmconfig_id: this.table.currentRow[0].ecmconfig_id,\r\n                // first_ecm: '',\r\n                // second_ecm: '',\r\n                ...this.dialog.form.defaultForm\r\n              }\r\n            ],\r\n            oldMsg: {\r\n              ...this.table.currentRow[0],\r\n              first_ecm: this.table.currentRow[0].first_ecm.split('-')[0],\r\n              second_ecm: this.table.currentRow[0].second_ecm.split('-')[0]\r\n            },\r\n            user_no: this.$store.getters.userNo,\r\n            before_data: {\r\n              ...this.table.currentRow[0],\r\n              first_ecm: this.table.currentRow[0].first_ecm.split('-')[0],\r\n              second_ecm: this.table.currentRow[0].second_ecm.split('-')[0]\r\n            },\r\n            oper_type: dictionaryGet('OPERATE_MODIFY')\r\n          }\r\n          // 修改\r\n          modify(msg)\r\n            .then((res) => {\r\n              commonMsgSuccess('修改成功', this)\r\n              this.showLoading(false)\r\n              this.queryList(1)\r\n            })\r\n            .catch(() => {\r\n              this.showLoading(false)\r\n            })\r\n          this.changeVisible(false) // 弹出框关闭\r\n        }\r\n      })\r\n    },\r\n    /**\r\n     * btn - 删除*/\r\n    handleDelete() {\r\n      const rows = this.table.currentRow\r\n      if (rows.length === 0) {\r\n        commonMsgWarn('请选择要删除的行', this)\r\n        return\r\n      }\r\n      commonMsgConfirm('是否确认删除当前选中的记录？', this, (param) => {\r\n        if (param) {\r\n          const dels = this.commonChoices(rows, [\r\n            'ecmconfig_id',\r\n            'first_ecm',\r\n            'second_ecm',\r\n            'organ_no'\r\n          ])\r\n          const msg = {\r\n            parameterList: dels,\r\n            operation_value: dels,\r\n            oper_type: dictionaryGet('OPERATE_DELETE')\r\n          }\r\n          del(msg)\r\n            .then((res) => {\r\n              commonMsgSuccess('删除成功', this)\r\n              this.queryList(1)\r\n            })\r\n            .catch((e) => {\r\n              // console.log(e)\r\n            })\r\n        }\r\n      })\r\n    },\r\n    /**\r\n     *页码更新 */\r\n    getList(pageParam) {\r\n      const { currentPage, pageSize } = pageParam\r\n      this.table.pageList.pageSize = pageSize\r\n      this.table.pageList.currentPage = currentPage\r\n      this.queryList()\r\n    },\r\n    /**\r\n     * 加载中动画配置*/\r\n    showLoading() {\r\n      this.listLoading = !this.listLoading\r\n    }\r\n  }\r\n}\r\n</script>\r\n<style scoped></style>\r\n"]}]}