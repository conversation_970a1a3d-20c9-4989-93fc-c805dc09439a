{"remainingRequest": "E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1\\数字运营平台-统一门户工程\\dopUnify\\node_modules\\babel-loader\\lib\\index.js!E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1\\数字运营平台-统一门户工程\\dopUnify\\node_modules\\eslint-loader\\index.js??ref--13-0!E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1\\数字运营平台-统一门户工程\\dopUnify\\src\\api\\views\\system\\config\\ecm\\index.js", "dependencies": [{"path": "E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1\\数字运营平台-统一门户工程\\dopUnify\\src\\api\\views\\system\\config\\ecm\\index.js", "mtime": 1716875179592}, {"path": "E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1\\数字运营平台-统一门户工程\\dopUnify\\babel.config.js", "mtime": 1716875191548}, {"path": "E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1\\数字运营平台-统一门户工程\\dopUnify\\.babelrc", "mtime": 1716875191502}, {"path": "E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1\\数字运营平台-统一门户工程\\dopUnify\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1\\数字运营平台-统一门户工程\\dopUnify\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1\\数字运营平台-统一门户工程\\dopUnify\\node_modules\\eslint-loader\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:aW1wb3J0IHJlcXVlc3QgZnJvbSAnQC91dGlscy9yZXF1ZXN0JzsKaW1wb3J0IGRlZmF1bHRTZXR0aW5ncyBmcm9tICdAL3NldHRpbmdzJzsKdmFyIHByZWZpeCA9IGRlZmF1bHRTZXR0aW5ncy5zZXJ2aWNlLnN5c3RlbSArICcvZWNtJzsKZXhwb3J0IHZhciBTeXNFY20gPSB7CiAgLy8g5o6l5Y+j5ZCN56ewCiAgcXVlcnk6IGZ1bmN0aW9uIHF1ZXJ5KGRhdGEpIHsKICAgIC8vIOafpeivogogICAgcmV0dXJuIHJlcXVlc3QoewogICAgICB1cmw6IHByZWZpeCArICcvcXVlcnkuZG8nLAogICAgICBtZXRob2Q6ICdnZXQnLAogICAgICBwYXJhbXM6IHsKICAgICAgICBtZXNzYWdlOiBkYXRhCiAgICAgIH0KICAgIH0pOwogIH0sCiAgYWRkOiBmdW5jdGlvbiBhZGQoZGF0YSkgewogICAgLy8g5paw5aKeCiAgICByZXR1cm4gcmVxdWVzdCh7CiAgICAgIHVybDogcHJlZml4ICsgJy9hZGQuZG8nLAogICAgICBtZXRob2Q6ICdwb3N0JywKICAgICAgZGF0YTogZGF0YQogICAgfSk7CiAgfSwKICB1cGRhdGU6IGZ1bmN0aW9uIHVwZGF0ZShkYXRhKSB7CiAgICAvLyDkv67mlLkKICAgIHJldHVybiByZXF1ZXN0KHsKICAgICAgdXJsOiBwcmVmaXggKyAnL21vZGlmeS5kbycsCiAgICAgIG1ldGhvZDogJ3Bvc3QnLAogICAgICBkYXRhOiBkYXRhCiAgICB9KTsKICB9LAogIGRlbDogZnVuY3Rpb24gZGVsKGRhdGEpIHsKICAgIC8vIOWIoOmZpAogICAgcmV0dXJuIHJlcXVlc3QoewogICAgICB1cmw6IHByZWZpeCArICcvZGVsZXRlLmRvJywKICAgICAgbWV0aG9kOiAnZGVsZXRlJywKICAgICAgZGF0YTogZGF0YQogICAgfSk7CiAgfQp9Ow=="}, {"version": 3, "names": ["request", "defaultSettings", "prefix", "service", "system", "SysEcm", "query", "data", "url", "method", "params", "message", "add", "update", "del"], "sources": ["E:/1_Project/XYD_Project/dop-4.0/dop-4.1/数字运营平台-统一门户工程/dopUnify/src/api/views/system/config/ecm/index.js"], "sourcesContent": ["import request from '@/utils/request'\r\nimport defaultSettings from '@/settings'\r\nconst prefix = defaultSettings.service.system + '/ecm'\r\n\r\nexport const SysEcm = {\r\n  // 接口名称\r\n  query(data) {\r\n    // 查询\r\n    return request({\r\n      url: prefix + '/query.do',\r\n      method: 'get',\r\n      params: { message: data }\r\n    })\r\n  },\r\n  add(data) {\r\n    // 新增\r\n    return request({\r\n      url: prefix + '/add.do',\r\n      method: 'post',\r\n      data\r\n    })\r\n  },\r\n  update(data) {\r\n    // 修改\r\n    return request({\r\n      url: prefix + '/modify.do',\r\n      method: 'post',\r\n      data\r\n    })\r\n  },\r\n  del(data) {\r\n    // 删除\r\n    return request({\r\n      url: prefix + '/delete.do',\r\n      method: 'delete',\r\n      data\r\n    })\r\n  }\r\n}\r\n"], "mappings": "AAAA,OAAOA,OAAO,MAAM,iBAAiB;AACrC,OAAOC,eAAe,MAAM,YAAY;AACxC,IAAMC,MAAM,GAAGD,eAAe,CAACE,OAAO,CAACC,MAAM,GAAG,MAAM;AAEtD,OAAO,IAAMC,MAAM,GAAG;EACpB;EACAC,KAAK,iBAACC,IAAI,EAAE;IACV;IACA,OAAOP,OAAO,CAAC;MACbQ,GAAG,EAAEN,MAAM,GAAG,WAAW;MACzBO,MAAM,EAAE,KAAK;MACbC,MAAM,EAAE;QAAEC,OAAO,EAAEJ;MAAK;IAC1B,CAAC,CAAC;EACJ,CAAC;EACDK,GAAG,eAACL,IAAI,EAAE;IACR;IACA,OAAOP,OAAO,CAAC;MACbQ,GAAG,EAAEN,MAAM,GAAG,SAAS;MACvBO,MAAM,EAAE,MAAM;MACdF,IAAI,EAAJA;IACF,CAAC,CAAC;EACJ,CAAC;EACDM,MAAM,kBAACN,IAAI,EAAE;IACX;IACA,OAAOP,OAAO,CAAC;MACbQ,GAAG,EAAEN,MAAM,GAAG,YAAY;MAC1BO,MAAM,EAAE,MAAM;MACdF,IAAI,EAAJA;IACF,CAAC,CAAC;EACJ,CAAC;EACDO,GAAG,eAACP,IAAI,EAAE;IACR;IACA,OAAOP,OAAO,CAAC;MACbQ,GAAG,EAAEN,MAAM,GAAG,YAAY;MAC1BO,MAAM,EAAE,QAAQ;MAChBF,IAAI,EAAJA;IACF,CAAC,CAAC;EACJ;AACF,CAAC"}]}