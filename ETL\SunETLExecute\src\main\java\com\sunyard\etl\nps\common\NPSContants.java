package com.sunyard.etl.nps.common;

import java.io.File;
import java.net.InetAddress;
import java.net.UnknownHostException;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.ExecutorService;

import com.sunyard.etl.nps.model.NpBusinessData;
import com.sunyard.etl.nps.service.base.ECMService;
import com.sunyard.etl.nps.service.base.FTPService;
import com.sunyard.etl.system.model.data.FlCheckoff;

public class NPSContants {
	// IP
	public static String IP_ADDRESS = null;

	// 后督5.x的批次状态
	// 98-结束
	// 10-等待自动识别
	// 20-等待人工补录
	// 30-等待业务审核
	// 40-等待核实（中心应用不读取）
	public static String ADMS5_PROCESS_FLAG = "98";
	public static String ADMS5_PROCESS_STATE = "100000";
	public static String IMAGE_FORMAT = "JPG";
	
	public static String ADMS5_PS_LEVEL_MAIN = "1"; // 主件标识
	public static String ADMS5_PS_LEVEL_ANNEX = "0"; // 附件标识
	
	
	/*
	 * 第一位：0=纯无纸化  1=混合无纸化
	 * 第二位：0=从ECM取  1=从FTP取
	 * 第三位：0=不需上传  1=需要上传
	 */
	public static String NP_SIMPLE_ECM = "000";
	public static String NP_SIMPLE_ECM_REUPLOAD = "001";
	public static String NP_SIMPLE_FTP = "011";	
	public static String NP_MIXED_ECM = "100";
	public static String NP_MIXED_ECM_REUPLOAD = "101";
	public static String NP_MIXED_FTP = "111";	
	public static String NP_ERROR_FORMNAME = "差错补扫件";
			
	
	
	public static String NP_TEMP_PATH = "D:"+File.separator + "temp";
	
	
	/*
	 * 无纸化参数改造
	 */
	public static final String NPS_BASE_LOG = "NPS_BASE_LOG";
	public static final String SET_ECM_KEYNAME = "ECM_SERVICE_ID";
	public static final String SET_FTP_KEYNAME = "FTP_SERVICE_ID";
	public static ECMService GLOBAL_ECM_SERVICE;
	public static FTPService GLOBAL_FTP_SERVICE;
	
	public static boolean SYSTEM_INIT = false; 
	
	static {
		try {
			IP_ADDRESS = InetAddress.getLocalHost().getHostAddress().toString();
		} catch (UnknownHostException e) {
			e.printStackTrace();
		}
	}
	
	/*
	 * 	多线程改造
	 */
    /* @Fields dataDaPool : TODO 数据归档线程池 */
	public static ExecutorService NPSPool = null;
	 /* @Fields maxServices : TODO 最大服务数 */
    public static int maxServices = 10;
    /* @Fields dataReadTime : TODO 数据读取时间频率 */
    public static int dataReadTime = 1;
    /* @Fields socketTimeOut : TODO socket超时时间 */
    public static int socketTimeOut = 0;
    /* @Fields daList : TODO 任务处理列表 */
    public static List<NpBusinessData> NPSList = new ArrayList<NpBusinessData>();
    
    
    public static String ADMS5 = "ADMS5";
    public static String DEFAULT_DATA_SOURCE_ID = "0";
    
}


