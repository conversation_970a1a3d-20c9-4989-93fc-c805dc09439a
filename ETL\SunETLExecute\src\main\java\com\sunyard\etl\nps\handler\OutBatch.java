package com.sunyard.etl.nps.handler;


import com.xxl.job.core.handler.annotation.JobHandler;

import org.springframework.stereotype.Service;

import com.sunyard.etl.nps.common.NPSContants;
import com.sunyard.etl.nps.common.SystemInit;
import com.sunyard.etl.nps.service.business.ImageQueryService;
import com.sunyard.etl.nps.service.business.OutBatchService;
import com.sunyard.etl.system.dao.JobParamDao;
import com.sunyard.etl.system.dao.impl.JobParamDaoImpl;
import com.sunyard.etl.system.model.JobParam;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.IJobHandler;
import com.xxl.job.core.log.XxlJobLogger;


@JobHandler(value="OutBatch",name = "输出批次到后督")
@Service
public  class OutBatch extends IJobHandler{
	private static final long serialVersionUID = 1L;
	private String tableName = "OutBatch";
	
	public ReturnT<String> execute(String jobId,String... params) throws Exception {
		
		JobParamDao jobParamDao = new JobParamDaoImpl();
		JobParam jobParam = jobParamDao.JobParam(Integer.parseInt(jobId));
		if( null == jobParam ){
			XxlJobLogger.log("JOBID:" + jobId + "未配置参数",tableName);
			return ReturnT.FAIL;	
		}
		OutBatchService ser = new OutBatchService(jobParam);
		String result = ser.outBatch();
		if(result.contains("FAIL")){
			XxlJobLogger.log("输出批次失败：" + result,tableName);
			return ReturnT.FAIL;
		}
		XxlJobLogger.log("本次任务完成",tableName);
		return ReturnT.SUCCESS;	
	}

	
	public static void main(String[] args) {
		OutBatch a = new OutBatch();
		try {
			a.execute("113", "");
		} catch (Exception e) {
			e.printStackTrace();
		}
	}
}
