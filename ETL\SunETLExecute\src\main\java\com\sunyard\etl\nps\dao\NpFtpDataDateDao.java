package com.sunyard.etl.nps.dao;

import java.sql.SQLException;
import java.util.ArrayList;
import java.util.List;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.sunyard.util.dbutil.DBHandler;

import com.sun.rowset.CachedRowSetImpl;
import com.sunyard.etl.system.common.Constants;
import com.xxl.job.core.log.XxlJobLogger;

public class NpFtpDataDateDao {

	protected final Logger log = LoggerFactory.getLogger(getClass());



	public List<String> getNpFtpDataDate(String dataSourceId) throws SQLException {
		List<String> list = new ArrayList<String>();
		DBHandler dbHandler = new DBHandler(Constants.CONN_POOL_NAME);
		CachedRowSetImpl rs = null;
		String sql = "SELECT T.OCCUR_DATE  FROM  NP_FTP_DATA_DATE  T WHERE  T.FLAG = '0'  ORDER BY OCCUR_DATE";
		XxlJobLogger.log("根据批次信息从无纸化业务表中取得业务信息:" + sql);
		rs = dbHandler.queryRs(sql);
		while (rs.next()) {
			list.add(rs.getString(1));
		}
		return list;
	}
	
	

	public boolean updateFTPDataDate(String FTPDataDate, String dataSourceId)
			throws SQLException {
		String sql = "UPDATE NP_FTP_DATA_DATE T SET T.FLAG = '1' WHERE T.OCCUR_DATE = ?  ";
		DBHandler dbHandler = new DBHandler(Constants.CONN_POOL_NAME);
		int result = dbHandler.execute(sql, FTPDataDate);
		if (result < 0) {
			return false;
		} else {
			return true;
		}
	}
	
	
	public boolean finish(String FTPDataDate, String dataSourceId)	throws SQLException {
		String sql = "INSERT INTO NP_FTP_DATA_DATE T VALUES(?,?,'1')";
		DBHandler dbHandler = new DBHandler(Constants.CONN_POOL_NAME);
		int result = dbHandler.execute(sql, FTPDataDate, dataSourceId);
		if (result < 0) {
			return false;
		} else {
			return true;
		}
	}
	

	public boolean isFinish(String occurDate) throws SQLException {
		DBHandler dbHandler = new DBHandler(Constants.CONN_POOL_NAME);
		CachedRowSetImpl rs = null;
		String sql = "SELECT * FROM NP_FTP_DATA_DATE T  WHERE  T.OCCUR_DATE = ? AND T.FLAG = '1'";
		XxlJobLogger.log("根据批次信息从无纸化业务表中取得业务信息:" + sql);
		rs = dbHandler.queryRs(sql, occurDate);
		while (rs.next()) {
			return true;
		}
		return false;
	}
	
	
}
