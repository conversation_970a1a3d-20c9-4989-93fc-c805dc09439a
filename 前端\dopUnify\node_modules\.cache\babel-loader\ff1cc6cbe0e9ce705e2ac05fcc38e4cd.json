{"remainingRequest": "E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan\\数字运营平台-统一门户工程\\dopUnify\\node_modules\\babel-loader\\lib\\index.js!E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan\\数字运营平台-统一门户工程\\dopUnify\\node_modules\\sunui\\src\\directive\\el-drag-dialog\\drag.js", "dependencies": [{"path": "E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-<PERSON><PERSON><PERSON>n\\数字运营平台-统一门户工程\\dopUnify\\node_modules\\sunui\\src\\directive\\el-drag-dialog\\drag.js", "mtime": 499162500000}, {"path": "E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-<PERSON><PERSON><PERSON>n\\数字运营平台-统一门户工程\\dopUnify\\babel.config.js", "mtime": 1686019818359}, {"path": "E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-<PERSON><PERSON><PERSON>n\\数字运营平台-统一门户工程\\dopUnify\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-<PERSON><PERSON><PERSON>n\\数字运营平台-统一门户工程\\dopUnify\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["bind", "el", "binding", "vnode", "dialogHeaderEl", "querySelector", "dragDom", "style", "cssText", "getStyle", "window", "document", "currentStyle", "dom", "attr", "getComputedStyle", "onmousedown", "e", "disX", "clientX", "offsetLeft", "disY", "clientY", "offsetTop", "dragD<PERSON><PERSON><PERSON>th", "offsetWidth", "dragDomHeight", "offsetHeight", "screenWidth", "body", "clientWidth", "screenHeight", "clientHeight", "minDragDomLeft", "maxDragDomLeft", "minDragDomTop", "maxDragDomTop", "styL", "styT", "includes", "replace", "<PERSON><PERSON><PERSON><PERSON>", "left", "top", "child", "$emit", "onmouseup"], "sources": ["E:/1_Project/XYD_Project/dop-4.0/dop-4.1-q<PERSON><PERSON>n/数字运营平台-统一门户工程/dopUnify/node_modules/sunui/src/directive/el-drag-dialog/drag.js"], "sourcesContent": ["export default {\n  bind(el, binding, vnode) {\n    const dialogHeaderEl = el.querySelector('.el-dialog__header')\n    const dragDom = el.querySelector('.el-dialog')\n    dialogHeaderEl.style.cssText += ';cursor:move;'\n    dragDom.style.cssText += ';top:0px;'\n\n    // 获取原有属性 ie dom元素.currentStyle 火狐谷歌 window.getComputedStyle(dom元素, null);\n    const getStyle = (function() {\n      if (window.document.currentStyle) {\n        return (dom, attr) => dom.currentStyle[attr]\n      } else {\n        return (dom, attr) => getComputedStyle(dom, false)[attr]\n      }\n    })()\n\n    dialogHeaderEl.onmousedown = (e) => {\n      // 鼠标按下，计算当前元素距离可视区的距离\n      const disX = e.clientX - dialogHeaderEl.offsetLeft\n      const disY = e.clientY - dialogHeaderEl.offsetTop\n\n      const dragDomWidth = dragDom.offsetWidth\n      const dragDomHeight = dragDom.offsetHeight\n\n      const screenWidth = document.body.clientWidth\n      const screenHeight = document.body.clientHeight\n\n      const minDragDomLeft = dragDom.offsetLeft\n      const maxDragDomLeft = screenWidth - dragDom.offsetLeft - dragDomWidth\n\n      const minDragDomTop = dragDom.offsetTop\n      const maxDragDomTop = screenHeight - dragDom.offsetTop - dragDomHeight\n\n      // 获取到的值带px 正则匹配替换\n      let styL = getStyle(dragDom, 'left')\n      let styT = getStyle(dragDom, 'top')\n\n      if (styL.includes('%')) {\n        styL = +document.body.clientWidth * (+styL.replace(/\\%/g, '') / 100)\n        styT = +document.body.clientHeight * (+styT.replace(/\\%/g, '') / 100)\n      } else {\n        styL = +styL.replace(/\\px/g, '')\n        styT = +styT.replace(/\\px/g, '')\n      }\n\n      document.onmousemove = function(e) {\n        // 通过事件委托，计算移动的距离\n        let left = e.clientX - disX\n        let top = e.clientY - disY\n\n        // 边界处理\n        if (-(left) > minDragDomLeft) {\n          left = -minDragDomLeft\n        } else if (left > maxDragDomLeft) {\n          left = maxDragDomLeft\n        }\n\n        if (-(top) > minDragDomTop) {\n          top = -minDragDomTop\n        } else if (top > maxDragDomTop) {\n          top = maxDragDomTop\n        }\n\n        // 移动当前元素\n        dragDom.style.cssText += `;left:${left + styL}px;top:${top + styT}px;`\n\n        // emit onDrag event\n        vnode.child.$emit('dragDialog')\n      }\n\n      document.onmouseup = function(e) {\n        document.onmousemove = null\n        document.onmouseup = null\n      }\n    }\n  }\n}\n"], "mappings": ";;;;;AAAA,eAAe;EACbA,IAAI,gBAACC,EAAE,EAAEC,OAAO,EAAEC,KAAK,EAAE;IACvB,IAAMC,cAAc,GAAGH,EAAE,CAACI,aAAa,CAAC,oBAAoB,CAAC;IAC7D,IAAMC,OAAO,GAAGL,EAAE,CAACI,aAAa,CAAC,YAAY,CAAC;IAC9CD,cAAc,CAACG,KAAK,CAACC,OAAO,IAAI,eAAe;IAC/CF,OAAO,CAACC,KAAK,CAACC,OAAO,IAAI,WAAW;;IAEpC;IACA,IAAMC,QAAQ,GAAI,YAAW;MAC3B,IAAIC,MAAM,CAACC,QAAQ,CAACC,YAAY,EAAE;QAChC,OAAO,UAACC,GAAG,EAAEC,IAAI;UAAA,OAAKD,GAAG,CAACD,YAAY,CAACE,IAAI,CAAC;QAAA;MAC9C,CAAC,MAAM;QACL,OAAO,UAACD,GAAG,EAAEC,IAAI;UAAA,OAAKC,gBAAgB,CAACF,GAAG,EAAE,KAAK,CAAC,CAACC,IAAI,CAAC;QAAA;MAC1D;IACF,CAAC,EAAG;IAEJV,cAAc,CAACY,WAAW,GAAG,UAACC,CAAC,EAAK;MAClC;MACA,IAAMC,IAAI,GAAGD,CAAC,CAACE,OAAO,GAAGf,cAAc,CAACgB,UAAU;MAClD,IAAMC,IAAI,GAAGJ,CAAC,CAACK,OAAO,GAAGlB,cAAc,CAACmB,SAAS;MAEjD,IAAMC,YAAY,GAAGlB,OAAO,CAACmB,WAAW;MACxC,IAAMC,aAAa,GAAGpB,OAAO,CAACqB,YAAY;MAE1C,IAAMC,WAAW,GAAGjB,QAAQ,CAACkB,IAAI,CAACC,WAAW;MAC7C,IAAMC,YAAY,GAAGpB,QAAQ,CAACkB,IAAI,CAACG,YAAY;MAE/C,IAAMC,cAAc,GAAG3B,OAAO,CAACc,UAAU;MACzC,IAAMc,cAAc,GAAGN,WAAW,GAAGtB,OAAO,CAACc,UAAU,GAAGI,YAAY;MAEtE,IAAMW,aAAa,GAAG7B,OAAO,CAACiB,SAAS;MACvC,IAAMa,aAAa,GAAGL,YAAY,GAAGzB,OAAO,CAACiB,SAAS,GAAGG,aAAa;;MAEtE;MACA,IAAIW,IAAI,GAAG5B,QAAQ,CAACH,OAAO,EAAE,MAAM,CAAC;MACpC,IAAIgC,IAAI,GAAG7B,QAAQ,CAACH,OAAO,EAAE,KAAK,CAAC;MAEnC,IAAI+B,IAAI,CAACE,QAAQ,CAAC,GAAG,CAAC,EAAE;QACtBF,IAAI,GAAG,CAAC1B,QAAQ,CAACkB,IAAI,CAACC,WAAW,IAAI,CAACO,IAAI,CAACG,OAAO,CAAC,KAAK,EAAE,EAAE,CAAC,GAAG,GAAG,CAAC;QACpEF,IAAI,GAAG,CAAC3B,QAAQ,CAACkB,IAAI,CAACG,YAAY,IAAI,CAACM,IAAI,CAACE,OAAO,CAAC,KAAK,EAAE,EAAE,CAAC,GAAG,GAAG,CAAC;MACvE,CAAC,MAAM;QACLH,IAAI,GAAG,CAACA,IAAI,CAACG,OAAO,CAAC,MAAM,EAAE,EAAE,CAAC;QAChCF,IAAI,GAAG,CAACA,IAAI,CAACE,OAAO,CAAC,MAAM,EAAE,EAAE,CAAC;MAClC;MAEA7B,QAAQ,CAAC8B,WAAW,GAAG,UAASxB,CAAC,EAAE;QACjC;QACA,IAAIyB,IAAI,GAAGzB,CAAC,CAACE,OAAO,GAAGD,IAAI;QAC3B,IAAIyB,GAAG,GAAG1B,CAAC,CAACK,OAAO,GAAGD,IAAI;;QAE1B;QACA,IAAI,CAAEqB,IAAK,GAAGT,cAAc,EAAE;UAC5BS,IAAI,GAAG,CAACT,cAAc;QACxB,CAAC,MAAM,IAAIS,IAAI,GAAGR,cAAc,EAAE;UAChCQ,IAAI,GAAGR,cAAc;QACvB;QAEA,IAAI,CAAES,GAAI,GAAGR,aAAa,EAAE;UAC1BQ,GAAG,GAAG,CAACR,aAAa;QACtB,CAAC,MAAM,IAAIQ,GAAG,GAAGP,aAAa,EAAE;UAC9BO,GAAG,GAAGP,aAAa;QACrB;;QAEA;QACA9B,OAAO,CAACC,KAAK,CAACC,OAAO,oBAAakC,IAAI,GAAGL,IAAI,oBAAUM,GAAG,GAAGL,IAAI,QAAK;;QAEtE;QACAnC,KAAK,CAACyC,KAAK,CAACC,KAAK,CAAC,YAAY,CAAC;MACjC,CAAC;MAEDlC,QAAQ,CAACmC,SAAS,GAAG,UAAS7B,CAAC,EAAE;QAC/BN,QAAQ,CAAC8B,WAAW,GAAG,IAAI;QAC3B9B,QAAQ,CAACmC,SAAS,GAAG,IAAI;MAC3B,CAAC;IACH,CAAC;EACH;AACF,CAAC"}]}