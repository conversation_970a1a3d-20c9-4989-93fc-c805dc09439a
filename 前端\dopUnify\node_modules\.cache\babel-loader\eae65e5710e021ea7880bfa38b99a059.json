{"remainingRequest": "D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\node_modules\\thread-loader\\dist\\cjs.js!D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\node_modules\\babel-loader\\lib\\index.js!D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\src\\views\\system\\outManage\\definition\\component\\table\\index.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\src\\views\\system\\outManage\\definition\\component\\table\\index.vue", "mtime": 1686019809185}, {"path": "D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\babel.config.js", "mtime": 1686019818359}, {"path": "D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\.babelrc", "mtime": 1705285078668}, {"path": "D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\node_modules\\thread-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1655715099000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, null]}