{"remainingRequest": "D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\node_modules\\thread-loader\\dist\\cjs.js!D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\node_modules\\babel-loader\\lib\\index.js!D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\src\\components\\Dialog\\SunNoticeDialog\\index.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\src\\components\\Dialog\\SunNoticeDialog\\index.vue", "mtime": 1703583638284}, {"path": "D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\babel.config.js", "mtime": 1686019818359}, {"path": "D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\.babelrc", "mtime": 1705285078668}, {"path": "D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\node_modules\\thread-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1655715099000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, null]}