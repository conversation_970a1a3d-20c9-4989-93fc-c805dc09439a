{"remainingRequest": "E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\node_modules\\babel-loader\\lib\\index.js!E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\src\\views\\system\\externalManage\\post\\index.vue?vue&type=script&lang=js&", "dependencies": [{"path": "E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\src\\views\\system\\externalManage\\post\\index.vue", "mtime": 1686019808935}, {"path": "E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\babel.config.js", "mtime": 1686019818359}, {"path": "E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\.babelrc", "mtime": 1705285078668}, {"path": "E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1655715099000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;AA2BA;AACA;AACA;;AAEA;EACAA;EACAC;IAAAC;EAAA;EACAC;IACA;MACAC;MACAC;QACAC;QACAC;MACA;MACAC;QACA;QACAC;QACAC;QACAC;QACAC;MACA;MACAC;MAAA;MACAC;IACA;EACA;EACAC;IACA;EACA;EACAC;IAAA;IACA;MACA;MACA;QACA;MACA;IACA;EACA;EACAC;IACA;AACA;IACAC;MACA;IACA;IACA;AACA;IACAC;MAAA;MACA;MACA,YACAC,yDACAC;QACA;QACA;QACAC;UACA;YACAC;UACA;UACAC;QACA;QACA;QACA;MACA;;MAEA;MACA;QACA;QACA;QACAF;UACA;YACAC;UACA;UACAT;QACA;QACA;QACA;MACA;IACA;IACA;AACA;AACA;IACAW;MACA;QACA;MACA;QACA;MACA;IACA;IACA;AACA;IACAC;MACA;IACA;EACA;AACA", "names": ["name", "components", "TableList", "data", "config", "defaultForm", "post_no", "external_system_no", "btnAll", "btnQuery", "btnAdd", "btnDelete", "btnModify", "systemArray", "postNoArr", "created", "mounted", "methods", "btnPermissions", "getDispatch", "dispatch", "then", "res", "label", "SystemArray", "validateForm", "queryList"], "sourceRoot": "src/views/system/externalManage/post", "sources": ["index.vue"], "sourcesContent": ["<!--\r\n* 关联系统运营岗位配置\r\n-->\r\n<template>\r\n  <div class=\"app-container\">\r\n    <div class=\"sun-content\">\r\n      <div class=\"filter-container\">\r\n        <sun-form\r\n          ref=\"formRef\"\r\n          :config=\"config\"\r\n          :default-form=\"defaultForm\"\r\n          :query=\"btnAll.btnQuery\"\r\n          @query=\"queryList\"\r\n          @validateForm=\"validateForm\"\r\n        />\r\n      </div>\r\n    </div>\r\n    <table-list\r\n      ref=\"tableListRef\"\r\n      :default-form=\"defaultForm\"\r\n      :btn-all=\"btnAll\"\r\n      :system-array=\"systemArray\"\r\n      :post-no-arr=\"postNoArr\"\r\n    />\r\n  </div>\r\n</template>\r\n<script>\r\nimport { config } from './info' // 表单配置\r\nimport TableList from './component/table' // 表格\r\nimport { permissionsBtn } from '@/utils/permissions' // 权限配置\r\n\r\nexport default {\r\n  name: 'ManagePost',\r\n  components: { TableList },\r\n  data() {\r\n    return {\r\n      config: config(this),\r\n      defaultForm: {\r\n        post_no: [],\r\n        external_system_no: ''\r\n      },\r\n      btnAll: {\r\n        // 当前页需要配置权限的按钮  权限获取\r\n        btnQuery: false,\r\n        btnAdd: true,\r\n        btnDelete: true,\r\n        btnModify: true\r\n      },\r\n      systemArray: [], // 处理系统编号\r\n      postNoArr: [] // 处理运营岗位\r\n    }\r\n  },\r\n  created() {\r\n    this.btnPermissions()\r\n  },\r\n  mounted() {\r\n    this.$nextTick().then(() => {\r\n      this.getDispatch()\r\n      if (this.btnAll.btnQuery) {\r\n        this.queryList()\r\n      }\r\n    })\r\n  },\r\n  methods: {\r\n    /**\r\n     * 按钮权限配置*/\r\n    btnPermissions() {\r\n      this.btnAll = permissionsBtn(this.$attrs.button_id, this.btnAll)\r\n    },\r\n    /**\r\n     * 获取外部字典*/\r\n    getDispatch() {\r\n      // 处理系统编号\r\n      this.$store\r\n        .dispatch('common/setExternalData', 'EXTERNAL_SYSTEM_NO')\r\n        .then((res) => {\r\n          // const arrayState = res\r\n          const SystemArray = []\r\n          res.map(function(item) {\r\n            const valueS = Object.assign({}, item, {\r\n              label: item.value + '-' + item.label\r\n            })\r\n            SystemArray.push(valueS)\r\n          })\r\n          this.config.external_system_no.options = SystemArray\r\n          this.systemArray = SystemArray\r\n        })\r\n\r\n      // 处理运营岗位\r\n      this.$store.dispatch('common/setExternalData', 'POST_NO').then((res) => {\r\n        // const postNo = res\r\n        const postNoArr = []\r\n        res.map(function(item) {\r\n          const valueS = Object.assign({}, item, {\r\n            label: item.value + '-' + item.label\r\n          })\r\n          postNoArr.push(valueS)\r\n        })\r\n        this.config.post_no.options = postNoArr\r\n        this.postNoArr = postNoArr\r\n      })\r\n    },\r\n    /**\r\n     * 表单校验\r\n     * @param {Boolean}valid 校验返回值*/\r\n    validateForm(valid) {\r\n      if (valid) {\r\n        this.$refs.tableListRef.queryList(1)\r\n      } else {\r\n        return false\r\n      }\r\n    },\r\n    /**\r\n     * 按钮：查询*/\r\n    queryList() {\r\n      this.$refs['formRef'].validateForm()\r\n    }\r\n  }\r\n}\r\n</script>\r\n<style scoped>\r\n.app-container {\r\n  width: 100%;\r\n  height: 100%;\r\n}\r\n</style>\r\n"]}]}