{"remainingRequest": "D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\node_modules\\babel-loader\\lib\\index.js!D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\src\\layout\\components\\AppMain.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\src\\layout\\components\\AppMain.vue", "mtime": 1686019810388}, {"path": "D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\babel.config.js", "mtime": 1686019818359}, {"path": "D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1655715099000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:aW1wb3J0ICJjb3JlLWpzL21vZHVsZXMvZXMuZnVuY3Rpb24ubmFtZS5qcyI7Ci8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCgppbXBvcnQgRXh0ZW5kIGZyb20gJy4uL2V4dGVuZCc7CmltcG9ydCBkZWZhdWx0U2V0dGluZ3MgZnJvbSAnQC9zZXR0aW5ncyc7CmV4cG9ydCBkZWZhdWx0IHsKICBuYW1lOiAnQXBwTWFpbicsCiAgY29tcG9uZW50czogewogICAgRXh0ZW5kOiBFeHRlbmQKICB9LAogIGRhdGE6IGZ1bmN0aW9uIGRhdGEoKSB7CiAgICByZXR1cm4gewogICAgICBjb21wQXJyOiBbXSwKICAgICAgLy8g5b2T5YmN5omA5pyJ5omT5byA55qE5aSW6YOo5o6l5YWl57O757ufCiAgICAgIG1lbnVEaXI6IGRlZmF1bHRTZXR0aW5ncy5tZW51RGlyLAogICAgICBwcm9OYW1lOiBkZWZhdWx0U2V0dGluZ3MubmFtZSwKICAgICAgcHJvY2VzczogZGVmYXVsdFNldHRpbmdzLnByb2Nlc3MgLy8g6YWN572u546v5aKDCiAgICB9OwogIH0sCgogIGNvbXB1dGVkOiB7CiAgICBjYWNoZWRWaWV3czogZnVuY3Rpb24gY2FjaGVkVmlld3MoKSB7CiAgICAgIHJldHVybiB0aGlzLiRzdG9yZS5zdGF0ZS50YWdzVmlldy5jYWNoZWRWaWV3czsKICAgIH0sCiAgICBrZXk6IGZ1bmN0aW9uIGtleSgpIHsKICAgICAgcmV0dXJuIHRoaXMuJHJvdXRlLnBhdGg7CiAgICB9LAogICAgaWZyYW1lVmlld3M6IGZ1bmN0aW9uIGlmcmFtZVZpZXdzKCkgewogICAgICByZXR1cm4gdGhpcy4kc3RvcmUuc3RhdGUudGFnc1ZpZXcuaWZyYW1lVmlld3M7IC8vIOaJgOaciWlmcmFtZSDnmoTkv6Hmga/lrZjlgqgKICAgIH0sCiAgICBwYXJhbTogZnVuY3Rpb24gcGFyYW0oKSB7CiAgICAgIC8vIOS/ruWkjSDlnKjpobbpg6joj5zljZXpgInpobnljaEg5Y+z6ZSu5Yi35paw5pe277yM5oql6ZSZIC0tc3RhcnQKICAgICAgaWYgKCF0aGlzLiRyb3V0ZS5uYW1lKSB7CiAgICAgICAgcmV0dXJuIHsKICAgICAgICAgIG5hbWU6ICdyZWRpcmVjdCcKICAgICAgICB9OwogICAgICB9CiAgICAgIC8vIC0tZW5kCiAgICAgIHJldHVybiB0aGlzLiRyb3V0ZTsKICAgIH0KICB9Cn07"}, {"version": 3, "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA+BA;AACA;AACA;EACAA;EACAC;IAAAC;EAAA;EACAC;IACA;MACAC;MAAA;MACAC;MACAC;MACAC;IACA;EACA;;EACAC;IACAC;MACA;IACA;IACAC;MACA;IACA;IACAC;MACA;IACA;IACAC;MACA;MACA;QACA;UACAZ;QACA;MACA;MACA;MACA;IACA;EACA;AACA", "names": ["name", "components", "Extend", "data", "compArr", "menuDir", "proName", "process", "computed", "cachedViews", "key", "iframeViews", "param"], "sourceRoot": "src/layout/components", "sources": ["AppMain.vue"], "sourcesContent": ["<!--父页面配置-->\n<template>\n  <section\n    class=\"app-main\"\n    :class=\"{\n      'app-main-produce': proName !== 'dopUnify' && process === 'production',\n      'top-menu': menuDir === 'top'\n    }\"\n  >\n    <!-- 判断是否的门户工程 -->\n    <template v-if=\"proName === 'dopUnify'\">\n      <div v-for=\"item in iframeViews\" :key=\"item.path\">\n        <!--iframe 外部接入系统-->\n        <extend\n          v-show=\"param.name.indexOf('Extend') > -1 && key === item.path\"\n        />\n      </div>\n    </template>\n    <transition name=\"fade-transform\" mode=\"out-in\">\n      <keep-alive :include=\"cachedViews\">\n        <router-view\n          v-if=\"param.name.indexOf('Extend') === -1\"\n          :key=\"key\"\n          ref=\"routerV\"\n        />\n      </keep-alive>\n    </transition>\n  </section>\n</template>\n\n<script>\nimport Extend from '../extend'\nimport defaultSettings from '@/settings'\nexport default {\n  name: 'AppMain',\n  components: { Extend },\n  data() {\n    return {\n      compArr: [], // 当前所有打开的外部接入系统\n      menuDir: defaultSettings.menuDir,\n      proName: defaultSettings.name,\n      process: defaultSettings.process // 配置环境\n    }\n  },\n  computed: {\n    cachedViews() {\n      return this.$store.state.tagsView.cachedViews\n    },\n    key() {\n      return this.$route.path\n    },\n    iframeViews() {\n      return this.$store.state.tagsView.iframeViews // 所有iframe 的信息存储\n    },\n    param() {\n      // 修复 在顶部菜单选项卡 右键刷新时，报错 --start\n      if (!this.$route.name) {\n        return {\n          name: 'redirect'\n        }\n      }\n      // --end\n      return this.$route\n    }\n  }\n}\n</script>\n\n<style lang=\"scss\" scoped>\n@import '~@/assets/scss/common/variable/variables.scss';\n\n.app-main {\n  height: calc(100vh - #{$topHeight});\n  width: 100%;\n  position: relative;\n  overflow: auto;\n  background: $pro-bgc;\n  &.top-menu {\n    height: calc(100vh - #{$topHeight} - #{$topMenuHeight});\n  }\n}\n// 生产环境：无$tabHeight、$topHeight\n.app-main-produce {\n  height: 100vh;\n}\n\n// .fixed-header+.app-main {\n//   padding-top: 50px;\n// }\n\n// .hasTagsView {\n//   .app-main {\n//     /* 84 = navbar + tags-view = 50 + 34 */\n//     min-height: calc(100vh - 8.4rem);\n//   }\n\n//   .fixed-header+.app-main {\n//     padding-top: 8.4rem;\n//   }\n// }\n</style>\n\n<style lang=\"scss\">\n// fix css style bug in open el-dialog\n.el-popup-parent--hidden {\n  .fixed-header {\n    padding-right: 1.5rem;\n  }\n}\n</style>\n"]}]}