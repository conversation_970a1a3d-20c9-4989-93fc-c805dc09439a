{"remainingRequest": "E:\\华南区项目\\中国银行\\开发区\\科技公司运营平台\\前端\\dopUnify\\node_modules\\thread-loader\\dist\\cjs.js!E:\\华南区项目\\中国银行\\开发区\\科技公司运营平台\\前端\\dopUnify\\node_modules\\babel-loader\\lib\\index.js!E:\\华南区项目\\中国银行\\开发区\\科技公司运营平台\\前端\\dopUnify\\node_modules\\js-base64\\base64.mjs", "dependencies": [{"path": "E:\\华南区项目\\中国银行\\开发区\\科技公司运营平台\\前端\\dopUnify\\node_modules\\js-base64\\base64.mjs", "mtime": 499162500000}, {"path": "E:\\华南区项目\\中国银行\\开发区\\科技公司运营平台\\前端\\dopUnify\\babel.config.js", "mtime": 1721631954662}, {"path": "E:\\华南区项目\\中国银行\\开发区\\科技公司运营平台\\前端\\dopUnify\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "E:\\华南区项目\\中国银行\\开发区\\科技公司运营平台\\前端\\dopUnify\\node_modules\\thread-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "E:\\华南区项目\\中国银行\\开发区\\科技公司运营平台\\前端\\dopUnify\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:aW1wb3J0ICJjb3JlLWpzL21vZHVsZXMvZXMuYXJyYXkuc2xpY2UuanMiOwppbXBvcnQgImNvcmUtanMvbW9kdWxlcy9lcy5vYmplY3QudG8tc3RyaW5nLmpzIjsKaW1wb3J0ICJjb3JlLWpzL21vZHVsZXMvd2ViLmRvbS1jb2xsZWN0aW9ucy5mb3ItZWFjaC5qcyI7CmltcG9ydCAiY29yZS1qcy9tb2R1bGVzL2VzLnR5cGVkLWFycmF5LmZyb20uanMiOwppbXBvcnQgImNvcmUtanMvbW9kdWxlcy9lcy5hcnJheS1idWZmZXIuc2xpY2UuanMiOwppbXBvcnQgImNvcmUtanMvbW9kdWxlcy9lcy50eXBlZC1hcnJheS51aW50OC1hcnJheS5qcyI7CmltcG9ydCAiY29yZS1qcy9tb2R1bGVzL2VzLnR5cGVkLWFycmF5LmNvcHktd2l0aGluLmpzIjsKaW1wb3J0ICJjb3JlLWpzL21vZHVsZXMvZXMudHlwZWQtYXJyYXkuZXZlcnkuanMiOwppbXBvcnQgImNvcmUtanMvbW9kdWxlcy9lcy50eXBlZC1hcnJheS5maWxsLmpzIjsKaW1wb3J0ICJjb3JlLWpzL21vZHVsZXMvZXMudHlwZWQtYXJyYXkuZmlsdGVyLmpzIjsKaW1wb3J0ICJjb3JlLWpzL21vZHVsZXMvZXMudHlwZWQtYXJyYXkuZmluZC5qcyI7CmltcG9ydCAiY29yZS1qcy9tb2R1bGVzL2VzLnR5cGVkLWFycmF5LmZpbmQtaW5kZXguanMiOwppbXBvcnQgImNvcmUtanMvbW9kdWxlcy9lcy50eXBlZC1hcnJheS5mb3ItZWFjaC5qcyI7CmltcG9ydCAiY29yZS1qcy9tb2R1bGVzL2VzLnR5cGVkLWFycmF5LmluY2x1ZGVzLmpzIjsKaW1wb3J0ICJjb3JlLWpzL21vZHVsZXMvZXMudHlwZWQtYXJyYXkuaW5kZXgtb2YuanMiOwppbXBvcnQgImNvcmUtanMvbW9kdWxlcy9lcy50eXBlZC1hcnJheS5pdGVyYXRvci5qcyI7CmltcG9ydCAiY29yZS1qcy9tb2R1bGVzL2VzLnR5cGVkLWFycmF5LmpvaW4uanMiOwppbXBvcnQgImNvcmUtanMvbW9kdWxlcy9lcy50eXBlZC1hcnJheS5sYXN0LWluZGV4LW9mLmpzIjsKaW1wb3J0ICJjb3JlLWpzL21vZHVsZXMvZXMudHlwZWQtYXJyYXkubWFwLmpzIjsKaW1wb3J0ICJjb3JlLWpzL21vZHVsZXMvZXMudHlwZWQtYXJyYXkucmVkdWNlLmpzIjsKaW1wb3J0ICJjb3JlLWpzL21vZHVsZXMvZXMudHlwZWQtYXJyYXkucmVkdWNlLXJpZ2h0LmpzIjsKaW1wb3J0ICJjb3JlLWpzL21vZHVsZXMvZXMudHlwZWQtYXJyYXkucmV2ZXJzZS5qcyI7CmltcG9ydCAiY29yZS1qcy9tb2R1bGVzL2VzLnR5cGVkLWFycmF5LnNldC5qcyI7CmltcG9ydCAiY29yZS1qcy9tb2R1bGVzL2VzLnR5cGVkLWFycmF5LnNsaWNlLmpzIjsKaW1wb3J0ICJjb3JlLWpzL21vZHVsZXMvZXMudHlwZWQtYXJyYXkuc29tZS5qcyI7CmltcG9ydCAiY29yZS1qcy9tb2R1bGVzL2VzLnR5cGVkLWFycmF5LnNvcnQuanMiOwppbXBvcnQgImNvcmUtanMvbW9kdWxlcy9lcy50eXBlZC1hcnJheS5zdWJhcnJheS5qcyI7CmltcG9ydCAiY29yZS1qcy9tb2R1bGVzL2VzLnR5cGVkLWFycmF5LnRvLWxvY2FsZS1zdHJpbmcuanMiOwppbXBvcnQgImNvcmUtanMvbW9kdWxlcy9lcy50eXBlZC1hcnJheS50by1zdHJpbmcuanMiOwppbXBvcnQgImNvcmUtanMvbW9kdWxlcy9lcy5hcnJheS5tYXAuanMiOwppbXBvcnQgImNvcmUtanMvbW9kdWxlcy9lcy5yZWdleHAuZXhlYy5qcyI7CmltcG9ydCAiY29yZS1qcy9tb2R1bGVzL2VzLnN0cmluZy5yZXBsYWNlLmpzIjsKaW1wb3J0ICJjb3JlLWpzL21vZHVsZXMvZXMucmVnZXhwLnRvLXN0cmluZy5qcyI7CmltcG9ydCAiY29yZS1qcy9tb2R1bGVzL2VzLmFycmF5LmpvaW4uanMiOwppbXBvcnQgImNvcmUtanMvbW9kdWxlcy9lcy5yZWdleHAudGVzdC5qcyI7Ci8qKgogKiAgYmFzZTY0LnRzCiAqCiAqICBMaWNlbnNlZCB1bmRlciB0aGUgQlNEIDMtQ2xhdXNlIExpY2Vuc2UuCiAqICAgIGh0dHA6Ly9vcGVuc291cmNlLm9yZy9saWNlbnNlcy9CU0QtMy1DbGF1c2UKICoKICogIFJlZmVyZW5jZXM6CiAqICAgIGh0dHA6Ly9lbi53aWtpcGVkaWEub3JnL3dpa2kvQmFzZTY0CiAqCiAqIEBhdXRob3IgRGFuIEtvZ2FpIChodHRwczovL2dpdGh1Yi5jb20vZGFua29nYWkpCiAqLwp2YXIgdmVyc2lvbiA9ICczLjcuMyc7Ci8qKgogKiBAZGVwcmVjYXRlZCB1c2UgbG93ZXJjYXNlIGB2ZXJzaW9uYC4KICovCnZhciBWRVJTSU9OID0gdmVyc2lvbjsKdmFyIF9oYXNhdG9iID0gdHlwZW9mIGF0b2IgPT09ICdmdW5jdGlvbic7CnZhciBfaGFzYnRvYSA9IHR5cGVvZiBidG9hID09PSAnZnVuY3Rpb24nOwp2YXIgX2hhc0J1ZmZlciA9IHR5cGVvZiBCdWZmZXIgPT09ICdmdW5jdGlvbic7CnZhciBfVEQgPSB0eXBlb2YgVGV4dERlY29kZXIgPT09ICdmdW5jdGlvbicgPyBuZXcgVGV4dERlY29kZXIoKSA6IHVuZGVmaW5lZDsKdmFyIF9URSA9IHR5cGVvZiBUZXh0RW5jb2RlciA9PT0gJ2Z1bmN0aW9uJyA/IG5ldyBUZXh0RW5jb2RlcigpIDogdW5kZWZpbmVkOwp2YXIgYjY0Y2ggPSAnQUJDREVGR0hJSktMTU5PUFFSU1RVVldYWVphYmNkZWZnaGlqa2xtbm9wcXJzdHV2d3h5ejAxMjM0NTY3ODkrLz0nOwp2YXIgYjY0Y2hzID0gQXJyYXkucHJvdG90eXBlLnNsaWNlLmNhbGwoYjY0Y2gpOwp2YXIgYjY0dGFiID0gZnVuY3Rpb24gKGEpIHsKICB2YXIgdGFiID0ge307CiAgYS5mb3JFYWNoKGZ1bmN0aW9uIChjLCBpKSB7CiAgICByZXR1cm4gdGFiW2NdID0gaTsKICB9KTsKICByZXR1cm4gdGFiOwp9KGI2NGNocyk7CnZhciBiNjRyZSA9IC9eKD86W0EtWmEtelxkK1wvXXs0fSkqPyg/OltBLVphLXpcZCtcL117Mn0oPzo9PSk/fFtBLVphLXpcZCtcL117M309Pyk/JC87CnZhciBfZnJvbUNDID0gU3RyaW5nLmZyb21DaGFyQ29kZS5iaW5kKFN0cmluZyk7CnZhciBfVThBZnJvbSA9IHR5cGVvZiBVaW50OEFycmF5LmZyb20gPT09ICdmdW5jdGlvbicgPyBVaW50OEFycmF5LmZyb20uYmluZChVaW50OEFycmF5KSA6IGZ1bmN0aW9uIChpdCkgewogIHZhciBmbiA9IGFyZ3VtZW50cy5sZW5ndGggPiAxICYmIGFyZ3VtZW50c1sxXSAhPT0gdW5kZWZpbmVkID8gYXJndW1lbnRzWzFdIDogZnVuY3Rpb24gKHgpIHsKICAgIHJldHVybiB4OwogIH07CiAgcmV0dXJuIG5ldyBVaW50OEFycmF5KEFycmF5LnByb3RvdHlwZS5zbGljZS5jYWxsKGl0LCAwKS5tYXAoZm4pKTsKfTsKdmFyIF9ta1VyaVNhZmUgPSBmdW5jdGlvbiBfbWtVcmlTYWZlKHNyYykgewogIHJldHVybiBzcmMucmVwbGFjZSgvPS9nLCAnJykucmVwbGFjZSgvWytcL10vZywgZnVuY3Rpb24gKG0wKSB7CiAgICByZXR1cm4gbTAgPT0gJysnID8gJy0nIDogJ18nOwogIH0pOwp9Owp2YXIgX3RpZHlCNjQgPSBmdW5jdGlvbiBfdGlkeUI2NChzKSB7CiAgcmV0dXJuIHMucmVwbGFjZSgvW15BLVphLXowLTlcK1wvXS9nLCAnJyk7Cn07Ci8qKgogKiBwb2x5ZmlsbCB2ZXJzaW9uIG9mIGBidG9hYAogKi8KdmFyIGJ0b2FQb2x5ZmlsbCA9IGZ1bmN0aW9uIGJ0b2FQb2x5ZmlsbChiaW4pIHsKICAvLyBjb25zb2xlLmxvZygncG9seWZpbGxlZCcpOwogIHZhciB1MzIsCiAgICBjMCwKICAgIGMxLAogICAgYzIsCiAgICBhc2MgPSAnJzsKICB2YXIgcGFkID0gYmluLmxlbmd0aCAlIDM7CiAgZm9yICh2YXIgaSA9IDA7IGkgPCBiaW4ubGVuZ3RoOykgewogICAgaWYgKChjMCA9IGJpbi5jaGFyQ29kZUF0KGkrKykpID4gMjU1IHx8IChjMSA9IGJpbi5jaGFyQ29kZUF0KGkrKykpID4gMjU1IHx8IChjMiA9IGJpbi5jaGFyQ29kZUF0KGkrKykpID4gMjU1KSB0aHJvdyBuZXcgVHlwZUVycm9yKCdpbnZhbGlkIGNoYXJhY3RlciBmb3VuZCcpOwogICAgdTMyID0gYzAgPDwgMTYgfCBjMSA8PCA4IHwgYzI7CiAgICBhc2MgKz0gYjY0Y2hzW3UzMiA+PiAxOCAmIDYzXSArIGI2NGNoc1t1MzIgPj4gMTIgJiA2M10gKyBiNjRjaHNbdTMyID4+IDYgJiA2M10gKyBiNjRjaHNbdTMyICYgNjNdOwogIH0KICByZXR1cm4gcGFkID8gYXNjLnNsaWNlKDAsIHBhZCAtIDMpICsgIj09PSIuc3Vic3RyaW5nKHBhZCkgOiBhc2M7Cn07Ci8qKgogKiBkb2VzIHdoYXQgYHdpbmRvdy5idG9hYCBvZiB3ZWIgYnJvd3NlcnMgZG8uCiAqIEBwYXJhbSB7U3RyaW5nfSBiaW4gYmluYXJ5IHN0cmluZwogKiBAcmV0dXJucyB7c3RyaW5nfSBCYXNlNjQtZW5jb2RlZCBzdHJpbmcKICovCnZhciBfYnRvYSA9IF9oYXNidG9hID8gZnVuY3Rpb24gKGJpbikgewogIHJldHVybiBidG9hKGJpbik7Cn0gOiBfaGFzQnVmZmVyID8gZnVuY3Rpb24gKGJpbikgewogIHJldHVybiBCdWZmZXIuZnJvbShiaW4sICdiaW5hcnknKS50b1N0cmluZygnYmFzZTY0Jyk7Cn0gOiBidG9hUG9seWZpbGw7CnZhciBfZnJvbVVpbnQ4QXJyYXkgPSBfaGFzQnVmZmVyID8gZnVuY3Rpb24gKHU4YSkgewogIHJldHVybiBCdWZmZXIuZnJvbSh1OGEpLnRvU3RyaW5nKCdiYXNlNjQnKTsKfSA6IGZ1bmN0aW9uICh1OGEpIHsKICAvLyBjZi4gaHR0cHM6Ly9zdGFja292ZXJmbG93LmNvbS9xdWVzdGlvbnMvMTI3MTAwMDEvaG93LXRvLWNvbnZlcnQtdWludDgtYXJyYXktdG8tYmFzZTY0LWVuY29kZWQtc3RyaW5nLzEyNzEzMzI2IzEyNzEzMzI2CiAgdmFyIG1heGFyZ3MgPSAweDEwMDA7CiAgdmFyIHN0cnMgPSBbXTsKICBmb3IgKHZhciBpID0gMCwgbCA9IHU4YS5sZW5ndGg7IGkgPCBsOyBpICs9IG1heGFyZ3MpIHsKICAgIHN0cnMucHVzaChfZnJvbUNDLmFwcGx5KG51bGwsIHU4YS5zdWJhcnJheShpLCBpICsgbWF4YXJncykpKTsKICB9CiAgcmV0dXJuIF9idG9hKHN0cnMuam9pbignJykpOwp9OwovKioKICogY29udmVydHMgYSBVaW50OEFycmF5IHRvIGEgQmFzZTY0IHN0cmluZy4KICogQHBhcmFtIHtib29sZWFufSBbdXJsc2FmZV0gVVJMLWFuZC1maWxlbmFtZS1zYWZlIGEgbGEgUkZDNDY0OCDCpzUKICogQHJldHVybnMge3N0cmluZ30gQmFzZTY0IHN0cmluZwogKi8KdmFyIGZyb21VaW50OEFycmF5ID0gZnVuY3Rpb24gZnJvbVVpbnQ4QXJyYXkodThhKSB7CiAgdmFyIHVybHNhZmUgPSBhcmd1bWVudHMubGVuZ3RoID4gMSAmJiBhcmd1bWVudHNbMV0gIT09IHVuZGVmaW5lZCA/IGFyZ3VtZW50c1sxXSA6IGZhbHNlOwogIHJldHVybiB1cmxzYWZlID8gX21rVXJpU2FmZShfZnJvbVVpbnQ4QXJyYXkodThhKSkgOiBfZnJvbVVpbnQ4QXJyYXkodThhKTsKfTsKLy8gVGhpcyB0cmljayBpcyBmb3VuZCBicm9rZW4gaHR0cHM6Ly9naXRodWIuY29tL2RhbmtvZ2FpL2pzLWJhc2U2NC9pc3N1ZXMvMTMwCi8vIGNvbnN0IHV0b2IgPSAoc3JjOiBzdHJpbmcpID0+IHVuZXNjYXBlKGVuY29kZVVSSUNvbXBvbmVudChzcmMpKTsKLy8gcmV2ZXJ0aW5nIGdvb2Qgb2xkIGZhdGlvbmVkIHJlZ2V4cAp2YXIgY2JfdXRvYiA9IGZ1bmN0aW9uIGNiX3V0b2IoYykgewogIGlmIChjLmxlbmd0aCA8IDIpIHsKICAgIHZhciBjYyA9IGMuY2hhckNvZGVBdCgwKTsKICAgIHJldHVybiBjYyA8IDB4ODAgPyBjIDogY2MgPCAweDgwMCA/IF9mcm9tQ0MoMHhjMCB8IGNjID4+PiA2KSArIF9mcm9tQ0MoMHg4MCB8IGNjICYgMHgzZikgOiBfZnJvbUNDKDB4ZTAgfCBjYyA+Pj4gMTIgJiAweDBmKSArIF9mcm9tQ0MoMHg4MCB8IGNjID4+PiA2ICYgMHgzZikgKyBfZnJvbUNDKDB4ODAgfCBjYyAmIDB4M2YpOwogIH0gZWxzZSB7CiAgICB2YXIgY2MgPSAweDEwMDAwICsgKGMuY2hhckNvZGVBdCgwKSAtIDB4RDgwMCkgKiAweDQwMCArIChjLmNoYXJDb2RlQXQoMSkgLSAweERDMDApOwogICAgcmV0dXJuIF9mcm9tQ0MoMHhmMCB8IGNjID4+PiAxOCAmIDB4MDcpICsgX2Zyb21DQygweDgwIHwgY2MgPj4+IDEyICYgMHgzZikgKyBfZnJvbUNDKDB4ODAgfCBjYyA+Pj4gNiAmIDB4M2YpICsgX2Zyb21DQygweDgwIHwgY2MgJiAweDNmKTsKICB9Cn07CnZhciByZV91dG9iID0gL1tcdUQ4MDAtXHVEQkZGXVtcdURDMDAtXHVERkZGRl18W15ceDAwLVx4N0ZdL2c7Ci8qKgogKiBAZGVwcmVjYXRlZCBzaG91bGQgaGF2ZSBiZWVuIGludGVybmFsIHVzZSBvbmx5LgogKiBAcGFyYW0ge3N0cmluZ30gc3JjIFVURi04IHN0cmluZwogKiBAcmV0dXJucyB7c3RyaW5nfSBVVEYtMTYgc3RyaW5nCiAqLwp2YXIgdXRvYiA9IGZ1bmN0aW9uIHV0b2IodSkgewogIHJldHVybiB1LnJlcGxhY2UocmVfdXRvYiwgY2JfdXRvYik7Cn07Ci8vCnZhciBfZW5jb2RlID0gX2hhc0J1ZmZlciA/IGZ1bmN0aW9uIChzKSB7CiAgcmV0dXJuIEJ1ZmZlci5mcm9tKHMsICd1dGY4JykudG9TdHJpbmcoJ2Jhc2U2NCcpOwp9IDogX1RFID8gZnVuY3Rpb24gKHMpIHsKICByZXR1cm4gX2Zyb21VaW50OEFycmF5KF9URS5lbmNvZGUocykpOwp9IDogZnVuY3Rpb24gKHMpIHsKICByZXR1cm4gX2J0b2EodXRvYihzKSk7Cn07Ci8qKgogKiBjb252ZXJ0cyBhIFVURi04LWVuY29kZWQgc3RyaW5nIHRvIGEgQmFzZTY0IHN0cmluZy4KICogQHBhcmFtIHtib29sZWFufSBbdXJsc2FmZV0gaWYgYHRydWVgIG1ha2UgdGhlIHJlc3VsdCBVUkwtc2FmZQogKiBAcmV0dXJucyB7c3RyaW5nfSBCYXNlNjQgc3RyaW5nCiAqLwp2YXIgZW5jb2RlID0gZnVuY3Rpb24gZW5jb2RlKHNyYykgewogIHZhciB1cmxzYWZlID0gYXJndW1lbnRzLmxlbmd0aCA+IDEgJiYgYXJndW1lbnRzWzFdICE9PSB1bmRlZmluZWQgPyBhcmd1bWVudHNbMV0gOiBmYWxzZTsKICByZXR1cm4gdXJsc2FmZSA/IF9ta1VyaVNhZmUoX2VuY29kZShzcmMpKSA6IF9lbmNvZGUoc3JjKTsKfTsKLyoqCiAqIGNvbnZlcnRzIGEgVVRGLTgtZW5jb2RlZCBzdHJpbmcgdG8gVVJMLXNhZmUgQmFzZTY0IFJGQzQ2NDggwqc1LgogKiBAcmV0dXJucyB7c3RyaW5nfSBCYXNlNjQgc3RyaW5nCiAqLwp2YXIgZW5jb2RlVVJJID0gZnVuY3Rpb24gZW5jb2RlVVJJKHNyYykgewogIHJldHVybiBlbmNvZGUoc3JjLCB0cnVlKTsKfTsKLy8gVGhpcyB0cmljayBpcyBmb3VuZCBicm9rZW4gaHR0cHM6Ly9naXRodWIuY29tL2RhbmtvZ2FpL2pzLWJhc2U2NC9pc3N1ZXMvMTMwCi8vIGNvbnN0IGJ0b3UgPSAoc3JjOiBzdHJpbmcpID0+IGRlY29kZVVSSUNvbXBvbmVudChlc2NhcGUoc3JjKSk7Ci8vIHJldmVydGluZyBnb29kIG9sZCBmYXRpb25lZCByZWdleHAKdmFyIHJlX2J0b3UgPSAvW1x4QzAtXHhERl1bXHg4MC1ceEJGXXxbXHhFMC1ceEVGXVtceDgwLVx4QkZdezJ9fFtceEYwLVx4RjddW1x4ODAtXHhCRl17M30vZzsKdmFyIGNiX2J0b3UgPSBmdW5jdGlvbiBjYl9idG91KGNjY2MpIHsKICBzd2l0Y2ggKGNjY2MubGVuZ3RoKSB7CiAgICBjYXNlIDQ6CiAgICAgIHZhciBjcCA9ICgweDA3ICYgY2NjYy5jaGFyQ29kZUF0KDApKSA8PCAxOCB8ICgweDNmICYgY2NjYy5jaGFyQ29kZUF0KDEpKSA8PCAxMiB8ICgweDNmICYgY2NjYy5jaGFyQ29kZUF0KDIpKSA8PCA2IHwgMHgzZiAmIGNjY2MuY2hhckNvZGVBdCgzKSwKICAgICAgICBvZmZzZXQgPSBjcCAtIDB4MTAwMDA7CiAgICAgIHJldHVybiBfZnJvbUNDKChvZmZzZXQgPj4+IDEwKSArIDB4RDgwMCkgKyBfZnJvbUNDKChvZmZzZXQgJiAweDNGRikgKyAweERDMDApOwogICAgY2FzZSAzOgogICAgICByZXR1cm4gX2Zyb21DQygoMHgwZiAmIGNjY2MuY2hhckNvZGVBdCgwKSkgPDwgMTIgfCAoMHgzZiAmIGNjY2MuY2hhckNvZGVBdCgxKSkgPDwgNiB8IDB4M2YgJiBjY2NjLmNoYXJDb2RlQXQoMikpOwogICAgZGVmYXVsdDoKICAgICAgcmV0dXJuIF9mcm9tQ0MoKDB4MWYgJiBjY2NjLmNoYXJDb2RlQXQoMCkpIDw8IDYgfCAweDNmICYgY2NjYy5jaGFyQ29kZUF0KDEpKTsKICB9Cn07Ci8qKgogKiBAZGVwcmVjYXRlZCBzaG91bGQgaGF2ZSBiZWVuIGludGVybmFsIHVzZSBvbmx5LgogKiBAcGFyYW0ge3N0cmluZ30gc3JjIFVURi0xNiBzdHJpbmcKICogQHJldHVybnMge3N0cmluZ30gVVRGLTggc3RyaW5nCiAqLwp2YXIgYnRvdSA9IGZ1bmN0aW9uIGJ0b3UoYikgewogIHJldHVybiBiLnJlcGxhY2UocmVfYnRvdSwgY2JfYnRvdSk7Cn07Ci8qKgogKiBwb2x5ZmlsbCB2ZXJzaW9uIG9mIGBhdG9iYAogKi8KdmFyIGF0b2JQb2x5ZmlsbCA9IGZ1bmN0aW9uIGF0b2JQb2x5ZmlsbChhc2MpIHsKICAvLyBjb25zb2xlLmxvZygncG9seWZpbGxlZCcpOwogIGFzYyA9IGFzYy5yZXBsYWNlKC9ccysvZywgJycpOwogIGlmICghYjY0cmUudGVzdChhc2MpKSB0aHJvdyBuZXcgVHlwZUVycm9yKCdtYWxmb3JtZWQgYmFzZTY0LicpOwogIGFzYyArPSAnPT0nLnNsaWNlKDIgLSAoYXNjLmxlbmd0aCAmIDMpKTsKICB2YXIgdTI0LAogICAgYmluID0gJycsCiAgICByMSwKICAgIHIyOwogIGZvciAodmFyIGkgPSAwOyBpIDwgYXNjLmxlbmd0aDspIHsKICAgIHUyNCA9IGI2NHRhYlthc2MuY2hhckF0KGkrKyldIDw8IDE4IHwgYjY0dGFiW2FzYy5jaGFyQXQoaSsrKV0gPDwgMTIgfCAocjEgPSBiNjR0YWJbYXNjLmNoYXJBdChpKyspXSkgPDwgNiB8IChyMiA9IGI2NHRhYlthc2MuY2hhckF0KGkrKyldKTsKICAgIGJpbiArPSByMSA9PT0gNjQgPyBfZnJvbUNDKHUyNCA+PiAxNiAmIDI1NSkgOiByMiA9PT0gNjQgPyBfZnJvbUNDKHUyNCA+PiAxNiAmIDI1NSwgdTI0ID4+IDggJiAyNTUpIDogX2Zyb21DQyh1MjQgPj4gMTYgJiAyNTUsIHUyNCA+PiA4ICYgMjU1LCB1MjQgJiAyNTUpOwogIH0KICByZXR1cm4gYmluOwp9OwovKioKICogZG9lcyB3aGF0IGB3aW5kb3cuYXRvYmAgb2Ygd2ViIGJyb3dzZXJzIGRvLgogKiBAcGFyYW0ge1N0cmluZ30gYXNjIEJhc2U2NC1lbmNvZGVkIHN0cmluZwogKiBAcmV0dXJucyB7c3RyaW5nfSBiaW5hcnkgc3RyaW5nCiAqLwp2YXIgX2F0b2IgPSBfaGFzYXRvYiA/IGZ1bmN0aW9uIChhc2MpIHsKICByZXR1cm4gYXRvYihfdGlkeUI2NChhc2MpKTsKfSA6IF9oYXNCdWZmZXIgPyBmdW5jdGlvbiAoYXNjKSB7CiAgcmV0dXJuIEJ1ZmZlci5mcm9tKGFzYywgJ2Jhc2U2NCcpLnRvU3RyaW5nKCdiaW5hcnknKTsKfSA6IGF0b2JQb2x5ZmlsbDsKLy8KdmFyIF90b1VpbnQ4QXJyYXkgPSBfaGFzQnVmZmVyID8gZnVuY3Rpb24gKGEpIHsKICByZXR1cm4gX1U4QWZyb20oQnVmZmVyLmZyb20oYSwgJ2Jhc2U2NCcpKTsKfSA6IGZ1bmN0aW9uIChhKSB7CiAgcmV0dXJuIF9VOEFmcm9tKF9hdG9iKGEpLCBmdW5jdGlvbiAoYykgewogICAgcmV0dXJuIGMuY2hhckNvZGVBdCgwKTsKICB9KTsKfTsKLyoqCiAqIGNvbnZlcnRzIGEgQmFzZTY0IHN0cmluZyB0byBhIFVpbnQ4QXJyYXkuCiAqLwp2YXIgdG9VaW50OEFycmF5ID0gZnVuY3Rpb24gdG9VaW50OEFycmF5KGEpIHsKICByZXR1cm4gX3RvVWludDhBcnJheShfdW5VUkkoYSkpOwp9OwovLwp2YXIgX2RlY29kZSA9IF9oYXNCdWZmZXIgPyBmdW5jdGlvbiAoYSkgewogIHJldHVybiBCdWZmZXIuZnJvbShhLCAnYmFzZTY0JykudG9TdHJpbmcoJ3V0ZjgnKTsKfSA6IF9URCA/IGZ1bmN0aW9uIChhKSB7CiAgcmV0dXJuIF9URC5kZWNvZGUoX3RvVWludDhBcnJheShhKSk7Cn0gOiBmdW5jdGlvbiAoYSkgewogIHJldHVybiBidG91KF9hdG9iKGEpKTsKfTsKdmFyIF91blVSSSA9IGZ1bmN0aW9uIF91blVSSShhKSB7CiAgcmV0dXJuIF90aWR5QjY0KGEucmVwbGFjZSgvWy1fXS9nLCBmdW5jdGlvbiAobTApIHsKICAgIHJldHVybiBtMCA9PSAnLScgPyAnKycgOiAnLyc7CiAgfSkpOwp9OwovKioKICogY29udmVydHMgYSBCYXNlNjQgc3RyaW5nIHRvIGEgVVRGLTggc3RyaW5nLgogKiBAcGFyYW0ge1N0cmluZ30gc3JjIEJhc2U2NCBzdHJpbmcuICBCb3RoIG5vcm1hbCBhbmQgVVJMLXNhZmUgYXJlIHN1cHBvcnRlZAogKiBAcmV0dXJucyB7c3RyaW5nfSBVVEYtOCBzdHJpbmcKICovCnZhciBkZWNvZGUgPSBmdW5jdGlvbiBkZWNvZGUoc3JjKSB7CiAgcmV0dXJuIF9kZWNvZGUoX3VuVVJJKHNyYykpOwp9OwovKioKICogY2hlY2sgaWYgYSB2YWx1ZSBpcyBhIHZhbGlkIEJhc2U2NCBzdHJpbmcKICogQHBhcmFtIHtTdHJpbmd9IHNyYyBhIHZhbHVlIHRvIGNoZWNrCiAgKi8KdmFyIGlzVmFsaWQgPSBmdW5jdGlvbiBpc1ZhbGlkKHNyYykgewogIGlmICh0eXBlb2Ygc3JjICE9PSAnc3RyaW5nJykgcmV0dXJuIGZhbHNlOwogIHZhciBzID0gc3JjLnJlcGxhY2UoL1xzKy9nLCAnJykucmVwbGFjZSgvPXswLDJ9JC8sICcnKTsKICByZXR1cm4gIS9bXlxzMC05YS16QS1aXCsvXS8udGVzdChzKSB8fCAhL1teXHMwLTlhLXpBLVpcLV9dLy50ZXN0KHMpOwp9OwovLwp2YXIgX25vRW51bSA9IGZ1bmN0aW9uIF9ub0VudW0odikgewogIHJldHVybiB7CiAgICB2YWx1ZTogdiwKICAgIGVudW1lcmFibGU6IGZhbHNlLAogICAgd3JpdGFibGU6IHRydWUsCiAgICBjb25maWd1cmFibGU6IHRydWUKICB9Owp9OwovKioKICogZXh0ZW5kIFN0cmluZy5wcm90b3R5cGUgd2l0aCByZWxldmFudCBtZXRob2RzCiAqLwp2YXIgZXh0ZW5kU3RyaW5nID0gZnVuY3Rpb24gZXh0ZW5kU3RyaW5nKCkgewogIHZhciBfYWRkID0gZnVuY3Rpb24gX2FkZChuYW1lLCBib2R5KSB7CiAgICByZXR1cm4gT2JqZWN0LmRlZmluZVByb3BlcnR5KFN0cmluZy5wcm90b3R5cGUsIG5hbWUsIF9ub0VudW0oYm9keSkpOwogIH07CiAgX2FkZCgnZnJvbUJhc2U2NCcsIGZ1bmN0aW9uICgpIHsKICAgIHJldHVybiBkZWNvZGUodGhpcyk7CiAgfSk7CiAgX2FkZCgndG9CYXNlNjQnLCBmdW5jdGlvbiAodXJsc2FmZSkgewogICAgcmV0dXJuIGVuY29kZSh0aGlzLCB1cmxzYWZlKTsKICB9KTsKICBfYWRkKCd0b0Jhc2U2NFVSSScsIGZ1bmN0aW9uICgpIHsKICAgIHJldHVybiBlbmNvZGUodGhpcywgdHJ1ZSk7CiAgfSk7CiAgX2FkZCgndG9CYXNlNjRVUkwnLCBmdW5jdGlvbiAoKSB7CiAgICByZXR1cm4gZW5jb2RlKHRoaXMsIHRydWUpOwogIH0pOwogIF9hZGQoJ3RvVWludDhBcnJheScsIGZ1bmN0aW9uICgpIHsKICAgIHJldHVybiB0b1VpbnQ4QXJyYXkodGhpcyk7CiAgfSk7Cn07Ci8qKgogKiBleHRlbmQgVWludDhBcnJheS5wcm90b3R5cGUgd2l0aCByZWxldmFudCBtZXRob2RzCiAqLwp2YXIgZXh0ZW5kVWludDhBcnJheSA9IGZ1bmN0aW9uIGV4dGVuZFVpbnQ4QXJyYXkoKSB7CiAgdmFyIF9hZGQgPSBmdW5jdGlvbiBfYWRkKG5hbWUsIGJvZHkpIHsKICAgIHJldHVybiBPYmplY3QuZGVmaW5lUHJvcGVydHkoVWludDhBcnJheS5wcm90b3R5cGUsIG5hbWUsIF9ub0VudW0oYm9keSkpOwogIH07CiAgX2FkZCgndG9CYXNlNjQnLCBmdW5jdGlvbiAodXJsc2FmZSkgewogICAgcmV0dXJuIGZyb21VaW50OEFycmF5KHRoaXMsIHVybHNhZmUpOwogIH0pOwogIF9hZGQoJ3RvQmFzZTY0VVJJJywgZnVuY3Rpb24gKCkgewogICAgcmV0dXJuIGZyb21VaW50OEFycmF5KHRoaXMsIHRydWUpOwogIH0pOwogIF9hZGQoJ3RvQmFzZTY0VVJMJywgZnVuY3Rpb24gKCkgewogICAgcmV0dXJuIGZyb21VaW50OEFycmF5KHRoaXMsIHRydWUpOwogIH0pOwp9OwovKioKICogZXh0ZW5kIEJ1aWx0aW4gcHJvdG90eXBlcyB3aXRoIHJlbGV2YW50IG1ldGhvZHMKICovCnZhciBleHRlbmRCdWlsdGlucyA9IGZ1bmN0aW9uIGV4dGVuZEJ1aWx0aW5zKCkgewogIGV4dGVuZFN0cmluZygpOwogIGV4dGVuZFVpbnQ4QXJyYXkoKTsKfTsKdmFyIGdCYXNlNjQgPSB7CiAgdmVyc2lvbjogdmVyc2lvbiwKICBWRVJTSU9OOiBWRVJTSU9OLAogIGF0b2I6IF9hdG9iLAogIGF0b2JQb2x5ZmlsbDogYXRvYlBvbHlmaWxsLAogIGJ0b2E6IF9idG9hLAogIGJ0b2FQb2x5ZmlsbDogYnRvYVBvbHlmaWxsLAogIGZyb21CYXNlNjQ6IGRlY29kZSwKICB0b0Jhc2U2NDogZW5jb2RlLAogIGVuY29kZTogZW5jb2RlLAogIGVuY29kZVVSSTogZW5jb2RlVVJJLAogIGVuY29kZVVSTDogZW5jb2RlVVJJLAogIHV0b2I6IHV0b2IsCiAgYnRvdTogYnRvdSwKICBkZWNvZGU6IGRlY29kZSwKICBpc1ZhbGlkOiBpc1ZhbGlkLAogIGZyb21VaW50OEFycmF5OiBmcm9tVWludDhBcnJheSwKICB0b1VpbnQ4QXJyYXk6IHRvVWludDhBcnJheSwKICBleHRlbmRTdHJpbmc6IGV4dGVuZFN0cmluZywKICBleHRlbmRVaW50OEFycmF5OiBleHRlbmRVaW50OEFycmF5LAogIGV4dGVuZEJ1aWx0aW5zOiBleHRlbmRCdWlsdGlucwp9OwovLyBtYWtlY2pzOkNVVCAvLwpleHBvcnQgeyB2ZXJzaW9uIH07CmV4cG9ydCB7IFZFUlNJT04gfTsKZXhwb3J0IHsgX2F0b2IgYXMgYXRvYiB9OwpleHBvcnQgeyBhdG9iUG9seWZpbGwgfTsKZXhwb3J0IHsgX2J0b2EgYXMgYnRvYSB9OwpleHBvcnQgeyBidG9hUG9seWZpbGwgfTsKZXhwb3J0IHsgZGVjb2RlIGFzIGZyb21CYXNlNjQgfTsKZXhwb3J0IHsgZW5jb2RlIGFzIHRvQmFzZTY0IH07CmV4cG9ydCB7IHV0b2IgfTsKZXhwb3J0IHsgZW5jb2RlIH07CmV4cG9ydCB7IGVuY29kZVVSSSB9OwpleHBvcnQgeyBlbmNvZGVVUkkgYXMgZW5jb2RlVVJMIH07CmV4cG9ydCB7IGJ0b3UgfTsKZXhwb3J0IHsgZGVjb2RlIH07CmV4cG9ydCB7IGlzVmFsaWQgfTsKZXhwb3J0IHsgZnJvbVVpbnQ4QXJyYXkgfTsKZXhwb3J0IHsgdG9VaW50OEFycmF5IH07CmV4cG9ydCB7IGV4dGVuZFN0cmluZyB9OwpleHBvcnQgeyBleHRlbmRVaW50OEFycmF5IH07CmV4cG9ydCB7IGV4dGVuZEJ1aWx0aW5zIH07Ci8vIGFuZCBmaW5hbGx5LApleHBvcnQgeyBnQmFzZTY0IGFzIEJhc2U2NCB9Ow=="}, null]}