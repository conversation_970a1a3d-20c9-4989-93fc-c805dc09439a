{"remainingRequest": "D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\node_modules\\babel-loader\\lib\\index.js!D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\node_modules\\@babel\\runtime\\helpers\\esm\\isNativeFunction.js", "dependencies": [{"path": "D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\node_modules\\@babel\\runtime\\helpers\\esm\\isNativeFunction.js", "mtime": 456789000000}, {"path": "D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\babel.config.js", "mtime": 1686019818359}, {"path": "D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:aW1wb3J0ICJjb3JlLWpzL21vZHVsZXMvZXMub2JqZWN0LnRvLXN0cmluZy5qcyI7CmltcG9ydCAiY29yZS1qcy9tb2R1bGVzL2VzLnJlZ2V4cC50by1zdHJpbmcuanMiOwpleHBvcnQgZGVmYXVsdCBmdW5jdGlvbiBfaXNOYXRpdmVGdW5jdGlvbihmbikgewogIHJldHVybiBGdW5jdGlvbi50b1N0cmluZy5jYWxsKGZuKS5pbmRleE9mKCJbbmF0aXZlIGNvZGVdIikgIT09IC0xOwp9"}, {"version": 3, "names": ["_isNativeFunction", "fn", "Function", "toString", "call", "indexOf"], "sources": ["D:/1_Project/XYD_Project/dop-4.0/dop-4.1-qian<PERSON>n-unify/dopUnify/node_modules/@babel/runtime/helpers/esm/isNativeFunction.js"], "sourcesContent": ["export default function _isNativeFunction(fn) {\n  return Function.toString.call(fn).indexOf(\"[native code]\") !== -1;\n}"], "mappings": ";;AAAA,eAAe,SAASA,iBAAiB,CAACC,EAAE,EAAE;EAC5C,OAAOC,QAAQ,CAACC,QAAQ,CAACC,IAAI,CAACH,EAAE,CAAC,CAACI,OAAO,CAAC,eAAe,CAAC,KAAK,CAAC,CAAC;AACnE"}]}