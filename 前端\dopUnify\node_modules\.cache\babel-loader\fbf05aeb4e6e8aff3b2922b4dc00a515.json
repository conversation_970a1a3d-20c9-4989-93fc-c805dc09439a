{"remainingRequest": "D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\node_modules\\babel-loader\\lib\\index.js!D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\src\\views\\home\\component\\externalMenu\\index.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\src\\views\\home\\component\\externalMenu\\index.vue", "mtime": 1686019809419}, {"path": "D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\babel.config.js", "mtime": 1686019818359}, {"path": "D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1655715099000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAuBA;AACA;AACA;AACA;AACA,IACAA,kBACAC,OADAD;AAEA;EACAE;EACAC;IACAC;MACAC;MACAC;IACA;IACAC;MACAF;MACAC;IACA;IACAE;MACAH;MACAC;IACA;EACA;EACAG;IACA;MACAC;MACAC;MACAC;MACAC;MAAA;MACAC;IACA;EACA;EACAC;IACAH;MACA;QACA;MACA;IACA;EACA;EACAI;EACAC;IAAA;IACAC;IACA;IACA;MAAA;MAAA;QAAA;UAAA;YAAA;cACA;cACA;cACA;cACA;cACA;cACA;cACA;cACA;cACA;cAAA,wBAEA;gBACA;cACA,IAHAC;cAIA;cACA;cAAA,IACAA;gBAAA;gBAAA;cAAA;cACAC;cAAA,KACAC;gBAAA;gBAAA;cAAA;cAAA;cAAA,OACA;YAAA;cAAA;cAAA;YAAA;cAEA;gBACAC;gBACAC;gBACAC,uBACAC;cAEA;YAAA;cAGA;cACA;gBACA;gBACAC,sBACA;gBACA;kBACA;kBACAC,0DACA;oBACA;kBACA,EACA;kBACA;oBACA;oBACA;oBACA;oBACA;kBACA;oBACA;oBACA;kBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;cACA;gBACA;cACA;cACA;cACA;cACA;YAAA;YAAA;cAAA;UAAA;QAAA;MAAA;IAAA,CACA;EACA;EACAC;IACA;AACA;AACA;IACAC;MAAA;MACA;QACA;UACAC;UACAC;UACAC;UAAA;UACAV;QACA;QACAtB;UACA;UACAyB;UACAA,qBACAH,yBACAW,+EACA;UACA;YACAX;YACAC;YACAC;UACA;UACAU;QACA;MACA;IACA;IACAC;MAAA;MACAC;QACA;QACA;QACA;QACA;QACA;UACA,6BACAH,yDACA;UACA;UACAI,mDACAC,uBACAA,8BACA;UACA;UACA;UACA;YACAC;YACAC,yCACA,IACAA,8CACAD,iCACA;UACA;YACAA;YACAC,yCACA,IACAA,8CAEAD,iCAEA;YACAC;cACAC;YACA;UACA;UAEA;YACA;cACA,IACAC,oBACAA,yBACAA,sBACA;gBACA;cACA;YACA;UACA;YACA;cACA,IACAA,8BACAA,0BACAA,uBACA;gBACA;cACA;YACA;UACA;QACA;QACA;QACA;UACA;YACAC;YACAC;YACAvC;YACAQ;UACA;UACA;UACAgC;UACA;UACAZ,iCACA,IACA;QACA;MACA;IACA;IACA;AACA;AACA;IACAa;MAAA;MACAC;QACA;QACA;QACA;UACA;UACA;YACA;YACA;YACA;UACA;UACA;UACA;QACA;MACA;IACA;EACA;AACA", "names": ["fieldController", "Common", "name", "props", "path", "type", "default", "title", "id", "data", "iframeUrl", "extendRef", "is<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "projectName", "systemNum", "watch", "activated", "mounted", "console", "system_no", "systemDic", "commonBlank", "systemNo", "value", "dictionary", "localStorage", "deplName", "depl<PERSON><PERSON><PERSON><PERSON>", "methods", "getDictionaryData", "parameterList", "fieldTime", "operType", "JSON", "resolve", "postMess", "setTimeout", "dictionaryPUnfiy", "dictionaryP", "childSystemNo", "commonData", "dictionaryTree", "item", "user", "common", "iframeWin", "postParam", "window"], "sourceRoot": "src/views/home/<USER>/externalMenu", "sources": ["index.vue"], "sourcesContent": ["<!--\r\n* 首页模块 配置外菜单入口\r\n-->\r\n<template>\r\n  <div class=\"homeContent\">\r\n    <div\r\n      class=\"homePageTitle\"\r\n      style=\"width: 100%; height: 2rem; line-height: 2rem\"\r\n    >\r\n      {{ title }}\r\n    </div>\r\n    <div ref=\"ccc\" class=\"content\">\r\n      <iframe\r\n        :ref=\"extendRef\"\r\n        class=\"extendIframe\"\r\n        :src=\"iframeUrl\"\r\n        frameborder=\"0\"\r\n      />\r\n    </div>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\n// import store from '@/store'\r\nimport { commonBlank } from '@/utils/common'\r\nimport { Common } from '@/api'\r\nimport { dictionaryFieds } from '@/utils/dictionary'\r\nconst {\r\n  fieldController // 同步字典\r\n} = Common\r\nexport default {\r\n  name: 'Extend',\r\n  props: {\r\n    path: {\r\n      type: String,\r\n      default: ''\r\n    },\r\n    title: {\r\n      type: String,\r\n      default: ''\r\n    },\r\n    id: {\r\n      type: String,\r\n      default: ''\r\n    }\r\n  },\r\n  data() {\r\n    return {\r\n      iframeUrl: '',\r\n      extendRef: 'extendRef',\r\n      isChildrenReady: false,\r\n      projectName: '', // 子项目名称\r\n      systemNum: ''\r\n    }\r\n  },\r\n  watch: {\r\n    isChildrenReady(val) {\r\n      if (val) {\r\n        this.postMess()\r\n      }\r\n    }\r\n  },\r\n  activated() {},\r\n  mounted() {\r\n    console.log(this.path)\r\n    // this.postMess()\r\n    this.$nextTick().then(async() => {\r\n      // console.log(this.$route.matched)\r\n      // const pathIndex = this.$route.matched.length - 1\r\n      // const routeMessage = this.$route.matched[pathIndex].props.default\r\n      // const path = routeMessage.path\r\n      // http://172.1.11.51:9528/redirect/report/#/report/dataSet\r\n      // http://172.1.1.21/dopUnify/redirect/report/#/report/dataSet\r\n      // if (!commonBlank(this.path)) {\r\n      // store.commit('user/SET_ROUTE_M', routeMessage)\r\n      // 获取该菜单的字典号\r\n      const { system_no, depl_name, menu_attr } =\r\n        this.$store.state.permission.addRoutesArray.find((item) => {\r\n          return item.menu_id === this.id\r\n        })\r\n      this.systemNum = system_no // 系统号\r\n      // 获取外系统菜单 数据字典\r\n      if (!(system_no in this.$store.state.common.dictionaryLet)) {\r\n        const systemDic = JSON.parse(localStorage.getItem(this.systemNum))\r\n        if (commonBlank(systemDic)) {\r\n          await this.getDictionaryData(this.systemNum)\r\n        } else {\r\n          this.$store.commit('common/ADD_DICTIONARYLET', {\r\n            systemNo: this.systemNum,\r\n            value: systemDic,\r\n            dictionary: JSON.parse(\r\n              localStorage.getItem(this.systemNum + 'dictionary')\r\n            )\r\n          })\r\n        }\r\n      }\r\n      // const pathUrl = this.path.split(':') // redirect:/report/report1\r\n      if (menu_attr === '2') {\r\n        // 外系统菜单\r\n        const deplName = depl_name\r\n        // this.$route.matched.slice(-1)[0].props.default.menu_id\r\n        if (!commonBlank(deplName)) {\r\n          // 有做代理\r\n          const deplNamelabel = dictionaryFieds('EXT_SYS_DEPL_NAME').find(\r\n            (item) => {\r\n              return item.value === deplName\r\n            }\r\n          ).label\r\n          if (this.path.indexOf('static/html') !== -1) {\r\n            // 兼容老基线sunaos\r\n            // http://172.1.1.101/dopUnify/redirect/unify/iframe.html\r\n            this.iframeUrl = `${window.location.origin}${window.location.pathname}redirect/${deplNamelabel}/iframe.html`\r\n            // this.iframeUrl = `http://172.1.10.185/dopUnify/redirect/${deplNamelabel}/iframe.html`\r\n          } else {\r\n            this.iframeUrl = `${window.location.origin}${window.location.pathname}redirect/${deplNamelabel}/#/${this.path}`\r\n            // this.iframeUrl = `http://***********/dopUnify/redirect/${deplNamelabel}/#/${path}`\r\n          }\r\n        }\r\n        // this.iframeUrl = `${window.location.origin}${\r\n        //   window.location.pathname\r\n        // }redirect/${\r\n        //   pathUrl[1].substring(1).split('/')[0]\r\n        // }/#/${pathUrl[1].substring(1)}`\r\n      } else {\r\n        this.iframeUrl = this.path\r\n      }\r\n      // this.iframeUrl = 'http://localhost:5000/#/ism/schedulingResult/week'\r\n      // this.extendRef = this.extendRef + this.$route.path\r\n      this.postParam()\r\n    })\r\n  },\r\n  methods: {\r\n    /**\r\n     * 获取数据字典\r\n     */\r\n    getDictionaryData(systemNo) {\r\n      return new Promise((resolve) => {\r\n        const msg = {\r\n          parameterList: [''],\r\n          fieldTime: '',\r\n          operType: '1', // 门户操作标识\r\n          systemNo: systemNo\r\n        }\r\n        fieldController(msg).then((res) => {\r\n          // 新增外系统字典\r\n          localStorage.setItem(systemNo, JSON.stringify(res.retMap[systemNo]))\r\n          localStorage.setItem(\r\n            systemNo + 'dictionary',\r\n            JSON.stringify({ [systemNo]: res.retMap.dictionary[systemNo] })\r\n          )\r\n          this.$store.commit('common/ADD_DICTIONARYLET', {\r\n            systemNo,\r\n            value: res.retMap[systemNo],\r\n            dictionary: res.retMap.dictionary\r\n          })\r\n          resolve()\r\n        })\r\n      })\r\n    },\r\n    postMess() {\r\n      setTimeout(() => {\r\n        const mapFrame = this.$refs[this.extendRef]\r\n        const iframeWin = mapFrame.contentWindow\r\n        // 合并父子工程数据字典 begin\r\n        let commonData = JSON.parse(JSON.stringify(this.$store.state.common))\r\n        if (this.systemNum !== 'UNIFY') {\r\n          const dictionaryP = JSON.parse(\r\n            JSON.stringify(this.$store.state.common.dictionaryLet)\r\n          )\r\n          let dictionaryPUnfiy = {}\r\n          dictionaryPUnfiy = {\r\n            ...dictionaryP['UNIFY'],\r\n            ...dictionaryP[this.systemNum]\r\n          }\r\n          let childSystemNo = ''\r\n          // 单独为指标修改传值模式\r\n          if (this.projectName === 'indicator') {\r\n            childSystemNo = 'AOS'\r\n            commonData.dictionaryLet = Object.assign(\r\n              {},\r\n              commonData.dictionaryLet,\r\n              { [childSystemNo]: dictionaryPUnfiy }\r\n            )\r\n          } else {\r\n            childSystemNo = 'UNIFY'\r\n            commonData.dictionaryLet = Object.assign(\r\n              {},\r\n              commonData.dictionaryLet,\r\n              {\r\n                [childSystemNo]: dictionaryPUnfiy\r\n              }\r\n            )\r\n            commonData = Object.assign({}, commonData, {\r\n              dictionaryTree: commonData.dictionaryTree[this.systemNum]\r\n            })\r\n          }\r\n\r\n          if (childSystemNo === 'UNIFY') {\r\n            for (const item in commonData.dictionaryLet) {\r\n              if (\r\n                item !== 'UNIFY' &&\r\n                item !== 'dictionary' &&\r\n                item !== 'fieldTime'\r\n              ) {\r\n                delete commonData.dictionaryLet[item]\r\n              }\r\n            }\r\n          } else {\r\n            for (const item in commonData.dictionaryLet) {\r\n              if (\r\n                item !== this.systemNum &&\r\n                item !== 'dictionary' &&\r\n                item !== 'fieldTime'\r\n              ) {\r\n                delete commonData.dictionaryLet[item]\r\n              }\r\n            }\r\n          }\r\n        }\r\n        // 合并父子工程数据字典 end\r\n        if (iframeWin) {\r\n          const msg = {\r\n            user: this.$store.state.user,\r\n            common: commonData,\r\n            type: 'dopUnify',\r\n            projectName: this.projectName\r\n          }\r\n          // console.log('父传子的数据', this.$store.state.user)\r\n          iframeWin.postMessage(\r\n            // 向子工程发送\r\n            JSON.parse(JSON.stringify(msg)),\r\n            '*'\r\n          )\r\n        }\r\n      }, 1000)\r\n    },\r\n    /**\r\n     * 向iframe子页面发送消息\r\n     */\r\n    postParam() {\r\n      window.addEventListener('message', (evt) => {\r\n        // 接收子工程数据\r\n        // 若子页面已经加载好通知父工程修改了isChildrenReady的状态\r\n        if (evt.data.type === 'dopUnify') {\r\n          // 当首页配置了 外系统菜单时 切换tab页面时 此时isChildrenReady在初始化时已经为true 所以手动给子工程传参\r\n          if (this.isChildrenReady) {\r\n            this.projectName = evt.data.projectName\r\n            this.postMess()\r\n            return\r\n          }\r\n          this.isChildrenReady = true\r\n          this.projectName = evt.data.projectName\r\n        }\r\n      })\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n.extendIframe {\r\n  width: 100%;\r\n  height: 100%;\r\n  position: absolute;\r\n  top: 3rem;\r\n  bottom: 0;\r\n  overflow: hidden;\r\n}\r\n.content {\r\n  margin-top: 30px;\r\n}\r\n</style>\r\n"]}]}