{"remainingRequest": "D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\node_modules\\babel-loader\\lib\\index.js!D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\node_modules\\qiankun\\es\\sandbox\\index.js", "dependencies": [{"path": "D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\node_modules\\qiankun\\es\\sandbox\\index.js", "mtime": 1667130453000}, {"path": "D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\babel.config.js", "mtime": 1686019818359}, {"path": "D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["_toConsumableArray", "_asyncToGenerator", "_regeneratorRuntime", "LegacySandbox", "patchAtBootstrapping", "patchAtMounting", "ProxySandbox", "SnapshotSandbox", "getCurrentRunningApp", "css", "createSandboxContainer", "appName", "elementGetter", "scopedCSS", "useLooseSandbox", "excludeAssetFilter", "globalContext", "speedySandBox", "sandbox", "window", "Proxy", "bootstrappingFreers", "mountingFreers", "sideEffectsRebuilders", "instance", "mount", "mark", "_callee", "sideEffectsRebuildersAtBootstrapping", "sideEffectsRebuildersAtMounting", "wrap", "_callee$", "_context", "prev", "next", "active", "slice", "length", "for<PERSON>ach", "rebuild", "stop", "unmount", "_callee2", "_callee2$", "_context2", "concat", "map", "free", "inactive"], "sources": ["D:/1_Project/XYD_Project/dop-4.0/dop-4.1-qianduan-unify/dopUnify/node_modules/qiankun/es/sandbox/index.js"], "sourcesContent": ["import _toConsumableArray from \"@babel/runtime/helpers/esm/toConsumableArray\";\nimport _asyncToGenerator from \"@babel/runtime/helpers/esm/asyncToGenerator\";\nimport _regeneratorRuntime from \"@babel/runtime/regenerator\";\nimport LegacySandbox from './legacy/sandbox';\nimport { patchAtBootstrapping, patchAtMounting } from './patchers';\nimport ProxySandbox from './proxySandbox';\nimport SnapshotSandbox from './snapshotSandbox';\nexport { getCurrentRunningApp } from './common';\nexport { css } from './patchers';\n/**\n * 生成应用运行时沙箱\n *\n * 沙箱分两个类型：\n * 1. app 环境沙箱\n *  app 环境沙箱是指应用初始化过之后，应用会在什么样的上下文环境运行。每个应用的环境沙箱只会初始化一次，因为子应用只会触发一次 bootstrap 。\n *  子应用在切换时，实际上切换的是 app 环境沙箱。\n * 2. render 沙箱\n *  子应用在 app mount 开始前生成好的的沙箱。每次子应用切换过后，render 沙箱都会重现初始化。\n *\n * 这么设计的目的是为了保证每个子应用切换回来之后，还能运行在应用 bootstrap 之后的环境下。\n *\n * @param appName\n * @param elementGetter\n * @param scopedCSS\n * @param useLooseSandbox\n * @param excludeAssetFilter\n * @param globalContext\n * @param speedySandBox\n */\nexport function createSandboxContainer(appName, elementGetter, scopedCSS, useLooseSandbox, excludeAssetFilter, globalContext, speedySandBox) {\n  var sandbox;\n  if (window.Proxy) {\n    sandbox = useLooseSandbox ? new LegacySandbox(appName, globalContext) : new ProxySandbox(appName, globalContext);\n  } else {\n    sandbox = new SnapshotSandbox(appName);\n  }\n  // some side effect could be be invoked while bootstrapping, such as dynamic stylesheet injection with style-loader, especially during the development phase\n  var bootstrappingFreers = patchAtBootstrapping(appName, elementGetter, sandbox, scopedCSS, excludeAssetFilter, speedySandBox);\n  // mounting freers are one-off and should be re-init at every mounting time\n  var mountingFreers = [];\n  var sideEffectsRebuilders = [];\n  return {\n    instance: sandbox,\n    /**\n     * 沙箱被 mount\n     * 可能是从 bootstrap 状态进入的 mount\n     * 也可能是从 unmount 之后再次唤醒进入 mount\n     */\n    mount: function mount() {\n      return _asyncToGenerator( /*#__PURE__*/_regeneratorRuntime.mark(function _callee() {\n        var sideEffectsRebuildersAtBootstrapping, sideEffectsRebuildersAtMounting;\n        return _regeneratorRuntime.wrap(function _callee$(_context) {\n          while (1) {\n            switch (_context.prev = _context.next) {\n              case 0:\n                /* ------------------------------------------ 因为有上下文依赖（window），以下代码执行顺序不能变 ------------------------------------------ */\n                /* ------------------------------------------ 1. 启动/恢复 沙箱------------------------------------------ */\n                sandbox.active();\n                sideEffectsRebuildersAtBootstrapping = sideEffectsRebuilders.slice(0, bootstrappingFreers.length);\n                sideEffectsRebuildersAtMounting = sideEffectsRebuilders.slice(bootstrappingFreers.length); // must rebuild the side effects which added at bootstrapping firstly to recovery to nature state\n                if (sideEffectsRebuildersAtBootstrapping.length) {\n                  sideEffectsRebuildersAtBootstrapping.forEach(function (rebuild) {\n                    return rebuild();\n                  });\n                }\n                /* ------------------------------------------ 2. 开启全局变量补丁 ------------------------------------------*/\n                // render 沙箱启动时开始劫持各类全局监听，尽量不要在应用初始化阶段有 事件监听/定时器 等副作用\n                mountingFreers = patchAtMounting(appName, elementGetter, sandbox, scopedCSS, excludeAssetFilter, speedySandBox);\n                /* ------------------------------------------ 3. 重置一些初始化时的副作用 ------------------------------------------*/\n                // 存在 rebuilder 则表明有些副作用需要重建\n                if (sideEffectsRebuildersAtMounting.length) {\n                  sideEffectsRebuildersAtMounting.forEach(function (rebuild) {\n                    return rebuild();\n                  });\n                }\n                // clean up rebuilders\n                sideEffectsRebuilders = [];\n              case 7:\n              case \"end\":\n                return _context.stop();\n            }\n          }\n        }, _callee);\n      }))();\n    },\n    /**\n     * 恢复 global 状态，使其能回到应用加载之前的状态\n     */\n    unmount: function unmount() {\n      return _asyncToGenerator( /*#__PURE__*/_regeneratorRuntime.mark(function _callee2() {\n        return _regeneratorRuntime.wrap(function _callee2$(_context2) {\n          while (1) {\n            switch (_context2.prev = _context2.next) {\n              case 0:\n                // record the rebuilders of window side effects (event listeners or timers)\n                // note that the frees of mounting phase are one-off as it will be re-init at next mounting\n                sideEffectsRebuilders = [].concat(_toConsumableArray(bootstrappingFreers), _toConsumableArray(mountingFreers)).map(function (free) {\n                  return free();\n                });\n                sandbox.inactive();\n              case 2:\n              case \"end\":\n                return _context2.stop();\n            }\n          }\n        }, _callee2);\n      }))();\n    }\n  };\n}"], "mappings": ";;;;;AAAA,OAAOA,kBAAkB,MAAM,8CAA8C;AAC7E,OAAOC,iBAAiB,MAAM,6CAA6C;AAC3E,OAAOC,mBAAmB,MAAM,4BAA4B;AAC5D,OAAOC,aAAa,MAAM,kBAAkB;AAC5C,SAASC,oBAAoB,EAAEC,eAAe,QAAQ,YAAY;AAClE,OAAOC,YAAY,MAAM,gBAAgB;AACzC,OAAOC,eAAe,MAAM,mBAAmB;AAC/C,SAASC,oBAAoB,QAAQ,UAAU;AAC/C,SAASC,GAAG,QAAQ,YAAY;AAChC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASC,sBAAsB,CAACC,OAAO,EAAEC,aAAa,EAAEC,SAAS,EAAEC,eAAe,EAAEC,kBAAkB,EAAEC,aAAa,EAAEC,aAAa,EAAE;EAC3I,IAAIC,OAAO;EACX,IAAIC,MAAM,CAACC,KAAK,EAAE;IAChBF,OAAO,GAAGJ,eAAe,GAAG,IAAIX,aAAa,CAACQ,OAAO,EAAEK,aAAa,CAAC,GAAG,IAAIV,YAAY,CAACK,OAAO,EAAEK,aAAa,CAAC;EAClH,CAAC,MAAM;IACLE,OAAO,GAAG,IAAIX,eAAe,CAACI,OAAO,CAAC;EACxC;EACA;EACA,IAAIU,mBAAmB,GAAGjB,oBAAoB,CAACO,OAAO,EAAEC,aAAa,EAAEM,OAAO,EAAEL,SAAS,EAAEE,kBAAkB,EAAEE,aAAa,CAAC;EAC7H;EACA,IAAIK,cAAc,GAAG,EAAE;EACvB,IAAIC,qBAAqB,GAAG,EAAE;EAC9B,OAAO;IACLC,QAAQ,EAAEN,OAAO;IACjB;AACJ;AACA;AACA;AACA;IACIO,KAAK,EAAE,SAASA,KAAK,GAAG;MACtB,OAAOxB,iBAAiB,EAAE,aAAaC,mBAAmB,CAACwB,IAAI,CAAC,SAASC,OAAO,GAAG;QACjF,IAAIC,oCAAoC,EAAEC,+BAA+B;QACzE,OAAO3B,mBAAmB,CAAC4B,IAAI,CAAC,SAASC,QAAQ,CAACC,QAAQ,EAAE;UAC1D,OAAO,CAAC,EAAE;YACR,QAAQA,QAAQ,CAACC,IAAI,GAAGD,QAAQ,CAACE,IAAI;cACnC,KAAK,CAAC;gBACJ;gBACA;gBACAhB,OAAO,CAACiB,MAAM,EAAE;gBAChBP,oCAAoC,GAAGL,qBAAqB,CAACa,KAAK,CAAC,CAAC,EAAEf,mBAAmB,CAACgB,MAAM,CAAC;gBACjGR,+BAA+B,GAAGN,qBAAqB,CAACa,KAAK,CAACf,mBAAmB,CAACgB,MAAM,CAAC,CAAC,CAAC;gBAC3F,IAAIT,oCAAoC,CAACS,MAAM,EAAE;kBAC/CT,oCAAoC,CAACU,OAAO,CAAC,UAAUC,OAAO,EAAE;oBAC9D,OAAOA,OAAO,EAAE;kBAClB,CAAC,CAAC;gBACJ;gBACA;gBACA;gBACAjB,cAAc,GAAGjB,eAAe,CAACM,OAAO,EAAEC,aAAa,EAAEM,OAAO,EAAEL,SAAS,EAAEE,kBAAkB,EAAEE,aAAa,CAAC;gBAC/G;gBACA;gBACA,IAAIY,+BAA+B,CAACQ,MAAM,EAAE;kBAC1CR,+BAA+B,CAACS,OAAO,CAAC,UAAUC,OAAO,EAAE;oBACzD,OAAOA,OAAO,EAAE;kBAClB,CAAC,CAAC;gBACJ;gBACA;gBACAhB,qBAAqB,GAAG,EAAE;cAC5B,KAAK,CAAC;cACN,KAAK,KAAK;gBACR,OAAOS,QAAQ,CAACQ,IAAI,EAAE;YAAC;UAE7B;QACF,CAAC,EAAEb,OAAO,CAAC;MACb,CAAC,CAAC,CAAC,EAAE;IACP,CAAC;IACD;AACJ;AACA;IACIc,OAAO,EAAE,SAASA,OAAO,GAAG;MAC1B,OAAOxC,iBAAiB,EAAE,aAAaC,mBAAmB,CAACwB,IAAI,CAAC,SAASgB,QAAQ,GAAG;QAClF,OAAOxC,mBAAmB,CAAC4B,IAAI,CAAC,SAASa,SAAS,CAACC,SAAS,EAAE;UAC5D,OAAO,CAAC,EAAE;YACR,QAAQA,SAAS,CAACX,IAAI,GAAGW,SAAS,CAACV,IAAI;cACrC,KAAK,CAAC;gBACJ;gBACA;gBACAX,qBAAqB,GAAG,EAAE,CAACsB,MAAM,CAAC7C,kBAAkB,CAACqB,mBAAmB,CAAC,EAAErB,kBAAkB,CAACsB,cAAc,CAAC,CAAC,CAACwB,GAAG,CAAC,UAAUC,IAAI,EAAE;kBACjI,OAAOA,IAAI,EAAE;gBACf,CAAC,CAAC;gBACF7B,OAAO,CAAC8B,QAAQ,EAAE;cACpB,KAAK,CAAC;cACN,KAAK,KAAK;gBACR,OAAOJ,SAAS,CAACJ,IAAI,EAAE;YAAC;UAE9B;QACF,CAAC,EAAEE,QAAQ,CAAC;MACd,CAAC,CAAC,CAAC,EAAE;IACP;EACF,CAAC;AACH"}]}