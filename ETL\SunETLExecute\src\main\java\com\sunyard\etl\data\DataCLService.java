package com.sunyard.etl.data;


import com.sunyard.etl.data.impl.DataClearService;
import com.sunyard.etl.system.common.Constants;
import com.sunyard.etl.system.common.GlobVar;
import com.sunyard.etl.system.dao.FlCheckoffDao;
import com.sunyard.etl.system.dao.SmGlobalSetDao;
import com.sunyard.etl.system.dao.impl.FlCheckoffDaoImpl;
import com.sunyard.etl.system.dao.impl.SmGlobalSetDaoImpl;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.IJobHandler;
import com.xxl.job.core.handler.annotation.JobHandler;
import org.springframework.stereotype.Service;
import org.sunyard.util.string.StringUtil;

import java.util.concurrent.Executors;

import static com.sunyard.etl.system.common.GlobVar.dataClearList;

/**
 * 归档程序
 */
@JobHandler(value = "DataCLService",name="数据清理")
@Service
public class DataCLService extends IJobHandler {

    private FlCheckoffDao flCheckoffDao = new FlCheckoffDaoImpl();
    private SmGlobalSetDao smGlobalSetDao = new SmGlobalSetDaoImpl();


    @Override
    public ReturnT<String> execute(String jobId, String... params) throws Exception {
        GlobVar.dataClearPool = Executors
                .newFixedThreadPool(GlobVar.maxServices);

        String DATA_CLEAN_DATE = smGlobalSetDao.getParam("SYSTEM","DATA_CLEAN_DATE");
        if(StringUtil.checkNull(DATA_CLEAN_DATE)){
            DATA_CLEAN_DATE = "0";
        }
            //清理主控
            dataClearList = flCheckoffDao.getCheckOffByCheckFlagDao(
                    Constants.ALL_CHECK_OFF_DA, Integer.valueOf(DATA_CLEAN_DATE));

            for (int i = 0; i < dataClearList.size(); i++) {
                if (dataClearList.get(i).getProcessing() == Constants.NOT_PROCESS) {
                    dataClearList.get(i).setProcessing(Constants.IN_PROCESS);
                    GlobVar.dataClearPool.execute(new DataClearService(dataClearList.get(i)));
                }
            }

        return  ReturnT.SUCCESS;

    }
}
