package com.sunyard.etl.nps.dao;

import java.sql.SQLException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.sunyard.util.date.DateUtil;
import org.sunyard.util.dbutil.DBHandler;

import com.sun.rowset.CachedRowSetImpl;
import com.sunyard.etl.nps.common.Parameters;
import com.sunyard.etl.nps.model.BpTmpdata1Tb;
import com.sunyard.etl.nps.model.NpImageData;
import com.sunyard.etl.nps.model.NpOutputDef;
import com.sunyard.etl.nps.model.BpTmpbatchTb;
import com.sunyard.etl.nps.orm.NpImageDataOrm;
import com.sunyard.etl.system.common.Constants;
import com.sunyard.etl.system.orm.Orm;

public class OutputNopaperDao {

	private Orm<NpImageData> npImageDataOrm = new NpImageDataOrm();

	/**
	 * 获取的需要输出到ADMS5的无纸化任务配置
	 * 
	 * <AUTHOR> 2017年7月11日
	 * @param target
	 * @return
	 * @throws SQLException
	 */
	public List<NpOutputDef> getOutputDef(String target) throws SQLException {
		List<NpOutputDef> npOutputDefList = new ArrayList<NpOutputDef>();
		DBHandler dbHandler = new DBHandler(Constants.CONN_POOL_NAME);
		CachedRowSetImpl rs = null;
		String sql = "SELECT * FROM NP_OUTPUT_DEF_TB T WHERE T.TARGET = '"
				+ target + "'";
		rs = dbHandler.queryRs(sql);
		while (rs.next()) {
			NpOutputDef npOutputDef = new NpOutputDef();
			npOutputDef.setNopaperId(rs.getString("NOPAPER_ID"));
			npOutputDef.setBusinessId(Integer.parseInt(rs
					.getString("BUSINESS_ID")));
			npOutputDef.setDataSourceId(rs.getString("DATA_SOURCE_ID"));
			npOutputDefList.add(npOutputDef);
		}
		return npOutputDefList;
	}

	/**
	 * 获取可插入的批次以及业务序号
	 * 
	 * <AUTHOR> 2017年7月11日
	 * @param nopaperId
	 * @return 返回一个MAP<批次表对象，业务序号数组>
	 * @throws SQLException
	 */
	public Map<BpTmpbatchTb, String[]> getBatch2(String nopaperId)
			throws SQLException {
		Map<BpTmpbatchTb, String[]> map = new HashMap<BpTmpbatchTb, String[]>();
		BpTmpbatchTb bpTmpbatchTb = new BpTmpbatchTb();
		DBHandler dbHandler = new DBHandler(Constants.CONN_POOL_NAME);
		CachedRowSetImpl rs = null;
		String sql = "SELECT * FROM NP_BUSINESS_DATA_TB T WHERE T.NOPAPER_ID = '"
				+ nopaperId + "'";
		rs = dbHandler.queryRs(sql);
		while (rs.next()) {
			map.containsKey(bpTmpbatchTb);

			// 不重新实例对象是否会有问题-Z
			bpTmpbatchTb.setBatchId(rs.getString("BATCH_ID"));
			bpTmpbatchTb.setOccurDate(rs.getString("OCCUR_DATE"));
			bpTmpbatchTb.setSiteNo(rs.getString("OCCUR_DATE"));
			bpTmpbatchTb.setOperatorNo(rs.getString("OPERATOR_NO"));
			bpTmpbatchTb.setBusinessId(Integer.parseInt(rs
					.getString("BUSINESS_ID")));
			bpTmpbatchTb.setBatchCommit("1");
			bpTmpbatchTb.setInputDate(DateUtil.getNow());
			bpTmpbatchTb.setProgressFlag(Parameters.ADMS_PROCESS_FLAG);
			bpTmpbatchTb.setInputTime(DateUtil.getNewDate("HHmmss"));
		}
		return map;
	}

	/**
	 * 
	 * @Title getBatch
	 * @Description 获取批次 返回一个MAP<批次号，业务流水序号列表>
	 * <AUTHOR> 2017年7月12日
	 * @param nopaperId
	 * @return
	 * @throws SQLException
	 */
	public Map<String, List<String>> getBatchInfo(String nopaperId)
			throws SQLException {
		Map<String, List<String>> map = new HashMap<String, List<String>>();
		DBHandler dbHandler = new DBHandler(Constants.CONN_POOL_NAME);
		CachedRowSetImpl rs = null;
		String sql = "SELECT * FROM NP_BUSINESS_DATA_TB T WHERE T.NOPAPER_ID = '"
				+ nopaperId + "'";
		rs = dbHandler.queryRs(sql);
		while (rs.next()) {
			String batchId = rs.getString("BATCH_ID");
			if (map.containsKey(batchId)) {
				map.get(batchId).add(rs.getString("BUSINESS_NO"));
			} else {
				List<String> businessNoList = new ArrayList<String>();
				businessNoList.add(rs.getString("BUSINESS_NO"));
				map.put(batchId, businessNoList);
			}
		}
		return map;
	}

	/**
	 * 
	 * @Title generateBatch
	 * @Description 根据批次号生成批次信息
	 * <AUTHOR> 2017年7月12日
	 * @param batchId
	 * @return
	 * @throws SQLException
	 */
	public BpTmpbatchTb generateBatch(String batchId) throws SQLException {
		BpTmpbatchTb bpTmpbatchTb = new BpTmpbatchTb();
		DBHandler dbHandler = new DBHandler(Constants.CONN_POOL_NAME);
		CachedRowSetImpl rs = null;
		String sql = "SELECT OCCUR_DATE,SITE_NO,OPERATOR_NO FROM NP_BUSINESS_DATA_TB  WHERE BATCH_ID = '"
				+ batchId + "'";
		rs = dbHandler.queryRs(sql);
		while (rs.next()) {
			bpTmpbatchTb.setBatchId(rs.getString("BATCH_ID"));
			bpTmpbatchTb.setOccurDate(rs.getString("OCCUR_DATE"));
			bpTmpbatchTb.setSiteNo(rs.getString("OCCUR_DATE"));
			bpTmpbatchTb.setOperatorNo(rs.getString("OPERATOR_NO"));
			bpTmpbatchTb.setBusinessId(Integer.parseInt(rs
					.getString("BUSINESS_ID")));
			bpTmpbatchTb.setBatchCommit("1");
			bpTmpbatchTb.setInputDate(DateUtil.getNow());
			bpTmpbatchTb.setProgressFlag(Parameters.ADMS_PROCESS_FLAG);
			bpTmpbatchTb.setInputTime(DateUtil.getNewDate("HHmmss"));
		}
		return bpTmpbatchTb;
	}

	/**
	 * 
	 * @Title generateData1
	 * @Description 生成图像数据表的数据
	 * <AUTHOR> 2017年7月12日
	 * @param batchId
	 * @param businessNoList
	 * @return
	 * @throws SQLException
	 */
	public List<BpTmpdata1Tb> generateData1(String batchId,
			List<String> businessNoList) throws SQLException {
		String businessNoString = businessNoList.toString();
		businessNoString = businessNoString.substring(1,
				businessNoString.length() - 1);
		List<BpTmpdata1Tb> bpTmpdata1TbList = new ArrayList<BpTmpdata1Tb>();
		DBHandler dbHandler = new DBHandler(Constants.CONN_POOL_NAME);
		CachedRowSetImpl rs = null;
		String sql = "SELECT * FROM NP_IMAGE_DATA_TB T WHERE T.BUSINESS_NO IN ("
				+ businessNoList + ") ORDER BY BUSINESS_NO";
		rs = dbHandler.queryRs(sql);
		int inccodeinBatch = 1;
		String businessNo = "";
		String psLevel = "0";
		while (rs.next()) {
			BpTmpdata1Tb bpTmpdata1Tb = new BpTmpdata1Tb();
			NpImageData npImageData = npImageDataOrm.orm(rs);
			// businessNo是新的，则将这张凭证设为主件
			if (!businessNo.equals(npImageData.getBusiDataNo())) {
				psLevel = "1";
				businessNo = npImageData.getBusiDataNo();
			} else {
				psLevel = "0";
			}
			bpTmpdata1Tb.setBatchId(batchId);
			bpTmpdata1Tb.setInccodeinBatch(inccodeinBatch);
			bpTmpdata1Tb.setPsLevel(psLevel);
			bpTmpdata1Tb.setPrimaryInccodein(0);
			bpTmpdata1Tb.setFormName(npImageData.getFormName());
			bpTmpdata1Tb.setProcessState(Parameters.ADMS_PROCESS_STATE);
			bpTmpdata1Tb.setFlowId(getFlowId(businessNo));
			bpTmpdata1Tb.setFileName(npImageData.getFileName());
			bpTmpdata1Tb.setBackFileName(npImageData.getBackFileName());
			bpTmpdata1TbList.add(bpTmpdata1Tb);
			inccodeinBatch++;
		}
		return bpTmpdata1TbList;
	}

	/**
	 * 
	 * @Title getBatch
	 * @Description 获取可接入的批次号列表
	 * <AUTHOR> 2017年7月12日
	 * @param nopaperId
	 * @return
	 * @throws SQLException
	 */
	public List<String> getBatchId(String nopaperId) throws SQLException {
		List<String> list = new ArrayList<String>();
		DBHandler dbHandler = new DBHandler(Constants.CONN_POOL_NAME);
		CachedRowSetImpl rs = null;
		String sql = "SELECT DISTINCT BATCH_ID  FROM NP_BUSINESS_DATA_TB T WHERE T.NOPAPER_ID = ? "
				+ "AND T.FLAG = '1' AND T.ADMS5 = '0' ORDER BY BATCH_ID";
		rs = dbHandler.queryRs(sql, nopaperId);
		while (rs.next()) {
			list.add(rs.getString(1));
		}
		return list;
	}

	/**
	 * 
	 * @Title getBatch
	 * @Description 提取批次
	 * <AUTHOR> 2017年8月8日
	 * @param nopaperId
	 * @return
	 * @throws SQLException
	 */
	public List<String> getBatch(String nopaperId) throws SQLException {
		List<String> list = new ArrayList<String>();
		DBHandler dbHandler = new DBHandler(Constants.CONN_POOL_NAME);
		CachedRowSetImpl rs = null;
		String sql = "SELECT DISTINCT T.OCCUR_DATE,T.SITE_NO,T.OPERATOR_NO FROM NP_BUSINESS_DATA_TB  T WHERE T.NOPAPER_ID = ? AND T.ERROR_FLAG = '0'";
		rs = dbHandler.queryRs(sql, nopaperId);
		while (rs.next()) {
			list.add(rs.getString(1) + "," + rs.getString(2) + ","
					+ rs.getString(3));
		}
		return list;
	}

	/**
	 * 
	 * @Title
	 * @Description 根据BUSINESS_NO找到流水号
	 * <AUTHOR> 2017年7月12日
	 * @param batchId
	 * @return
	 * @throws SQLException
	 */
	public String getFlowId(String businessNo) throws SQLException {
		DBHandler dbHandler = new DBHandler(Constants.CONN_POOL_NAME);
		CachedRowSetImpl rs = null;
		String sql = "SELECT T.FLOW_ID FROM NP_BUSINESS_DATA_TB T WHERE  T.BUSINESS_NO = '"
				+ businessNo + "'";
		rs = dbHandler.queryRs(sql);
		while (rs.next()) {
			return rs.getString(1);
		}
		return "";
	}

	/**
	 * 
	 * @Title
	 * @Description 提取ADMS5需要的数据集合
	 * <AUTHOR> 2017年7月19日
	 * @param occurDate
	 * @param nopaperId
	 * @return
	 * @throws SQLException
	 */
	public List<NpImageData> getADMS5DataList(String batchId)
			throws SQLException {
		List<NpImageData> list = new ArrayList<NpImageData>();

		DBHandler dbHandler = new DBHandler(Constants.CONN_POOL_NAME);
		CachedRowSetImpl rs = null;
		String sql = "SELECT A.OCCUR_DATE,A.SITE_NO,A.OPERATOR_NO, A.FLOW_ID ,A.CONTENT_ID, A.INDEX_VALUE, B.* "
				+ "FROM NP_BUSINESS_DATA_TB A ,NP_IMAGE_DATA_TB B "
				+ "WHERE A.BUSI_DATA_NO = B.BUSI_DATA_NO AND A.BATCH_ID = ?  "
				+ "ORDER BY B.BUSI_DATA_NO, B.ORDER_NUM";
		rs = dbHandler.queryRs(sql, batchId);
		while (rs.next()) {
			NpImageData npImageData = npImageDataOrm.orm(rs);
			// 关联查询非表字段
			npImageData.setOccurDate(rs.getString("OCCUR_DATE"));
			npImageData.setSiteNo(rs.getString("SITE_NO"));
			npImageData.setOperator(rs.getString("OPERATOR_NO"));
			npImageData.setFlowId(rs.getString("FLOW_ID"));
			npImageData.setContentId(rs.getString("CONTENT_ID"));
			npImageData.setIndexValue(rs.getString("INDEX_VALUE"));
			list.add(npImageData);
		}
		return list;
	}

	/**
	 * 
	 * @Title getBusiInfo
	 * @Description 根据批次号获取日期网点柜员
	 * <AUTHOR> 2017年7月19日
	 * @param batchId
	 * @return
	 * @throws SQLException
	 */
	public String getBusiInfo(String batchId) throws SQLException {
		String busiInfo = "";
		DBHandler dbHandler = new DBHandler(Constants.CONN_POOL_NAME);
		CachedRowSetImpl rs = null;
		String sql = "SELECT DISTINCT  T.OCCUR_DATE, T.SITE_NO, T.OPERATOR_NO FROM NP_BUSINESS_DATA_TB T  "
				+ "WHERE T.BATCH_ID = ?";
		rs = dbHandler.queryRs(sql, batchId);
		while (rs.next()) {
			busiInfo = rs.getString(1) + "," + rs.getString(2) + ","
					+ rs.getString(3);
		}
		return busiInfo;
	}

	/**
	 * 
	 * @Title getImgInfo
	 * @Description 根据busiDataNo从np_image_data_tb中找到图像信息数据
	 * <AUTHOR> 2017年8月9日
	 * @param busiDataNo
	 * @return
	 * @throws SQLException
	 */
	public List<NpImageData> getImgInfo(String busiDataNo) throws SQLException {
		List<NpImageData> list = new ArrayList<NpImageData>();
		DBHandler dbHandler = new DBHandler(Constants.CONN_POOL_NAME);
		CachedRowSetImpl rs = null;
		String sql = "SELECT * FROM NP_IMAGE_DATA_TB  T WHERE  T.BUSI_DATA_NO = ?";
		rs = dbHandler.queryRs(sql, busiDataNo);
		while (rs.next()) {
			NpImageData npImageData = npImageDataOrm.orm(rs);
			list.add(npImageData);
		}
		return list;
	}

	/**
	 * 
	 * @Title updateFlag
	 * @Description 更新状态
	 * <AUTHOR> 2017年7月20日
	 * @return
	 * @throws SQLException
	 */
	public boolean updateFlag(String batchId, String target) {
		DBHandler dbHandler = new DBHandler(Constants.CONN_POOL_NAME);
		String sql = "UPDATE NP_BUSINESS_DATA_TB T SET T." + target
				+ " = '1'  WHERE T.BATCH_ID = ?";
		try {
			int i = dbHandler.execute(sql, batchId);
			if (i < 0)
				return false;
		} catch (SQLException e) {
			e.printStackTrace();
			return false;
		}
		return true;
	}

	public boolean updateFlagByBusiDataNo(String batchId, String target) {
		DBHandler dbHandler = new DBHandler(Constants.CONN_POOL_NAME);
		String sql = "UPDATE NP_BUSINESS_DATA_TB T SET T." + target
				+ " = '1'  WHERE T.BUSI_DATA_NO = ?";
		try {
			int i = dbHandler.execute(sql, batchId);
			if (i < 0)
				return false;
		} catch (SQLException e) {
			e.printStackTrace();
			return false;
		}
		return true;
	}

	public boolean updateBatchId(String batchId, String occurDate,
			String siteNo, String operatorNo, String nopaperId)
			throws SQLException {
		int i = 0;
		DBHandler dbHandler = new DBHandler(Constants.CONN_POOL_NAME);
		String sql = "UPDATE NP_BUSINESS_DATA_TB T SET T.BATCH_ID = ? "
				+ "WHERE T.OCCUR_DATE = ? AND  T.SITE_NO = ? "
				+ "AND T.OPERATOR_NO = ? AND T.NOPAPER_ID = ? "
				+ "AND T.ERROR_FLAG = '0' AND T.FLAG = '1'";
		i = dbHandler.execute(sql, batchId, occurDate, siteNo, operatorNo,
				nopaperId);
		if (i == 0) {
			return false;
		} else {
			return true;
		}
	}

}
