package com.sunyard.etl.nps.common;

import java.sql.SQLException;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.sunyard.etl.nps.dao.NpGlobalSetTbDao;
import com.sunyard.etl.nps.service.base.ECMService;
import com.sunyard.etl.nps.service.base.FTPService;
import com.sunyard.etl.system.model.SmServiceReg;
import com.sunyard.etl.system.dao.SmServiceRegDao;
import com.sunyard.etl.system.dao.impl.SmServiceRegDaoImpl;
import com.xxl.job.core.log.XxlJobLogger;
/**
 * 程序启动初始化
 * <AUTHOR>
 * 2017年10月25日下午5:10:49
 */
public class SystemInit {
	private static String tableName = NPSContants.NPS_BASE_LOG;
	protected final Logger log = LoggerFactory.getLogger(getClass());
	
	private static NpGlobalSetTbDao npGlobalSetTbDao = new NpGlobalSetTbDao();

	/**
	 * 服务初始化
	 * @throws SQLException 
	 */
	public static String serviceInit() throws SQLException{
		/*
		 * 加载线程配置
		 */
		String maxServices = npGlobalSetTbDao.getKeyValueByKeyName("THREADS");
		if (null != maxServices && "" != maxServices) {
			XxlJobLogger.log("进行多线程初始化......");
			NPSContants.maxServices = Integer.parseInt(maxServices);
		}
		return "SUCCESS|成功";
	}
	
}




