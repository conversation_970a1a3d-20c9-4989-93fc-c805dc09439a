{"remainingRequest": "E:\\华南区项目\\中国银行\\开发区\\科技公司运营平台\\前端\\dopUnify\\node_modules\\thread-loader\\dist\\cjs.js!E:\\华南区项目\\中国银行\\开发区\\科技公司运营平台\\前端\\dopUnify\\node_modules\\babel-loader\\lib\\index.js!E:\\华南区项目\\中国银行\\开发区\\科技公司运营平台\\前端\\dopUnify\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!E:\\华南区项目\\中国银行\\开发区\\科技公司运营平台\\前端\\dopUnify\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\华南区项目\\中国银行\\开发区\\科技公司运营平台\\前端\\dopUnify\\src\\layout\\components\\TagsView\\index.vue?vue&type=script&lang=js&", "dependencies": [{"path": "E:\\华南区项目\\中国银行\\开发区\\科技公司运营平台\\前端\\dopUnify\\src\\layout\\components\\TagsView\\index.vue", "mtime": 1721631954662}, {"path": "E:\\华南区项目\\中国银行\\开发区\\科技公司运营平台\\前端\\dopUnify\\babel.config.js", "mtime": 1721631954662}, {"path": "E:\\华南区项目\\中国银行\\开发区\\科技公司运营平台\\前端\\dopUnify\\.babelrc", "mtime": 1721631954662}, {"path": "E:\\华南区项目\\中国银行\\开发区\\科技公司运营平台\\前端\\dopUnify\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "E:\\华南区项目\\中国银行\\开发区\\科技公司运营平台\\前端\\dopUnify\\node_modules\\thread-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "E:\\华南区项目\\中国银行\\开发区\\科技公司运营平台\\前端\\dopUnify\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "E:\\华南区项目\\中国银行\\开发区\\科技公司运营平台\\前端\\dopUnify\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "E:\\华南区项目\\中国银行\\开发区\\科技公司运营平台\\前端\\dopUnify\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1655715099000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:aW1wb3J0IF9jcmVhdGVGb3JPZkl0ZXJhdG9ySGVscGVyIGZyb20gIkU6L1x1NTM0RVx1NTM1N1x1NTMzQVx1OTg3OVx1NzZFRS9cdTRFMkRcdTU2RkRcdTk0RjZcdTg4NEMvXHU1RjAwXHU1M0QxXHU1MzNBL1x1NzlEMVx1NjI4MFx1NTE2Q1x1NTNGOFx1OEZEMFx1ODQyNVx1NUU3M1x1NTNGMC9cdTUyNERcdTdBRUYvZG9wVW5pZnkvbm9kZV9tb2R1bGVzL0BiYWJlbC9ydW50aW1lL2hlbHBlcnMvZXNtL2NyZWF0ZUZvck9mSXRlcmF0b3JIZWxwZXIuanMiOwppbXBvcnQgX3RvQ29uc3VtYWJsZUFycmF5IGZyb20gIkU6L1x1NTM0RVx1NTM1N1x1NTMzQVx1OTg3OVx1NzZFRS9cdTRFMkRcdTU2RkRcdTk0RjZcdTg4NEMvXHU1RjAwXHU1M0QxXHU1MzNBL1x1NzlEMVx1NjI4MFx1NTE2Q1x1NTNGOFx1OEZEMFx1ODQyNVx1NUU3M1x1NTNGMC9cdTUyNERcdTdBRUYvZG9wVW5pZnkvbm9kZV9tb2R1bGVzL0BiYWJlbC9ydW50aW1lL2hlbHBlcnMvZXNtL3RvQ29uc3VtYWJsZUFycmF5LmpzIjsKaW1wb3J0IF9vYmplY3RTcHJlYWQgZnJvbSAiRTovXHU1MzRFXHU1MzU3XHU1MzNBXHU5ODc5XHU3NkVFL1x1NEUyRFx1NTZGRFx1OTRGNlx1ODg0Qy9cdTVGMDBcdTUzRDFcdTUzM0EvXHU3OUQxXHU2MjgwXHU1MTZDXHU1M0Y4XHU4RkQwXHU4NDI1XHU1RTczXHU1M0YwL1x1NTI0RFx1N0FFRi9kb3BVbmlmeS9ub2RlX21vZHVsZXMvQGJhYmVsL3J1bnRpbWUvaGVscGVycy9lc20vb2JqZWN0U3ByZWFkMi5qcyI7CmltcG9ydCAiY29yZS1qcy9tb2R1bGVzL2VzLm9iamVjdC50by1zdHJpbmcuanMiOwppbXBvcnQgImNvcmUtanMvbW9kdWxlcy93ZWIuZG9tLWNvbGxlY3Rpb25zLmZvci1lYWNoLmpzIjsKaW1wb3J0ICJjb3JlLWpzL21vZHVsZXMvZXMuZnVuY3Rpb24ubmFtZS5qcyI7CmltcG9ydCAiY29yZS1qcy9tb2R1bGVzL2VzLmFycmF5LmNvbmNhdC5qcyI7CmltcG9ydCAiY29yZS1qcy9tb2R1bGVzL2VzLnJlZ2V4cC5leGVjLmpzIjsKaW1wb3J0ICJjb3JlLWpzL21vZHVsZXMvZXMuc3RyaW5nLnJlcGxhY2UuanMiOwppbXBvcnQgImNvcmUtanMvbW9kdWxlcy9lcy5zdHJpbmcubWF0Y2guanMiOwppbXBvcnQgImNvcmUtanMvbW9kdWxlcy9lcy5hcnJheS5maWx0ZXIuanMiOwppbXBvcnQgImNvcmUtanMvbW9kdWxlcy9lcy5hcnJheS5zbGljZS5qcyI7CmltcG9ydCAiY29yZS1qcy9tb2R1bGVzL2VzLmFycmF5LnJlZHVjZS5qcyI7CmltcG9ydCAiY29yZS1qcy9tb2R1bGVzL2VzLmFycmF5LmZyb20uanMiOwppbXBvcnQgImNvcmUtanMvbW9kdWxlcy9lcy5zdHJpbmcuaXRlcmF0b3IuanMiOwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwoKaW1wb3J0IHsgbWFwR2V0dGVycyB9IGZyb20gJ3Z1ZXgnOwppbXBvcnQgU2Nyb2xsUGFuZSBmcm9tICcuL1Njcm9sbFBhbmUnOwppbXBvcnQgc2NyZWVuZnVsbCBmcm9tICdzY3JlZW5mdWxsJzsKaW1wb3J0IHBhdGggZnJvbSAncGF0aCc7CmltcG9ydCB7IGNvbW1vbkJsYW5rIH0gZnJvbSAnQC91dGlscy9jb21tb24nOwovLyBpbXBvcnQgXyBmcm9tICdsb2Rhc2gnCgp2YXIgdGltZW91dDsKZXhwb3J0IGRlZmF1bHQgewogIGNvbXBvbmVudHM6IHsKICAgIFNjcm9sbFBhbmU6IFNjcm9sbFBhbmUKICB9LAogIGRhdGE6IGZ1bmN0aW9uIGRhdGEoKSB7CiAgICByZXR1cm4gewogICAgICB2aXNpYmxlOiBmYWxzZSwKICAgICAgdG9wOiAwLAogICAgICBsZWZ0OiAwLAogICAgICBzZWxlY3RlZFRhZzoge30sCiAgICAgIGFmZml4VGFnczogW10sCiAgICAgIHRhYk1vcmU6IHRydWUsCiAgICAgIHBvc2l0aW9uT2JqOiB7CiAgICAgICAgbGVmdDogJzAnLAogICAgICAgIHRvcDogJzAnCiAgICAgIH0sCiAgICAgIHNob3dUYWJzTnVtOiAxMDAsCiAgICAgIHRhYk1vcmVMaXN0OiBmYWxzZSwKICAgICAgYVdpZHRoOiAwLAogICAgICAvLyDmiYDmnInoj5zljZXmoIfnrb7nmoTlrr3luqYKICAgICAgdmlzaXRlZFZpZXdzQ29weTogW10KICAgICAgLy8gaXNTaG93U2VhcmNoOiBmYWxzZSAvLyDmmK/lkKblvLnlh7rmkJzntKLmoYYKICAgIH07CiAgfSwKCiAgY29tcHV0ZWQ6IF9vYmplY3RTcHJlYWQoewogICAgdmlzaXRlZFZpZXdzOiBmdW5jdGlvbiB2aXNpdGVkVmlld3MoKSB7CiAgICAgIHJldHVybiB0aGlzLiRzdG9yZS5zdGF0ZS50YWdzVmlldy52aXNpdGVkVmlld3M7CiAgICB9LAogICAgcm91dGVzOiBmdW5jdGlvbiByb3V0ZXMoKSB7CiAgICAgIHJldHVybiB0aGlzLiRzdG9yZS5zdGF0ZS5wZXJtaXNzaW9uLnJvdXRlczsKICAgIH0KICB9LCBtYXBHZXR0ZXJzKFsndGhlbWUnLCAnaXNTaG93U2VhcmNoJ10pKSwKICB3YXRjaDogewogICAgJHJvdXRlOiBmdW5jdGlvbiAkcm91dGUoKSB7CiAgICAgIHRoaXMuYWRkVGFncygpOwogICAgICB0aGlzLm1vdmVUb0N1cnJlbnRUYWcoKTsKICAgIH0sCiAgICB2aXNpYmxlOiBmdW5jdGlvbiB2aXNpYmxlKHZhbHVlKSB7CiAgICAgIGlmICh2YWx1ZSkgewogICAgICAgIGRvY3VtZW50LmJvZHkuYWRkRXZlbnRMaXN0ZW5lcignY2xpY2snLCB0aGlzLmNsb3NlTWVudSk7CiAgICAgIH0gZWxzZSB7CiAgICAgICAgZG9jdW1lbnQuYm9keS5yZW1vdmVFdmVudExpc3RlbmVyKCdjbGljaycsIHRoaXMuY2xvc2VNZW51KTsKICAgICAgfQogICAgfSwKICAgIHRhYk1vcmVMaXN0OiBmdW5jdGlvbiB0YWJNb3JlTGlzdCh2YWx1ZSkgewogICAgICBpZiAodmFsdWUpIHsKICAgICAgICBkb2N1bWVudC5ib2R5LmFkZEV2ZW50TGlzdGVuZXIoJ2NsaWNrJywgdGhpcy5jbG9zZU1lbnVMaXN0KTsKICAgICAgfSBlbHNlIHsKICAgICAgICBkb2N1bWVudC5ib2R5LnJlbW92ZUV2ZW50TGlzdGVuZXIoJ2NsaWNrJywgdGhpcy5jbG9zZU1lbnVMaXN0KTsKICAgICAgfQogICAgfSwKICAgIC8vIOebkeWQrHRhYuagj+agh+etvuWPmOWMlgogICAgdmlzaXRlZFZpZXdzOiBmdW5jdGlvbiB2aXNpdGVkVmlld3ModmFsdWUsIG9sZFZhbHVlKSB7CiAgICAgIC8vIGNvbnNvbGUubG9nKCfnm5HlkKx0YWLmoI/moIfnrb7lj5jljJYnLCB2YWx1ZSkKICAgICAgdGhpcy50YWJSZXppZSgpOwogICAgfSwKICAgICckc3RvcmUuZ2V0dGVycy50aXRsZXMucGF0aCc6IHsKICAgICAgaGFuZGxlcjogZnVuY3Rpb24gaGFuZGxlcih2YWwpIHsKICAgICAgICBpZiAodmFsID09PSB0aGlzLiRyb3V0ZS5wYXRoKSB7CiAgICAgICAgICB0aGlzLmNsb3NlU2VsZWN0ZWRUYWcodGhpcy4kc3RvcmUuZ2V0dGVycy50aXRsZXMpOwogICAgICAgICAgdGhpcy4kc3RvcmUuY29tbWl0KCdjb21tb24vVGVzdCcsIHt9KTsKICAgICAgICB9CiAgICAgIH0sCiAgICAgIGRlZXA6IHRydWUKICAgIH0KICB9LAogIGJlZm9yZU1vdW50OiBmdW5jdGlvbiBiZWZvcmVNb3VudCgpIHt9LAogIC8vIOmUgOavgeWvueW6lOiHquWumuS5ieS6i+S7tgogIGJlZm9yZURlc3Ryb3k6IGZ1bmN0aW9uIGJlZm9yZURlc3Ryb3koKSB7CiAgICBjbGVhclRpbWVvdXQodGltZW91dCk7CiAgfSwKICBtb3VudGVkOiBmdW5jdGlvbiBtb3VudGVkKCkgewogICAgdGhpcy5pbml0VGFncygpOwogICAgdGhpcy5hZGRUYWdzKCk7CiAgICB0aGlzLmFkZFRhZ3MoKTsKICAgIHRoaXMudGFiUmV6aWUoKTsKICAgIHdpbmRvdy5hZGRFdmVudExpc3RlbmVyKCdyZXNpemUnLCB0aGlzLnRhYlJlemllKTsKICB9LAogIG1ldGhvZHM6IHsKICAgIHNjcmVlbkZ1bGw6IGZ1bmN0aW9uIHNjcmVlbkZ1bGwoKSB7CiAgICAgIGlmICghc2NyZWVuZnVsbC5pc0VuYWJsZWQpIHsKICAgICAgICB0aGlzLiRtZXNzYWdlKHsKICAgICAgICAgIG1lc3NhZ2U6ICd5b3UgYnJvd3NlciBjYW4gbm90IHdvcmsnLAogICAgICAgICAgdHlwZTogJ3dhcm5pbmcnCiAgICAgICAgfSk7CiAgICAgICAgcmV0dXJuIGZhbHNlOwogICAgICB9CiAgICAgIHNjcmVlbmZ1bGwucmVxdWVzdChkb2N1bWVudC5xdWVyeVNlbGVjdG9yKCcuYXBwLW1haW4nKSk7CiAgICAgIHNjcmVlbmZ1bGwuZXhpdCgpOwogICAgfSwKICAgIGlzQWN0aXZlOiBmdW5jdGlvbiBpc0FjdGl2ZShyb3V0ZSkgewogICAgICByZXR1cm4gcm91dGUucGF0aCA9PT0gdGhpcy4kcm91dGUucGF0aDsKICAgIH0sCiAgICBpc0FmZml4OiBmdW5jdGlvbiBpc0FmZml4KHRhZykgewogICAgICByZXR1cm4gdGFnLm1ldGEgJiYgdGFnLm1ldGEuYWZmaXg7CiAgICB9LAogICAgZmlsdGVyQWZmaXhUYWdzOiBmdW5jdGlvbiBmaWx0ZXJBZmZpeFRhZ3Mocm91dGVzKSB7CiAgICAgIHZhciBfdGhpcyA9IHRoaXM7CiAgICAgIHZhciBiYXNlUGF0aCA9IGFyZ3VtZW50cy5sZW5ndGggPiAxICYmIGFyZ3VtZW50c1sxXSAhPT0gdW5kZWZpbmVkID8gYXJndW1lbnRzWzFdIDogJy8nOwogICAgICB2YXIgdGFncyA9IFtdOwogICAgICByb3V0ZXMuZm9yRWFjaChmdW5jdGlvbiAocm91dGUpIHsKICAgICAgICBpZiAocm91dGUubWV0YSAmJiByb3V0ZS5tZXRhLmFmZml4KSB7CiAgICAgICAgICB2YXIgdGFnUGF0aCA9IHBhdGgucmVzb2x2ZShiYXNlUGF0aCwgcm91dGUucGF0aCk7CiAgICAgICAgICB0YWdzLnB1c2goewogICAgICAgICAgICBmdWxsUGF0aDogdGFnUGF0aCwKICAgICAgICAgICAgcGF0aDogdGFnUGF0aCwKICAgICAgICAgICAgbmFtZTogcm91dGUubmFtZSwKICAgICAgICAgICAgbWV0YTogX29iamVjdFNwcmVhZCh7fSwgcm91dGUubWV0YSkKICAgICAgICAgIH0pOwogICAgICAgIH0KICAgICAgICBpZiAocm91dGUuY2hpbGRyZW4pIHsKICAgICAgICAgIHZhciB0ZW1wVGFncyA9IF90aGlzLmZpbHRlckFmZml4VGFncyhyb3V0ZS5jaGlsZHJlbiwgcm91dGUucGF0aCk7CiAgICAgICAgICBpZiAodGVtcFRhZ3MubGVuZ3RoID49IDEpIHsKICAgICAgICAgICAgdGFncyA9IFtdLmNvbmNhdChfdG9Db25zdW1hYmxlQXJyYXkodGFncyksIF90b0NvbnN1bWFibGVBcnJheSh0ZW1wVGFncykpOwogICAgICAgICAgfQogICAgICAgIH0KICAgICAgfSk7CiAgICAgIHJldHVybiB0YWdzOwogICAgfSwKICAgIGluaXRUYWdzOiBmdW5jdGlvbiBpbml0VGFncygpIHsKICAgICAgdmFyIGFmZml4VGFncyA9IHRoaXMuYWZmaXhUYWdzID0gdGhpcy5maWx0ZXJBZmZpeFRhZ3ModGhpcy5yb3V0ZXMpOwogICAgICB2YXIgX2l0ZXJhdG9yID0gX2NyZWF0ZUZvck9mSXRlcmF0b3JIZWxwZXIoYWZmaXhUYWdzKSwKICAgICAgICBfc3RlcDsKICAgICAgdHJ5IHsKICAgICAgICBmb3IgKF9pdGVyYXRvci5zKCk7ICEoX3N0ZXAgPSBfaXRlcmF0b3IubigpKS5kb25lOykgewogICAgICAgICAgdmFyIHRhZyA9IF9zdGVwLnZhbHVlOwogICAgICAgICAgLy8gTXVzdCBoYXZlIHRhZyBuYW1lCiAgICAgICAgICBpZiAodGFnLm5hbWUpIHsKICAgICAgICAgICAgdGhpcy4kc3RvcmUuZGlzcGF0Y2goJ3RhZ3NWaWV3L2FkZFZpc2l0ZWRWaWV3JywgdGFnKTsKICAgICAgICAgIH0KICAgICAgICB9CiAgICAgIH0gY2F0Y2ggKGVycikgewogICAgICAgIF9pdGVyYXRvci5lKGVycik7CiAgICAgIH0gZmluYWxseSB7CiAgICAgICAgX2l0ZXJhdG9yLmYoKTsKICAgICAgfQogICAgfSwKICAgIGFkZFRhZ3M6IGZ1bmN0aW9uIGFkZFRhZ3MoKSB7CiAgICAgIHZhciBuYW1lID0gdGhpcy4kcm91dGUubmFtZTsKICAgICAgaWYgKG5hbWUpIHsKICAgICAgICB0aGlzLiRzdG9yZS5kaXNwYXRjaCgndGFnc1ZpZXcvYWRkVmlldycsIHRoaXMuJHJvdXRlKTsKICAgICAgICB0aGlzLnJlc2l6ZVN1YigpOyAvLyDlrZDlt6XnqIvmoLzlvI/ljJbpobXpnaLlsLrlr7gKICAgICAgfQoKICAgICAgcmV0dXJuIGZhbHNlOwogICAgfSwKICAgIG1vdmVUb0N1cnJlbnRUYWc6IGZ1bmN0aW9uIG1vdmVUb0N1cnJlbnRUYWcoKSB7CiAgICAgIHZhciBfdGhpczIgPSB0aGlzOwogICAgICB2YXIgdGFncyA9IHRoaXMuJHJlZnMudGFnOwogICAgICB0aGlzLiRuZXh0VGljayhmdW5jdGlvbiAoKSB7CiAgICAgICAgdmFyIF9pdGVyYXRvcjIgPSBfY3JlYXRlRm9yT2ZJdGVyYXRvckhlbHBlcih0YWdzKSwKICAgICAgICAgIF9zdGVwMjsKICAgICAgICB0cnkgewogICAgICAgICAgZm9yIChfaXRlcmF0b3IyLnMoKTsgIShfc3RlcDIgPSBfaXRlcmF0b3IyLm4oKSkuZG9uZTspIHsKICAgICAgICAgICAgdmFyIHRhZyA9IF9zdGVwMi52YWx1ZTsKICAgICAgICAgICAgaWYgKHRhZy50by5wYXRoID09PSBfdGhpczIuJHJvdXRlLnBhdGgpIHsKICAgICAgICAgICAgICBfdGhpczIuJHJlZnMuc2Nyb2xsUGFuZS5tb3ZlVG9UYXJnZXQodGFnKTsKICAgICAgICAgICAgICAvLyB3aGVuIHF1ZXJ5IGlzIGRpZmZlcmVudCB0aGVuIHVwZGF0ZQogICAgICAgICAgICAgIGlmICh0YWcudG8uZnVsbFBhdGggIT09IF90aGlzMi4kcm91dGUuZnVsbFBhdGgpIHsKICAgICAgICAgICAgICAgIF90aGlzMi4kc3RvcmUuZGlzcGF0Y2goJ3RhZ3NWaWV3L3VwZGF0ZVZpc2l0ZWRWaWV3JywgX3RoaXMyLiRyb3V0ZSk7CiAgICAgICAgICAgICAgfQogICAgICAgICAgICAgIGJyZWFrOwogICAgICAgICAgICB9CiAgICAgICAgICB9CiAgICAgICAgfSBjYXRjaCAoZXJyKSB7CiAgICAgICAgICBfaXRlcmF0b3IyLmUoZXJyKTsKICAgICAgICB9IGZpbmFsbHkgewogICAgICAgICAgX2l0ZXJhdG9yMi5mKCk7CiAgICAgICAgfQogICAgICB9KTsKICAgIH0sCiAgICAvKioKICAgICAqIOWIt+aWsOW9k+WJjemAiemhueWNoQogICAgICogQHBhcmFtIHtPYmplY3R9IHZpZXcg6Lev55Sx5L+h5oGvCiAgICAgKi8KICAgIHJlZnJlc2hTZWxlY3RlZFRhZzogZnVuY3Rpb24gcmVmcmVzaFNlbGVjdGVkVGFnKHZpZXcpIHsKICAgICAgdmFyIF90aGlzMyA9IHRoaXM7CiAgICAgIHRoaXMuJHN0b3JlLmRpc3BhdGNoKCd0YWdzVmlldy9kZWxDYWNoZWRWaWV3JywgdmlldykudGhlbihmdW5jdGlvbiAoYSkgewogICAgICAgIHZhciBmdWxsUGF0aCA9IHZpZXcuZnVsbFBhdGg7CiAgICAgICAgX3RoaXMzLiRuZXh0VGljayhmdW5jdGlvbiAoKSB7CiAgICAgICAgICBfdGhpczMuJHJvdXRlci5yZXBsYWNlKHsKICAgICAgICAgICAgcGF0aDogJy9yZWRpcmVjdCcgKyBmdWxsUGF0aAogICAgICAgICAgfSk7CiAgICAgICAgfSk7CiAgICAgIH0pOwogICAgfSwKICAgIC8qKgogICAgICog5YWz6Zet6YCJ5oup55qEdGFiCiAgICAgKiBAcGFyYW0ge09iamVjdH0geHh4eHh4CiAgICAgKi8KICAgIGNsb3NlU2VsZWN0ZWRUYWc6IGZ1bmN0aW9uIGNsb3NlU2VsZWN0ZWRUYWcodmlldykgewogICAgICB2YXIgX3RoaXM0ID0gdGhpczsKICAgICAgdmFyIGlzRW50ZW5kID0gISF2aWV3Lm5hbWUubWF0Y2goL1xkL2cpOyAvLyDmmK/lkKbkuLrlpJbns7vnu58KICAgICAgaWYgKGlzRW50ZW5kKSB7CiAgICAgICAgc2Vzc2lvblN0b3JhZ2Uuc2V0SXRlbSgncmVzZXRGbGFnJywgdHJ1ZSk7CiAgICAgIH0KICAgICAgaWYgKHZpZXcubWV0YS5tb2R1bGVfbm8gPT09ICcjWDYwODgyJyB8fCB2aWV3Lm1ldGEubW9kdWxlX25vID09PSAnI1g2MDkwMicpIHsKICAgICAgICAvLyDkuJrliqHlrqHmoLgg5oiWIOW9seWDj+mHh+mbhgogICAgICAgIHZhciBpZnJhbWVXaW5MaXN0ID0gdGhpcy4kc3RvcmUuZ2V0dGVycy5leHRlbmRMaXN0LmZpbHRlcihmdW5jdGlvbiAoaXRlbSkgewogICAgICAgICAgcmV0dXJuIGl0ZW0udGl0bGUgPT09IHZpZXcubmFtZTsKICAgICAgICB9KTsKICAgICAgICBpZiAoIWNvbW1vbkJsYW5rKGlmcmFtZVdpbkxpc3QpKSB7CiAgICAgICAgICBpZnJhbWVXaW5MaXN0WzBdLmlmcmFtZVdpbi5wb3N0TWVzc2FnZSh7CiAgICAgICAgICAgIHR5cGU6ICdkb3BVbmlmeScsCiAgICAgICAgICAgIHByb2plY3ROYW1lOiAnZnJlZUJhdGNoJywKICAgICAgICAgICAgaWQ6IHZpZXcubWV0YS5tb2R1bGVfbm8KICAgICAgICAgIH0sICcqJyk7CiAgICAgICAgICB0aGlzLiRzdG9yZS5jb21taXQoJ2Zsb3dQYXRoL0RFTEVDVF9FWFRFTkRfTElTVCcsIGlmcmFtZVdpbkxpc3RbMF0udGl0bGUpOwogICAgICAgIH0KICAgICAgfQogICAgICB0aW1lb3V0ID0gc2V0VGltZW91dChmdW5jdGlvbiAoKSB7CiAgICAgICAgX3RoaXM0LiRzdG9yZS5kaXNwYXRjaCgndGFnc1ZpZXcvZGVsVmlldycsIHZpZXcpLnRoZW4oZnVuY3Rpb24gKF9yZWYpIHsKICAgICAgICAgIHZhciB2aXNpdGVkVmlld3MgPSBfcmVmLnZpc2l0ZWRWaWV3czsKICAgICAgICAgIGlmIChfdGhpczQuaXNBY3RpdmUodmlldykpIHsKICAgICAgICAgICAgX3RoaXM0LnRvTGFzdFZpZXcodmlzaXRlZFZpZXdzLCB2aWV3KTsKICAgICAgICAgIH0KICAgICAgICB9KTsKICAgICAgfSk7CiAgICB9LAogICAgY2xvc2VPdGhlcnNUYWdzOiBmdW5jdGlvbiBjbG9zZU90aGVyc1RhZ3MoKSB7CiAgICAgIHZhciBfdGhpczUgPSB0aGlzOwogICAgICB0aGlzLiRyb3V0ZXIucHVzaCh0aGlzLnNlbGVjdGVkVGFnKTsKICAgICAgdGhpcy4kc3RvcmUuZGlzcGF0Y2goJ3RhZ3NWaWV3L2RlbE90aGVyc1ZpZXdzJywgdGhpcy5zZWxlY3RlZFRhZykudGhlbihmdW5jdGlvbiAoKSB7CiAgICAgICAgX3RoaXM1Lm1vdmVUb0N1cnJlbnRUYWcoKTsKICAgICAgfSk7CiAgICB9LAogICAgY2xvc2VBbGxUYWdzOiBmdW5jdGlvbiBjbG9zZUFsbFRhZ3ModmlldykgewogICAgICB2YXIgX3RoaXM2ID0gdGhpczsKICAgICAgdGhpcy4kc3RvcmUuZGlzcGF0Y2goJ3RhZ3NWaWV3L2RlbEFsbFZpZXdzJykudGhlbihmdW5jdGlvbiAoX3JlZjIpIHsKICAgICAgICB2YXIgdmlzaXRlZFZpZXdzID0gX3JlZjIudmlzaXRlZFZpZXdzOwogICAgICAgIGlmIChfdGhpczYuYWZmaXhUYWdzLnNvbWUoZnVuY3Rpb24gKHRhZykgewogICAgICAgICAgcmV0dXJuIHRhZy5wYXRoID09PSB2aWV3LnBhdGg7CiAgICAgICAgfSkpIHsKICAgICAgICAgIHJldHVybjsKICAgICAgICB9CiAgICAgICAgX3RoaXM2LnRvTGFzdFZpZXcodmlzaXRlZFZpZXdzLCB2aWV3KTsKICAgICAgICBfdGhpczYudGFiTW9yZSA9IGZhbHNlOyAvLyDpmpDol4/mm7TlpJroj5zljZXmjInpkq4KICAgICAgfSk7CiAgICB9LAogICAgdG9MYXN0VmlldzogZnVuY3Rpb24gdG9MYXN0Vmlldyh2aXNpdGVkVmlld3MsIHZpZXcpIHsKICAgICAgdmFyIGxhdGVzdFZpZXcgPSB2aXNpdGVkVmlld3Muc2xpY2UoLTEpWzBdOwogICAgICBpZiAobGF0ZXN0VmlldykgewogICAgICAgIHRoaXMuJHJvdXRlci5wdXNoKGxhdGVzdFZpZXcuZnVsbFBhdGgpOwogICAgICB9IGVsc2UgewogICAgICAgIC8vIG5vdyB0aGUgZGVmYXVsdCBpcyB0byByZWRpcmVjdCB0byB0aGUgaG9tZSBwYWdlIGlmIHRoZXJlIGlzIG5vIHRhZ3MtdmlldywKICAgICAgICAvLyB5b3UgY2FuIGFkanVzdCBpdCBhY2NvcmRpbmcgdG8geW91ciBuZWVkcy4KICAgICAgICBpZiAodmlldy5uYW1lID09PSAnSG9tZScpIHsKICAgICAgICAgIC8vIHRvIHJlbG9hZCBob21lIHBhZ2UKICAgICAgICAgIHRoaXMuJHJvdXRlci5yZXBsYWNlKHsKICAgICAgICAgICAgcGF0aDogJy9yZWRpcmVjdCcgKyB2aWV3LmZ1bGxQYXRoCiAgICAgICAgICB9KTsKICAgICAgICB9IGVsc2UgewogICAgICAgICAgdGhpcy4kcm91dGVyLnB1c2goJy8nKTsKICAgICAgICB9CiAgICAgIH0KICAgIH0sCiAgICBvcGVuTWVudTogZnVuY3Rpb24gb3Blbk1lbnUodGFnLCBlKSB7CiAgICAgIHZhciBtZW51TWluV2lkdGggPSAxMDU7CiAgICAgIHZhciBvZmZzZXRMZWZ0ID0gdGhpcy4kZWwuZ2V0Qm91bmRpbmdDbGllbnRSZWN0KCkubGVmdDsgLy8gY29udGFpbmVyIG1hcmdpbiBsZWZ0CiAgICAgIHZhciBvZmZzZXRXaWR0aCA9IHRoaXMuJGVsLm9mZnNldFdpZHRoOyAvLyBjb250YWluZXIgd2lkdGgKICAgICAgdmFyIG1heExlZnQgPSBvZmZzZXRXaWR0aCAtIG1lbnVNaW5XaWR0aDsgLy8gbGVmdCBib3VuZGFyeQogICAgICB2YXIgbGVmdCA9IGUuY2xpZW50WCAtIG9mZnNldExlZnQgKyAxNTsgLy8gMTU6IG1hcmdpbiByaWdodAoKICAgICAgaWYgKGxlZnQgPiBtYXhMZWZ0KSB7CiAgICAgICAgdGhpcy5sZWZ0ID0gbWF4TGVmdDsKICAgICAgfSBlbHNlIHsKICAgICAgICB0aGlzLmxlZnQgPSBsZWZ0OwogICAgICB9CiAgICAgIHRoaXMudG9wID0gZS5jbGllbnRZOwogICAgICB0aGlzLnZpc2libGUgPSB0cnVlOwogICAgICB0aGlzLnNlbGVjdGVkVGFnID0gdGFnOwogICAgfSwKICAgIGNsb3NlTWVudTogZnVuY3Rpb24gY2xvc2VNZW51KCkgewogICAgICB0aGlzLnZpc2libGUgPSBmYWxzZTsKICAgIH0sCiAgICAvKioKICAgICAqIOWFs+mXreiPnOWNleahhgogICAgICogQHBhcmFtIHtPYmplY3R9IHh4eHh4eAogICAgICovCiAgICBjbG9zZU1lbnVMaXN0OiBmdW5jdGlvbiBjbG9zZU1lbnVMaXN0KGUpIHsKICAgICAgaWYgKCdzaG93TW9yZS1tZW51UG9pbnQnLmluZGV4T2YoZS50YXJnZXQuY2xhc3NOYW1lKSAhPT0gLTEpIHsKICAgICAgICByZXR1cm47CiAgICAgIH0KICAgICAgdGhpcy50YWJNb3JlTGlzdCA9IGZhbHNlOwogICAgfSwKICAgIGhhbmRsZVNjcm9sbDogZnVuY3Rpb24gaGFuZGxlU2Nyb2xsKCkgewogICAgICB0aGlzLmNsb3NlTWVudSgpOwogICAgfSwKICAgIC8qKgogICAgICog5qCH562+6aG15bC65a+45pS55Y+YCiAgICAgKi8KICAgIHRhYlJlemllOiBmdW5jdGlvbiB0YWJSZXppZSgpIHsKICAgICAgdmFyIF90aGlzNyA9IHRoaXM7CiAgICAgIC8vIGNvbnNvbGUubG9nKCfmoIfnrb7pobXlsLrlr7jmlLnlj5gnKQogICAgICB0aGlzLiRuZXh0VGljayhmdW5jdGlvbiAoKSB7CiAgICAgICAgX3RoaXM3LnRhYk1vcmVMaXN0ID0gZmFsc2U7CiAgICAgICAgdmFyIGRvbSA9IGRvY3VtZW50LnF1ZXJ5U2VsZWN0b3IoJy50YWdzLXZpZXctY29udGFpbmVyIC5lbC1zY3JvbGxiYXJfX3ZpZXcnKTsKICAgICAgICB2YXIgb2Zmc2V0V2lkdGggPSBkb20ub2Zmc2V0V2lkdGg7CiAgICAgICAgdmFyIGFkb21zID0gZG9tLnF1ZXJ5U2VsZWN0b3JBbGwoJy50YWdzLXZpZXctaXRlbScpOwogICAgICAgIC8vIOiOt+WPlnRhYuagh+etvueahOaJgOacieWuveW6pgogICAgICAgIHZhciBhV2lkdGggPSBBcnJheS5mcm9tKGFkb21zKS5yZWR1Y2UoZnVuY3Rpb24gKHRvdGFsLCBjdXJyZW50VmFsdWUsIGN1cnJlbnRJbmRleCwgYXJyYXkpIHsKICAgICAgICAgIGlmIChjdXJyZW50SW5kZXggPT09IDApIHsKICAgICAgICAgICAgLy8g56ys5LiA5Liq5qCH562+CiAgICAgICAgICAgIHRvdGFsID0gdG90YWwgKyAxNSArIGN1cnJlbnRWYWx1ZS5vZmZzZXRXaWR0aDsKICAgICAgICAgIH0gZWxzZSBpZiAoY3VycmVudEluZGV4ID09PSBhcnJheS5sZW5ndGggLSAxKSB7CiAgICAgICAgICAgIC8vIOacgOWQjuS4gOS4quagh+etvgogICAgICAgICAgICB0b3RhbCA9IHRvdGFsICsgMTUgKyBjdXJyZW50VmFsdWUub2Zmc2V0V2lkdGggKyA1OwogICAgICAgICAgfSBlbHNlIHsKICAgICAgICAgICAgdG90YWwgPSB0b3RhbCArIDUgKyBjdXJyZW50VmFsdWUub2Zmc2V0V2lkdGg7CiAgICAgICAgICB9CiAgICAgICAgICByZXR1cm4gdG90YWw7CiAgICAgICAgfSwgMCk7CiAgICAgICAgLy8gY29uc29sZS5sb2coJ29mZnNldFdpZHRoJywgb2Zmc2V0V2lkdGgpCiAgICAgICAgLy8gY29uc29sZS5sb2coJ2FXaWR0aCcsIGFXaWR0aCkKICAgICAgICBpZiAoYVdpZHRoID4gb2Zmc2V0V2lkdGgpIHsKICAgICAgICAgIF90aGlzNy50YWJNb3JlID0gdHJ1ZTsKICAgICAgICB9IGVsc2UgewogICAgICAgICAgX3RoaXM3LnRhYk1vcmUgPSBmYWxzZTsKICAgICAgICB9CiAgICAgIH0pOwogICAgfSwKICAgIC8qKgogICAgICog5pi+56S65omA5pyJ6I+c5Y2V5YiX6KGoCiAgICAgKi8KICAgIHNob3dNb3JlOiBmdW5jdGlvbiBzaG93TW9yZSgpIHsKICAgICAgdmFyIGRvbUMgPSBkb2N1bWVudC5xdWVyeVNlbGVjdG9yKCcudGFncy12aWV3LWNvbnRhaW5lciAuZWwtc2Nyb2xsYmFyX192aWV3Jyk7CiAgICAgIHZhciBvZmZzZXRXaWR0aCA9IGRvbUMub2Zmc2V0V2lkdGg7CiAgICAgIHZhciBkb20gPSB0aGlzLiRyZWZzLnNob3dNb3JlOwogICAgICB2YXIgb2Zmc2V0TGVmdCA9IGRvbS5vZmZzZXRMZWZ0LAogICAgICAgIG9mZnNldFRvcCA9IGRvbS5vZmZzZXRUb3A7CiAgICAgIHZhciBsZWZ0TWVudVdpZHRoID0gMjIwOwogICAgICBpZiAoIXRoaXMuJHN0b3JlLnN0YXRlLmFwcC5zaWRlYmFyLm9wZW5lZCkgewogICAgICAgIGxlZnRNZW51V2lkdGggPSA1NDsKICAgICAgfQogICAgICB0aGlzLnBvc2l0aW9uT2JqLmxlZnQgPSBvZmZzZXRMZWZ0ICsgb2Zmc2V0V2lkdGggKyBsZWZ0TWVudVdpZHRoICsgOCArICdweCc7CiAgICAgIHRoaXMucG9zaXRpb25PYmoudG9wID0gb2Zmc2V0VG9wICsgNDAgKyAncHgnOwogICAgICB0aGlzLnRhYk1vcmVMaXN0ID0gdHJ1ZTsKICAgIH0sCiAgICAvKioKICAgICAq5qC85byP5YyW5a2Q5bel56iL6aG16Z2i5bC65a+4IDIwMjMuMTIuMTEKICAgICovCiAgICByZXNpemVTdWI6IGZ1bmN0aW9uIHJlc2l6ZVN1YigpIHsKICAgICAgdmFyIF9tYXRjaGVkVCRwcm9wcywgX21hdGNoZWRUJHByb3BzJGRlZmF1OwogICAgICAvKiDnvLrpmbfvvJrov4flv6vnmoTljZXlh7vlt6bkvqfot6/nlLHvvIzlnKjml6fns7vnu5/kuK3liqDovb3nmoTpobXpnaLmoLflvI/plJnkubHpl67popggMjAyMy4xMi4xMSBiZWdpbiovCiAgICAgIHZhciBtYXRjaGVkVCA9IHRoaXMuJHJvdXRlLm1hdGNoZWQ7CiAgICAgIHZhciBudW1UID0gbWF0Y2hlZFQubGVuZ3RoOwogICAgICB2YXIgcm91dGVyUGF0aCA9IChfbWF0Y2hlZFQkcHJvcHMgPSBtYXRjaGVkVFtudW1UIC0gMV0ucHJvcHMpID09PSBudWxsIHx8IF9tYXRjaGVkVCRwcm9wcyA9PT0gdm9pZCAwID8gdm9pZCAwIDogKF9tYXRjaGVkVCRwcm9wcyRkZWZhdSA9IF9tYXRjaGVkVCRwcm9wcy5kZWZhdWx0KSA9PT0gbnVsbCB8fCBfbWF0Y2hlZFQkcHJvcHMkZGVmYXUgPT09IHZvaWQgMCA/IHZvaWQgMCA6IF9tYXRjaGVkVCRwcm9wcyRkZWZhdS5wYXRoOwogICAgICBpZiAoY29tbW9uQmxhbmsocm91dGVyUGF0aCkpIHsKICAgICAgICByZXR1cm47CiAgICAgIH0KICAgICAgaWYgKHJvdXRlclBhdGguaW5kZXhPZignc3RhdGljL2h0bWwnKSAhPT0gLTEpIHsKICAgICAgICAvLyDliKTmlq3mmK/lkKbkuLrml6fns7vnu5/kuK3nmoTlnLDlnYAKICAgICAgICAvLyBzZXRUaW1lb3V0KCgpID0+IHsKICAgICAgICB0aGlzLiRuZXh0VGljayhmdW5jdGlvbiAoKSB7CiAgICAgICAgICBmb3IgKHZhciBpZnJhbWVJbmRleCBpbiB3aW5kb3cpIHsKICAgICAgICAgICAgLy8g6I635Y+WaWZyYW1l5LiL55qEd2luZG9377yM5pON5L2c5a2Q5bel56iL5Lit55qE5pa55rOVCiAgICAgICAgICAgIGlmICghaXNOYU4ocGFyc2VJbnQoaWZyYW1lSW5kZXgpKSkgewogICAgICAgICAgICAgIHZhciBfd2luZG93JGlmcmFtZUluZGV4JGk7CiAgICAgICAgICAgICAgaWYgKCFPYmplY3QucHJvdG90eXBlLmhhc093blByb3BlcnR5LmNhbGwod2luZG93W2lmcmFtZUluZGV4XSwgJ2lmcmFtZUluZm9EYXRhJykpIHsKICAgICAgICAgICAgICAgIGNvbnRpbnVlOwogICAgICAgICAgICAgIH0KICAgICAgICAgICAgICBpZiAoKChfd2luZG93JGlmcmFtZUluZGV4JGkgPSB3aW5kb3dbaWZyYW1lSW5kZXhdLmlmcmFtZUluZm9EYXRhKSA9PT0gbnVsbCB8fCBfd2luZG93JGlmcmFtZUluZGV4JGkgPT09IHZvaWQgMCA/IHZvaWQgMCA6IF93aW5kb3ckaWZyYW1lSW5kZXgkaS51c2VyLnJvdXRlTS5wYXRoKSA9PT0gcm91dGVyUGF0aCkgewogICAgICAgICAgICAgICAgLy8g5Li65pWw5a2X5YiZ5omn6KGM5Lul5LiL5pa55rOVCiAgICAgICAgICAgICAgICAvLyBkb2N1bWVudC5xdWVyeVNlbGVjdG9yQWxsKCcuZXh0ZW5kSWZyYW1lJylbaWZyYW1lSW5kZXhdLmNvbnRlbnRXaW5kb3cuY29udGVudFJlc2l6ZSgpCiAgICAgICAgICAgICAgICB3aW5kb3dbaWZyYW1lSW5kZXhdLmNvbnRlbnRSZXNpemUoKTsKICAgICAgICAgICAgICB9CiAgICAgICAgICAgIH0KICAgICAgICAgIH0KICAgICAgICB9KTsKICAgICAgICAvLyB9LCAzMDApCiAgICAgIH0KICAgICAgLyog57y66Zm377ya5L+u5aSN6L+H5b+r55qE5Y2V5Ye75bem5L6n6Lev55Sx77yM5Zyo5pen57O757uf5Lit5Yqg6L2955qE6aG16Z2i5qC35byP6ZSZ5Lmx6Zeu6aKYIDIwMjMuMTIuMTEgYmVnaW4qLwogICAgfQogIH0KfTs="}, null]}