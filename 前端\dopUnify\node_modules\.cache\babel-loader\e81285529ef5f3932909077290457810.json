{"remainingRequest": "D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\node_modules\\babel-loader\\lib\\index.js!D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\src\\components\\Layout\\Screenfull\\index.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\src\\components\\Layout\\Screenfull\\index.vue", "mtime": 1703583638403}, {"path": "D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\babel.config.js", "mtime": 1686019818359}, {"path": "D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\.babelrc", "mtime": 1705285078668}, {"path": "D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1655715099000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KCmltcG9ydCBzY3JlZW5mdWxsIGZyb20gJ3NjcmVlbmZ1bGwnOwpleHBvcnQgZGVmYXVsdCB7CiAgbmFtZTogJ1NjcmVlbmZ1bGwnLAogIGRhdGE6IGZ1bmN0aW9uIGRhdGEoKSB7CiAgICByZXR1cm4gewogICAgICBpc0Z1bGxzY3JlZW46IGZhbHNlCiAgICB9OwogIH0sCiAgbW91bnRlZDogZnVuY3Rpb24gbW91bnRlZCgpIHsKICAgIHRoaXMuaW5pdCgpOwogIH0sCiAgYmVmb3JlRGVzdHJveTogZnVuY3Rpb24gYmVmb3JlRGVzdHJveSgpIHsKICAgIHRoaXMuZGVzdHJveSgpOwogIH0sCiAgbWV0aG9kczogewogICAgY2xpY2s6IGZ1bmN0aW9uIGNsaWNrKCkgewogICAgICBpZiAoIXNjcmVlbmZ1bGwuaXNFbmFibGVkKSB7CiAgICAgICAgdGhpcy4kbWVzc2FnZSh7CiAgICAgICAgICBtZXNzYWdlOiAneW91IGJyb3dzZXIgY2FuIG5vdCB3b3JrJywKICAgICAgICAgIHR5cGU6ICd3YXJuaW5nJwogICAgICAgIH0pOwogICAgICAgIHJldHVybiBmYWxzZTsKICAgICAgfQogICAgICBpZiAoISF3aW5kb3cuQWN0aXZlWE9iamVjdCB8fCAnQWN0aXZlWE9iamVjdCcgaW4gd2luZG93KSB7CiAgICAgICAgLy8gaWXmtY/op4jlmajkuK3mlL7lpKflip/og73nmoTlhbzlrrnpl67popgKICAgICAgICBzY3JlZW5mdWxsLnRvZ2dsZShkb2N1bWVudC5nZXRFbGVtZW50c0J5VGFnTmFtZSgnYm9keScpWzBdKTsKICAgICAgfSBlbHNlIHsKICAgICAgICBzY3JlZW5mdWxsLnRvZ2dsZSgpOwogICAgICB9CiAgICB9LAogICAgY2hhbmdlOiBmdW5jdGlvbiBjaGFuZ2UoKSB7CiAgICAgIHRoaXMuaXNGdWxsc2NyZWVuID0gc2NyZWVuZnVsbC5pc0Z1bGxzY3JlZW47CiAgICB9LAogICAgaW5pdDogZnVuY3Rpb24gaW5pdCgpIHsKICAgICAgaWYgKHNjcmVlbmZ1bGwuaXNFbmFibGVkKSB7CiAgICAgICAgc2NyZWVuZnVsbC5vbignY2hhbmdlJywgdGhpcy5jaGFuZ2UpOwogICAgICB9CiAgICB9LAogICAgZGVzdHJveTogZnVuY3Rpb24gZGVzdHJveSgpIHsKICAgICAgaWYgKHNjcmVlbmZ1bGwuaXNFbmFibGVkKSB7CiAgICAgICAgc2NyZWVuZnVsbC5vZmYoJ2NoYW5nZScsIHRoaXMuY2hhbmdlKTsKICAgICAgfQogICAgfQogIH0KfTs="}, {"version": 3, "mappings": ";;;;;;;;;;;AAWA;AAEA;EACAA;EACAC;IACA;MACAC;IACA;EACA;EACAC;IACA;EACA;EACAC;IACA;EACA;EACAC;IACAC;MACA;QACA;UACAC;UACAC;QACA;QACA;MACA;MACA;QACA;QACAC;MACA;QACAA;MACA;IACA;IACAC;MACA;IACA;IACAC;MACA;QACAF;MACA;IACA;IACAG;MACA;QACAH;MACA;IACA;EACA;AACA", "names": ["name", "data", "isFullscreen", "mounted", "<PERSON><PERSON><PERSON><PERSON>", "methods", "click", "message", "type", "screenfull", "change", "init", "destroy"], "sourceRoot": "src/components/Layout/Screenfull", "sources": ["index.vue"], "sourcesContent": ["<template>\n  <div>\n    <sun-svg-icon\n      class=\"full\"\n      :icon-class=\"isFullscreen ? 'exit-fullscreen' : 'fullscreen'\"\n      @click=\"click\"\n    />\n  </div>\n</template>\n\n<script>\nimport screenfull from 'screenfull'\n\nexport default {\n  name: 'Screenfull',\n  data() {\n    return {\n      isFullscreen: false\n    }\n  },\n  mounted() {\n    this.init()\n  },\n  beforeDestroy() {\n    this.destroy()\n  },\n  methods: {\n    click() {\n      if (!screenfull.isEnabled) {\n        this.$message({\n          message: 'you browser can not work',\n          type: 'warning'\n        })\n        return false\n      }\n      if (!!window.ActiveXObject || 'ActiveXObject' in window) {\n        // ie浏览器中放大功能的兼容问题\n        screenfull.toggle(document.getElementsByTagName('body')[0])\n      } else {\n        screenfull.toggle()\n      }\n    },\n    change() {\n      this.isFullscreen = screenfull.isFullscreen\n    },\n    init() {\n      if (screenfull.isEnabled) {\n        screenfull.on('change', this.change)\n      }\n    },\n    destroy() {\n      if (screenfull.isEnabled) {\n        screenfull.off('change', this.change)\n      }\n    }\n  }\n}\n</script>\n\n<style lang=\"scss\" scoped>\n@import '~@/assets/scss/common/index';\n.sun-svg-icon {\n  &.full {\n    fill: $pro_iconColor;\n    width: 1.6rem;\n    height: 1.6rem;\n    &:hover {\n      fill: $primary;\n    }\n  }\n}\n</style>\n"]}]}