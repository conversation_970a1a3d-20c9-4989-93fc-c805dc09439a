{"remainingRequest": "D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\node_modules\\babel-loader\\lib\\index.js!D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\src\\views\\home\\component\\important\\index.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\src\\views\\home\\component\\important\\index.vue", "mtime": 1703583638871}, {"path": "D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\babel.config.js", "mtime": 1686019818359}, {"path": "D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\.babelrc", "mtime": 1705285078668}, {"path": "D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1655715099000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAgEA;AACA;AACA;AACA;AACA;AACA;EAAAA;EAAAC;AACA;AACA;AACA;AACA;AACA;AACA;EACAC;EACAC;IAAAC;IAAAC;EAAA;EACAC;IACA;MACAC;MAAA;MACAC;MACAC;MACAC;MAAA;MACAC;QACAC;QAAA;QACAC;QAAA;QACAC;QAAA;QACAC;UACA;UACAR;UACAS;QACA;;QACAC;UACAC;UAAA;UACAX;UAAA;UACAY;UAAA;UACAnB;UAAA;UACAoB;UAAA;UACAC;QACA;MACA;MACAC;MAAA;MACAC;MAAA;MACAC;MACAC;IACA;EACA;;EACAC;IACA;IACAC;MACA;QACA;MACA;IACA;EACA;EACAC;IAAA;IACA;MACAC;MACAC;QACAC;QACA;MACA;IACA;;IACAC;MACA;MACA;MACA;MACA;MACA;IACA;IACA;EACA;EACAC;IAAA;IACA;;IAEA;MACA;MACAC;QACA;UACAC;QACA;UACA;QACA;MACA;MACA;MACAD;QACA;UACA;QACA;UACA;QACA;MACA;IACA;EACA;EACA;EACAE;IACAD;IACAE;EACA;EACAC;IACA;MACAH;IACA;IACA;MACA;IACA;MACA;QACA;MACA;IACA;EACA;EACAI;IACAJ;EACA;EACAK;IACA;AACA;AACA;IACAC;MAAA;MACA;QACAC;QACAC;QACAC;QACAC;QACA;QACAC;QAAA;QACAC;MACA;MACAC;QACA;QACA;QACA;QACAxC;UACA;YACA,iBACAyC;cACAC;cAAA;cACAC;cAAA;cACAC;cAAA;cACAC;YACA,GACA;UACA;QACA;;QACA;QACA;UACA;UACA;UACA;UACA;YACA;YACA;YACAC;YACA;YACAA,oBACA,SACA,mDACA;YACAC;UACA;UACA;YACA;UACA;YACA;UACA;QACA;MACA;IACA;IACA;AACA;AACA;IACAC;MAAA;MACA;QACA;UACA;YACA;UACA;UACA;UACA;UACA;YACAC;YACAC;YACAC;UACA;UACA;QACA;MACA;IACA;IACA;AACA;AACA;IACAC;MAAA;MACA;QACA;MACA;MACAzB;MACA0B;QACA;QACA;QACA;QACAC;UACAC;QACA;QACA;UACA;QACA;QAEAC,iEACAA,iDACA;QAEA;MACA;IACA;IACA;AACA;AACA;IACAC;MAAA;MACA;QACA;UACAR;UACAS;UACAC;UACAR;UACAS;QACA;QACAlC;UACA;QACA;QACA;QACAA;UACA;QACA;MACA;IACA;IACA;AACA;AACA;IACAmC;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBACA;gBACA;gBACA;gBACA;gBACA;kBACAC;oBACAC;oBACApB;oBACAqB;oBACAC;kBACA;kBACA;gBACA;gBACA;gBACA;gBACAC;kBACA7C;kBACA0C;kBACA3B;kBACAF;kBACAW;gBACA;gBAAA;gBAAA,OACArD;cAAA;gBAAAM;gBACA;kBACA;kBACA;oBACA;kBACA;oBACA6B;kBACA;;kBAEA;kBACA;kBACA;kBACA;kBACA;kBACA;kBACA;kBACAwC;kBACAA;kBACAA;kBACAxD;kBACA;kBACA,gDACAyD,kBACA;kBACA;oBACA,8CACAC,uCACA;kBACA;oBACA;kBACA;kBACA;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IACA;IACA;AACA;AACA;AACA;AACA;AACA;IACAC;MAAA;MACA;MACA;QACAP;QACA3B;QACAF;QACAqC;QACAC;QACAC;MACA;MACAhF;QACA;MACA;IACA;IACA;IACAiF;MACA;IACA;IACA;AACA;IACAC;MACA;IACA;IACA;AACA;AACA;IACAC;MACA;MACA;QACA;MACA;QACA;MACA;MAEA;MACA;MACA;IACA;EACA;AACA", "names": ["readNum", "noticeModify", "name", "components", "SunNoticeDialog", "Print", "data", "title", "list", "tween", "li<PERSON><PERSON><PERSON>", "dialog", "visible", "btnCancle", "btnSubmit", "componentProps", "width", "noticeConfig", "imgSrc", "info", "content", "files", "horizontal", "num", "activeNum", "timer", "computed", "varColor", "created", "parameterList", "sysMap", "param_item", "getSysParam", "mounted", "document", "clearInterval", "<PERSON><PERSON><PERSON><PERSON>", "clearTimeout", "activated", "deactivated", "methods", "queryList", "organ_no", "role_no", "user_no", "organ_level", "pageSize", "currentPage", "<PERSON><PERSON><PERSON><PERSON>", "Object", "publish_time", "notice_title", "publish_organ", "publish_user", "fD", "ulDom", "scrollX", "duration", "x", "ease", "resizeBox", "timeout1", "Array", "li", "ulbox", "scrollY", "y", "delay", "repeat", "handleClick", "param<PERSON><PERSON>", "notice_id", "notice_content", "file_url", "readMsg", "infoObj", "val", "JSON", "firstPage", "read_time", "msg_no", "parpamData", "handlePrint", "dialogSumbit", "changeVisible"], "sourceRoot": "src/views/home/<USER>/important", "sources": ["index.vue"], "sourcesContent": ["<!--\r\n* 首页-重要信息\r\n-->\r\n<template>\r\n  <div class=\"homeContent importantMessage\">\r\n    <div class=\"homePageTitle2\">\r\n      <img src=\"../../../../assets/img/newVersion/somepeople.svg\" alt=\"\">\r\n      <img src=\"../../../../assets/img/newVersion/importItem.svg\" alt=\"\">\r\n    </div>\r\n    <div\r\n      id=\"importantContent\"\r\n      ref=\"messageBox\"\r\n      class=\"homePageBox2\"\r\n      :style=\"varColor\"\r\n    >\r\n      <ul\r\n        id=\"importantNotice\"\r\n        class=\"bottom\"\r\n        :class=\"{ horizontal: horizontal }\"\r\n      >\r\n        <li\r\n          v-for=\"item in list\"\r\n          :key=\"item.id\"\r\n          class=\"importantSpan\"\r\n          :style=\"{ width: horizontal ? liWidth + 'px' : '100%' }\"\r\n          @click=\"handleClick(item)\"\r\n        >\r\n          <!-- <span class=\"notice-icon importantIcon\" /> -->\r\n          <span class=\"Title\">\r\n            {{ item.notice_title }}\r\n            <span v-if=\"item.read_state === '0'\" class=\"center\">new</span>\r\n          </span>\r\n          <span class=\"Time\">{{ item.publish_time }}</span>\r\n        </li>\r\n      </ul>\r\n      <!-- <ul id=\"importantNoticeCopy\" class=\"bottom\" /> -->\r\n    </div>\r\n    <!-- <div id=\"copyScroll2\" class=\"homePageBox\" :style=\"varColor\" /> -->\r\n    <!--公告弹出框begin-->\r\n    <sun-notice-dialog\r\n      :dialog-config=\"dialog\"\r\n      @dialogClose=\"changeVisible\"\r\n      @dialogSubmit=\"dialogSumbit\"\r\n    >\r\n      <!--按钮插槽begin-->\r\n      <div slot=\"noticeSlot\" class=\"printBtn\">\r\n        <a title=\"打印\">\r\n          <svg class=\"link-svg\">\r\n            <use xlink:href=\"#icon-print\" @click=\"handlePrint\" />\r\n          </svg>\r\n        </a>\r\n      </div>\r\n      <!--按钮插槽end-->\r\n    </sun-notice-dialog>\r\n    <!--公告弹出框end-->\r\n    <!--打印组件begin-->\r\n    <div style=\"display: none\">\r\n      <Print ref=\"printRef\" :notice-config=\"dialog.noticeConfig\" />\r\n    </div>\r\n    <!--打印组件end-->\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport { SunNoticeDialog } from '@/components' // 公共弹窗组件\r\nimport Print from './print' // 打印组件\r\nimport { dateNowFormat10 } from '@/utils/date.js' // 日期格式化\r\nimport { uploadFile } from '@/utils/common' // 公共方法\r\nimport { Home, Common } from '@/api'\r\nconst { importantQuery, readNum, noticeModify } = Home\r\nconst { getSysParam } = Common\r\nimport { date10Format } from '@/filters'\r\nimport gsap from 'gsap'\r\nimport { commonBlank } from '@/utils/common'\r\nlet timeout1\r\nexport default {\r\n  name: 'Important',\r\n  components: { SunNoticeDialog, Print },\r\n  data() {\r\n    return {\r\n      title: '业务量', // 标题\r\n      list: [],\r\n      tween: null,\r\n      liWidth: 0, // li元素的宽度\r\n      dialog: {\r\n        visible: false, // 开启/关闭弹窗\r\n        btnCancle: false, // 取消按钮\r\n        btnSubmit: false, // 确定按钮\r\n        componentProps: {\r\n          // 弹出框配置属性\r\n          title: '公告详情',\r\n          width: '74.8rem' // 当前弹出框宽度\r\n        },\r\n        noticeConfig: {\r\n          imgSrc: require('@/assets/img/other/notice_images/noticeBackground.png'), // 背景图片加载\r\n          title: '标题', // 公告标题\r\n          info: [], // 发布机构、发布者名称、发布时间\r\n          readNum: 0, // 阅读量\r\n          content: '', // 公告正文\r\n          files: []\r\n        }\r\n      },\r\n      horizontal: true, // 是否为水平动画\r\n      num: 0, // 消息条数\r\n      activeNum: 0,\r\n      timer: null // 定时器\r\n    }\r\n  },\r\n  computed: {\r\n    // 计算属性调用store中的主题色\r\n    varColor() {\r\n      return {\r\n        '--color': this.$store.state.settings.theme\r\n      }\r\n    }\r\n  },\r\n  created() {\r\n    const msg = {\r\n      parameterList: [],\r\n      sysMap: {\r\n        param_item: 'SCROLL_TYPE'\r\n        // param_item: 'REPORT_USER_ROLE2'\r\n      }\r\n    }\r\n    getSysParam(msg).then((res) => {\r\n      const { retMap } = res\r\n      // console.log('retMap', retMap)\r\n      // 1 为左右滚动  0 为上下滚动\r\n      // this.horizontal = '1'\r\n      this.horizontal = retMap.sysParam === '1'\r\n    })\r\n    this.queryList()\r\n  },\r\n  mounted() {\r\n    // 界面加载完毕之后执行\r\n\r\n    this.$nextTick(() => {\r\n      this.$bus.$on('resizeImportantM', this.resizeBox)\r\n      document.getElementById('importantContent').onmouseover = () => {\r\n        if (this.horizontal) {\r\n          clearInterval(this.timer)\r\n        } else {\r\n          this.tween.pause()\r\n        }\r\n      }\r\n      // 滚动区域内移出鼠标 滚动暂停 再次点击鼠标 继续滚动\r\n      document.getElementById('importantContent').onmouseout = () => {\r\n        if (this.horizontal) {\r\n          this.scrollX()\r\n        } else {\r\n          this.tween.play()\r\n        }\r\n      }\r\n    })\r\n  },\r\n  // 组件销毁前\r\n  beforeDestroy() {\r\n    clearInterval(this.timer)\r\n    clearTimeout(timeout1)\r\n  },\r\n  activated() {\r\n    if (this.timer) {\r\n      clearInterval(this.timer)\r\n    }\r\n    if (this.horizontal) {\r\n      this.scrollX()\r\n    } else {\r\n      if (!commonBlank(this.list)) {\r\n        this.tween.play()\r\n      }\r\n    }\r\n  },\r\n  deactivated() {\r\n    clearInterval(this.timer)\r\n  },\r\n  methods: {\r\n    /**\r\n     * 查询-重要公告信息\r\n     * @param {Number} page 当前一级菜单 */\r\n    queryList() {\r\n      const msg = {\r\n        organ_no: this.$store.getters.organNo,\r\n        role_no: this.$store.getters.roleNo,\r\n        user_no: this.$store.getters.userNo,\r\n        organ_level: this.$store.getters.organLevel,\r\n        // pageSize: this.$store.getters.pageSize,\r\n        pageSize: 15, // 暂时写死\r\n        currentPage: 1\r\n      }\r\n      importantQuery(msg).then((response) => {\r\n        const { list } = response.retMap\r\n        // console.log('list', list)\r\n        this.list = []\r\n        list.forEach((element) => {\r\n          if (element.create_user !== this.$store.getters.userNo) {\r\n            this.list.push(\r\n              Object.assign(element, {\r\n                publish_time: date10Format(element.create_time), // 发布时间\r\n                notice_title: element.msg_content, // 公告标题\r\n                publish_organ: element.create_organ, // 发布机构\r\n                publish_user: element.create_user // 发布人\r\n              })\r\n            )\r\n          }\r\n        })\r\n        this.num = this.list.length\r\n        this.$nextTick(() => {\r\n          this.liWidth = this.$refs.messageBox.offsetWidth\r\n          const ulDom = document.querySelector('#importantNotice')\r\n          // 判断是否有重要信息\r\n          if (ulDom.firstElementChild) {\r\n            // 克隆节点\r\n            const fD = ulDom.firstElementChild.cloneNode(true)\r\n            fD.style.width = this.liWidth + 'px'\r\n            // 添加克隆节点事件\r\n            fD.addEventListener(\r\n              'click',\r\n              this.handleClick.bind(undefined, this.list[0])\r\n            )\r\n            ulDom.appendChild(fD)\r\n          }\r\n          if (this.horizontal) {\r\n            this.scrollX()\r\n          } else {\r\n            this.scrollY()\r\n          }\r\n        })\r\n      })\r\n    },\r\n    /**\r\n     * 横向滚动\r\n     */\r\n    scrollX() {\r\n      this.$nextTick(() => {\r\n        this.timer = setInterval(() => {\r\n          if (this.activeNum > this.num) {\r\n            this.activeNum = 0\r\n          }\r\n          const ulW = document.querySelector('#importantContent').offsetWidth\r\n          const ww = -ulW * this.activeNum\r\n          this.tween = gsap.to('#importantNotice', {\r\n            duration: this.activeNum === 0 ? 0 : 1,\r\n            x: ww,\r\n            ease: 'power1'\r\n          })\r\n          this.activeNum++\r\n        }, 3000)\r\n      })\r\n    },\r\n    /**\r\n     * 左右滚动时 当前盒子尺寸变化时执行\r\n     */\r\n    resizeBox() {\r\n      if (this.horizontal === '0') {\r\n        return\r\n      }\r\n      clearInterval(this.timer)\r\n      timeout1 = setTimeout(() => {\r\n        const ulbox = document.querySelector('#importantContent')\r\n        this.ulW = ulbox.offsetWidth\r\n        const lis = ulbox.querySelectorAll('li')\r\n        Array.from(lis).forEach((li) => {\r\n          li.style.width = ulbox.offsetWidth + 'px'\r\n        })\r\n        if (this.activeNum === this.num) {\r\n          this.activeNum = 0\r\n        }\r\n\r\n        ulbox.querySelector('ul').style.transform = `translate(-${\r\n          ulbox.offsetWidth * this.activeNum\r\n        }px, 0px)`\r\n\r\n        this.scrollX()\r\n      }, 200)\r\n    },\r\n    /**\r\n     * 纵向滚动\r\n     */\r\n    scrollY() {\r\n      this.$nextTick(() => {\r\n        this.tween = gsap.to('#importantNotice', {\r\n          duration: this.num * 3,\r\n          y: -30 * this.num,\r\n          delay: 1,\r\n          ease: 'none',\r\n          repeat: -1\r\n        })\r\n        document.getElementById('importantContent').onmouseover = () => {\r\n          this.tween.pause()\r\n        }\r\n        // 滚动区域内移出鼠标 滚动暂停 再次点击鼠标 继续滚动\r\n        document.getElementById('importantContent').onmouseout = () => {\r\n          this.tween.play()\r\n        }\r\n      })\r\n    },\r\n    /**\r\n     * 点击-初始化公告弹窗\r\n     * @param {Object} val 当前点击的行信息 */\r\n    async handleClick(val) {\r\n      // 清空每次弹窗数据\r\n      this.dialog.noticeConfig.info = []\r\n      this.dialog.visible = true\r\n      // 弹窗打开 如果阅读状态为未阅，改为已阅，同时修改系统消息相关信息\r\n      if (val.read_state === '0') {\r\n        const paramJson = {\r\n          notice_id: JSON.parse(val.msg_parameter).notice_id,\r\n          notice_title: JSON.parse(val.msg_parameter).notice_title,\r\n          notice_content: JSON.parse(val.msg_parameter).notice_content,\r\n          file_url: JSON.parse(val.msg_parameter).file_url\r\n        }\r\n        this.firstPage(val.notice_id, '', paramJson)\r\n      }\r\n      // console.log(JSON.parse(val.msg_parameter).file_url)\r\n      // 查询阅读量\r\n      const readMsg = {\r\n        parameterList: [],\r\n        notice_id: JSON.parse(val.msg_parameter).notice_id,\r\n        user_no: this.$store.getters.userNo,\r\n        organ_no: this.$store.getters.organNo,\r\n        publish_user: val.publish_user\r\n      }\r\n      const data = await readNum(readMsg)\r\n      this.$nextTick(() => {\r\n        // 清除定时器\r\n        if (!this.horizontal) {\r\n          this.tween.pause()\r\n        } else {\r\n          clearInterval(this.timer)\r\n        }\r\n\r\n        // 弹窗加载完毕后赋值\r\n        this.dialog.noticeConfig.title = val.notice_title // 标题\r\n        const info = []\r\n        const publish_organ = val.publish_organ\r\n        const publish_user = val.publish_user\r\n        const publish_time = val.publish_time\r\n        const infoObj = {}\r\n        infoObj['publish_organ'] = publish_organ\r\n        infoObj['publish_user'] = publish_user\r\n        infoObj['publish_time'] = publish_time\r\n        info.push(infoObj)\r\n        this.dialog.noticeConfig.info = info\r\n        this.dialog.noticeConfig.content = JSON.parse(\r\n          val.msg_parameter\r\n        ).notice_content // 公告内容\r\n        if (JSON.parse(val.msg_parameter).file_url) {\r\n          this.dialog.noticeConfig.files = uploadFile(\r\n            JSON.parse(val.msg_parameter).file_url\r\n          )\r\n        } else {\r\n          this.dialog.noticeConfig.files = []\r\n        }\r\n        this.dialog.noticeConfig.readNum = data.retMap.read_num\r\n      })\r\n    },\r\n    /**\r\n     * 公告信息: 更改阅读状态\r\n     * @param notice_id:公告id\r\n     * @param msg_no:系统消息编号\r\n     * @param paramJson：系统消息参数\r\n     * */\r\n    firstPage(notice_id, msg_no, paramJson) {\r\n      const read_time = dateNowFormat10() // 当前时间的十位数格式\r\n      const msg = {\r\n        notice_id: notice_id,\r\n        user_no: this.$store.getters.userNo,\r\n        organ_no: this.$store.getters.organNo,\r\n        read_time: read_time,\r\n        msg_no: msg_no,\r\n        parpamData: paramJson\r\n      }\r\n      noticeModify(msg).then((response) => {\r\n        this.queryList()\r\n      })\r\n    },\r\n    // 打印\r\n    handlePrint() {\r\n      this.$print(this.$refs.printRef)\r\n    },\r\n    /**\r\n     * 弹出框 - 确认*/\r\n    dialogSumbit() {\r\n      this.changeVisible(false)\r\n    },\r\n    /**\r\n     * 弹出框 - 关闭\r\n     * @param {Boolean} param 弹出框显示隐藏配置*/\r\n    changeVisible(param) {\r\n      // 关闭弹框后继续动画\r\n      if (!this.horizontal) {\r\n        this.tween.play()\r\n      } else {\r\n        this.scrollX()\r\n      }\r\n\r\n      this.dialog.visible = param\r\n      this.dialog.noticeConfig.title = ''\r\n      this.dialog.noticeConfig.content = ''\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style scoped lang=\"scss\">\r\n@import '~@/assets/scss/common/variable/variable/size.scss';\r\n@import '~@/assets/scss/common/variable/variable/color.scss';\r\n.importantMessage {\r\n  background: linear-gradient(90deg, #c7d9ff 0%, #e0ecff 100%);\r\n  overflow: hidden;\r\n  position: relative;\r\n  &::after {\r\n    content: '';\r\n    display: block;\r\n    width: 6rem;\r\n    background: #8b9dff;\r\n    opacity: 0.3;\r\n    height: 6rem;\r\n    border-radius: 50%;\r\n    position: absolute;\r\n    top: -3rem;\r\n    right: -3rem;\r\n  }\r\n  .homePageTitle2 {\r\n    float: left;\r\n    img:nth-child(2) {\r\n      position: relative;\r\n      top: -2rem;\r\n    }\r\n  }\r\n  .homePageBox2 {\r\n    width: calc(100% - 220px);\r\n    height: 3rem;\r\n    float: right;\r\n    position: relative;\r\n    margin-top: 2.3rem;\r\n    overflow: hidden;\r\n    .bottom {\r\n      width: 100%;\r\n      position: absolute;\r\n      top: 0;\r\n      left: 0;\r\n      .importantSpan {\r\n        height: 3rem;\r\n        list-style: none;\r\n        span {\r\n          display: inline-block;\r\n        }\r\n        .Title {\r\n          width: 70%;\r\n          padding-left: 2rem;\r\n          text-align: left;\r\n        }\r\n        .Time {\r\n          width: 30%;\r\n          text-align: left;\r\n          color: #99a2b2;\r\n        }\r\n      }\r\n      &.horizontal {\r\n        width: 1000%;\r\n        .importantSpan {\r\n          float: left;\r\n        }\r\n      }\r\n    }\r\n  }\r\n}\r\n.InfoMore {\r\n  float: right;\r\n  color: gray;\r\n  font-size: $noteFomt;\r\n  cursor: pointer;\r\n}\r\n.homePageBox {\r\n  margin-top: 4%;\r\n  height: 96%;\r\n  // 适配各种浏览器中 滚动条宽度\r\n  &::-webkit-scrollbar {\r\n    width: 0px; // y轴滚动条宽度\r\n  }\r\n  &::-webkit-scrollbar-thumb {\r\n    // 滚动条配置\r\n    width: 0px;\r\n  }\r\n  &::-webkit-scrollbar-track {\r\n    width: 0px;\r\n  }\r\n  .bottom {\r\n    list-style-type: none;\r\n    margin: 0;\r\n    padding: 0;\r\n    li:hover {\r\n      color: var(--color);\r\n      background-color: #ececec;\r\n      .notice-icon {\r\n        background: var(--color);\r\n      }\r\n    }\r\n    li {\r\n      cursor: pointer;\r\n      margin-bottom: 1rem;\r\n      line-height: 30px;\r\n      overflow: hidden;\r\n      list-style: none;\r\n      .notice-icon {\r\n        padding-right: 10px;\r\n        line-height: 30px;\r\n        width: 10px;\r\n        height: 10px;\r\n        background: $color_font;\r\n        margin-right: 1rem;\r\n        border-radius: 50%;\r\n        display: inline-block;\r\n      }\r\n\r\n      .Title {\r\n        overflow: hidden;\r\n        text-overflow: ellipsis;\r\n        width: calc(100% - 100px);\r\n        white-space: nowrap;\r\n        display: inline-block;\r\n        vertical-align: middle;\r\n        .center {\r\n          color: $colorLoading;\r\n          background: $color_li_import;\r\n          margin-left: 1.5rem;\r\n          border: 0.1rem;\r\n          font-size: 1.2px;\r\n          border-radius: 5px;\r\n          border-bottom-left-radius: 0;\r\n          display: inline-block;\r\n          width: 3rem;\r\n          line-height: 1.8rem;\r\n          text-align: center;\r\n        }\r\n      }\r\n      .Time {\r\n        float: right;\r\n      }\r\n    }\r\n    // 公告级别为重要的样式\r\n\r\n    .importantIcon {\r\n      background: $color_li_import !important;\r\n    }\r\n  }\r\n}\r\n// 打印按钮\r\n.printBtn {\r\n  position: absolute;\r\n  right: 2%;\r\n  top: 20%;\r\n  .link-svg {\r\n    cursor: pointer;\r\n    width: 1.4rem;\r\n    height: 1.4rem;\r\n    fill: currentColor;\r\n    overflow: hidden;\r\n    float: left;\r\n  }\r\n}\r\n</style>\r\n"]}]}