{"remainingRequest": "D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\node_modules\\babel-loader\\lib\\index.js!D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\node_modules\\eslint-loader\\index.js??ref--13-0!D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\src\\api\\common\\audit\\index.js", "dependencies": [{"path": "D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\src\\api\\common\\audit\\index.js", "mtime": 1686019810966}, {"path": "D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\babel.config.js", "mtime": 1686019818359}, {"path": "D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\.babelrc", "mtime": 1705285078668}, {"path": "D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\node_modules\\eslint-loader\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:aW1wb3J0IHJlcXVlc3QgZnJvbSAnQC91dGlscy9yZXF1ZXN0JzsKaW1wb3J0IGRlZmF1bHRTZXR0aW5ncyBmcm9tICdAL3NldHRpbmdzJzsKdmFyIHByZWZpeCA9IGRlZmF1bHRTZXR0aW5ncy5zZXJ2aWNlLnN5c3RlbSArICcvb3BlcmF0aW9uUmVxdWVzdCc7CnZhciBwcmVmaXgyID0gZGVmYXVsdFNldHRpbmdzLnNlcnZpY2Uuc3lzdGVtOwoKLy8g6I+c5Y2V5a6h5qC46YWN572u55u45YWz5o6l5Y+jCmV4cG9ydCB2YXIgU3lzQXVkaXQgPSB7CiAgLy8g5o6l5Y+j5ZCN56ewCiAgcXVlcnk6IGZ1bmN0aW9uIHF1ZXJ5KGRhdGEpIHsKICAgIC8vIOafpeivogogICAgcmV0dXJuIHJlcXVlc3QoewogICAgICB1cmw6IHByZWZpeCArICcvc2VsZWN0TWVudU9wZXJhdGlvblJlcXVlc3RDb25maWcuZG8nLAogICAgICBtZXRob2Q6ICdnZXQnLAogICAgICBwYXJhbXM6IHsKICAgICAgICBtZXNzYWdlOiBkYXRhCiAgICAgIH0KICAgIH0pOwogIH0sCiAgYWRkOiBmdW5jdGlvbiBhZGQoZGF0YSkgewogICAgLy8g5paw5aKeCiAgICByZXR1cm4gcmVxdWVzdCh7CiAgICAgIHVybDogcHJlZml4ICsgJy9vcGVyYXRpb25SZXF1ZXN0Q29uZmlnVXBkYXRlLmRvJywKICAgICAgbWV0aG9kOiAncG9zdCcsCiAgICAgIGRhdGE6IGRhdGEKICAgIH0pOwogIH0sCiAgbW9kaWZ5OiBmdW5jdGlvbiBtb2RpZnkoZGF0YSkgewogICAgLy8g5L+u5pS5CiAgICByZXR1cm4gcmVxdWVzdCh7CiAgICAgIHVybDogcHJlZml4ICsgJy9vcGVyYXRpb25SZXF1ZXN0Q29uZmlnVXBkYXRlLmRvJywKICAgICAgbWV0aG9kOiAncG9zdCcsCiAgICAgIGRhdGE6IGRhdGEKICAgIH0pOwogIH0sCiAgYWR1aXRNZW51VHJlZTogZnVuY3Rpb24gYWR1aXRNZW51VHJlZShkYXRhKSB7CiAgICAvLyDmn6Xor6IKICAgIHJldHVybiByZXF1ZXN0KHsKICAgICAgdXJsOiBwcmVmaXggKyAnL3NlbGVjdE1lbnVUcmVlRGF0YS5kbycsCiAgICAgIG1ldGhvZDogJ2dldCcsCiAgICAgIHBhcmFtczogewogICAgICAgIG1lc3NhZ2U6IGRhdGEKICAgICAgfQogICAgfSk7CiAgfSwKICBidG5uYW1lOiBmdW5jdGlvbiBidG5uYW1lKGRhdGEpIHsKICAgIC8vIOaMiemSruWQjeensAogICAgcmV0dXJuIHJlcXVlc3QoewogICAgICB1cmw6IHByZWZpeCArICcvc2VsZWN0QnV0dG9uQnlNZW51LmRvJywKICAgICAgbWV0aG9kOiAnZ2V0JywKICAgICAgcGFyYW1zOiB7CiAgICAgICAgbWVzc2FnZTogZGF0YQogICAgICB9CiAgICB9KTsKICB9LAogIGNoZWNrcnVsZTogZnVuY3Rpb24gY2hlY2tydWxlKGRhdGEpIHsKICAgIC8vIOi/nOeoi+WuoeaJueaooeadvwogICAgcmV0dXJuIHJlcXVlc3QoewogICAgICB1cmw6IHByZWZpeCArICcvcXVlcnlNb2R1bGUuZG8nLAogICAgICBtZXRob2Q6ICdnZXQnLAogICAgICBwYXJhbXM6IHsKICAgICAgICBtZXNzYWdlOiBkYXRhCiAgICAgIH0KICAgIH0pOwogIH0sCiAgZGF0YVNvdXJjZTogZnVuY3Rpb24gZGF0YVNvdXJjZShkYXRhKSB7CiAgICAvLyDlh4blhaXjgIHnpoHmraLmnaHku7bliJ3lp4vljJbopoHntKDmlbDmja7mupAKICAgIHJldHVybiByZXF1ZXN0KHsKICAgICAgdXJsOiBwcmVmaXggKyAnL3F1ZXJ5T3BlckVsZW1lbnQuZG8nLAogICAgICBtZXRob2Q6ICdnZXQnLAogICAgICBwYXJhbXM6IHsKICAgICAgICBtZXNzYWdlOiBkYXRhCiAgICAgIH0KICAgIH0pOwogIH0sCiAgY3JlYXRlRmxvdzogZnVuY3Rpb24gY3JlYXRlRmxvdyhkYXRhKSB7CiAgICAvLyDlrqHmoLjmlrnlvI/kuLrov5znqIvlrqHmoLgtLS0t5Yib5bu65rWB56iLCiAgICByZXR1cm4gcmVxdWVzdCh7CiAgICAgIHVybDogcHJlZml4ICsgJy9jcmVhdGVGbG93LmRvJywKICAgICAgbWV0aG9kOiAncG9zdCcsCiAgICAgIGRhdGE6IGRhdGEKICAgIH0pOwogIH0sCiAgY29tbW9uUmVxOiBmdW5jdGlvbiBjb21tb25SZXEoZGF0YSwgdXJsLCBhcHBseVR5cGUpIHsKICAgIGlmIChhcHBseVR5cGUgPT09ICdnZXQnKSB7CiAgICAgIHJldHVybiByZXF1ZXN0KHsKICAgICAgICB1cmw6IHVybCwKICAgICAgICBtZXRob2Q6IGFwcGx5VHlwZSwKICAgICAgICBwYXJhbXM6IHsKICAgICAgICAgIG1lc3NhZ2U6IGRhdGEKICAgICAgICB9CiAgICAgIH0pOwogICAgfSBlbHNlIHsKICAgICAgcmV0dXJuIHJlcXVlc3QoewogICAgICAgIHVybDogdXJsLAogICAgICAgIG1ldGhvZDogYXBwbHlUeXBlLAogICAgICAgIGRhdGE6IGRhdGEKICAgICAgfSk7CiAgICB9CiAgfSwKICBjb21tb25BdXRoOiBmdW5jdGlvbiBjb21tb25BdXRoKGRhdGEpIHsKICAgIC8vIOeUqOaIt+i0puWPt+WvhueggeagoemqjAogICAgcmV0dXJuIHJlcXVlc3QoewogICAgICB1cmw6IHByZWZpeDIgKyAnL3VzZXIvY29tbW9uQXV0aC5kbycsCiAgICAgIG1ldGhvZDogJ3Bvc3QnLAogICAgICBkYXRhOiBkYXRhCiAgICB9KTsKICB9Cn07"}, {"version": 3, "names": ["request", "defaultSettings", "prefix", "service", "system", "prefix2", "SysAudit", "query", "data", "url", "method", "params", "message", "add", "modify", "aduitMenuTree", "btnname", "<PERSON><PERSON>le", "dataSource", "createFlow", "commonReq", "applyType", "commonAuth"], "sources": ["D:/1_Project/XYD_Project/dop-4.0/dop-4.1-qianduan-unify/dopUnify/src/api/common/audit/index.js"], "sourcesContent": ["import request from '@/utils/request'\r\nimport defaultSettings from '@/settings'\r\nconst prefix = defaultSettings.service.system + '/operationRequest'\r\nconst prefix2 = defaultSettings.service.system\r\n\r\n// 菜单审核配置相关接口\r\nexport const SysAudit = {\r\n  // 接口名称\r\n  query(data) {\r\n    // 查询\r\n    return request({\r\n      url: prefix + '/selectMenuOperationRequestConfig.do',\r\n      method: 'get',\r\n      params: { message: data }\r\n    })\r\n  },\r\n  add(data) {\r\n    // 新增\r\n    return request({\r\n      url: prefix + '/operationRequestConfigUpdate.do',\r\n      method: 'post',\r\n      data\r\n    })\r\n  },\r\n  modify(data) {\r\n    // 修改\r\n    return request({\r\n      url: prefix + '/operationRequestConfigUpdate.do',\r\n      method: 'post',\r\n      data\r\n    })\r\n  },\r\n  aduitMenuTree(data) {\r\n    // 查询\r\n    return request({\r\n      url: prefix + '/selectMenuTreeData.do',\r\n      method: 'get',\r\n      params: { message: data }\r\n    })\r\n  },\r\n  btnname(data) {\r\n    // 按钮名称\r\n    return request({\r\n      url: prefix + '/selectButtonByMenu.do',\r\n      method: 'get',\r\n      params: { message: data }\r\n    })\r\n  },\r\n  checkrule(data) {\r\n    // 远程审批模板\r\n    return request({\r\n      url: prefix + '/queryModule.do',\r\n      method: 'get',\r\n      params: { message: data }\r\n    })\r\n  },\r\n  dataSource(data) {\r\n    // 准入、禁止条件初始化要素数据源\r\n    return request({\r\n      url: prefix + '/queryOperElement.do',\r\n      method: 'get',\r\n      params: { message: data }\r\n    })\r\n  },\r\n  createFlow(data) {\r\n    // 审核方式为远程审核----创建流程\r\n    return request({\r\n      url: prefix + '/createFlow.do',\r\n      method: 'post',\r\n      data\r\n    })\r\n  },\r\n  commonReq(data, url, applyType) {\r\n    if (applyType === 'get') {\r\n      return request({\r\n        url: url,\r\n        method: applyType,\r\n        params: { message: data }\r\n      })\r\n    } else {\r\n      return request({\r\n        url: url,\r\n        method: applyType,\r\n        data\r\n      })\r\n    }\r\n  },\r\n  commonAuth(data) {\r\n    // 用户账号密码校验\r\n    return request({\r\n      url: prefix2 + '/user/commonAuth.do',\r\n      method: 'post',\r\n      data\r\n    })\r\n  }\r\n}\r\n"], "mappings": "AAAA,OAAOA,OAAO,MAAM,iBAAiB;AACrC,OAAOC,eAAe,MAAM,YAAY;AACxC,IAAMC,MAAM,GAAGD,eAAe,CAACE,OAAO,CAACC,MAAM,GAAG,mBAAmB;AACnE,IAAMC,OAAO,GAAGJ,eAAe,CAACE,OAAO,CAACC,MAAM;;AAE9C;AACA,OAAO,IAAME,QAAQ,GAAG;EACtB;EACAC,KAAK,iBAACC,IAAI,EAAE;IACV;IACA,OAAOR,OAAO,CAAC;MACbS,GAAG,EAAEP,MAAM,GAAG,sCAAsC;MACpDQ,MAAM,EAAE,KAAK;MACbC,MAAM,EAAE;QAAEC,OAAO,EAAEJ;MAAK;IAC1B,CAAC,CAAC;EACJ,CAAC;EACDK,GAAG,eAACL,IAAI,EAAE;IACR;IACA,OAAOR,OAAO,CAAC;MACbS,GAAG,EAAEP,MAAM,GAAG,kCAAkC;MAChDQ,MAAM,EAAE,MAAM;MACdF,IAAI,EAAJA;IACF,CAAC,CAAC;EACJ,CAAC;EACDM,MAAM,kBAACN,IAAI,EAAE;IACX;IACA,OAAOR,OAAO,CAAC;MACbS,GAAG,EAAEP,MAAM,GAAG,kCAAkC;MAChDQ,MAAM,EAAE,MAAM;MACdF,IAAI,EAAJA;IACF,CAAC,CAAC;EACJ,CAAC;EACDO,aAAa,yBAACP,IAAI,EAAE;IAClB;IACA,OAAOR,OAAO,CAAC;MACbS,GAAG,EAAEP,MAAM,GAAG,wBAAwB;MACtCQ,MAAM,EAAE,KAAK;MACbC,MAAM,EAAE;QAAEC,OAAO,EAAEJ;MAAK;IAC1B,CAAC,CAAC;EACJ,CAAC;EACDQ,OAAO,mBAACR,IAAI,EAAE;IACZ;IACA,OAAOR,OAAO,CAAC;MACbS,GAAG,EAAEP,MAAM,GAAG,wBAAwB;MACtCQ,MAAM,EAAE,KAAK;MACbC,MAAM,EAAE;QAAEC,OAAO,EAAEJ;MAAK;IAC1B,CAAC,CAAC;EACJ,CAAC;EACDS,SAAS,qBAACT,IAAI,EAAE;IACd;IACA,OAAOR,OAAO,CAAC;MACbS,GAAG,EAAEP,MAAM,GAAG,iBAAiB;MAC/BQ,MAAM,EAAE,KAAK;MACbC,MAAM,EAAE;QAAEC,OAAO,EAAEJ;MAAK;IAC1B,CAAC,CAAC;EACJ,CAAC;EACDU,UAAU,sBAACV,IAAI,EAAE;IACf;IACA,OAAOR,OAAO,CAAC;MACbS,GAAG,EAAEP,MAAM,GAAG,sBAAsB;MACpCQ,MAAM,EAAE,KAAK;MACbC,MAAM,EAAE;QAAEC,OAAO,EAAEJ;MAAK;IAC1B,CAAC,CAAC;EACJ,CAAC;EACDW,UAAU,sBAACX,IAAI,EAAE;IACf;IACA,OAAOR,OAAO,CAAC;MACbS,GAAG,EAAEP,MAAM,GAAG,gBAAgB;MAC9BQ,MAAM,EAAE,MAAM;MACdF,IAAI,EAAJA;IACF,CAAC,CAAC;EACJ,CAAC;EACDY,SAAS,qBAACZ,IAAI,EAAEC,GAAG,EAAEY,SAAS,EAAE;IAC9B,IAAIA,SAAS,KAAK,KAAK,EAAE;MACvB,OAAOrB,OAAO,CAAC;QACbS,GAAG,EAAEA,GAAG;QACRC,MAAM,EAAEW,SAAS;QACjBV,MAAM,EAAE;UAAEC,OAAO,EAAEJ;QAAK;MAC1B,CAAC,CAAC;IACJ,CAAC,MAAM;MACL,OAAOR,OAAO,CAAC;QACbS,GAAG,EAAEA,GAAG;QACRC,MAAM,EAAEW,SAAS;QACjBb,IAAI,EAAJA;MACF,CAAC,CAAC;IACJ;EACF,CAAC;EACDc,UAAU,sBAACd,IAAI,EAAE;IACf;IACA,OAAOR,OAAO,CAAC;MACbS,GAAG,EAAEJ,OAAO,GAAG,qBAAqB;MACpCK,MAAM,EAAE,MAAM;MACdF,IAAI,EAAJA;IACF,CAAC,CAAC;EACJ;AACF,CAAC"}]}