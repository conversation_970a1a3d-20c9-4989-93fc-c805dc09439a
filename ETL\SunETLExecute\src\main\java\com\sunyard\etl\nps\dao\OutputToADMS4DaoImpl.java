package com.sunyard.etl.nps.dao;

import java.io.File;
import java.sql.Connection;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.sunyard.util.dbutil.DBHandler;
import org.sunyard.util.string.StringUtil;

import com.sun.rowset.CachedRowSetImpl;
import com.sunyard.etl.nps.common.Parameters;
import com.sunyard.etl.nps.model.BpTmpbatchTb;
import com.sunyard.etl.nps.model.BpTmpdata1Tb;
import com.sunyard.etl.nps.model.SmMixedNopaperTb;
import com.sunyard.etl.system.common.Constants;

public class OutputToADMS4DaoImpl implements OutputToADMS4Dao {

	protected final Logger log = LoggerFactory.getLogger(getClass());

	public String armsDataDate() throws SQLException {
		String armsDataData = null;
		String sql = "SELECT DATA_DATE FROM ARMS_DATA_DATE";
		
		DBHandler dbHandler = new DBHandler(Constants.CONN_POOL_NAME);
	
		CachedRowSetImpl rs = null;	
		rs = dbHandler.queryRs(sql);
		while(rs.next()){
			armsDataData = rs.getString(1);
		}
		if(StringUtil.checkNull(armsDataData)){
			return null;
		}
		return armsDataData;
	}



	public String vtmDataDate() throws SQLException {
		String vtmDataData = null;
		String sql = "SELECT MAX(OCCUR_DATE) FROM VTM_FLAG";
		DBHandler dbHandler = new DBHandler(Constants.CONN_POOL_NAME);
		CachedRowSetImpl rs = null;
		rs = dbHandler.queryRs(sql);
		while (rs.next()) {
			vtmDataData = rs.getString(1);
		}
		if (StringUtil.checkNull(vtmDataData)) {
			return null;
		}
		return vtmDataData;
	}


	public List<BpTmpbatchTb> getBatchInfo() throws SQLException {

		List<BpTmpbatchTb> list = new ArrayList<BpTmpbatchTb>();
		String sql = "SELECT OCCUR_DATE,SITE_NO,OPERATOR_NO FROM SM_MIXED_NOPAPER_TB WHERE STATUS = '?'  GROUP BY  OCCUR_DATE,SITE_NO,OPERATOR_NO";

		DBHandler dbHandler = new DBHandler(Constants.CONN_POOL_NAME);
		CachedRowSetImpl rs = null;
		rs = dbHandler.queryRs(sql, 2);
		while (rs.next()) {
			BpTmpbatchTb batch = new BpTmpbatchTb(rs.getString(1), rs.getString(2), rs.getString(3));
			list.add(batch);
		}
		return list;
	}

	public void getMoreBatchInfo(BpTmpbatchTb bpTmpbatchTb) throws SQLException {

		String sql = "SELECT * FROM BP_TMPBATCH_TB T WHERE  T.OCCUUR_DATE = ? AND  SITE_NO = ? AND OPERATOR_NO = ? AND NOPAPER_FLAG = 2";
		DBHandler dbHandler = new DBHandler(Constants.CONN_POOL_NAME);
		CachedRowSetImpl rs = null;
		rs = dbHandler.queryRs(sql, bpTmpbatchTb.getOccurDate(),bpTmpbatchTb.getSiteNo(), bpTmpbatchTb.getOperatorNo());
		log.info("根据批次索引获取该批次更多的当前信息:" + sql);
		while (rs.next()) {
			bpTmpbatchTb.setBatchId(rs.getString(""));
			bpTmpbatchTb.setProgressFlag(rs.getString(""));
			bpTmpbatchTb.setBatchTotalPage(rs.getString(""));
		}
	
	}

	
	public List<SmMixedNopaperTb> getFlowInfo(BpTmpbatchTb bpTmpbatchTb) throws SQLException {
		DBHandler dbHandler = new DBHandler(Constants.CONN_POOL_NAME);
		CachedRowSetImpl rs = null;
		List<SmMixedNopaperTb> list = new ArrayList<SmMixedNopaperTb>();
		String sql = "SELECT  * FROM SM_MIXED_NOPAPER_TB  T WHERE  T.OCCUR_DATE = ? AND T.SITE_NO = ?  AND T.OPERATOR_NO = ?  ORDER BY FLOW_ID";
		log.info("提取批次"+bpTmpbatchTb.getBatchId()+"待处理的混合业务流水信息:" + sql);
		rs = dbHandler.queryRs(sql, bpTmpbatchTb.getOccurDate(), bpTmpbatchTb.getSiteNo(), bpTmpbatchTb.getOperatorNo());
		while (rs.next()) {
			SmMixedNopaperTb info = new SmMixedNopaperTb();
			info.setFLOW_ID(rs.getString("FLOW_ID"));
			info.setFLIE_NAME(rs.getString("FLIE_NAME"));
			list.add(info);
		}
		return list;
	}

	
	public List<BpTmpbatchTb> getPendingBatch(String occurDate) throws SQLException {
		DBHandler dbHandler = new DBHandler(Constants.CONN_POOL_NAME);
		CachedRowSetImpl rs = null;
		List<BpTmpbatchTb> pendingBatchList = new ArrayList<BpTmpbatchTb>();
		String sql = null;
		sql = "SELECT * FROM BP_TMPBATCH_TB T  WHERE OCCUR_DATE = '"
				+ occurDate
				+ "' AND T.NOPAPER_FLAG = 2  AND  T.PROGRESS_FLAG IN (2,18)  AND T.IS_INVALID =  1 AND  T.NEED_PROCESS = 1";
		log.info("获取待处理的批次:" + sql);
		rs = dbHandler.queryRs(sql);
		while (rs.next()) {
			BpTmpbatchTb bpTmpbatchTb = new BpTmpbatchTb(
					rs.getString("BATCH_ID"), rs.getString("OCCUR_DATE"),
					rs.getString("SITE_NO"), rs.getString("OPERATOR_NO"),
					rs.getString("BATCH_TOTAL_PAGE"),
					rs.getString("FS_MACHINE_IP"),
					rs.getString("FS_MACHINE_PORT"),
					rs.getString("LARGE_FILE_NAME"));
			pendingBatchList.add(bpTmpbatchTb);
		}
		return pendingBatchList;
	}


	public List<BpTmpdata1Tb> getBpTmpData(String batchId) throws SQLException {
		DBHandler dbHandler = new DBHandler(Constants.CONN_POOL_NAME);
		CachedRowSetImpl rs = null;
		List<BpTmpdata1Tb> list = new ArrayList<BpTmpdata1Tb>();
		String sql = "SELECT BATCH_ID,CHECK_FLAG,FLOW_ID,FORM_NAME,INCCODEIN_BATCH,LENGTH_OF_IMAGE,"
				+ "OFFSET_OF_IMAGE,PROCESS_STATE,PS_LEVEL,SLAVE_COUNT "
				+ "FROM  BP_TMPDATA_1_TB  T   "
				+ "WHERE T.BATCH_ID = ? AND T.CHECK_FLAG = 1 AND PS_LEVEL = 0"
				+ "ORDER BY  T.INCCODEIN_BATCH";

			log.info("根据批次号获取数据表信息:" + sql);
			rs = dbHandler.queryRs(sql, batchId);
			while (rs.next()) {
				BpTmpdata1Tb bpTmpdata1 = new BpTmpdata1Tb(rs.getString(1),
						rs.getString(2), rs.getString(3), rs.getString(4),
						rs.getInt(5), rs.getInt(6), rs.getInt(7),
						rs.getString(8), rs.getString(9), rs.getString(10));
				list.add(bpTmpdata1);
			}

		return list;
	}

	public List<String> getPendingDateList() throws SQLException {
		DBHandler dbHandler = new DBHandler(Constants.CONN_POOL_NAME);
		CachedRowSetImpl rs = null;
		List<String> pendingDateList = new ArrayList<String>();
		String sql = "SELECT OCCUR_DATE FROM BP_TMPBATCH_TB T WHERE T.PROGRESS_FLAG IN (2,18)  "
				+ "AND  T.IS_INVALID =  1 "
				+ "AND  T.NEED_PROCESS = 1 "
				+ "AND  T.NOPAPER_FLAG = 2  " + "ORDER BY T.OCCUR_DATE";

		log.info("混合业务待处理的业务日期:" + sql);
		rs = dbHandler.queryRs(sql);
		while (rs.next()) {
			pendingDateList.add(rs.getString(1));
		}
		return pendingDateList;
	}

	public Map<String, String> getJPGPathMap(String occurDate) throws SQLException {
		DBHandler dbHandler = new DBHandler(Constants.CONN_POOL_NAME);
		CachedRowSetImpl rs = null;
		Map<String, String> map = new HashMap<String, String>();
		String sql = "SELECT T.FLOW_ID,T.TERMINAL_NO,T.FLIE_NAME FROM SM_MIXED_NOPAPER_TB  T WHERE T.OCCUR_DATE = '"
				+ occurDate + "' ORDER BY FLOW_ID";
		log.info("混合业务待处理的业务日期:" + sql);
		rs = dbHandler.queryRs(sql);
		while (rs.next()) {
			if (map.containsKey(rs.getString(1))) {
				map.put(rs.getString(1),map.get(rs.getString(1)) + ";" + rs.getString(2) + File.separator + rs.getString(3));
			} else {
				map.put(rs.getString(1), rs.getString(2) + File.separator + rs.getString(3));
			}
		}
		return map;
	}

	public Long getMaxOffset(String batchId) throws SQLException {
		DBHandler dbHandler = new DBHandler(Constants.CONN_POOL_NAME);
		CachedRowSetImpl rs = null;
		Long MaxOffset = 0l;
		String sql = "SELECT  T.OFFSET_OF_IMAGE, T.LENGTH_OF_IMAGE FROM BP_TMPDATA_1_TB T  WHERE T.BATCH_ID = '"
				+ batchId
				+ "'  AND T.OFFSET_OF_IMAGE >= 0  ORDER BY  T.OFFSET_OF_IMAGE  DESC";

		log.info("获取偏移量最大的图像的偏移量和图像大小:" + sql);
		rs = dbHandler.queryRs(sql);
		while (rs.next()) {
			MaxOffset = rs.getLong(1) + rs.getLong(2);
			return MaxOffset;
		}
		return MaxOffset;
	}



	public int updateInccodeinBatch(Connection conn, String batchId, String inccodeinBatch) throws SQLException {
		DBHandler dbHandler = new DBHandler(Constants.CONN_POOL_NAME);
		int flag = 0;
		String sql = "UPDATE BP_TMPDATA_1_TB T SET T.INCCODEIN_BATCH = INCCODEIN_BATCH +1 WHERE T.BATCH_ID = ? AND  T.INCCODEIN_BATCH >= ?";
		flag = dbHandler.execute(sql, batchId, inccodeinBatch);
		return flag;
	}


	public int inertBpTmpata1Tb(Connection conn, BpTmpdata1Tb bpTmpdata1Tb) throws SQLException {
		DBHandler dbHandler = new DBHandler(Constants.CONN_POOL_NAME);
		int flag = 0;
		String sql = "INSERT INTO BP_TMPDATA_1_TB (BATCH_ID,INCCODEIN_BATCH,IS_FRONT_PAGE,OFFSET_OF_IMAGE,LENGTH_OF_IMAGE,"
				+ "CHECK_FLAG,PROCESS_STATE,PS_LEVEL,FLOW_ID,FORM_NAME,NOPAPER_FLAG)  "
				+ "VALUES (?,?,'1',?,?,'5',?,'1',?,?,'2')";
		flag = dbHandler.execute(sql, bpTmpdata1Tb.getBatchId(),
				bpTmpdata1Tb.getInccodeinBatch(),
				bpTmpdata1Tb.getOffsetOfImage(),
				bpTmpdata1Tb.getLengthOfImage(),
				Parameters.MIXED_PROCESS_STATE_MIXED, 
				bpTmpdata1Tb.getFlowId(),
				Parameters.MIXED_FORM_NAME);
		return flag;
	}

	
	public int updateBatchInfo(Connection conn, BpTmpbatchTb bpTmpbatchTb) throws SQLException {
		DBHandler dbHandler = new DBHandler(Constants.CONN_POOL_NAME);
		int flag = 0;
		String sql = "UPDATE BP_TMPBATCH_TB T SET T.BATCH_TOTAL_PAGE = BATCH_TOTAL_PAGE +1 ,T.IS_WAITINSERT = 1 WHERE T.BATCH_ID = ?";
		flag = dbHandler.execute(sql, bpTmpbatchTb.getBatchId());
		return flag;
	}

	
	public int updateSmMixedNopaperTb(Connection conn, String occurDate, String flowId) throws SQLException {
		DBHandler dbHandler = new DBHandler(Constants.CONN_POOL_NAME);
		int flag = 0;
		String sql = "UPDATE SM_MIXED_NOPAPER_TB T  SET T.STATUS = 1 WHERE T.OCCUR_DATE = ?  AND T.FLOW_ID = ?";
		flag = dbHandler.execute(sql, occurDate, flowId);
		return flag;
	}

}
