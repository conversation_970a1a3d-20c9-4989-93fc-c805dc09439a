{"remainingRequest": "E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\node_modules\\babel-loader\\lib\\index.js!E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\node_modules\\@babel\\runtime\\helpers\\esm\\construct.js", "dependencies": [{"path": "E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\node_modules\\@babel\\runtime\\helpers\\esm\\construct.js", "mtime": 456789000000}, {"path": "E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\babel.config.js", "mtime": 1686019818359}, {"path": "E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:aW1wb3J0ICJjb3JlLWpzL21vZHVsZXMvZXMucmVmbGVjdC5jb25zdHJ1Y3QuanMiOwppbXBvcnQgImNvcmUtanMvbW9kdWxlcy9lcy5vYmplY3QudG8tc3RyaW5nLmpzIjsKaW1wb3J0IHNldFByb3RvdHlwZU9mIGZyb20gIi4vc2V0UHJvdG90eXBlT2YuanMiOwppbXBvcnQgaXNOYXRpdmVSZWZsZWN0Q29uc3RydWN0IGZyb20gIi4vaXNOYXRpdmVSZWZsZWN0Q29uc3RydWN0LmpzIjsKZXhwb3J0IGRlZmF1bHQgZnVuY3Rpb24gX2NvbnN0cnVjdChQYXJlbnQsIGFyZ3MsIENsYXNzKSB7CiAgaWYgKGlzTmF0aXZlUmVmbGVjdENvbnN0cnVjdCgpKSB7CiAgICBfY29uc3RydWN0ID0gUmVmbGVjdC5jb25zdHJ1Y3QuYmluZCgpOwogIH0gZWxzZSB7CiAgICBfY29uc3RydWN0ID0gZnVuY3Rpb24gX2NvbnN0cnVjdChQYXJlbnQsIGFyZ3MsIENsYXNzKSB7CiAgICAgIHZhciBhID0gW251bGxdOwogICAgICBhLnB1c2guYXBwbHkoYSwgYXJncyk7CiAgICAgIHZhciBDb25zdHJ1Y3RvciA9IEZ1bmN0aW9uLmJpbmQuYXBwbHkoUGFyZW50LCBhKTsKICAgICAgdmFyIGluc3RhbmNlID0gbmV3IENvbnN0cnVjdG9yKCk7CiAgICAgIGlmIChDbGFzcykgc2V0UHJvdG90eXBlT2YoaW5zdGFuY2UsIENsYXNzLnByb3RvdHlwZSk7CiAgICAgIHJldHVybiBpbnN0YW5jZTsKICAgIH07CiAgfQogIHJldHVybiBfY29uc3RydWN0LmFwcGx5KG51bGwsIGFyZ3VtZW50cyk7Cn0="}, {"version": 3, "names": ["setPrototypeOf", "isNativeReflectConstruct", "_construct", "Parent", "args", "Class", "Reflect", "construct", "bind", "a", "push", "apply", "<PERSON><PERSON><PERSON><PERSON>", "Function", "instance", "prototype", "arguments"], "sources": ["E:/1_Project/XYD_Project/dop-4.0/dop-4.1-qianduan-unify/dopUnify/node_modules/@babel/runtime/helpers/esm/construct.js"], "sourcesContent": ["import setPrototypeOf from \"./setPrototypeOf.js\";\nimport isNativeReflectConstruct from \"./isNativeReflectConstruct.js\";\nexport default function _construct(Parent, args, Class) {\n  if (isNativeReflectConstruct()) {\n    _construct = Reflect.construct.bind();\n  } else {\n    _construct = function _construct(Parent, args, Class) {\n      var a = [null];\n      a.push.apply(a, args);\n      var Constructor = Function.bind.apply(Parent, a);\n      var instance = new Constructor();\n      if (Class) setPrototypeOf(instance, Class.prototype);\n      return instance;\n    };\n  }\n  return _construct.apply(null, arguments);\n}"], "mappings": ";;AAAA,OAAOA,cAAc,MAAM,qBAAqB;AAChD,OAAOC,wBAAwB,MAAM,+BAA+B;AACpE,eAAe,SAASC,UAAU,CAACC,MAAM,EAAEC,IAAI,EAAEC,KAAK,EAAE;EACtD,IAAIJ,wBAAwB,EAAE,EAAE;IAC9BC,UAAU,GAAGI,OAAO,CAACC,SAAS,CAACC,IAAI,EAAE;EACvC,CAAC,MAAM;IACLN,UAAU,GAAG,SAASA,UAAU,CAACC,MAAM,EAAEC,IAAI,EAAEC,KAAK,EAAE;MACpD,IAAII,CAAC,GAAG,CAAC,IAAI,CAAC;MACdA,CAAC,CAACC,IAAI,CAACC,KAAK,CAACF,CAAC,EAAEL,IAAI,CAAC;MACrB,IAAIQ,WAAW,GAAGC,QAAQ,CAACL,IAAI,CAACG,KAAK,CAACR,MAAM,EAAEM,CAAC,CAAC;MAChD,IAAIK,QAAQ,GAAG,IAAIF,WAAW,EAAE;MAChC,IAAIP,KAAK,EAAEL,cAAc,CAACc,QAAQ,EAAET,KAAK,CAACU,SAAS,CAAC;MACpD,OAAOD,QAAQ;IACjB,CAAC;EACH;EACA,OAAOZ,UAAU,CAACS,KAAK,CAAC,IAAI,EAAEK,SAAS,CAAC;AAC1C"}]}