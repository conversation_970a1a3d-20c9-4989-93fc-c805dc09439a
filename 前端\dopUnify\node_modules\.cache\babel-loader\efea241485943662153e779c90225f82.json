{"remainingRequest": "D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\node_modules\\thread-loader\\dist\\cjs.js!D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\node_modules\\babel-loader\\lib\\index.js!D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\src\\views\\system\\notice\\query\\component\\table\\index.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\src\\views\\system\\notice\\query\\component\\table\\index.vue", "mtime": 1688371436312}, {"path": "D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\babel.config.js", "mtime": 1686019818359}, {"path": "D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\.babelrc", "mtime": 1705285078668}, {"path": "D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\node_modules\\thread-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1655715099000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KCmltcG9ydCB7IGRhdGUxMEZvcm1hdCwgb3JnYW5OYW1lRm9ybWF0IH0gZnJvbSAnQC9maWx0ZXJzJzsgLy8g6L+H5ruk5ZmoCi8vIGltcG9ydCBTdW5QYWdpbmF0aW9uIGZyb20gJ0AvY29tcG9uZW50cy9TdW5UYWJsZS9TdW5QYWdpbmF0aW9uJyAvLyDpobXnoIEKLy8gaW1wb3J0IFJlc2l6ZU1peGluIGZyb20gJ0AvdXRpbHMvUmVzaXplSGFuZGxlcicgLy8g5pW05L2T6aG16Z2i5piv5ZCm5qC55o2u5oC76auY6YWN572uCgppbXBvcnQgeyBTdW5Ob3RpY2VEaWFsb2cgfSBmcm9tICdAL2NvbXBvbmVudHMnOyAvLyDlhazlkYrlvLnnqpcKaW1wb3J0IHsgdXBsb2FkRmlsZSB9IGZyb20gJ0AvdXRpbHMvY29tbW9uJzsgLy8g5YWs5YWx5pa55rOVCmltcG9ydCB7IGRhdGVOb3dGb3JtYXQxMCB9IGZyb20gJ0AvdXRpbHMvZGF0ZS5qcyc7IC8vIOaXpeacn+agvOW8j+WMlgppbXBvcnQgUHJpbnQgZnJvbSAnLi4vcHJpbnQvaW5kZXgudnVlJzsgLy8g5omT5Y2w57uE5Lu2CgppbXBvcnQgeyBzeXN0ZW0sIEhvbWUgfSBmcm9tICdAL2FwaSc7CnZhciBub3RpY2VNb2RpZnkgPSBIb21lLm5vdGljZU1vZGlmeTsKdmFyIF9zeXN0ZW0kU3lzUXVlcnkgPSBzeXN0ZW0uU3lzUXVlcnksCiAgcXVlcnkgPSBfc3lzdGVtJFN5c1F1ZXJ5LnF1ZXJ5LAogIHJlYWROdW0gPSBfc3lzdGVtJFN5c1F1ZXJ5LnJlYWROdW07CmV4cG9ydCBkZWZhdWx0IHsKICBuYW1lOiAnVGFibGVMaXN0JywKICBjb21wb25lbnRzOiB7CiAgICBTdW5Ob3RpY2VEaWFsb2c6IFN1bk5vdGljZURpYWxvZywKICAgIFByaW50OiBQcmludAogIH0sCiAgZmlsdGVyczogewogICAgZGF0ZTEwRm9ybWF0OiBkYXRlMTBGb3JtYXQsCiAgICAvLyDml6XmnJ/moLzlvI/ljJYgWVlZWS1NTS1ERAogICAgb3JnYW5OYW1lRm9ybWF0OiBvcmdhbk5hbWVGb3JtYXQsCiAgICAvLyDmnLrmnoTlj7fmoLzlvI/ljJYKICAgIHBhcGVyVHlwZTogZnVuY3Rpb24gcGFwZXJUeXBlKCkgewogICAgICByZXR1cm47CiAgICB9CiAgfSwKICBwcm9wczogewogICAgZGVmYXVsdEZvcm06IHsKICAgICAgdHlwZTogT2JqZWN0LAogICAgICBkZWZhdWx0OiBmdW5jdGlvbiBfZGVmYXVsdCgpIHsKICAgICAgICByZXR1cm4ge307CiAgICAgIH0KICAgIH0sCiAgICByb2xlbGlzdDogewogICAgICB0eXBlOiBBcnJheSwKICAgICAgZGVmYXVsdDogZnVuY3Rpb24gX2RlZmF1bHQoKSB7CiAgICAgICAgcmV0dXJuIFtdOwogICAgICB9CiAgICB9CiAgfSwKICBkYXRhOiBmdW5jdGlvbiBkYXRhKCkgewogICAgcmV0dXJuIHsKICAgICAgbm90aWNlOiB7CiAgICAgICAgdG90YWxOdW06IDAsCiAgICAgICAgLy8g5b2T5YmN54q25oCB5YWs5ZGK55qE5pWw6YePCiAgICAgICAgZGF0YTogW10sCiAgICAgICAgLy8g5YWs5ZGK5pWw5o2uCiAgICAgICAgbG9hZGluZzogZmFsc2UsCiAgICAgICAgLy8g5Yqg6L295Yqo55S7CiAgICAgICAgcGFnZUxpc3Q6IHsKICAgICAgICAgIHRvdGFsTnVtOiAwLAogICAgICAgICAgLy8g5oC75pWw6YePCiAgICAgICAgICBjdXJyZW50UGFnZTogMSwKICAgICAgICAgIC8vIOW9k+WJjemhtQogICAgICAgICAgcGFnZVNpemU6IHRoaXMuJHN0b3JlLmdldHRlcnMucGFnZVNpemUgLy8g5b2T5YmN6aG15pi+56S65p2h5pWwCiAgICAgICAgfSAvLyDpobXnoIEKICAgICAgfSwKCiAgICAgIG5vdGljZU51bTogewogICAgICAgIHVuUmVhZDogMCwKICAgICAgICByZWFkOiAwLAogICAgICAgIHNlbmQ6IDAKICAgICAgfSwKICAgICAgLy8g5LiJ56eN5YWs5ZGK54q25oCB55qE5pWw6YePCiAgICAgIGRpYWxvZzogewogICAgICAgIHZpc2libGU6IGZhbHNlLAogICAgICAgIC8vIOW8gOWQry/lhbPpl63lvLnnqpcKICAgICAgICBidG5DYW5jbGU6IGZhbHNlLAogICAgICAgIC8vIOWPlua2iOaMiemSrgogICAgICAgIGJ0blN1Ym1pdDogZmFsc2UsCiAgICAgICAgLy8g56Gu5a6a5oyJ6ZKuCiAgICAgICAgY29tcG9uZW50UHJvcHM6IHsKICAgICAgICAgIC8vIOW8ueWHuuahhumFjee9ruWxnuaApwogICAgICAgICAgdGl0bGU6ICfmn6XnnIvlhazlkYonLAogICAgICAgICAgd2lkdGg6ICc3NC44cmVtJyAvLyDlvZPliY3lvLnlh7rmoYblrr3luqYKICAgICAgICB9LAoKICAgICAgICBub3RpY2VDb25maWc6IHsKICAgICAgICAgIGltZ1NyYzogcmVxdWlyZSgnQC9hc3NldHMvaW1nL290aGVyL25vdGljZV9pbWFnZXMvbm90aWNlQmFja2dyb3VuZC5wbmcnKSwKICAgICAgICAgIC8vIOiDjOaZr+WbvueJh+WKoOi9vQogICAgICAgICAgdGl0bGU6ICfmoIfpopgnLAogICAgICAgICAgLy8g5YWs5ZGK5qCH6aKYCiAgICAgICAgICBpbmZvOiBbXSwKICAgICAgICAgIC8vIOWPkeW4g+acuuaehOOAgeWPkeW4g+iAheWQjeensOOAgeWPkeW4g+aXtumXtAogICAgICAgICAgcmVhZE51bTogJycsCiAgICAgICAgICAvLyDpmIXor7vph48KICAgICAgICAgIGNvbnRlbnQ6ICcnLAogICAgICAgICAgLy8g5YWs5ZGK5q2j5paHCiAgICAgICAgICBmaWxlczogW10KICAgICAgICB9CiAgICAgIH0sCiAgICAgIG5vd1N0YXRlOiAnMCcsCiAgICAgIC8vIOW9k+WJjeafpeivoueahOWFrOWRiueKtuaAgSAnMCct5pyq6ZiFICcxJy3lt7LpmIUgJzInLeW3suWPkQogICAgICB0b3RhbEhlaWdodDogewogICAgICAgIGhlaWdodDogMAogICAgICB9LAogICAgICAvLyDkuIvmlrnnu4Tku7bliqjmgIHpq5jluqYKICAgICAgdWxIZWlnaHQ6IHsKICAgICAgICBoZWlnaHQ6IDAKICAgICAgfQogICAgfTsKICB9LAogIGNvbXB1dGVkOiB7CiAgICAvLyDorqHnrpflhazlkYrnsbvlnosg5pyq6K+7LeW3suivuy3lt7Llj5HpgIEKICAgIG5vdGljZUljb246IGZ1bmN0aW9uIG5vdGljZUljb24oKSB7CiAgICAgIHJldHVybiB0aGlzLm5vd1N0YXRlID09PSAnMCcgPyAnIzM3NjRmYycgOiB0aGlzLm5vd1N0YXRlID09PSAnMScgPyAnI2Q1ZDVkNScgOiAnI2Q1ZDVkNSc7CiAgICB9LAogICAgLy8g6K6h566X5bGe5oCn6LCD55Soc3RvcmXkuK3nmoTkuLvpopjoibIKICAgIHZhckNvbG9yOiBmdW5jdGlvbiB2YXJDb2xvcigpIHsKICAgICAgcmV0dXJuIHsKICAgICAgICAnLS1jb2xvcic6IHRoaXMuJHN0b3JlLnN0YXRlLnNldHRpbmdzLnRoZW1lCiAgICAgIH07CiAgICB9CiAgfSwKICB3YXRjaDogewogICAgLy8gbG9hZGluZyh2YWx1ZSkgewogICAgLy8gICB0aGlzLmxpc3RMb2FkaW5nID0gdGhpcy5sb2FkaW5nCiAgICAvLyB9CiAgfSwKICBjcmVhdGVkOiBmdW5jdGlvbiBjcmVhdGVkKCkge30sCiAgbW91bnRlZDogZnVuY3Rpb24gbW91bnRlZCgpIHsKICAgIHZhciBfdGhpcyA9IHRoaXM7CiAgICB0aGlzLiRuZXh0VGljayhmdW5jdGlvbiAoKSB7CiAgICAgIF90aGlzLnRvdGFsSGVpZ2h0LmhlaWdodCA9IF90aGlzLiRhdHRycy5oZWlnaHQgLSA0MCArICdweCc7IC8vIDIw5LiK5LiL57uE5Lu25LmL6Ze05Li66L656LedCiAgICAgIF90aGlzLnVsSGVpZ2h0LmhlaWdodCA9IF90aGlzLiRhdHRycy5oZWlnaHQgLSA0MCAtIDE5MSArICdweCc7CiAgICB9KTsKICAgIC8vIOafpeeci+WFrOWRiu+8iOmihOiniO+8iQogICAgdGhpcy4kYnVzLiRvbignbm90aWNlVXBkYXRlJywgZnVuY3Rpb24gKGRhdGEpIHsKICAgICAgaWYgKGRhdGEpIHsKICAgICAgICBfdGhpcy5xdWVyeUxpc3QoKTsKICAgICAgfQogICAgfSk7CiAgfSwKICAvLyDnu4Tku7bplIDmr4HliY3vvIzmuIXpmaTlrprml7blmagKICBiZWZvcmVEZXN0cm95OiBmdW5jdGlvbiBiZWZvcmVEZXN0cm95KCkgewogICAgY2xlYXJJbnRlcnZhbCh0aGlzLnRpbWVyKTsKICAgIHRoaXMuJGJ1cy4kb2ZmKCdub3RpY2VVcGRhdGUnKTsKICB9LAogIG1ldGhvZHM6IHsKICAgIC8qKg0KICAgICAqIOWFrOWRiuWIl+ihqOWNleWHu+ihjA0KICAgICAqIEBwYXJhbSB7T2JqZWN0fSByb3cg5b2T5YmN6KGM5YWs5ZGKICovCiAgICBjdXJyZW50Q2hhbmdlOiBmdW5jdGlvbiBjdXJyZW50Q2hhbmdlKHJvdykgewogICAgICB2YXIgX3RoaXMyID0gdGhpczsKICAgICAgLy8g5riF56m65q+P5qyh5by556qX5pWw5o2uCiAgICAgIHRoaXMuZGlhbG9nLm5vdGljZUNvbmZpZy5pbmZvID0gW107CiAgICAgIHRoaXMuZGlhbG9nLnZpc2libGUgPSB0cnVlOwogICAgICAvLyDlvLnnqpfmiZPlvIAg5aaC5p6c6ZiF6K+754q25oCB5Li65pyq6ZiF77yM5pS55Li65bey6ZiF77yM5ZCM5pe25L+u5pS557O757uf5raI5oGv55u45YWz5L+h5oGvCiAgICAgIGlmIChyb3cucmVhZF9zdGF0ZSA9PT0gJzAnKSB7CiAgICAgICAgdmFyIHBhcmFtSnNvbiA9IHsKICAgICAgICAgIG5vdGljZV9pZDogcm93Lm5vdGljZV9pZCwKICAgICAgICAgIG5vdGljZV90aXRsZTogcm93Lm5vdGljZV90aXRsZSwKICAgICAgICAgIG5vdGljZV9jb250ZW50OiByb3cubm90aWNlX2NvbnRlbnQsCiAgICAgICAgICBmaWxlX3VybDogcm93LmZpbGVfdXJsCiAgICAgICAgfTsKICAgICAgICB0aGlzLmZpcnN0UGFnZShyb3cubm90aWNlX2lkLCAnJywgcGFyYW1Kc29uKTsKICAgICAgfQogICAgICAvLyDmn6Xor6LlhazlkYrmmI7nu4YKICAgICAgdmFyIG1zZyA9IHsKICAgICAgICBwYXJhbWV0ZXJMaXN0OiBbXSwKICAgICAgICBub3RpY2VfaWQ6IHJvdy5ub3RpY2VfaWQsCiAgICAgICAgcmVhZF9zdGF0ZTogcm93LnJlYWRfc3RhdGUsCiAgICAgICAgdXNlcl9ubzogdGhpcy4kc3RvcmUuZ2V0dGVycy51c2VyTm8sCiAgICAgICAgY3VycmVudFBhZ2U6IDEsCiAgICAgICAgcGFnZVNpemU6IHRoaXMuJHN0b3JlLmdldHRlcnMucGFnZVNpemUsCiAgICAgICAgLy8g5b2T5YmN6aG15pi+56S65p2h5pWwCiAgICAgICAgb3JnYW5fbm86IHRoaXMuJHN0b3JlLmdldHRlcnMub3JnYW5ObwogICAgICB9OwogICAgICAvLyDmn6Xor6LpmIXor7vph48KICAgICAgdmFyIHJlYWRNc2cgPSB7CiAgICAgICAgcGFyYW1ldGVyTGlzdDogW10sCiAgICAgICAgbm90aWNlX2lkOiByb3cubm90aWNlX2lkLAogICAgICAgIHVzZXJfbm86IHRoaXMuJHN0b3JlLmdldHRlcnMudXNlck5vLAogICAgICAgIG9yZ2FuX25vOiB0aGlzLiRzdG9yZS5nZXR0ZXJzLm9yZ2FuTm8sCiAgICAgICAgcHVibGlzaF91c2VyOiByb3cucHVibGlzaF91c2VyCiAgICAgIH07CiAgICAgIHRoaXMuJG5leHRUaWNrKGZ1bmN0aW9uICgpIHsKICAgICAgICAvLyDlvLnnqpfliqDovb3lrozmr5XlkI7otYvlgLwKICAgICAgICBxdWVyeShtc2cpLnRoZW4oZnVuY3Rpb24gKHJlc3BvbnNlKSB7CiAgICAgICAgICB2YXIgbm90aWNlID0gcmVzcG9uc2UucmV0TWFwLm5vdGljZTsKICAgICAgICAgIF90aGlzMi5kaWFsb2cubm90aWNlQ29uZmlnLnRpdGxlID0gbm90aWNlWzBdLm5vdGljZV90aXRsZTsgLy8g5qCH6aKYCiAgICAgICAgICBfdGhpczIuZGlhbG9nLm5vdGljZUNvbmZpZy5pbmZvID0gbm90aWNlOwogICAgICAgICAgX3RoaXMyLmRpYWxvZy5ub3RpY2VDb25maWcuY29udGVudCA9IG5vdGljZVswXS5ub3RpY2VfY29udGVudDsgLy8g5YWs5ZGK5YaF5a65CiAgICAgICAgICBpZiAobm90aWNlWzBdLmZpbGVfdXJsKSB7CiAgICAgICAgICAgIF90aGlzMi5kaWFsb2cubm90aWNlQ29uZmlnLmZpbGVzID0gdXBsb2FkRmlsZShub3RpY2VbMF0uZmlsZV91cmwpOwogICAgICAgICAgfSBlbHNlIHsKICAgICAgICAgICAgX3RoaXMyLmRpYWxvZy5ub3RpY2VDb25maWcuZmlsZXMgPSBbXTsKICAgICAgICAgIH0KICAgICAgICB9KS5jYXRjaChmdW5jdGlvbiAoKSB7fSk7CiAgICAgICAgcmVhZE51bShyZWFkTXNnKS50aGVuKGZ1bmN0aW9uIChyZXNwb25zZSkgewogICAgICAgICAgdmFyIHJlYWRfbnVtID0gcmVzcG9uc2UucmV0TWFwLnJlYWRfbnVtOwogICAgICAgICAgX3RoaXMyLmRpYWxvZy5ub3RpY2VDb25maWcucmVhZE51bSA9IHJlYWRfbnVtOwogICAgICAgIH0pOwogICAgICB9KTsKICAgIH0sCiAgICAvKioNCiAgICAgKiDlhazlkYrkv6Hmga86IOabtOaUuemYheivu+eKtuaAgQ0KICAgICAqIEBwYXJhbSBub3RpY2VfaWQ65YWs5ZGKaWQNCiAgICAgKiBAcGFyYW0gbXNnX25vOuezu+e7n+a2iOaBr+e8luWPtw0KICAgICAqIEBwYXJhbSBwYXJhbUpzb27vvJrns7vnu5/mtojmga/lj4LmlbANCiAgICAgKiAqLwogICAgZmlyc3RQYWdlOiBmdW5jdGlvbiBmaXJzdFBhZ2Uobm90aWNlX2lkLCBtc2dfbm8sIHBhcmFtSnNvbikgewogICAgICB2YXIgX3RoaXMzID0gdGhpczsKICAgICAgdmFyIHJlYWRfdGltZSA9IGRhdGVOb3dGb3JtYXQxMCgpOyAvLyDlvZPliY3ml7bpl7TnmoTljYHkvY3mlbDmoLzlvI8KICAgICAgdmFyIG1zZyA9IHsKICAgICAgICBub3RpY2VfaWQ6IG5vdGljZV9pZCwKICAgICAgICB1c2VyX25vOiB0aGlzLiRzdG9yZS5nZXR0ZXJzLnVzZXJObywKICAgICAgICBvcmdhbl9ubzogdGhpcy4kc3RvcmUuZ2V0dGVycy5vcmdhbk5vLAogICAgICAgIHJlYWRfdGltZTogcmVhZF90aW1lLAogICAgICAgIG1zZ19ubzogbXNnX25vLAogICAgICAgIHBhcnBhbURhdGE6IHBhcmFtSnNvbgogICAgICB9OwogICAgICBub3RpY2VNb2RpZnkobXNnKS50aGVuKGZ1bmN0aW9uIChyZXNwb25zZSkgewogICAgICAgIF90aGlzMy4kYnVzLiRlbWl0KCdub3RpY2VVcGRhdGUnLCB0cnVlKTsKICAgICAgfSk7CiAgICB9LAogICAgLyoqDQogICAgICog5oyJ6ZKuOiDmn6Xor6INCiAgICAgKiBAcGFyYW0ge051bWJlcn0gY3VycmVudFBhZ2Ug5b2T5YmN6aG1DQogICAgICogQHBhcmFtIHtTdHJpbmd9IHN0YXRlIOW9k+WJjemhteWFrOWRiueKtuaAgSAqLwogICAgcXVlcnlMaXN0OiBmdW5jdGlvbiBxdWVyeUxpc3QoY3VycmVudFBhZ2UsIHN0YXRlKSB7CiAgICAgIHZhciBfdGhpczQgPSB0aGlzOwogICAgICB0aGlzLm5vdGljZS5wYWdlTGlzdC5jdXJyZW50UGFnZSA9IGN1cnJlbnRQYWdlOwogICAgICB0aGlzLnNob3dMb2FkaW5nKHRydWUpOwogICAgICAvLyDmn6Xor6Lml7bvvIzpu5jorqTnmoTnirbmgIHmmK8wLeacqumYhQogICAgICBpZiAoc3RhdGUpIHsKICAgICAgICB0aGlzLm5vd1N0YXRlID0gc3RhdGU7CiAgICAgIH0KICAgICAgdmFyIG1zZyA9IHsKICAgICAgICBvcmdhbl9ubzogdGhpcy4kc3RvcmUuZ2V0dGVycy5vcmdhbk5vLAogICAgICAgIHJvbGVfbm86IHRoaXMuJHN0b3JlLmdldHRlcnMucm9sZU5vLAogICAgICAgIHVzZXJfbm86IHRoaXMuJHN0b3JlLmdldHRlcnMudXNlck5vLAogICAgICAgIHJlYWRfc3RhdGU6IHRoaXMubm93U3RhdGUsCiAgICAgICAgbm90aWNlSW5mb19rZXl3b3JkOiB0aGlzLmRlZmF1bHRGb3JtLmtleVdvcmQsCiAgICAgICAgY3VycmVudFBhZ2U6IGN1cnJlbnRQYWdlIHx8IDEsCiAgICAgICAgcGFnZVNpemU6IHRoaXMubm90aWNlLnBhZ2VMaXN0LnBhZ2VTaXplIC8vIOW9k+WJjemhteaYvuekuuadoeaVsAogICAgICB9OwoKICAgICAgcXVlcnkobXNnKS50aGVuKGZ1bmN0aW9uIChyZXNwb25zZSkgewogICAgICAgIHZhciBfcmVzcG9uc2UkcmV0TWFwID0gcmVzcG9uc2UucmV0TWFwLAogICAgICAgICAgbm90aWNlcyA9IF9yZXNwb25zZSRyZXRNYXAubm90aWNlcywKICAgICAgICAgIGFsbFJvdyA9IF9yZXNwb25zZSRyZXRNYXAuYWxsUm93LAogICAgICAgICAgdW5yZWFkX251bXMgPSBfcmVzcG9uc2UkcmV0TWFwLnVucmVhZF9udW1zLAogICAgICAgICAgbm90aWNlX251bXMgPSBfcmVzcG9uc2UkcmV0TWFwLm5vdGljZV9udW1zLAogICAgICAgICAgcHVibGlzaF9udW1zID0gX3Jlc3BvbnNlJHJldE1hcC5wdWJsaXNoX251bXM7CiAgICAgICAgX3RoaXM0Lm5vdGljZS5kYXRhID0gbm90aWNlczsgLy8g6KGo5qC85pWw5o2uCiAgICAgICAgX3RoaXM0Lm5vdGljZS50b3RhbE51bSA9IGFsbFJvdzsgLy8g5p+l6K+i5pWw5o2u5oC75p2h5pWwCiAgICAgICAgLy8g5paH5a2X6ZO+5o6l5aSE55qE5YWs5ZGK5pWw6YeP6LWL5YC8CiAgICAgICAgX3RoaXM0Lm5vdGljZU51bS51blJlYWQgPSB1bnJlYWRfbnVtczsgLy8g5pyq6K+75YWs5ZGK5pWw6YePCiAgICAgICAgX3RoaXM0Lm5vdGljZU51bS5yZWFkID0gbm90aWNlX251bXM7IC8vIOW3suivu+WFrOWRiuaVsOmHjwogICAgICAgIF90aGlzNC5ub3RpY2VOdW0uc2VuZCA9IHB1Ymxpc2hfbnVtczsgLy8g5bey5Y+R5biD5YWs5ZGK5pWw6YePCiAgICAgICAgX3RoaXM0Lm5vdGljZS5wYWdlTGlzdC50b3RhbE51bSA9IGFsbFJvdzsKICAgICAgICBfdGhpczQuc2hvd0xvYWRpbmcoZmFsc2UpOwogICAgICB9KS5jYXRjaChmdW5jdGlvbiAoKSB7fSk7CiAgICB9LAogICAgLy8g5omT5Y2wCiAgICBoYW5kbGVQcmludDogZnVuY3Rpb24gaGFuZGxlUHJpbnQoKSB7CiAgICAgIHRoaXMuJHByaW50KHRoaXMuJHJlZnMucHJpbnRSZWYpOwogICAgfSwKICAgIC8qKg0KICAgICAqIOW8ueWHuuahhiAtIOWFs+mXrQ0KICAgICAqIEBwYXJhbSB7Qm9vbGVhbn0gcGFyYW0g5by55Ye65qGG5pi+56S66ZqQ6JeP6YWN572uKi8KICAgIGNoYW5nZVZpc2libGU6IGZ1bmN0aW9uIGNoYW5nZVZpc2libGUocGFyYW0pIHsKICAgICAgdGhpcy5kaWFsb2cudmlzaWJsZSA9IHBhcmFtOwogICAgICB0aGlzLnF1ZXJ5TGlzdCh0aGlzLm5vdGljZS5wYWdlTGlzdC5jdXJyZW50UGFnZSwgdGhpcy5ub3dTdGF0ZSk7CiAgICB9LAogICAgLyoqDQogICAgICog5by55Ye65qGGIC0g56Gu6K6kKi8KICAgIGRpYWxvZ1N1bWJpdDogZnVuY3Rpb24gZGlhbG9nU3VtYml0KCkgewogICAgICB0aGlzLmNoYW5nZVZpc2libGUoZmFsc2UpOwogICAgfSwKICAgIC8qKg0KICAgICAqIOWKoOi9veS4reWKqOeUu+mFjee9rg0KICAgICAqIEBwYXJhbSB7Qm9vbGVhbn1wYXJhbSDlvZPliY3liqDovb3mmL7npLrnirbmgIEqLwogICAgc2hvd0xvYWRpbmc6IGZ1bmN0aW9uIHNob3dMb2FkaW5nKHBhcmFtKSB7CiAgICAgIHRoaXMubm90aWNlLmxvYWRpbmcgPSBwYXJhbTsKICAgIH0sCiAgICAvKioNCiAgICAgKumhteeggeabtOaWsA0KICAgICAqQHBhcmFtIHtPYmplY3R9cGFyYW0g5b2T5YmN6aG156CB5L+h5oGvKi8KICAgIGdldExpc3Q6IGZ1bmN0aW9uIGdldExpc3QocGFyYW0pIHsKICAgICAgdGhpcy5xdWVyeUxpc3QocGFyYW0uY3VycmVudFBhZ2UpOwogICAgICAvLyB0aGlzLm5vdGljZS5wYWdlTGlzdC5wYWdlU2l6ZSA9IHBhcmFtLmN1cnJlbnRQYWdlCiAgICB9CiAgfQp9Ow=="}, null]}