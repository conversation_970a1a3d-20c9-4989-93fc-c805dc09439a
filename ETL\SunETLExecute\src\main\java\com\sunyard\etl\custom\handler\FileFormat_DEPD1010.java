package com.sunyard.etl.custom.handler;

import java.io.BufferedReader;
import java.io.File;
import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.InputStreamReader;
import java.io.OutputStreamWriter;
import java.io.PrintWriter;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.List;

import com.sunyard.etl.system.common.Constants;
import org.springframework.stereotype.Service;

import com.sunyard.etl.system.dao.DataDateDAO;
import com.sunyard.etl.system.dao.impl.DataDateDAOImpl;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.enums.JobStartEnum;
import com.xxl.job.core.handler.IJobHandler;
import com.xxl.job.core.handler.annotation.JobHandler;
import com.xxl.job.core.log.XxlJobLogger;

@JobHandler(value = "FileFormat_DEPD1010", name = "DEPD1010文件格式化")
@Service
public class FileFormat_DEPD1010 extends IJobHandler {

	private static final long serialVersionUID = 1L;

	private static DataDateDAO dateDao = new DataDateDAOImpl();

	@Override
	public ReturnT<String> execute(String jobId, String... arg1) throws Exception {
		XxlJobLogger.log("开始DEPD1010文件格式化...");
		String jobDate = dateDao.getDataDate();
		if (null != arg1[0]) {
			String dirPath = arg1[0].toString().replace("@", jobDate);
			File dir = new File(dirPath);
			if (!dir.isDirectory()) {
				XxlJobLogger.log("INFO: 资源不足，目录不存在：" + dir, jobId + "");
				return new ReturnT<String>(ReturnT.FAIL_CODE, JobStartEnum.TASK_STATE_NO_RESOURCE.getCode(),
						"文件目录" + dir.getPath() + "不存在");
			}
			File preFile = new File(dir, "DEPD1010_"+jobDate+".txt");
			File file = new File(dir, "DEPD1010_"+jobDate+"_proc.txt");
			if (file.exists()) {
				XxlJobLogger.log("INFO: 文件已存在，先删除后再转换：" + file.getPath(), jobId + "");
				file.delete();
			}
			PrintWriter pw;
			pw = new PrintWriter(new OutputStreamWriter(new FileOutputStream(file), "GBK"));
			if (preFile.length()==0) {
				pw.print("|||");
				pw.flush();
				pw.close();
				XxlJobLogger.log("INFO: 源文件不存在，生成转换文件：" + preFile.getPath() + " >> " + file.getPath(), jobId + "");
				return ReturnT.SUCCESS;
			}
			BufferedReader br = new BufferedReader(new InputStreamReader(new FileInputStream(preFile), "GBK"));
			
			String line = null;
			String siteNo = "";
			String siteName = "";
			String occurDate = "";
			String accept_flag = "";//受理标识
			int index = -1;
			String customer_type = "";//客户类型
			String account_no = "";//账号
			String product_type = "";//产品类别
			String sub_account_no = "";//子账户类别
			String volumn_no = "";//册号
			String serial_no = "";//序号
			String freeze_type = "";//冻结类型
			String tx_code = "";//交易码
			String tx_log_no = "";//交易日志号
			String freeze_reason = "";//冻结原因
			String freeze_start_date = "";//冻结起始日
			String freeze_end_date = "";//冻结结束日
			String currency_type = "";//货币
			String freeze_amount = "";//冻结金额
			String prac_freeze_amount = ""; //实际冻结金额
			String unfreeze_amount = "";//解冻金额
			String teller_no = "";//交易柜员
			String auth_teller_no = "";//授权柜员
			String open_site = "";//开户网点
			String memo = "";//备注
			String btLine1 = "";
			String[] strArr1 = null;
			List list1 = new ArrayList();
			String btLine2 = "";
			String[] strArr2 = null;
			List list2 = new ArrayList();
			try {
				while ((line = br.readLine()) != null || index ==2 ) {
					if(line.contains("1              ") && line.contains("-DEPD1010")){
						siteNo = line.substring(line.indexOf("( ")+"( ".length(),line.indexOf("-DEPD1010"));
						index = -1;
						continue;
					} 
					if(line.contains("机构 :") && line.contains("日期 :")){
						siteName = line.substring(line.indexOf("机构 :  ")+"机构 :  ".length(),line.indexOf("日期 :")).trim().replace("　", "");
						occurDate = line.substring(line.indexOf("日期 : ")+"日期 : ".length(),line.indexOf("日期 : ")+"日期 : ".length()+10).trim();
						occurDate = new SimpleDateFormat("yyyyMMdd").format(new SimpleDateFormat("yyyy/MM/dd").parse(occurDate));
						index = -1;
						continue;
					}
					if(line.contains("受理标识 :")){
						accept_flag = line.substring(line.indexOf("受理标识 :  ")+"受理标识 :  ".length()).trim().replace("　", "");
						index = -1;
						continue;
					}
					if(line.contains("客户类型") && line.contains("产品类别") && line.contains("子账户类别") ){
						btLine1 = line;
						strArr1 = new String[]{
								btLine1.substring(0,btLine1.indexOf("客户类型 ")),	
								btLine1.substring(btLine1.indexOf("客户类型 "),btLine1.indexOf("账号 ")),	
								btLine1.substring(btLine1.indexOf("账号 "),btLine1.indexOf("产品类别 ")),	
								btLine1.substring(btLine1.indexOf("产品类别 "),btLine1.indexOf("子账户类别 ")),	
								btLine1.substring(btLine1.indexOf("子账户类别 "),btLine1.indexOf("册号")),	
								btLine1.substring(btLine1.indexOf("册号"),btLine1.indexOf("序号 ")),	
								btLine1.substring(btLine1.indexOf("序号 "),btLine1.indexOf("冻结类型 ")),	
								btLine1.substring(btLine1.indexOf("冻结类型 "),btLine1.indexOf("交易码 ")),	
								btLine1.substring(btLine1.indexOf("交易码 "),btLine1.indexOf("交易日志号 ")),	
								btLine1.substring(btLine1.indexOf("交易日志号 "),btLine1.indexOf("冻结原因 ")),	
								btLine1.substring(btLine1.indexOf("冻结原因 "),btLine1.indexOf("冻结起始日 ")),	
								btLine1.substring(btLine1.indexOf("冻结起始日 "),btLine1.indexOf("- ")),	
								btLine1.substring(btLine1.indexOf("- "),btLine1.indexOf("冻结结束日")),	
								btLine1.substring(btLine1.indexOf("冻结结束日"))};
						int idx = 0;
						list1 = new ArrayList();
						for(String str:strArr1){
							list1.add(new int[]{idx, str.getBytes("GBK").length});
							idx += str.getBytes("GBK").length;
						}
						index = -1;
						continue;
					}
					if(line.contains("货币") && line.contains("冻结金额") && line.contains("实际冻结金额")){
						btLine2 = line;
						strArr2 = new String[]{
								btLine2.substring(0,btLine2.indexOf("货币 ")),	
								//btLine2.substring(btLine2.indexOf("货币 "),btLine2.indexOf("冻结金额 ")),
								//btLine2.substring(btLine2.indexOf("冻结金额 "),btLine2.indexOf("实际冻结金额 ")),	
								//btLine2.substring(btLine2.indexOf("实际冻结金额 "),btLine2.indexOf("解冻金额 ")),	
								//btLine2.substring(btLine2.indexOf("解冻金额 "),btLine2.indexOf("交易柜员")),	
								btLine2.substring(btLine2.indexOf("货币 "),btLine2.indexOf("货币 ")+7),	
								btLine2.substring(btLine2.indexOf("货币 ")+7,btLine2.indexOf("冻结金额 ")+"冻结金额".length()),	
								btLine2.substring(btLine2.indexOf("冻结金额 ")+"冻结金额".length(),btLine2.indexOf("实际冻结金额 ")+"实际冻结金额".length()),	
								btLine2.substring(btLine2.indexOf("实际冻结金额 ")+"实际冻结金额".length(),btLine2.indexOf("解冻金额")+"解冻金额".length()),
								btLine2.substring(btLine2.indexOf("交易柜员"),btLine2.indexOf("授权柜员")),
								btLine2.substring(btLine2.indexOf("授权柜员"),btLine2.indexOf("开户网点")),
								btLine2.substring(btLine2.indexOf("开户网点"))};
						int idx = 0;
						list2 = new ArrayList();
						for(String str:strArr2){
							list2.add(new int[]{idx, str.getBytes("GBK").length});
							idx += str.getBytes("GBK").length;
						}
						index = -1;
						continue;
					}
					if(("".equals(line.trim()) && index!=2)||line.trim().equals("冻结／解冻交易清单（交易行）")||line.contains("=============================")||"冻结解冻类型 ".equals(line.trim())){
						index = -1;
						continue;
					}
					if(line.trim().equals("备注")){
						index = 0;
						continue;
					}
					if(index == 0){
						int lineLength = line.getBytes().length;
						int btLength = btLine1.getBytes().length;
						if(lineLength<btLength){
							line = String.format("%-"+btLength+"s", line);
						}
						byte[] arr = line.getBytes("GBK");
						customer_type = new String(arr,((int[])list1.get(1))[0],((int[])list1.get(1))[1],"GBK").trim(); //客户类型      
						account_no = new String(arr,((int[])list1.get(2))[0],((int[])list1.get(2))[1],"GBK").trim(); //账号         
						product_type = new String(arr,((int[])list1.get(3))[0],((int[])list1.get(3))[1],"GBK").trim();//产品类别       
						sub_account_no = new String(arr,((int[])list1.get(4))[0],((int[])list1.get(4))[1],"GBK").trim();//子账户类别   
						volumn_no = new String(arr,((int[])list1.get(5))[0],((int[])list1.get(5))[1],"GBK").trim();//册号              
						serial_no = new String(arr,((int[])list1.get(6))[0],((int[])list1.get(6))[1],"GBK").trim();//序号              
						freeze_type = new String(arr,((int[])list1.get(7))[0],((int[])list1.get(7))[1],"GBK").trim();//冻结类型        
						tx_code = new String(arr,((int[])list1.get(8))[0],((int[])list1.get(8))[1],"GBK").trim();//交易码              
						tx_log_no = new String(arr,((int[])list1.get(9))[0],((int[])list1.get(9))[1],"GBK").trim();//交易日志号        
						freeze_reason = new String(arr,((int[])list1.get(10))[0],((int[])list1.get(10))[1],"GBK").trim();//冻结原因      
						freeze_start_date = new String(arr,((int[])list1.get(11))[0],((int[])list1.get(11))[1],"GBK").trim();//冻结起始日
						freeze_start_date = new SimpleDateFormat("yyyyMMdd").format(new SimpleDateFormat("yyyy/MM/dd").parse(freeze_start_date));
						freeze_end_date = new String(arr,((int[])list1.get(13))[0],line.getBytes("GBK").length-((int[])list1.get(13))[0],"GBK").trim();//冻结结束日  
						freeze_end_date = new SimpleDateFormat("yyyyMMdd").format(new SimpleDateFormat("yyyy/MM/dd").parse(freeze_end_date));
						index = 1;
						continue;
					}
					if(index == 1){
						int lineLength = line.getBytes().length;
						int btLength = btLine2.getBytes().length;
						if(lineLength<btLength){
							line = String.format("%-"+btLength+"s", line);
						}
						byte[] arr = line.getBytes("GBK");
						currency_type = new String(arr,((int[])list2.get(1))[0],((int[])list2.get(1))[1],"GBK").trim();//货币
						freeze_amount = new String(arr,((int[])list2.get(2))[0],((int[])list2.get(2))[1],"GBK").trim().replace(",", "");//冻结金额
						prac_freeze_amount = new String(arr,((int[])list2.get(3))[0],((int[])list2.get(3))[1],"GBK").trim().replace(",", "");//实际冻结金额
						unfreeze_amount = new String(arr,((int[])list2.get(4))[0],((int[])list2.get(4))[1],"GBK").trim().replace(",", "");//解冻金额
						teller_no = new String(arr,((int[])list2.get(5))[0],((int[])list2.get(5))[1],"GBK").trim();//交易柜员
						auth_teller_no = new String(arr,((int[])list2.get(6))[0],((int[])list2.get(6))[1],"GBK").trim();//授权柜员
						open_site = new String(arr,((int[])list2.get(7))[0],line.getBytes("GBK").length-((int[])list2.get(7))[0],"GBK").trim();//开户网点
						index = 2;
						continue;
					}
					if(index == 2){
						memo = line.trim();//变更后内容
						String newLine = siteNo+"|"+siteName+"|"+occurDate+"|"
								+accept_flag+"|"	//受理标识
								+customer_type+"|"	//客户类型
								+account_no+"|"	//账号
								+product_type+"|"	//产品类别
								+sub_account_no+"|"	//子账户类别
								+volumn_no+"|"	//册号
								+serial_no+"|"	//序号
								+freeze_type+"|"	//冻结类型
								+tx_code+"|"	//交易码
								+tx_log_no+"|"	//交易日志号
								+freeze_reason+"|"	//冻结原因
								+freeze_start_date+"|"	//冻结起始日
								+freeze_end_date+"|"	//冻结结束日
								+currency_type+"|"	//货币
								+freeze_amount+"|"	//冻结金额
								+prac_freeze_amount+"|"	//实际冻结金额
								+unfreeze_amount+"|"	//解冻金额
								+teller_no+"|"	//交易柜员
								+auth_teller_no+"|"	//授权柜员
								+open_site+"|"	//开户网点
								+memo;	//备注
						
						if (!memo.contains("长期不动户")) {//需要过滤长期不动户的数据20220215hwj
							pw.print(newLine+System.getProperty("line.separator"));
						}
						index = 0;
						continue;
					}
				}
			} catch (IOException e) {
				XxlJobLogger.log("ERROR:转换异常："+e);
				e.printStackTrace();
			}finally{
				pw.flush();
				pw.close();
			}
			XxlJobLogger.log("INFO: 文件转换成功：" + file.getPath(), jobId + "");
			return ReturnT.SUCCESS;
		}
		return ReturnT.FAIL;
	}

	public static void main(String[] args) {
		FileFormat_DEPD1010 ff = new FileFormat_DEPD1010();
		try {
			ff.execute("9", "D:\\data\\@");
		} catch (Exception e) {
			// TODO Auto-generated catch block
			e.printStackTrace();
		}
	}
}
