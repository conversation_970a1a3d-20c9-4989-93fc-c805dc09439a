{"remainingRequest": "E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1\\数字运营平台-统一门户工程\\dopUnify\\node_modules\\thread-loader\\dist\\cjs.js!E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1\\数字运营平台-统一门户工程\\dopUnify\\node_modules\\babel-loader\\lib\\index.js!E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1\\数字运营平台-统一门户工程\\dopUnify\\node_modules\\qiankun\\es\\sandbox\\patchers\\dynamicAppend\\forStrictSandbox.js", "dependencies": [{"path": "E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1\\数字运营平台-统一门户工程\\dopUnify\\node_modules\\qiankun\\es\\sandbox\\patchers\\dynamicAppend\\forStrictSandbox.js", "mtime": 1667130453000}, {"path": "E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1\\数字运营平台-统一门户工程\\dopUnify\\babel.config.js", "mtime": 1716875191548}, {"path": "E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1\\数字运营平台-统一门户工程\\dopUnify\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1\\数字运营平台-统一门户工程\\dopUnify\\node_modules\\thread-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1\\数字运营平台-统一门户工程\\dopUnify\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, null]}