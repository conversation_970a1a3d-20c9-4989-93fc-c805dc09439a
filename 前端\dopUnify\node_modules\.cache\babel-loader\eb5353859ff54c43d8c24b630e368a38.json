{"remainingRequest": "E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-q<PERSON><PERSON>n\\数字运营平台-统一门户工程\\dopUnify\\node_modules\\babel-loader\\lib\\index.js!E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan\\数字运营平台-统一门户工程\\dopUnify\\node_modules\\eslint-loader\\index.js??ref--13-0!E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan\\数字运营平台-统一门户工程\\dopUnify\\src\\views\\system\\dataAuditing\\component\\table\\info.js", "dependencies": [{"path": "E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-<PERSON><PERSON><PERSON>n\\数字运营平台-统一门户工程\\dopUnify\\src\\views\\system\\dataAuditing\\component\\table\\info.js", "mtime": 1686019808326}, {"path": "E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-<PERSON><PERSON><PERSON>n\\数字运营平台-统一门户工程\\dopUnify\\babel.config.js", "mtime": 1686019818359}, {"path": "E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-<PERSON><PERSON><PERSON>n\\数字运营平台-统一门户工程\\dopUnify\\.babelrc", "mtime": 1705285078668}, {"path": "E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-<PERSON><PERSON><PERSON>n\\数字运营平台-统一门户工程\\dopUnify\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-<PERSON><PERSON><PERSON>n\\数字运营平台-统一门户工程\\dopUnify\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-<PERSON><PERSON><PERSON>n\\数字运营平台-统一门户工程\\dopUnify\\node_modules\\eslint-loader\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:aW1wb3J0IHsgdjEgYXMgdXVpZHYxIH0gZnJvbSAndXVpZCc7CmltcG9ydCBkZWZhdWx0U2V0dGluZ3MgZnJvbSAnQC9zZXR0aW5ncyc7CnZhciBwcmVmaXggPSBkZWZhdWx0U2V0dGluZ3Muc2VydmljZS5zeXN0ZW07IC8vIOWJjee8gOWFrOWFsei3r+eUsQovLyDooajlpLQKZXhwb3J0IHZhciBjb25maWdUYWJsZSA9IGZ1bmN0aW9uIGNvbmZpZ1RhYmxlKHRoYXQpIHsKICByZXR1cm4gW3sKICAgIG5hbWU6ICdpbnN0X2lkJywKICAgIGxhYmVsOiAn55Sz6K+35rWB5rC05Y+3JywKICAgIHdpZHRoOiAxODAsCiAgICBpZDogdXVpZHYxKCkKICB9LCB7CiAgICBuYW1lOiAnbWVudV9uYW1lJywKICAgIGxhYmVsOiAn5Y+C5pWw6YWN572u6aG16Z2iJywKICAgIHdpZHRoOiAxMzAsCiAgICBpZDogdXVpZHYxKCkKICB9LCB7CiAgICBuYW1lOiAnb3Blcl90eXBlX2ZsYWcnLAogICAgbGFiZWw6ICfnlLPor7fmk43kvZwnLAogICAgd2lkdGg6IDEyMCwKICAgIGlkOiB1dWlkdjEoKQogIH0sIHsKICAgIG5hbWU6ICdhcHBseV91c2VyJywKICAgIGxhYmVsOiAn55Sz6K+355So5oi3JywKICAgIHdpZHRoOiA5MCwKICAgIGlkOiB1dWlkdjEoKQogIH0sIHsKICAgIG5hbWU6ICdhcHBseV90aW1lJywKICAgIGxhYmVsOiAn55Sz6K+35pe26Ze0JywKICAgIHdpZHRoOiAxNzAsCiAgICBpZDogdXVpZHYxKCkKICB9LCB7CiAgICBuYW1lOiAnYXBwcm92ZV9hZ3JlZScsCiAgICBsYWJlbDogJ+WuoeaJuee7k+aenCcsCiAgICB3aWR0aDogMTEwLAogICAgaWQ6IHV1aWR2MSgpCiAgfSwgewogICAgbmFtZTogJ2N1cnJlbnRfYXBwcm92ZV9vcmdhbicsCiAgICBsYWJlbDogJ+W+heWuoeaJueacuuaehCcsCiAgICB3aWR0aDogMTYwLAogICAgaWQ6IHV1aWR2MSgpCiAgfSwgewogICAgbmFtZTogJ3JvbGVfbm8nLAogICAgbGFiZWw6ICflvoXlrqHmibnop5LoibInLAogICAgaWQ6IHV1aWR2MSgpCiAgfV07Cn07CgovLyDor6bmg4XooajlpLQKZXhwb3J0IHZhciBjb25maWdUYWJsZURldGFpbCA9IGZ1bmN0aW9uIGNvbmZpZ1RhYmxlRGV0YWlsKHRoYXQpIHsKICByZXR1cm4gW3sKICAgIG5hbWU6ICdub2RlX25hbWUnLAogICAgbGFiZWw6ICflpITnkIbnjq/oioInLAogICAgd2lkdGg6IDgwLAogICAgaWQ6IHV1aWR2MSgpCiAgfSwgewogICAgbmFtZTogJ29yZ2FuX25vJywKICAgIGxhYmVsOiAn5aSE55CG5py65p6EJywKICAgIHdpZHRoOiAxODAsCiAgICBpZDogdXVpZHYxKCkKICB9LCB7CiAgICBuYW1lOiAncm9sZV9ubycsCiAgICBsYWJlbDogJ+Wkh<PERSON>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"}, {"version": 3, "names": ["v1", "uuidv1", "defaultSettings", "prefix", "service", "system", "configTable", "that", "name", "label", "width", "id", "configTableDetail", "configTableFile", "config", "file_url", "component", "colSpan", "componentProps", "action", "process", "env", "VUE_APP_BASE_API", "placeholder", "limit", "filterable", "autoUpload", "fileList", "onRemove", "file", "delFileList", "onChange", "changeFileList", "onExceed", "postscript", "type", "autosize", "minRows", "maxRows", "clearable"], "sources": ["E:/1_Project/XYD_Project/dop-4.0/dop-4.1-qianduan/数字运营平台-统一门户工程/dopUnify/src/views/system/dataAuditing/component/table/info.js"], "sourcesContent": ["import { v1 as uuidv1 } from 'uuid'\r\nimport defaultSettings from '@/settings'\r\nconst prefix = defaultSettings.service.system // 前缀公共路由\r\n// 表头\r\nexport const configTable = (that) => [\r\n  {\r\n    name: 'inst_id',\r\n    label: '申请流水号',\r\n    width: 180,\r\n    id: uuidv1()\r\n  },\r\n  {\r\n    name: 'menu_name',\r\n    label: '参数配置页面',\r\n    width: 130,\r\n    id: uuidv1()\r\n  },\r\n  {\r\n    name: 'oper_type_flag',\r\n    label: '申请操作',\r\n    width: 120,\r\n    id: uuidv1()\r\n  },\r\n  {\r\n    name: 'apply_user',\r\n    label: '申请用户',\r\n    width: 90,\r\n    id: uuidv1()\r\n  },\r\n  {\r\n    name: 'apply_time',\r\n    label: '申请时间',\r\n    width: 170,\r\n    id: uuidv1()\r\n  },\r\n  {\r\n    name: 'approve_agree',\r\n    label: '审批结果',\r\n    width: 110,\r\n    id: uuidv1()\r\n  },\r\n  {\r\n    name: 'current_approve_organ',\r\n    label: '待审批机构',\r\n    width: 160,\r\n    id: uuidv1()\r\n  },\r\n  {\r\n    name: 'role_no',\r\n    label: '待审批角色',\r\n    id: uuidv1()\r\n  }\r\n]\r\n\r\n// 详情表头\r\nexport const configTableDetail = (that) => [\r\n  {\r\n    name: 'node_name',\r\n    label: '处理环节',\r\n    width: 80,\r\n    id: uuidv1()\r\n  },\r\n  {\r\n    name: 'organ_no',\r\n    label: '处理机构',\r\n    width: 180,\r\n    id: uuidv1()\r\n  },\r\n  {\r\n    name: 'role_no',\r\n    label: '处理角色',\r\n    id: uuidv1()\r\n  },\r\n  {\r\n    name: 'user_no',\r\n    label: '处理人',\r\n    width: 90,\r\n    id: uuidv1()\r\n  },\r\n  {\r\n    name: 'deal_state',\r\n    label: '处理状态',\r\n    width: 120,\r\n    id: uuidv1()\r\n  },\r\n  {\r\n    name: 'deal_result',\r\n    label: '处理结果',\r\n    width: 80,\r\n    id: uuidv1()\r\n  },\r\n  {\r\n    name: 'deal_time',\r\n    label: '处理时间',\r\n    width: 160,\r\n    id: uuidv1()\r\n  },\r\n  {\r\n    name: 'user_comment',\r\n    label: '处理意见',\r\n    width: 120,\r\n    id: uuidv1()\r\n  },\r\n  {\r\n    name: 'attachment',\r\n    label: '附件',\r\n    width: 80,\r\n    id: uuidv1()\r\n  }\r\n]\r\n\r\n// 详情查看附件表头\r\nexport const configTableFile = (that) => [\r\n  {\r\n    name: 'name',\r\n    label: '名称',\r\n    id: uuidv1()\r\n  },\r\n  {\r\n    name: 'show',\r\n    label: '预览',\r\n    width: 100,\r\n    id: uuidv1()\r\n  },\r\n  {\r\n    name: 'download',\r\n    label: '下载',\r\n    width: 100,\r\n    id: uuidv1()\r\n  }\r\n]\r\n\r\n// 操作详情同意 -添加附件 表单\r\nexport const config = (that) => ({\r\n  file_url: {\r\n    component: 'upload',\r\n    label: '上传附件-可多选',\r\n    colSpan: 24,\r\n    name: 'file_url',\r\n    config: {},\r\n    componentProps: {\r\n      action:\r\n        process.env.VUE_APP_BASE_API +\r\n        prefix +\r\n        '/fileController/fileUpload.do?filePath=taskFilePath',\r\n      placeholder: '上传所有类型的文件',\r\n      limit: 3, // 文件上传个数\r\n      filterable: true,\r\n      autoUpload: false, // 是否在选择文件之后立即上传\r\n      fileList: [],\r\n      // 移除列表中的文件时，触发的钩子\r\n      onRemove: (file, fileList) => {\r\n        that.delFileList(fileList)\r\n      },\r\n      // 文件列表状态改变时，触发的钩子\r\n      onChange: (file, fileList) => {\r\n        that.changeFileList(fileList)\r\n      },\r\n      // 文件个数超出3个时，触发的钩子\r\n      onExceed: (file, fileList) => {\r\n        that.onExceed(fileList)\r\n      }\r\n    }\r\n  },\r\n  postscript: {\r\n    component: 'input',\r\n    label: '附言',\r\n    colSpan: 24,\r\n    name: 'postscript',\r\n    config: {\r\n      // form-item 配置\r\n    },\r\n    componentProps: {\r\n      // input组件配置\r\n      type: 'textarea',\r\n      autosize: { minRows: 4, maxRows: 6 },\r\n      placeholder: '请输入不大于800字...',\r\n      clearable: true\r\n    }\r\n  }\r\n})\r\n"], "mappings": "AAAA,SAASA,EAAE,IAAIC,MAAM,QAAQ,MAAM;AACnC,OAAOC,eAAe,MAAM,YAAY;AACxC,IAAMC,MAAM,GAAGD,eAAe,CAACE,OAAO,CAACC,MAAM,EAAC;AAC9C;AACA,OAAO,IAAMC,WAAW,GAAG,SAAdA,WAAW,CAAIC,IAAI;EAAA,OAAK,CACnC;IACEC,IAAI,EAAE,SAAS;IACfC,KAAK,EAAE,OAAO;IACdC,KAAK,EAAE,GAAG;IACVC,EAAE,EAAEV,MAAM;EACZ,CAAC,EACD;IACEO,IAAI,EAAE,WAAW;IACjBC,KAAK,EAAE,QAAQ;IACfC,KAAK,EAAE,GAAG;IACVC,EAAE,EAAEV,MAAM;EACZ,CAAC,EACD;IACEO,IAAI,EAAE,gBAAgB;IACtBC,KAAK,EAAE,MAAM;IACbC,KAAK,EAAE,GAAG;IACVC,EAAE,EAAEV,MAAM;EACZ,CAAC,EACD;IACEO,IAAI,EAAE,YAAY;IAClBC,KAAK,EAAE,MAAM;IACbC,KAAK,EAAE,EAAE;IACTC,EAAE,EAAEV,MAAM;EACZ,CAAC,EACD;IACEO,IAAI,EAAE,YAAY;IAClBC,KAAK,EAAE,MAAM;IACbC,KAAK,EAAE,GAAG;IACVC,EAAE,EAAEV,MAAM;EACZ,CAAC,EACD;IACEO,IAAI,EAAE,eAAe;IACrBC,KAAK,EAAE,MAAM;IACbC,KAAK,EAAE,GAAG;IACVC,EAAE,EAAEV,MAAM;EACZ,CAAC,EACD;IACEO,IAAI,EAAE,uBAAuB;IAC7BC,KAAK,EAAE,OAAO;IACdC,KAAK,EAAE,GAAG;IACVC,EAAE,EAAEV,MAAM;EACZ,CAAC,EACD;IACEO,IAAI,EAAE,SAAS;IACfC,KAAK,EAAE,OAAO;IACdE,EAAE,EAAEV,MAAM;EACZ,CAAC,CACF;AAAA;;AAED;AACA,OAAO,IAAMW,iBAAiB,GAAG,SAApBA,iBAAiB,CAAIL,IAAI;EAAA,OAAK,CACzC;IACEC,IAAI,EAAE,WAAW;IACjBC,KAAK,EAAE,MAAM;IACbC,KAAK,EAAE,EAAE;IACTC,EAAE,EAAEV,MAAM;EACZ,CAAC,EACD;IACEO,IAAI,EAAE,UAAU;IAChBC,KAAK,EAAE,MAAM;IACbC,KAAK,EAAE,GAAG;IACVC,EAAE,EAAEV,MAAM;EACZ,CAAC,EACD;IACEO,IAAI,EAAE,SAAS;IACfC,KAAK,EAAE,MAAM;IACbE,EAAE,EAAEV,MAAM;EACZ,CAAC,EACD;IACEO,IAAI,EAAE,SAAS;IACfC,KAAK,EAAE,KAAK;IACZC,KAAK,EAAE,EAAE;IACTC,EAAE,EAAEV,MAAM;EACZ,CAAC,EACD;IACEO,IAAI,EAAE,YAAY;IAClBC,KAAK,EAAE,MAAM;IACbC,KAAK,EAAE,GAAG;IACVC,EAAE,EAAEV,MAAM;EACZ,CAAC,EACD;IACEO,IAAI,EAAE,aAAa;IACnBC,KAAK,EAAE,MAAM;IACbC,KAAK,EAAE,EAAE;IACTC,EAAE,EAAEV,MAAM;EACZ,CAAC,EACD;IACEO,IAAI,EAAE,WAAW;IACjBC,KAAK,EAAE,MAAM;IACbC,KAAK,EAAE,GAAG;IACVC,EAAE,EAAEV,MAAM;EACZ,CAAC,EACD;IACEO,IAAI,EAAE,cAAc;IACpBC,KAAK,EAAE,MAAM;IACbC,KAAK,EAAE,GAAG;IACVC,EAAE,EAAEV,MAAM;EACZ,CAAC,EACD;IACEO,IAAI,EAAE,YAAY;IAClBC,KAAK,EAAE,IAAI;IACXC,KAAK,EAAE,EAAE;IACTC,EAAE,EAAEV,MAAM;EACZ,CAAC,CACF;AAAA;;AAED;AACA,OAAO,IAAMY,eAAe,GAAG,SAAlBA,eAAe,CAAIN,IAAI;EAAA,OAAK,CACvC;IACEC,IAAI,EAAE,MAAM;IACZC,KAAK,EAAE,IAAI;IACXE,EAAE,EAAEV,MAAM;EACZ,CAAC,EACD;IACEO,IAAI,EAAE,MAAM;IACZC,KAAK,EAAE,IAAI;IACXC,KAAK,EAAE,GAAG;IACVC,EAAE,EAAEV,MAAM;EACZ,CAAC,EACD;IACEO,IAAI,EAAE,UAAU;IAChBC,KAAK,EAAE,IAAI;IACXC,KAAK,EAAE,GAAG;IACVC,EAAE,EAAEV,MAAM;EACZ,CAAC,CACF;AAAA;;AAED;AACA,OAAO,IAAMa,MAAM,GAAG,SAATA,MAAM,CAAIP,IAAI;EAAA,OAAM;IAC/BQ,QAAQ,EAAE;MACRC,SAAS,EAAE,QAAQ;MACnBP,KAAK,EAAE,UAAU;MACjBQ,OAAO,EAAE,EAAE;MACXT,IAAI,EAAE,UAAU;MAChBM,MAAM,EAAE,CAAC,CAAC;MACVI,cAAc,EAAE;QACdC,MAAM,EACJC,OAAO,CAACC,GAAG,CAACC,gBAAgB,GAC5BnB,MAAM,GACN,qDAAqD;QACvDoB,WAAW,EAAE,WAAW;QACxBC,KAAK,EAAE,CAAC;QAAE;QACVC,UAAU,EAAE,IAAI;QAChBC,UAAU,EAAE,KAAK;QAAE;QACnBC,QAAQ,EAAE,EAAE;QACZ;QACAC,QAAQ,EAAE,kBAACC,IAAI,EAAEF,QAAQ,EAAK;UAC5BpB,IAAI,CAACuB,WAAW,CAACH,QAAQ,CAAC;QAC5B,CAAC;QACD;QACAI,QAAQ,EAAE,kBAACF,IAAI,EAAEF,QAAQ,EAAK;UAC5BpB,IAAI,CAACyB,cAAc,CAACL,QAAQ,CAAC;QAC/B,CAAC;QACD;QACAM,QAAQ,EAAE,kBAACJ,IAAI,EAAEF,QAAQ,EAAK;UAC5BpB,IAAI,CAAC0B,QAAQ,CAACN,QAAQ,CAAC;QACzB;MACF;IACF,CAAC;IACDO,UAAU,EAAE;MACVlB,SAAS,EAAE,OAAO;MAClBP,KAAK,EAAE,IAAI;MACXQ,OAAO,EAAE,EAAE;MACXT,IAAI,EAAE,YAAY;MAClBM,MAAM,EAAE;QACN;MAAA,CACD;MACDI,cAAc,EAAE;QACd;QACAiB,IAAI,EAAE,UAAU;QAChBC,QAAQ,EAAE;UAAEC,OAAO,EAAE,CAAC;UAAEC,OAAO,EAAE;QAAE,CAAC;QACpCf,WAAW,EAAE,eAAe;QAC5BgB,SAAS,EAAE;MACb;IACF;EACF,CAAC;AAAA,CAAC"}]}