{"remainingRequest": "D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\node_modules\\babel-loader\\lib\\index.js!D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\node_modules\\eslint-loader\\index.js??ref--13-0!D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\src\\views\\system\\externalManage\\role\\info.js", "dependencies": [{"path": "D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\src\\views\\system\\externalManage\\role\\info.js", "mtime": 1686019809029}, {"path": "D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\babel.config.js", "mtime": 1686019818359}, {"path": "D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\node_modules\\eslint-loader\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8gaW1wb3J0IHsgZGljdGlvbmFyeUZpZWRzIH0gZnJvbSAnQC91dGlscy9kaWN0aW9uYXJ5JyAvLyDlrZflhbgKLy8g6KGo5Y2VCmV4cG9ydCB2YXIgY29uZmlnID0gZnVuY3Rpb24gY29uZmlnKHRoYXQpIHsKICByZXR1cm4gewogICAgZXh0ZXJuYWxfc3lzdGVtX25vOiB7CiAgICAgIGNvbXBvbmVudDogJ3NlbGVjdCcsCiAgICAgIGxhYmVsOiAn57O757uf57yW5Y+3JywKICAgICAgY29sU3BhbjogOCwKICAgICAgbmFtZTogJ2V4dGVybmFsX3N5c3RlbV9ubycsCiAgICAgIGNvbmZpZzogewogICAgICAgIC8vIGZvcm0taXRlbSDphY3nva4KICAgICAgICBydWxlczogW3sKICAgICAgICAgIG1pbjogMCwKICAgICAgICAgIG1heDogMjAwLAogICAgICAgICAgbWVzc2FnZTogJ+ivt+acgOWkmuWhq+WGmTIwMOS4quWtl+espicKICAgICAgICB9XQogICAgICB9LAogICAgICBjb21wb25lbnRQcm9wczogewogICAgICAgIC8vIGlucHV057uE5Lu26YWN572uCiAgICAgICAgcGxhY2Vob2xkZXI6ICfor7fpgInmi6knLAogICAgICAgIGNsZWFyYWJsZTogdHJ1ZQogICAgICB9LAogICAgICBvcHRpb25zOiBbXQogICAgfSwKICAgIHJvbGVfbmFtZTogewogICAgICBjb21wb25lbnQ6ICdpbnB1dCcsCiAgICAgIGxhYmVsOiAn6KeS6Imy5ZCNJywKICAgICAgY29sU3BhbjogOCwKICAgICAgbmFtZTogJ3JvbGVfbmFtZScsCiAgICAgIGNvbmZpZzogewogICAgICAgIHJ1bGVzOiBbewogICAgICAgICAgbWluOiAwLAogICAgICAgICAgbWF4OiAxNTAsCiAgICAgICAgICBtZXNzYWdlOiAn6K+35pyA5aSa5aGr5YaZMTUw5Liq5a2X56ymJwogICAgICAgIH1dCiAgICAgIH0sCiAgICAgIGNvbXBvbmVudFByb3BzOiB7CiAgICAgICAgcGxhY2Vob2xkZXI6ICfmlK/mjIHmjInop5LoibLlkI3mqKHns4rmn6Xor6InLAogICAgICAgIGZpbHRlcmFibGU6IHRydWUsCiAgICAgICAgY2xlYXJhYmxlOiB0cnVlCiAgICAgIH0KICAgIH0KICB9Owp9Ow=="}, {"version": 3, "names": ["config", "that", "external_system_no", "component", "label", "colSpan", "name", "rules", "min", "max", "message", "componentProps", "placeholder", "clearable", "options", "role_name", "filterable"], "sources": ["D:/1_Project/XYD_Project/dop-4.0/dop-4.1-qianduan-unify/dopUnify/src/views/system/externalManage/role/info.js"], "sourcesContent": ["// import { dictionaryFieds } from '@/utils/dictionary' // 字典\r\n// 表单\r\nexport const config = (that) => ({\r\n  external_system_no: {\r\n    component: 'select',\r\n    label: '系统编号',\r\n    colSpan: 8,\r\n    name: 'external_system_no',\r\n    config: {\r\n      // form-item 配置\r\n      rules: [\r\n        { min: 0, max: 200, message: '请最多填写200个字符' }\r\n      ]\r\n    },\r\n    componentProps: {\r\n      // input组件配置\r\n      placeholder: '请选择',\r\n      clearable: true\r\n    },\r\n    options: []\r\n  },\r\n  role_name: {\r\n    component: 'input',\r\n    label: '角色名',\r\n    colSpan: 8,\r\n    name: 'role_name',\r\n    config: {\r\n      rules: [\r\n        { min: 0, max: 150, message: '请最多填写150个字符' }\r\n      ]\r\n    },\r\n    componentProps: {\r\n      placeholder: '支持按角色名模糊查询',\r\n      filterable: true,\r\n      clearable: true\r\n    }\r\n  }\r\n})\r\n"], "mappings": "AAAA;AACA;AACA,OAAO,IAAMA,MAAM,GAAG,SAATA,MAAM,CAAIC,IAAI;EAAA,OAAM;IAC/BC,kBAAkB,EAAE;MAClBC,SAAS,EAAE,QAAQ;MACnBC,KAAK,EAAE,MAAM;MACbC,OAAO,EAAE,CAAC;MACVC,IAAI,EAAE,oBAAoB;MAC1BN,MAAM,EAAE;QACN;QACAO,KAAK,EAAE,CACL;UAAEC,GAAG,EAAE,CAAC;UAAEC,GAAG,EAAE,GAAG;UAAEC,OAAO,EAAE;QAAc,CAAC;MAEhD,CAAC;MACDC,cAAc,EAAE;QACd;QACAC,WAAW,EAAE,KAAK;QAClBC,SAAS,EAAE;MACb,CAAC;MACDC,OAAO,EAAE;IACX,CAAC;IACDC,SAAS,EAAE;MACTZ,SAAS,EAAE,OAAO;MAClBC,KAAK,EAAE,KAAK;MACZC,OAAO,EAAE,CAAC;MACVC,IAAI,EAAE,WAAW;MACjBN,MAAM,EAAE;QACNO,KAAK,EAAE,CACL;UAAEC,GAAG,EAAE,CAAC;UAAEC,GAAG,EAAE,GAAG;UAAEC,OAAO,EAAE;QAAc,CAAC;MAEhD,CAAC;MACDC,cAAc,EAAE;QACdC,WAAW,EAAE,YAAY;QACzBI,UAAU,EAAE,IAAI;QAChBH,SAAS,EAAE;MACb;IACF;EACF,CAAC;AAAA,CAAC"}]}