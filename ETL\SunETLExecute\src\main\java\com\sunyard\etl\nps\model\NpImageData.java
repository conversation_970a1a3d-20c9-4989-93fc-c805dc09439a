package com.sunyard.etl.nps.model;

//无纸化图像数据表
public class NpImageData {
	private String busiDataNo;// 业务序号
	private String orderNum; // 批内码
	private String formName; // 版面名称
	private String psLevel; // 主附件
	private String fileName;
	private String backFileName;
	
	private int imageSize;
	private int backImageSize;
	
	
	// 非表字段
	private String URL;
	private String occurDate;
	private String siteNo;
	private String operator;
	private String flowId;
	private String contentId;
	private String indexValue;
	



	public String getFormName() {
		return formName;
	}

	public void setFormName(String formName) {
		this.formName = formName;
	}

	public String getFileName() {
		return fileName;
	}

	public void setFileName(String fileName) {
		this.fileName = fileName;
	}

	public String getBackFileName() {
		return backFileName;
	}

	public void setBackFileName(String backFileName) {
		this.backFileName = backFileName;
	}

	public int getImageSize() {
		return imageSize;
	}

	public void setImageSize(int imageSize) {
		this.imageSize = imageSize;
	}

	public int getBackImageSize() {
		return backImageSize;
	}

	public void setBackImageSize(int backImageSize) {
		this.backImageSize = backImageSize;
	}

	public String getOrderNum() {
		return orderNum;
	}

	public void setOrderNum(String orderNum) {
		this.orderNum = orderNum;
	}

	public String getURL() {
		return URL;
	}

	public void setURL(String uRL) {
		URL = uRL;
	}

	public String getOccurDate() {
		return occurDate;
	}

	public void setOccurDate(String occurDate) {
		this.occurDate = occurDate;
	}

	public String getSiteNo() {
		return siteNo;
	}

	public void setSiteNo(String siteNo) {
		this.siteNo = siteNo;
	}

	public String getOperator() {
		return operator;
	}

	public void setOperator(String operator) {
		this.operator = operator;
	}

	public String getBusiDataNo() {
		return busiDataNo;
	}

	public void setBusiDataNo(String busiDataNo) {
		this.busiDataNo = busiDataNo;
	}

	public String getFlowId() {
		return flowId;
	}

	public void setFlowId(String flowId) {
		this.flowId = flowId;
	}

	public String getPsLevel() {
		return psLevel;
	}

	public void setPsLevel(String psLevel) {
		this.psLevel = psLevel;
	}

	public String getContentId() {
		return contentId;
	}

	public void setContentId(String contentId) {
		this.contentId = contentId;
	}

	public String getIndexValue() {
		return indexValue;
	}

	public void setIndexValue(String indexValue) {
		this.indexValue = indexValue;
	}


}
