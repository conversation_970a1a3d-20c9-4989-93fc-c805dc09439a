package com.sunyard.etl.nps.model;

import com.sunyard.etl.nps.common.NPSContants;




public class BpTmpbatchTb {

	// Fields
    // 共有字段
	private String batchId;
	private String batchLock; // default 0 
	private String batchTotalPage; 
	private int businessId; 
	private String batchCommit = "1";// default 0   set 1
	private String inputDate;
	private String tempDataTable = "BP_TMPDATA_1_TB";
	private String inputWorker = "NPS";
	private String machineId = NPSContants.IP_ADDRESS;
	private String needProcess = "1";
	private String occurDate;
	private String operatorNo;
	private String progressFlag;
	private String siteNo;
	private String inputTime;
	private String imageStatus = "1";
	private String isInvalid = "1";

	
	//4.x特有字段
	private String sessionTime;
	private Integer businessType;
	private String mark;
	private String registerTotalPage;
	private String isWaitinsert;
	private String nopaperFlag;
	private String fsMachineIp;
	private String fsMachinePort;
	private String largeFileName;
	
	
	
	//5.x特有字段
	private String priorityLevel;
	private String dataSource;
	
	
	public BpTmpbatchTb(){
		
	}
	
	public BpTmpbatchTb(String batchId, String inputDate,String occurDate,String siteNo,String operatorNo)
	{
		this.batchId = batchId;
		this.inputDate = inputDate;
		this.occurDate = occurDate;
		this.siteNo = siteNo;
		this.operatorNo = operatorNo;
	}
	
	public BpTmpbatchTb(String occurDate,String siteNo,String operatorNo)
	{
		this.occurDate = occurDate;
		this.siteNo = siteNo;
		this.operatorNo = operatorNo;
	}

	public BpTmpbatchTb(String batchId, String occurDate, String siteNo,
			String operatorNo, String batchTotalPage, String fsMachineIp,
			String fsMachinePort, String largeFileName) {
		this.batchId = batchId;
		this.occurDate = occurDate;
		this.siteNo = siteNo;
		this.operatorNo = operatorNo;
		this.batchTotalPage = batchTotalPage;
		this.setFsMachineIp(fsMachineIp);
		this.setFsMachinePort(fsMachineIp);
		this.setLargeFileName(largeFileName);
	}
	
	
	
	/**
	 * @return the batchId
	 */
	public String getBatchId() {
		return batchId;
	}
	/**
	 * @param batchId the batchId to set
	 */
	public void setBatchId(String batchId) {
		this.batchId = batchId;
	}
	/**
	 * @return the batchLock
	 */
	public String getBatchLock() {
		return batchLock;
	}
	/**
	 * @param batchLock the batchLock to set
	 */
	public void setBatchLock(String batchLock) {
		this.batchLock = batchLock;
	}
	/**
	 * @return the batchTotalPage
	 */
	public String getBatchTotalPage() {
		return batchTotalPage;
	}
	/**
	 * @param batchTotalPage the batchTotalPage to set
	 */
	public void setBatchTotalPage(String batchTotalPage) {
		this.batchTotalPage = batchTotalPage;
	}
	/**
	 * @return the businessId
	 */
	public int getBusinessId() {
		return businessId;
	}
	/**
	 * @param businessId the businessId to set
	 */
	public void setBusinessId(int businessId) {
		this.businessId = businessId;
	}
	/**
	 * @return the batchCommit
	 */
	public String getBatchCommit() {
		return batchCommit;
	}
	/**
	 * @param batchCommit the batchCommit to set
	 */
	public void setBatchCommit(String batchCommit) {
		this.batchCommit = batchCommit;
	}
	/**
	 * @return the inputDate
	 */
	public String getInputDate() {
		return inputDate;
	}
	/**
	 * @param inputDate the inputDate to set
	 */
	public void setInputDate(String inputDate) {
		this.inputDate = inputDate;
	}
	/**
	 * @return the tempDataTable
	 */
	public String getTempDataTable() {
		return tempDataTable;
	}
	/**
	 * @param tempDataTable the tempDataTable to set
	 */
	public void setTempDataTable(String tempDataTable) {
		this.tempDataTable = tempDataTable;
	}
	/**
	 * @return the inputWorker
	 */
	public String getInputWorker() {
		return inputWorker;
	}
	/**
	 * @param inputWorker the inputWorker to set
	 */
	public void setInputWorker(String inputWorker) {
		this.inputWorker = inputWorker;
	}
	/**
	 * @return the machineId
	 */
	public String getMachineId() {
		return machineId;
	}
	/**
	 * @param machineId the machineId to set
	 */
	public void setMachineId(String machineId) {
		this.machineId = machineId;
	}
	/**
	 * @return the needProcess
	 */
	public String getNeedProcess() {
		return needProcess;
	}
	/**
	 * @param needProcess the needProcess to set
	 */
	public void setNeedProcess(String needProcess) {
		this.needProcess = needProcess;
	}
	/**
	 * @return the occurDate
	 */
	public String getOccurDate() {
		return occurDate;
	}
	/**
	 * @param occurDate the occurDate to set
	 */
	public void setOccurDate(String occurDate) {
		this.occurDate = occurDate;
	}
	/**
	 * @return the operatorNo
	 */
	public String getOperatorNo() {
		return operatorNo;
	}
	/**
	 * @param operatorNo the operatorNo to set
	 */
	public void setOperatorNo(String operatorNo) {
		this.operatorNo = operatorNo;
	}
	/**
	 * @return the progressFlag
	 */
	public String getProgressFlag() {
		return progressFlag;
	}
	/**
	 * @param progressFlag the progressFlag to set
	 */
	public void setProgressFlag(String progressFlag) {
		this.progressFlag = progressFlag;
	}
	/**
	 * @return the siteNo
	 */
	public String getSiteNo() {
		return siteNo;
	}
	/**
	 * @param siteNo the siteNo to set
	 */
	public void setSiteNo(String siteNo) {
		this.siteNo = siteNo;
	}
	/**
	 * @return the inputTime
	 */
	public String getInputTime() {
		return inputTime;
	}
	/**
	 * @param inputTime the inputTime to set
	 */
	public void setInputTime(String inputTime) {
		this.inputTime = inputTime;
	}
	/**
	 * @return the imageStatus
	 */
	public String getImageStatus() {
		return imageStatus;
	}
	/**
	 * @param imageStatus the imageStatus to set
	 */
	public void setImageStatus(String imageStatus) {
		this.imageStatus = imageStatus;
	}
	/**
	 * @return the isInvalid
	 */
	public String getIsInvalid() {
		return isInvalid;
	}
	/**
	 * @param isInvalid the isInvalid to set
	 */
	public void setIsInvalid(String isInvalid) {
		this.isInvalid = isInvalid;
	}
	/**
	 * @return the sessionTime
	 */
	public String getSessionTime() {
		return sessionTime;
	}
	/**
	 * @param sessionTime the sessionTime to set
	 */
	public void setSessionTime(String sessionTime) {
		this.sessionTime = sessionTime;
	}
	/**
	 * @return the businessType
	 */
	public Integer getBusinessType() {
		return businessType;
	}
	/**
	 * @param businessType the businessType to set
	 */
	public void setBusinessType(Integer businessType) {
		this.businessType = businessType;
	}
	/**
	 * @return the mark
	 */
	public String getMark() {
		return mark;
	}
	/**
	 * @param mark the mark to set
	 */
	public void setMark(String mark) {
		this.mark = mark;
	}
	/**
	 * @return the registerTotalPage
	 */
	public String getRegisterTotalPage() {
		return registerTotalPage;
	}
	/**
	 * @param registerTotalPage the registerTotalPage to set
	 */
	public void setRegisterTotalPage(String registerTotalPage) {
		this.registerTotalPage = registerTotalPage;
	}

	public String getIsWaitinsert() {
		return isWaitinsert;
	}

	public void setIsWaitinsert(String isWaitinsert) {
		this.isWaitinsert = isWaitinsert;
	}

	public String getLargeFileName() {
		return largeFileName;
	}

	public void setLargeFileName(String largeFileName) {
		this.largeFileName = largeFileName;
	}

	public String getFsMachinePort() {
		return fsMachinePort;
	}

	public void setFsMachinePort(String fsMachinePort) {
		this.fsMachinePort = fsMachinePort;
	}

	public String getFsMachineIp() {
		return fsMachineIp;
	}

	public void setFsMachineIp(String fsMachineIp) {
		this.fsMachineIp = fsMachineIp;
	}

	public String getNopaperFlag() {
		return nopaperFlag;
	}

	public void setNopaperFlag(String nopaperFlag) {
		this.nopaperFlag = nopaperFlag;
	}

	public String getPriorityLevel() {
		return priorityLevel;
	}

	public void setPriorityLevel(String priorityLevel) {
		this.priorityLevel = priorityLevel;
	}

	public String getDataSource() {
		return dataSource;
	}

	public void setDataSource(String dataSource) {
		this.dataSource = dataSource;
	}
	
		
	
}