{"remainingRequest": "E:\\华南区项目\\中国银行\\开发区\\科技公司运营平台\\前端\\dopUnify\\node_modules\\babel-loader\\lib\\index.js!E:\\华南区项目\\中国银行\\开发区\\科技公司运营平台\\前端\\dopUnify\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!E:\\华南区项目\\中国银行\\开发区\\科技公司运营平台\\前端\\dopUnify\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\华南区项目\\中国银行\\开发区\\科技公司运营平台\\前端\\dopUnify\\src\\views\\home\\component\\externalMenu\\index.vue?vue&type=script&lang=js&", "dependencies": [{"path": "E:\\华南区项目\\中国银行\\开发区\\科技公司运营平台\\前端\\dopUnify\\src\\views\\home\\component\\externalMenu\\index.vue", "mtime": 1721631954662}, {"path": "E:\\华南区项目\\中国银行\\开发区\\科技公司运营平台\\前端\\dopUnify\\babel.config.js", "mtime": 1721631954662}, {"path": "E:\\华南区项目\\中国银行\\开发区\\科技公司运营平台\\前端\\dopUnify\\.babelrc", "mtime": 1721631954662}, {"path": "E:\\华南区项目\\中国银行\\开发区\\科技公司运营平台\\前端\\dopUnify\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "E:\\华南区项目\\中国银行\\开发区\\科技公司运营平台\\前端\\dopUnify\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "E:\\华南区项目\\中国银行\\开发区\\科技公司运营平台\\前端\\dopUnify\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "E:\\华南区项目\\中国银行\\开发区\\科技公司运营平台\\前端\\dopUnify\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1655715099000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAuBA;AACA;AACA;AACA;AACA;AACA,IACAA,kBACAC,OADAD;AAEA;AACA;EACAE;EACAC;IACAC;MACAC;MACAC;IACA;IACAC;MACAF;MACAC;IACA;IACAE;MACAH;MACAC;IACA;EACA;EACAG;IACA;MACAC;MACAC;MACAC;MACAC;MAAA;MACAC;MACAC;IACA;EACA;EACAC;IACAJ;MACA;QACA;MACA;IACA;EACA;EACAK;IACAC;EACA;EACAC;EACAC;IAAA;IACA;IACA;MAAA;MAAA;QAAA;UAAA;YAAA;cACA;cACA;cACA;cACA;cACA;cACA;cACA;cACA;cACA;cAAA,wBAEA;gBACA;cACA,IAHAC;cAIA,uEACA;gBACA;cACA,EACA;cACA;cACA;cACA;cAAA,IACAA;gBAAA;gBAAA;cAAA;cACAC;cAAA,KACAC;gBAAA;gBAAA;cAAA;cAAA;cAAA,OACA;YAAA;cAAA;cAAA;YAAA;cAEA;gBACAC;gBACAC;gBACAC,uBACAC;cAEA;YAAA;cAGA;cACA;gBACA;gBACAC,sBACA;gBACA;kBACAC;kBACA;oBACA;oBACAC,0DACA;sBACA;oBACA,EACA;oBACA;sBACA;sBACA;sBACA;sBACA;oBACA;sBACA;sBACA;oBACA;kBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;cACA;gBACA;cACA;cACA;cACA;cACA;YAAA;YAAA;cAAA;UAAA;QAAA;MAAA;IAAA,CACA;EACA;EACAC;IACA;AACA;AACA;IACAC;MAAA;MACA;QACA;UACAC;UACAC;UACAC;UAAA;UACAX;QACA;QACAxB;UACA;UACA2B;UACAA,qBACAH,yBACAY,+EACA;UACA;YACAZ;YACAC;YACAC;UACA;UACAW;QACA;MACA;IACA;IACAC;MAAA;MACAC;QACA;QACA;QACA;QACA;QACA;UACA,6BACAH,yDACA;UACA;UACAI,mDACAC,uBACAA,8BACA;UACA;UACA;UACA;YACAC;YACAC,yCACA,IACAA,8CACAD,iCACA;UACA;YACAA;YACAC,yCACA,IACAA,8CAEAD,iCAEA;YACAC;cACAC;YACA;UACA;UAEA;YACA;cACA,IACAC,oBACAA,yBACAA,sBACA;gBACA;cACA;YACA;UACA;YACA;cACA,IACAA,8BACAA,0BACAA,uBACA;gBACA;cACA;YACA;UACA;QACA;QACA;QACA;UACA;YACAC;YACAC;YACA1C;YACAQ;UACA;UACA;UACAmC;UACA;UACAZ,iCACAa,sEACA;QACA;MACA;IACA;IACA;AACA;AACA;IACAC;MAAA;MACAC;QACA;QACA;QACA;UACA;UACA;YACA;YACA;YACA;UACA;UACA;UACA;QACA;MACA;IACA;EACA;AACA", "names": ["fieldController", "Common", "name", "props", "path", "type", "default", "title", "id", "data", "iframeUrl", "extendRef", "is<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "projectName", "systemNum", "routeMessage", "watch", "<PERSON><PERSON><PERSON><PERSON>", "clearTimeout", "activated", "mounted", "system_no", "systemDic", "commonBlank", "systemNo", "value", "dictionary", "localStorage", "deplName", "store", "depl<PERSON><PERSON><PERSON><PERSON>", "methods", "getDictionaryData", "parameterList", "fieldTime", "operType", "JSON", "resolve", "postMess", "timeout1", "dictionaryPUnfiy", "dictionaryP", "childSystemNo", "commonData", "dictionaryTree", "item", "user", "common", "iframeWin", "process", "postParam", "window"], "sourceRoot": "src/views/home/<USER>/externalMenu", "sources": ["index.vue"], "sourcesContent": ["<!--\r\n* 首页模块 配置外菜单入口\r\n-->\r\n<template>\r\n  <div class=\"homeContent\">\r\n    <div\r\n      class=\"homePageTitle\"\r\n      style=\"width: 100%; height: 2rem; line-height: 2rem\"\r\n    >\r\n      {{ title }}\r\n    </div>\r\n    <div ref=\"ccc\" class=\"content\">\r\n      <iframe\r\n        :ref=\"extendRef\"\r\n        class=\"extendIframe\"\r\n        :src=\"iframeUrl\"\r\n        frameborder=\"0\"\r\n      />\r\n    </div>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\n// import store from '@/store'\r\nimport { commonBlank } from '@/utils/common'\r\nimport { Common } from '@/api'\r\nimport { dictionaryFieds } from '@/utils/dictionary'\r\nimport store from '@/store'\r\nconst {\r\n  fieldController // 同步字典\r\n} = Common\r\nlet timeout1 // 延时配置\r\nexport default {\r\n  name: 'ExtendMenu',\r\n  props: {\r\n    path: {\r\n      type: String,\r\n      default: ''\r\n    },\r\n    title: {\r\n      type: String,\r\n      default: ''\r\n    },\r\n    id: {\r\n      type: String,\r\n      default: ''\r\n    }\r\n  },\r\n  data() {\r\n    return {\r\n      iframeUrl: '',\r\n      extendRef: 'extendRef' + new Date().getTime(),\r\n      isChildrenReady: false,\r\n      projectName: '', // 子项目名称\r\n      systemNum: '',\r\n      routeMessage: {}\r\n    }\r\n  },\r\n  watch: {\r\n    isChildrenReady(val) {\r\n      if (val) {\r\n        this.postMess()\r\n      }\r\n    }\r\n  },\r\n  beforeDestroy() {\r\n    clearTimeout(timeout1)\r\n  },\r\n  activated() {},\r\n  mounted() {\r\n    // this.postMess()systemNum\r\n    this.$nextTick().then(async() => {\r\n      // console.log(this.$route.matched)\r\n      // const pathIndex = this.$route.matched.length - 1\r\n      // const routeMessage = this.$route.matched[pathIndex].props.default\r\n      // const path = routeMessage.path\r\n      // http://172.1.11.51:9528/redirect/report/#/report/dataSet\r\n      // http://172.1.1.21/dopUnify/redirect/report/#/report/dataSet\r\n      // if (!commonBlank(this.path)) {\r\n      // store.commit('user/SET_ROUTE_M', routeMessage)\r\n      // 获取该菜单的字典号\r\n      const { system_no, depl_name, menu_attr } =\r\n        this.$store.state.permission.addRoutesArray.find((item) => {\r\n          return item.menu_id === this.id\r\n        })\r\n      this.routeMessage = this.$store.state.permission.addRoutesArray.find(\r\n        (item) => {\r\n          return item.menu_id === this.id\r\n        }\r\n      )\r\n      this.routeMessage.path = this.routeMessage.menu_url\r\n      this.systemNum = system_no // 系统号\r\n      // 获取外系统菜单 数据字典\r\n      if (!(system_no in this.$store.state.common.dictionaryLet)) {\r\n        const systemDic = JSON.parse(localStorage.getItem(this.systemNum))\r\n        if (commonBlank(systemDic)) {\r\n          await this.getDictionaryData(this.systemNum)\r\n        } else {\r\n          this.$store.commit('common/ADD_DICTIONARYLET', {\r\n            systemNo: this.systemNum,\r\n            value: systemDic,\r\n            dictionary: JSON.parse(\r\n              localStorage.getItem(this.systemNum + 'dictionary')\r\n            )\r\n          })\r\n        }\r\n      }\r\n      // const pathUrl = this.path.split(':') // redirect:/report/report1\r\n      if (menu_attr === '2') {\r\n        // 外系统菜单\r\n        const deplName = depl_name\r\n        // this.$route.matched.slice(-1)[0].props.default.menu_id\r\n        if (!commonBlank(this.path)) {\r\n          store.commit('user/SET_ROUTE_M', this.routeMessage)\r\n          if (!commonBlank(deplName)) {\r\n            // 有做代理\r\n            const deplNamelabel = dictionaryFieds('EXT_SYS_DEPL_NAME').find(\r\n              (item) => {\r\n                return item.value === deplName\r\n              }\r\n            ).label\r\n            if (this.path.indexOf('static/html') !== -1) {\r\n              // 兼容老基线sunaos\r\n              // http://172.1.1.101/dopUnify/redirect/unify/iframe.html\r\n              this.iframeUrl = `${window.location.origin}${window.location.pathname}redirect/${deplNamelabel}/iframe.html`\r\n              // this.iframeUrl = `http://172.1.10.185/dopUnify/redirect/${deplNamelabel}/iframe.html`\r\n            } else {\r\n              this.iframeUrl = `${window.location.origin}${window.location.pathname}redirect/${deplNamelabel}/#/${this.path}`\r\n              // this.iframeUrl = `http://***********/dopUnify/redirect/${deplNamelabel}/#/${path}`\r\n            }\r\n          }\r\n        }\r\n        // this.iframeUrl = `${window.location.origin}${\r\n        //   window.location.pathname\r\n        // }redirect/${\r\n        //   pathUrl[1].substring(1).split('/')[0]\r\n        // }/#/${pathUrl[1].substring(1)}`\r\n      } else {\r\n        this.iframeUrl = this.path\r\n      }\r\n      // this.iframeUrl = 'http://localhost:5000/#/ism/schedulingResult/week'\r\n      // this.extendRef = this.extendRef + this.$route.path\r\n      this.postParam()\r\n    })\r\n  },\r\n  methods: {\r\n    /**\r\n     * 获取数据字典\r\n     */\r\n    getDictionaryData(systemNo) {\r\n      return new Promise((resolve) => {\r\n        const msg = {\r\n          parameterList: [''],\r\n          fieldTime: '',\r\n          operType: '1', // 门户操作标识\r\n          systemNo: systemNo\r\n        }\r\n        fieldController(msg).then((res) => {\r\n          // 新增外系统字典\r\n          localStorage.setItem(systemNo, JSON.stringify(res.retMap[systemNo]))\r\n          localStorage.setItem(\r\n            systemNo + 'dictionary',\r\n            JSON.stringify({ [systemNo]: res.retMap.dictionary[systemNo] })\r\n          )\r\n          this.$store.commit('common/ADD_DICTIONARYLET', {\r\n            systemNo,\r\n            value: res.retMap[systemNo],\r\n            dictionary: res.retMap.dictionary\r\n          })\r\n          resolve()\r\n        })\r\n      })\r\n    },\r\n    postMess() {\r\n      timeout1 = setTimeout(() => {\r\n        const mapFrame = this.$refs[this.extendRef]\r\n        const iframeWin = mapFrame.contentWindow\r\n        // 合并父子工程数据字典 begin\r\n        let commonData = JSON.parse(JSON.stringify(this.$store.state.common))\r\n        if (this.systemNum !== 'UNIFY') {\r\n          const dictionaryP = JSON.parse(\r\n            JSON.stringify(this.$store.state.common.dictionaryLet)\r\n          )\r\n          let dictionaryPUnfiy = {}\r\n          dictionaryPUnfiy = {\r\n            ...dictionaryP['UNIFY'],\r\n            ...dictionaryP[this.systemNum]\r\n          }\r\n          let childSystemNo = ''\r\n          // 单独为指标修改传值模式\r\n          if (this.projectName === 'indicator') {\r\n            childSystemNo = 'AOS'\r\n            commonData.dictionaryLet = Object.assign(\r\n              {},\r\n              commonData.dictionaryLet,\r\n              { [childSystemNo]: dictionaryPUnfiy }\r\n            )\r\n          } else {\r\n            childSystemNo = 'UNIFY'\r\n            commonData.dictionaryLet = Object.assign(\r\n              {},\r\n              commonData.dictionaryLet,\r\n              {\r\n                [childSystemNo]: dictionaryPUnfiy\r\n              }\r\n            )\r\n            commonData = Object.assign({}, commonData, {\r\n              dictionaryTree: commonData.dictionaryTree[this.systemNum]\r\n            })\r\n          }\r\n\r\n          if (childSystemNo === 'UNIFY') {\r\n            for (const item in commonData.dictionaryLet) {\r\n              if (\r\n                item !== 'UNIFY' &&\r\n                item !== 'dictionary' &&\r\n                item !== 'fieldTime'\r\n              ) {\r\n                delete commonData.dictionaryLet[item]\r\n              }\r\n            }\r\n          } else {\r\n            for (const item in commonData.dictionaryLet) {\r\n              if (\r\n                item !== this.systemNum &&\r\n                item !== 'dictionary' &&\r\n                item !== 'fieldTime'\r\n              ) {\r\n                delete commonData.dictionaryLet[item]\r\n              }\r\n            }\r\n          }\r\n        }\r\n        // 合并父子工程数据字典 end\r\n        if (iframeWin) {\r\n          const msg = {\r\n            user: this.$store.state.user,\r\n            common: commonData,\r\n            type: 'dopUnify',\r\n            projectName: this.projectName\r\n          }\r\n          // console.log('父传子的数据', this.$store.state.user)\r\n          iframeWin.postMessage(\r\n            // 向子工程发送\r\n            JSON.parse(JSON.stringify(msg)),\r\n            process.env.NODE_ENV === 'development' ? '*' : window.location.origin\r\n          )\r\n        }\r\n      }, 1000)\r\n    },\r\n    /**\r\n     * 向iframe子页面发送消息\r\n     */\r\n    postParam() {\r\n      window.addEventListener('message', (evt) => {\r\n        // 接收子工程数据\r\n        // 若子页面已经加载好通知父工程修改了isChildrenReady的状态\r\n        if (evt.data.type === 'dopUnify') {\r\n          // 当首页配置了 外系统菜单时 切换tab页面时 此时isChildrenReady在初始化时已经为true 所以手动给子工程传参\r\n          if (this.isChildrenReady) {\r\n            this.projectName = evt.data.projectName\r\n            this.postMess()\r\n            return\r\n          }\r\n          this.isChildrenReady = true\r\n          this.projectName = evt.data.projectName\r\n        }\r\n      })\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n.extendIframe {\r\n  width: 100%;\r\n  height: 100%;\r\n  position: absolute;\r\n  top: 3rem;\r\n  bottom: 0;\r\n  overflow: hidden;\r\n}\r\n.content {\r\n  margin-top: 30px;\r\n}\r\n</style>\r\n"]}]}