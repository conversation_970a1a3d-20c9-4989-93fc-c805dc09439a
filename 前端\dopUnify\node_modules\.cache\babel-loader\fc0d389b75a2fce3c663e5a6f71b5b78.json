{"remainingRequest": "E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1\\数字运营平台-统一门户工程\\dopUnify\\node_modules\\babel-loader\\lib\\index.js!E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1\\数字运营平台-统一门户工程\\dopUnify\\node_modules\\eslint-loader\\index.js??ref--13-0!E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1\\数字运营平台-统一门户工程\\dopUnify\\src\\views\\system\\dailyManage\\log\\component\\table\\info.js", "dependencies": [{"path": "E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1\\数字运营平台-统一门户工程\\dopUnify\\src\\views\\system\\dailyManage\\log\\component\\table\\info.js", "mtime": 1716875177707}, {"path": "E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1\\数字运营平台-统一门户工程\\dopUnify\\babel.config.js", "mtime": 1716875191548}, {"path": "E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1\\数字运营平台-统一门户工程\\dopUnify\\.babelrc", "mtime": 1716875191502}, {"path": "E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1\\数字运营平台-统一门户工程\\dopUnify\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1\\数字运营平台-统一门户工程\\dopUnify\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1\\数字运营平台-统一门户工程\\dopUnify\\node_modules\\eslint-loader\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:aW1wb3J0IHsgdjEgYXMgdXVpZHYxIH0gZnJvbSAndXVpZCc7Ci8vIOihqOWktApleHBvcnQgdmFyIGNvbmZpZ1RhYmxlID0gZnVuY3Rpb24gY29uZmlnVGFibGUodGhhdCkgewogIHJldHVybiBbewogICAgbmFtZTogJ29yZ2FuX25vJywKICAgIGxhYmVsOiAn5py65p6E5Y+3JywKICAgIHdpZHRoOiAxODAsCiAgICBpZDogdXVpZHYxKCkKICB9LCB7CiAgICBuYW1lOiAndXNlcl9uYW1lJywKICAgIGxhYmVsOiAn55So5oi35ZCNJywKICAgIHdpZHRoOiAxMDAsCiAgICBpZDogdXVpZHYxKCkKICB9LCB7CiAgICBuYW1lOiAnb3Blcl9tb2R1bGUnLAogICAgbGFiZWw6ICfmk43kvZzmqKHlnZcnLAogICAgd2lkdGg6IDE0MCwKICAgIGlkOiB1dWlkdjEoKQogIH0sIHsKICAgIG5hbWU6ICdvcGVyX3R5cGUnLAogICAgbGFiZWw6ICfmk43kvZznsbvlnosnLAogICAgd2lkdGg6IDEzMCwKICAgIGlkOiB1dWlkdjEoKQogIH0sIHsKICAgIG5hbWU6ICdjb250ZW50JywKICAgIGxhYmVsOiAn5pON5L2c5YaF5a65JywKICAgIGlkOiB1dWlkdjEoKQogIH0sIHsKICAgIG5hbWU6ICdsb2dfZGF0ZScsCiAgICBsYWJlbDogJ+W9leWFpeaXtumXtCcsCiAgICB3aWR0aDogMTYwLAogICAgaWQ6IHV1aWR2MSgpCiAgfV07Cn07"}, {"version": 3, "names": ["v1", "uuidv1", "configTable", "that", "name", "label", "width", "id"], "sources": ["E:/1_Project/XYD_Project/dop-4.0/dop-4.1/数字运营平台-统一门户工程/dopUnify/src/views/system/dailyManage/log/component/table/info.js"], "sourcesContent": ["import { v1 as uuidv1 } from 'uuid'\r\n// 表头\r\nexport const configTable = (that) => [\r\n  {\r\n    name: 'organ_no',\r\n    label: '机构号',\r\n    width: 180,\r\n    id: uuidv1()\r\n  },\r\n  {\r\n    name: 'user_name',\r\n    label: '用户名',\r\n    width: 100,\r\n    id: uuidv1()\r\n  },\r\n  {\r\n    name: 'oper_module',\r\n    label: '操作模块',\r\n    width: 140,\r\n    id: uuidv1()\r\n  },\r\n  {\r\n    name: 'oper_type',\r\n    label: '操作类型',\r\n    width: 130,\r\n    id: uuidv1()\r\n  },\r\n  {\r\n    name: 'content',\r\n    label: '操作内容',\r\n    id: uuidv1()\r\n  },\r\n  {\r\n    name: 'log_date',\r\n    label: '录入时间',\r\n    width: 160,\r\n    id: uuidv1()\r\n  }\r\n]\r\n"], "mappings": "AAAA,SAASA,EAAE,IAAIC,MAAM,QAAQ,MAAM;AACnC;AACA,OAAO,IAAMC,WAAW,GAAG,SAAdA,WAAW,CAAIC,IAAI;EAAA,OAAK,CACnC;IACEC,IAAI,EAAE,UAAU;IAChBC,KAAK,EAAE,KAAK;IACZC,KAAK,EAAE,GAAG;IACVC,EAAE,EAAEN,MAAM;EACZ,CAAC,EACD;IACEG,IAAI,EAAE,WAAW;IACjBC,KAAK,EAAE,KAAK;IACZC,KAAK,EAAE,GAAG;IACVC,EAAE,EAAEN,MAAM;EACZ,CAAC,EACD;IACEG,IAAI,EAAE,aAAa;IACnBC,KAAK,EAAE,MAAM;IACbC,KAAK,EAAE,GAAG;IACVC,EAAE,EAAEN,MAAM;EACZ,CAAC,EACD;IACEG,IAAI,EAAE,WAAW;IACjBC,KAAK,EAAE,MAAM;IACbC,KAAK,EAAE,GAAG;IACVC,EAAE,EAAEN,MAAM;EACZ,CAAC,EACD;IACEG,IAAI,EAAE,SAAS;IACfC,KAAK,EAAE,MAAM;IACbE,EAAE,EAAEN,MAAM;EACZ,CAAC,EACD;IACEG,IAAI,EAAE,UAAU;IAChBC,KAAK,EAAE,MAAM;IACbC,KAAK,EAAE,GAAG;IACVC,EAAE,EAAEN,MAAM;EACZ,CAAC,CACF;AAAA"}]}