{"remainingRequest": "E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1\\数字运营平台-统一门户工程\\dopUnify\\node_modules\\babel-loader\\lib\\index.js!E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1\\数字运营平台-统一门户工程\\dopUnify\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1\\数字运营平台-统一门户工程\\dopUnify\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1\\数字运营平台-统一门户工程\\dopUnify\\src\\views\\system\\externalManage\\priv\\component\\tree\\index.vue?vue&type=script&lang=js&", "dependencies": [{"path": "E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1\\数字运营平台-统一门户工程\\dopUnify\\src\\views\\system\\externalManage\\priv\\component\\tree\\index.vue", "mtime": 1716875178537}, {"path": "E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1\\数字运营平台-统一门户工程\\dopUnify\\babel.config.js", "mtime": 1716875191548}, {"path": "E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1\\数字运营平台-统一门户工程\\dopUnify\\.babelrc", "mtime": 1716875191502}, {"path": "E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1\\数字运营平台-统一门户工程\\dopUnify\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1\\数字运营平台-统一门户工程\\dopUnify\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1\\数字运营平台-统一门户工程\\dopUnify\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1\\数字运营平台-统一门户工程\\dopUnify\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1655715099000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:aW1wb3J0IF90b0NvbnN1bWFibGVBcnJheSBmcm9tICJFOi8xX1Byb2plY3QvWFlEX1Byb2plY3QvZG9wLTQuMC9kb3AtNC4xL1x1NjU3MFx1NUI1N1x1OEZEMFx1ODQyNVx1NUU3M1x1NTNGMC1cdTdFREZcdTRFMDBcdTk1RThcdTYyMzdcdTVERTVcdTdBMEIvZG9wVW5pZnkvbm9kZV9tb2R1bGVzL0BiYWJlbC9ydW50aW1lL2hlbHBlcnMvZXNtL3RvQ29uc3VtYWJsZUFycmF5LmpzIjsKaW1wb3J0IF9jcmVhdGVGb3JPZkl0ZXJhdG9ySGVscGVyIGZyb20gIkU6LzFfUHJvamVjdC9YWURfUHJvamVjdC9kb3AtNC4wL2RvcC00LjEvXHU2NTcwXHU1QjU3XHU4RkQwXHU4NDI1XHU1RTczXHU1M0YwLVx1N0VERlx1NEUwMFx1OTVFOFx1NjIzN1x1NURFNVx1N0EwQi9kb3BVbmlmeS9ub2RlX21vZHVsZXMvQGJhYmVsL3J1bnRpbWUvaGVscGVycy9lc20vY3JlYXRlRm9yT2ZJdGVyYXRvckhlbHBlci5qcyI7CmltcG9ydCAiY29yZS1qcy9tb2R1bGVzL2VzLmFycmF5LnJlZHVjZS5qcyI7CmltcG9ydCAiY29yZS1qcy9tb2R1bGVzL2VzLm9iamVjdC50by1zdHJpbmcuanMiOwppbXBvcnQgImNvcmUtanMvbW9kdWxlcy9lcy5udW1iZXIuY29uc3RydWN0b3IuanMiOwppbXBvcnQgImNvcmUtanMvbW9kdWxlcy9lcy5hcnJheS5jb25jYXQuanMiOwppbXBvcnQgImNvcmUtanMvbW9kdWxlcy9lcy5hcnJheS5zb3J0LmpzIjsKLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KCmltcG9ydCB7IGNvbW1vbk1zZ1N1Y2Nlc3MsIGNvbW1vbk1zZ1dhcm4sIGNvbW1vbk1zZ0NvbmZpcm0gfSBmcm9tICdAL3V0aWxzL21lc3NhZ2UuanMnOyAvLyDmj5DnpLrkv6Hmga8KaW1wb3J0IHsgZGljdGlvbmFyeUdldCB9IGZyb20gJ0AvdXRpbHMvZGljdGlvbmFyeS5qcyc7IC8vIOWtl+WFuOW4uOmHjwoKaW1wb3J0IHsgc3lzdGVtIH0gZnJvbSAnQC9hcGknOwp2YXIgX3N5c3RlbSRTeXNFeHRlclByaXYgPSBzeXN0ZW0uU3lzRXh0ZXJQcml2LAogIHF1ZXJ5ID0gX3N5c3RlbSRTeXNFeHRlclByaXYucXVlcnksCiAgbW9kaWZ5ID0gX3N5c3RlbSRTeXNFeHRlclByaXYubW9kaWZ5OwpleHBvcnQgZGVmYXVsdCB7CiAgbmFtZTogJ1RyZWVMaXN0JywKICBwcm9wczogewogICAgZGVmYXVsdEZvcm06IHsKICAgICAgdHlwZTogT2JqZWN0LAogICAgICBkZWZhdWx0OiBmdW5jdGlvbiBfZGVmYXVsdCgpIHsKICAgICAgICByZXR1cm4ge307CiAgICAgIH0KICAgIH0KICB9LAogIGRhdGE6IGZ1bmN0aW9uIGRhdGEoKSB7CiAgICByZXR1cm4gewogICAgICB0cmVlRGF0YTogW10sCiAgICAgIC8vIOagkeW9oue7hOS7tueahOaVsOaNrgogICAgICBsb2FkaW5nOiBmYWxzZSwKICAgICAgLy8g5Yqg6L295Yqo55S7CiAgICAgIGRlZmF1bHRJZDogJzAwMDAwMScsCiAgICAgIC8vIOm7mOiupOWxleW8gOagueiKgueCuQogICAgICBvbGRTZWxlY3REYXRhczogW10sCiAgICAgIC8vIOm7mOiupOmAieS4reiKgueCuQogICAgICBiYWNrZGF0YTogW10sCiAgICAgIC8vIOS/neWtmOaVsOaNruWQiOW5tgogICAgICBidG5EYXRhczogewogICAgICAgIGJ0blN1Ym1pdDogewogICAgICAgICAgc2hvdzogdGhpcy4kYXR0cnNbJ2J0bi1hbGwnXS5idG5TdWJtaXQKICAgICAgICB9LAogICAgICAgIGJ0bkVtcHR5OiB7CiAgICAgICAgICBzaG93OiB0aGlzLiRhdHRyc1snYnRuLWFsbCddLmJ0bkVtcHR5CiAgICAgICAgfQogICAgICB9LAogICAgICB0b3RhbEhlaWdodDogewogICAgICAgIGhlaWdodDogMCwKICAgICAgICBvdmVyZmxvdzogJ2F1dG8nCiAgICAgIH0gLy8g5LiL5pa557uE5Lu25Yqo5oCB6auY5bqmCiAgICB9OwogIH0sCgogIHdhdGNoOiB7CiAgICBkZWZhdWx0Rm9ybTogewogICAgICBoYW5kbGVyOiBmdW5jdGlvbiBoYW5kbGVyKHZhbCkgewogICAgICAgIHRoaXMucXVlcnlMaXN0KCk7CiAgICAgIH0sCiAgICAgIGRlZXA6IHRydWUKICAgIH0KICB9LAogIGNyZWF0ZWQ6IGZ1bmN0aW9uIGNyZWF0ZWQoKSB7CiAgICB2YXIgbWluT2JqID0gdGhpcy4kc3RvcmUuZ2V0dGVycy5vcmdhbkxpc3QucmVkdWNlKGZ1bmN0aW9uIChwcmV2LCBjdXJyKSB7CiAgICAgIHJldHVybiBOdW1iZXIocHJldi5yZXNlcnZlKSA8IE51bWJlcihjdXJyLnJlc2VydmUpID8gcHJldiA6IGN1cnI7CiAgICB9KTsKICAgIHRoaXMuZGVmYXVsdElkID0gbWluT2JqLmlkOwogIH0sCiAgbW91bnRlZDogZnVuY3Rpb24gbW91bnRlZCgpIHsKICAgIHZhciBfdGhpcyA9IHRoaXM7CiAgICB0aGlzLnRyZWVEYXRhID0gdGhpcy4kc3RvcmUuZ2V0dGVycy5vcmdhblRyZWU7CiAgICB0aGlzLiRuZXh0VGljayhmdW5jdGlvbiAoKSB7CiAgICAgIF90aGlzLnRvdGFsSGVpZ2h0LmhlaWdodCA9IF90aGlzLiRhdHRycy5oZWlnaHQgLSA1MCArICdweCc7IC8vIDIw5LiK5LiL57uE5Lu25LmL6Ze05Li66L656LedCiAgICB9KTsKICB9LAoKICBtZXRob2RzOiB7CiAgICAvKioNCiAgICAgKiDljZXlh7vmoJHoioLngrk6IOiKgueCuemAieS4reeKtuaAgeWPkeeUn+WPmOWMluaXtueahOWbnuiwgyAgYmVnaW4gLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLQ0KICAgICAqIEBwYXJhbSB7T2JqZWN0fWRhdGHvvJpjdXJyZW50T2JqIOivpeiKgueCueaJgOWvueW6lOeahOWvueixoQ0KICAgICAqIEBwYXJhbSB7Qm9vbGVhbn1jaGVja2Vk77yadHJlZVN0YXR1cyDoioLngrnmnKzouqvmmK/lkKbooqvpgInkuK0NCiAgICAgKi8KICAgIGhhbmRsZUNoZWNrQ2hhbmdlOiBmdW5jdGlvbiBoYW5kbGVDaGVja0NoYW5nZShjdXJyZW50T2JqLCB0cmVlU3RhdHVzKSB7CiAgICAgIC8vIOeUqOS6ju+8mueItuWtkOiKgueCueS4peagvOS6kuS4jeWFs+iBlOaXtu+8jOeItuiKgueCueWLvumAieWPmOWMluaXtumAmuefpeWtkOiKgueCueWQjOatpeWPmOWMlu+8jOWunueOsOWNleWQkeWFs+iBlOOAggogICAgICB2YXIgc2VsZWN0ZWQgPSB0cmVlU3RhdHVzLmNoZWNrZWRLZXlzLmluZGV4T2YoY3VycmVudE9iai5pZCk7IC8vIC0x5pyq6YCJ5LitCiAgICAgIC8vIOmAieS4rQogICAgICBpZiAoc2VsZWN0ZWQgIT09IC0xKSB7CiAgICAgICAgdGhpcy5zZWxlY3RlZFBhcmVudChjdXJyZW50T2JqKTsgLy8g5a2Q6IqC54K55Y+q6KaB6KKr6YCJ5Lit54i26IqC54K55bCx6KKr6YCJ5LitCiAgICAgICAgdGhpcy51bml0ZUNoaWxkU2FtZShjdXJyZW50T2JqLCB0cnVlKTsgLy8g57uf5LiA5aSE55CG5a2Q6IqC54K55Li655u45ZCM55qE5Yu+6YCJ54q25oCBCiAgICAgIH0gZWxzZSB7CiAgICAgICAgaWYgKGN1cnJlbnRPYmouY2hpbGRyZW4ubGVuZ3RoICE9PSAwKSB7CiAgICAgICAgICAvLyDmnKrpgInkuK0g5aSE55CG5a2Q6IqC54K55YWo6YOo5pyq6YCJ5LitCiAgICAgICAgICB0aGlzLnVuaXRlQ2hpbGRTYW1lKGN1cnJlbnRPYmosIGZhbHNlKTsKICAgICAgICB9CiAgICAgIH0KICAgIH0sCiAgICAvKioNCiAgICAgKiDnu5/kuIDlpITnkIblrZDoioLngrnkuLrnm7jlkIznmoTli77pgInnirbmgIENCiAgICAgKiBAcGFyYW0ge09iamVjdH0gdHJlZUxpc3Qg6YCJ5Lit6IqC54K5DQogICAgICogQHBhcmFtIHtCb29sZWFufSBpc1NlbGVjdGVkIOmAieS4reeKtuaAgQ0KICAgICAqICovCiAgICB1bml0ZUNoaWxkU2FtZTogZnVuY3Rpb24gdW5pdGVDaGlsZFNhbWUodHJlZUxpc3QsIGlzU2VsZWN0ZWQpIHsKICAgICAgdGhpcy4kcmVmcy50cmVlLnNldENoZWNrZWQodHJlZUxpc3QsIGlzU2VsZWN0ZWQpOwogICAgICBmb3IgKHZhciBpID0gMDsgaSA8IHRyZWVMaXN0LmNoaWxkcmVuLmxlbmd0aDsgaSsrKSB7CiAgICAgICAgdGhpcy51bml0ZUNoaWxkU2FtZSh0cmVlTGlzdC5jaGlsZHJlbltpXSwgaXNTZWxlY3RlZCk7CiAgICAgIH0KICAgIH0sCiAgICAvKioNCiAgICAgKiDnu5/kuIDlpITnkIbniLboioLngrnkuLrpgInkuK0NCiAgICAgKiBAcGFyYW0ge09iamVjdH1jdXJyZW50T2JqIOW9k+WJjemAieS4reiKgueCueeahOeItuiKgueCuSovCiAgICBzZWxlY3RlZFBhcmVudDogZnVuY3Rpb24gc2VsZWN0ZWRQYXJlbnQoY3VycmVudE9iaikgewogICAgICB2YXIgY3VycmVudE5vZGUgPSB0aGlzLiRyZWZzLnRyZWUuZ2V0Tm9kZShjdXJyZW50T2JqKTsKICAgICAgaWYgKGN1cnJlbnROb2RlLnBhcmVudC5rZXkgIT09IHVuZGVmaW5lZCkgewogICAgICAgIHRoaXMuJHJlZnMudHJlZS5zZXRDaGVja2VkKGN1cnJlbnROb2RlLnBhcmVudCwgdHJ1ZSk7CiAgICAgICAgdGhpcy5zZWxlY3RlZFBhcmVudChjdXJyZW50Tm9kZS5wYXJlbnQpOwogICAgICB9CiAgICB9LAogICAgLyog5Y2V5Ye75qCR6IqC54K5OiDoioLngrnpgInkuK3nirbmgIHlj5HnlJ/lj5jljJbml7bnmoTlm57osIMgIGVuZCAtLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tKi8KICAgIC8qKg0KICAgICAqIOaMiemSru+8muafpeivoiovCiAgICBxdWVyeUxpc3Q6IGZ1bmN0aW9uIHF1ZXJ5TGlzdCgpIHsKICAgICAgdmFyIF90aGlzMiA9IHRoaXM7CiAgICAgIHRoaXMuaGFuZGxlRW1wdHkoKTsKICAgICAgdmFyIG1zZyA9IHsKICAgICAgICBwYXJhbWV0ZXJMaXN0OiBbe31dLAogICAgICAgIHN5c01hcDogewogICAgICAgICAgb3Blcl90eXBlOiBkaWN0aW9uYXJ5R2V0KCdPUEVSQVRFX1FVRVJZJyksCiAgICAgICAgICBzeXN0ZW1fbm86IHRoaXMuZGVmYXVsdEZvcm0uc3lzdGVtX25vCiAgICAgICAgfQogICAgICB9OwogICAgICBxdWVyeShtc2cpLnRoZW4oZnVuY3Rpb24gKHJlc3BvbnNlKSB7CiAgICAgICAgdmFyIGxpc3QgPSByZXNwb25zZS5yZXRNYXAubGlzdDsKICAgICAgICBpZiAobGlzdC5sZW5ndGggPT09IDApIHsKICAgICAgICAgIF90aGlzMi5vbGRTZWxlY3REYXRhcyA9IFtdOwogICAgICAgICAgX3RoaXMyLiRyZWZzLnRyZWUuc2V0Q2hlY2tlZEtleXMoW10pOyAvLyDmuIXnqbrmiYDmnInpgInkuK0KICAgICAgICB9IGVsc2UgewogICAgICAgICAgdmFyIGtleUFyciA9IFtdOwogICAgICAgICAgdmFyIF9pdGVyYXRvciA9IF9jcmVhdGVGb3JPZkl0ZXJhdG9ySGVscGVyKGxpc3QpLAogICAgICAgICAgICBfc3RlcDsKICAgICAgICAgIHRyeSB7CiAgICAgICAgICAgIGZvciAoX2l0ZXJhdG9yLnMoKTsgIShfc3RlcCA9IF9pdGVyYXRvci5uKCkpLmRvbmU7KSB7CiAgICAgICAgICAgICAgdmFyIGl0ZW0gPSBfc3RlcC52YWx1ZTsKICAgICAgICAgICAgICBrZXlBcnIucHVzaChpdGVtLm9yZ2FuX25vKTsKICAgICAgICAgICAgfQogICAgICAgICAgfSBjYXRjaCAoZXJyKSB7CiAgICAgICAgICAgIF9pdGVyYXRvci5lKGVycik7CiAgICAgICAgICB9IGZpbmFsbHkgewogICAgICAgICAgICBfaXRlcmF0b3IuZigpOwogICAgICAgICAgfQogICAgICAgICAgX3RoaXMyLm9sZFNlbGVjdERhdGFzID0ga2V5QXJyOwogICAgICAgIH0KICAgICAgfSk7CiAgICB9LAogICAgaGFuZGxlRW1wdHk6IGZ1bmN0aW9uIGhhbmRsZUVtcHR5KCkgewogICAgICB0aGlzLiRyZWZzLnRyZWUuc2V0Q2hlY2tlZEtleXMoW10pOyAvLyDmuIXnqbrmiYDmnInpgInkuK0KICAgIH0sCiAgICBoYW5kbGVTdWJtaXQ6IGZ1bmN0aW9uIGhhbmRsZVN1Ym1pdCgpIHsKICAgICAgdmFyIF90aGlzMyA9IHRoaXM7CiAgICAgIHZhciBzeXN0ZW1fbm8gPSB0aGlzLmRlZmF1bHRGb3JtLnN5c3RlbV9ubzsKICAgICAgaWYgKCFzeXN0ZW1fbm8pIHsKICAgICAgICBjb21tb25Nc2dXYXJuKCfor7fpgInmi6npnIDopoHphY3nva7mnYPpmZDnmoTns7vnu5/vvIEnLCB0aGlzKTsKICAgICAgICByZXR1cm47CiAgICAgIH0KICAgICAgdGhpcy5iYWNrZGF0YSA9IFtdOwogICAgICBjb21tb25Nc2dDb25maXJtKCfmmK/lkKbnoa7orqTkv53lrZjvvJ8nLCB0aGlzLCBmdW5jdGlvbiAocGFyYW0pIHsKICAgICAgICBpZiAocGFyYW0pIHsKICAgICAgICAgIF90aGlzMy5zaG93TG9hZGluZygpOwogICAgICAgICAgdmFyIGNoZWNrZWRBbGwgPSBfdGhpczMuJHJlZnMudHJlZS5nZXRDaGVja2VkS2V5cygpOyAvLyDojrflj5bmiYDmnInpgInkuK1ub2RlcwogICAgICAgICAgLy8g5paw5pen5pWw5o2u5qC85byP5YyWCiAgICAgICAgICB2YXIgb2xkRGF0YSA9IF90aGlzMy5ub2Rlc1NwaWx0KF90aGlzMy5vbGRTZWxlY3REYXRhcyk7CiAgICAgICAgICB2YXIgbmV3RGF0YSA9IF90aGlzMy5ub2Rlc1NwaWx0KGNoZWNrZWRBbGwpOwogICAgICAgICAgLy8g5paw5pen5pWw5o2u5q+U5a+5CiAgICAgICAgICB2YXIgYWRkTm9kZXMgPSBfdGhpczMuZGF0YUZpbHRlcihuZXdEYXRhLCBvbGREYXRhKTsKICAgICAgICAgIHZhciBkZWxldGVOb2RlcyA9IF90aGlzMy5kYXRhRmlsdGVyKG9sZERhdGEsIG5ld0RhdGEpOwoKICAgICAgICAgIC8vIOS8oOmAkuaVsOaNruagvOW8j+WMliBiZWdpbgogICAgICAgICAgX3RoaXMzLmZpbHRlckRhdGEoc3lzdGVtX25vLCBhZGROb2RlcywgJ2FkZE5vZGVzJyk7CiAgICAgICAgICBfdGhpczMuZmlsdGVyRGF0YShzeXN0ZW1fbm8sIGRlbGV0ZU5vZGVzLCAnZGVsZXRlTm9kZXMnKTsKICAgICAgICAgIC8vIOaTjeS9nOaVsOaNruagvOW8j+WMliBlbmQKICAgICAgICAgIHZhciBtc2cgPSB7CiAgICAgICAgICAgIHBhcmFtZXRlckxpc3Q6IF90aGlzMy5iYWNrZGF0YSwKICAgICAgICAgICAgb3Blcl90eXBlOiBkaWN0aW9uYXJ5R2V0KCdPUEVSQVRFX01PRElGWScpLAogICAgICAgICAgICBzeXN0ZW1fbm86IHN5c3RlbV9ubwogICAgICAgICAgfTsKICAgICAgICAgIGlmICghX3RoaXMzLmJhY2tkYXRhLmxlbmd0aCkgewogICAgICAgICAgICBjb21tb25Nc2dXYXJuKCfmsqHmnInlj5jmm7TnmoTmnYPpmZDvvIzor7fli77pgInmnYPpmZDvvIEnLCBfdGhpczMpOwogICAgICAgICAgICBfdGhpczMuc2hvd0xvYWRpbmcoKTsKICAgICAgICAgICAgcmV0dXJuOwogICAgICAgICAgfQogICAgICAgICAgbW9kaWZ5KG1zZykudGhlbihmdW5jdGlvbiAocmVzcG9uc2UpIHsKICAgICAgICAgICAgY29tbW9uTXNnU3VjY2Vzcygn5p2D6ZmQ6YWN572u5oiQ5Yqf77yBJywgX3RoaXMzKTsKICAgICAgICAgICAgX3RoaXMzLnF1ZXJ5TGlzdCgpOwogICAgICAgICAgfSkuY2F0Y2goZnVuY3Rpb24gKCkgewogICAgICAgICAgICBjb21tb25Nc2dXYXJuKCfmnYPpmZDphY3nva7plJnor68nLCBfdGhpczMpOwogICAgICAgICAgfSk7CiAgICAgICAgICBfdGhpczMuc2hvd0xvYWRpbmcoKTsKICAgICAgICB9CiAgICAgIH0pOwogICAgfSwKICAgIC8qKg0KICAgICAqIOS/neWtmO+8muaVsOaNruagvOW8j+WMlg0KICAgICAqIEBwYXJhbSB7U3RyaW5nfSBzeXN0ZW1fbm8g57O757uf5Y+3DQogICAgICogQHBhcmFtIHtBcnJheX0gYXJyIOagvOW8j+WMluaVsOe7hA0KICAgICAqIEBwYXJhbSB7U3RyaW5nfSBvcGVyX25vZGVzIOagvOW8j+WMluexu+WeiyovCiAgICBmaWx0ZXJEYXRhOiBmdW5jdGlvbiBmaWx0ZXJEYXRhKHN5c3RlbV9ubywgYXJyLCBvcGVyX25vZGVzKSB7CiAgICAgIHZhciBfaXRlcmF0b3IyID0gX2NyZWF0ZUZvck9mSXRlcmF0b3JIZWxwZXIoYXJyKSwKICAgICAgICBfc3RlcDI7CiAgICAgIHRyeSB7CiAgICAgICAgZm9yIChfaXRlcmF0b3IyLnMoKTsgIShfc3RlcDIgPSBfaXRlcmF0b3IyLm4oKSkuZG9uZTspIHsKICAgICAgICAgIHZhciBrZXkgPSBfc3RlcDIudmFsdWU7CiAgICAgICAgICBpZiAoa2V5ICE9PSAnJykgewogICAgICAgICAgICB0aGlzLmJhY2tkYXRhID0gW10uY29uY2F0KF90b0NvbnN1bWFibGVBcnJheSh0aGlzLmJhY2tkYXRhKSwgW3sKICAgICAgICAgICAgICBleHRlcm5hbF9zeXN0ZW1fbm86IHN5c3RlbV9ubywKICAgICAgICAgICAgICBvcmdhbl9ubzoga2V5LAogICAgICAgICAgICAgIG9wZXJOb2Rlczogb3Blcl9ub2RlcwogICAgICAgICAgICB9XSk7CiAgICAgICAgICB9CiAgICAgICAgfQogICAgICB9IGNhdGNoIChlcnIpIHsKICAgICAgICBfaXRlcmF0b3IyLmUoZXJyKTsKICAgICAgfSBmaW5hbGx5IHsKICAgICAgICBfaXRlcmF0b3IyLmYoKTsKICAgICAgfQogICAgfSwKICAgIC8qKg0KICAgICAqIOWPlm5vZGVz55qE5a2X56ym5Liy77yM55So6YCX5Y+35ou85o6l5py65p6EaWQNCiAgICAgKiDmoLzlvI/kuLoxLDIsMw0KICAgICAqIEBwYXJhbSB7QXJyYXl9c3RyIOaLvOaOpeaVsOe7hCovCiAgICBub2Rlc1NwaWx0OiBmdW5jdGlvbiBub2Rlc1NwaWx0KHN0cikgewogICAgICB2YXIgc3RycyA9IHN0cjsKICAgICAgdmFyIG5ld1N0ciA9IFtdOwogICAgICBpZiAoc3Rycy5sZW5ndGggPT09IDApIHsKICAgICAgICByZXR1cm4gbmV3U3RyOwogICAgICB9CiAgICAgIHJldHVybiBzdHJzOwogICAgfSwKICAgIC8qKg0KICAgICAqIOe8lui+keaVsOaNrui/h+a7pCA6IOaWsOWinuOAgeWIoOmZpCBvbmXmnIl0d2/msqHmnInnmoTmg4XlhrUNCiAgICAgKiBAcGFyYW0ge0FycmF5fW9uZSDmlbDmja7mr5Tlr7kxDQogICAgICogQHBhcmFtIHtBcnJheX10d28g5pWw5o2u5q+U5a+5MiAqLwogICAgZGF0YUZpbHRlcjogZnVuY3Rpb24gZGF0YUZpbHRlcihvbmUxLCB0d28pIHsKICAgICAgdmFyIG9uZSA9IG9uZTEuc29ydCgpOwogICAgICB2YXIgQXMgPSBbXTsKICAgICAgdmFyIF9pdGVyYXRvcjMgPSBfY3JlYXRlRm9yT2ZJdGVyYXRvckhlbHBlcihvbmUpLAogICAgICAgIF9zdGVwMzsKICAgICAgdHJ5IHsKICAgICAgICBmb3IgKF9pdGVyYXRvcjMucygpOyAhKF9zdGVwMyA9IF9pdGVyYXRvcjMubigpKS5kb25lOykgewogICAgICAgICAgdmFyIGtleSA9IF9zdGVwMy52YWx1ZTsKICAgICAgICAgIHZhciBmbGFnID0gMDsKICAgICAgICAgIHZhciBfaXRlcmF0b3I0ID0gX2NyZWF0ZUZvck9mSXRlcmF0b3JIZWxwZXIodHdvKSwKICAgICAgICAgICAgX3N0ZXA0OwogICAgICAgICAgdHJ5IHsKICAgICAgICAgICAgZm9yIChfaXRlcmF0b3I0LnMoKTsgIShfc3RlcDQgPSBfaXRlcmF0b3I0Lm4oKSkuZG9uZTspIHsKICAgICAgICAgICAgICB2YXIgaXRlbSA9IF9zdGVwNC52YWx1ZTsKICAgICAgICAgICAgICBpZiAoa2V5LnN1YnN0cmluZygwLCA3KSA9PT0gaXRlbS5zdWJzdHJpbmcoMCwgNykpIHsKICAgICAgICAgICAgICAgIGZsYWcgPSAxOwogICAgICAgICAgICAgICAgYnJlYWs7CiAgICAgICAgICAgICAgfQogICAgICAgICAgICB9CiAgICAgICAgICB9IGNhdGNoIChlcnIpIHsKICAgICAgICAgICAgX2l0ZXJhdG9yNC5lKGVycik7CiAgICAgICAgICB9IGZpbmFsbHkgewogICAgICAgICAgICBfaXRlcmF0b3I0LmYoKTsKICAgICAgICAgIH0KICAgICAgICAgIGlmIChmbGFnID09PSAwKSB7CiAgICAgICAgICAgIEFzID0gW10uY29uY2F0KF90b0NvbnN1bWFibGVBcnJheShBcyksIFtrZXldKTsKICAgICAgICAgIH0KICAgICAgICB9CiAgICAgIH0gY2F0Y2ggKGVycikgewogICAgICAgIF9pdGVyYXRvcjMuZShlcnIpOwogICAgICB9IGZpbmFsbHkgewogICAgICAgIF9pdGVyYXRvcjMuZigpOwogICAgICB9CiAgICAgIHJldHVybiBBczsKICAgIH0sCiAgICAvKioNCiAgICAgKiDmoJHliqDovb3kuK3liqjnlLvphY3nva4qLwogICAgc2hvd0xvYWRpbmc6IGZ1bmN0aW9uIHNob3dMb2FkaW5nKCkgewogICAgICB0aGlzLmxvYWRpbmcgPSAhdGhpcy5sb2FkaW5nOwogICAgfQogIH0KfTs="}, null]}