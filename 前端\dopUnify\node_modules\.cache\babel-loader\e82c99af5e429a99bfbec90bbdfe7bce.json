{"remainingRequest": "E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-q<PERSON><PERSON>n\\数字运营平台-统一门户工程\\dopUnify\\node_modules\\babel-loader\\lib\\index.js!E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan\\数字运营平台-统一门户工程\\dopUnify\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan\\数字运营平台-统一门户工程\\dopUnify\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan\\数字运营平台-统一门户工程\\dopUnify\\src\\views\\system\\config\\message\\systemMsg\\index.vue?vue&type=script&lang=js&", "dependencies": [{"path": "E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-<PERSON><PERSON><PERSON>n\\数字运营平台-统一门户工程\\dopUnify\\src\\views\\system\\config\\message\\systemMsg\\index.vue", "mtime": 1703583638636}, {"path": "E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-<PERSON><PERSON><PERSON>n\\数字运营平台-统一门户工程\\dopUnify\\babel.config.js", "mtime": 1686019818359}, {"path": "E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-<PERSON><PERSON><PERSON>n\\数字运营平台-统一门户工程\\dopUnify\\.babelrc", "mtime": 1705285078668}, {"path": "E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-<PERSON><PERSON><PERSON>n\\数字运营平台-统一门户工程\\dopUnify\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-<PERSON><PERSON><PERSON>n\\数字运营平台-统一门户工程\\dopUnify\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-<PERSON><PERSON><PERSON>n\\数字运营平台-统一门户工程\\dopUnify\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-<PERSON><PERSON><PERSON>n\\数字运营平台-统一门户工程\\dopUnify\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1655715099000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA4JA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA,SACAA,eACAC,kBACAC,wBACA;AACA;AACA;EAAAC;EAAAC;EAAAC;EAAAC;AACA;AACA;AACA;AACA;AACA;AAEA;EACAC;EACAC;IACAC;IACAC;IACAC;IACAC;EACA;EACAC;EACAC;IACA;MACAC;MACAC;MAAA;MACAC;MAAA;MACAC;MAAA;MACAC;MACAC;QAAAC;QAAAC;MAAA;MACAC;QACA;QACAC;MACA;MACAC;QACAC;UACAC;QACA;QACAC;UACAD;QACA;MACA;MACAE;MACAC;QACA;QACAC;QAAA;QACAC;QACAC;QAAA;QACAC;QAAA;QACAC;QACAC;UACAtB;UAAA;UACAuB;UACAC;QACA;;QACAC;UACAC;UACAC;UAAA;UACAC;QACA;;QACAC;MACA;;MACAC;QACAC;QAAA;QACAC;QAAA;QACAC;QAAA;QACAX;UACA;UACAY;UACAC;UACAC;QACA;;QACAC;QAAA;QACAC;QAAA;QACAC;UACAC;UAAA;UACAL;UAAA;UACAM;UAAA;UACAC;UAAA;UACAC;UAAA;UACAC;QACA;MACA;MACAC;QACA;QACAd;QACA;QACAT;UACA;UACAa;UAAA;UACAC;QACA;;QACAU;UACA;UACAzC;UAAA;UACAC;YACAyC;UACA;UAAA;UACAC;QACA;MACA;;MACAC;MAAA;MACAC;MAAA;MACAC;MAAA;MACAC;MAAA;MACAC;QACAC;MACA;;MACAC;QACAD;MACA;;MACAE;QACA;QACAzB;QACAT;UACA;UACAa;UAAA;UACAC;QACA;;QACAqB;QACA5B;QAAA;QACA6B;UACA;UACAzC;UAAA;UACAE;UAAA;UACAC;UAAA;UACAC;UACAC;YACAtB;YAAA;YACAuB;YACAC;UACA;;UACAC;YACAC;YACAC;YAAA;YACAC;UACA;QACA;;QACA+B;QAAA;QACAC;QAAA;QACAC;QAAA;QACAC;QAAA;QACAC;MACA;;MACAC;QACA;QACAjC;QACAT;UACA;UACAa;UAAA;UACAD;UACAE;QACA;;QACA6B;QAAA;QACAC;QAAA;QACAC;MACA;;MACA5C;IACA;EACA;EACA6C;EACAC;IACAhD;MACA;IACA;IACA;MACA;MACAiD;QACA;QACA;UACA;UACA;QACA;UACA;QACA;MACA;MACAC;IACA;EACA;EACAC;EACAC;IACAC;EACA;EACAC;IAAA;IACA;IACA;IACA;MACA;QACA;MACA;IACA;IACA;IACA;MACA;QACA;MACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;MACA;QACA;MACA;IACA;IACA;IACA;MACA;QACA;UACAC;QACA;QACAvF;UACA;UAAA,2CACAwF;YAAA;UAAA;YAAA;cAAA;gBAAAC;gBAAAC;cACA;gBACA;cACA;YACA;UAAA;YAAA;UAAA;YAAA;UAAA;QACA;MACA;QAAA,4CACA;UAAA;QAAA;UAAA;YAAA;cAAAD;cAAAC;YACA;cACA;YACA;UACA;QAAA;UAAA;QAAA;UAAA;QAAA;MACA;IACA;EACA;EACAC;IACA;IACA;IACA;IACA;IACA;IACA;IACAN;IACAO;IACAA;EACA;EACAC;IACA;AACA;AACA;IACAC;MACA;MACA,mCACA,qCACA,4CACA,MACA;IACA;IACA;AACA;IACAC;MACA;MACA;IACA;IACA;AACA;IACAC;MAAA;MACA;QACAT;MACA;MACAvF;QACA;QACA;QACA;UACA;UACA;QACA;MACA;IACA;IACA;AACA;IACAiG;MAAA;MACA;MACA;MACA;MACA;QACAV;QACAW;MACA;;MACAjG;QACA;QACA;UACA;YACAsF;YACAjD;YACAC;YACA4D;YACAC;YACAC;YACAC;YACAC,UACA,2CACA,4CACA,2CACA,KACA;YACAC;YACAtF,8CACA,iCACA;YAAA;YACAC,4CACA,gCACA,0CACA,KACA,2CACA,IACA,2CACA,IACA;UACA;UACA;UACAjB;YACA;YACAuG;cACA;cACA;YACA;YACA,kBAQAC;cAPAC;cACAtE;cACAC;cACAsE;cACAC;cACAC;cACAzD;YAEA;YACA;YACA;YACA;YACA;YACA;YACAuD;cACA;cACAG;YACA;YACA;YACA;cACA;gBACAC;gBAAA;gBACAC;cACA;;cACA;cACA;YACA;YACA;YACA;cACA;cACA,IACA,iCACAC,gBACAC,WACAC,8CACA;gBACA;gBACAF;gBACA;gBACAA;kBACA;oBACAA;kBACA;gBACA;cACA;gBACA;gBACAA;gBACA;gBACAA;cACA;YACA;YACA;YACA;YACA;UACA;QACA;MACA;IACA;IACA;AACA;AACA;AACA;IACAG;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBACA;gBACA;gBAAA,MACAC;kBAAA;kBAAA;gBAAA;gBACA;gBACA;gBACA;gBACA;gBACA;kBACA;kBACA;kBACA;kBACA;oBACAC;oBACAC;oBACAC;kBACA;kBACArE;kBACA;kBACA;kBACA;oBACA,8CACAkE,2BACA;kBACA;oBACA;kBACA;kBACA;kBACA;oBACA/B;oBACAmC;oBACAvB;oBACAC;oBACAoB;kBACA;kBACAnE;oBACA;oBACA;oBACA;oBACA;oBACA;oBACA;oBACA;sBACAsE;wBACAD;wBACAE;wBACAC;wBACAC;sBACA;oBACA;oBACA;sBACA;sBACA;oBACA;kBACA;gBACA;gBAAA;gBAAA;cAAA;gBAAA,MACAR;kBAAA;kBAAA;gBAAA;gBACA;gBACA;kBACA;kBACAS;kBACA;oBACAT;kBACA;kBACAA;gBACA;gBACA;gBAAA;gBAAA,OACAU;cAAA;gBAAAC;gBAAA,KACAC;kBAAA;kBAAA;gBAAA;gBAAA;cAAA;gBAAA,MAIAD;kBAAA;kBAAA;gBAAA;gBACAE,2CACAb;kBACA;kBACAc,WACAH;gBAAA,IAEA;;gBACA;gBACA;gBACA;gBAAA;gBAAA;cAAA;gBAEA;gBACA;gBACA;kBACA;gBACA;gBAAA;gBAAA,OACAI,uBACAJ,SACAA,mBACA;cAAA;gBAHAK;gBAAA,MAMAhB,yBACA;kBAAA;kBAAA;gBAAA;gBAEA;gBACA;kBACA;kBACA;kBACA;gBACA;kBACA;kBACA;gBACA;gBACA;gBACA;kBACA;kBACA;kBACA;kBACA;kBACA;oBACA,8CACAgB,uBACA;kBACA;oBACA;kBACA;gBACA;gBAAA;gBAAA;cAAA;gBAAA,MAEAhB,yBACA;kBAAA;kBAAA;gBAAA;gBAEA;gBACA;kBACAzH;gBACA;kBACA;kBACA;kBACA;kBACA;kBACA;kBACA;gBACA;gBAAA;gBAAA;cAAA;gBAAA,MAGA;kBAAA;kBAAA;gBAAA;gBACA;gBACA;gBACA;gBACA;gBACA;gBAAA;gBAAA;cAAA;gBAAA,KACAqI;kBAAA;kBAAA;gBAAA;gBACArI;gBAAA;gBAAA;cAAA;gBAAA,MACA;kBAAA;kBAAA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;cAAA;gBAMA;kBACA;kBACA;kBACA;kBACA;kBACA;oBACA;oBACA;oBACA;oBACA;oBACA;oBACA;sBACA0H;sBACAC;sBACAC;oBACA;oBACArE;oBACA;oBACA;oBACA;sBACA,8CACAkE,2BACA;oBACA;sBACA;oBACA;kBACA;gBACA;kBACA;kBACAiB;kBACA;kBACAC;oBACA;kBACA,IACA;kBACAC;oBACA;kBACA,IACA;kBACAC;oBACA;kBACA,IACA;kBACAC;oBACA;kBACA;;kBACA;gBACA;kBACA;oBACA;kBACA;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IACA;IACA;AACA;AACA;AACA;IACAC;MAAA;MACA;QACArD;QACAsD;QACAtC;MACA;MACAuC;QACA/I;QACA;QACA;UACA;QACA;MACA;IACA;IACA;AACA;AACA;IACAgJ;MACA;MACA;MACA;MACA;IACA;IACA;IACAC;MAAA;MACA;MACA;QACA;QACA;QAEAC,sBACA;UACAC;UACAC;UACAtE;QACA,GACAuE,sEACA;MACA;MACAC;QACA;QACA;QACA;MACA;IACA;IACA;AACA;AACA;AACA;AACA;AACA;IACAC;MAAA;MACA;MACA;QACA5B;QACAvB;QACAC;QACAmD;QACAV;QACAW;MACA;MACArJ;QACA;QACA;UACA;QACA;MACA;IACA;IACA;AACA;AACA;IACAsJ;MAAA;MACA3J;QACA;UACA;UACA;YACA4H;YACAE;YACAC;YACAtB;YACAmD;YACAC;YACAC;YACAC;YACA/B;YACAP;YACAC;YACAC;YACAqC;YACAC;UACA;UACA;YACAxE;YACAY;YACAzC;YAAA;YACAwF;YAAA;YACAc;UACA;UACAC;YACAlK;YACA;YACA;YACA;UACA;QACA;MACA;IACA;IACA;AACA;IACAmK;MACA;IACA;IACA;AACA;AACA;AACA;IACAC;MAAA;MACA;MACArK;QACA;UACA;UACA;YACA4H;YACAE;YACAC;YACAtB;YACAmD;YACAC;YACAC;YACAC;YACA/B;YACAP;YACAC;YACAC;YACAqC;YACAC;UACA;UACA;YACAxE;YACAY;YACAzC;YAAA;YACAwF;YAAA;YACAc;UACA;UACAC;YACAlK;YACA;YACA;YACA;YACA;UACA;QACA;MACA;IACA;IACA;AACA;AACA;IACAqK;MACA;IACA;IACA;AACA;AACA;IACAC;MACA;IACA;IACA;AACA;IACAC;MACA;IACA;IACA;AACA;AACA;IACAC;MACA;IACA;IACA;AACA;IACAC;MACA;IACA;IACA;AACA;IACAC;MACA;QAAAlI;MACA;MACA;MACA;IACA;IACA;AACA;IACAmI;MACA;IACA;EACA;AACA", "names": ["commonMsgInfo", "commonMsgConfirm", "commonMsgSuccess", "queryTree", "selectClassify", "queryMsg", "noticeModify", "name", "components", "SunNoticeDialog", "Print", "SunOperDetailDialog", "SunFlowDialog", "mixins", "data", "defaultActive", "menus", "dealArr", "activeMenu", "config", "defaultForm", "msg_content", "deal_state", "btnAll", "btnQuery", "dialogBtnDatas", "btnPass", "show", "btnRefuse", "listLoading", "table", "tableColumns", "ref", "selection", "indexNumber", "loading", "componentProps", "height", "formRow", "pageList", "totalNum", "currentPage", "pageSize", "currentRow", "dialog", "visible", "btnCancle", "btnSubmit", "top", "title", "width", "btnPrintVis", "btnAgreeVis", "noticeConfig", "imgSrc", "info", "readNum", "content", "files", "dialogForm", "form", "remark", "labelWidth", "selectClass", "msgTotalNum", "msgUnReadNum", "msgReadNum", "nodealStatus", "color", "dealStatus", "dialogOper", "queryType", "tableData", "desData", "defaultForm2", "tableDetailData", "isSublicense", "btnId", "flowConfig", "module_id", "task_id", "childFlowUrl", "computed", "watch", "handler", "deep", "created", "beforeMount", "window", "mounted", "parameterList", "returnList", "index", "item", "<PERSON><PERSON><PERSON><PERSON>", "clearTimeout", "methods", "getHeight", "handleSelect", "initMenu", "queryMenu", "content_title", "user_no", "organ_no", "organ_level", "role_no", "msg_type", "classify", "timout1", "res", "list", "numlist", "msgtotalNum", "unReadNum", "msgArr", "allNum", "unDealNum", "key", "split", "indexOf", "contentClick", "row", "publish_organ", "publish_user", "publish_time", "notice_id", "param<PERSON><PERSON>", "notice_title", "notice_content", "file_url", "flowValue", "getTaskNodePath", "rowData", "commonBlank", "flowData", "flow_type", "openTaskPageByNodePath", "detailData", "router", "kshMenuId", "yjMenuId", "unifyR", "noticeR", "transformStatus", "msg_no", "updateMessage", "moudleOpen", "dialogFlowClose", "iframeWin", "type", "projectName", "process", "timout2", "firstPage", "read_time", "parpamData", "handlePass", "notice_level", "organs", "roles", "users", "item_id", "inst_id", "oper_type", "Approve", "handleRefuse", "dialogRefuseSubmit", "changeOperVisible", "changeVisibleForm", "handlePrint", "changeVisible", "dialogSumbit", "getList", "showLoading"], "sourceRoot": "src/views/system/config/message/systemMsg", "sources": ["index.vue"], "sourcesContent": ["<!--\r\n* 系统消息: 左侧菜单\r\n-->\r\n<template>\r\n  <div class=\"app-container\">\r\n    <el-row :gutter=\"20\" class=\"commonHeight\">\r\n      <el-col :span=\"6\" style=\"position: relative\" class=\"commonHeight\">\r\n        <div class=\"app-container sun-content sun-content-center commonHeight\">\r\n          <div class=\"sun-card-title\">系统消息</div>\r\n          <div class=\"sun-card-content\">\r\n            <el-menu\r\n              :default-active=\"defaultActive\"\r\n              class=\"el-menu-vertical-demo\"\r\n              @select=\"handleSelect\"\r\n            >\r\n              <el-menu-item\r\n                v-for=\"(item, index) in menus\"\r\n                :key=\"index\"\r\n                :index=\"index + ''\"\r\n              >\r\n                <i class=\"el-icon-menu\" />\r\n                <span>{{ item.message_type }}</span>\r\n                <span\r\n                  v-if=\"item.message_type === '全部'\"\r\n                >({{ msgTotalNum }})</span>\r\n                <span\r\n                  v-else-if=\"item.message_type === '待处理'\"\r\n                >({{ msgUnReadNum }})</span>\r\n                <span\r\n                  v-else-if=\"item.message_type === '已处理'\"\r\n                >({{ msgReadNum }})</span>\r\n                <span v-else>({{ item.unDealNum }}/ {{ item.allNum }})</span>\r\n              </el-menu-item>\r\n            </el-menu>\r\n          </div>\r\n        </div>\r\n      </el-col>\r\n      <el-col :span=\"18\" class=\"commonHeight\">\r\n        <div\r\n          ref=\"box-ref\"\r\n          class=\"app-container sun-content sun-content-center commonHeight\"\r\n        >\r\n          <div ref=\"titles\" class=\"sun-card-title\">\r\n            {{ activeMenu.message_type }}\r\n          </div>\r\n          <div ref=\"roleModule\" class=\"sun-card-content\">\r\n            <sun-form\r\n              ref=\"moduleForm\"\r\n              :config=\"config\"\r\n              :default-form=\"defaultForm\"\r\n              :query=\"btnAll.btnQuery\"\r\n              :reset=\"true\"\r\n              label-width=\"10rem\"\r\n              @query=\"queryMenu\"\r\n            />\r\n            <sun-table :table-config=\"table\" @pagination=\"getList\">\r\n              <template slot=\"tableColumn\">\r\n                <el-table-column\r\n                  v-for=\"item in table.tableColumns\"\r\n                  :key=\"item.id\"\r\n                  :prop=\"item.name\"\r\n                  :label=\"item.label\"\r\n                  :width=\"item.width\"\r\n                >\r\n                  <div slot-scope=\"{ row }\">\r\n                    <span v-if=\"item.name === 'msg_type'\">{{\r\n                      row[item.name] | commonFormatValue('WORK_TYPE')\r\n                    }}</span>\r\n                    <span\r\n                      v-else-if=\"item.name === 'msg_content'\"\r\n                      class=\"msgContent textOverflow\"\r\n                      :title=\"row[item.name]\"\r\n                      @click=\"contentClick(row)\"\r\n                    >{{ row[item.name] }}</span>\r\n                    <span v-else-if=\"item.name === 'create_time'\">{{\r\n                      row[item.name] | dateTimeFormat\r\n                    }}</span>\r\n                    <span v-else-if=\"item.name === 'end_time'\">{{\r\n                      row[item.name] | dateTimeFormat\r\n                    }}</span>\r\n                    <span\r\n                      v-else-if=\"item.name === 'dealed_time' && row[item.name]\"\r\n                    >{{ row[item.name] | dateTimeFormat }}</span>\r\n                    <span\r\n                      v-else-if=\"item.name === 'deal_state'\"\r\n                      :style=\"\r\n                        row[item.name] === '0' ? nodealStatus : dealStatus\r\n                      \"\r\n                    >{{\r\n                      row[item.name] | commonFormatValue('DEAL_STATUS')\r\n                    }}</span>\r\n                    <span v-else>{{ row[item.name] }}</span>\r\n                  </div>\r\n                </el-table-column>\r\n              </template>\r\n            </sun-table>\r\n          </div>\r\n        </div>\r\n      </el-col>\r\n    </el-row>\r\n    <!--流程事项--操作申请弹框 begin -->\r\n    <SunOperDetailDialog\r\n      ref=\"refOper\"\r\n      :dialog-config=\"dialogOper\"\r\n      @dialogClose=\"changeOperVisible\"\r\n    />\r\n    <!--流程事项--操作申请弹框 end -->\r\n\r\n    <!--公告弹出框begin-->\r\n    <sun-notice-dialog\r\n      :dialog-config=\"dialog\"\r\n      @dialogClose=\"changeVisible\"\r\n      @dialogSubmit=\"dialogSumbit\"\r\n    >\r\n      <!--打印按钮插槽begin -查看公告按钮有打印 v-if 审批公告流程无打印公告按钮-->\r\n      <div slot=\"noticeSlot\" class=\"printBtn\">\r\n        <a v-if=\"dialog.btnPrintVis\" title=\"打印\">\r\n          <svg class=\"link-svg\">\r\n            <use xlink:href=\"#icon-print\" @click=\"handlePrint\" />\r\n          </svg>\r\n        </a>\r\n      </div>\r\n      <!--通过、不通过按钮插槽begin 公告流程审批会有打印按钮 v-if-->\r\n      <div v-if=\"dialog.btnAgreeVis\" slot=\"rightBtn\" class=\"dialog-footer\">\r\n        <sun-button\r\n          :btn-datas=\"dialogBtnDatas\"\r\n          @handlePass=\"handlePass()\"\r\n          @handleRefuse=\"handleRefuse()\"\r\n        />\r\n      </div>\r\n    </sun-notice-dialog>\r\n\r\n    <!--打印组件begin-->\r\n    <div style=\"display: none\">\r\n      <Print ref=\"printRef\" :notice-config=\"dialog.noticeConfig\" />\r\n    </div>\r\n\r\n    <!--公告审批--拒绝弹窗begin-->\r\n    <sun-form-dialog\r\n      ref=\"refDialog\"\r\n      :dialog-config=\"dialogForm\"\r\n      @dialogClose=\"changeVisibleForm\"\r\n      @dialogSubmit=\"dialogRefuseSubmit\"\r\n    />\r\n    <!-- 调用流程弹框 begin -->\r\n    <sun-flow-dialog\r\n      v-if=\"flowConfig.visible\"\r\n      ref=\"flowDialog\"\r\n      :dialog-config=\"flowConfig\"\r\n      @queryList=\"initMenu\"\r\n      @dialogClose=\"dialogFlowClose\"\r\n    />\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport { SunNoticeDialog, Print, SunFlowDialog } from '@/components' // 表单\r\nimport { config, configTable, formConfig } from './info' // 表单表格配置\r\nimport SunOperDetailDialog from './SunOperDetailDialog'\r\n\r\nimport ResizeMixin from '@/utils/ResizeHandler' // 整体页面是否根据总高配置\r\nimport { uploadFile, commonBlank } from '@/utils/common' // 公共方法\r\nimport { dateNowFormat10 } from '@/utils/date.js' // 日期格式化\r\nimport { dictionaryGet } from '@/utils/dictionary'\r\nimport { openTaskPageByNodePath, getTaskNodePath } from '@/utils/flowPath'\r\nimport {\r\n  commonMsgInfo,\r\n  commonMsgConfirm,\r\n  commonMsgSuccess\r\n} from '@/utils/message.js' // 提示信息\r\nimport { Common, system } from '@/api'\r\nconst { queryTree, selectClassify, queryMsg, noticeModify } = system.SysMes\r\nconst { readNum } = system.SysQuery\r\nconst { Approve } = Common.SysApproval\r\nconst { updateMessage } = Common.DataAuditing\r\nlet timout1\r\nlet timout2\r\n\r\nexport default {\r\n  name: 'SystemMsg',\r\n  components: {\r\n    SunNoticeDialog,\r\n    Print,\r\n    SunOperDetailDialog,\r\n    SunFlowDialog\r\n  },\r\n  mixins: [ResizeMixin],\r\n  data() {\r\n    return {\r\n      defaultActive: '0',\r\n      menus: [], // 左侧菜单数组\r\n      dealArr: [], // 左侧菜单消息全部/待处理条数\r\n      activeMenu: {}, // 当前点击菜单\r\n      config: config(this),\r\n      defaultForm: { msg_content: '', deal_state: '' },\r\n      btnAll: {\r\n        // 当前页需要配置权限的按钮  权限获取\r\n        btnQuery: true\r\n      },\r\n      dialogBtnDatas: {\r\n        btnPass: {\r\n          show: true\r\n        },\r\n        btnRefuse: {\r\n          show: true\r\n        }\r\n      },\r\n      listLoading: false,\r\n      table: {\r\n        // 表格配置\r\n        tableColumns: configTable(), // 表头配置\r\n        ref: 'tableRef',\r\n        selection: false, // 复选\r\n        indexNumber: true, // 序号\r\n        loading: false,\r\n        componentProps: {\r\n          data: [], // 表格数据\r\n          height: '0px',\r\n          formRow: 0 // 表单行数\r\n        },\r\n        pageList: {\r\n          totalNum: 0,\r\n          currentPage: 1, // 当前页\r\n          pageSize: this.$store.getters.pageSize // 当前页显示条数\r\n        },\r\n        currentRow: [] // 选中行\r\n      },\r\n      dialog: {\r\n        visible: false, // 开启/关闭弹窗\r\n        btnCancle: false, // 取消按钮\r\n        btnSubmit: false, // 确定按钮\r\n        componentProps: {\r\n          // 弹出框配置属性\r\n          top: '5vh',\r\n          title: '公告审批',\r\n          width: '74.8rem' // 当前弹出框宽度\r\n        },\r\n        btnPrintVis: true, // 打印按钮  默认显示\r\n        btnAgreeVis: false, // 同意、拒绝按钮隐藏\r\n        noticeConfig: {\r\n          imgSrc: require('@/assets/img/other/notice_images/noticeBackground.png'), // 背景图片加载\r\n          title: '标题', // 公告标题\r\n          info: [], // 发布机构、发布者名称、发布时间\r\n          readNum: '', // 阅读量\r\n          content: '', // 公告正文\r\n          files: []\r\n        }\r\n      },\r\n      dialogForm: {\r\n        // 公告弹窗拒绝通过说明\r\n        visible: false,\r\n        // oprate: 'refuse', // 弹窗标识\r\n        componentProps: {\r\n          // 弹出框属性\r\n          title: '拒绝说明', // 弹出框标题\r\n          width: '50rem' // 当前弹出框宽度 默认80%\r\n        },\r\n        form: {\r\n          // 表单属性\r\n          config: formConfig(this), // 表单项配置\r\n          defaultForm: {\r\n            remark: ''\r\n          }, // 默认值配置\r\n          labelWidth: '10rem' // 当前表单标签宽度配置\r\n        }\r\n      },\r\n      selectClass: '', // 消息类型参数\r\n      msgTotalNum: '', // 全部消息\r\n      msgUnReadNum: '', // 未处理消息\r\n      msgReadNum: '', // 已处理消息\r\n      nodealStatus: {\r\n        color: 'red' // 未处理消息颜色\r\n      },\r\n      dealStatus: {\r\n        color: '#646266' // 已处理消息颜色\r\n      },\r\n      dialogOper: {\r\n        // 流程事项-操作申请弹框数据\r\n        visible: false,\r\n        componentProps: {\r\n          // 弹出框属性\r\n          title: '', // 弹出框标题\r\n          width: '80%' // 当前弹出框宽度 默认80%\r\n        },\r\n        queryType: '',\r\n        currentRow: [], // 选中行\r\n        tableData: {\r\n          // 操作详情弹窗---删除-表格\r\n          tableColumns: [], // 表头配置\r\n          selection: false, // 复选\r\n          indexNumber: true, // 序号\r\n          loading: false,\r\n          componentProps: {\r\n            data: [], // 表格数据\r\n            height: '300px',\r\n            formRow: 0 // 表单行数\r\n          },\r\n          pageList: {\r\n            totalNum: 0,\r\n            currentPage: 1, // 当前页\r\n            pageSize: this.$store.getters.pageSize // 当前页显示条数\r\n          }\r\n        },\r\n        desData: {}, // 操作详情表单value\r\n        defaultForm2: [], // 操作详情表单key\r\n        tableDetailData: [], // 表格处理明细数据\r\n        isSublicense: false, // 转授权操作标识\r\n        btnId: '' // 上传文件提交时判断是同意操作或不同意操作标志\r\n      },\r\n      flowConfig: {\r\n        // 流程模板调用弹窗\r\n        visible: false,\r\n        componentProps: {\r\n          // 弹出框属性\r\n          title: '', // 弹出框标题\r\n          top: '0px',\r\n          width: '100%' // 当前弹出框宽度 默认80%\r\n        },\r\n        module_id: '', // 模板id\r\n        task_id: '', // 流程id\r\n        childFlowUrl: 'static/html/flow/workflow.html' // 子工程流程页面url\r\n      },\r\n      height: ''\r\n    }\r\n  },\r\n  computed: {},\r\n  watch: {\r\n    loading(value) {\r\n      this.listLoading = this.loading\r\n    },\r\n    'activeMenu.message_type': {\r\n      // 打开、关闭弹出框\r\n      handler(val) {\r\n        // this.defaultForm.deal_state = ''//菜单发生变化 处理状态会置空  根据需求选择清空或者不清空\r\n        if (val === '待处理' || val === '已处理') {\r\n          this.config.deal_state.hidden = true\r\n          this.defaultForm.deal_state = ''\r\n        } else {\r\n          this.config.deal_state.hidden = false\r\n        }\r\n      },\r\n      deep: true\r\n    }\r\n  },\r\n  created() {},\r\n  beforeMount() {\r\n    window.addEventListener('resize', this.getHeight)\r\n  },\r\n  mounted() {\r\n    // 操作流程处理完毕 更新未处理列表\r\n    this.initMenu()\r\n    this.$bus.$on('operApproveUpdate', (data) => {\r\n      if (data) {\r\n        this.initMenu()\r\n      }\r\n    })\r\n    // 转授权流程、基线流程模板调用处理完毕 更新未处理列表\r\n    this.$bus.$on('moudleSubUpdate', (data) => {\r\n      if (data) {\r\n        this.initMenu()\r\n      }\r\n    })\r\n    // 查看公告\r\n    // this.$bus.$on('noticeUpdate', (data) => {\r\n    //   if (data) {\r\n    //     this.initMenu()\r\n    //   }\r\n    // })\r\n    // 公告流程审批 重新查询系统消息页面公告\r\n    this.$bus.$on('noticeAproveUpdate', (data) => {\r\n      if (data) {\r\n        this.initMenu()\r\n      }\r\n    })\r\n    // 待办事项 查看待办事项详情\r\n    this.$bus.$on('jumpRouter', (data) => {\r\n      if (commonBlank(this.menus)) {\r\n        const msg = {\r\n          parameterList: []\r\n        }\r\n        queryTree(msg).then((res) => {\r\n          const { returnList } = res.retMap\r\n          for (const [index, item] of returnList.entries()) {\r\n            if (item.message_type === data) {\r\n              this.defaultActive = index.toString()\r\n            }\r\n          }\r\n        })\r\n      } else {\r\n        for (const [index, item] of this.menus.entries()) {\r\n          if (item.message_type === data) {\r\n            this.defaultActive = index.toString()\r\n          }\r\n        }\r\n      }\r\n    })\r\n  },\r\n  beforeDestroy() {\r\n    // 组件销毁之前，清除事件总线\r\n    this.$bus.$off('operApproveUpdate')\r\n    this.$bus.$off('noticeUpdate')\r\n    this.$bus.$off('noticeAproveUpdate')\r\n    this.$bus.$off('moudleSubUpdate')\r\n    this.$bus.$off('jumpRouter')\r\n    window.removeEventListener('resize', this.getHeight)\r\n    clearTimeout(timout1)\r\n    clearTimeout(timout2)\r\n  },\r\n  methods: {\r\n    /**\r\n     * 计算表格高度\r\n     * 动态获取角色表格的高度 */\r\n    getHeight() {\r\n      // 盒子总高度-表单高度-152(标题60px,页码52px,上下padding40px,放大缩小误差5px)\r\n      this.table.componentProps.height =\r\n        this.$refs['box-ref'].clientHeight -\r\n        this.$refs['moduleForm'].$el.clientHeight -\r\n        157 +\r\n        'px'\r\n    },\r\n    /**\r\n     *左侧菜单点击事件 */\r\n    handleSelect(key) {\r\n      this.activeMenu = this.menus[key]\r\n      this.queryMenu()\r\n    },\r\n    /**\r\n     *左侧菜单初始化 */\r\n    initMenu() {\r\n      const msg = {\r\n        parameterList: []\r\n      }\r\n      queryTree(msg).then((res) => {\r\n        const { returnList } = res.retMap\r\n        this.menus = returnList\r\n        this.$nextTick(() => {\r\n          this.handleSelect(0)\r\n          // this.queryMenu()\r\n        })\r\n      })\r\n    },\r\n    /**\r\n     * 按钮：查询*/\r\n    queryMenu() {\r\n      // this.table.loading = !this.table.loading\r\n      this.showLoading(true)\r\n      // 先进行类型查询请求    请求回来的数据作为参数进行消息列表查询参数\r\n      const msg = {\r\n        parameterList: [],\r\n        content_title: this.activeMenu.message_type // 当前点击菜单名称\r\n      }\r\n      selectClassify(msg).then((res) => {\r\n        this.selectClass = res.retMap.classify\r\n        if (res.retCode === '200') {\r\n          const msg2 = {\r\n            parameterList: [{}],\r\n            currentPage: this.table.pageList.currentPage,\r\n            pageSize: this.table.pageList.pageSize,\r\n            user_no: this.$store.getters.userNo,\r\n            organ_no: this.$store.getters.organNo,\r\n            organ_level: this.$store.getters.organLevel,\r\n            role_no: this.$store.getters.roleNo,\r\n            msg_type:\r\n              this.activeMenu.message_type === '全部' ||\r\n              this.activeMenu.message_type === '待处理' ||\r\n              this.activeMenu.message_type === '已处理'\r\n                ? ''\r\n                : this.activeMenu.field_value,\r\n            classify: this.selectClass,\r\n            msg_content: this.defaultForm.msg_content\r\n              ? this.defaultForm.msg_content\r\n              : '', // 表单消息内容\r\n            deal_state: this.defaultForm.deal_state\r\n              ? this.defaultForm.deal_state\r\n              : this.activeMenu.message_type === '全部'\r\n                ? ''\r\n                : this.activeMenu.message_type === '待处理'\r\n                  ? 0\r\n                  : this.activeMenu.message_type === '已处理'\r\n                    ? 1\r\n                    : '' // 处理状态 已处理1  待处理0 其他菜单''  menu默认选中全部\r\n          }\r\n          // 查询\r\n          queryMsg(msg2).then((res) => {\r\n            // 'readNum':0,//已读消息条数\r\n            timout1 = setTimeout(() => {\r\n              // this.table.loading = !this.table.loading\r\n              this.getHeight()\r\n            }, 700)\r\n            const {\r\n              list,\r\n              totalNum,\r\n              currentPage,\r\n              numlist,\r\n              msgtotalNum, // 全部\r\n              unReadNum, // 未处理\r\n              readNum // 已处理\r\n            } = res.retMap\r\n            this.msgTotalNum = msgtotalNum // 全部\r\n            this.msgUnReadNum = unReadNum // 未处理\r\n            this.msgReadNum = readNum // 已处理\r\n            this.table.componentProps.data = list // table表格数据\r\n            // 左侧菜单数据处理\r\n            const msgArr = []\r\n            numlist.forEach((item) => {\r\n              const obj = item.replace(/\\\\|{|}/g, '') // 处理后端返还数据\r\n              msgArr.push(...JSON.parse(`[{${obj}}]`))\r\n            })\r\n            this.dealArr = msgArr\r\n            this.menus.forEach((item, index) => {\r\n              const newtep = {\r\n                allNum: this.dealArr[index].allNum, // 所有消息\r\n                unDealNum: this.dealArr[index].unDealNum // 未读消息\r\n              }\r\n              this.$set(item, 'allNum', newtep.allNum)\r\n              this.$set(item, 'unDealNum', newtep.unDealNum)\r\n            })\r\n            // table表格数据处理\r\n            this.table.componentProps.data.forEach((key) => {\r\n              // 1.处理人存在且包括当前用户,处理状态为 1-已处理 处理时间为对应的处理时间;2.否则，处理状态为0-未处理，处理时间为空\r\n              if (\r\n                !commonBlank(key.dealed_user) &&\r\n                key.dealed_user\r\n                  .split(',')\r\n                  .indexOf(this.$store.getters.userNo) !== -1\r\n              ) {\r\n                // 处理状态\r\n                key.deal_state = '1'\r\n                // 处理时间\r\n                key.dealed_user.split(',').forEach((item, index) => {\r\n                  if (item === this.$store.getters.userNo) {\r\n                    key.dealed_time = key.dealed_time.split(',')[index]\r\n                  }\r\n                })\r\n              } else {\r\n                // 处理状态\r\n                key.deal_state = '0'\r\n                // 处理时间\r\n                key.dealed_time = ''\r\n              }\r\n            })\r\n            this.table.pageList.totalNum = totalNum\r\n            this.table.pageList.currentPage = currentPage\r\n            this.showLoading(false)\r\n          })\r\n        }\r\n      })\r\n    },\r\n    /**\r\n     * 消息内容--操作详情\r\n     * @param row 当前行数据\r\n     */\r\n    async contentClick(row) {\r\n      this.table.currentRow = row\r\n      // 判断消息类型\r\n      if (row.msg_type === '012') {\r\n        // 消息类型为公告\r\n        // 清空每次弹窗数据\r\n        this.dialog.noticeConfig.info = []\r\n        this.dialog.visible = true\r\n        this.$nextTick(() => {\r\n          // 弹窗加载完毕后赋值 公告内容等\r\n          this.dialog.noticeConfig.title = row.msg_parameter.notice_title // 标题\r\n          const info = []\r\n          const infoObj = {\r\n            publish_organ: row.create_organ,\r\n            publish_user: row.create_user,\r\n            publish_time: row.create_time\r\n          }\r\n          info.push(infoObj)\r\n          this.dialog.noticeConfig.info = info\r\n          this.dialog.noticeConfig.content = row.msg_parameter.notice_content // 公告内容\r\n          if (row.msg_parameter.file_url) {\r\n            this.dialog.noticeConfig.files = uploadFile(\r\n              row.msg_parameter.file_url\r\n            )\r\n          } else {\r\n            this.dialog.noticeConfig.files = []\r\n          }\r\n          // 查询阅读量\r\n          const readMsg = {\r\n            parameterList: [],\r\n            notice_id: row.msg_parameter.notice_id,\r\n            user_no: this.$store.getters.userNo,\r\n            organ_no: this.$store.getters.organNo,\r\n            publish_user: row.create_user\r\n          }\r\n          readNum(readMsg).then((response) => {\r\n            const { read_num } = response.retMap\r\n            this.dialog.noticeConfig.readNum = read_num\r\n            this.dialog.componentProps.title = '查看公告'\r\n            // 改变处理状态 0-未处理--->1-已处理在\r\n            const { notice_id } = row.msg_parameter\r\n            let paramJson = {}\r\n            if (commonBlank(row.msg_no)) {\r\n              paramJson = {\r\n                notice_id: row.notice_id,\r\n                notice_title: row.notice_title,\r\n                notice_content: row.notice_content,\r\n                file_url: row.file_url\r\n              }\r\n            }\r\n            if (row.deal_state === '0') {\r\n              // 如果是未处理状态  点击后更新阅读状态，否则不需要更改阅读状态\r\n              this.firstPage(notice_id, row.msg_no, paramJson)\r\n            }\r\n          })\r\n        })\r\n      } else if (row.msg_type === '011') {\r\n        // 消息类型为流程事项\r\n        if (row.hasOwnProperty.call('value')) {\r\n          // 右下角打开\r\n          const flowValue = JSON.parse(row.value)\r\n          for (const key in flowValue) {\r\n            row[key] = flowValue[key]\r\n          }\r\n          row.value = ''\r\n        }\r\n        // 获取任务节点路径\r\n        const rowData = await getTaskNodePath(row)\r\n        if (commonBlank(rowData)) {\r\n          return\r\n        }\r\n        // 走流程插件\r\n        if (rowData.msg_parameter.general_flag === '1') {\r\n          const flowData = {\r\n            ...row.msg_parameter,\r\n            // 添加流程类型属性  // 未审批（正常加载）\r\n            flow_type:\r\n              rowData.deal_state === '0' ? 'dealFlowMsg' : 'showFlowMsg' // flow_type = 'dealFlowMsg' // 处理流程系统消息 flow_type = 'showFlowMsg' // 查看流程系统消息\r\n          }\r\n          // 点击时将数据存储到vuex里\r\n          this.$store.commit('common/SET_FLOW_DATA', flowData) // 流程详情参数\r\n          // 打开流程界面\r\n          this.moudleOpen(row)\r\n        } else {\r\n          // 非流程插件审批/查看\r\n          // 如果是公告流程审批且是未处理状态，公告弹窗通过、拒绝按钮显示\r\n          if (row.deal_state === '0') {\r\n            this.dialog.btnVisible = true\r\n          }\r\n          const detailData = await openTaskPageByNodePath(\r\n            rowData,\r\n            rowData.deal_state\r\n          )\r\n          // 公告流程事项处理\r\n          if (\r\n            row.node_module_path ===\r\n            'static/js/action/system/noticeManage/noticeDialogApprove.js'\r\n          ) {\r\n            this.table.currentRow = detailData[0] // 公告流程审批数据进行备份\r\n            if (row.deal_state === '0') {\r\n              // 未处理显示同意不同意按钮   不显示打印按钮\r\n              this.dialog.btnPrintVis = false\r\n              this.dialog.btnAgreeVis = true\r\n            } else {\r\n              this.dialog.btnPrintVis = true\r\n              this.dialog.btnAgreeVis = false\r\n            }\r\n            this.dialog.visible = true // 公告流程处理弹窗\r\n            this.$nextTick(() => {\r\n              this.dialog.componentProps.title = '公告审批'\r\n              this.dialog.noticeConfig.title = detailData[0].notice_title // 标题\r\n              this.dialog.noticeConfig.info = detailData\r\n              this.dialog.noticeConfig.content = detailData[0].notice_content // 公告内容\r\n              if (detailData[0].file_url) {\r\n                this.dialog.noticeConfig.files = uploadFile(\r\n                  detailData[0].file_url\r\n                )\r\n              } else {\r\n                this.dialog.noticeConfig.files = []\r\n              }\r\n            })\r\n          } else if (\r\n            row.node_module_path ===\r\n            'static/js/action/index/author/authorRequestDialog.js'\r\n          ) {\r\n            // 转授权审批\r\n            if (commonBlank(detailData)) {\r\n              commonMsgInfo('查询详情数据为空！', this)\r\n            } else {\r\n              // 转授权模板\r\n              this.changeOperVisible(true)\r\n              this.dialogOper.currentRow = detailData[0]\r\n              this.dialogOper.queryType = detailData[1] // 查询类型0-我的申请 1-我的代办 2-我的已办\r\n              this.dialogOper.tableDetailData = detailData[3]\r\n              this.dialogOper.isSublicense = detailData[4]\r\n            }\r\n          } else {\r\n            // 流程事项操作申请\r\n            if (!commonBlank(detailData) && detailData[2] === true) {\r\n              // haveRight 为true\r\n              this.changeOperVisible(true)\r\n              this.dialogOper.currentRow = detailData[0]\r\n              this.dialogOper.queryType = detailData[1] // 查询类型0-我的申请 1-我的代办 2-我的已办\r\n              this.dialogOper.tableDetailData = detailData[3]\r\n            } else if (commonBlank(detailData)) {\r\n              commonMsgInfo('查询详情数据为空！', this)\r\n            } else if (!commonBlank(detailData) && !detailData[2]) {\r\n              // 属于openTaskPageByNodePath 中else的一种情况\r\n              return\r\n            }\r\n          }\r\n        }\r\n      } else if (row.msg_type === '169') {\r\n        this.dialog.componentProps.title = '查看'\r\n        // 清空每次弹窗数据\r\n        this.dialog.noticeConfig.info = []\r\n        this.dialog.visible = true\r\n        this.$nextTick(() => {\r\n          // 弹窗加载完毕后赋值\r\n          this.dialog.noticeConfig.title = '人力预警信息' // 标题\r\n          this.dialog.noticeConfig.imgSrc = ''\r\n          this.dialog.noticeConfig.readNum = 0\r\n          const info = []\r\n          const infoObj = {\r\n            publish_organ: row.create_organ,\r\n            publish_user: row.create_user,\r\n            publish_time: row.create_time\r\n          }\r\n          info.push(infoObj)\r\n          this.dialog.noticeConfig.info = info\r\n          this.dialog.noticeConfig.content = row.msg_parameter.msg // 公告内容\r\n          if (row.msg_parameter.file_url) {\r\n            this.dialog.noticeConfig.files = uploadFile(\r\n              row.msg_parameter.file_url\r\n            )\r\n          } else {\r\n            this.dialog.noticeConfig.files = []\r\n          }\r\n        })\r\n      } else if (row.msg_type === '168') {\r\n        // 可视化预警跳转预警记录页面\r\n        const router = this.$store.getters.permission_routes // 所有菜单\r\n        // 获取可视化菜单id\r\n        const kshMenuId = this.$store.getters.menuArr.find((item) => {\r\n          return item.menu_name.indexOf('可视化平台') > -1\r\n        })\r\n        // 获取预警记录菜单id\r\n        const yjMenuId = this.$store.getters.menuArr.find((item) => {\r\n          return item.menu_name.indexOf('预警记录') > -1\r\n        })\r\n        // 可视化动态菜单路由\r\n        const unifyR = router.find((item) => {\r\n          return item.menu_id === kshMenuId.menu_id // 可视化菜单id  （父id）\r\n        })\r\n        // 预警记录动态菜单路由\r\n        const noticeR = unifyR.children.find((item) => {\r\n          return item.menu_id === yjMenuId.menu_id // 预警记录id\r\n        })\r\n        this.$router.push(unifyR.path + '/' + noticeR.path)\r\n      } else {\r\n        if (row.deal_state === '0') {\r\n          this.transformStatus(row)\r\n        }\r\n      }\r\n    },\r\n    /**\r\n     * 公共方法：未处理->已处理\r\n     * @param row 当前处理行\r\n     * */\r\n    transformStatus(row) {\r\n      const msg = {\r\n        parameterList: [{}],\r\n        msg_no: row.msg_no,\r\n        msg_type: row.msg_type\r\n      }\r\n      updateMessage(msg).then((response) => {\r\n        commonMsgSuccess('处理成功', this)\r\n        this.queryMenu()\r\n        this.$nextTick(() => {\r\n          this.$bus.$emit('noticeUpdate', true) // true 更新系统消息的数据/更新系统消息右下角弹窗数据\r\n        })\r\n      })\r\n    },\r\n    /**\r\n     * 流程模板调用方法\r\n     */\r\n    moudleOpen(row) {\r\n      this.flowConfig.componentProps.title = row.msg_content // 模板标题和弹窗标题\r\n      this.flowConfig.module_id = row.msg_parameter.module_id // 模板ID\r\n      this.flowConfig.task_id = row.msg_parameter.item_id // 流程ID\r\n      this.flowConfig.visible = true\r\n    },\r\n    // 流程弹窗关闭\r\n    dialogFlowClose(param) {\r\n      // 需要释放任务\r\n      if (param) {\r\n        const mapFrame = this.$refs['flowDialog'].$refs[param]\r\n        const iframeWin = mapFrame.contentWindow\r\n\r\n        iframeWin.postMessage(\r\n          {\r\n            type: 'dopUnify',\r\n            projectName: 'resetFlow',\r\n            task_id: this.flowConfig.task_id\r\n          },\r\n          process.env.NODE_ENV === 'development' ? '*' : window.location.origin\r\n        )\r\n      }\r\n      timout2 = setTimeout(() => {\r\n        this.$store.state.common.flow.module_id = '' // 置空流程id\r\n        this.$store.state.common.flow.workflow_dataStr.flow_type = 'add' // 重流程类型为新增\r\n        this.flowConfig.visible = false\r\n      }, 300)\r\n    },\r\n    /**\r\n     * 公告信息: 更改阅读状态\r\n     * @param notice_id:公告id\r\n     * @param msg_no:系统消息编号\r\n     * @param paramJson：系统消息参数\r\n     * */\r\n    firstPage(notice_id, msg_no, paramJson) {\r\n      const read_time = dateNowFormat10() // 当前时间的十位数格式\r\n      const msg = {\r\n        notice_id: notice_id,\r\n        user_no: this.$store.getters.userNo,\r\n        organ_no: this.$store.getters.organNo,\r\n        read_time: read_time,\r\n        msg_no: msg_no,\r\n        parpamData: paramJson\r\n      }\r\n      noticeModify(msg).then((response) => {\r\n        this.queryMenu()\r\n        this.$nextTick(() => {\r\n          this.$bus.$emit('noticeUpdate', true) // true 更新系统消息的数据/更新系统消息右下角弹窗数据\r\n        })\r\n      })\r\n    },\r\n    /**\r\n     * btn - 通过\r\n     * @param row 当前行公告流程数据*/\r\n    handlePass() {\r\n      commonMsgConfirm('确认通过审批？', this, (param) => {\r\n        if (param) {\r\n          const row = this.table.currentRow\r\n          const json = {\r\n            notice_id: row.notice_id,\r\n            notice_title: row.notice_title,\r\n            notice_content: row.notice_content,\r\n            msg_type: row.msg_type,\r\n            notice_level: row.notice_level,\r\n            organs: row.organs,\r\n            roles: row.roles,\r\n            users: row.users,\r\n            file_url: row.file_url,\r\n            publish_organ: row.publish_organ,\r\n            publish_user: row.publish_user,\r\n            publish_time: row.publish_time,\r\n            item_id: row.item_id,\r\n            inst_id: row.inst_id\r\n          }\r\n          const msg = {\r\n            parameterList: [json],\r\n            user_no: this.$store.getters.userNo,\r\n            remark: this.dialogForm.form.defaultForm.remark, // 表单数据：拒绝说明\r\n            type: '1', // 通过的标识\r\n            oper_type: dictionaryGet('OPERATE_OTHER')\r\n          }\r\n          Approve(msg).then((response) => {\r\n            commonMsgSuccess(response.retMsg, this)\r\n            this.queryMenu()\r\n            this.changeVisible(false) // 公告审批弹框关闭\r\n            this.$bus.$emit('noticeAproveUpdate', true)\r\n          })\r\n        }\r\n      })\r\n    },\r\n    /**\r\n     * btn - 拒绝*/\r\n    handleRefuse() {\r\n      this.changeVisibleForm(true)\r\n    },\r\n    /**\r\n     * btn - 拒绝\r\n     * @param row 当前行公告流程数据\r\n     * */\r\n    dialogRefuseSubmit() {\r\n      // 发送流程终止请求\r\n      commonMsgConfirm('确认拒绝审批？', this, (flag) => {\r\n        if (flag) {\r\n          const row = this.table.currentRow\r\n          const json = {\r\n            notice_id: row.notice_id,\r\n            notice_title: row.notice_title,\r\n            notice_content: row.notice_content,\r\n            msg_type: row.msg_type,\r\n            notice_level: row.notice_level,\r\n            organs: row.organs,\r\n            roles: row.roles,\r\n            users: row.users,\r\n            file_url: row.file_url,\r\n            publish_organ: row.publish_organ,\r\n            publish_user: row.publish_user,\r\n            publish_time: row.publish_time,\r\n            item_id: row.item_id,\r\n            inst_id: row.inst_id\r\n          }\r\n          const msg = {\r\n            parameterList: [json],\r\n            user_no: this.$store.getters.userNo,\r\n            remark: this.dialogForm.form.defaultForm.remark, // 表单数据：拒绝说明\r\n            type: '0', // 拒绝的标识\r\n            oper_type: dictionaryGet('OPERATE_OTHER')\r\n          }\r\n          Approve(msg).then((response) => {\r\n            commonMsgSuccess(response.retMsg, this)\r\n            this.queryMenu()\r\n            this.$bus.$emit('noticeAproveUpdate', true)\r\n            this.changeVisibleForm(false) // 拒绝说明弹框关闭\r\n            this.changeVisible(false) // 公告审批弹框关闭\r\n          })\r\n        }\r\n      })\r\n    },\r\n    /**\r\n     * 流程事项-操作申请-操作详情 - 弹窗关闭\r\n     * @param {Boolean} param 弹出框显示隐藏配置*/\r\n    changeOperVisible(param) {\r\n      this.dialogOper.visible = param\r\n    },\r\n    /**\r\n     * 拒绝说明弹出框 - 关闭\r\n     * @param {Boolean} param 弹出框显示隐藏配置*/\r\n    changeVisibleForm(param) {\r\n      this.dialogForm.visible = param\r\n    },\r\n    /* 公告弹出框\r\n     *打印 */\r\n    handlePrint() {\r\n      this.$print(this.$refs.printRef)\r\n    },\r\n    /**\r\n     * 公告弹出框 - 关闭\r\n     * @param {Boolean} param 弹出框显示隐藏配置*/\r\n    changeVisible(param) {\r\n      this.dialog.visible = param\r\n    },\r\n    /**\r\n     * 公告弹出框 - 确认*/\r\n    dialogSumbit() {\r\n      this.changeVisible(false)\r\n    },\r\n    /**\r\n     *页码更新 */\r\n    getList(pageParam) {\r\n      const { currentPage, pageSize } = pageParam\r\n      this.table.pageList.pageSize = pageSize\r\n      this.table.pageList.currentPage = currentPage\r\n      this.queryMenu()\r\n    },\r\n    /**\r\n     * 加载中动画配置*/\r\n    showLoading() {\r\n      this.listLoading = !this.listLoading\r\n    }\r\n  }\r\n}\r\n</script>\r\n<style scoped lang=\"scss\">\r\n.commonHeight {\r\n  height: 100%;\r\n}\r\n.msgContent {\r\n  display: inline-block;\r\n  color: rgba(0, 0, 255, 0.662);\r\n  cursor: pointer;\r\n  &:hover {\r\n    color: #e9313e;\r\n  }\r\n}\r\n// 打印按钮\r\n.printBtn {\r\n  position: absolute;\r\n  right: 2%;\r\n  top: 20%;\r\n  .link-svg {\r\n    cursor: pointer;\r\n    width: 1.4rem;\r\n    height: 1.4rem;\r\n    fill: currentColor;\r\n    overflow: hidden;\r\n    float: left;\r\n  }\r\n}\r\n</style>\r\n"]}]}