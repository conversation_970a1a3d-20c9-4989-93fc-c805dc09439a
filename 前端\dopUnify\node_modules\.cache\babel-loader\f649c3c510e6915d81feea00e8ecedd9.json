{"remainingRequest": "E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1\\数字运营平台-统一门户工程\\dopUnify\\node_modules\\thread-loader\\dist\\cjs.js!E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1\\数字运营平台-统一门户工程\\dopUnify\\node_modules\\babel-loader\\lib\\index.js!E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1\\数字运营平台-统一门户工程\\dopUnify\\src\\store\\modules\\common.js", "dependencies": [{"path": "E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1\\数字运营平台-统一门户工程\\dopUnify\\src\\store\\modules\\common.js", "mtime": 1719452474986}, {"path": "E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1\\数字运营平台-统一门户工程\\dopUnify\\babel.config.js", "mtime": 1716875191548}, {"path": "E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1\\数字运营平台-统一门户工程\\dopUnify\\.babelrc", "mtime": 1716875191502}, {"path": "E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1\\数字运营平台-统一门户工程\\dopUnify\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1\\数字运营平台-统一门户工程\\dopUnify\\node_modules\\thread-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1\\数字运营平台-统一门户工程\\dopUnify\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:aW1wb3J0IF9zbGljZWRUb0FycmF5IGZyb20gIkU6LzFfUHJvamVjdC9YWURfUHJvamVjdC9kb3AtNC4wL2RvcC00LjEvXHU2NTcwXHU1QjU3XHU4RkQwXHU4NDI1XHU1RTczXHU1M0YwLVx1N0VERlx1NEUwMFx1OTVFOFx1NjIzN1x1NURFNVx1N0EwQi9kb3BVbmlmeS9ub2RlX21vZHVsZXMvQGJhYmVsL3J1bnRpbWUvaGVscGVycy9lc20vc2xpY2VkVG9BcnJheS5qcyI7CmltcG9ydCBfdG9Db25zdW1hYmxlQXJyYXkgZnJvbSAiRTovMV9Qcm9qZWN0L1hZRF9Qcm9qZWN0L2RvcC00LjAvZG9wLTQuMS9cdTY1NzBcdTVCNTdcdThGRDBcdTg0MjVcdTVFNzNcdTUzRjAtXHU3RURGXHU0RTAwXHU5NUU4XHU2MjM3XHU1REU1XHU3QTBCL2RvcFVuaWZ5L25vZGVfbW9kdWxlcy9AYmFiZWwvcnVudGltZS9oZWxwZXJzL2VzbS90b0NvbnN1bWFibGVBcnJheS5qcyI7CmltcG9ydCBfcmVnZW5lcmF0b3JSdW50aW1lIGZyb20gIkU6LzFfUHJvamVjdC9YWURfUHJvamVjdC9kb3AtNC4wL2RvcC00LjEvXHU2NTcwXHU1QjU3XHU4RkQwXHU4NDI1XHU1RTczXHU1M0YwLVx1N0VERlx1NEUwMFx1OTVFOFx1NjIzN1x1NURFNVx1N0EwQi9kb3BVbmlmeS9ub2RlX21vZHVsZXMvQGJhYmVsL3J1bnRpbWUvaGVscGVycy9lc20vcmVnZW5lcmF0b3JSdW50aW1lLmpzIjsKaW1wb3J0IF9hc3luY1RvR2VuZXJhdG9yIGZyb20gIkU6LzFfUHJvamVjdC9YWURfUHJvamVjdC9kb3AtNC4wL2RvcC00LjEvXHU2NTcwXHU1QjU3XHU4RkQwXHU4NDI1XHU1RTczXHU1M0YwLVx1N0VERlx1NEUwMFx1OTVFOFx1NjIzN1x1NURFNVx1N0EwQi9kb3BVbmlmeS9ub2RlX21vZHVsZXMvQGJhYmVsL3J1bnRpbWUvaGVscGVycy9lc20vYXN5bmNUb0dlbmVyYXRvci5qcyI7CmltcG9ydCBfY3JlYXRlRm9yT2ZJdGVyYXRvckhlbHBlciBmcm9tICJFOi8xX1Byb2plY3QvWFlEX1Byb2plY3QvZG9wLTQuMC9kb3AtNC4xL1x1NjU3MFx1NUI1N1x1OEZEMFx1ODQyNVx1NUU3M1x1NTNGMC1cdTdFREZcdTRFMDBcdTk1RThcdTYyMzdcdTVERTVcdTdBMEIvZG9wVW5pZnkvbm9kZV9tb2R1bGVzL0BiYWJlbC9ydW50aW1lL2hlbHBlcnMvZXNtL2NyZWF0ZUZvck9mSXRlcmF0b3JIZWxwZXIuanMiOwppbXBvcnQgX29iamVjdFNwcmVhZCBmcm9tICJFOi8xX1Byb2plY3QvWFlEX1Byb2plY3QvZG9wLTQuMC9kb3AtNC4xL1x1NjU3MFx1NUI1N1x1OEZEMFx1ODQyNVx1NUU3M1x1NTNGMC1cdTdFREZcdTRFMDBcdTk1RThcdTYyMzdcdTVERTVcdTdBMEIvZG9wVW5pZnkvbm9kZV9tb2R1bGVzL0BiYWJlbC9ydW50aW1lL2hlbHBlcnMvZXNtL29iamVjdFNwcmVhZDIuanMiOwppbXBvcnQgImNvcmUtanMvbW9kdWxlcy9lcy5mdW5jdGlvbi5uYW1lLmpzIjsKaW1wb3J0ICJjb3JlLWpzL21vZHVsZXMvZXMuanNvbi5zdHJpbmdpZnkuanMiOwppbXBvcnQgImNvcmUtanMvbW9kdWxlcy9lcy5hcnJheS5maWx0ZXIuanMiOwppbXBvcnQgImNvcmUtanMvbW9kdWxlcy9lcy5vYmplY3QudG8tc3RyaW5nLmpzIjsKaW1wb3J0ICJjb3JlLWpzL21vZHVsZXMvd2ViLmRvbS1jb2xsZWN0aW9ucy5mb3ItZWFjaC5qcyI7CmltcG9ydCAiY29yZS1qcy9tb2R1bGVzL2VzLnJlZ2V4cC50by1zdHJpbmcuanMiOwppbXBvcnQgImNvcmUtanMvbW9kdWxlcy9lcy5hcnJheS5tYXAuanMiOwppbXBvcnQgImNvcmUtanMvbW9kdWxlcy9lcy5hcnJheS5jb25jYXQuanMiOwppbXBvcnQgImNvcmUtanMvbW9kdWxlcy93ZWIuZG9tLWNvbGxlY3Rpb25zLml0ZXJhdG9yLmpzIjsKaW1wb3J0ICJjb3JlLWpzL21vZHVsZXMvZXMub2JqZWN0LmtleXMuanMiOwovKioKICog5YWs5YWx6YWN572uCiAqLwppbXBvcnQgeyBMb2FkaW5nIH0gZnJvbSAnZWxlbWVudC11aSc7CmltcG9ydCB7IENvbW1vbiB9IGZyb20gJ0AvYXBpJzsKaW1wb3J0IHsgY29tbW9uQmxhbmssIGFycmF5VG9UcmVlTG9vcCB9IGZyb20gJ0AvdXRpbHMvY29tbW9uJzsKaW1wb3J0IHsgZmlsdGVyQXN5bmNSb3V0ZXMgfSBmcm9tICcuL3Blcm1pc3Npb24nOwppbXBvcnQgeyBnZXRNb2R1bGVWdWVCeU5hbWUgfSBmcm9tICdAL3V0aWxzL2V4dGVybmFsRGF0YS5qcyc7CmltcG9ydCBkZWZhdWx0U2V0dGluZ3MgZnJvbSAnQC9zZXR0aW5ncyc7CnZhciBzZXR0aW5nc05hbWUgPSBkZWZhdWx0U2V0dGluZ3MubmFtZTsKdmFyIG9yZ2FuVHJlZSA9IENvbW1vbi5vcmdhblRyZWUsCiAgZmllbGRDb250cm9sbGVyID0gQ29tbW9uLmZpZWxkQ29udHJvbGxlciwKICBleHRlbmRTb3VyY2UgPSBDb21tb24uZXh0ZW5kU291cmNlLAogIGdldEFsbFJvbGVzID0gQ29tbW9uLmdldEFsbFJvbGVzLAogIGdldEFsbFVzZXJzID0gQ29tbW9uLmdldEFsbFVzZXJzLAogIGNvbW1vbk1lbnUgPSBDb21tb24uY29tbW9uTWVudSwKICBleHRlbmRTb3VyY2VBcnIgPSBDb21tb24uZXh0ZW5kU291cmNlQXJyLAogIHF1ZXJ5UmlnaHRPcmdhbiA9IENvbW1vbi5xdWVyeVJpZ2h0T3JnYW4sCiAgX2luaXRTeXNQYXJhbSA9IENvbW1vbi5pbml0U3lzUGFyYW07CnZhciBzdGF0ZSA9IHsKICBpc1Nob3dTZWFyY2g6IGZhbHNlLAogIC8vIOaYr+WQpuaYvuekuumhtumDqOiPnOWNleaQnOe0ouahhgogIG9yZ2FuVHJlZU9iamVjdDoge30sCiAgLy8g5py65p6E5qCR5a+56LGh77yMa2V5OiDmnLrmnoTlj7cKICBvcmdhblRyZWU6IFtdLAogIC8vIOacuuaehOagkQogIHJpZ2h0T3JnYW5UcmVlOiBbXSwKICAvLyDmnYPpmZDmnLrmnoTmoJEKICByaWdodE9yZ2FuQXJyYXk6IFtdLAogIC8vIOadg+mZkOacuuaehOaVsOe7hAogIG9yZ2FuTGlzdDogW10sCiAgLy8g5py65p6E5qCR5pWw5o2u5pWw57uECiAgcm9sZUxpc3Q6IFtdLAogIC8vIOinkuiJsuetiee6p+aVsOe7hAogIHJvbGVMZXZlbERhdGE6IHt9LAogIC8vIOinkuiJsuetiee6p+WvueixoQogIHVzZXJMaXN0OiBbXSwKICAvLyDlhajooYznlKjmiLfkv6Hmga8KICBkaWN0aW9uYXJ5Q29uc3Q6IHt9LAogIC8vIOW4uOmHj+Wtl+WFuAogIGRpY3Rpb25hcnlMZXQ6IHt9LAogIC8vIOWQjuWPsOS8oOWFpeWtl+WFuAogIGRpY3Rpb25hcnlMZXRDaGlsZFA6IHt9LAogIC8vIOWtkOW3peeoi+Wtl+WFuAogIGRpY3Rpb25hcnlUcmVlOiBbXSwKICAvLyDlrZflhbjmoJEKICByb3V0ZVN5czoge30sCiAgLy8g6I+c5Y2V6YWN572uCiAgcGFnZVNpemU6IDEwLAogIC8vIOW9k+WJjemhtem7mOiupOadoeaVsAogIGV4dGVybmFsRGF0YToge30sCiAgLy8g5aSW6KGo5a2X5YW4CiAgZXh0ZXJuYWxEYXRhU291cmNlOiBbXSwKICAvLyDlpJbooajlrZflhbjmlbDnu4QKICBob21lTW91ZGxlczogW10sCiAgLy8g6buY6K6k5pi+56S65qih5Z2XCiAgaG9tZU1vdWRsZXNDaGVja2VkOiBbXSwKICAvLyDpgInmi6nmmL7npLrmqKHlnZcKICBob21lTW91ZGxlc0RlZmF1bHRDaGVja2VkOiBbXSwKICAvLyDnlKjmiLfmnKzouqvpu5jorqTnmoTmqKHlnZforr7nva4KICByb3V0ZXNQYXJlbnQ6IFtdLAogIC8vIOeItue6p+iPnOWNlQogIG1lbnVBcnI6IFtdLAogIC8vIOeItue6p+iPnOWNleaVsOe7hAogIG1lbnVPYmo6IHt9LAogIC8vIOeItue6p+iPnOWNleWvueixoSAvLyDnlKjkuo7oj5zljZXlm57mmL7lgLwKICBmbG93OiB7CiAgICAvLyDmtYHnqIsKICAgIG1vZHVsZV9pZDogJycsCiAgICB3b3JrZmxvd19kYXRhU3RyOiAnJywKICAgIC8vIOWtkOW3peeoi+a1geeoi+aooeadv+ebuOWFs+aVsOaNrgogICAgbWVudV9jb25kaXRpb246ICcnIC8vIOiPnOWNleadoeS7tgogIH0sCgogIHRpdGxlczoge30sCiAgLy8g5YWz6Zet5b2T5YmNdGFi6Lev55Sx5L+h5oGvCiAgc2F2ZURhdGE6IHsKICAgIC8vIOWtkOW3peeoi+S4reeahOaVsOaNruWtmOWCqAogICAgLy8gYW9zOiB7fQogIH0sCiAgLy8g5pWw5o2u5a2Y5YKoCiAgaW5pdFN5c1BhcmFtOiB7fSAvLyDliJ3lp4vljJbns7vnu5/lj4LmlbAKfTsKCi8vIOWtl+WFuOagkeagvOW8j+WMluaWueazlQpmdW5jdGlvbiBhcnJUb1RyZWVEaWN0aW9uYXJ5KG9iaikgewogIC8vIOi/meS4quaVsOaNruacieS4gOS4qumXrumimO+8jOWwseaXtuagueiKgueCueeahGZpZWxkX2lkIOWSjCAgcGFyZW50X2ZpZWxkIOS4gOagtyDpg73kuLowMDAwMDAKICB2YXIgbmV3T2JqID0ge307CiAgdmFyIG5ld0FyciA9IFtdOwogIHZhciBtYXBPYmogPSB7fTsKICBmb3IgKHZhciBpdGVtIGluIG9iaikgewogICAgdmFyIGFyck9iaiA9IEpTT04ucGFyc2UoSlNPTi5zdHJpbmdpZnkob2JqW2l0ZW1dLmZpbHRlcihmdW5jdGlvbiAoaXRlbSkgewogICAgICByZXR1cm4gaXRlbS5maWVsZF9pZCAhPT0gJzAwMDAwMCc7CiAgICB9KSkpOwogICAgYXJyT2JqLmZvckVhY2goZnVuY3Rpb24gKGl0ZW0pIHsKICAgICAgaXRlbS5jaGlsZHJlbiA9IFtdOwogICAgICBpdGVtLmlkID0gaXRlbS5maWVsZF9pZDsKICAgICAgaXRlbS5sYWJlbCA9IGl0ZW0uZmllbGRfaWQgKyAnLScgKyBpdGVtLmZpZWxkX25hbWU7CiAgICAgIG1hcE9ialtpdGVtLmZpZWxkX2lkXSA9IGl0ZW07CiAgICB9KTsKICAgIGFyck9iai5mb3JFYWNoKGZ1bmN0aW9uIChpdGVtKSB7CiAgICAgIHZhciBwYXJlbnQgPSBtYXBPYmpbaXRlbS5wYXJlbnRfZmllbGRdOwogICAgICBpZiAocGFyZW50KSB7CiAgICAgICAgcGFyZW50LmNoaWxkcmVuLnB1c2goaXRlbSk7CiAgICAgIH0gZWxzZSB7CiAgICAgICAgbmV3QXJyLnB1c2goaXRlbSk7CiAgICAgIH0KICAgIH0pOwogICAgbmV3T2JqW2l0ZW1dID0gbmV3QXJyOwogIH0KICByZXR1cm4gbmV3T2JqOwp9Ci8qKgogKiDojrflj5bmn5DkuKrnuqfliKvnmoTop5LoibIKICogQHBhcmFtIHtBcnJheX0gcm9sZUxpc3Qg5YWo6YOo6KeS6ImyCiAqIEBwYXJhbSB7TnVtYmVyfSBsZXZlbCDop5LoibLnrYnnuqcKICogQHJldHVybnMge09iamVjdH0g6L+U5Zue55u45bqU6KeS6Imy562J57qn55qE6KeS6Imy5a+56LGhCiAqLwpmdW5jdGlvbiBnZXRMZXZlbFJvbGUocm9sZUxpc3QsIGxldmVsKSB7CiAgdmFyIGxldmVsU3RyaW5nID0gbGV2ZWwudG9TdHJpbmcoKTsKICB2YXIgcm9sZUxldmVsRGF0YSA9IHJvbGVMaXN0LmZpbHRlcihmdW5jdGlvbiAoaXRlbSwgaW5kZXgpIHsKICAgIHJldHVybiBpdGVtLnJvbGVfbGV2ZWwgPT09IGxldmVsU3RyaW5nOwogIH0pOwogIHJldHVybiByb2xlTGV2ZWxEYXRhOwp9Ci8qKgogKiDpgJrov4drZXnvvIzmoLzlvI/ljJblpJbooajmlbDmja7mupAKICogQHBhcmFtIHtTdHJpbmd9IGtleSDlgLwKICovCi8vIGFzeW5jIGZ1bmN0aW9uIGNvbW1vbkFvc0Zvcm1hdEV4dGVyU291cmNlKGtleSkgewovLyAgIGlmICh0eXBlb2Yga2V5ID09PSAnc3RyaW5nJyAmJiBrZXkuY29uc3RydWN0b3IgPT09IFN0cmluZyAmJiBrZXkgIT09ICcnKSB7Ci8vICAgICBjb25zdCBtc2cgPSB7Ci8vICAgICAgIHBhcmFtZXRlckxpc3Q6IFtdLAovLyAgICAgICBvcGVyX3R5cGU6ICdleHRlcm5hbERhdGEnLAovLyAgICAgICBrZXk6IGtleQovLyAgICAgfQovLyAgICAgY29uc3QgZGF0YSA9IGF3YWl0IGV4dGVuZFNvdXJjZShtc2cpCi8vICAgICBjb25zdCBsaXN0ID0gZGF0YS5yZXRNYXAubGlzdAovLyAgICAgaWYgKGxpc3QpIHsKLy8gICAgICAgbGlzdC5mb3JFYWNoKChpdGVtKSA9PiB7Ci8vICAgICAgICAgaXRlbS5sYWJlbCA9IGl0ZW0udmFsdWUKLy8gICAgICAgICBpdGVtLnZhbHVlID0gaXRlbS5rZXkKLy8gICAgICAgfSkKLy8gICAgICAgcmV0dXJuIGxpc3QKLy8gICAgIH0gZWxzZSB7Ci8vICAgICAgIHJldHVybiBbXQovLyAgICAgfQovLyAgIH0gZWxzZSB7Ci8vICAgICBhbGVydCgn5aSW6KGo5a+55bqUa2V55pyJ6K+vJykKLy8gICB9Ci8vIH0KdmFyIG11dGF0aW9ucyA9IHsKICBTRVRfVFJFRTogZnVuY3Rpb24gU0VUX1RSRUUoc3RhdGUsIGRhdGEpIHsKICAgIHN0YXRlLm9yZ2FuVHJlZSA9IGRhdGE7CiAgfSwKICBTRVRfUklHSFRUUkVFOiBmdW5jdGlvbiBTRVRfUklHSFRUUkVFKHN0YXRlLCBkYXRhKSB7CiAgICBzdGF0ZS5yaWdodE9yZ2FuVHJlZSA9IGRhdGE7CiAgfSwKICBTRVRfUklHSFRBUlJBWTogZnVuY3Rpb24gU0VUX1JJR0hUQVJSQVkoc3RhdGUsIGRhdGEpIHsKICAgIHN0YXRlLnJpZ2h0T3JnYW5BcnJheSA9IGRhdGE7CiAgfSwKICBTRVRfVFJFRV9PQkpFQ1Q6IGZ1bmN0aW9uIFNFVF9UUkVFX09CSkVDVChzdGF0ZSwgZGF0YSkgewogICAgc3RhdGUub3JnYW5UcmVlT2JqZWN0ID0gZGF0YTsKICB9LAogIFNFVF9PUkdBTl9MSVNUOiBmdW5jdGlvbiBTRVRfT1JHQU5fTElTVChzdGF0ZSwgZGF0YSkgewogICAgc3RhdGUub3JnYW5MaXN0ID0gZGF0YTsKICB9LAogIFNFVF9ESUNUSU9OQVJZOiBmdW5jdGlvbiBTRVRfRElDVElPTkFSWShzdGF0ZSwgZGF0YSkgewogICAgc3RhdGUuZGljdGlvbmFyeUNvbnN0ID0gZGF0YTsKICB9LAogIFNFVF9ESUNUSU9OQVJZTEVUOiBmdW5jdGlvbiBTRVRfRElDVElPTkFSWUxFVChzdGF0ZSwgZGF0YSkgewogICAgc3RhdGUuZGljdGlvbmFyeUxldCA9IE9iamVjdC5hc3NpZ24oe30sIHN0YXRlLmRpY3Rpb25hcnlMZXQsIGRhdGEpOwogIH0sCiAgVGVzdDogZnVuY3Rpb24gVGVzdChzdGF0ZSwgZGF0YSkgewogICAgc3RhdGUudGl0bGVzID0gZGF0YTsKICB9LAogIEFERF9ESUNUSU9OQVJZTEVUOiBmdW5jdGlvbiBBRERfRElDVElPTkFSWUxFVChzdGF0ZSwgZGF0YSkgewogICAgc3RhdGUuZGljdGlvbmFyeUxldFtkYXRhLnN5c3RlbU5vXSA9IGRhdGEudmFsdWU7CiAgICAvLyBzdGF0ZS5kaWN0aW9uYXJ5TGV0W2RhdGEuc3lzdGVtTm9dID0gT2JqZWN0LmFzc2lnbih7fSwgZGF0YS52YWx1ZSkKICAgIC8vIGRlYnVnZ2VyCiAgICBzdGF0ZS5kaWN0aW9uYXJ5TGV0WydkaWN0aW9uYXJ5J11bZGF0YS5zeXN0ZW1Ob10gPSBkYXRhLmRpY3Rpb25hcnlbZGF0YS5zeXN0ZW1Ob107CiAgICB2YXIgZGljRGF0YSA9IGFyclRvVHJlZURpY3Rpb25hcnkoZGF0YS5kaWN0aW9uYXJ5KTsKICAgIGZvciAodmFyIGl0ZW0gaW4gZGF0YS5kaWN0aW9uYXJ5KSB7CiAgICAgIGlmICghY29tbW9uQmxhbmsoZGF0YS5kaWN0aW9uYXJ5W2l0ZW1dKSkgewogICAgICAgIHN0YXRlLmRpY3Rpb25hcnlUcmVlW2l0ZW1dID0gZGljRGF0YVtpdGVtXTsKICAgICAgfQogICAgfQogIH0sCiAgQUREX0RJQ1RJT05BUllMRVRfU1VCS0VZOiBmdW5jdGlvbiBBRERfRElDVElPTkFSWUxFVF9TVUJLRVkoc3RhdGUsIGRhdGEpIHsKICAgIGZvciAodmFyIGtleSBpbiBkYXRhKSB7CiAgICAgIGlmIChrZXkgIT09ICdkaWN0aW9uYXJ5JyAmJiBrZXkgIT09ICdmaWVsZFRpbWUnKSB7CiAgICAgICAgc3RhdGUuZGljdGlvbmFyeUxldFtrZXldID0gZGF0YVtrZXldOwogICAgICB9IGVsc2UgaWYgKGtleSA9PT0gJ2RpY3Rpb25hcnknKSB7CiAgICAgICAgc3RhdGUuZGljdGlvbmFyeUxldFtrZXldID0gZGF0YVtrZXldOwogICAgICAgIHZhciBkaWNEYXRhID0gYXJyVG9UcmVlRGljdGlvbmFyeShkYXRhW2tleV0pOwogICAgICAgIGZvciAodmFyIGl0ZW0gaW4gZGF0YVtrZXldKSB7CiAgICAgICAgICBpZiAoIWNvbW1vbkJsYW5rKGRhdGFba2V5XVtpdGVtXSkpIHsKICAgICAgICAgICAgc3RhdGUuZGljdGlvbmFyeVRyZWVbaXRlbV0gPSBkaWNEYXRhW2l0ZW1dOwogICAgICAgICAgfQogICAgICAgIH0KICAgICAgfSBlbHNlIGlmIChrZXkgPT09ICdmaWVsZFRpbWUnKSB7CiAgICAgICAgc3RhdGUuZGljdGlvbmFyeUxldFtrZXldID0gZGF0YVtrZXldOwogICAgICB9CiAgICB9CiAgfSwKICBNRVJHRV9ESUNUSU9OQVJZTEVUOiBmdW5jdGlvbiBNRVJHRV9ESUNUSU9OQVJZTEVUKHN0YXRlLCBkYXRhKSB7CiAgICB2YXIgZGljRGF0YSA9IGFyclRvVHJlZURpY3Rpb25hcnkoZGF0YS5kaWN0aW9uYXJ5KTsKICAgIGZvciAodmFyIGl0ZW0gaW4gZGF0YS5kaWN0aW9uYXJ5KSB7CiAgICAgIGlmICghY29tbW9uQmxhbmsoZGF0YS5kaWN0aW9uYXJ5W2l0ZW1dKSkgewogICAgICAgIHN0YXRlLmRpY3Rpb25hcnlUcmVlW2l0ZW1dID0gZGljRGF0YVtpdGVtXTsKICAgICAgfQogICAgfQogICAgc3RhdGUuZGljdGlvbmFyeVRyZWUgPSBzdGF0ZS5kaWN0aW9uYXJ5VHJlZVtkYXRhLnN5c3RlbU5vXTsKICAgIHZhciBkaWN0aW9uYXJ5UFVuZml5ID0ge307CiAgICBkaWN0aW9uYXJ5UFVuZml5ID0gX29iamVjdFNwcmVhZChfb2JqZWN0U3ByZWFkKHt9LCBzdGF0ZS5kaWN0aW9uYXJ5TGV0WydVTklGWSddKSwgZGF0YS52YWx1ZSk7CiAgICBzdGF0ZS5kaWN0aW9uYXJ5TGV0ID0gT2JqZWN0LmFzc2lnbih7fSwgc3RhdGUuZGljdGlvbmFyeUxldCwgewogICAgICBVTklGWTogZGljdGlvbmFyeVBVbmZpeQogICAgfSk7CiAgfSwKICBTRVRfRElDVElPTkFSWV9UUkVFOiBmdW5jdGlvbiBTRVRfRElDVElPTkFSWV9UUkVFKHN0YXRlLCBkYXRhKSB7CiAgICBzdGF0ZS5kaWN0aW9uYXJ5VHJlZSA9IGRhdGE7CiAgfSwKICBTRVRfVFJFRV9ST1VURTogZnVuY3Rpb24gU0VUX1RSRUVfUk9VVEUoc3RhdGUsIGRhdGEpIHsKICAgIHN0YXRlLnJvdXRlU3lzID0gZGF0YTsKICB9LAogIFNFVF9FWFRFUk5BTF9EQVRBOiBmdW5jdGlvbiBTRVRfRVhURVJOQUxfREFUQShzdGF0ZSwgZGF0YSkgewogICAgaWYgKGRhdGFbMF0uaW5kZXhPZignLCcpICE9PSAtMSkgewogICAgICB2YXIgZGF0YXMgPSBkYXRhWzBdLnNwbGl0KCcsJyk7CiAgICAgIHZhciBfaXRlcmF0b3IgPSBfY3JlYXRlRm9yT2ZJdGVyYXRvckhlbHBlcihkYXRhcyksCiAgICAgICAgX3N0ZXA7CiAgICAgIHRyeSB7CiAgICAgICAgZm9yIChfaXRlcmF0b3IucygpOyAhKF9zdGVwID0gX2l0ZXJhdG9yLm4oKSkuZG9uZTspIHsKICAgICAgICAgIHZhciBpdGVtID0gX3N0ZXAudmFsdWU7CiAgICAgICAgICBzdGF0ZS5leHRlcm5hbERhdGFbaXRlbV0gPSBkYXRhWzFdW2l0ZW1dOwogICAgICAgIH0KICAgICAgfSBjYXRjaCAoZXJyKSB7CiAgICAgICAgX2l0ZXJhdG9yLmUoZXJyKTsKICAgICAgfSBmaW5hbGx5IHsKICAgICAgICBfaXRlcmF0b3IuZigpOwogICAgICB9CiAgICB9IGVsc2UgewogICAgICBzdGF0ZS5leHRlcm5hbERhdGFbZGF0YVswXV0gPSBkYXRhWzFdOwogICAgfQogIH0sCiAgU0VUX0hPTUVfTU9VRExFUzogZnVuY3Rpb24gU0VUX0hPTUVfTU9VRExFUyhzdGF0ZSwgZGF0YSkgewogICAgc3RhdGUuaG9tZU1vdWRsZXMgPSBkYXRhOwogIH0sCiAgU0VUX0hPTUVfTU9VRExFU19DSEVDS0VEOiBmdW5jdGlvbiBTRVRfSE9NRV9NT1VETEVTX0NIRUNLRUQoc3RhdGUsIGRhdGEpIHsKICAgIHN0YXRlLmhvbWVNb3VkbGVzQ2hlY2tlZCA9IGRhdGE7CiAgfSwKICBTRVRfSE9NRV9NT1VETEVTX0RFRkFVTFQ6IGZ1bmN0aW9uIFNFVF9IT01FX01PVURMRVNfREVGQVVMVChzdGF0ZSwgZGF0YSkgewogICAgc3RhdGUuaG9tZU1vdWRsZXNEZWZhdWx0Q2hlY2tlZCA9IGRhdGE7CiAgfSwKICBTRVRfUk9MRV9MSVNUOiBmdW5jdGlvbiBTRVRfUk9MRV9MSVNUKHN0YXRlLCBkYXRhKSB7CiAgICBzdGF0ZS5yb2xlTGlzdCA9IGRhdGE7CiAgfSwKICBTRVRfUk9MRV9EQVRBOiBmdW5jdGlvbiBTRVRfUk9MRV9EQVRBKHN0YXRlLCBkYXRhKSB7CiAgICBzdGF0ZS5yb2xlTGV2ZWxEYXRhID0gZGF0YTsKICB9LAogIFNFVF9VU0VSX0xJU1Q6IGZ1bmN0aW9uIFNFVF9VU0VSX0xJU1Qoc3RhdGUsIGRhdGEpIHsKICAgIHN0YXRlLnVzZXJMaXN0ID0gZGF0YTsKICB9LAogIFNFVF9ST1VURVNfUEFSRU5UOiBmdW5jdGlvbiBTRVRfUk9VVEVTX1BBUkVOVChzdGF0ZSwgcm91dGVzKSB7CiAgICBzdGF0ZS5yb3V0ZXNQYXJlbnQgPSByb3V0ZXM7CiAgfSwKICAvLyDorr7nva7oj5zljZXmlbDmja7mlbDmja4KICBTRVRfTUVOVV9BUlI6IGZ1bmN0aW9uIFNFVF9NRU5VX0FSUihzdGF0ZSwgZGF0YSkgewogICAgc3RhdGUubWVudUFyciA9IGRhdGE7CiAgfSwKICAvLyDorr7nva7oj5zljZXlr7nosaEKICBTRVRfTUVOVV9PQko6IGZ1bmN0aW9uIFNFVF9NRU5VX09CSihzdGF0ZSwgZGF0YSkgewogICAgc3RhdGUubWVudU9iaiA9IGRhdGE7CiAgfSwKICAvLyDosIPnlKjln7rnur/mtYHnqIvmqKHmnb8KICBTRVRfRkxPV19EQVRBOiBmdW5jdGlvbiBTRVRfRkxPV19EQVRBKHN0YXRlLCBkYXRhKSB7CiAgICBzdGF0ZS5mbG93LndvcmtmbG93X2RhdGFTdHIgPSBkYXRhOwogIH0sCiAgU0VUX0ZMT1dfREFUQTI6IGZ1bmN0aW9uIFNFVF9GTE9XX0RBVEEyKHN0YXRlLCBkYXRhKSB7CiAgICBzdGF0ZS5mbG93Lm1vZHVsZV9pZCA9IGRhdGE7CiAgfSwKICAvLyDlrZjlgqjoj5zljZXmn6Xor6LmnaHku7YKICBTRVRfRkxPV19DT05ESVRJT046IGZ1bmN0aW9uIFNFVF9GTE9XX0NPTkRJVElPTihzdGF0ZSwgZGF0YSkgewogICAgc3RhdGUuZmxvdy5tZW51X2NvbmRpdGlvbiA9IGRhdGE7CiAgfSwKICBTRVRfRVhURVJOQUxfREFUQVM6IGZ1bmN0aW9uIFNFVF9FWFRFUk5BTF9EQVRBUyhzdGF0ZSwgZGF0YSkgewogICAgc3RhdGUuZXh0ZXJuYWxEYXRhU291cmNlID0gZGF0YTsKICB9LAogIFNFVF9DT01NT046IGZ1bmN0aW9uIFNFVF9DT01NT04oc3RhdGUsIGRhdGEpIHsKICAgIC8vIOmFjee9ruaJgOacieWxnuaApwogICAgZm9yICh2YXIgaXRlbSBpbiBkYXRhKSB7CiAgICAgIHN0YXRlW2l0ZW1dID0gZGF0YVtpdGVtXTsKICAgIH0KICB9LAogIC8vIOWtkOW3peeoi+S4reeahOaVsOaNruWtmOWCqO+8jCBpZnJhbWXlr7nmjqUKICBTRVRfU0FWRV9EQVRBOiBmdW5jdGlvbiBTRVRfU0FWRV9EQVRBKHN0YXRlLCBkYXRhKSB7CiAgICB2YXIgZGF0YXMgPSBzdGF0ZS5zYXZlRGF0YTsKICAgIGRhdGFzW2RhdGEua2V5XSA9IGRhdGEuZGF0YTsKICAgIHN0YXRlLnNhdmVEYXRhID0gZGF0YXM7CiAgfSwKICAvLyDorr7nva7mmK/lkKbmmL7npLrpobbpg6joj5zljZXmoI/mkJzntKLmoYYKICBTRVRfU0VBUkNIX0ZMQUc6IGZ1bmN0aW9uIFNFVF9TRUFSQ0hfRkxBRyhzdGF0ZSwgZGF0YSkgewogICAgc3RhdGUuaXNTaG93U2VhcmNoID0gZGF0YTsKICB9LAogIC8vIOiuvue9ruW9k+WJjeezu+e7n+WPtwogIFNFVF9TWVNURU1fTk86IGZ1bmN0aW9uIFNFVF9TWVNURU1fTk8oc3RhdGUsIGRhdGEpIHsKICAgIHN0YXRlLnN5c3RlbU5vID0gZGF0YTsKICB9LAogIC8vIOWIneWni+WMluezu+e7n+WPguaVsAogIElOSVRfU1lTX1BBUkFNOiBmdW5jdGlvbiBJTklUX1NZU19QQVJBTShzdGF0ZSwgZGF0YSkgewogICAgc3RhdGUuaW5pdFN5c1BhcmFtID0gZGF0YTsKICB9Cn07CnZhciBhY3Rpb25zID0gewogIC8qKgogICAqIOiOt+WPluacuuaehOagkSovCiAgb3JnYW5EYXRhOiBmdW5jdGlvbiBvcmdhbkRhdGEoX3JlZiwgZGF0YSkgewogICAgcmV0dXJuIF9hc3luY1RvR2VuZXJhdG9yKCAvKiNfX1BVUkVfXyovX3JlZ2VuZXJhdG9yUnVudGltZSgpLm1hcmsoZnVuY3Rpb24gX2NhbGxlZSgpIHsKICAgICAgdmFyIGNvbW1pdCwgZGlzcGF0Y2gsIHN0YXRlLCBsb2FkaW5nRnVuLCBtc2csIG9yZ2FuTnVsbCwgZGF0YXMsIG9yZ2FuTGlzdDsKICAgICAgcmV0dXJuIF9yZWdlbmVyYXRvclJ1bnRpbWUoKS53cmFwKGZ1bmN0aW9uIF9jYWxsZWUkKF9jb250ZXh0KSB7CiAgICAgICAgd2hpbGUgKDEpIHsKICAgICAgICAgIHN3aXRjaCAoX2NvbnRleHQucHJldiA9IF9jb250ZXh0Lm5leHQpIHsKICAgICAgICAgICAgY2FzZSAwOgogICAgICAgICAgICAgIGNvbW1pdCA9IF9yZWYuY29tbWl0LCBkaXNwYXRjaCA9IF9yZWYuZGlzcGF0Y2gsIHN0YXRlID0gX3JlZi5zdGF0ZTsKICAgICAgICAgICAgICBsb2FkaW5nRnVuID0gTG9hZGluZy5zZXJ2aWNlKHsKICAgICAgICAgICAgICAgIC8vIOWKoOi9veW8ueWHuuahhgogICAgICAgICAgICAgICAgbG9jazogdHJ1ZSwKICAgICAgICAgICAgICAgIHRleHQ6ICfmnLrmnoTmlbDmja7liqDovb3kuK3vvIzor7fnqI3lkI4uLi4gLi4uJywKICAgICAgICAgICAgICAgIHNwaW5uZXI6ICdlbC1pY29uLWxvYWRpbmcnLAogICAgICAgICAgICAgICAgYmFja2dyb3VuZDogJ3JnYmEoMCwgMCwgMCwgMC41KScsCiAgICAgICAgICAgICAgICBjdXN0b21DbGFzczogJ2xvYWRpbmdTdHknCiAgICAgICAgICAgICAgfSk7CiAgICAgICAgICAgICAgbXNnID0gewogICAgICAgICAgICAgICAgb3Blcl90eXBlOiAwCiAgICAgICAgICAgICAgfTsgLy8gY29uc3Qgb3JnYW5OdWxsID0gT2JqZWN0LmtleXMoc3RhdGUub3JnYW5UcmVlKS5sZW5ndGgKICAgICAgICAgICAgICBvcmdhbk51bGwgPSBzdGF0ZS5vcmdhblRyZWUubGVuZ3RoOwogICAgICAgICAgICAgIGlmICghKG9yZ2FuTnVsbCA9PT0gMCkpIHsKICAgICAgICAgICAgICAgIF9jb250ZXh0Lm5leHQgPSAxNTsKICAgICAgICAgICAgICAgIGJyZWFrOwogICAgICAgICAgICAgIH0KICAgICAgICAgICAgICBfY29udGV4dC5uZXh0ID0gNzsKICAgICAgICAgICAgICByZXR1cm4gb3JnYW5UcmVlKG1zZyk7CiAgICAgICAgICAgIGNhc2UgNzoKICAgICAgICAgICAgICBkYXRhcyA9IF9jb250ZXh0LnNlbnQ7CiAgICAgICAgICAgICAgbG9hZGluZ0Z1bi5jbG9zZSgpOyAvLyDliqDovb3nu5PmnZ8KICAgICAgICAgICAgICBvcmdhbkxpc3QgPSBkYXRhcy5yZXRNYXAub3JnYW5MaXN0OwogICAgICAgICAgICAgIGNvbW1pdCgnU0VUX09SR0FOX0xJU1QnLCBvcmdhbkxpc3QpOyAvLyDlsIbmnLrmnoTmoJHmlbDnu4TlrZjlnKhzdG9yZeS4rQogICAgICAgICAgICAgIGRpc3BhdGNoKCdvYmplY3REYXRhVHJhbnNsYXRlJywgb3JnYW5MaXN0KTsKICAgICAgICAgICAgICBjb21taXQoJ1NFVF9UUkVFJywgYXJyYXlUb1RyZWVMb29wKG9yZ2FuTGlzdCkpOwogICAgICAgICAgICAgIF9jb250ZXh0Lm5leHQgPSAxNjsKICAgICAgICAgICAgICBicmVhazsKICAgICAgICAgICAgY2FzZSAxNToKICAgICAgICAgICAgICBsb2FkaW5nRnVuLmNsb3NlKCk7IC8vIOWKoOi9vee7k+adnwogICAgICAgICAgICBjYXNlIDE2OgogICAgICAgICAgICBjYXNlICJlbmQiOgogICAgICAgICAgICAgIHJldHVybiBfY29udGV4dC5zdG9wKCk7CiAgICAgICAgICB9CiAgICAgICAgfQogICAgICB9LCBfY2FsbGVlKTsKICAgIH0pKSgpOwogIH0sCiAgLyoqCiAgICog6I635Y+W5p2D6ZmQ5py65p6EKi8KICByaWdodE9yZ2FuRGF0YTogZnVuY3Rpb24gcmlnaHRPcmdhbkRhdGEoX3JlZjIsIGRhdGEpIHsKICAgIHJldHVybiBfYXN5bmNUb0dlbmVyYXRvciggLyojX19QVVJFX18qL19yZWdlbmVyYXRvclJ1bnRpbWUoKS5tYXJrKGZ1bmN0aW9uIF9jYWxsZWUyKCkgewogICAgICB2YXIgY29tbWl0LCBkaXNwYXRjaCwgc3RhdGUsIG1zZywgcmVzRGF0YSwgb3JnYW5MaXN0OwogICAgICByZXR1cm4gX3JlZ2VuZXJhdG9yUnVudGltZSgpLndyYXAoZnVuY3Rpb24gX2NhbGxlZTIkKF9jb250ZXh0MikgewogICAgICAgIHdoaWxlICgxKSB7CiAgICAgICAgICBzd2l0Y2ggKF9jb250ZXh0Mi5wcmV2ID0gX2NvbnRleHQyLm5leHQpIHsKICAgICAgICAgICAgY2FzZSAwOgogICAgICAgICAgICAgIGNvbW1pdCA9IF9yZWYyLmNvbW1pdCwgZGlzcGF0Y2ggPSBfcmVmMi5kaXNwYXRjaCwgc3RhdGUgPSBfcmVmMi5zdGF0ZTsKICAgICAgICAgICAgICBtc2cgPSB7CiAgICAgICAgICAgICAgICBvcGVyX3R5cGU6IDAKICAgICAgICAgICAgICB9OwogICAgICAgICAgICAgIF9jb250ZXh0Mi5uZXh0ID0gNDsKICAgICAgICAgICAgICByZXR1cm4gcXVlcnlSaWdodE9yZ2FuKG1zZyk7CiAgICAgICAgICAgIGNhc2UgNDoKICAgICAgICAgICAgICByZXNEYXRhID0gX2NvbnRleHQyLnNlbnQ7CiAgICAgICAgICAgICAgaWYgKHJlc0RhdGEucmV0TWFwLm9yZ2FuRmxhZykgewogICAgICAgICAgICAgICAgb3JnYW5MaXN0ID0gcmVzRGF0YS5yZXRNYXAub3JnYW5MaXN0OwogICAgICAgICAgICAgICAgb3JnYW5MaXN0LmZvckVhY2goZnVuY3Rpb24gKGl0ZW0pIHsKICAgICAgICAgICAgICAgICAgaXRlbS5uYW1lID0gaXRlbS5pZCArICctJyArIGl0ZW0ubmFtZTsKICAgICAgICAgICAgICAgIH0pOwogICAgICAgICAgICAgICAgY29tbWl0KCdTRVRfUklHSFRBUlJBWScsIG9yZ2FuTGlzdCk7IC8vIOWtmOWCqOadg+mZkOacuuaehOaVsOe7hAogICAgICAgICAgICAgICAgY29tbWl0KCdTRVRfUklHSFRUUkVFJywgYXJyYXlUb1RyZWVMb29wKG9yZ2FuTGlzdCkpOwogICAgICAgICAgICAgIH0gZWxzZSB7CiAgICAgICAgICAgICAgICBjb21taXQoJ1NFVF9SSUdIVFRSRUUnLCBbXSk7CiAgICAgICAgICAgICAgfQogICAgICAgICAgICBjYXNlIDY6CiAgICAgICAgICAgIGNhc2UgImVuZCI6CiAgICAgICAgICAgICAgcmV0dXJuIF9jb250ZXh0Mi5zdG9wKCk7CiAgICAgICAgICB9CiAgICAgICAgfQogICAgICB9LCBfY2FsbGVlMik7CiAgICB9KSkoKTsKICB9LAogIC8vIOiOt+WPluaJgOacieinkuiJsuS/oeaBrwogIGdldFJvbGVEYXRhOiBmdW5jdGlvbiBnZXRSb2xlRGF0YShfcmVmMywgZGF0YSkgewogICAgcmV0dXJuIF9hc3luY1RvR2VuZXJhdG9yKCAvKiNfX1BVUkVfXyovX3JlZ2VuZXJhdG9yUnVudGltZSgpLm1hcmsoZnVuY3Rpb24gX2NhbGxlZTMoKSB7CiAgICAgIHZhciBjb21taXQsIGRpc3BhdGNoLCBzdGF0ZSwgbXNnLCBkYXRhcywgcm9sZUxpc3QsIHJvbGVMZXZlbERhdGEsIGxldmVsLCBsZXZlbFJvbGU7CiAgICAgIHJldHVybiBfcmVnZW5lcmF0b3JSdW50aW1lKCkud3JhcChmdW5jdGlvbiBfY2FsbGVlMyQoX2NvbnRleHQzKSB7CiAgICAgICAgd2hpbGUgKDEpIHsKICAgICAgICAgIHN3aXRjaCAoX2NvbnRleHQzLnByZXYgPSBfY29udGV4dDMubmV4dCkgewogICAgICAgICAgICBjYXNlIDA6CiAgICAgICAgICAgICAgY29tbWl0ID0gX3JlZjMuY29tbWl0LCBkaXNwYXRjaCA9IF9yZWYzLmRpc3BhdGNoLCBzdGF0ZSA9IF9yZWYzLnN0YXRlOwogICAgICAgICAgICAgIG1zZyA9IHsKICAgICAgICAgICAgICAgIHBhcmFtZXRlckxpc3Q6IFtdCiAgICAgICAgICAgICAgfTsKICAgICAgICAgICAgICBfY29udGV4dDMubmV4dCA9IDQ7CiAgICAgICAgICAgICAgcmV0dXJuIGdldEFsbFJvbGVzKG1zZyk7CiAgICAgICAgICAgIGNhc2UgNDoKICAgICAgICAgICAgICBkYXRhcyA9IF9jb250ZXh0My5zZW50OwogICAgICAgICAgICAgIHJvbGVMaXN0ID0gZGF0YXMucmV0TWFwLnJvbGVMaXN0OwogICAgICAgICAgICAgIHJvbGVMZXZlbERhdGEgPSB7fTsKICAgICAgICAgICAgICBsZXZlbCA9IDE7CiAgICAgICAgICAgIGNhc2UgODoKICAgICAgICAgICAgICBsZXZlbFJvbGUgPSBnZXRMZXZlbFJvbGUocm9sZUxpc3QsIGxldmVsKTsKICAgICAgICAgICAgICBpZiAoISghbGV2ZWxSb2xlIHx8IGxldmVsUm9sZS5sZW5ndGggPD0gMCkpIHsKICAgICAgICAgICAgICAgIF9jb250ZXh0My5uZXh0ID0gMTE7CiAgICAgICAgICAgICAgICBicmVhazsKICAgICAgICAgICAgICB9CiAgICAgICAgICAgICAgcmV0dXJuIF9jb250ZXh0My5hYnJ1cHQoImJyZWFrIiwgMTUpOwogICAgICAgICAgICBjYXNlIDExOgogICAgICAgICAgICAgIHJvbGVMZXZlbERhdGFbJ2xldmVsJyArIGxldmVsXSA9IGxldmVsUm9sZTsKICAgICAgICAgICAgY2FzZSAxMjoKICAgICAgICAgICAgICBsZXZlbCsrOwogICAgICAgICAgICAgIF9jb250ZXh0My5uZXh0ID0gODsKICAgICAgICAgICAgICBicmVhazsKICAgICAgICAgICAgY2FzZSAxNToKICAgICAgICAgICAgICBjb21taXQoJ1NFVF9ST0xFX0RBVEEnLCByb2xlTGV2ZWxEYXRhKTsgLy8g5bCG6KeS6Imy5pWw5o2u5a+56LGh5a2Y5Zyoc3RvcmXkuK0KICAgICAgICAgICAgICByb2xlTGlzdC5tYXAoZnVuY3Rpb24gKHJvbGUpIHsKICAgICAgICAgICAgICAgIHJvbGUubGFiZWwgPSByb2xlLm5hbWU7CiAgICAgICAgICAgICAgfSk7CiAgICAgICAgICAgICAgY29tbWl0KCdTRVRfUk9MRV9MSVNUJywgcm9sZUxpc3QpOyAvLyDlsIbop5LoibLmlbDnu4TlrZjlnKhzdG9yZeS4rQogICAgICAgICAgICBjYXNlIDE4OgogICAgICAgICAgICBjYXNlICJlbmQiOgogICAgICAgICAgICAgIHJldHVybiBfY29udGV4dDMuc3RvcCgpOwogICAgICAgICAgfQogICAgICAgIH0KICAgICAgfSwgX2NhbGxlZTMpOwogICAgfSkpKCk7CiAgfSwKICAvLyDliqDovb3lhajooYznlKjmiLfkv6Hmga8KICBnZXRVc2VyRGF0YTogZnVuY3Rpb24gZ2V0VXNlckRhdGEoX3JlZjQsIGRhdGEpIHsKICAgIHJldHVybiBfYXN5bmNUb0dlbmVyYXRvciggLyojX19QVVJFX18qL19yZWdlbmVyYXRvclJ1bnRpbWUoKS5tYXJrKGZ1bmN0aW9uIF9jYWxsZWU0KCkgewogICAgICB2YXIgY29tbWl0LCBkaXNwYXRjaCwgc3RhdGUsIG1zZywgZGF0YXMsIHVzZXJMaXN0OwogICAgICByZXR1cm4gX3JlZ2VuZXJhdG9yUnVudGltZSgpLndyYXAoZnVuY3Rpb24gX2NhbGxlZTQkKF9jb250ZXh0NCkgewogICAgICAgIHdoaWxlICgxKSB7CiAgICAgICAgICBzd2l0Y2ggKF9jb250ZXh0NC5wcmV2ID0gX2NvbnRleHQ0Lm5leHQpIHsKICAgICAgICAgICAgY2FzZSAwOgogICAgICAgICAgICAgIGNvbW1pdCA9IF9yZWY0LmNvbW1pdCwgZGlzcGF0Y2ggPSBfcmVmNC5kaXNwYXRjaCwgc3RhdGUgPSBfcmVmNC5zdGF0ZTsKICAgICAgICAgICAgICBtc2cgPSB7CiAgICAgICAgICAgICAgICBwYXJhbWV0ZXJMaXN0OiBbXQogICAgICAgICAgICAgIH07CiAgICAgICAgICAgICAgX2NvbnRleHQ0Lm5leHQgPSA0OwogICAgICAgICAgICAgIHJldHVybiBnZXRBbGxVc2Vycyhtc2cpOwogICAgICAgICAgICBjYXNlIDQ6CiAgICAgICAgICAgICAgZGF0YXMgPSBfY29udGV4dDQuc2VudDsKICAgICAgICAgICAgICB1c2VyTGlzdCA9IGRhdGFzLnJldE1hcC51c2VyTGlzdDsKICAgICAgICAgICAgICB1c2VyTGlzdC5tYXAoZnVuY3Rpb24gKHJvbGUpIHsKICAgICAgICAgICAgICAgIHJvbGUubGFiZWwgPSByb2xlLm5hbWU7CiAgICAgICAgICAgICAgfSk7CiAgICAgICAgICAgICAgY29tbWl0KCdTRVRfVVNFUl9MSVNUJywgdXNlckxpc3QpOyAvLyDlsIbnlKjmiLfkv6Hmga/mlbDnu4TlrZjlnKhzdG9yZeS4rQogICAgICAgICAgICBjYXNlIDg6CiAgICAgICAgICAgIGNhc2UgImVuZCI6CiAgICAgICAgICAgICAgcmV0dXJuIF9jb250ZXh0NC5zdG9wKCk7CiAgICAgICAgICB9CiAgICAgICAgfQogICAgICB9LCBfY2FsbGVlNCk7CiAgICB9KSkoKTsKICB9LAogIGdlbmVyYXRlUm91dGVzOiBmdW5jdGlvbiBnZW5lcmF0ZVJvdXRlcyhfcmVmNSwgcGFyYW0pIHsKICAgIHJldHVybiBfYXN5bmNUb0dlbmVyYXRvciggLyojX19QVVJFX18qL19yZWdlbmVyYXRvclJ1bnRpbWUoKS5tYXJrKGZ1bmN0aW9uIF9jYWxsZWU1KCkgewogICAgICB2YXIgY29tbWl0LCBkaXNwYXRjaCwgc3RhdGUsIGFjY2Vzc2VkUm91dGVzLCBtc2csIGRhdGFzLCBtZW51TGlzdCwgcmV0dXJuTGlzdERhdGEsIF9pdGVyYXRvcjIsIF9zdGVwMiwgaXRlbTsKICAgICAgcmV0dXJuIF9yZWdlbmVyYXRvclJ1bnRpbWUoKS53cmFwKGZ1bmN0aW9uIF9jYWxsZWU1JChfY29udGV4dDUpIHsKICAgICAgICB3aGlsZSAoMSkgewogICAgICAgICAgc3dpdGNoIChfY29udGV4dDUucHJldiA9IF9jb250ZXh0NS5uZXh0KSB7CiAgICAgICAgICAgIGNhc2UgMDoKICAgICAgICAgICAgICBjb21taXQgPSBfcmVmNS5jb21taXQsIGRpc3BhdGNoID0gX3JlZjUuZGlzcGF0Y2gsIHN0YXRlID0gX3JlZjUuc3RhdGU7CiAgICAgICAgICAgICAgLy8g6I635Y+W5bem5L6n54i257qn6I+c5Y2VCiAgICAgICAgICAgICAgYWNjZXNzZWRSb3V0ZXMgPSBbXTsKICAgICAgICAgICAgICBtc2cgPSB7CiAgICAgICAgICAgICAgICBvcGVyX3R5cGU6ICcwJywKICAgICAgICAgICAgICAgIGlzUEM6IHRydWUsCiAgICAgICAgICAgICAgICBtZW51X3R5cGU6ICcyJwogICAgICAgICAgICAgIH07CiAgICAgICAgICAgICAgX2NvbnRleHQ1Lm5leHQgPSA1OwogICAgICAgICAgICAgIHJldHVybiBjb21tb25NZW51KG1zZyk7CiAgICAgICAgICAgIGNhc2UgNToKICAgICAgICAgICAgICBkYXRhcyA9IF9jb250ZXh0NS5zZW50OwogICAgICAgICAgICAgIG1lbnVMaXN0ID0gZGF0YXMucmV0TWFwLm1lbnVMaXN0OwogICAgICAgICAgICAgIGlmIChjb21tb25CbGFuayhtZW51TGlzdCkpIHsKICAgICAgICAgICAgICAgIG1lbnVMaXN0ID0gW107CiAgICAgICAgICAgICAgfQogICAgICAgICAgICAgIHJldHVybkxpc3REYXRhID0gW107IC8vIOaJgOacieiPnOWNlQogICAgICAgICAgICAgIF9pdGVyYXRvcjIgPSBfY3JlYXRlRm9yT2ZJdGVyYXRvckhlbHBlcihtZW51TGlzdCk7CiAgICAgICAgICAgICAgdHJ5IHsKICAgICAgICAgICAgICAgIGZvciAoX2l0ZXJhdG9yMi5zKCk7ICEoX3N0ZXAyID0gX2l0ZXJhdG9yMi5uKCkpLmRvbmU7KSB7CiAgICAgICAgICAgICAgICAgIGl0ZW0gPSBfc3RlcDIudmFsdWU7CiAgICAgICAgICAgICAgICAgIHJldHVybkxpc3REYXRhID0gW10uY29uY2F0KF90b0NvbnN1bWFibGVBcnJheShyZXR1cm5MaXN0RGF0YSksIFtfb2JqZWN0U3ByZWFkKHsKICAgICAgICAgICAgICAgICAgICBpbmRleDogJzAnCiAgICAgICAgICAgICAgICAgIH0sIGl0ZW0pXSk7CiAgICAgICAgICAgICAgICB9CiAgICAgICAgICAgICAgfSBjYXRjaCAoZXJyKSB7CiAgICAgICAgICAgICAgICBfaXRlcmF0b3IyLmUoZXJyKTsKICAgICAgICAgICAgICB9IGZpbmFsbHkgewogICAgICAgICAgICAgICAgX2l0ZXJhdG9yMi5mKCk7CiAgICAgICAgICAgICAgfQogICAgICAgICAgICAgIGFjY2Vzc2VkUm91dGVzID0gZmlsdGVyQXN5bmNSb3V0ZXMocmV0dXJuTGlzdERhdGEsICdwYXJlbnQnKTsgLy8g6YWN572u5Yqo5oCB6Lev55SxCiAgICAgICAgICAgICAgY29tbWl0KCdTRVRfUk9VVEVTX1BBUkVOVCcsIGFjY2Vzc2VkUm91dGVzKTsKICAgICAgICAgICAgY2FzZSAxMzoKICAgICAgICAgICAgY2FzZSAiZW5kIjoKICAgICAgICAgICAgICByZXR1cm4gX2NvbnRleHQ1LnN0b3AoKTsKICAgICAgICAgIH0KICAgICAgICB9CiAgICAgIH0sIF9jYWxsZWU1KTsKICAgIH0pKSgpOwogIH0sCiAgLy8g6I635Y+W5aSW6KGo5a2X5YW4CiAgZ2V0RXh0ZXJuYWw6IGZ1bmN0aW9uIGdldEV4dGVybmFsKF9yZWY2LCBrZXkpIHsKICAgIHZhciBjb21taXQgPSBfcmVmNi5jb21taXQsCiAgICAgIHN0YXRlID0gX3JlZjYuc3RhdGU7CiAgICB2YXIgbXNnID0gewogICAgICBwYXJhbWV0ZXJMaXN0OiBbXSwKICAgICAgb3Blcl90eXBlOiAnZXh0ZXJuYWxLZXlzJwogICAgfTsKICAgIGV4dGVuZFNvdXJjZUFycihtc2cpLnRoZW4oZnVuY3Rpb24gKHJlc3BvbnNlKSB7CiAgICAgIHZhciBrZXlzID0gcmVzcG9uc2UucmV0TWFwLmtleXM7CiAgICAgIHZhciBfaXRlcmF0b3IzID0gX2NyZWF0ZUZvck9mSXRlcmF0b3JIZWxwZXIoa2V5cyksCiAgICAgICAgX3N0ZXAzOwogICAgICB0cnkgewogICAgICAgIGZvciAoX2l0ZXJhdG9yMy5zKCk7ICEoX3N0ZXAzID0gX2l0ZXJhdG9yMy5uKCkpLmRvbmU7KSB7CiAgICAgICAgICB2YXIgaSA9IF9zdGVwMy52YWx1ZTsKICAgICAgICAgIGlbJ2xhYmVsJ10gPSBpWydleHRlcm5hbF9kZXNjJ107CiAgICAgICAgICBpWydjaGlsZHJlbiddID0gW107CiAgICAgICAgICBpWydpZCddID0gaVsnZXh0ZXJuYWxfa2V5J107CiAgICAgICAgfQogICAgICB9IGNhdGNoIChlcnIpIHsKICAgICAgICBfaXRlcmF0b3IzLmUoZXJyKTsKICAgICAgfSBmaW5hbGx5IHsKICAgICAgICBfaXRlcmF0b3IzLmYoKTsKICAgICAgfQogICAgICBjb21taXQoJ1NFVF9FWFRFUk5BTF9EQVRBUycsIGtleXMpOwogICAgfSk7CiAgfSwKICAvLyDojrflj5blpJbooajmlbDmja7mupAo5a+56LGh5b2i5byPKQogIGdldERhdGFTb3VyY2U6IGZ1bmN0aW9uIGdldERhdGFTb3VyY2UoX3JlZjcpIHsKICAgIHZhciBjb21taXQgPSBfcmVmNy5jb21taXQsCiAgICAgIGRpc3BhdGNoID0gX3JlZjcuZGlzcGF0Y2g7CiAgICB2YXIgYXJyID0gWydJTlRFX1JVTEVfTk8nLCAnUE9TVF9OTycsICdFTVBfTk8nLCAnSU5URV9JVEVNX05PJ107CiAgICBmb3IgKHZhciBfaSA9IDAsIF9hcnIgPSBhcnI7IF9pIDwgX2Fyci5sZW5ndGg7IF9pKyspIHsKICAgICAgdmFyIGkgPSBfYXJyW19pXTsKICAgICAgZGlzcGF0Y2goJ3NldEV4dGVybmFsRGF0YScsIGkpOwogICAgfQogIH0sCiAgZ2V0T3duRGljdGlvbmFyeTogZnVuY3Rpb24gZ2V0T3duRGljdGlvbmFyeShfcmVmOCwgcGFyYW0pIHsKICAgIHJldHVybiBfYXN5bmNUb0dlbmVyYXRvciggLyojX19QVVJFX18qL19yZWdlbmVyYXRvclJ1bnRpbWUoKS5tYXJrKGZ1bmN0aW9uIF9jYWxsZWU2KCkgewogICAgICB2YXIgY29tbWl0LCBkaXNwYXRjaCwgc3RhdGUsIG1zZywgcmVzOwogICAgICByZXR1cm4gX3JlZ2VuZXJhdG9yUnVudGltZSgpLndyYXAoZnVuY3Rpb24gX2NhbGxlZTYkKF9jb250ZXh0NikgewogICAgICAgIHdoaWxlICgxKSB7CiAgICAgICAgICBzd2l0Y2ggKF9jb250ZXh0Ni5wcmV2ID0gX2NvbnRleHQ2Lm5leHQpIHsKICAgICAgICAgICAgY2FzZSAwOgogICAgICAgICAgICAgIGNvbW1pdCA9IF9yZWY4LmNvbW1pdCwgZGlzcGF0Y2ggPSBfcmVmOC5kaXNwYXRjaCwgc3RhdGUgPSBfcmVmOC5zdGF0ZTsKICAgICAgICAgICAgICAvLyDojrflj5blrZDns7vnu5/mlbDmja7lrZflhbgKICAgICAgICAgICAgICBtc2cgPSB7CiAgICAgICAgICAgICAgICBwYXJhbWV0ZXJMaXN0OiBbJyddLAogICAgICAgICAgICAgICAgZmllbGRUaW1lOiAnJywKICAgICAgICAgICAgICAgIG9wZXJUeXBlOiAnMScsCiAgICAgICAgICAgICAgICAvLyDpl6jmiLfmk43kvZzmoIfor4YKICAgICAgICAgICAgICAgIHN5c3RlbU5vOiBkZWZhdWx0U2V0dGluZ3Muc3lzdGVtTm8KICAgICAgICAgICAgICB9OwogICAgICAgICAgICAgIF9jb250ZXh0Ni5uZXh0ID0gNDsKICAgICAgICAgICAgICByZXR1cm4gZmllbGRDb250cm9sbGVyKG1zZyk7CiAgICAgICAgICAgIGNhc2UgNDoKICAgICAgICAgICAgICByZXMgPSBfY29udGV4dDYuc2VudDsKICAgICAgICAgICAgICBjb21taXQoJ01FUkdFX0RJQ1RJT05BUllMRVQnLCB7CiAgICAgICAgICAgICAgICBzeXN0ZW1ObzogZGVmYXVsdFNldHRpbmdzLnN5c3RlbU5vLAogICAgICAgICAgICAgICAgdmFsdWU6IHJlcy5yZXRNYXBbZGVmYXVsdFNldHRpbmdzLnN5c3RlbU5vXSwKICAgICAgICAgICAgICAgIGRpY3Rpb25hcnk6IHJlcy5yZXRNYXAuZGljdGlvbmFyeQogICAgICAgICAgICAgIH0pOwogICAgICAgICAgICBjYXNlIDY6CiAgICAgICAgICAgIGNhc2UgImVuZCI6CiAgICAgICAgICAgICAgcmV0dXJuIF9jb250ZXh0Ni5zdG9wKCk7CiAgICAgICAgICB9CiAgICAgICAgfQogICAgICB9LCBfY2FsbGVlNik7CiAgICB9KSkoKTsKICB9LAogIC8vIOWIneivleWMluezu+e7n+WPguaVsAogIGluaXRTeXNQYXJhbTogZnVuY3Rpb24gaW5pdFN5c1BhcmFtKF9yZWY5LCBwYXJhbSkgewogICAgcmV0dXJuIF9hc3luY1RvR2VuZXJhdG9yKCAvKiNfX1BVUkVfXyovX3JlZ2VuZXJhdG9yUnVudGltZSgpLm1hcmsoZnVuY3Rpb24gX2NhbGxlZTcoKSB7CiAgICAgIHZhciBjb21taXQsIGRpc3BhdGNoLCBzdGF0ZSwgbXNnLCBkYXRhcywgc3lzcGFybXM7CiAgICAgIHJldHVybiBfcmVnZW5lcmF0b3JSdW50aW1lKCkud3JhcChmdW5jdGlvbiBfY2FsbGVlNyQoX2NvbnRleHQ3KSB7CiAgICAgICAgd2hpbGUgKDEpIHsKICAgICAgICAgIHN3aXRjaCAoX2NvbnRleHQ3LnByZXYgPSBfY29udGV4dDcubmV4dCkgewogICAgICAgICAgICBjYXNlIDA6CiAgICAgICAgICAgICAgY29tbWl0ID0gX3JlZjkuY29tbWl0LCBkaXNwYXRjaCA9IF9yZWY5LmRpc3BhdGNoLCBzdGF0ZSA9IF9yZWY5LnN0YXRlOwogICAgICAgICAgICAgIG1zZyA9IHsKICAgICAgICAgICAgICAgIHBhcmFtZXRlckxpc3Q6IFtdLAogICAgICAgICAgICAgICAgc3lzTWFwOiB7fQogICAgICAgICAgICAgIH07CiAgICAgICAgICAgICAgX2NvbnRleHQ3Lm5leHQgPSA0OwogICAgICAgICAgICAgIHJldHVybiBfaW5pdFN5c1BhcmFtKG1zZyk7CiAgICAgICAgICAgIGNhc2UgNDoKICAgICAgICAgICAgICBkYXRhcyA9IF9jb250ZXh0Ny5zZW50OwogICAgICAgICAgICAgIHN5c3Bhcm1zID0gZGF0YXMucmV0TWFwLnN5c3Bhcm1zOwogICAgICAgICAgICAgIGNvbW1pdCgnSU5JVF9TWVNfUEFSQU0nLCBzeXNwYXJtcyk7IC8vIOWwhuacuuaehOagkeaVsOe7hOWtmOWcqHN0b3Jl5LitCiAgICAgICAgICAgIGNhc2UgNzoKICAgICAgICAgICAgY2FzZSAiZW5kIjoKICAgICAgICAgICAgICByZXR1cm4gX2NvbnRleHQ3LnN0b3AoKTsKICAgICAgICAgIH0KICAgICAgICB9CiAgICAgIH0sIF9jYWxsZWU3KTsKICAgIH0pKSgpOwogIH0sCiAgY29tbW9uSW5pdDogZnVuY3Rpb24gY29tbW9uSW5pdChfcmVmMTApIHsKICAgIHZhciByb290U3RhdGUgPSBfcmVmMTAucm9vdFN0YXRlLAogICAgICBkaXNwYXRjaCA9IF9yZWYxMC5kaXNwYXRjaCwKICAgICAgc3RhdGUgPSBfcmVmMTAuc3RhdGU7CiAgICBpZiAoY29tbW9uQmxhbmsoc3RhdGUub3JnYW5UcmVlKSkgewogICAgICBkaXNwYXRjaCgnZ2V0RmllbGREYXRhJyk7IC8vIOWKoOi9veaVsOaNruWtl+WFuCDlkIzmraXmlbDmja7lrZflhbgKICAgICAgZGlzcGF0Y2goJ29yZ2FuRGF0YScsIHJvb3RTdGF0ZS51c2VyLm9yZ2FuTm8pOyAvLyDojrflj5bmnLrmnoTmoJEKICAgICAgZGlzcGF0Y2goJ3JpZ2h0T3JnYW5EYXRhJywgcm9vdFN0YXRlLnVzZXIub3JnYW5Obyk7IC8vIOiOt+WPluadg+mZkOacuuaehAogICAgICBkaXNwYXRjaCgnZ2V0Um9sZURhdGEnKTsgLy8g6I635Y+W6KeS6ImyCiAgICAgIGRpc3BhdGNoKCdnZXRVc2VyRGF0YScpOyAvLyDliqDovb3lhajooYznlKjmiLfkv6Hmga8KICAgICAgZGlzcGF0Y2goJ2dlbmVyYXRlUm91dGVzJyk7IC8vIOWKoOi9veeItue6p+iPnOWNlQogICAgICBkaXNwYXRjaCgnZ2V0RXh0ZXJuYWwnKTsgLy8g6I635Y+W5aSW6KGo5pWw5o2u5rqQCiAgICAgIGRpc3BhdGNoKCdpbml0U3lzUGFyYW0nKTsgLy8g5Yid5aeL5YyW57O757uf5Y+C5pWwCiAgICAgIGlmIChzZXR0aW5nc05hbWUgIT09ICdkb3BVbmlmeScpIHsKICAgICAgICBkaXNwYXRjaCgnZ2V0T3duRGljdGlvbmFyeScpOyAvLyDlrZDns7vnu5/ojrflj5bmlbDmja7lrZflhbgKICAgICAgfQogICAgfQogIH0sCiAgLyoqCiAgICog5py65p6E5qC85byP5YyW5Li65a+56LGhKi8KICBvYmplY3REYXRhVHJhbnNsYXRlOiBmdW5jdGlvbiBvYmplY3REYXRhVHJhbnNsYXRlKF9yZWYxMSwgb2JqKSB7CiAgICB2YXIgY29tbWl0ID0gX3JlZjExLmNvbW1pdDsKICAgIHZhciBkYXRhID0gb2JqOwogICAgdmFyIGRhdGFzID0ge307CiAgICBpZiAoZGF0YSkgewogICAgICB2YXIgX2l0ZXJhdG9yNCA9IF9jcmVhdGVGb3JPZkl0ZXJhdG9ySGVscGVyKGRhdGEpLAogICAgICAgIF9zdGVwNDsKICAgICAgdHJ5IHsKICAgICAgICBmb3IgKF9pdGVyYXRvcjQucygpOyAhKF9zdGVwNCA9IF9pdGVyYXRvcjQubigpKS5kb25lOykgewogICAgICAgICAgdmFyIGtleSA9IF9zdGVwNC52YWx1ZTsKICAgICAgICAgIGRhdGFzW2tleS5pZF0gPSBrZXk7CiAgICAgICAgfQogICAgICB9IGNhdGNoIChlcnIpIHsKICAgICAgICBfaXRlcmF0b3I0LmUoZXJyKTsKICAgICAgfSBmaW5hbGx5IHsKICAgICAgICBfaXRlcmF0b3I0LmYoKTsKICAgICAgfQogICAgICBjb21taXQoJ1NFVF9UUkVFX09CSkVDVCcsIGRhdGFzKTsKICAgIH0KICB9LAogIC8qKgogICAqIOW4uOmHj+Wtl+WFuOmFjee9riovCiAgZGljdGlvbmFyeTogZnVuY3Rpb24gZGljdGlvbmFyeShfcmVmMTIsIGRhdGEpIHsKICAgIHZhciBjb21taXQgPSBfcmVmMTIuY29tbWl0LAogICAgICBzdGF0ZSA9IF9yZWYxMi5zdGF0ZTsKICAgIGNvbW1pdCgnU0VUX0RJQ1RJT05BUlknLCBkYXRhKTsKICB9LAogIC8vIOWKoOi9veaVsOaNruWtl+WFuAogIGdldEZpZWxkRGF0YTogZnVuY3Rpb24gZ2V0RmllbGREYXRhKF9yZWYxMywgZGF0YSkgewogICAgdmFyIGNvbW1pdCA9IF9yZWYxMy5jb21taXQsCiAgICAgIHN0YXRlID0gX3JlZjEzLnN0YXRlOwogICAgdmFyIGRpY3Rpb25hcnlGaWVsZFRpbWUgPSBsb2NhbFN0b3JhZ2UuZ2V0SXRlbSgnZmllbGRUaW1lJyk7IC8vIOiOt+WPluaVsOaNruWtl+WFuOiOt+WPluaXtumXtAogICAgdmFyIGZpZWxkVGltZSA9ICcnOwogICAgaWYgKCFjb21tb25CbGFuayhkaWN0aW9uYXJ5RmllbGRUaW1lKSkgewogICAgICBmaWVsZFRpbWUgPSBkaWN0aW9uYXJ5RmllbGRUaW1lOwogICAgfQogICAgdmFyIG1zZyA9IHsKICAgICAgcGFyYW1ldGVyTGlzdDogWycnXSwKICAgICAgc3lzTWFwOiB7CiAgICAgICAgcGFyYW1ldGVyTGlzdDogWycnXSwKICAgICAgICBvcGVyVHlwZTogJzEnLAogICAgICAgIGZpZWxkVGltZTogZmllbGRUaW1lLAogICAgICAgIHN5c3RlbU5vOiAnVU5JRlksQU9TJwogICAgICB9CiAgICB9OwogICAgdmFyIG9yZ2FuTnVsbCA9IE9iamVjdC5rZXlzKHN0YXRlLmRpY3Rpb25hcnlMZXQpLmxlbmd0aDsKICAgIGlmIChvcmdhbk51bGwgPT09IDApIHsKICAgICAgLy8g5pWw5o2u5Li656m66YeN5paw6K+35rGCCiAgICAgIGZpZWxkQ29udHJvbGxlcihtc2cpLnRoZW4oZnVuY3Rpb24gKHJlc3BvbnNlKSB7CiAgICAgICAgbG9jYWxTdG9yYWdlLnNldEl0ZW0oJ2ZpZWxkVGltZScsIHJlc3BvbnNlLnJldE1hcC5maWVsZFRpbWUpOyAvLyDlrZjlgqjmlbDmja7lrZflhbjojrflj5bml7bpl7QKICAgICAgICBpZiAoY29tbW9uQmxhbmsoZGljdGlvbmFyeUZpZWxkVGltZSkpIHsKICAgICAgICAgIC8vIOWtl+WFuOivt+axguaXtumXtOS4uuepuuaXtu+8jGxvY2FsU3RvcmFnZemHjOayoeacieWtl+WFuOaXtuimgeWPkeivt+axguaLv+Wtl+WFuO+8jOWIhuWIq+WtmOWIsGxvY2FsU3RvcmFnZeWSjHZ1ZXgKICAgICAgICAgIGxvY2FsU3RvcmFnZS5zZXRJdGVtKCdVTklGWScsIEpTT04uc3RyaW5naWZ5KHJlc3BvbnNlLnJldE1hcC5VTklGWSkpOwogICAgICAgICAgbG9jYWxTdG9yYWdlLnNldEl0ZW0oJ0FPUycsIEpTT04uc3RyaW5naWZ5KHJlc3BvbnNlLnJldE1hcC5BT1MpKTsKICAgICAgICAgIGxvY2FsU3RvcmFnZS5zZXRJdGVtKCdVTklGWWRpY3Rpb25hcnknLCBKU09OLnN0cmluZ2lmeSh7CiAgICAgICAgICAgIFVOSUZZOiByZXNwb25zZS5yZXRNYXAuZGljdGlvbmFyeS5VTklGWSwKICAgICAgICAgICAgQU9TOiByZXNwb25zZS5yZXRNYXAuZGljdGlvbmFyeS5BT1MKICAgICAgICAgIH0pKTsKICAgICAgICAgIC8vIOeZu+W9leivt+axggogICAgICAgICAgY29tbWl0KCdTRVRfRElDVElPTkFSWUxFVCcsIHJlc3BvbnNlLnJldE1hcCk7CiAgICAgICAgICBjb21taXQoJ1NFVF9ESUNUSU9OQVJZX1RSRUUnLCBhcnJUb1RyZWVEaWN0aW9uYXJ5KHsKICAgICAgICAgICAgVU5JRlk6IHJlc3BvbnNlLnJldE1hcC5kaWN0aW9uYXJ5LlVOSUZZCiAgICAgICAgICB9KSk7CiAgICAgICAgfSBlbHNlIHsKICAgICAgICAgIC8vIOeZu+W9leivt+axgijlvZNsb2NhbFN0b3JhZ2Xph4zmnInlrZflhbjml7bnm7TmjqXlj5Zsb2NhbFN0b3JhZ2Xph4znmoQgICDkuI3lho3lj5Hor7fmsYLmi7/mi7/lrZflhbgpCgogICAgICAgICAgLy8g5pu05pawZmllbGRUSW1lIOS4jeabtOaWsGxvY2Fsc3RvcmXnvJPlrZjvvIzlr7zoh7TkuIvmrKHnmbvlvZXvvIxmaWVsZFRpbWXmmK/mnIDmlrDvvIzmlbDmja7lrZflhbjkuI3mmK/mnIDmlrDnmoQKICAgICAgICAgIHZhciBVTklGWSA9IEpTT04ucGFyc2UobG9jYWxTdG9yYWdlLmdldEl0ZW0oJ1VOSUZZJykpOwogICAgICAgICAgdmFyIEFPUyA9IEpTT04ucGFyc2UobG9jYWxTdG9yYWdlLmdldEl0ZW0oJ0FPUycpKTsKICAgICAgICAgIHZhciBVTklGWWRpY3Rpb25hcnkgPSBKU09OLnBhcnNlKGxvY2FsU3RvcmFnZS5nZXRJdGVtKCdVTklGWWRpY3Rpb25hcnknKSk7CiAgICAgICAgICAvLyDmm7TmlrAgVU5JRlkKICAgICAgICAgIGlmICghY29tbW9uQmxhbmsocmVzcG9uc2UucmV0TWFwLlVOSUZZKSkgewogICAgICAgICAgICBmb3IgKHZhciBkaWN0S0VZIGluIHJlc3BvbnNlLnJldE1hcC5VTklGWSkgewogICAgICAgICAgICAgIFVOSUZZW2RpY3RLRVldID0gcmVzcG9uc2UucmV0TWFwLlVOSUZZW2RpY3RLRVldOwogICAgICAgICAgICB9CiAgICAgICAgICAgIGxvY2FsU3RvcmFnZS5zZXRJdGVtKCdVTklGWScsIEpTT04uc3RyaW5naWZ5KFVOSUZZKSk7CiAgICAgICAgICB9CiAgICAgICAgICAvLyDmm7TmlrAgVU5JRllkaWN0aW9uYXJ5CiAgICAgICAgICBpZiAoIWNvbW1vbkJsYW5rKHJlc3BvbnNlLnJldE1hcC5kaWN0aW9uYXJ5LlVOSUZZKSkgewogICAgICAgICAgICBVTklGWWRpY3Rpb25hcnkgPSBPYmplY3QuYXNzaWduKHt9LCBVTklGWWRpY3Rpb25hcnksIHJlc3BvbnNlLnJldE1hcC5kaWN0aW9uYXJ5KTsKICAgICAgICAgICAgbG9jYWxTdG9yYWdlLnNldEl0ZW0oJ1VOSUZZZGljdGlvbmFyeScsIEpTT04uc3RyaW5naWZ5KFVOSUZZZGljdGlvbmFyeSkpOwogICAgICAgICAgfQogICAgICAgICAgLy8g5pu05pawIEFPUwogICAgICAgICAgaWYgKCFjb21tb25CbGFuayhyZXNwb25zZS5yZXRNYXAuQU9TKSkgewogICAgICAgICAgICBmb3IgKHZhciBfZGljdEtFWSBpbiByZXNwb25zZS5yZXRNYXAuQU9TKSB7CiAgICAgICAgICAgICAgQU9TW19kaWN0S0VZXSA9IHJlc3BvbnNlLnJldE1hcC5BT1NbX2RpY3RLRVldOwogICAgICAgICAgICB9CiAgICAgICAgICAgIGxvY2FsU3RvcmFnZS5zZXRJdGVtKCdBT1MnLCBKU09OLnN0cmluZ2lmeShBT1MpKTsKICAgICAgICAgIH0KICAgICAgICAgIGNvbW1pdCgnU0VUX0RJQ1RJT05BUllMRVQnLCB7CiAgICAgICAgICAgIFVOSUZZOiBVTklGWSwKICAgICAgICAgICAgQU9TOiBBT1MsCiAgICAgICAgICAgIGRpY3Rpb25hcnk6IEpTT04ucGFyc2UobG9jYWxTdG9yYWdlLmdldEl0ZW0oJ1VOSUZZZGljdGlvbmFyeScpKSwKICAgICAgICAgICAgZmllbGRUaW1lOiBsb2NhbFN0b3JhZ2UuZ2V0SXRlbSgnZmllbGRUaW1lJykKICAgICAgICAgIH0pOwogICAgICAgICAgY29tbWl0KCdTRVRfRElDVElPTkFSWV9UUkVFJywgYXJyVG9UcmVlRGljdGlvbmFyeShKU09OLnBhcnNlKGxvY2FsU3RvcmFnZS5nZXRJdGVtKCdVTklGWWRpY3Rpb25hcnknKSkpKTsKICAgICAgICB9CiAgICAgIH0pOwogICAgfQogIH0sCiAgLy8g5Yqg6L295aSW6YOo5pWw5o2u5a2X5YW46KGoCiAgc2V0RXh0ZXJuYWxEYXRhOiBmdW5jdGlvbiBzZXRFeHRlcm5hbERhdGEoX3JlZjE0LCBkYXRhLCBsb2dvdHlwZSkgewogICAgdmFyIGNvbW1pdCA9IF9yZWYxNC5jb21taXQsCiAgICAgIHN0YXRlID0gX3JlZjE0LnN0YXRlOwogICAgcmV0dXJuIG5ldyBQcm9taXNlKGZ1bmN0aW9uIChyZXNvbHZlLCByZWplY3QpIHsKICAgICAgaWYgKGRhdGEuaW5kZXhPZignWk5QQicpICE9PSAtMSkgewogICAgICAgIGlmICh0eXBlb2YgZGF0YSA9PT0gJ3N0cmluZycgJiYgZGF0YS5jb25zdHJ1Y3RvciA9PT0gU3RyaW5nICYmIGRhdGEgIT09ICcnKSB7CiAgICAgICAgICB2YXIgbXNnID0gewogICAgICAgICAgICBwYXJhbWV0ZXJMaXN0OiBbXSwKICAgICAgICAgICAgb3Blcl90eXBlOiAnZXh0ZXJuYWxEYXRhJywKICAgICAgICAgICAga2V5OiBkYXRhCiAgICAgICAgICB9OwogICAgICAgICAgZXh0ZW5kU291cmNlKG1zZykudGhlbihmdW5jdGlvbiAocmVzRGF0YSkgewogICAgICAgICAgICAvLyDoh6rlrprkuYnlpJbooajmlbDmja7mupAg6YCa6L+H5pa55rOV5ZCN6I635Y+WCiAgICAgICAgICAgIHZhciBtZXRob2QgPSByZXNEYXRhLnJldE1hcC5tZXRob2Q7CiAgICAgICAgICAgIHZhciBtb2R1bGVWdWUgPSBnZXRNb2R1bGVWdWVCeU5hbWUobWV0aG9kKS5tZXRob2RzOyAvLyDpgJrov4dtZXRob2Tmlrnms5XlkI3mi7/liLDlr7nlupTnmoTlr7nosaEKICAgICAgICAgICAgZm9yICh2YXIga2V5IGluIG1vZHVsZVZ1ZSkgewogICAgICAgICAgICAgIGlmIChtZXRob2QgPT09IGtleSkgewogICAgICAgICAgICAgICAgLy8g6L+U5ZueIOWvueW6lOeahOWHveaVsCAgbW9kdWxlVnVlW2tleV0oKemAmui/h+iwg+eUqOivpeaWueazleaLv+WIsOi/lOWbnuWAvAogICAgICAgICAgICAgICAgdmFyIHJldE9iaiA9IG1vZHVsZVZ1ZVtrZXldKCk7CiAgICAgICAgICAgICAgICBjb21taXQoJ1NFVF9FWFRFUk5BTF9EQVRBJywgW2RhdGEsIHJldE9ial0pOwogICAgICAgICAgICAgICAgcmVzb2x2ZShyZXRPYmopOwogICAgICAgICAgICAgIH0KICAgICAgICAgICAgfQogICAgICAgICAgfSk7CiAgICAgICAgfQogICAgICB9IGVsc2UgewogICAgICAgIGlmIChzdGF0ZS5leHRlcm5hbERhdGFbZGF0YV0pIHsKICAgICAgICAgIHJlc29sdmUoc3RhdGUuZXh0ZXJuYWxEYXRhW2RhdGFdKTsKICAgICAgICB9IGVsc2UgewogICAgICAgICAgLy8gY29tbW9uQW9zRm9ybWF0RXh0ZXJTb3VyY2UoZGF0YSkudGhlbigocmVzKSA9PiB7CiAgICAgICAgICAvLyAgIGNvbW1pdCgnU0VUX0VYVEVSTkFMX0RBVEEnLCBbZGF0YSwgcmVzXSkKICAgICAgICAgIC8vICAgY29uc29sZS5sb2coJzIyMjIyMjIyMjIyMjIyMjIyMicsIHJlcykKICAgICAgICAgIC8vICAgcmVzb2x2ZShyZXMpCiAgICAgICAgICAvLyB9KQogICAgICAgICAgaWYgKHR5cGVvZiBkYXRhID09PSAnc3RyaW5nJyAmJiBkYXRhLmNvbnN0cnVjdG9yID09PSBTdHJpbmcgJiYgZGF0YSAhPT0gJycpIHsKICAgICAgICAgICAgdmFyIF9tc2cgPSB7CiAgICAgICAgICAgICAgcGFyYW1ldGVyTGlzdDogW10sCiAgICAgICAgICAgICAgb3Blcl90eXBlOiAnZXh0ZXJuYWxEYXRhJywKICAgICAgICAgICAgICBrZXk6IGRhdGEKICAgICAgICAgICAgfTsKICAgICAgICAgICAgZXh0ZW5kU291cmNlKF9tc2cpLnRoZW4oZnVuY3Rpb24gKHJlc0RhdGEpIHsKICAgICAgICAgICAgICB2YXIgbGlzdCA9IHJlc0RhdGEucmV0TWFwLmxpc3Q7CiAgICAgICAgICAgICAgaWYgKGRhdGEuaW5kZXhPZignLCcpICE9PSAtMSkgewogICAgICAgICAgICAgICAgLy8g5aSa5Liq5aSW6KGo5pWw5o2u5rqQ5pWw5o2u5LiA6LW35p+l6K+iCiAgICAgICAgICAgICAgICB2YXIgZGF0YXMgPSBkYXRhLnNwbGl0KCcsJyk7CiAgICAgICAgICAgICAgICBpZiAoIWNvbW1vbkJsYW5rKGxpc3QpKSB7CiAgICAgICAgICAgICAgICAgIHZhciBsaXN0QyA9IHt9OwogICAgICAgICAgICAgICAgICB2YXIgX2l0ZXJhdG9yNSA9IF9jcmVhdGVGb3JPZkl0ZXJhdG9ySGVscGVyKGRhdGFzLmVudHJpZXMoKSksCiAgICAgICAgICAgICAgICAgICAgX3N0ZXA1OwogICAgICAgICAgICAgICAgICB0cnkgewogICAgICAgICAgICAgICAgICAgIGZvciAoX2l0ZXJhdG9yNS5zKCk7ICEoX3N0ZXA1ID0gX2l0ZXJhdG9yNS5uKCkpLmRvbmU7KSB7CiAgICAgICAgICAgICAgICAgICAgICB2YXIgX3N0ZXA1JHZhbHVlID0gX3NsaWNlZFRvQXJyYXkoX3N0ZXA1LnZhbHVlLCAyKSwKICAgICAgICAgICAgICAgICAgICAgICAgaW5kZXggPSBfc3RlcDUkdmFsdWVbMF0sCiAgICAgICAgICAgICAgICAgICAgICAgIGl0ZW0gPSBfc3RlcDUkdmFsdWVbMV07CiAgICAgICAgICAgICAgICAgICAgICB2YXIgbGlzdEluZGV4ID0gJ2xpc3QnICsgaW5kZXg7CiAgICAgICAgICAgICAgICAgICAgICBsaXN0Q1tpdGVtXSA9IGxpc3RbbGlzdEluZGV4XS5mb3JFYWNoKGZ1bmN0aW9uIChpdGVtMikgewogICAgICAgICAgICAgICAgICAgICAgICBpdGVtMi5sYWJlbCA9IGl0ZW0yLnZhbHVlOwogICAgICAgICAgICAgICAgICAgICAgICBpdGVtMi52YWx1ZSA9IGl0ZW0yLmtleTsKICAgICAgICAgICAgICAgICAgICAgIH0pOwogICAgICAgICAgICAgICAgICAgIH0KICAgICAgICAgICAgICAgICAgfSBjYXRjaCAoZXJyKSB7CiAgICAgICAgICAgICAgICAgICAgX2l0ZXJhdG9yNS5lKGVycik7CiAgICAgICAgICAgICAgICAgIH0gZmluYWxseSB7CiAgICAgICAgICAgICAgICAgICAgX2l0ZXJhdG9yNS5mKCk7CiAgICAgICAgICAgICAgICAgIH0KICAgICAgICAgICAgICAgICAgY29tbWl0KCdTRVRfRVhURVJOQUxfREFUQScsIFtkYXRhLCBsaXN0Q10pOwogICAgICAgICAgICAgICAgICByZXNvbHZlKGxpc3RDKTsKICAgICAgICAgICAgICAgIH0KICAgICAgICAgICAgICB9IGVsc2UgewogICAgICAgICAgICAgICAgaWYgKCFjb21tb25CbGFuayhsaXN0KSkgewogICAgICAgICAgICAgICAgICBsaXN0LmZvckVhY2goZnVuY3Rpb24gKGl0ZW0pIHsKICAgICAgICAgICAgICAgICAgICBpdGVtLmxhYmVsID0gaXRlbS52YWx1ZTsKICAgICAgICAgICAgICAgICAgICBpdGVtLnZhbHVlID0gaXRlbS5rZXk7CiAgICAgICAgICAgICAgICAgIH0pOwogICAgICAgICAgICAgICAgICBjb21taXQoJ1NFVF9FWFRFUk5BTF9EQVRBJywgW2RhdGEsIGxpc3RdKTsKICAgICAgICAgICAgICAgICAgcmVzb2x2ZShsaXN0KTsKICAgICAgICAgICAgICAgIH0gZWxzZSB7CiAgICAgICAgICAgICAgICAgIC8vIOiHquWumuS5ieWkluihqOaVsOaNrua6kCDpgJrov4fmlrnms5XlkI3ojrflj5YKICAgICAgICAgICAgICAgICAgdmFyIG1ldGhvZCA9IHJlc0RhdGEucmV0TWFwLm1ldGhvZDsKICAgICAgICAgICAgICAgICAgdmFyIG1vZHVsZVZ1ZSA9IGdldE1vZHVsZVZ1ZUJ5TmFtZShtZXRob2QpLm1ldGhvZHM7IC8vIOmAmui/h21ldGhvZOaWueazleWQjeaLv+WIsOWvueW6lOeahOWvueixoQogICAgICAgICAgICAgICAgICBmb3IgKHZhciBrZXkgaW4gbW9kdWxlVnVlKSB7CiAgICAgICAgICAgICAgICAgICAgaWYgKG1ldGhvZCA9PT0ga2V5KSB7CiAgICAgICAgICAgICAgICAgICAgICAvLyDov5Tlm54g5a+55bqU55qE5Ye95pWwICBtb2R1bGVWdWVba2V5XSgp6YCa6L+H6LCD55So6K+l5pa55rOV5ou/5Yiw6L+U5Zue5YC8CiAgICAgICAgICAgICAgICAgICAgICB2YXIgcmV0T2JqID0gbW9kdWxlVnVlW2tleV0oKTsKICAgICAgICAgICAgICAgICAgICAgIGNvbW1pdCgnU0VUX0VYVEVSTkFMX0RBVEEnLCBbZGF0YSwgcmV0T2JqXSk7CiAgICAgICAgICAgICAgICAgICAgICByZXNvbHZlKHJldE9iaik7CiAgICAgICAgICAgICAgICAgICAgfQogICAgICAgICAgICAgICAgICB9CiAgICAgICAgICAgICAgICB9CiAgICAgICAgICAgICAgfQogICAgICAgICAgICB9KTsKICAgICAgICAgIH0KICAgICAgICB9CiAgICAgIH0KICAgIH0pOwogIH0sCiAgLy8g6K6+572u5Li76aG16buY6K6k5pi+56S65qih5Z2XCiAgc2V0SG9tZU1vdWRsZXM6IGZ1bmN0aW9uIHNldEhvbWVNb3VkbGVzKF9yZWYxNSwgZGF0YSkgewogICAgdmFyIGNvbW1pdCA9IF9yZWYxNS5jb21taXQsCiAgICAgIHN0YXRlID0gX3JlZjE1LnN0YXRlOwogICAgY29tbWl0KCdTRVRfSE9NRV9NT1VETEVTJywgZGF0YS5kZWZhdWx0KTsKICAgIGNvbW1pdCgnU0VUX0hPTUVfTU9VRExFU19DSEVDS0VEJywgZGF0YS5jaGVja2VkKTsKICAgIGNvbW1pdCgnU0VUX0hPTUVfTU9VRExFU19ERUZBVUxUJywgZGF0YS5kZWZhdWx0Q2hlY2tlZCk7CiAgfQp9OwpleHBvcnQgZGVmYXVsdCB7CiAgbmFtZXNwYWNlZDogdHJ1ZSwKICBzdGF0ZTogc3RhdGUsCiAgbXV0YXRpb25zOiBtdXRhdGlvbnMsCiAgYWN0aW9uczogYWN0aW9ucwp9Ow=="}, null]}