{"remainingRequest": "E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-q<PERSON><PERSON>n\\数字运营平台-统一门户工程\\dopUnify\\node_modules\\thread-loader\\dist\\cjs.js!E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan\\数字运营平台-统一门户工程\\dopUnify\\node_modules\\babel-loader\\lib\\index.js!E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan\\数字运营平台-统一门户工程\\dopUnify\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan\\数字运营平台-统一门户工程\\dopUnify\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-q<PERSON>duan\\数字运营平台-统一门户工程\\dopUnify\\src\\views\\login\\index.vue?vue&type=script&lang=js&", "dependencies": [{"path": "E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-<PERSON><PERSON><PERSON>n\\数字运营平台-统一门户工程\\dopUnify\\src\\views\\login\\index.vue", "mtime": 1705301835617}, {"path": "E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-<PERSON><PERSON><PERSON>n\\数字运营平台-统一门户工程\\dopUnify\\babel.config.js", "mtime": 1686019818359}, {"path": "E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-<PERSON><PERSON><PERSON>n\\数字运营平台-统一门户工程\\dopUnify\\.babelrc", "mtime": 1705285078668}, {"path": "E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-<PERSON><PERSON><PERSON>n\\数字运营平台-统一门户工程\\dopUnify\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-<PERSON><PERSON><PERSON>n\\数字运营平台-统一门户工程\\dopUnify\\node_modules\\thread-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-<PERSON><PERSON><PERSON>n\\数字运营平台-统一门户工程\\dopUnify\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-<PERSON><PERSON><PERSON>n\\数字运营平台-统一门户工程\\dopUnify\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-<PERSON><PERSON><PERSON>n\\数字运营平台-统一门户工程\\dopUnify\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1655715099000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, null]}