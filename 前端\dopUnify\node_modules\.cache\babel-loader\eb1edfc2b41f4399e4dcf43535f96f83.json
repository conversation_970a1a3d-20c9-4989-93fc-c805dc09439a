{"remainingRequest": "D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\node_modules\\babel-loader\\lib\\index.js!D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\node_modules\\eslint-loader\\index.js??ref--13-0!D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\src\\views\\system\\dailyManage\\log\\info.js", "dependencies": [{"path": "D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\src\\views\\system\\dailyManage\\log\\info.js", "mtime": 1686019808451}, {"path": "D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\babel.config.js", "mtime": 1686019818359}, {"path": "D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\.babelrc", "mtime": 1705285078668}, {"path": "D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\node_modules\\eslint-loader\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8g6KGo5Y2VCmV4cG9ydCB2YXIgY29uZmlnID0gZnVuY3Rpb24gY29uZmlnKHRoYXQpIHsKICByZXR1cm4gewogICAgb3JnYW5fbm86IHsKICAgICAgY29tcG9uZW50OiAnc2VsZWN0LXRyZWUnLAogICAgICBsYWJlbDogJ+acuuaehOWPtycsCiAgICAgIGNvbFNwYW46IDgsCiAgICAgIGNvbmZpZzogewogICAgICAgIC8vIGZvcm0taXRlbSDphY3nva4KICAgICAgICBydWxlczogW3sKICAgICAgICAgIG1pbjogMCwKICAgICAgICAgIG1heDogMTAsCiAgICAgICAgICBtZXNzYWdlOiAn6K+35pyA5aSa5aGr5YaZMTDkuKrlrZfnrKYnCiAgICAgICAgfV0KICAgICAgfSwKICAgICAgbmFtZTogJ29yZ2FuX25vJywKICAgICAgY29tcG9uZW50UHJvcHM6IHsKICAgICAgICAvLyBpbnB1dOe7hOS7tumFjee9rgogICAgICAgIGNsZWFyYWJsZTogdHJ1ZQogICAgICB9LAogICAgICBvcHRpb25zOiBbXQogICAgfSwKICAgIHVzZXJfbm86IHsKICAgICAgY29tcG9uZW50OiAnaW5wdXQnLAogICAgICBsYWJlbDogJ+aTjeS9nOeUqOaItycsCiAgICAgIGNvbFNwYW46IDgsCiAgICAgIGNvbmZpZzogewogICAgICAgIC8vIGZvcm0taXRlbSDphY3nva4KICAgICAgICBydWxlczogW3sKICAgICAgICAgIG1pbjogMCwKICAgICAgICAgIG1heDogNTAsCiAgICAgICAgICBtZXNzYWdlOiAn6K+35pyA5aSa5aGr5YaZNTDkuKrlrZfnrKYnCiAgICAgICAgfV0KICAgICAgfSwKICAgICAgbmFtZTogJ3VzZXJfbm8nLAogICAgICBjb21wb25lbnRQcm9wczogewogICAgICAgIGNsZWFyYWJsZTogdHJ1ZSwKICAgICAgICBwbGFjZWhvbGRlcjogJ+aUr+aMgeaTjeS9nOeUqOaIt+aooeeziuafpeivoicKICAgICAgfQogICAgfSwKICAgIGNvbnRlbnQ6IHsKICAgICAgY29tcG9uZW50OiAnaW5wdXQnLAogICAgICBsYWJlbDogJ+aTjeS9nOWGheWuuScsCiAgICAgIGNvbFNwYW46IDgsCiAgICAgIGNvbmZpZzogewogICAgICAgIC8vIGZvcm0taXRlbSDphY3nva4KICAgICAgICBydWxlczogW3sKICAgICAgICAgIG1pbjogMCwKICAgICAgICAgIG1heDogMzQwLAogICAgICAgICAgbWVzc2FnZTogJ+ivt+acgOWkmuWhq+WGmTM0MOS4quWtl+espicKICAgICAgICB9XQogICAgICB9LAogICAgICBuYW1lOiAnY29udGVudCcsCiAgICAgIGNvbXBvbmVudFByb3BzOiB7CiAgICAgICAgY2xlYXJhYmxlOiB0cnVlLAogICAgICAgIHBsYWNlaG9sZGVyOiAn5pSv5oyB5pON5L2c5YaF5a655qih57OK5p+l6K+iJwogICAgICB9CiAgICB9LAogICAgb3Blcl9tb2R1bGU6IHsKICAgICAgY29tcG9uZW50OiAnaW5wdXQnLAogICAgICBsYWJlbDogJ+aTjeS9nOaooeWdlycsCiAgICAgIGNvbFNwYW46IDgsCiAgICAgIGNvbmZpZzogewogICAgICAgIC8vIGZvcm0taXRlbSDphY3nva4KICAgICAgICBydWxlczogW3sKICAgICAgICAgIG1pbjogMCwKICAgICAgICAgIG1heDogMzAsCiAgICAgICAgICBtZXNzYWdlOiAn6K+35pyA5aSa5aGr5YaZMzDkuKrlrZfnrKYnCiAgICAgICAgfV0KICAgICAgfSwKICAgICAgbmFtZTogJ29wZXJfbW9kdWxlJywKICAgICAgY29tcG9uZW50UHJvcHM6IHsKICAgICAgICBjbGVhcmFibGU6IHRydWUsCiAgICAgICAgcGxhY2Vob2xkZXI6ICfmlK/mjIHmk43kvZzmqKHlnZfmqKHns4rmn6Xor6InCiAgICAgIH0KICAgIH0sCiAgICBkYXRlX3R3bzogewogICAgICBjb21wb25lbnQ6ICdkYXRlLXBpY2tlcicsCiAgICAgIGxhYmVsOiAn5pel5pyf5Yy66Ze0JywKICAgICAgY29sU3BhbjogOCwKICAgICAgbmFtZTogJ2RhdGVfdHdvJywKICAgICAgY29uZmlnOiB7fSwKICAgICAgY29tcG9uZW50UHJvcHM6IHsKICAgICAgICB0eXBlOiAnZGF0ZXJhbmdlJywKICAgICAgICByYW5nZVNlcGFyYXRvcjogJy0nLAogICAgICAgIHN0YXJ0UGxhY2Vob2xkZXI6ICflvIDlp4vml6XmnJ8nLAogICAgICAgIGVuZFBsYWNlaG9sZGVyOiAn57uT5p2f5pel5pyfJywKICAgICAgICB2YWx1ZUZvcm1hdDogJ3l5eXlNTWRkJywKICAgICAgICBlZGl0YWJsZTogZmFsc2UKICAgICAgICAvLyB0eXBlOiAneWVhcicKICAgICAgfQogICAgfSwKCiAgICBvcGVyX3R5cGU6IHsKICAgICAgY29tcG9uZW50OiAnaW5wdXQnLAogICAgICBsYWJlbDogJ+aTjeS9nOexu+WeiycsCiAgICAgIGNvbFNwYW46IDgsCiAgICAgIGNvbmZpZzogewogICAgICAgIC8vIGZvcm0taXRlbSDphY3nva4KICAgICAgICBydWxlczogW3sKICAgICAgICAgIG1pbjogMCwKICAgICAgICAgIG1heDogNTAsCiAgICAgICAgICBtZXNzYWdlOiAn6K+35pyA5aSa5aGr5YaZNTDkuKrlrZfnrKYnCiAgICAgICAgfV0KICAgICAgfSwKICAgICAgbmFtZTogJ29wZXJfdHlwZScsCiAgICAgIGNvbXBvbmVudFByb3BzOiB7CiAgICAgICAgY2xlYXJhYmxlOiB0cnVlLAogICAgICAgIHBsYWNlaG9sZGVyOiAn5pSv5oyB5pON5L2c57G75Z6L5qih57OK5p+l6K+iJwogICAgICB9CiAgICB9CiAgfTsKfTs="}, {"version": 3, "names": ["config", "that", "organ_no", "component", "label", "colSpan", "rules", "min", "max", "message", "name", "componentProps", "clearable", "options", "user_no", "placeholder", "content", "oper_module", "date_two", "type", "rangeSeparator", "startPlaceholder", "endPlaceholder", "valueFormat", "editable", "oper_type"], "sources": ["D:/1_Project/XYD_Project/dop-4.0/dop-4.1-qian<PERSON>n-unify/dopUnify/src/views/system/dailyManage/log/info.js"], "sourcesContent": ["// 表单\r\nexport const config = (that) => ({\r\n  organ_no: {\r\n    component: 'select-tree',\r\n    label: '机构号',\r\n    colSpan: 8,\r\n    config: {\r\n      // form-item 配置\r\n      rules: [{ min: 0, max: 10, message: '请最多填写10个字符' }]\r\n    },\r\n    name: 'organ_no',\r\n    componentProps: {\r\n      // input组件配置\r\n      clearable: true\r\n    },\r\n    options: []\r\n  },\r\n  user_no: {\r\n    component: 'input',\r\n    label: '操作用户',\r\n    colSpan: 8,\r\n    config: {\r\n      // form-item 配置\r\n      rules: [{ min: 0, max: 50, message: '请最多填写50个字符' }]\r\n    },\r\n    name: 'user_no',\r\n    componentProps: {\r\n      clearable: true,\r\n      placeholder: '支持操作用户模糊查询'\r\n    }\r\n  },\r\n  content: {\r\n    component: 'input',\r\n    label: '操作内容',\r\n    colSpan: 8,\r\n    config: {\r\n      // form-item 配置\r\n      rules: [{ min: 0, max: 340, message: '请最多填写340个字符' }]\r\n    },\r\n    name: 'content',\r\n    componentProps: {\r\n      clearable: true,\r\n      placeholder: '支持操作内容模糊查询'\r\n    }\r\n  },\r\n  oper_module: {\r\n    component: 'input',\r\n    label: '操作模块',\r\n    colSpan: 8,\r\n    config: {\r\n      // form-item 配置\r\n      rules: [{ min: 0, max: 30, message: '请最多填写30个字符' }]\r\n    },\r\n    name: 'oper_module',\r\n    componentProps: {\r\n      clearable: true,\r\n      placeholder: '支持操作模块模糊查询'\r\n    }\r\n  },\r\n  date_two: {\r\n    component: 'date-picker',\r\n    label: '日期区间',\r\n    colSpan: 8,\r\n    name: 'date_two',\r\n    config: {},\r\n    componentProps: {\r\n      type: 'daterange',\r\n      rangeSeparator: '-',\r\n      startPlaceholder: '开始日期',\r\n      endPlaceholder: '结束日期',\r\n      valueFormat: 'yyyyMMdd',\r\n      editable: false\r\n      // type: 'year'\r\n    }\r\n  },\r\n  oper_type: {\r\n    component: 'input',\r\n    label: '操作类型',\r\n    colSpan: 8,\r\n    config: {\r\n      // form-item 配置\r\n      rules: [{ min: 0, max: 50, message: '请最多填写50个字符' }]\r\n    },\r\n    name: 'oper_type',\r\n    componentProps: {\r\n      clearable: true,\r\n      placeholder: '支持操作类型模糊查询'\r\n    }\r\n  }\r\n})\r\n"], "mappings": "AAAA;AACA,OAAO,IAAMA,MAAM,GAAG,SAATA,MAAM,CAAIC,IAAI;EAAA,OAAM;IAC/BC,QAAQ,EAAE;MACRC,SAAS,EAAE,aAAa;MACxBC,KAAK,EAAE,KAAK;MACZC,OAAO,EAAE,CAAC;MACVL,MAAM,EAAE;QACN;QACAM,KAAK,EAAE,CAAC;UAAEC,GAAG,EAAE,CAAC;UAAEC,GAAG,EAAE,EAAE;UAAEC,OAAO,EAAE;QAAa,CAAC;MACpD,CAAC;MACDC,IAAI,EAAE,UAAU;MAChBC,cAAc,EAAE;QACd;QACAC,SAAS,EAAE;MACb,CAAC;MACDC,OAAO,EAAE;IACX,CAAC;IACDC,OAAO,EAAE;MACPX,SAAS,EAAE,OAAO;MAClBC,KAAK,EAAE,MAAM;MACbC,OAAO,EAAE,CAAC;MACVL,MAAM,EAAE;QACN;QACAM,KAAK,EAAE,CAAC;UAAEC,GAAG,EAAE,CAAC;UAAEC,GAAG,EAAE,EAAE;UAAEC,OAAO,EAAE;QAAa,CAAC;MACpD,CAAC;MACDC,IAAI,EAAE,SAAS;MACfC,cAAc,EAAE;QACdC,SAAS,EAAE,IAAI;QACfG,WAAW,EAAE;MACf;IACF,CAAC;IACDC,OAAO,EAAE;MACPb,SAAS,EAAE,OAAO;MAClBC,KAAK,EAAE,MAAM;MACbC,OAAO,EAAE,CAAC;MACVL,MAAM,EAAE;QACN;QACAM,KAAK,EAAE,CAAC;UAAEC,GAAG,EAAE,CAAC;UAAEC,GAAG,EAAE,GAAG;UAAEC,OAAO,EAAE;QAAc,CAAC;MACtD,CAAC;MACDC,IAAI,EAAE,SAAS;MACfC,cAAc,EAAE;QACdC,SAAS,EAAE,IAAI;QACfG,WAAW,EAAE;MACf;IACF,CAAC;IACDE,WAAW,EAAE;MACXd,SAAS,EAAE,OAAO;MAClBC,KAAK,EAAE,MAAM;MACbC,OAAO,EAAE,CAAC;MACVL,MAAM,EAAE;QACN;QACAM,KAAK,EAAE,CAAC;UAAEC,GAAG,EAAE,CAAC;UAAEC,GAAG,EAAE,EAAE;UAAEC,OAAO,EAAE;QAAa,CAAC;MACpD,CAAC;MACDC,IAAI,EAAE,aAAa;MACnBC,cAAc,EAAE;QACdC,SAAS,EAAE,IAAI;QACfG,WAAW,EAAE;MACf;IACF,CAAC;IACDG,QAAQ,EAAE;MACRf,SAAS,EAAE,aAAa;MACxBC,KAAK,EAAE,MAAM;MACbC,OAAO,EAAE,CAAC;MACVK,IAAI,EAAE,UAAU;MAChBV,MAAM,EAAE,CAAC,CAAC;MACVW,cAAc,EAAE;QACdQ,IAAI,EAAE,WAAW;QACjBC,cAAc,EAAE,GAAG;QACnBC,gBAAgB,EAAE,MAAM;QACxBC,cAAc,EAAE,MAAM;QACtBC,WAAW,EAAE,UAAU;QACvBC,QAAQ,EAAE;QACV;MACF;IACF,CAAC;;IACDC,SAAS,EAAE;MACTtB,SAAS,EAAE,OAAO;MAClBC,KAAK,EAAE,MAAM;MACbC,OAAO,EAAE,CAAC;MACVL,MAAM,EAAE;QACN;QACAM,KAAK,EAAE,CAAC;UAAEC,GAAG,EAAE,CAAC;UAAEC,GAAG,EAAE,EAAE;UAAEC,OAAO,EAAE;QAAa,CAAC;MACpD,CAAC;MACDC,IAAI,EAAE,WAAW;MACjBC,cAAc,EAAE;QACdC,SAAS,EAAE,IAAI;QACfG,WAAW,EAAE;MACf;IACF;EACF,CAAC;AAAA,CAAC"}]}