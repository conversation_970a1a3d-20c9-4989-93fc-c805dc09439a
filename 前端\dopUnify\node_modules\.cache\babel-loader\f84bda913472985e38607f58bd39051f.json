{"remainingRequest": "D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\node_modules\\babel-loader\\lib\\index.js!D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\src\\views\\system\\dataAuditing\\component\\table\\index.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\src\\views\\system\\dataAuditing\\component\\table\\index.vue", "mtime": 1686019808310}, {"path": "D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\babel.config.js", "mtime": 1686019818359}, {"path": "D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\.babelrc", "mtime": 1705285078668}, {"path": "D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1655715099000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAoHA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;EACAA;EACAC;IAAAC;EAAA;EACAC;EACAC;IACAC;MACAC;MACAC;QACA;MACA;IACA;IACAC;MACAF;MACAC;QACA;MACA;IACA;EACA;EACAE;IACA;MACA;MACAC;MACAC;QACA;QACAC;QAAA;QACAC;QACAC;QAAA;QACAC;QAAA;QACAC;QACAC;UACAR;UAAA;UACAS;UACAC;QACA;;QACAC;UACAC;UACAC;UAAA;UACAC;QACA;MACA;;MACAC;MACAC;QAAAC;MAAA;MACAC;QACAD;MACA;MACA;MACAE;MAAA;MACAC;QACA;QACAC;QACAC;MACA;MACAC;MAAA;MACAC;QACA;QACArB;QAAA;QACAE;QAAA;QACAC;QAAA;QACAC;QACAC;UACAR;UAAA;UACAS;UACAC;QACA;;QACAC;UACAC;UACAC;UAAA;UACAC;QACA;MACA;;MACA;MACAW;QACA;QACAJ;QACAb;UACA;UACAc;UACAI;QACA;;QACAC;UACAC;UACAC;UACAjC;YACAkC;YAAA;YACAC;UACA;QACA;MACA;;MACAC;MAAA;MACAC;MAAA;MACAC;MAAA;MACAC;MAAA;MACA;MACAC;QACA;QACAf;QACAb;UACA;UACAc;UAAA;UACAI;QACA;;QACAW;QACAC;QAAA;QACAC;UACA;UACApC;UAAA;UACAE;UAAA;UACAC;UAAA;UACAC;UACAC;YACAR;YAAA;YACAS;YACAC;UACA;;UACAC;YACAC;YACAC;YAAA;YACAC;UACA;;UACA0B;QACA;QACAC;QAAA;QACAC;QAAA;QACAC;QAAA;QACAR;MACA;IACA;EACA;;EACAS;IACArC;MACA;IACA;IACA;MACA;MACAsC;QACA;UACA;UACA,IACA,uCACA,+BACA;YACAC;UACA;QACA;MACA;MACAC;IACA;EACA;EACAC;IACA;EACA;EACAC;IAAA;IACA;IACA;MACA;QACA;MACA;IACA;EACA;EACA;EACAC;IACA;EACA;EACAC;IACA;AACA;IACAC;MAAA;MACA;MACA;QACAC;QACAC;QACAC;QACAC;QACAC;QACAC;QACAC;QACAC,kBACA,qCACA,6CACA,KACA;QAAA,EACA;QACAC,gBACA,qCACA,6CACA,KACA;QAAA,EACA;QACAhD;QACAiD;MACA;MACA;MACAC;QACA;UAAAC;UAAApD;UAAAC;QACA;QACA;QACA;QACA;UACA;UACA;YACA;YACA;cACA;YACA;YACA;cACA;cACA;gBACA;kBACAoD;oBACA;sBACAC;oBACA;kBACA;gBACA;cACA;YACA;UACA;YACA;YACAA;YACAA;UACA;QACA;QACA;MACA;IACA;IACA;AACA;AACA;AACA;IACAC;MACA;MACA;QACA;QACA,IACAC,6BACAA,+BACAA,+CACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;QACA;UACA;UACA;UACA;YACA;YACA;UACA;YACA;YACA;cACAC;cACA9E;YACA;YACA;UACA;QACA;MACA;IACA;IACA;AACA;AACA;AACA;IACA+E;MACA;MACA;QACAC;MACA;MACA;MACAA;MACAhF;MACA;QACAA;MACA;MACA;IACA;IACA;AACA;IACAiF;MACA;MACA;MACA;MACA;MACA;MACA;;MAEA,kCACA;QACAH;QACA9E;MACA,GACA;QACA8E;QACA9E;MACA,GACA;QACA8E;QACA9E;MACA,EACA;MACA;MACA;;MAEA;MACA;MACA;IACA;IACA;AACA;AACA;IACAkF;MACA;MACA;MACA;MACA;MACA;MACA;MACA;IACA;IACA;AACA;AACA;AACA;IACAC;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA;gBAAA,OACAC,qCACAP,KACA,8BACA;cAAA;gBAHAQ;gBAIA;kBACA;kBACA;kBACA;kBACA;kBACA;gBACA;kBACA;kBACAC;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IACA;IACA;AACA;AACA;IACAC;MACA;IACA;IACA;AACA;AACA;IACAC;MACA;IACA;IACA;AACA;IACAC;MACA;QAAAlE;MACA;MACA;MACA;IACA;IACA;AACA;IACAmE;MACA;IACA;EACA;AACA", "names": ["name", "components", "SunOperDetailDialog", "mixins", "props", "defaultForm", "type", "default", "btnAll", "data", "listLoading", "table", "tableColumns", "ref", "selection", "indexNumber", "loading", "componentProps", "height", "formRow", "pageList", "totalNum", "currentPage", "pageSize", "approve_agree2", "redColor", "color", "blueBlack", "aproveCurrentRow", "dialog2", "visible", "title", "errorMsg", "errDetail", "dialogFile", "width", "form", "labelWidth", "config", "file_url", "postscript", "fileList", "delFile", "fileRow", "btnId", "dialogOper", "queryType", "currentRow", "tableData", "showPage", "desData", "defaultForm2", "tableDetailData", "watch", "handler", "releaseFlowTask", "deep", "created", "mounted", "<PERSON><PERSON><PERSON><PERSON>", "methods", "queryList", "parameterList", "query_type", "approve_type", "approve_agree", "inst_id", "oper_type_flag", "apply_user", "start_apply_time", "end_apply_time", "operationRequestWorkbench_pageNum", "query", "list", "val", "item", "approveRest", "row", "label", "getAttachmentName", "index", "approveDisAgreeDetail", "downloadErrFile", "rowClick", "sysMessageOperationRequestDeatilShow", "detailData", "commonMsgInfo", "changeOperVisible", "dialog2Close", "getList", "showLoading"], "sourceRoot": "src/views/system/dataAuditing/component/table", "sources": ["index.vue"], "sourcesContent": ["/* * 数据审核工作台:表格 */\r\n<template>\r\n  <div class=\"sun-content\">\r\n    <sun-table :table-config=\"table\" @pagination=\"getList\">\r\n      <template slot=\"tableColumn\">\r\n        <el-table-column\r\n          v-for=\"item in table.tableColumns\"\r\n          :key=\"item.id\"\r\n          :prop=\"item.name\"\r\n          :label=\"item.label\"\r\n          :width=\"item.width\"\r\n        >\r\n          <div slot-scope=\"{ row }\">\r\n            <span\r\n              v-if=\"item.name === 'inst_id'\"\r\n              class=\"instId\"\r\n              @click=\"rowClick(row)\"\r\n            >{{ row[item.name] }}</span>\r\n            <span v-else-if=\"item.name === 'apply_time'\">{{\r\n              row[item.name] | dateTimeFormat\r\n            }}</span>\r\n            <span v-else-if=\"item.name === 'oper_type_flag'\">{{\r\n              row[item.name]\r\n            }}</span>\r\n            <span\r\n              v-else-if=\"\r\n                item.name === 'approve_agree' && row[item.name] !== '2'\r\n              \"\r\n              :style=\"row[item.name] === '0' ? redColor : blueBlack\"\r\n              @click=\"approveRest(row)\"\r\n            >{{\r\n              row[item.name] | commonFormatValue('SM_APPROVE_AGREE')\r\n            }}</span>\r\n            <span\r\n              v-else-if=\"\r\n                item.name === 'approve_agree' && row[item.name] === '2'\r\n              \"\r\n              style=\"color: red\"\r\n            >{{ approve_agree2 }}</span>\r\n            <span\r\n              v-else-if=\"item.name === 'current_approve_organ'\"\r\n              class=\"textOverflow\"\r\n              :title=\"row[item.name] | organNameFormat\"\r\n            >{{ row[item.name] | organNameFormat }}</span>\r\n            <span\r\n              v-else-if=\"item.name === 'role_no'\"\r\n              class=\"textOverflow\"\r\n              :title=\"row[item.name]\"\r\n            >{{ row[item.name] }}</span>\r\n            <span v-else>{{ row[item.name] }}</span>\r\n          </div>\r\n        </el-table-column>\r\n      </template>\r\n    </sun-table>\r\n    <!-- 审批结果点击弹框begin--------->\r\n    <el-dialog\r\n      class=\"detailDialog\"\r\n      :title=\"dialog2.title\"\r\n      :visible.sync=\"dialog2.visible\"\r\n      width=\"80%\"\r\n      @dialogClose=\"dialog2Close\"\r\n    >\r\n      <el-descriptions :column=\"3\">\r\n        <el-descriptions-item\r\n          v-for=\"item in dialogOper.defaultForm2\"\r\n          :key=\"item.name\"\r\n          :label=\"item.label\"\r\n        >\r\n          <span v-if=\"item.name === 'errorDetail'\" style=\"color: blue\">{{\r\n            errorMsg\r\n          }}</span>\r\n          <span\r\n            v-else-if=\"item.name === 'failData'\"\r\n            style=\"color: blue\"\r\n            @click=\"downloadErrFile(aproveCurrentRow)\"\r\n          >下载</span>\r\n          <span v-else>{{ dialogOper.desData[item.name] }}</span>\r\n        </el-descriptions-item>\r\n      </el-descriptions>\r\n      <!-- 处理明细 begin -->\r\n      <div\r\n        v-if=\"\r\n          aproveCurrentRow.approve_agree === '3' &&\r\n            aproveCurrentRow.oper_type_flag === '导入'\r\n        \"\r\n      >\r\n        <div class=\"title\">处理明细</div>\r\n        <sun-table :table-config=\"errDetail\">\r\n          <template slot=\"tableColumn\">\r\n            <el-table-column\r\n              v-for=\"item in errDetail.tableColumns\"\r\n              :key=\"item.id\"\r\n              :prop=\"item.name\"\r\n              :label=\"item.label\"\r\n              :width=\"item.width\"\r\n            >\r\n              <div slot-scope=\"{ row }\">\r\n                <span v-if=\"item.name === 'organ_no'\">{{\r\n                  row[item.name] | organNameFormat\r\n                }}</span>\r\n                <span v-else>{{ row[item.name] }}</span>\r\n              </div>\r\n            </el-table-column>\r\n          </template>\r\n        </sun-table>\r\n      </div>\r\n    </el-dialog>\r\n    <!--流程事项--操作申请弹框 begin -->\r\n    <SunOperDetailDialog\r\n      ref=\"refOper\"\r\n      :dialog-config=\"dialogOper\"\r\n      @dialogClose=\"changeOperVisible\"\r\n    />\r\n  </div>\r\n</template>\r\n<script>\r\nimport { configTable, config } from './info' // 表头、表单配置\r\nimport SunOperDetailDialog from '@/views/system/config/message/systemMsg/SunOperDetailDialog'\r\nimport { commonMsgInfo } from '@/utils/message.js' // 提示信息\r\nimport { commonBlank } from '@/utils/common'\r\nimport { releaseFlowTask } from '@/utils/flowPath.js'\r\nimport { sysMessageOperationRequestDeatilShow } from '@/utils/flowPath'\r\nimport ResizeMixin from '@/utils/ResizeHandler' // 整体页面是否根据总高配置\r\n\r\nimport { organNameFormat } from '@/filters'\r\nimport { Common } from '@/api'\r\nconst { query } = Common.DataAuditing\r\nexport default {\r\n  name: 'TableList',\r\n  components: { SunOperDetailDialog },\r\n  mixins: [ResizeMixin],\r\n  props: {\r\n    defaultForm: {\r\n      type: Object,\r\n      default: function() {\r\n        return {}\r\n      }\r\n    },\r\n    btnAll: {\r\n      type: Object,\r\n      default: function() {\r\n        return {}\r\n      }\r\n    }\r\n  },\r\n  data() {\r\n    return {\r\n      // table列表相关参数\r\n      listLoading: false,\r\n      table: {\r\n        // 表格配置\r\n        tableColumns: configTable(), // 表头配置\r\n        ref: 'tableRef',\r\n        selection: false, // 复选\r\n        indexNumber: true, // 序号\r\n        loading: false,\r\n        componentProps: {\r\n          data: [], // 表格数据\r\n          height: '300px',\r\n          formRow: 2 // 表单行数\r\n        },\r\n        pageList: {\r\n          totalNum: 0,\r\n          currentPage: 1, // 当前页\r\n          pageSize: this.$store.getters.pageSize // 当前页显示条数\r\n        }\r\n      },\r\n      approve_agree2: '未终审',\r\n      redColor: { color: 'red' },\r\n      blueBlack: {\r\n        color: '#15518c'\r\n      },\r\n      // 审批结果详情相关参数\r\n      aproveCurrentRow: [], // 选中行\r\n      dialog2: {\r\n        // 审批结果--执行异常-弹框\r\n        visible: false,\r\n        title: '处理详情'\r\n      },\r\n      errorMsg: '', // 审批结果-审核异常-详情\r\n      errDetail: {\r\n        // 审核结果-处理明细-表格配置\r\n        tableColumns: [], // 表头配置\r\n        selection: false, // 复选\r\n        indexNumber: true, // 序号\r\n        loading: false,\r\n        componentProps: {\r\n          data: [], // 表格数据\r\n          height: '100px',\r\n          formRow: 2 // 表单行数\r\n        },\r\n        pageList: {\r\n          totalNum: 0,\r\n          currentPage: 1, // 当前页\r\n          pageSize: this.$store.getters.pageSize // 当前页显示条数\r\n        }\r\n      },\r\n      // 文件上传相关参数\r\n      dialogFile: {\r\n        // 上传文件表单\r\n        visible: false,\r\n        componentProps: {\r\n          // 弹出框配置属性\r\n          title: '审核提交',\r\n          width: '60rem' // 当前弹出框宽度\r\n        },\r\n        form: {\r\n          labelWidth: '15rem',\r\n          config: config(this),\r\n          defaultForm: {\r\n            file_url: '', // 表单地址\r\n            postscript: '' // 附言\r\n          }\r\n        }\r\n      },\r\n      fileList: [], // 上传文件列表\r\n      delFile: [], // 删除文件后的文件列表\r\n      fileRow: [], // 下载附件当前row信息\r\n      btnId: '', // 上传文件提交时判断是同意操作或不同意操作标志\r\n      // 操作详情弹窗相关参数\r\n      dialogOper: {\r\n        // 流程事项-操作申请弹框数据\r\n        visible: false,\r\n        componentProps: {\r\n          // 弹出框属性\r\n          title: '', // 弹出框标题\r\n          width: '80%' // 当前弹出框宽度 默认80%\r\n        },\r\n        queryType: '',\r\n        currentRow: [], // 选中行\r\n        tableData: {\r\n          // 操作详情弹窗---删除-表格\r\n          tableColumns: [], // 表头配置\r\n          selection: false, // 复选\r\n          indexNumber: true, // 序号\r\n          loading: false,\r\n          componentProps: {\r\n            data: [], // 表格数据\r\n            height: '100px',\r\n            formRow: 0 // 表单行数\r\n          },\r\n          pageList: {\r\n            totalNum: 0,\r\n            currentPage: 1, // 当前页\r\n            pageSize: this.$store.getters.pageSize // 当前页显示条数\r\n          },\r\n          showPage: false\r\n        },\r\n        desData: {}, // 操作详情表单value\r\n        defaultForm2: [], // 操作详情表单key\r\n        tableDetailData: [], // 表格处理明细数据\r\n        btnId: '' // 上传文件提交时判断是同意操作或不同意操作标志\r\n      }\r\n    }\r\n  },\r\n  watch: {\r\n    loading(value) {\r\n      this.listLoading = this.loading\r\n    },\r\n    'dialog.visible': {\r\n      // 如果浏览器正常关闭状态\r\n      handler(val) {\r\n        if (val === false) {\r\n          // 监听流水号操作详情弹窗关闭状态   关闭时释放任务\r\n          if (\r\n            this.$store.getters.releaseTaskFlag &&\r\n            !commonBlank(this.bindTaskId)\r\n          ) {\r\n            releaseFlowTask(this.bindTaskId)\r\n          }\r\n        }\r\n      },\r\n      deep: true\r\n    }\r\n  },\r\n  created() {\r\n    this.queryList()\r\n  },\r\n  mounted() {\r\n    // 流程处理完毕 更新数据审核工作台未处理列表\r\n    this.$bus.$on('operApproveUpdate', (data) => {\r\n      if (data) {\r\n        this.queryList()\r\n      }\r\n    })\r\n  },\r\n  // 销毁对应自定义事件\r\n  beforeDestroy() {\r\n    this.$bus.$off('operApproveUpdate')\r\n  },\r\n  methods: {\r\n    /**\r\n     * 按钮：查询*/\r\n    queryList(currentPage) {\r\n      this.showLoading(true)\r\n      const msg = {\r\n        parameterList: [],\r\n        query_type: this.defaultForm.query_type,\r\n        approve_type: this.defaultForm.approve_type,\r\n        approve_agree: this.defaultForm.approve_agree,\r\n        inst_id: this.defaultForm.inst_id,\r\n        oper_type_flag: this.defaultForm.oper_type_flag,\r\n        apply_user: this.defaultForm.apply_user,\r\n        start_apply_time:\r\n          this.defaultForm.date_two !== null\r\n            ? this.defaultForm.date_two[0] === undefined\r\n              ? ''\r\n              : this.defaultForm.date_two[0] + '000000' // 格式化 yyyyMMddHHmmss\r\n            : '',\r\n        end_apply_time:\r\n          this.defaultForm.date_two !== null\r\n            ? this.defaultForm.date_two[1] === undefined\r\n              ? ''\r\n              : this.defaultForm.date_two[1] + '240000' // 格式化 yyyyMMddHHmmss\r\n            : '',\r\n        currentPage: currentPage || this.table.pageList.currentPage,\r\n        operationRequestWorkbench_pageNum: this.table.pageList.pageSize\r\n      }\r\n      // 查询\r\n      query(msg).then((res) => {\r\n        const { list, totalNum, currentPage } = res.retMap\r\n        this.table.componentProps.data = list\r\n        this.table.pageList.totalNum = totalNum\r\n        this.table.pageList.currentPage = currentPage\r\n        this.table.componentProps.data.forEach((item, index) => {\r\n          // 审批状态格式化\r\n          if (item.approve_agree === '2') {\r\n            //  item.approve_agree = '未终审' // 审批状态\r\n            if (!commonBlank(item.current_approve_organ)) {\r\n              return organNameFormat(item.current_approve_organ)\r\n            }\r\n            if (!commonBlank(item.role_no)) {\r\n              // 待审批角色格式化\r\n              this.$store.getters.userList.map((val) => {\r\n                if (!commonBlank(val.role_no)) {\r\n                  val.role_no.split(',').forEach((i) => {\r\n                    if (item.role_no === i) {\r\n                      item.role_no = `${i}-${val.value}`\r\n                    }\r\n                  })\r\n                }\r\n              })\r\n            }\r\n          } else {\r\n            // item.approve_agree = approveAgree(item.approve_agree)\r\n            item.current_approve_organ = ''\r\n            item.role_no = ''\r\n          }\r\n        })\r\n        this.showLoading(false)\r\n      })\r\n    },\r\n    /**\r\n     *表格-审批结果点击事件\r\n     * @param row 当前行数据\r\n     */\r\n    approveRest(row) {\r\n      this.aproveCurrentRow = row\r\n      if (row) {\r\n        // 审批结果为1-已通过&&参数配置类型为导出&&申请用户===当前用户   点击审批结果可下载导出文件\r\n        if (\r\n          row.approve_agree === '1' &&\r\n          row.oper_type_flag === '导出' &&\r\n          row.apply_user === this.$store.getters.userNo\r\n        ) {\r\n          // 调用下载方法\r\n          const downPathArr = row.file_path.split('\\\\')\r\n          const file_name = downPathArr[1]\r\n          const file_path = downPathArr[0] + '##' + downPathArr[1]\r\n          // const file_path = row.file_path.replace('\\\\', '/')\r\n          // const file_name = this.getAttachmentName(file_path)\r\n          this.downloadFile(file_name, file_path + '##' + file_name)\r\n        } else if (row.approve_agree === '3') {\r\n          // 审批异常\r\n          this.dialog2.visible = true\r\n          if (row.oper_type_flag === '导入') {\r\n            // 如果参数配置类型为导入   调用导入失败错误详情\r\n            this.approveDisAgreeDetail(row)\r\n          } else {\r\n            // 其他操作异常详情\r\n            this.dialogOper.defaultForm2.push({\r\n              label: '错误详情',\r\n              name: 'errorDetail'\r\n            })\r\n            this.errorMsg = row.error_msg // value值\r\n          }\r\n        }\r\n      }\r\n    },\r\n    /**\r\n     * 根据附件路径获取附件名称\r\n     * @param\tpath\t附件路径\r\n     */\r\n    getAttachmentName(path) {\r\n      let index = path.lastIndexOf('\\\\')\r\n      if (index === -1) {\r\n        index = path.lastIndexOf('/')\r\n      }\r\n      let name = path.substr(index)\r\n      index = name.indexOf('_')\r\n      name = name.substr(index + 1)\r\n      if (name.indexOf('##') !== -1) {\r\n        name = name.split('##')[1]\r\n      }\r\n      return name\r\n    },\r\n    /**\r\n     *失败错误提示方法 */\r\n    approveDisAgreeDetail(row) {\r\n      this.dialogOper.defaultForm2 = []\r\n      this.dialog2.title = '导入失败数据'\r\n      // 导入失败错误详情//box+table\r\n      const operationValue = JSON.parse(row.operation_value)\r\n      const retData = JSON.parse(row.remarks)\r\n      // descpiptions  数据\r\n\r\n      this.dialogOper.defaultForm2.push(\r\n        {\r\n          label: '导入成功数量(条)',\r\n          name: 'succCount'\r\n        },\r\n        {\r\n          label: '导入失败数量(条)',\r\n          name: 'failCount'\r\n        },\r\n        {\r\n          label: '导入失败数据',\r\n          name: 'failData'\r\n        }\r\n      )\r\n      this.dialogOper.desData['succCount'] = retData.succCount // 成功数量\r\n      this.dialogOper.desData['failCount'] = retData.failCount // 失败数量\r\n\r\n      // 处理明细\r\n      this.errDetail.tableColumns = operationValue.table_column // 表格  表头\r\n      this.errDetail.componentProps.data = retData.list // 表格  数据\r\n    },\r\n    /**\r\n     * 审批结果-执行异常-下载\r\n     */\r\n    downloadErrFile(row) {\r\n      const downPath = JSON.parse(row.remarks).importFailExcelPath\r\n      const downPathArr = downPath.split('\\\\')\r\n      const fileName = downPathArr[1]\r\n      // const file_path = downPath.replace('\\\\', '/')\r\n      const file_path = downPathArr[0] + '##' + downPathArr[1]\r\n      // console.log('path',downPath, downPathArr,file_path);\r\n      this.downloadFile(fileName, file_path)\r\n    },\r\n    /**\r\n     *流水号：操作详情\r\n     * @param row 当前行数据\r\n     */\r\n    async rowClick(row) {\r\n      const detailData = await sysMessageOperationRequestDeatilShow(\r\n        row,\r\n        this.defaultForm.query_type\r\n      )\r\n      if (!commonBlank(detailData) && detailData[2] === true) {\r\n        // haveRight 为true\r\n        this.changeOperVisible(true)\r\n        this.dialogOper.currentRow = row\r\n        this.dialogOper.queryType = detailData[1] // 查询类型0-我的申请 1-我的代办 2-我的已办\r\n        this.dialogOper.tableDetailData = detailData[3]\r\n      } else if (!detailData[2]) {\r\n        // haveRight 为false\r\n        commonMsgInfo('查询详情数据为空！', this)\r\n      }\r\n    },\r\n    /**\r\n     * 流程事项-操作申请-操作详情 - 弹窗关闭\r\n     * @param {Boolean} param 弹出框显示隐藏配置*/\r\n    changeOperVisible(param) {\r\n      this.dialogOper.visible = param\r\n    },\r\n    /**\r\n     * 审批结果执行异常弹出框 - 关闭\r\n     */\r\n    dialog2Close() {\r\n      this.dialog2.visible = false\r\n    },\r\n    /**\r\n     *页码更新 */\r\n    getList(pageParam) {\r\n      const { currentPage, pageSize } = pageParam\r\n      this.table.pageList.pageSize = pageSize\r\n      this.table.pageList.currentPage = currentPage\r\n      this.queryList()\r\n    },\r\n    /**\r\n     * 加载中动画配置*/\r\n    showLoading() {\r\n      this.listLoading = !this.listLoading\r\n    }\r\n  }\r\n}\r\n</script>\r\n<style lang=\"scss\" scoped>\r\n@import '@/assets/scss/main.scss';\r\n$ParamsBorder: 1px solid #f1f1f1; // 线框\r\n\r\n::v-deep {\r\n  .el-dialog {\r\n    .el-dialog__body {\r\n      .title {\r\n        font-size: 14px;\r\n        margin-bottom: 1rem;\r\n        font-weight: bold;\r\n      }\r\n      .el-descriptions {\r\n        border: $ParamsBorder;\r\n        border-radius: 1rem;\r\n        padding: 2rem 2rem 1rem 1rem;\r\n        margin-bottom: 2rem;\r\n        .el-descriptions__header {\r\n          margin-bottom: 1.5rem;\r\n          .el-descriptions__title {\r\n            font-size: 14px;\r\n            font-weight: bold;\r\n            color: #606266;\r\n          }\r\n        }\r\n        .el-descriptions__body {\r\n          margin-left: 4rem;\r\n        }\r\n      }\r\n      .el-table {\r\n        height: 170px !important;\r\n      }\r\n    }\r\n  }\r\n}\r\n.instId {\r\n  color: #0000ff;\r\n  cursor: pointer;\r\n  &:hover {\r\n    color: #e9313e;\r\n  }\r\n}\r\n</style>\r\n"]}]}