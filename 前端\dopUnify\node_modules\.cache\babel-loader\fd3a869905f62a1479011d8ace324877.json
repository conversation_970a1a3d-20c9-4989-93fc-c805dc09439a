{"remainingRequest": "E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1\\数字运营平台-统一门户工程\\dopUnify\\node_modules\\babel-loader\\lib\\index.js!E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1\\数字运营平台-统一门户工程\\dopUnify\\node_modules\\eslint-loader\\index.js??ref--13-0!E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1\\数字运营平台-统一门户工程\\dopUnify\\src\\views\\system\\notice\\query\\info.js", "dependencies": [{"path": "E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1\\数字运营平台-统一门户工程\\dopUnify\\src\\views\\system\\notice\\query\\info.js", "mtime": 1716875178068}, {"path": "E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1\\数字运营平台-统一门户工程\\dopUnify\\babel.config.js", "mtime": 1716875191548}, {"path": "E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1\\数字运营平台-统一门户工程\\dopUnify\\.babelrc", "mtime": 1716875191502}, {"path": "E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1\\数字运营平台-统一门户工程\\dopUnify\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1\\数字运营平台-统一门户工程\\dopUnify\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1\\数字运营平台-统一门户工程\\dopUnify\\node_modules\\eslint-loader\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8g6KGo5Y2VCmV4cG9ydCB2YXIgY29uZmlnID0gZnVuY3Rpb24gY29uZmlnKHRoYXQpIHsKICByZXR1cm4gewogICAga2V5V29yZDogewogICAgICBjb21wb25lbnQ6ICdpbnB1dCcsCiAgICAgIGxhYmVsOiAn5YWs5ZGK5YWz6ZSu5a2XJywKICAgICAgY29sU3BhbjogOCwKICAgICAgbmFtZTogJ2tleVdvcmQnLAogICAgICBjb25maWc6IHsKICAgICAgICAvLyBmb3JtLWl0ZW0g6YWN572uCiAgICAgICAgcnVsZXM6IFt7CiAgICAgICAgICBtaW46IDAsCiAgICAgICAgICBtYXg6IDYwLAogICAgICAgICAgbWVzc2FnZTogJ+ivt+acgOWkmuWhq+WGmTYw5Liq5a2X56ymJwogICAgICAgIH1dCiAgICAgIH0sCiAgICAgIGNvbXBvbmVudFByb3BzOiB7CiAgICAgICAgLy8gaW5wdXTnu4Tku7bphY3nva4KICAgICAgICBwbGFjZWhvbGRlcjogJ+aUr+aMgeaMieWFrOWRiuWFs+mUruWtl+aooeeziuafpeivoicsCiAgICAgICAgY2xlYXJhYmxlOiB0cnVlCiAgICAgIH0KICAgIH0KICB9Owp9Ow=="}, {"version": 3, "names": ["config", "that", "key<PERSON>ord", "component", "label", "colSpan", "name", "rules", "min", "max", "message", "componentProps", "placeholder", "clearable"], "sources": ["E:/1_Project/XYD_Project/dop-4.0/dop-4.1/数字运营平台-统一门户工程/dopUnify/src/views/system/notice/query/info.js"], "sourcesContent": ["// 表单\r\nexport const config = (that) => ({\r\n  keyWord: {\r\n    component: 'input',\r\n    label: '公告关键字',\r\n    colSpan: 8,\r\n    name: 'keyWord',\r\n    config: {\r\n      // form-item 配置\r\n      rules: [\r\n        { min: 0, max: 60, message: '请最多填写60个字符' }\r\n      ]\r\n    },\r\n    componentProps: {\r\n      // input组件配置\r\n      placeholder: '支持按公告关键字模糊查询',\r\n      clearable: true\r\n    }\r\n  }\r\n})\r\n"], "mappings": "AAAA;AACA,OAAO,IAAMA,MAAM,GAAG,SAATA,MAAM,CAAIC,IAAI;EAAA,OAAM;IAC/BC,OAAO,EAAE;MACPC,SAAS,EAAE,OAAO;MAClBC,KAAK,EAAE,OAAO;MACdC,OAAO,EAAE,CAAC;MACVC,IAAI,EAAE,SAAS;MACfN,MAAM,EAAE;QACN;QACAO,KAAK,EAAE,CACL;UAAEC,GAAG,EAAE,CAAC;UAAEC,GAAG,EAAE,EAAE;UAAEC,OAAO,EAAE;QAAa,CAAC;MAE9C,CAAC;MACDC,cAAc,EAAE;QACd;QACAC,WAAW,EAAE,cAAc;QAC3BC,SAAS,EAAE;MACb;IACF;EACF,CAAC;AAAA,CAAC"}]}