{"remainingRequest": "E:\\华南区项目\\中国银行\\开发区\\科技公司运营平台\\前端\\dopUnify\\node_modules\\thread-loader\\dist\\cjs.js!E:\\华南区项目\\中国银行\\开发区\\科技公司运营平台\\前端\\dopUnify\\node_modules\\babel-loader\\lib\\index.js!E:\\华南区项目\\中国银行\\开发区\\科技公司运营平台\\前端\\dopUnify\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!E:\\华南区项目\\中国银行\\开发区\\科技公司运营平台\\前端\\dopUnify\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\华南区项目\\中国银行\\开发区\\科技公司运营平台\\前端\\dopUnify\\src\\layout\\components\\Sidebar\\SidebarItem.vue?vue&type=script&lang=js&", "dependencies": [{"path": "E:\\华南区项目\\中国银行\\开发区\\科技公司运营平台\\前端\\dopUnify\\src\\layout\\components\\Sidebar\\SidebarItem.vue", "mtime": 1721631954662}, {"path": "E:\\华南区项目\\中国银行\\开发区\\科技公司运营平台\\前端\\dopUnify\\babel.config.js", "mtime": 1721631954662}, {"path": "E:\\华南区项目\\中国银行\\开发区\\科技公司运营平台\\前端\\dopUnify\\.babelrc", "mtime": 1721631954662}, {"path": "E:\\华南区项目\\中国银行\\开发区\\科技公司运营平台\\前端\\dopUnify\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "E:\\华南区项目\\中国银行\\开发区\\科技公司运营平台\\前端\\dopUnify\\node_modules\\thread-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "E:\\华南区项目\\中国银行\\开发区\\科技公司运营平台\\前端\\dopUnify\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "E:\\华南区项目\\中国银行\\开发区\\科技公司运营平台\\前端\\dopUnify\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "E:\\华南区项目\\中国银行\\开发区\\科技公司运营平台\\前端\\dopUnify\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1655715099000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, null]}