{"remainingRequest": "D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\node_modules\\thread-loader\\dist\\cjs.js!D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\node_modules\\babel-loader\\lib\\index.js!D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\src\\views\\system\\organ\\component\\table\\index.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\src\\views\\system\\organ\\component\\table\\index.vue", "mtime": 1687679873471}, {"path": "D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\babel.config.js", "mtime": 1686019818359}, {"path": "D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\node_modules\\thread-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1655715099000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, null]}