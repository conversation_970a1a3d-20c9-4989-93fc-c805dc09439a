{"remainingRequest": "E:\\华南区项目\\中国银行\\开发区\\科技公司运营平台\\前端\\dopUnify\\node_modules\\babel-loader\\lib\\index.js!E:\\华南区项目\\中国银行\\开发区\\科技公司运营平台\\前端\\dopUnify\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!E:\\华南区项目\\中国银行\\开发区\\科技公司运营平台\\前端\\dopUnify\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\华南区项目\\中国银行\\开发区\\科技公司运营平台\\前端\\dopUnify\\src\\views\\home\\page\\Calendar.vue?vue&type=script&lang=js&", "dependencies": [{"path": "E:\\华南区项目\\中国银行\\开发区\\科技公司运营平台\\前端\\dopUnify\\src\\views\\home\\page\\Calendar.vue", "mtime": 1721631954662}, {"path": "E:\\华南区项目\\中国银行\\开发区\\科技公司运营平台\\前端\\dopUnify\\babel.config.js", "mtime": 1721631954662}, {"path": "E:\\华南区项目\\中国银行\\开发区\\科技公司运营平台\\前端\\dopUnify\\.babelrc", "mtime": 1721631954662}, {"path": "E:\\华南区项目\\中国银行\\开发区\\科技公司运营平台\\前端\\dopUnify\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "E:\\华南区项目\\中国银行\\开发区\\科技公司运营平台\\前端\\dopUnify\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "E:\\华南区项目\\中国银行\\开发区\\科技公司运营平台\\前端\\dopUnify\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "E:\\华南区项目\\中国银行\\开发区\\科技公司运营平台\\前端\\dopUnify\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1655715099000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KCmV4cG9ydCBkZWZhdWx0IHsKICBuYW1lOiAnSG9tZUNhbGVuZGFyJywKICBkYXRhOiBmdW5jdGlvbiBkYXRhKCkgewogICAgcmV0dXJuIHsKICAgICAgdmFsdWU6IG5ldyBEYXRlKCkKICAgIH07CiAgfSwKICBjb21wdXRlZDoge30sCiAgY3JlYXRlZDogZnVuY3Rpb24gY3JlYXRlZCgpIHt9Cn07"}, {"version": 3, "mappings": ";;;;;;;;AAQA;EACAA;EAEAC;IACA;MACAC;IACA;EACA;EACAC;EACAC;AACA", "names": ["name", "data", "value", "computed", "created"], "sourceRoot": "src/views/home/<USER>", "sources": ["Calendar.vue"], "sourcesContent": ["<template>\n  <div class=\"default-item\">\n    <div class=\"default-item-inner\">\n      <el-calendar v-model=\"value\" />\n    </div>\n  </div>\n</template>\n<script>\nexport default {\n  name: 'HomeCalendar',\n\n  data() {\n    return {\n      value: new Date()\n    }\n  },\n  computed: {},\n  created() {}\n}\n</script>\n<style scoped lang=\"scss\">\n.default-item {\n  padding: 1rem;\n  width: 100%;\n  height: 100%;\n  border-radius: 1rem;\n  background-color: #fff;\n  .default-item-inner{\n    height: 100%;\n    width: 100%;\n    overflow: auto;\n  }\n}\n</style>\n"]}]}