{"remainingRequest": "E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\node_modules\\babel-loader\\lib\\index.js!E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\node_modules\\sunui\\src\\directive\\waves\\index.js", "dependencies": [{"path": "E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\node_modules\\sunui\\src\\directive\\waves\\index.js", "mtime": 499162500000}, {"path": "E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\babel.config.js", "mtime": 1686019818359}, {"path": "E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:aW1wb3J0IHdhdmVzIGZyb20gJy4vd2F2ZXMnOwp2YXIgaW5zdGFsbCA9IGZ1bmN0aW9uIGluc3RhbGwoVnVlKSB7CiAgVnVlLmRpcmVjdGl2ZSgnd2F2ZXMnLCB3YXZlcyk7Cn07CmlmICh3aW5kb3cuVnVlKSB7CiAgd2luZG93LndhdmVzID0gd2F2ZXM7CiAgVnVlLnVzZShpbnN0YWxsKTsgLy8gZXNsaW50LWRpc2FibGUtbGluZQp9Cgp3YXZlcy5pbnN0YWxsID0gaW5zdGFsbDsKZXhwb3J0IGRlZmF1bHQgd2F2ZXM7"}, {"version": 3, "names": ["waves", "install", "<PERSON><PERSON>", "directive", "window", "use"], "sources": ["E:/1_Project/XYD_Project/dop-4.0/dop-4.1-qianduan-unify/dopUnify/node_modules/sunui/src/directive/waves/index.js"], "sourcesContent": ["import waves from './waves'\n\nconst install = function(Vue) {\n  Vue.directive('waves', waves)\n}\n\nif (window.Vue) {\n  window.waves = waves\n  Vue.use(install); // eslint-disable-line\n}\n\nwaves.install = install\nexport default waves\n"], "mappings": "AAAA,OAAOA,KAAK,MAAM,SAAS;AAE3B,IAAMC,OAAO,GAAG,SAAVA,OAAO,CAAYC,GAAG,EAAE;EAC5BA,GAAG,CAACC,SAAS,CAAC,OAAO,EAAEH,KAAK,CAAC;AAC/B,CAAC;AAED,IAAII,MAAM,CAACF,GAAG,EAAE;EACdE,MAAM,CAACJ,KAAK,GAAGA,KAAK;EACpBE,GAAG,CAACG,GAAG,CAACJ,OAAO,CAAC,CAAC,CAAC;AACpB;;AAEAD,KAAK,CAACC,OAAO,GAAGA,OAAO;AACvB,eAAeD,KAAK"}]}