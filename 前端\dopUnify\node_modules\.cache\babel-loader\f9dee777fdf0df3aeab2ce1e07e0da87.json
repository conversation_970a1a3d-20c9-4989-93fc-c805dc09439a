{"remainingRequest": "D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\node_modules\\babel-loader\\lib\\index.js!D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\src\\views\\error-page\\401.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\src\\views\\error-page\\401.vue", "mtime": 1686019809654}, {"path": "D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\babel.config.js", "mtime": 1686019818359}, {"path": "D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\.babelrc", "mtime": 1705285078668}, {"path": "D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1655715099000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KCmltcG9ydCBlcnJHaWYgZnJvbSAnQC9hc3NldHMvaW1nL290aGVyLzQwMV9pbWFnZXMvNDAxLmdpZic7CmV4cG9ydCBkZWZhdWx0IHsKICBuYW1lOiAnUGFnZTQwMScsCiAgZGF0YTogZnVuY3Rpb24gZGF0YSgpIHsKICAgIHJldHVybiB7CiAgICAgIGVyckdpZjogZXJyR2lmICsgJz8nICsgK25ldyBEYXRlKCksCiAgICAgIGV3aXphcmRDbGFwOiAnaHR0cHM6Ly93cGltZy53YWxsc3Rjbi5jb20vMDA3ZWY1MTctYmFmZC00MDY2LWFhZTQtNjg4MzYzMmQ5NjQ2JywKICAgICAgZGlhbG9nVmlzaWJsZTogZmFsc2UKICAgIH07CiAgfSwKICBtZXRob2RzOiB7CiAgICBiYWNrOiBmdW5jdGlvbiBiYWNrKCkgewogICAgICBpZiAodGhpcy4kcm91dGUucXVlcnkubm9Hb0JhY2spIHsKICAgICAgICB0aGlzLiRyb3V0ZXIucHVzaCh7CiAgICAgICAgICBwYXRoOiAnL2hvbWUnCiAgICAgICAgfSk7CiAgICAgIH0gZWxzZSB7CiAgICAgICAgdGhpcy4kcm91dGVyLmdvKC0xKTsKICAgICAgfQogICAgfQogIH0KfTs="}, {"version": 3, "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAqCA;AAEA;EACAA;EACAC;IACA;MACAC;MACAC;MACAC;IACA;EACA;EACAC;IACAC;MACA;QACA;UAAAC;QAAA;MACA;QACA;MACA;IACA;EACA;AACA", "names": ["name", "data", "err<PERSON><PERSON>", "ewizard<PERSON>lap", "dialogVisible", "methods", "back", "path"], "sourceRoot": "src/views/error-page", "sources": ["401.vue"], "sourcesContent": ["<template>\n  <div class=\"errPage-container\">\n    <el-button icon=\"el-icon-arrow-left\" class=\"pan-back-btn\" @click=\"back\">\n      返回\n    </el-button>\n    <el-row>\n      <el-col :span=\"12\">\n        <h1 class=\"text-jumbo text-ginormous\">\n          Oops!\n        </h1>\n        gif来源<a href=\"https://zh.airbnb.com/\" target=\"_blank\">airbnb</a> 页面\n        <h2>你没有权限去该页面</h2>\n        <h6>如有不满请联系你领导</h6>\n        <ul class=\"list-unstyled\">\n          <li>或者你可以去:</li>\n          <li class=\"link-type\">\n            <router-link to=\"/home\">\n              回首页\n            </router-link>\n          </li>\n          <li class=\"link-type\">\n            <a href=\"https://www.taobao.com/\">随便看看</a>\n          </li>\n          <li><a href=\"#\" @click.prevent=\"dialogVisible=true\">点我看图</a></li>\n        </ul>\n      </el-col>\n      <el-col :span=\"12\">\n        <img :src=\"errGif\" width=\"313\" height=\"428\" alt=\"Girl has dropped her ice cream.\">\n      </el-col>\n    </el-row>\n    <el-dialog :visible.sync=\"dialogVisible\" title=\"随便看\">\n      <img :src=\"ewizardClap\" class=\"pan-img\">\n    </el-dialog>\n  </div>\n</template>\n\n<script>\nimport errGif from '@/assets/img/other/401_images/401.gif'\n\nexport default {\n  name: 'Page401',\n  data() {\n    return {\n      errGif: errGif + '?' + +new Date(),\n      ewizardClap: 'https://wpimg.wallstcn.com/007ef517-bafd-4066-aae4-6883632d9646',\n      dialogVisible: false\n    }\n  },\n  methods: {\n    back() {\n      if (this.$route.query.noGoBack) {\n        this.$router.push({ path: '/home' })\n      } else {\n        this.$router.go(-1)\n      }\n    }\n  }\n}\n</script>\n\n<style lang=\"scss\" scoped>\n  .errPage-container {\n    width: 800px;\n    max-width: 100%;\n    margin: 100px auto;\n    .pan-back-btn {\n      background: #008489;\n      color: #fff;\n      border: none!important;\n    }\n    .pan-gif {\n      margin: 0 auto;\n      display: block;\n    }\n    .pan-img {\n      display: block;\n      margin: 0 auto;\n      width: 100%;\n    }\n    .text-jumbo {\n      font-size: 60px;\n      font-weight: 700;\n      color: #484848;\n    }\n    .list-unstyled {\n      font-size: 14px;\n      li {\n        padding-bottom: 5px;\n      }\n      a {\n        color: #008489;\n        text-decoration: none;\n        &:hover {\n          text-decoration: underline;\n        }\n      }\n    }\n  }\n</style>\n"]}]}