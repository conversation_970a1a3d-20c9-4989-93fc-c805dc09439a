package com.sunyard.etl.nps.model;

public class SmDataSourceSetTb {
		
	private String dataSourceId;
	private String groupName;
	private String modeCode;
	private String indexName;
	private String filePartName;
	private String uaIp;
	private String uaPort;
	private String dataDesc;
	private String custId;
	private String serviceId;
	
	
	public String getDataSourceId() {
		return dataSourceId;
	}
	public void setDataSourceId(String dataSourceId) {
		this.dataSourceId = dataSourceId;
	}
	public String getGroupName() {
		return groupName;
	}
	public void setGroupName(String groupName) {
		this.groupName = groupName;
	}
	public String getModeCode() {
		return modeCode;
	}
	public void setModeCode(String modeCode) {
		this.modeCode = modeCode;
	}
	public String getIndexName() {
		return indexName;
	}
	public void setIndexName(String indexName) {
		this.indexName = indexName;
	}
	public String getFilePartName() {
		return filePartName;
	}
	public void setFilePartName(String filePartName) {
		this.filePartName = filePartName;
	}
	public String getUaIp() {
		return uaIp;
	}
	public void setUaIp(String uaIp) {
		this.uaIp = uaIp;
	}
	public String getUaPort() {
		return uaPort;
	}
	public void setUaPort(String uaPort) {
		this.uaPort = uaPort;
	}
	public String getDataDesc() {
		return dataDesc;
	}
	public void setDataDesc(String dataDesc) {
		this.dataDesc = dataDesc;
	}
	public String getCustId() {
		return custId;
	}
	public void setCustId(String custId) {
		this.custId = custId;
	}
	public String getServiceId() {
		return serviceId;
	}
	public void setServiceId(String serviceId) {
		this.serviceId = serviceId;
	}

}
