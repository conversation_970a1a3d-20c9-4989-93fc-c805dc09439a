{"remainingRequest": "E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1\\数字运营平台-统一门户工程\\dopUnify\\node_modules\\babel-loader\\lib\\index.js!E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1\\数字运营平台-统一门户工程\\dopUnify\\node_modules\\eslint-loader\\index.js??ref--13-0!E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1\\数字运营平台-统一门户工程\\dopUnify\\src\\views\\system\\outManage\\definition\\info.js", "dependencies": [{"path": "E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1\\数字运营平台-统一门户工程\\dopUnify\\src\\views\\system\\outManage\\definition\\info.js", "mtime": 1716875178801}, {"path": "E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1\\数字运营平台-统一门户工程\\dopUnify\\babel.config.js", "mtime": 1716875191548}, {"path": "E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1\\数字运营平台-统一门户工程\\dopUnify\\.babelrc", "mtime": 1716875191502}, {"path": "E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1\\数字运营平台-统一门户工程\\dopUnify\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1\\数字运营平台-统一门户工程\\dopUnify\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1\\数字运营平台-统一门户工程\\dopUnify\\node_modules\\eslint-loader\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:aW1wb3J0IHsgZGljdGlvbmFyeUZpZWRzIH0gZnJvbSAnQC91dGlscy9kaWN0aW9uYXJ5JzsgLy8g5a2X5YW46YWN572uCgovLyDooajljZUKZXhwb3J0IHZhciBjb25maWcgPSBmdW5jdGlvbiBjb25maWcodGhhdCkgewogIHJldHVybiB7CiAgICB0ZF9ubzogewogICAgICBjb21wb25lbnQ6ICdpbnB1dCcsCiAgICAgIGxhYmVsOiAn5o6l5Y+j5qCH6K+GJywKICAgICAgY29sU3BhbjogOCwKICAgICAgbmFtZTogJ3RkX25vJywKICAgICAgY29tcG9uZW50UHJvcHM6IHsKICAgICAgICAvLyBpbnB1dOe7hOS7tumFjee9rgogICAgICAgIGNsZWFyYWJsZTogdHJ1ZSwKICAgICAgICBwbGFjZWhvbGRlcjogJ+aUr+aMgeaOpeWPo+agh+ivhuaooeeziuafpeivoicKICAgICAgfQogICAgfSwKICAgIHRkX3R5cGU6IHsKICAgICAgY29tcG9uZW50OiAnc2VsZWN0JywKICAgICAgbGFiZWw6ICfmjqXlj6PliIbnsbsnLAogICAgICBjb2xTcGFuOiA4LAogICAgICBuYW1lOiAndGRfdHlwZScsCiAgICAgIGNvbXBvbmVudFByb3BzOiB7CiAgICAgICAgcGxhY2Vob2xkZXI6ICfor7fpgInmi6knLAogICAgICAgIGNsZWFyYWJsZTogdHJ1ZQogICAgICB9LAogICAgICBvcHRpb25zOiBkaWN0aW9uYXJ5RmllZHMoJ1REX1RZUEUnKQogICAgfSwKICAgIGlzX29wZW46IHsKICAgICAgY29tcG9uZW50OiAnc2VsZWN0JywKICAgICAgbGFiZWw6ICflkK/nlKjmoIflv5cnLAogICAgICBjb2xTcGFuOiA4LAogICAgICBuYW1lOiAnaXNfb3BlbicsCiAgICAgIGNvbXBvbmVudFByb3BzOiB7CiAgICAgICAgcGxhY2Vob2xkZXI6ICfor7fpgInmi6knLAogICAgICAgIGNsZWFyYWJsZTogdHJ1ZQogICAgICB9LAogICAgICBvcHRpb25zOiBkaWN0aW9uYXJ5RmllZHMoJ0lTX09QRU4nKQogICAgfQogIH07Cn07"}, {"version": 3, "names": ["dictionaryFieds", "config", "that", "td_no", "component", "label", "colSpan", "name", "componentProps", "clearable", "placeholder", "td_type", "options", "is_open"], "sources": ["E:/1_Project/XYD_Project/dop-4.0/dop-4.1/数字运营平台-统一门户工程/dopUnify/src/views/system/outManage/definition/info.js"], "sourcesContent": ["import { dictionaryFieds } from '@/utils/dictionary' // 字典配置\r\n\r\n// 表单\r\nexport const config = (that) => ({\r\n  td_no: {\r\n    component: 'input',\r\n    label: '接口标识',\r\n    colSpan: 8,\r\n    name: 'td_no',\r\n    componentProps: {\r\n      // input组件配置\r\n      clearable: true,\r\n      placeholder: '支持接口标识模糊查询'\r\n    }\r\n  },\r\n  td_type: {\r\n    component: 'select',\r\n    label: '接口分类',\r\n    colSpan: 8,\r\n    name: 'td_type',\r\n    componentProps: {\r\n      placeholder: '请选择',\r\n      clearable: true\r\n    },\r\n    options: dictionaryFieds('TD_TYPE')\r\n  },\r\n  is_open: {\r\n    component: 'select',\r\n    label: '启用标志',\r\n    colSpan: 8,\r\n    name: 'is_open',\r\n    componentProps: {\r\n      placeholder: '请选择',\r\n      clearable: true\r\n    },\r\n    options: dictionaryFieds('IS_OPEN')\r\n  }\r\n})\r\n"], "mappings": "AAAA,SAASA,eAAe,QAAQ,oBAAoB,EAAC;;AAErD;AACA,OAAO,IAAMC,MAAM,GAAG,SAATA,MAAM,CAAIC,IAAI;EAAA,OAAM;IAC/BC,KAAK,EAAE;MACLC,SAAS,EAAE,OAAO;MAClBC,KAAK,EAAE,MAAM;MACbC,OAAO,EAAE,CAAC;MACVC,IAAI,EAAE,OAAO;MACbC,cAAc,EAAE;QACd;QACAC,SAAS,EAAE,IAAI;QACfC,WAAW,EAAE;MACf;IACF,CAAC;IACDC,OAAO,EAAE;MACPP,SAAS,EAAE,QAAQ;MACnBC,KAAK,EAAE,MAAM;MACbC,OAAO,EAAE,CAAC;MACVC,IAAI,EAAE,SAAS;MACfC,cAAc,EAAE;QACdE,WAAW,EAAE,KAAK;QAClBD,SAAS,EAAE;MACb,CAAC;MACDG,OAAO,EAAEZ,eAAe,CAAC,SAAS;IACpC,CAAC;IACDa,OAAO,EAAE;MACPT,SAAS,EAAE,QAAQ;MACnBC,KAAK,EAAE,MAAM;MACbC,OAAO,EAAE,CAAC;MACVC,IAAI,EAAE,SAAS;MACfC,cAAc,EAAE;QACdE,WAAW,EAAE,KAAK;QAClBD,SAAS,EAAE;MACb,CAAC;MACDG,OAAO,EAAEZ,eAAe,CAAC,SAAS;IACpC;EACF,CAAC;AAAA,CAAC"}]}