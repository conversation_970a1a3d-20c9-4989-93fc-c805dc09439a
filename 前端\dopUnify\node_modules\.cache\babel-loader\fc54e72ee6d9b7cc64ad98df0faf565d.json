{"remainingRequest": "D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\node_modules\\babel-loader\\lib\\index.js!D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\src\\components\\Dialog\\SunNoticeDialog\\index.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\src\\components\\Dialog\\SunNoticeDialog\\index.vue", "mtime": 1689922250581}, {"path": "D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\babel.config.js", "mtime": 1686019818359}, {"path": "D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1655715099000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA6HA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;EACAA;EACAC;IAAAC;EAAA;EACAC;IACAC;IACAC;EACA;EACAC;IACA;IACAC;EACA;EACA;EACAC;IACAC;MACAC;MACAC;QACA;UACAC;UAAA;UACAC;UAAA;UACAC;UAAA;UACAC;YACA;YACAC;YAAA;YACAC;UACA;;UACAC;YACA;YACAC;YAAA;YACAH;YAAA;YACAI;YAAA;YACAC;YAAA;YACAC;YAAA;YACAC;UACA;QACA;MACA;IACA;EACA;EACAC;IACA;MACAC;QACAb;QACAI;QACAC;QACAS;QACAC;QAAA;QACAjB;MACA;;MACAkB;MACAC;MACAC;MACAC;QAAAL;MAAA;MAAA;MACAM;QAAAN;MAAA;MAAA;MACAO;QAAAC;MAAA;IACA;EACA;;EACAC;IACA;MACA;MACAC;QACA;UACA;QACA;UACA;QACA;MACA;MACAC;IACA;IACAR;MACA;MACAO;QACA;UACA;UACA;UACA;QACA;UACA;UACA;UACA;QACA;MACA;MACAC;IACA;EACA;EACAC;IAAA;IACA;MACA;MACA;IACA;EACA;EACAC;IACA;EACA;EACAC;IACAC;MACA;MACA;IACA;IACA;AACA;AACA;AACA;AACA;IACAC;MACA;MACA;MACA;MACA,yCACAC,cACA,qDACAC,SACA,CACA;MACA,sCACAD,cACA,qDACAE,uBACA,CACA;MACA;MACA;MACA;;MAEA,WACAC;MACA;;MAEA;MAEA,+GACAC,mBACA;;MAEA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;;MAEA;QACA;MACA;QACA;MACA;QACA;MACA;QACA;MACA;QACA;MACA;QACA;MACA;IACA;IACAC;MACA;IACA;IACA;AACA;IACAC;MACA;QACA;QACA;UACA,yDACAC;QACA;UACA,yDACAA;QACA;QACA;MACA;IACA;IACAC;MACA;QACA;UACAC;UACA1C;QACA;QACA;MACA;MACA;QACA;QACA;UACA;UACA2C;QACA;UACA;UACAA;QACA;QACA;MACA;QACAA;QACAA;MACA;MACA;IACA;IACAC;MACA;IACA;IACAC;MACA;QACAF;MACA;IACA;IACAG;MACA;QACAH;MACA;IACA;IACA;AACA;AACA;AACA;IACAI;MACAC;QACA;UACAC;UACAC;QACA;MACA;IACA;IACA;AACA;AACA;AACA;IACAC;MACAH;QACA;UACAE;QACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;;MAEA;MACA;;MAEA;MACA;MACA;;MAEA;MACA;MACA;MACA;MACA;;MAEA;IACA;IACA;AACA;AACA;IACAE;MACAC;IACA;IACA;AACA;AACA;IACAC;MACAD;IACA;IACA;AACA;AACA;IACAE;MACA;MACA;MACA;MACA;MACA;MACA;QACAC;MACA;QACAA;MACA;QACAA;MACA;QACAA;MACA;QACAA;MACA;QACAA;MACA;QACAA;MACA;QACAA;MACA;QACAA;MACA;MACA;IACA;IACA;AACA;AACA;IACAC;MAAA;MACA;QACA;MACA;IACA;IACA;AACA;IACAC;MACA;IACA;EACA;AACA", "names": ["name", "directives", "elDragDialog", "filters", "organNoFormat", "dateTimeFormat", "components", "Preview", "props", "dialogConfig", "type", "default", "visible", "btnSubmit", "btnCancle", "componentProps", "title", "width", "noticeConfig", "imgSrc", "info", "readNum", "content", "files", "data", "previewData", "height", "imageSrc", "btn", "isFullscreen", "fullscreen", "classObj", "classObjNotice", "classObjTitle", "top", "watch", "handler", "deep", "mounted", "<PERSON><PERSON><PERSON><PERSON>", "methods", "filPre", "fiePreview", "encryptResult", "fileName", "fileUrl", "href", "Base64", "closePreview", "resize<PERSON><PERSON>ler", "screenHeight", "click", "message", "screenfull", "change", "init", "destroy", "downloadAllFile", "commonMsgConfirm", "saveFileName", "commonDownLoadFile", "downloadFile", "openFile", "alert", "fileInto", "fileIcon", "icon", "dialogClose", "dialogSubmit"], "sourceRoot": "src/components/Dialog/SunNoticeDialog", "sources": ["index.vue"], "sourcesContent": ["<!--\r\n* 弹出框：单个表格\r\n-->\r\n<template>\r\n  <div>\r\n    <el-dialog\r\n      ref=\"dialogScreenfull\"\r\n      v-el-drag-dialog\r\n      :class=\"{ 'full-screen': isFullscreen }\"\r\n      name=\"dialogN\"\r\n      :visible.sync=\"dialogConfig.visible\"\r\n      :append-to-body=\"true\"\r\n      :close-on-click-modal=\"false\"\r\n      :before-close=\"dialogClose\"\r\n      v-bind=\"dialogConfig.componentProps\"\r\n    >\r\n      <div title=\"全屏展示\" class=\"full-icon\">\r\n        <sun-svg-icon\r\n          :icon-class=\"isFullscreen ? 'exit-fullscreen' : 'fullscreen'\"\r\n          @click=\"click\"\r\n        /><!-- 全屏 -->\r\n      </div>\r\n      <div class=\"top-img\">\r\n        <img :src=\"dialogConfig.noticeConfig.imgSrc\" alt=\"\" class=\"imgonly\">\r\n        <div class=\"notice-top\" :style=\"classObjTitle\">\r\n          <h3 class=\"notice-title\">{{ dialogConfig.noticeConfig.title }}</h3>\r\n          <div\r\n            v-for=\"(item, index) in dialogConfig.noticeConfig.info\"\r\n            :key=\"index\"\r\n            class=\"notice-info\"\r\n          >\r\n            <span>{{ item.publish_organ | organNoFormat }}</span>\r\n            <span>{{ item.publish_user }}</span>\r\n            <span>{{ item.publish_time | dateTimeFormat }}</span>\r\n            <span>阅读量：{{ dialogConfig.noticeConfig.readNum ? dialogConfig.noticeConfig.readNum : 0 }}</span>\r\n          </div>\r\n        </div>\r\n        <slot name=\"noticeSlot\" />\r\n      </div>\r\n      <div\r\n        v-if=\"dialogConfig.noticeConfig\"\r\n        class=\"notice\"\r\n        :style=\"classObjNotice\"\r\n      >\r\n        <div\r\n          class=\"notice-content\"\r\n          :style=\"classObj\"\r\n          v-html=\"dialogConfig.noticeConfig.content\"\r\n        />\r\n      </div>\r\n      <div slot=\"footer\" class=\"dialog-footer\">\r\n        <!--附件列表 begin-->\r\n        <div v-if=\"dialogConfig.noticeConfig.files.length\" class=\"file-box\">\r\n          <span class=\"file-top\">\r\n            <a\r\n              title=\"批量下载\"\r\n              @click.prevent=\"\r\n                downloadAllFile(\r\n                  dialogConfig.noticeConfig.title + '.zip ',\r\n                  dialogConfig.noticeConfig.files\r\n                )\r\n              \"\r\n            >\r\n              <sun-svg-icon icon-class=\"color-file\" />\r\n              <span class=\"link-font-color\">附件</span>\r\n              <span>({{ dialogConfig.noticeConfig.files.length }})</span>\r\n            </a>\r\n          </span>\r\n          <ul\r\n            v-show=\"dialogConfig.noticeConfig.files.length > 0\"\r\n            class=\"attach-file-over\"\r\n          >\r\n            <li\r\n              v-for=\"(item, index) in dialogConfig.noticeConfig.files\"\r\n              :key=\"index\"\r\n            >\r\n              <div class=\"filesFl\">\r\n                <!-- <i class=\"el-icon-folder\" /> -->\r\n                <svg class=\"link-svg-file\">\r\n                  <use :xlink:href=\"fileIcon(item.name)\" />\r\n                </svg>\r\n              </div>\r\n              <div class=\"filesFl filesFlB\">\r\n                <a :title=\"item.name\">{{ item.name }}</a>\r\n                <a\r\n                  class=\"function-button\"\r\n                  title=\"下载\"\r\n                  @click.prevent=\"\r\n                    downloadFile(item.name, item.url + '##' + item.name)\r\n                  \"\r\n                >\r\n                  <span>下载</span>\r\n                </a>\r\n                <span class=\"function-button-pre\" title=\"预览\" @click=\"filPre(item.name, item.url + '##' + item.name)\">预览</span>\r\n              </div>\r\n            </li>\r\n          </ul>\r\n        </div>\r\n        <!--附件列表 end-->\r\n        <el-row :gutter=\"0\" style=\"margin-top: 1rem\">\r\n          <el-col :span=\"12\" class=\"leftBtn\">\r\n            <!--左侧按钮-->\r\n            <slot name=\"leftBtn\" />\r\n          </el-col>\r\n          <el-col :span=\"$scopedSlots.leftBtn ? 12 : 24\" class=\"rightBtn\">\r\n            <!--右侧按钮-->\r\n            <slot name=\"rightBtn\" />\r\n            <el-button\r\n              v-if=\"dialogConfig.btnCancle === false ? false : true\"\r\n              @click=\"dialogClose\"\r\n            >取 消</el-button>\r\n            <el-button\r\n              v-if=\"dialogConfig.btnSubmit === false ? false : true\"\r\n              type=\"primary\"\r\n              @click=\"dialogSubmit\"\r\n            >确 定</el-button>\r\n          </el-col>\r\n        </el-row>\r\n      </div>\r\n    </el-dialog>\r\n    <Preview :dialog-config=\"previewData\" @dialogClose=\"closePreview\" />\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport * as Base64 from 'js-base64'\r\nimport { commonMsgConfirm } from '@/utils/message.js' // 提示信息\r\nimport elDragDialog from '@/directive/el-drag-dialog' // base on element-ui\r\nimport { dateTimeFormat, organNoFormat } from '@/filters'\r\nimport { commonDownLoadFile, uploadFileUrl } from '@/utils/common' // 公共方法\r\nimport screenfull from 'screenfull'\r\nimport { encryptResult } from '@/utils/crypto' // 加密解密密码\r\nimport Preview from '@/components/Dialog/SunPreview'\r\nimport { dateNowFormat10 } from '@/utils/date.js'\r\nimport defaultSettings from '@/settings'\r\nconst prefix = defaultSettings.service.system // 前缀公共路由\r\n// import { Screenfull } from '@/components' // 全屏\r\n// import { getToken } from '@/utils/auth'\r\n\r\n// import { system } from '@/api'\r\n// const { upload } = system.SysApproval // 下载接口\r\n// import store from '@/store'\r\nexport default {\r\n  name: 'SunNoticeDialog',\r\n  directives: { elDragDialog },\r\n  filters: {\r\n    organNoFormat,\r\n    dateTimeFormat\r\n  },\r\n  components: {\r\n    // Screenfull\r\n    Preview\r\n  },\r\n  // inheritAttrs: false,\r\n  props: {\r\n    dialogConfig: {\r\n      type: Object,\r\n      default: () => {\r\n        return {\r\n          visible: false, // 显示隐藏配置\r\n          btnSubmit: true, // 确定按钮\r\n          btnCancle: true, // 取消按钮\r\n          componentProps: {\r\n            // 弹出框属性\r\n            title: '表格弹出框', // 弹出框标题\r\n            width: '' // 当前弹出框宽度 默认80%\r\n          },\r\n          noticeConfig: {\r\n            // 消息对象\r\n            imgSrc: '', // 背景图片加载\r\n            title: '标题', // 公告标题\r\n            info: ['总行运营管理部', '门户超级管理员', '2022-03-23 16:19:45'], // 发布机构、发布者名称、发布时间\r\n            readNum: 0, // 阅读量\r\n            content: '这是一段正文', // 公告正文\r\n            files: [] // 附件\r\n          }\r\n        }\r\n      }\r\n    }\r\n  },\r\n  data() {\r\n    return {\r\n      previewData: {\r\n        visible: false,\r\n        title: '预览',\r\n        width: '100%',\r\n        height: '50%',\r\n        imageSrc: '', // 组件显示内容\r\n        type: '' // 预览组件类型(eg: pdf, xls)\r\n      },\r\n      btn: false,\r\n      isFullscreen: false,\r\n      fullscreen: true,\r\n      classObj: { height: '20rem' }, // 公告内容盒子\r\n      classObjNotice: { height: '100%' }, // 公告body\r\n      classObjTitle: { top: '1.6rem' } // 公告标题\r\n    }\r\n  },\r\n  watch: {\r\n    'dialogConfig.visible': {\r\n      // 打开、关闭弹出框\r\n      handler(val) {\r\n        if (val) {\r\n          this.resizeHandler()\r\n        } else if (this.isFullscreen && !val) {\r\n          this.click()\r\n        }\r\n      },\r\n      deep: true\r\n    },\r\n    isFullscreen: {\r\n      // 打开，关闭全屏功能\r\n      handler(val) {\r\n        if (val) {\r\n          this.classObj.height = '100%'\r\n          this.classObjNotice.height = '80%'\r\n          this.classObjTitle.top = '34%'\r\n        } else {\r\n          this.classObj.height = '20rem'\r\n          this.classObjNotice.height = '100%'\r\n          this.classObjTitle.top = '1.6rem'\r\n        }\r\n      },\r\n      deep: true\r\n    }\r\n  },\r\n  mounted() {\r\n    this.$nextTick(() => {\r\n      this.resizeHandler()\r\n      this.init()\r\n    })\r\n  },\r\n  beforeDestroy() {\r\n    this.destroy()\r\n  },\r\n  methods: {\r\n    filPre(fileName, saveFileName) {\r\n      this.fiePreview(fileName, saveFileName)\r\n      this.previewData.visible = true\r\n    },\r\n    /**\r\n     * 文件预览方法\r\n     * @param {String} fileName 文件名\r\n     * @param {String} fileUrl 文件路径\r\n     */\r\n    fiePreview(fileName, fileUrl) {\r\n      const loginKkIP = this.$store.getters.loginKkIP // kk ip地址\r\n      const loginKkport = this.$store.getters.loginKkport // kk 端口号\r\n      const type = fileName.split('.').reverse()[0] // 文件类型 xls pdf jpg\r\n      const encryptFileName = encodeURIComponent(\r\n        encryptResult(\r\n          this.$store.getters.initParams.enSecMap.encryptType,\r\n          fileName\r\n        )\r\n      ) // fileName '1111.pdf'\r\n      const encryptFName = encodeURIComponent(\r\n        encryptResult(\r\n          this.$store.getters.initParams.enSecMap.encryptType,\r\n          fileUrl.split('##')[0]\r\n        )\r\n      ) // fileUrl  \"/home/<USER>/TempFiles/uploadTemp/c3231d5e-359c-4bce-9540-a9ed35f6a4f8_1111.pdf\"\r\n      const fileNames = fileName.split('##')[0]\r\n      const watermark = dateNowFormat10() + ` ${this.$store.getters.userName}`\r\n      const href = window.location.href.split('#')[0].slice(0, -1) // 生产环境 http://**********/dopUnify/\r\n\r\n      const urls =\r\n        href + process.env.VUE_APP_BASE_API + prefix + '/download/file.do' // 生产环境地址\r\n      // const urls = 'http://127.0.0.1:5001/dev-api/unify/download/file.do' // 开发环境地址 /prod-api-------------------\r\n\r\n      const str = `${urls}?fileName=${encryptFileName}&saveFileName=${encryptFName}&isEncrypt=1&fullfilename=${fileNames}`\r\n\r\n      const aa = `http://${loginKkIP}:${loginKkport}/onlinePreview?url=${encodeURIComponent(\r\n        Base64.encode(str)\r\n      )}&watermarkTxt=${encodeURIComponent(watermark)}`// 开发环境\r\n\r\n      // const aa = `http://${this.$store.getters.loginKkIP}:${\r\n      //   this.$store.getters.loginKkport\r\n      // }/onlinePreview?url=${encodeURIComponent(\r\n      //   Base64.encode(str)\r\n      // )}&watermarkTxt=${encodeURIComponent(watermark)}`\r\n      // const aa = `http://***********:8018/onlinePreview?url=${encodeURIComponent(\r\n      //   Base64.encode(str)\r\n      // )}&watermarkTxt=${encodeURIComponent(watermark)}` // 生产环境地址\r\n\r\n      if (type === 'pdf') {\r\n        this.previewData.imageSrc = str\r\n      } else if (type === 'jpg') {\r\n        this.previewData.imageSrc = str\r\n      } else if (type === 'xls') {\r\n        this.previewData.imageSrc = aa\r\n      } else if (type === 'xlsx') {\r\n        this.previewData.imageSrc = aa\r\n      } else if (type === 'doc') {\r\n        this.previewData.imageSrc = aa\r\n      } else if (type === 'docx') {\r\n        this.previewData.imageSrc = aa\r\n      }\r\n    },\r\n    closePreview(param) {\r\n      this.previewData.visible = param\r\n    },\r\n    /**\r\n     * 页面尺寸配置*/\r\n    resizeHandler() {\r\n      if (this.$refs.tableContent) {\r\n        const screenHeight = this.$refs.tableContent.offsetHeight // 父节点高\r\n        if (this.dialogConfig.btnCancle || this.dialogConfig.btnSubmit) {\r\n          this.dialogConfig.tableConfig.componentProps['height'] =\r\n            screenHeight - 102 + 'px'\r\n        } else {\r\n          this.dialogConfig.tableConfig.componentProps['height'] =\r\n            screenHeight - 62 + 'px'\r\n        }\r\n        this.dialogConfig.tableConfig = { ...this.dialogConfig.tableConfig } // 数据监听\r\n      }\r\n    },\r\n    click() {\r\n      if (!screenfull.enabled) {\r\n        this.$message({\r\n          message: 'you browser can not work',\r\n          type: 'warning'\r\n        })\r\n        return false\r\n      }\r\n      if (!!window.ActiveXObject || 'ActiveXObject' in window) {\r\n        // 全屏模式兼容ie begin\r\n        if (!this.isFullscreen) {\r\n          this.$refs.dialogScreenfull.$refs.dialog.style.marginTop = '0'\r\n          screenfull.request(this.$refs.dialogScreenfull.$refs.dialog)\r\n        } else {\r\n          this.$refs.dialogScreenfull.$refs.dialog.style.marginTop = '15vh'\r\n          screenfull.exit()\r\n        }\r\n        // 全屏模式兼容ie end\r\n      } else {\r\n        screenfull.request(this.$refs.dialogScreenfull.$refs.dialog)\r\n        screenfull.exit()\r\n      }\r\n      // screenfull.request(this.$refs.dialogScreenfull.$refs.dialog)\r\n    },\r\n    change() {\r\n      this.isFullscreen = screenfull.isFullscreen\r\n    },\r\n    init() {\r\n      if (screenfull.enabled) {\r\n        screenfull.on('change', this.change)\r\n      }\r\n    },\r\n    destroy() {\r\n      if (screenfull.enabled) {\r\n        screenfull.off('change', this.change)\r\n      }\r\n    },\r\n    /**\r\n     * 文件 - 批量下载\r\n     * @param {String} fileName 文件名\r\n     * @param {String} saveFileName 文件路径*/\r\n    downloadAllFile(fileName, saveFileName) {\r\n      commonMsgConfirm('是否确认批量下载?', this, (param) => {\r\n        if (param) {\r\n          saveFileName = uploadFileUrl(saveFileName)\r\n          commonDownLoadFile(fileName, saveFileName)\r\n        }\r\n      })\r\n    },\r\n    /**\r\n     * 文件 - 下载\r\n     * @param {String} fileName 文件名\r\n     * @param {String} saveFileName 文件路径*/\r\n    downloadFile(fileName, saveFileName) {\r\n      commonMsgConfirm('是否确认下载?', this, (param) => {\r\n        if (param) {\r\n          commonDownLoadFile(fileName, saveFileName)\r\n        }\r\n      })\r\n      // const path = `/system/fileController/fileDownload.do`\r\n      // console.log(fileName, saveFileName)\r\n      // const form = document.getElementById('uploadForm')\r\n      // form.setAttribute('style', 'display:none')\r\n      // form.setAttribute('target', '')\r\n      // form.setAttribute('method', 'post')\r\n      // form.setAttribute(\r\n      //   'action',\r\n      //   `http://127.0.0.1:9527/dev-api/system/fileController/fileDownload.do?Authorization=${getToken()}`\r\n      // ) // 下载文件的请求路径\r\n\r\n      // const input1 = document.createElement('input')\r\n      // const input2 = document.createElement('input')\r\n\r\n      // input1.setAttribute('type', 'text')\r\n      // input1.setAttribute('name', 'fileName')\r\n      // input1.setAttribute('value', fileName.replace(/\\s+/g, ''))\r\n\r\n      // input2.setAttribute('type', 'text')\r\n      // input2.setAttribute('name', 'saveFileName')\r\n      // input2.setAttribute('value', saveFileName)\r\n      // form.appendChild(input1)\r\n      // form.appendChild(input2)\r\n\r\n      // form.submit()\r\n    },\r\n    /**\r\n     * 文件 - 打开（预览）\r\n     * @param {Boolean} param */\r\n    openFile() {\r\n      alert('待开发')\r\n    },\r\n    /**\r\n     * 文件 - 转存\r\n     * @param {Boolean} param */\r\n    fileInto() {\r\n      alert('待开发')\r\n    },\r\n    /**\r\n     * 文件图标判断\r\n     * @param {String} name 文件的名称 */\r\n    fileIcon(name) {\r\n      const arr = name.split('.') // 切割文件名称之后的数组\r\n      const length = arr.length // 文件数组的长度\r\n      const type = arr[length - 1]\r\n      let icon = ''\r\n      // 考虑各种情况的附件图标\r\n      if (type === 'png' || type === 'jpg' || type === 'jpeg') {\r\n        icon = '#icon-jpg'\r\n      } else if (type === 'doc' || type === 'docx') {\r\n        icon = '#icon-doc'\r\n      } else if (type === 'xls' || type === 'xlsx') {\r\n        icon = '#icon-xls'\r\n      } else if (type === 'ppt') {\r\n        icon = '#icon-ppt'\r\n      } else if (type === 'pdf') {\r\n        icon = '#icon-pdf-two'\r\n      } else if (type === 'zip' || type === 'rar') {\r\n        icon = '#icon-zip-two'\r\n      } else if (type === 'mov') {\r\n        icon = '#icon-mov'\r\n      } else if (type === 'exe') {\r\n        icon = '#icon-exe'\r\n      } else if (type === 'txt') {\r\n        icon = '#icon-txt'\r\n      }\r\n      return icon\r\n    },\r\n    /**\r\n     * 弹出框关闭\r\n     */\r\n    dialogClose() {\r\n      this.$nextTick(() => {\r\n        this.$emit('dialogClose', false)\r\n      })\r\n    },\r\n    /**\r\n     * 确定*/\r\n    dialogSubmit() {\r\n      this.$emit('dialogSubmit')\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n@import '~@/assets/scss/common/variable/variable/color.scss';\r\n@import '~@/assets/scss/common/variable/variable/size.scss';\r\n$color_border: #f2f2f2;\r\n$color: #606266;\r\n$color_border_2: #d4d9e4;\r\n$font_title: 1.6rem;\r\n$font_info: 1.6rem;\r\n$font_content: 1.4rem;\r\n$title-color: #333333;\r\n.el-dialog__wrapper {\r\n  overflow: hidden;\r\n}\r\n// ::v-deep {\r\n//   .el-dialog {\r\n//     width: 100% !important;\r\n//     height: 100% !important;\r\n//     margin-top: 0 !important;\r\n//   }\r\n// }\r\n\r\n::v-deep {\r\n  .el-dialog__body {\r\n    padding: 0 0 0 0;\r\n    height: 80%;\r\n  }\r\n}\r\n.full-screen {\r\n  width: 100%;\r\n  height: 100%;\r\n  margin-top: 0;\r\n}\r\n.rightBtn {\r\n  // display: flex;\r\n  // justify-content: flex-end;\r\n  text-align: right;\r\n}\r\n.leftBtn {\r\n  text-align: left;\r\n}\r\n// 全屏按钮\r\n.full-icon {\r\n  position: absolute;\r\n  top: 2.1rem;\r\n  right: 5rem;\r\n  cursor: pointer;\r\n}\r\n.dialog-footer {\r\n  // 弹出框底部\r\n  margin-top: -1%;\r\n  .file-box {\r\n    // 上传列表样式\r\n    border: 1px solid $colorBorder;\r\n    border-radius: $pageRadius;\r\n    overflow: hidden;\r\n    height: 11rem;\r\n    // 上传列表 顶部样式\r\n    .file-top {\r\n      width: 100%;\r\n      text-align: left;\r\n      padding-left: 5px;\r\n      background: $color_li;\r\n      display: block;\r\n      height: 2.4rem;\r\n      line-height: 2.4rem;\r\n      .link-svg-file {\r\n        width: 18px;\r\n        height: 13px;\r\n      }\r\n      .link-font-color {\r\n        font-size: 1.5rem;\r\n        font-family: 'Noto Sans S Chinese';\r\n        font-weight: 900;\r\n        color: $title-color;\r\n        margin-right: 5px;\r\n        margin-left: 1rem;\r\n      }\r\n    }\r\n    // 上传列表 内容样式\r\n    .attach-file-over {\r\n      overflow: auto;\r\n      position: static;\r\n      width: 100%;\r\n      list-style-type: none;\r\n      margin: 0;\r\n      height: 6.2rem;\r\n      padding-left: 2.1rem;\r\n      margin-top: 1rem;\r\n      margin-bottom: 2rem;\r\n      li {\r\n        float: left;\r\n        height: 45px;\r\n        width: 200px;\r\n        line-height: 35px;\r\n        margin-right: 2.5rem;\r\n        margin-top: 10px;\r\n        .filesFl {\r\n          float: left;\r\n          .link-svg-file {\r\n            width: 3rem;\r\n            height: 3.8rem;\r\n            fill: currentColor;\r\n            overflow: hidden;\r\n          }\r\n          a {\r\n            display: block;\r\n            text-align: left;\r\n            font-size: 12px;\r\n            margin-top: -15px;\r\n            white-space: nowrap;\r\n            overflow: hidden;\r\n            text-overflow: ellipsis;\r\n            min-width: 6rem;\r\n            &:nth-child(1) {\r\n              display: block;\r\n              margin-top: 0;\r\n              font-style: 13px;\r\n            }\r\n          }\r\n          .function-button {\r\n            text-align: left;\r\n            font-size: 12px;\r\n            margin-top: -15px;\r\n            float: left;\r\n            margin-right: 1rem;\r\n            color: $color_main;\r\n          }\r\n          .function-button-pre {\r\n            text-align: left;\r\n            font-size: 12px;\r\n            margin-right: 1rem;\r\n            color: $color_main;\r\n            margin-top: -15px;\r\n            float: left;\r\n            cursor: pointer;;\r\n          }\r\n        }\r\n        .filesFlB {\r\n          width: 15rem;\r\n          margin: -5px 5px 0 5px;\r\n        }\r\n      }\r\n    }\r\n  }\r\n}\r\n.top-img {\r\n  position: relative;\r\n  .notice-top {\r\n    display: flex;\r\n    justify-content: center;\r\n    flex-direction: column;\r\n    align-items: center;\r\n    text-align: center;\r\n    position: absolute;\r\n    left: 50%;\r\n    transform: translateX(-50%);\r\n    .notice-title {\r\n      font-size: $font_title;\r\n      font-family: 'Noto Sans S Chinese';\r\n      font-weight: 900;\r\n      color: $title-color;\r\n      padding: 0 0 0.8rem 0;\r\n    }\r\n    .notice-info {\r\n      font-size: $contFont;\r\n      opacity: 0.5;\r\n      width: 130%;\r\n      ::v-deep {\r\n        span {\r\n          border-right: 2px solid #b4b5b7;\r\n          margin-right: 0.6rem;\r\n          padding-right: 0.6rem;\r\n          &:last-child {\r\n            border-right: none;\r\n          }\r\n        }\r\n      }\r\n    }\r\n  }\r\n  .imgonly {\r\n    width: 100%;\r\n  }\r\n}\r\n\r\n.notice {\r\n  padding: 0 0 0 2.3rem;\r\n  font-size: $font_content;\r\n  // color: $color;\r\n  position: relative;\r\n  .notice-content {\r\n    line-height: 2rem;\r\n    overflow: auto;\r\n    padding-right: 2.3rem;\r\n    padding-top: 0.8rem;\r\n  }\r\n}\r\n</style>\r\n"]}]}