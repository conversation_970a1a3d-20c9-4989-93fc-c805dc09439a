{"remainingRequest": "E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-q<PERSON><PERSON>n\\数字运营平台-统一门户工程\\dopUnify\\node_modules\\babel-loader\\lib\\index.js!E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan\\数字运营平台-统一门户工程\\dopUnify\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan\\数字运营平台-统一门户工程\\dopUnify\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan\\数字运营平台-统一门户工程\\dopUnify\\src\\components\\Layout\\ThemePicker\\index.vue?vue&type=script&lang=js&", "dependencies": [{"path": "E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-<PERSON><PERSON><PERSON>n\\数字运营平台-统一门户工程\\dopUnify\\src\\components\\Layout\\ThemePicker\\index.vue", "mtime": 1686019810044}, {"path": "E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-<PERSON><PERSON><PERSON>n\\数字运营平台-统一门户工程\\dopUnify\\babel.config.js", "mtime": 1686019818359}, {"path": "E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-<PERSON><PERSON><PERSON>n\\数字运营平台-统一门户工程\\dopUnify\\.babelrc", "mtime": 1705285078668}, {"path": "E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-<PERSON><PERSON><PERSON>n\\数字运营平台-统一门户工程\\dopUnify\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-<PERSON><PERSON><PERSON>n\\数字运营平台-统一门户工程\\dopUnify\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-<PERSON><PERSON><PERSON>n\\数字运营平台-统一门户工程\\dopUnify\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-<PERSON><PERSON><PERSON>n\\数字运营平台-统一门户工程\\dopUnify\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1655715099000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;AAUA;AACA;AACA;;AAEA;EACAA;IACA;MACAC;MAAA;MACAC;IACA;EACA;EACAC;IACAC;MACA;IACA;EACA;EACAC;IACAD;MACAE;QACA;MACA;MACAC;IACA;IACAL;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBACAM;gBAAA,MACA;kBAAA;kBAAA;gBAAA;gBAAA;cAAA;gBACAC;gBACAC,kEACA;gBAEAC;kBACAC;kBACAC;kBACAC;kBACAC;kBACAC;gBACA;gBAEAC;kBACA;oBACA;oBACA;oBAEA;oBACA;sBACAC;sBACAA;sBACAC;oBACA;oBACAD;kBACA;gBACA,GAEA;gBACA;gBACA;gBACA;gBACA;gBAAA,CACAE;gBAEAC;gBAEAA;gBAEAC,2DACAC;kBACA;kBACA;gBACA;gBACAD;kBACA;kBACA;kBACAE;gBACA;gBAEA;gBAEAb;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IACA;EACA;EACAc;IAAA;IACA;MACA;IACA;EACA;EACAC;IACAC;MACA;MACAC;QACAC;MACA;MACA;IACA;IAEAC;MAAA;MACA;QACA;QACAC;UACA;YACA;YACAC;UACA;QACA;QACAD;QACAA;MACA;IACA;IAEAE;MACA;QACA;QACA;QACA;QAEA;UAAA;UACA;QACA;UACAC;UACAC;UACAC;UAEAF;UACAC;UACAC;UAEA;QACA;MACA;MAEA;QACA;QACA;QACA;QAEAF;QACAC;QACAC;QAEAF;QACAC;QACAC;QAEA;MACA;MAEA;MACA;QACAC;MACA;MACAA;MACA;IACA;EACA;AACA", "names": ["data", "chalk", "theme", "computed", "defaultTheme", "watch", "handler", "immediate", "oldVal", "themeCluster", "originalCluster", "$message", "message", "customClass", "type", "duration", "iconClass", "<PERSON><PERSON><PERSON><PERSON>", "styleTag", "document", "replace", "<PERSON><PERSON><PERSON><PERSON>", "styles", "filter", "style", "mounted", "methods", "updateStyle", "oldCluster", "newStyle", "getCSSString", "xhr", "resolve", "getThemeCluster", "red", "green", "blue", "clusters"], "sourceRoot": "src/components/Layout/ThemePicker", "sources": ["index.vue"], "sourcesContent": ["<template>\n  <el-color-picker\n    v-model=\"theme\"\n    :predefine=\"['#409EFF', '#1890ff', '#304156','#212121','#11a983', '#13c2c2', '#6959CD', '#f5222d', ]\"\n    class=\"theme-picker\"\n    popper-class=\"theme-picker-dropdown\"\n  />\n</template>\n\n<script>\nimport ElementUiCSS from './ElementUiCSS.js' // 就是上文说的默认样式 index.css; (字符串)\n// const version = require('element-ui/package.json').version // element-ui version from node_modules\nconst ORIGINAL_THEME = '#409EFF' // default color\n\nexport default {\n  data() {\n    return {\n      chalk: '', // content of theme-chalk css\n      theme: ''\n    }\n  },\n  computed: {\n    defaultTheme() {\n      return this.$store.state.settings.theme\n    }\n  },\n  watch: {\n    defaultTheme: {\n      handler: function(val, oldVal) {\n        this.theme = val\n      },\n      immediate: true\n    },\n    async theme(val) {\n      const oldVal = this.chalk ? this.theme : this.$store.state.settings.theme\n      if (typeof val !== 'string') return\n      const themeCluster = this.getThemeCluster(val.replace('#', ''))\n      const originalCluster = this.getThemeCluster(oldVal.replace('#', ''))\n      // console.log(themeCluster, originalCluster)\n\n      const $message = this.$message({\n        message: '  Compiling the theme',\n        customClass: 'theme-message',\n        type: 'success',\n        duration: 0,\n        iconClass: 'el-icon-loading'\n      })\n\n      const getHandler = (variable, id) => {\n        return () => {\n          const originalCluster = this.getThemeCluster(ORIGINAL_THEME.replace('#', ''))\n          const newStyle = this.updateStyle(this[variable], originalCluster, themeCluster)\n\n          let styleTag = document.getElementById(id)\n          if (!styleTag) {\n            styleTag = document.createElement('style')\n            styleTag.setAttribute('id', id)\n            document.head.appendChild(styleTag)\n          }\n          styleTag.innerText = newStyle\n        }\n      }\n\n      // if (!this.chalk) {\n      //   const url = `https://unpkg.com/element-ui@${version}/lib/theme-chalk/index.css`\n      //   await this.getCSSString(url, 'chalk')\n      // }\n      this.chalk = ElementUiCSS.replace(/@font-face{[^}]+}/g, '') // 去掉字体样式\n        .replace(/.el-icon-[a-zA-Z0-9-:^]+before{content:\"[^}]+}/g, '') // 去掉图标样式\n\n      const chalkHandler = getHandler('chalk', 'chalk-style')\n\n      chalkHandler()\n\n      const styles = [].slice.call(document.querySelectorAll('style'))\n        .filter(style => {\n          const text = style.innerText\n          return new RegExp(oldVal, 'i').test(text) && !/Chalk Variables/.test(text)\n        })\n      styles.forEach(style => {\n        const { innerText } = style\n        if (typeof innerText !== 'string') return\n        style.innerText = this.updateStyle(innerText, originalCluster, themeCluster)\n      })\n\n      this.$emit('change', val)\n\n      $message.close()\n    }\n  },\n  mounted() {\n    this.$nextTick(() => {\n      this.theme = this.$store.state.settings.theme\n    })\n  },\n  methods: {\n    updateStyle(style, oldCluster, newCluster) {\n      let newStyle = style\n      oldCluster.forEach((color, index) => {\n        newStyle = newStyle.replace(new RegExp(color, 'ig'), newCluster[index])\n      })\n      return newStyle\n    },\n\n    getCSSString(url, variable) {\n      return new Promise(resolve => {\n        const xhr = new XMLHttpRequest()\n        xhr.onreadystatechange = () => {\n          if (xhr.readyState === 4 && xhr.status === 200) {\n            this[variable] = xhr.responseText.replace(/@font-face{[^}]+}/, '')\n            resolve()\n          }\n        }\n        xhr.open('GET', url)\n        xhr.send()\n      })\n    },\n\n    getThemeCluster(theme) {\n      const tintColor = (color, tint) => {\n        let red = parseInt(color.slice(0, 2), 16)\n        let green = parseInt(color.slice(2, 4), 16)\n        let blue = parseInt(color.slice(4, 6), 16)\n\n        if (tint === 0) { // when primary color is in its rgb space\n          return [red, green, blue].join(',')\n        } else {\n          red += Math.round(tint * (255 - red))\n          green += Math.round(tint * (255 - green))\n          blue += Math.round(tint * (255 - blue))\n\n          red = red.toString(16)\n          green = green.toString(16)\n          blue = blue.toString(16)\n\n          return `#${red}${green}${blue}`\n        }\n      }\n\n      const shadeColor = (color, shade) => {\n        let red = parseInt(color.slice(0, 2), 16)\n        let green = parseInt(color.slice(2, 4), 16)\n        let blue = parseInt(color.slice(4, 6), 16)\n\n        red = Math.round((1 - shade) * red)\n        green = Math.round((1 - shade) * green)\n        blue = Math.round((1 - shade) * blue)\n\n        red = red.toString(16)\n        green = green.toString(16)\n        blue = blue.toString(16)\n\n        return `#${red}${green}${blue}`\n      }\n\n      const clusters = [theme]\n      for (let i = 0; i <= 9; i++) {\n        clusters.push(tintColor(theme, Number((i / 10).toFixed(2))))\n      }\n      clusters.push(shadeColor(theme, 0.1))\n      return clusters\n    }\n  }\n}\n</script>\n\n<style>\n.theme-message,\n.theme-picker-dropdown {\n  z-index: 99999 !important;\n}\n\n.theme-picker .el-color-picker__trigger {\n  height: 26px !important;\n  width: 26px !important;\n  padding: 2px;\n}\n\n.theme-picker-dropdown .el-color-dropdown__link-btn {\n  display: none;\n}\n</style>\n"]}]}