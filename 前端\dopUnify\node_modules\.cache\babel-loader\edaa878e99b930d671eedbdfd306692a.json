{"remainingRequest": "D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\node_modules\\babel-loader\\lib\\index.js!D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\node_modules\\sunui\\srcobf\\components\\Dialog\\SunFormDialog\\index.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\node_modules\\sunui\\srcobf\\components\\Dialog\\SunFormDialog\\index.vue", "mtime": 499162500000}, {"path": "D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\babel.config.js", "mtime": 1686019818359}, {"path": "D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1655715099000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KCmltcG9ydCBlbERyYWdEaWFsb2cgZnJvbSAnLi4vLi4vLi4vZGlyZWN0aXZlL2VsLWRyYWctZGlhbG9nJzsgLy8g5by55Ye65qGG5Y+v5ouW5YqoCmltcG9ydCBTdW5Gb3JtIGZyb20gJy4uLy4uL1N1bkZvcm0nOyAvLyDooajljZUKZXhwb3J0IGRlZmF1bHQgewogIG5hbWU6ICdTdW5Gb3JtRGlhbG9nJywKICBjb21wb25lbnRzOiB7CiAgICBTdW5Gb3JtOiBTdW5Gb3JtCiAgfSwKICBkaXJlY3RpdmVzOiB7CiAgICBlbERyYWdEaWFsb2c6IGVsRHJhZ0RpYWxvZwogIH0sCiAgaW5oZXJpdEF0dHJzOiBmYWxzZSwKICBwcm9wczogewogICAgZGlhbG9nQ29uZmlnOiB7CiAgICAgIHR5cGU6IE9iamVjdCwKICAgICAgZGVmYXVsdDogZnVuY3Rpb24gX2RlZmF1bHQoKSB7CiAgICAgICAgcmV0dXJuIHsKICAgICAgICAgIGN1c3RvbUJ1dHRvbjogZmFsc2UsCiAgICAgICAgICAvLyDmmK/lkKbpnIDopoHpu5jorqTnmoTlvLnnqpfmjInpkq4KICAgICAgICAgIGxvYWRpbmc6IGZhbHNlLAogICAgICAgICAgLy8g6buY6K6kbG9hZGluZ+mF<PERSON>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"}, {"version": 3, "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAkFA;AACA;AACA;EACAA;EACAC;IAAAC;EAAA;EACAC;IAAAC;EAAA;EACAC;EACAC;IACAC;MACAC;MACAC;QACA;UACAC;UAAA;UACAC;UAAA;UACAC;UAAA;UACAC;YACA;YACAC;YAAA;YACAC;YAAA;YACAC;UACA;;UACAC;YACA;YACAC;YAAA;YACAC;YAAA;YACAC;UACA;QACA;MACA;IACA;;IACAC;MACAb;MACAC;IACA;EACA;EACAa;IACA;MACAC;MACAC;MAAA;MACAC;MAAA;MACAC;IACA;EACA;;EACAC;IACA;MACA;MACAC;QAAA;QACA;UACA;YACA;YACA;UACA;QACA;UACA;YACA;YACA;UACA;QACA;MACA;MACAC;IACA;EACA;EACAC;IACAC;EACA;EACAC;IACAD;EACA;EACAE;IACA;AACA;AACA;IACAC;MAAA;MACA;MACA;QACA;MACA;IACA;IACA;AACA;IACAC;MACA;IACA;IACA;AACA;AACA;IACAC;MACA;QACA;QACA;MACA;QACA;MACA;IACA;IACA;IACAC;MAAA;MACA;QACA;QACA;QACA;QACA;QACA;UACA;UACA;UACA;UACA;UACA;UACA;QACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA,mEACA;UACA;UACA;UACA;UACA;UACA;QACA;MACA;IACA;EACA;AACA", "names": ["name", "components", "SunForm", "directives", "elDragDialog", "inheritAttrs", "props", "dialogConfig", "type", "default", "customButton", "loading", "visible", "componentProps", "title", "width", "destroyOnClose", "form", "config", "defaultForm", "labelWidth", "nowFunction", "data", "btn", "windowHeight", "dialogHeight", "dragFlag", "watch", "handler", "deep", "mounted", "window", "<PERSON><PERSON><PERSON><PERSON>", "methods", "dialogClose", "dialogSubmit", "validateForm", "getHeight"], "sourceRoot": "node_modules/sunui/srcobf/components/Dialog/SunFormDialog", "sources": ["index.vue"], "sourcesContent": ["<!--\n* 弹出框：单个表单\n-->\n<template>\n  <el-dialog\n    v-if=\"dragFlag\"\n    ref=\"refDialog\"\n    v-el-drag-dialog\n    :visible.sync=\"dialogConfig.visible\"\n    :before-close=\"dialogClose\"\n    :destroy-on-close=\"true\"\n    :append-to-body=\"true\"\n    :close-on-click-modal=\"false\"\n    v-bind=\"dialogConfig.componentProps\"\n  >\n    <div v-loading=\"dialogConfig.loading\">\n      <sun-form\n        ref=\"refFormDialog\"\n        :query=\"btn\"\n        :reset=\"btn\"\n        v-bind=\"dialogConfig.form\"\n        @validateForm=\"validateForm\"\n      >\n        <template v-if=\"dialogConfig.form.menuIcon\">\n          <div\n            v-for=\"item in dialogConfig.form.config.menu_icon.options\"\n            :key=\"item.value\"\n            :slot=\"'menu_icon_' + item.value\"\n          >\n            <sun-svg-icon :icon-class=\"'color-'+ item.value\" />\n            {{ item.value }}\n          </div>\n        </template>\n      </sun-form>\n      <slot name=\"other\" />\n      <div slot=\"footer\" class=\"dialog-footer\">\n        <div class=\"footerRightBtn\">\n          <slot name=\"rightBtn\" />\n        </div>\n        <div v-if=\"!dialogConfig.customButton\">\n          <el-button @click=\"dialogClose\">取 消</el-button>\n          <el-button type=\"primary\" @click=\"dialogSubmit\">确 定</el-button>\n        </div>\n      </div>\n    </div>\n  </el-dialog>\n  <el-dialog\n    v-else\n    ref=\"refDialog\"\n    :visible.sync=\"dialogConfig.visible\"\n    :before-close=\"dialogClose\"\n    :destroy-on-close=\"true\"\n    :append-to-body=\"true\"\n    :close-on-click-modal=\"false\"\n    v-bind=\"dialogConfig.componentProps\"\n  >\n    <div v-loading=\"dialogConfig.loading\">\n      <sun-form\n        ref=\"refFormDialog\"\n        :query=\"btn\"\n        :reset=\"btn\"\n        v-bind=\"dialogConfig.form\"\n        @validateForm=\"validateForm\"\n      >\n        <!-- <template slot=\"header\">\n      </template> -->\n      </sun-form>\n      <slot name=\"other\" />\n      <div slot=\"footer\" class=\"dialog-footer\">\n        <div class=\"footerRightBtn\">\n          <slot name=\"rightBtn\" />\n        </div>\n        <div v-if=\"!dialogConfig.customButton\">\n          <el-button @click=\"dialogClose\">取 消</el-button>\n          <el-button type=\"primary\" @click=\"dialogSubmit\">确 定</el-button>\n        </div>\n      </div>\n    </div>\n  </el-dialog>\n</template>\n\n<script>\nimport elDragDialog from '../../../directive/el-drag-dialog' // 弹出框可拖动\nimport SunForm from '../../SunForm' // 表单\nexport default {\n  name: 'SunFormDialog',\n  components: { SunForm },\n  directives: { elDragDialog },\n  inheritAttrs: false,\n  props: {\n    dialogConfig: {\n      type: Object,\n      default: () => {\n        return {\n          customButton: false, // 是否需要默认的弹窗按钮\n          loading: false, // 默认loading配置\n          visible: false, // 显示隐藏配置\n          componentProps: {\n            // 弹出框属性\n            title: '表单弹出框', // 弹出框标题\n            width: '', // 当前弹出框宽度 默认80%\n            destroyOnClose: true // 关闭时销毁 Dialog 中的元素\n          },\n          form: {\n            // 表单属性\n            config: {}, // 表单项配置\n            defaultForm: {}, // 默认值配置\n            labelWidth: '10rem' // 当前表单标签宽度配置\n          }\n        }\n      }\n    },\n    nowFunction: {\n      type: String,\n      default: 'dialogSubmit'\n    }\n  },\n  data() {\n    return {\n      btn: false,\n      windowHeight: '', // 浏览器的高度\n      dialogHeight: '', // 弹窗的高度\n      dragFlag: true // 拖拽标识\n    }\n  },\n  watch: {\n    'dialogConfig.visible': {\n      // 当前选中组件改变，重新获取当前表单组件中所有的 字段标识 name\n      handler(val) {\n        if (val === false) {\n          this.$nextTick(() => {\n            this.dragFlag = false\n            this.$refs['refFormDialog'].resetForm()\n          })\n        } else {\n          this.$nextTick(() => {\n            this.dragFlag = true\n            this.getHeight()\n          })\n        }\n      },\n      deep: true\n    }\n  },\n  mounted() {\n    window.addEventListener('resize', this.getHeight)\n  },\n  beforeDestroy() {\n    window.removeEventListener('resize', this.getHeight)\n  },\n  methods: {\n    /**\n     * 弹出框：关闭\n     */\n    dialogClose() {\n      this.$refs['refFormDialog'].resetForm()\n      this.$nextTick(() => {\n        this.$emit('dialogClose', false)\n      })\n    },\n    /**\n     * 确定*/\n    dialogSubmit() {\n      this.$refs['refFormDialog'].validateForm()\n    },\n    /**\n     * 表单校验\n     * @param {Boolean}valid 校验返回值*/\n    validateForm(valid) {\n      if (valid) {\n        this.$emit('dialogSubmit', this.dialogConfig.form.defaultForm)\n        // this.dialogClose()\n      } else {\n        return false\n      }\n    },\n    // 获取浏览器窗口高度与弹窗高度\n    getHeight() {\n      this.$nextTick(() => {\n        this.windowHeight = window.innerHeight\n        this.dialogHeight = this.$refs.refDialog.$refs.dialog.offsetHeight\n        // 判断二者之间大小关系，做出相应操作\n        // 当浏览器窗口>弹窗高度\n        if (this.windowHeight > this.dialogHeight) {\n          const dialogTop = this.windowHeight - this.dialogHeight\n          // 设置弹窗上外边距\n          // this.$refs.refDialog.$refs.dialog.style.marginTop =\n          //   dialogTop / 2 + 'px'\n          const marginTop = dialogTop / 2 + 'px'\n          this.$refs.refDialog.$refs.dialog.style.margin = `${marginTop} auto 0`\n        } else {\n          // 当浏览器窗口<弹窗高度\n          // 弹窗总高度\n          this.$refs.refDialog.$refs.dialog.style.height = '83%'\n          // 获取更改后的总高度\n          const dialogHeight = this.$refs.refDialog.$refs.dialog.offsetHeight\n          // 弹窗body区域百分比高度\n          this.$refs.refDialog.$refs.dialog.childNodes[1].style.height = '83%'\n          this.$refs.refDialog.$refs.dialog.childNodes[1].style.overflow =\n            'auto'\n          // 设置弹窗上外边距\n          // this.$refs.refDialog.$refs.dialog.style.marginTop =\n          //   (this.windowHeight - dialogHeight) / 2 + 'px'\n          const marginTop = (this.windowHeight - dialogHeight) / 2 + 'px'\n          this.$refs.refDialog.$refs.dialog.style.margin = `${marginTop} auto 0`\n        }\n      })\n    }\n  }\n}\n</script>\n\n<style lang=\"scss\" scoped>\n.dialog-footer {\n  display: flex;\n  justify-content: flex-end;\n  .footerRightBtn {\n    margin-right: 10px;\n    .rightBtn {\n      margin: 0rem 20rem 0rem 2rem;\n      position: absolute;\n      right: 2rem;\n    }\n  }\n}\n</style>\n"]}]}