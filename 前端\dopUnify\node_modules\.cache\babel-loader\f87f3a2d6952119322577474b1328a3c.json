{"remainingRequest": "D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\node_modules\\babel-loader\\lib\\index.js!D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\node_modules\\@babel\\runtime\\helpers\\createForOfIteratorHelper.js", "dependencies": [{"path": "D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\node_modules\\@babel\\runtime\\helpers\\createForOfIteratorHelper.js", "mtime": 456789000000}, {"path": "D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\babel.config.js", "mtime": 1686019818359}, {"path": "D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["unsupportedIterableToArray", "require", "_createForOfIteratorHelper", "o", "allowArrayLike", "it", "Symbol", "iterator", "Array", "isArray", "length", "i", "F", "s", "n", "done", "value", "e", "_e", "f", "TypeError", "normalCompletion", "didErr", "err", "call", "step", "next", "_e2", "module", "exports", "__esModule"], "sources": ["D:/1_Project/XYD_Project/dop-4.0/dop-4.1-qianduan-unify/dopUnify/node_modules/@babel/runtime/helpers/createForOfIteratorHelper.js"], "sourcesContent": ["var unsupportedIterableToArray = require(\"./unsupportedIterableToArray.js\");\nfunction _createForOfIteratorHelper(o, allowArrayLike) {\n  var it = typeof Symbol !== \"undefined\" && o[Symbol.iterator] || o[\"@@iterator\"];\n  if (!it) {\n    if (Array.isArray(o) || (it = unsupportedIterableToArray(o)) || allowArrayLike && o && typeof o.length === \"number\") {\n      if (it) o = it;\n      var i = 0;\n      var F = function F() {};\n      return {\n        s: F,\n        n: function n() {\n          if (i >= o.length) return {\n            done: true\n          };\n          return {\n            done: false,\n            value: o[i++]\n          };\n        },\n        e: function e(_e) {\n          throw _e;\n        },\n        f: F\n      };\n    }\n    throw new TypeError(\"Invalid attempt to iterate non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.\");\n  }\n  var normalCompletion = true,\n    didErr = false,\n    err;\n  return {\n    s: function s() {\n      it = it.call(o);\n    },\n    n: function n() {\n      var step = it.next();\n      normalCompletion = step.done;\n      return step;\n    },\n    e: function e(_e2) {\n      didErr = true;\n      err = _e2;\n    },\n    f: function f() {\n      try {\n        if (!normalCompletion && it[\"return\"] != null) it[\"return\"]();\n      } finally {\n        if (didErr) throw err;\n      }\n    }\n  };\n}\nmodule.exports = _createForOfIteratorHelper, module.exports.__esModule = true, module.exports[\"default\"] = module.exports;"], "mappings": ";;;;;;AAAA,IAAIA,0BAA0B,GAAGC,OAAO,CAAC,iCAAiC,CAAC;AAC3E,SAASC,0BAA0B,CAACC,CAAC,EAAEC,cAAc,EAAE;EACrD,IAAIC,EAAE,GAAG,OAAOC,MAAM,KAAK,WAAW,IAAIH,CAAC,CAACG,MAAM,CAACC,QAAQ,CAAC,IAAIJ,CAAC,CAAC,YAAY,CAAC;EAC/E,IAAI,CAACE,EAAE,EAAE;IACP,IAAIG,KAAK,CAACC,OAAO,CAACN,CAAC,CAAC,KAAKE,EAAE,GAAGL,0BAA0B,CAACG,CAAC,CAAC,CAAC,IAAIC,cAAc,IAAID,CAAC,IAAI,OAAOA,CAAC,CAACO,MAAM,KAAK,QAAQ,EAAE;MACnH,IAAIL,EAAE,EAAEF,CAAC,GAAGE,EAAE;MACd,IAAIM,CAAC,GAAG,CAAC;MACT,IAAIC,CAAC,GAAG,SAASA,CAAC,GAAG,CAAC,CAAC;MACvB,OAAO;QACLC,CAAC,EAAED,CAAC;QACJE,CAAC,EAAE,SAASA,CAAC,GAAG;UACd,IAAIH,CAAC,IAAIR,CAAC,CAACO,MAAM,EAAE,OAAO;YACxBK,IAAI,EAAE;UACR,CAAC;UACD,OAAO;YACLA,IAAI,EAAE,KAAK;YACXC,KAAK,EAAEb,CAAC,CAACQ,CAAC,EAAE;UACd,CAAC;QACH,CAAC;QACDM,CAAC,EAAE,SAASA,CAAC,CAACC,EAAE,EAAE;UAChB,MAAMA,EAAE;QACV,CAAC;QACDC,CAAC,EAAEP;MACL,CAAC;IACH;IACA,MAAM,IAAIQ,SAAS,CAAC,uIAAuI,CAAC;EAC9J;EACA,IAAIC,gBAAgB,GAAG,IAAI;IACzBC,MAAM,GAAG,KAAK;IACdC,GAAG;EACL,OAAO;IACLV,CAAC,EAAE,SAASA,CAAC,GAAG;MACdR,EAAE,GAAGA,EAAE,CAACmB,IAAI,CAACrB,CAAC,CAAC;IACjB,CAAC;IACDW,CAAC,EAAE,SAASA,CAAC,GAAG;MACd,IAAIW,IAAI,GAAGpB,EAAE,CAACqB,IAAI,EAAE;MACpBL,gBAAgB,GAAGI,IAAI,CAACV,IAAI;MAC5B,OAAOU,IAAI;IACb,CAAC;IACDR,CAAC,EAAE,SAASA,CAAC,CAACU,GAAG,EAAE;MACjBL,MAAM,GAAG,IAAI;MACbC,GAAG,GAAGI,GAAG;IACX,CAAC;IACDR,CAAC,EAAE,SAASA,CAAC,GAAG;MACd,IAAI;QACF,IAAI,CAACE,gBAAgB,IAAIhB,EAAE,CAAC,QAAQ,CAAC,IAAI,IAAI,EAAEA,EAAE,CAAC,QAAQ,CAAC,EAAE;MAC/D,CAAC,SAAS;QACR,IAAIiB,MAAM,EAAE,MAAMC,GAAG;MACvB;IACF;EACF,CAAC;AACH;AACAK,MAAM,CAACC,OAAO,GAAG3B,0BAA0B,EAAE0B,MAAM,CAACC,OAAO,CAACC,UAAU,GAAG,IAAI,EAAEF,MAAM,CAACC,OAAO,CAAC,SAAS,CAAC,GAAGD,MAAM,CAACC,OAAO"}]}