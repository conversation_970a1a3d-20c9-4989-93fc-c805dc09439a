{"remainingRequest": "D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\node_modules\\babel-loader\\lib\\index.js!D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\src\\views\\system\\externalManage\\external\\index.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\src\\views\\system\\externalManage\\external\\index.vue", "mtime": 1686019808982}, {"path": "D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\babel.config.js", "mtime": 1686019818359}, {"path": "D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\.babelrc", "mtime": 1705285078668}, {"path": "D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1655715099000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:aW1wb3J0ICJjb3JlLWpzL21vZHVsZXMvZXMuYXJyYXkubWFwLmpzIjsKLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KCmltcG9ydCB7IGNvbmZpZyB9IGZyb20gJy4vaW5mbyc7IC8vIOihqOWNlemFjee9rgppbXBvcnQgVGFibGVMaXN0IGZyb20gJy4vY29tcG9uZW50L3RhYmxlJzsgLy8g6KGo5qC8CmltcG9ydCB7IHBlcm1pc3Npb25zQnRuIH0gZnJvbSAnQC91dGlscy9wZXJtaXNzaW9ucyc7IC8vIOadg+mZkOmFjee9rgoKZXhwb3J0IGRlZmF1bHQgewogIG5hbWU6ICdNYW5hZ2VFeHRlcm5hbCcsCiAgY29tcG9uZW50czogewogICAgVGFibGVMaXN0OiBUYWJsZUxpc3QKICB9LAogIGRhdGE6IGZ1bmN0aW9uIGRhdGEoKSB7CiAgICByZXR1cm4gewogICAgICBjb25maWc6IGNvbmZpZyh0aGlzKSwKICAgICAgZGVmYXVsdEZvcm06IHsKICAgICAgICBvcmdhbl9ubzogJycsCiAgICAgICAgdXNlcl9ubzogJycsCiAgICAgICAgZXh0ZXJuYWxfc3lzdGVtX25vOiAnJywKICAgICAgICBsYXN0X3RpbWU6IFtdCiAgICAgIH0sCiAgICAgIGJ0bkFsbDogewogICAgICAgIC8vIOW9k+WJjemhtemcgOimgemFjee9ruadg+mZkOeahOaMiemSriAg5p2D6ZmQ6I635Y+WCiAgICAgICAgYnRuUXVlcnk6IGZhbHNlLAogICAgICAgIGJ0bkFkZDogdHJ1ZSwKICAgICAgICBidG5EZWxldGU6IHRydWUsCiAgICAgICAgYnRuTW9kaWZ5OiB0cnVlCiAgICAgIH0sCiAgICAgIHN5c3RlbUFycmF5OiBbXSwKICAgICAgLy8g57O757uf57yW5Y+3CiAgICAgIHVzZXJOb0FycjogW10gLy8g55So5oi357yW5Y+3CiAgICB9OwogIH0sCiAgY3JlYXRlZDogZnVuY3Rpb24gY3JlYXRlZCgpIHsKICAgIHRoaXMuYnRuUGVybWlzc2lvbnMoKTsKICAgIHRoaXMuZ2V0RGlzcGF0Y2goKTsKICB9LAogIG1vdW50ZWQ6IGZ1bmN0aW9uIG1vdW50ZWQoKSB7CiAgICB2YXIgX3RoaXMgPSB0aGlzOwogICAgdGhpcy4kbmV4dFRpY2soKS50aGVuKGZ1bmN0aW9uICgpIHsKICAgICAgX3RoaXMub3JnYW5UcmVlR2V0KCk7CiAgICAgIGlmIChfdGhpcy5idG5BbGwuYnRuUXVlcnkpIHsKICAgICAgICBfdGhpcy5xdWVyeUxpc3QoKTsKICAgICAgfQogICAgfSk7CiAgfSwKICBtZXRob2RzOiB7CiAgICAvKioNCiAgICAgKiDmjInpkq7mnYPpmZDphY3nva4qLwogICAgYnRuUGVybWlzc2lvbnM6IGZ1bmN0aW9uIGJ0blBlcm1pc3Npb25zKCkgewogICAgICB0aGlzLmJ0bkFsbCA9IHBlcm1pc3Npb25zQnRuKHRoaXMuJGF0dHJzLmJ1dHRvbl9pZCwgdGhpcy5idG5BbGwpOwogICAgfSwKICAgIC8qKg0KICAgICAqIOmFjee9ruacuuaehOagkSovCiAgICBvcmdhblRyZWVHZXQ6IGZ1bmN0aW9uIG9yZ2FuVHJlZUdldCgpIHsKICAgICAgdGhpcy5jb25maWcub3JnYW5fbm8ub3B0aW9ucyA9IHRoaXMuJHN0b3JlLmdldHRlcnMub3JnYW5UcmVlOwogICAgfSwKICAgIC8qKg0KICAgICAqIOiOt+WPluWklumDqOWtl+WFuCovCiAgICBnZXREaXNwYXRjaDogZnVuY3Rpb24gZ2V0RGlzcGF0Y2goKSB7CiAgICAgIHZhciBfdGhpczIgPSB0aGlzOwogICAgICAvLyDns7vnu5/nvJblj7cKICAgICAgdGhpcy4kc3RvcmUuZGlzcGF0Y2goJ2NvbW1vbi9zZXRFeHRlcm5hbERhdGEnLCAnRVhURVJOQUxfU1lTVEVNX05PJykudGhlbihmdW5jdGlvbiAocmVzKSB7CiAgICAgICAgdmFyIFN5c3RlbUFycmF5ID0gW107CiAgICAgICAgcmVzLm1hcChmdW5jdGlvbiAoaXRlbSkgewogICAgICAgICAgdmFyIHZhbHVlUyA9IE9iamVjdC5hc3NpZ24oe30sIGl0ZW0sIHsKICAgICAgICAgICAgbGFiZWw6IGl0ZW0udmFsdWUgKyAnLScgKyBpdGVtLmxhYmVsCiAgICAgICAgICB9KTsKICAgICAgICAgIFN5c3RlbUFycmF5LnB1c2godmFsdWVTKTsKICAgICAgICB9KTsKICAgICAgICBfdGhpczIuY29uZmlnLmV4dGVybmFsX3N5c3RlbV9uby5vcHRpb25zID0gU3lzdGVtQXJyYXk7CiAgICAgICAgX3RoaXMyLnN5c3RlbUFycmF5ID0gU3lzdGVtQXJyYXk7CiAgICAgIH0pOwogICAgICAvLyDnlKjmiLfnvJblj7cKICAgICAgdGhpcy4kc3RvcmUuZGlzcGF0Y2goJ2NvbW1vbi9zZXRFeHRlcm5hbERhdGEnLCAnVVNFUl9OTycpLnRoZW4oZnVuY3Rpb24gKHJlcykgewogICAgICAgIF90aGlzMi51c2VOb0FyciA9IHJlczsKICAgICAgfSk7CiAgICB9LAogICAgLyoqDQogICAgICog6KGo5Y2V5qCh6aqMDQogICAgICogQHBhcmFtIHtCb29sZWFufXZhbGlkIOagoemqjOi/lOWbnuWAvCovCiAgICB2YWxpZGF0ZUZvcm06IGZ1bmN0aW9uIHZhbGlkYXRlRm9ybSh2YWxpZCkgewogICAgICBpZiAodmFsaWQpIHsKICAgICAgICB0aGlzLiRyZWZzLnRhYmxlTGlzdFJlZi5xdWVyeUxpc3QoMSk7CiAgICAgIH0gZWxzZSB7CiAgICAgICAgcmV0dXJuIGZhbHNlOwogICAgICB9CiAgICB9LAogICAgLyoqDQogICAgICog5oyJ6ZKu77ya5p+l6K+iKi8KICAgIHF1ZXJ5TGlzdDogZnVuY3Rpb24gcXVlcnlMaXN0KCkgewogICAgICB0aGlzLiRyZWZzWydmb3JtUmVmJ10udmFsaWRhdGVGb3JtKCk7CiAgICB9CiAgfQp9Ow=="}, {"version": 3, "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA4BA;AACA;AACA;;AAEA;EACAA;EACAC;IAAAC;EAAA;EACAC;IACA;MACAC;MACAC;QACAC;QACAC;QACAC;QACAC;MACA;MACAC;QACA;QACAC;QACAC;QACAC;QACAC;MACA;MACAC;MAAA;MACAC;IACA;EACA;EACAC;IACA;IACA;EACA;EACAC;IAAA;IACA;MACA;MACA;QACA;MACA;IACA;EACA;EACAC;IACA;AACA;IACAC;MACA;IACA;IACA;AACA;IACAC;MACA;IACA;IACA;AACA;IACAC;MAAA;MACA;MACA,YACAC,yDACAC;QACA;QACAC;UACA;YACAC;UACA;UACAC;QACA;QACA;QACA;MACA;MACA;MACA;QACA;MACA;IACA;IACA;AACA;AACA;IACAC;MACA;QACA;MACA;QACA;MACA;IACA;IACA;AACA;IACAC;MACA;IACA;EACA;AACA", "names": ["name", "components", "TableList", "data", "config", "defaultForm", "organ_no", "user_no", "external_system_no", "last_time", "btnAll", "btnQuery", "btnAdd", "btnDelete", "btnModify", "systemArray", "userNoArr", "created", "mounted", "methods", "btnPermissions", "organTreeGet", "getDispatch", "dispatch", "then", "res", "label", "SystemArray", "validateForm", "queryList"], "sourceRoot": "src/views/system/externalManage/external", "sources": ["index.vue"], "sourcesContent": ["<!--\r\n* 用户关联系统管理\r\n-->\r\n<template>\r\n  <div class=\"app-container\">\r\n    <div class=\"sun-content\">\r\n      <div class=\"filter-container\">\r\n        <sun-form\r\n          ref=\"formRef\"\r\n          :config=\"config\"\r\n          :default-form=\"defaultForm\"\r\n          label-width=\"12rem\"\r\n          :query=\"btnAll.btnQuery\"\r\n          @query=\"queryList\"\r\n          @validateForm=\"validateForm\"\r\n        />\r\n      </div>\r\n    </div>\r\n    <table-list\r\n      ref=\"tableListRef\"\r\n      :default-form=\"defaultForm\"\r\n      :btn-all=\"btnAll\"\r\n      :system-array=\"systemArray\"\r\n      :user-no-arr=\"useNoArr\"\r\n    />\r\n  </div>\r\n</template>\r\n<script>\r\nimport { config } from './info' // 表单配置\r\nimport TableList from './component/table' // 表格\r\nimport { permissionsBtn } from '@/utils/permissions' // 权限配置\r\n\r\nexport default {\r\n  name: 'ManageExternal',\r\n  components: { TableList },\r\n  data() {\r\n    return {\r\n      config: config(this),\r\n      defaultForm: {\r\n        organ_no: '',\r\n        user_no: '',\r\n        external_system_no: '',\r\n        last_time: []\r\n      },\r\n      btnAll: {\r\n        // 当前页需要配置权限的按钮  权限获取\r\n        btnQuery: false,\r\n        btnAdd: true,\r\n        btnDelete: true,\r\n        btnModify: true\r\n      },\r\n      systemArray: [], // 系统编号\r\n      userNoArr: [] // 用户编号\r\n    }\r\n  },\r\n  created() {\r\n    this.btnPermissions()\r\n    this.getDispatch()\r\n  },\r\n  mounted() {\r\n    this.$nextTick().then(() => {\r\n      this.organTreeGet()\r\n      if (this.btnAll.btnQuery) {\r\n        this.queryList()\r\n      }\r\n    })\r\n  },\r\n  methods: {\r\n    /**\r\n     * 按钮权限配置*/\r\n    btnPermissions() {\r\n      this.btnAll = permissionsBtn(this.$attrs.button_id, this.btnAll)\r\n    },\r\n    /**\r\n     * 配置机构树*/\r\n    organTreeGet() {\r\n      this.config.organ_no.options = this.$store.getters.organTree\r\n    },\r\n    /**\r\n     * 获取外部字典*/\r\n    getDispatch() {\r\n      // 系统编号\r\n      this.$store\r\n        .dispatch('common/setExternalData', 'EXTERNAL_SYSTEM_NO')\r\n        .then((res) => {\r\n          const SystemArray = []\r\n          res.map(function(item) {\r\n            const valueS = Object.assign({}, item, {\r\n              label: item.value + '-' + item.label\r\n            })\r\n            SystemArray.push(valueS)\r\n          })\r\n          this.config.external_system_no.options = SystemArray\r\n          this.systemArray = SystemArray\r\n        })\r\n      // 用户编号\r\n      this.$store.dispatch('common/setExternalData', 'USER_NO').then((res) => {\r\n        this.useNoArr = res\r\n      })\r\n    },\r\n    /**\r\n     * 表单校验\r\n     * @param {Boolean}valid 校验返回值*/\r\n    validateForm(valid) {\r\n      if (valid) {\r\n        this.$refs.tableListRef.queryList(1)\r\n      } else {\r\n        return false\r\n      }\r\n    },\r\n    /**\r\n     * 按钮：查询*/\r\n    queryList() {\r\n      this.$refs['formRef'].validateForm()\r\n    }\r\n  }\r\n}\r\n</script>\r\n<style scoped>\r\n.app-container {\r\n  width: 100%;\r\n  height: 100%;\r\n}\r\n</style>\r\n"]}]}