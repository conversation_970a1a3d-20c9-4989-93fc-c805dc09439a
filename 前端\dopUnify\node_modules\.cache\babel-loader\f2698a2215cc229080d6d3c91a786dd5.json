{"remainingRequest": "E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\node_modules\\babel-loader\\lib\\index.js!E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\node_modules\\eslint-loader\\index.js??ref--13-0!E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\src\\api\\views\\system\\index.js", "dependencies": [{"path": "E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\src\\api\\views\\system\\index.js", "mtime": 1686019810685}, {"path": "E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\babel.config.js", "mtime": 1686019818359}, {"path": "E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\.babelrc", "mtime": 1705285078668}, {"path": "E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\node_modules\\eslint-loader\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["SysUser", "SysParams", "SysMenu", "SysDictionary", "<PERSON><PERSON><PERSON>rgan", "SysRole", "SysApplication", "SysPrac", "SysEcm", "SysEcmConf", "SysExt", "SysEnc", "SysMes", "SysTimingSer", "SysRelease", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SysSystem", "SysExterRole", "SysExterUser", "SysExterExternal", "SysExterPriv", "SysExterPost", "SysOutDefint", "SysOutSys", "SysOutPerm", "SysOutReg", "SysLink", "SysLog", "SysHoliday", "SysTiming", "system"], "sources": ["E:/1_Project/XYD_Project/dop-4.0/dop-4.1-qianduan-unify/dopUnify/src/api/views/system/index.js"], "sourcesContent": ["// 系统管理 begin\nimport { SysUser } from './user' // 用户管理\nimport { SysParams } from './config/params' // 参数设置\n\nimport { SysMenu } from './config/menu' // 菜单配置\nimport { SysDictionary } from './config/dictionary' // 数据字典\nimport { SysOrgan } from './organ' // 机构管理\n\nimport { SysRole } from './role' // 角色管理\nimport { SysApplication } from './config/application' // 应用服务配置\n\nimport { SysPrac } from './config/prac' // 公告审批配置\nimport { SysEcm } from './config/ecm' // 内容管理服务定义\nimport { SysEcmConf } from './config/ecmConf' // 机构内容服务配置\nimport { SysExt } from './config/external' // 外表数据源配置\nimport { SysEnc } from './config/encryp' // 敏感字段加密配置\nimport { SysMes } from './config/message' // 消息分类配置SysTimingSer\nimport { SysTimingSer } from './config/timingService' // 定时服务配置\n// 公告管理begin\nimport { SysRelease } from './notice/release' // 发布公告\nimport { SysQuery } from './notice/query' // 查看公告\n\n// 公告管理end\n\n// 关联系统管理begin\nimport { SysSystem } from './externalManage/system' // 关联系统配置\nimport { SysExterRole } from './externalManage/role' // 关联系统角色管理\nimport { SysExterUser } from './externalManage/user' // 关联系统用户查询\nimport { SysExterExternal } from './externalManage/external' // 关联系统角色管理\nimport { SysExterPriv } from './externalManage/priv' // 关联系统机构权限配置\nimport { SysExterPost } from './externalManage/post' // 关联系统运营岗位配置\n// 关联系统管理end\n\n// 对外接口管理begin\nimport { SysOutDefint } from './outManage/definition' // 对外接口定义\nimport { SysOutSys } from './outManage/system' // 对外接口系统定义\nimport { SysOutPerm } from './outManage/permissions' // 对外接口系统权限配置\nimport { SysOutReg } from './outManage/registration' // 对外接口请求登记簿\n// 对外接口管理end\n\n// 日常维护begin\nimport { SysLink } from './dailyManage/sysLink' // 对外接口定义\nimport { SysLog } from './dailyManage/log' // 对外接口系统定义\nimport { SysHoliday } from './dailyManage/holiday' // 对外接口系统权限配置\nimport { SysTiming } from './dailyManage/timing' // 对外接口系统定义\n// 日常维护end\n\n// 系统管理 end\nexport const system = {\n  SysUser,\n  SysParams,\n  SysMenu,\n  SysRole,\n  SysDictionary,\n  SysApplication,\n  SysPrac,\n  SysEcm,\n  SysExt,\n  SysEcmConf,\n  SysRelease,\n  SysMes,\n  SysEnc,\n  SysOrgan,\n  SysSystem,\n  SysExterRole,\n  SysOutDefint,\n  SysOutSys,\n  SysOutPerm,\n  SysOutReg,\n  SysQuery,\n  SysLink,\n  SysLog,\n  SysHoliday,\n  SysExterUser,\n  SysExterExternal,\n  SysExterPriv,\n  SysExterPost,\n\n  SysTiming,\n  SysTimingSer\n}\n"], "mappings": "AAAA;AACA,SAASA,OAAO,QAAQ,QAAQ,EAAC;AACjC,SAASC,SAAS,QAAQ,iBAAiB,EAAC;;AAE5C,SAASC,OAAO,QAAQ,eAAe,EAAC;AACxC,SAASC,aAAa,QAAQ,qBAAqB,EAAC;AACpD,SAASC,QAAQ,QAAQ,SAAS,EAAC;;AAEnC,SAASC,OAAO,QAAQ,QAAQ,EAAC;AACjC,SAASC,cAAc,QAAQ,sBAAsB,EAAC;;AAEtD,SAASC,OAAO,QAAQ,eAAe,EAAC;AACxC,SAASC,MAAM,QAAQ,cAAc,EAAC;AACtC,SAASC,UAAU,QAAQ,kBAAkB,EAAC;AAC9C,SAASC,MAAM,QAAQ,mBAAmB,EAAC;AAC3C,SAASC,MAAM,QAAQ,iBAAiB,EAAC;AACzC,SAASC,MAAM,QAAQ,kBAAkB,EAAC;AAC1C,SAASC,YAAY,QAAQ,wBAAwB,EAAC;AACtD;AACA,SAASC,UAAU,QAAQ,kBAAkB,EAAC;AAC9C,SAASC,QAAQ,QAAQ,gBAAgB,EAAC;;AAE1C;;AAEA;AACA,SAASC,SAAS,QAAQ,yBAAyB,EAAC;AACpD,SAASC,YAAY,QAAQ,uBAAuB,EAAC;AACrD,SAASC,YAAY,QAAQ,uBAAuB,EAAC;AACrD,SAASC,gBAAgB,QAAQ,2BAA2B,EAAC;AAC7D,SAASC,YAAY,QAAQ,uBAAuB,EAAC;AACrD,SAASC,YAAY,QAAQ,uBAAuB,EAAC;AACrD;;AAEA;AACA,SAASC,YAAY,QAAQ,wBAAwB,EAAC;AACtD,SAASC,SAAS,QAAQ,oBAAoB,EAAC;AAC/C,SAASC,UAAU,QAAQ,yBAAyB,EAAC;AACrD,SAASC,SAAS,QAAQ,0BAA0B,EAAC;AACrD;;AAEA;AACA,SAASC,OAAO,QAAQ,uBAAuB,EAAC;AAChD,SAASC,MAAM,QAAQ,mBAAmB,EAAC;AAC3C,SAASC,UAAU,QAAQ,uBAAuB,EAAC;AACnD,SAASC,SAAS,QAAQ,sBAAsB,EAAC;AACjD;;AAEA;AACA,OAAO,IAAMC,MAAM,GAAG;EACpB9B,OAAO,EAAPA,OAAO;EACPC,SAAS,EAATA,SAAS;EACTC,OAAO,EAAPA,OAAO;EACPG,OAAO,EAAPA,OAAO;EACPF,aAAa,EAAbA,aAAa;EACbG,cAAc,EAAdA,cAAc;EACdC,OAAO,EAAPA,OAAO;EACPC,MAAM,EAANA,MAAM;EACNE,MAAM,EAANA,MAAM;EACND,UAAU,EAAVA,UAAU;EACVK,UAAU,EAAVA,UAAU;EACVF,MAAM,EAANA,MAAM;EACND,MAAM,EAANA,MAAM;EACNP,QAAQ,EAARA,QAAQ;EACRY,SAAS,EAATA,SAAS;EACTC,YAAY,EAAZA,YAAY;EACZK,YAAY,EAAZA,YAAY;EACZC,SAAS,EAATA,SAAS;EACTC,UAAU,EAAVA,UAAU;EACVC,SAAS,EAATA,SAAS;EACTV,QAAQ,EAARA,QAAQ;EACRW,OAAO,EAAPA,OAAO;EACPC,MAAM,EAANA,MAAM;EACNC,UAAU,EAAVA,UAAU;EACVV,YAAY,EAAZA,YAAY;EACZC,gBAAgB,EAAhBA,gBAAgB;EAChBC,YAAY,EAAZA,YAAY;EACZC,YAAY,EAAZA,YAAY;EAEZQ,SAAS,EAATA,SAAS;EACThB,YAAY,EAAZA;AACF,CAAC"}]}