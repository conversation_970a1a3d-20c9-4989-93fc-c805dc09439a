{"remainingRequest": "D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\node_modules\\babel-loader\\lib\\index.js!D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\node_modules\\eslint-loader\\index.js??ref--13-0!D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\src\\api\\views\\system\\config\\message\\index.js", "dependencies": [{"path": "D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\src\\api\\views\\system\\config\\message\\index.js", "mtime": 1686019810513}, {"path": "D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\babel.config.js", "mtime": 1686019818359}, {"path": "D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\node_modules\\eslint-loader\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["request", "defaultSettings", "prefix", "service", "system", "prefix2", "prefix3", "SysMes", "query", "data", "url", "method", "params", "message", "add", "modify", "del", "queryTree", "selectClassify", "queryMsg", "ignoreMsg", "unReadNum", "noticeModify"], "sources": ["D:/1_Project/XYD_Project/dop-4.0/dop-4.1-qian<PERSON>n-unify/dopUnify/src/api/views/system/config/message/index.js"], "sourcesContent": ["import request from '@/utils/request'\r\nimport defaultSettings from '@/settings'\r\nconst prefix = defaultSettings.service.system + '/sysMessage'\r\nconst prefix2 = defaultSettings.service.system + '/systemMsg'\r\nconst prefix3 = defaultSettings.service.system\r\n// 消息分类配置相关接口\r\nexport const SysMes = {\r\n  query(data) {\r\n    // 列表查询\r\n    return request({\r\n      url: prefix + '/query.do',\r\n      method: 'get',\r\n      params: { message: data }\r\n    })\r\n  },\r\n  add(data) {\r\n    // 新增\r\n    return request({\r\n      url: prefix + '/add.do',\r\n      method: 'post',\r\n      data\r\n    })\r\n  },\r\n  modify(data) {\r\n    // 修改\r\n    return request({\r\n      url: prefix + '/modify.do',\r\n      method: 'post',\r\n      data\r\n    })\r\n  },\r\n  del(data) {\r\n    // 删除\r\n    return request({\r\n      url: prefix + '/delete.do',\r\n      method: 'delete',\r\n      data\r\n    })\r\n  },\r\n  queryTree(data) {\r\n    // 左侧菜单列表树查询\r\n    return request({\r\n      url: prefix + '/sysMessage.do',\r\n      method: 'get',\r\n      params: { message: data }\r\n    })\r\n  },\r\n  selectClassify(data) {\r\n    // 系统消息类型查询\r\n    return request({\r\n      url: prefix2 + '/selectClassify.do',\r\n      method: 'get',\r\n      params: { message: data }\r\n    })\r\n  },\r\n  queryMsg(data) {\r\n    // 系统消息查询\r\n    return request({\r\n      url: prefix2 + '/query.do',\r\n      method: 'post',\r\n      data\r\n    })\r\n  },\r\n  ignoreMsg(data) {\r\n    // 系统消息卡片弹窗  忽略消息\r\n    return request({\r\n      url: prefix2 + '/ignoreMsg.do',\r\n      method: 'post',\r\n      data\r\n    })\r\n  },\r\n  unReadNum(data) {\r\n    // 系统消息卡片弹窗 未处理消息列表查询\r\n    return request({\r\n      url: prefix2 + '/initMsgQuary.do',\r\n      method: 'get',\r\n      params: { message: data }\r\n    })\r\n  },\r\n  noticeModify(data) {\r\n    // 修改公告阅读状态\r\n    return request({\r\n      url: prefix3 + '/notice/modify.do',\r\n      method: 'post',\r\n      data\r\n    })\r\n  }\r\n}\r\n"], "mappings": "AAAA,OAAOA,OAAO,MAAM,iBAAiB;AACrC,OAAOC,eAAe,MAAM,YAAY;AACxC,IAAMC,MAAM,GAAGD,eAAe,CAACE,OAAO,CAACC,MAAM,GAAG,aAAa;AAC7D,IAAMC,OAAO,GAAGJ,eAAe,CAACE,OAAO,CAACC,MAAM,GAAG,YAAY;AAC7D,IAAME,OAAO,GAAGL,eAAe,CAACE,OAAO,CAACC,MAAM;AAC9C;AACA,OAAO,IAAMG,MAAM,GAAG;EACpBC,KAAK,iBAACC,IAAI,EAAE;IACV;IACA,OAAOT,OAAO,CAAC;MACbU,GAAG,EAAER,MAAM,GAAG,WAAW;MACzBS,MAAM,EAAE,KAAK;MACbC,MAAM,EAAE;QAAEC,OAAO,EAAEJ;MAAK;IAC1B,CAAC,CAAC;EACJ,CAAC;EACDK,GAAG,eAACL,IAAI,EAAE;IACR;IACA,OAAOT,OAAO,CAAC;MACbU,GAAG,EAAER,MAAM,GAAG,SAAS;MACvBS,MAAM,EAAE,MAAM;MACdF,IAAI,EAAJA;IACF,CAAC,CAAC;EACJ,CAAC;EACDM,MAAM,kBAACN,IAAI,EAAE;IACX;IACA,OAAOT,OAAO,CAAC;MACbU,GAAG,EAAER,MAAM,GAAG,YAAY;MAC1BS,MAAM,EAAE,MAAM;MACdF,IAAI,EAAJA;IACF,CAAC,CAAC;EACJ,CAAC;EACDO,GAAG,eAACP,IAAI,EAAE;IACR;IACA,OAAOT,OAAO,CAAC;MACbU,GAAG,EAAER,MAAM,GAAG,YAAY;MAC1BS,MAAM,EAAE,QAAQ;MAChBF,IAAI,EAAJA;IACF,CAAC,CAAC;EACJ,CAAC;EACDQ,SAAS,qBAACR,IAAI,EAAE;IACd;IACA,OAAOT,OAAO,CAAC;MACbU,GAAG,EAAER,MAAM,GAAG,gBAAgB;MAC9BS,MAAM,EAAE,KAAK;MACbC,MAAM,EAAE;QAAEC,OAAO,EAAEJ;MAAK;IAC1B,CAAC,CAAC;EACJ,CAAC;EACDS,cAAc,0BAACT,IAAI,EAAE;IACnB;IACA,OAAOT,OAAO,CAAC;MACbU,GAAG,EAAEL,OAAO,GAAG,oBAAoB;MACnCM,MAAM,EAAE,KAAK;MACbC,MAAM,EAAE;QAAEC,OAAO,EAAEJ;MAAK;IAC1B,CAAC,CAAC;EACJ,CAAC;EACDU,QAAQ,oBAACV,IAAI,EAAE;IACb;IACA,OAAOT,OAAO,CAAC;MACbU,GAAG,EAAEL,OAAO,GAAG,WAAW;MAC1BM,MAAM,EAAE,MAAM;MACdF,IAAI,EAAJA;IACF,CAAC,CAAC;EACJ,CAAC;EACDW,SAAS,qBAACX,IAAI,EAAE;IACd;IACA,OAAOT,OAAO,CAAC;MACbU,GAAG,EAAEL,OAAO,GAAG,eAAe;MAC9BM,MAAM,EAAE,MAAM;MACdF,IAAI,EAAJA;IACF,CAAC,CAAC;EACJ,CAAC;EACDY,SAAS,qBAACZ,IAAI,EAAE;IACd;IACA,OAAOT,OAAO,CAAC;MACbU,GAAG,EAAEL,OAAO,GAAG,kBAAkB;MACjCM,MAAM,EAAE,KAAK;MACbC,MAAM,EAAE;QAAEC,OAAO,EAAEJ;MAAK;IAC1B,CAAC,CAAC;EACJ,CAAC;EACDa,YAAY,wBAACb,IAAI,EAAE;IACjB;IACA,OAAOT,OAAO,CAAC;MACbU,GAAG,EAAEJ,OAAO,GAAG,mBAAmB;MAClCK,MAAM,EAAE,MAAM;MACdF,IAAI,EAAJA;IACF,CAAC,CAAC;EACJ;AACF,CAAC"}]}