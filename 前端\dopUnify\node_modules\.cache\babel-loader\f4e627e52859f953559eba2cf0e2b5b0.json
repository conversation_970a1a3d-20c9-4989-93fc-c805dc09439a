{"remainingRequest": "E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-q<PERSON><PERSON>n\\数字运营平台-统一门户工程\\dopUnify\\node_modules\\babel-loader\\lib\\index.js!E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan\\数字运营平台-统一门户工程\\dopUnify\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan\\数字运营平台-统一门户工程\\dopUnify\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan\\数字运营平台-统一门户工程\\dopUnify\\src\\components\\SunTransfer\\index.vue?vue&type=script&lang=js&", "dependencies": [{"path": "E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-<PERSON><PERSON><PERSON>n\\数字运营平台-统一门户工程\\dopUnify\\src\\components\\SunTransfer\\index.vue", "mtime": 1686019810185}, {"path": "E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-<PERSON><PERSON><PERSON>n\\数字运营平台-统一门户工程\\dopUnify\\babel.config.js", "mtime": 1686019818359}, {"path": "E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-<PERSON><PERSON><PERSON>n\\数字运营平台-统一门户工程\\dopUnify\\.babelrc", "mtime": 1705285078668}, {"path": "E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-<PERSON><PERSON><PERSON>n\\数字运营平台-统一门户工程\\dopUnify\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-<PERSON><PERSON><PERSON>n\\数字运营平台-统一门户工程\\dopUnify\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-<PERSON><PERSON><PERSON>n\\数字运营平台-统一门户工程\\dopUnify\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-<PERSON><PERSON><PERSON>n\\数字运营平台-统一门户工程\\dopUnify\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1655715099000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, null]}