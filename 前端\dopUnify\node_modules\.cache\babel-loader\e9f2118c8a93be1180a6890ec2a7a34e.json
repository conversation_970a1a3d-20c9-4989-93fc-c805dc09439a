{"remainingRequest": "D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\node_modules\\babel-loader\\lib\\index.js!D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\node_modules\\eslint-loader\\index.js??ref--13-0!D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\src\\api\\views\\system\\config\\external\\index.js", "dependencies": [{"path": "D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\src\\api\\views\\system\\config\\external\\index.js", "mtime": 1686019810544}, {"path": "D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\babel.config.js", "mtime": 1686019818359}, {"path": "D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\.babelrc", "mtime": 1705285078668}, {"path": "D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\node_modules\\eslint-loader\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:aW1wb3J0IHJlcXVlc3QgZnJvbSAnQC91dGlscy9yZXF1ZXN0JzsKaW1wb3J0IGRlZmF1bHRTZXR0aW5ncyBmcm9tICdAL3NldHRpbmdzJzsKdmFyIHByZWZpeCA9IGRlZmF1bHRTZXR0aW5ncy5zZXJ2aWNlLnN5c3RlbSArICcvZXh0ZXJuYWxEYXRhJzsKCi8vIOWkluihqOaVsOaNrua6kOmFjee9ruebuOWFs+aOpeWPowpleHBvcnQgdmFyIFN5c0V4dCA9IHsKICBxdWVyeTogZnVuY3Rpb24gcXVlcnkoZGF0YSkgewogICAgLy8g55So5oi35p+l6K+iCiAgICByZXR1cm4gcmVxdWVzdCh7CiAgICAgIHVybDogcHJlZml4ICsgJy9xdWVyeS5kbycsCiAgICAgIG1ldGhvZDogJ2dldCcsCiAgICAgIHBhcmFtczogewogICAgICAgIG1lc3NhZ2U6IGRhdGEKICAgICAgfQogICAgfSk7CiAgfSwKICBjaGVjazogZnVuY3Rpb24gY2hlY2soZGF0YSkgewogICAgLy8g5qOA5rWLCiAgICByZXR1cm4gcmVxdWVzdCh7CiAgICAgIHVybDogcHJlZml4ICsgJy9jaGVja1NxbC5kbycsCiAgICAgIG1ldGhvZDogJ3Bvc3QnLAogICAgICBkYXRhOiBkYXRhCiAgICB9KTsKICB9LAogIGlkQ2hlY2s6IGZ1bmN0aW9uIGlkQ2hlY2soZGF0YSkgewogICAgLy8gaWTmo4DmtYvmmK/lkKbph43lpI0KICAgIHJldHVybiByZXF1ZXN0KHsKICAgICAgdXJsOiBwcmVmaXggKyAnL2lkQ2hlY2suZG8nLAogICAgICBtZXRob2Q6ICdnZXQnLAogICAgICBwYXJhbXM6IHsKICAgICAgICBtZXNzYWdlOiBkYXRhCiAgICAgIH0KICAgIH0pOwogIH0sCiAgYWRkOiBmdW5jdGlvbiBhZGQoZGF0YSkgewogICAgLy8g5paw5aKeCiAgICByZXR1cm4gcmVxdWVzdCh7CiAgICAgIHVybDogcHJlZml4ICsgJy9hZGQuZG8nLAogICAgICBtZXRob2Q6ICdwb3N0JywKICAgICAgZGF0YTogZGF0YQogICAgfSk7CiAgfSwKICBtb2RpZnk6IGZ1bmN0aW9uIG1vZGlmeShkYXRhKSB7CiAgICAvLyDkv67mlLkKICAgIHJldHVybiByZXF1ZXN0KHsKICAgICAgdXJsOiBwcmVmaXggKyAnL21vZGlmeS5kbycsCiAgICAgIG1ldGhvZDogJ3Bvc3QnLAogICAgICBkYXRhOiBkYXRhCiAgICB9KTsKICB9LAogIGRlbDogZnVuY3Rpb24gZGVsKGRhdGEpIHsKICAgIC8vIOWIoOmZpAogICAgcmV0dXJuIHJlcXVlc3QoewogICAgICB1cmw6IHByZWZpeCArICcvZGVsZXRlLmRvJywKICAgICAgbWV0aG9kOiAnZGVsZXRlJywKICAgICAgZGF0YTogZGF0YQogICAgfSk7CiAgfQp9Ow=="}, {"version": 3, "names": ["request", "defaultSettings", "prefix", "service", "system", "SysExt", "query", "data", "url", "method", "params", "message", "check", "id<PERSON><PERSON><PERSON>", "add", "modify", "del"], "sources": ["D:/1_Project/XYD_Project/dop-4.0/dop-4.1-qianduan-unify/dopUnify/src/api/views/system/config/external/index.js"], "sourcesContent": ["import request from '@/utils/request'\r\nimport defaultSettings from '@/settings'\r\nconst prefix = defaultSettings.service.system + '/externalData'\r\n\r\n// 外表数据源配置相关接口\r\nexport const SysExt = {\r\n  query(data) {\r\n    // 用户查询\r\n    return request({\r\n      url: prefix + '/query.do',\r\n      method: 'get',\r\n      params: { message: data }\r\n    })\r\n  },\r\n  check(data) {\r\n    // 检测\r\n    return request({\r\n      url: prefix + '/checkSql.do',\r\n      method: 'post',\r\n      data\r\n    })\r\n  },\r\n  idCheck(data) {\r\n    // id检测是否重复\r\n    return request({\r\n      url: prefix + '/idCheck.do',\r\n      method: 'get',\r\n      params: { message: data }\r\n    })\r\n  },\r\n  add(data) {\r\n    // 新增\r\n    return request({\r\n      url: prefix + '/add.do',\r\n      method: 'post',\r\n      data\r\n    })\r\n  },\r\n  modify(data) {\r\n    // 修改\r\n    return request({\r\n      url: prefix + '/modify.do',\r\n      method: 'post',\r\n      data\r\n    })\r\n  },\r\n  del(data) {\r\n    // 删除\r\n    return request({\r\n      url: prefix + '/delete.do',\r\n      method: 'delete',\r\n      data\r\n    })\r\n  }\r\n}\r\n"], "mappings": "AAAA,OAAOA,OAAO,MAAM,iBAAiB;AACrC,OAAOC,eAAe,MAAM,YAAY;AACxC,IAAMC,MAAM,GAAGD,eAAe,CAACE,OAAO,CAACC,MAAM,GAAG,eAAe;;AAE/D;AACA,OAAO,IAAMC,MAAM,GAAG;EACpBC,KAAK,iBAACC,IAAI,EAAE;IACV;IACA,OAAOP,OAAO,CAAC;MACbQ,GAAG,EAAEN,MAAM,GAAG,WAAW;MACzBO,MAAM,EAAE,KAAK;MACbC,MAAM,EAAE;QAAEC,OAAO,EAAEJ;MAAK;IAC1B,CAAC,CAAC;EACJ,CAAC;EACDK,KAAK,iBAACL,IAAI,EAAE;IACV;IACA,OAAOP,OAAO,CAAC;MACbQ,GAAG,EAAEN,MAAM,GAAG,cAAc;MAC5BO,MAAM,EAAE,MAAM;MACdF,IAAI,EAAJA;IACF,CAAC,CAAC;EACJ,CAAC;EACDM,OAAO,mBAACN,IAAI,EAAE;IACZ;IACA,OAAOP,OAAO,CAAC;MACbQ,GAAG,EAAEN,MAAM,GAAG,aAAa;MAC3BO,MAAM,EAAE,KAAK;MACbC,MAAM,EAAE;QAAEC,OAAO,EAAEJ;MAAK;IAC1B,CAAC,CAAC;EACJ,CAAC;EACDO,GAAG,eAACP,IAAI,EAAE;IACR;IACA,OAAOP,OAAO,CAAC;MACbQ,GAAG,EAAEN,MAAM,GAAG,SAAS;MACvBO,MAAM,EAAE,MAAM;MACdF,IAAI,EAAJA;IACF,CAAC,CAAC;EACJ,CAAC;EACDQ,MAAM,kBAACR,IAAI,EAAE;IACX;IACA,OAAOP,OAAO,CAAC;MACbQ,GAAG,EAAEN,MAAM,GAAG,YAAY;MAC1BO,MAAM,EAAE,MAAM;MACdF,IAAI,EAAJA;IACF,CAAC,CAAC;EACJ,CAAC;EACDS,GAAG,eAACT,IAAI,EAAE;IACR;IACA,OAAOP,OAAO,CAAC;MACbQ,GAAG,EAAEN,MAAM,GAAG,YAAY;MAC1BO,MAAM,EAAE,QAAQ;MAChBF,IAAI,EAAJA;IACF,CAAC,CAAC;EACJ;AACF,CAAC"}]}