{"remainingRequest": "E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\node_modules\\babel-loader\\lib\\index.js!E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\node_modules\\eslint-loader\\index.js??ref--13-0!E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\src\\views\\system\\config\\audit\\info.js", "dependencies": [{"path": "E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\src\\views\\system\\config\\audit\\info.js", "mtime": 1686019807904}, {"path": "E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\babel.config.js", "mtime": 1686019818359}, {"path": "E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\.babelrc", "mtime": 1705285078668}, {"path": "E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\node_modules\\eslint-loader\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:aW1wb3J0IHsgZGljdGlvbmFyeUZpZWRzIH0gZnJvbSAnQC91dGlscy9kaWN0aW9uYXJ5JzsgLy8g5a2X5YW46YWN572uCmV4cG9ydCB2YXIgY29uZmlnID0gZnVuY3Rpb24gY29uZmlnKHRoYXQpIHsKICByZXR1cm4gewogICAgbWVudV9pZDogewogICAgICBjb21wb25lbnQ6ICdpbnB1dCcsCiAgICAgIGxhYmVsOiAn6I+c5Y2VaWQnLAogICAgICBjb2xTcGFuOiA4LAogICAgICBuYW1lOiAnbWVudV9pZCcsCiAgICAgIGNvbmZpZzogewogICAgICAgIC8vIGZvcm0taXRlbSDphY3nva4KICAgICAgfSwKICAgICAgY29tcG9uZW50UHJvcHM6IHsKICAgICAgICAvLyBpbnB1dOe7hOS7tumFjee9rgogICAgICAgIHBsYWNlaG9sZGVyOiAn5pSv5oyB6I+c5Y2VaWTmqKHns4rmn6Xor6InLAogICAgICAgIGNsZWFyYWJsZTogdHJ1ZQogICAgICB9CiAgICB9LAogICAgbWVudV9uYW1lOiB7CiAgICAgIGNvbXBvbmVudDogJ2lucHV0JywKICAgICAgbGFiZWw6ICfoj5zljZXlkI3np7AnLAogICAgICBjb2xTcGFuOiA4LAogICAgICBuYW1lOiAnbWVudV9uYW1lJywKICAgICAgY29uZmlnOiB7fSwKICAgICAgY29tcG9uZW50UHJvcHM6IHsKICAgICAgICBwbGFjZWhvbGRlcjogJ+aUr+aMgeiPnOWNleWQjeensOaooeeziuafpeivoicsCiAgICAgICAgY2xlYXJhYmxlOiB0cnVlCiAgICAgIH0KICAgIH0sCiAgICBjaGVja19mbGFnOiB7CiAgICAgIGNvbXBvbmVudDogJ3NlbGVjdCcsCiAgICAgIGxhYmVsOiAn5a6h5qC45pa55byPJywKICAgICAgY29sU3BhbjogOCwKICAgICAgbmFtZTogJ2NoZWNrX2ZsYWcnLAogICAgICBjb25maWc6IHt9LAogICAgICBjb21wb25lbnRQcm9wczogewogICAgICAgIHBsYWNlaG9sZGVyOiAn6K+36YCJ5oupJywKICAgICAgICBjbGVhcmFibGU6IHRydWUKICAgICAgfSwKICAgICAgb3B0aW9uczogZGljdGlvbmFyeUZpZWRzKCdTTV9IT01FX1NVQk1JVF9UWVBFX0ZMQUcnKQogICAgfQogIH07Cn07"}, {"version": 3, "names": ["dictionaryFieds", "config", "that", "menu_id", "component", "label", "colSpan", "name", "componentProps", "placeholder", "clearable", "menu_name", "check_flag", "options"], "sources": ["E:/1_Project/XYD_Project/dop-4.0/dop-4.1-qian<PERSON>n-unify/dopUnify/src/views/system/config/audit/info.js"], "sourcesContent": ["import { dictionaryFieds } from '@/utils/dictionary' // 字典配置\r\nexport const config = (that) => ({\r\n  menu_id: {\r\n    component: 'input',\r\n    label: '菜单id',\r\n    colSpan: 8,\r\n    name: 'menu_id',\r\n    config: {\r\n      // form-item 配置\r\n    },\r\n    componentProps: {\r\n      // input组件配置\r\n      placeholder: '支持菜单id模糊查询',\r\n      clearable: true\r\n    }\r\n  },\r\n  menu_name: {\r\n    component: 'input',\r\n    label: '菜单名称',\r\n    colSpan: 8,\r\n    name: 'menu_name',\r\n    config: {},\r\n    componentProps: {\r\n      placeholder: '支持菜单名称模糊查询',\r\n      clearable: true\r\n    }\r\n  },\r\n  check_flag: {\r\n    component: 'select',\r\n    label: '审核方式',\r\n    colSpan: 8,\r\n    name: 'check_flag',\r\n    config: {},\r\n    componentProps: {\r\n      placeholder: '请选择',\r\n      clearable: true\r\n    },\r\n    options: dictionaryFieds('SM_HOME_SUBMIT_TYPE_FLAG')\r\n  }\r\n})\r\n"], "mappings": "AAAA,SAASA,eAAe,QAAQ,oBAAoB,EAAC;AACrD,OAAO,IAAMC,MAAM,GAAG,SAATA,MAAM,CAAIC,IAAI;EAAA,OAAM;IAC/BC,OAAO,EAAE;MACPC,SAAS,EAAE,OAAO;MAClBC,KAAK,EAAE,MAAM;MACbC,OAAO,EAAE,CAAC;MACVC,IAAI,EAAE,SAAS;MACfN,MAAM,EAAE;QACN;MAAA,CACD;MACDO,cAAc,EAAE;QACd;QACAC,WAAW,EAAE,YAAY;QACzBC,SAAS,EAAE;MACb;IACF,CAAC;IACDC,SAAS,EAAE;MACTP,SAAS,EAAE,OAAO;MAClBC,KAAK,EAAE,MAAM;MACbC,OAAO,EAAE,CAAC;MACVC,IAAI,EAAE,WAAW;MACjBN,MAAM,EAAE,CAAC,CAAC;MACVO,cAAc,EAAE;QACdC,WAAW,EAAE,YAAY;QACzBC,SAAS,EAAE;MACb;IACF,CAAC;IACDE,UAAU,EAAE;MACVR,SAAS,EAAE,QAAQ;MACnBC,KAAK,EAAE,MAAM;MACbC,OAAO,EAAE,CAAC;MACVC,IAAI,EAAE,YAAY;MAClBN,MAAM,EAAE,CAAC,CAAC;MACVO,cAAc,EAAE;QACdC,WAAW,EAAE,KAAK;QAClBC,SAAS,EAAE;MACb,CAAC;MACDG,OAAO,EAAEb,eAAe,CAAC,0BAA0B;IACrD;EACF,CAAC;AAAA,CAAC"}]}