package com.sunyard.etl.nps.orm;

import java.sql.ResultSet;
import java.sql.SQLException;

import com.sunyard.etl.nps.model.BpTmpbatchTb;
import com.sunyard.etl.system.orm.Orm;

public class BpTmpbatchOrm implements Orm<BpTmpbatchTb> {


	public BpTmpbatchTb orm(ResultSet rs) {
		BpTmpbatchTb bpTmpbatchTb = new BpTmpbatchTb();
		try {
			bpTmpbatchTb.setBatchId(rs.getString("BATCH_ID"));
			bpTmpbatchTb.setOccurDate(rs.getString("OCCUR_DATE"));
			bpTmpbatchTb.setSiteNo(rs.getString("SITE_NO"));
			bpTmpbatchTb.setOperatorNo(rs.getString("OPERATOR_NO"));
			bpTmpbatchTb.setBatchTotalPage(rs.getString("BATCH_TOTAL_PAGE"));
			bpTmpbatchTb.setInputDate(rs.getString("INPUT_DATE"));
		} catch (SQLException e) {
			e.printStackTrace();
		}
		return bpTmpbatchTb;
	}


}
