{"remainingRequest": "E:\\华南区项目\\中国银行\\开发区\\科技公司运营平台\\前端\\dopUnify\\node_modules\\thread-loader\\dist\\cjs.js!E:\\华南区项目\\中国银行\\开发区\\科技公司运营平台\\前端\\dopUnify\\node_modules\\babel-loader\\lib\\index.js!E:\\华南区项目\\中国银行\\开发区\\科技公司运营平台\\前端\\dopUnify\\src\\utils\\request.js", "dependencies": [{"path": "E:\\华南区项目\\中国银行\\开发区\\科技公司运营平台\\前端\\dopUnify\\src\\utils\\request.js", "mtime": 1721631954662}, {"path": "E:\\华南区项目\\中国银行\\开发区\\科技公司运营平台\\前端\\dopUnify\\babel.config.js", "mtime": 1721631954662}, {"path": "E:\\华南区项目\\中国银行\\开发区\\科技公司运营平台\\前端\\dopUnify\\.babelrc", "mtime": 1721631954662}, {"path": "E:\\华南区项目\\中国银行\\开发区\\科技公司运营平台\\前端\\dopUnify\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "E:\\华南区项目\\中国银行\\开发区\\科技公司运营平台\\前端\\dopUnify\\node_modules\\thread-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "E:\\华南区项目\\中国银行\\开发区\\科技公司运营平台\\前端\\dopUnify\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, null]}