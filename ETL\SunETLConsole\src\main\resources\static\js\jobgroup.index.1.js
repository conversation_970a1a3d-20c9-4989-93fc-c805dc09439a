$(function() {

	// remove
	$('.remove').on('click', function(){
		var id = $(this).attr('id');

		layer.confirm('确认删除分组?', {icon: 3, title:'系统提示'}, function(index){
			layer.close(index);

			$.ajax({
				type : 'POST',
				url : base_url + '/jobgroup/remove',
				data : {"id":id},
				dataType : "json",
				success : function(data){
					if (data.code == 200) {
						layer.open({
							title: '系统提示',
							content: '删除成功',
							icon: '1',
							end: function(layero, index){
								window.location.reload();
							}
						});
					} else {
						layer.open({
							title: '系统提示',
							content: (data.msg || "删除失败"),
							icon: '2'
						});
					}
				},
			});
		});

	});

	// jquery.validate 自定义校验 “英文字母开头，只含有英文字母、数字和下划线”
	jQuery.validator.addMethod("myValid01", function(value, element) {
		var length = value.length;
		var valid = /^[A-Z][a-zA-Z0-9_]*$/;
		return this.optional(element) || valid.test(value);
	}, "限制以小写字母开头，由小写字母、数字和下划线组成");

	$('.add').on('click', function(){
		$('#addModal').modal({backdrop: false, keyboard: false}).modal('show');
	});
	var addModalValidate = $("#addModal .form").validate({
		errorElement : 'span',
		errorClass : 'help-block',
		focusInvalid : true,
		rules : {
			appName : {
				required : true,
				rangelength:[4,64],
				myValid01 : true
			},
			title : {
				required : true,
				rangelength:[4, 12]
			},
			order : {
				required : true,
				digits:true,
				range:[1,1000]
			}
		},
		messages : {
			appName : {
				required :"请输入“AppName”",
				rangelength:"AppName长度限制为4~64",
				myValid01: "限制以小写字母开头，由小写字母、数字和下划线组成"
			},
			title : {
				required :"请输入“执行器名称”",
				rangelength:"长度限制为4~12"
			},
			order : {
				required :"请输入“排序”",
				digits: "请输入整数",
				range: "取值范围为1~1000"
			}
		},
		highlight : function(element) {
			$(element).closest('.form-group').addClass('has-error');
		},
		success : function(label) {
			label.closest('.form-group').removeClass('has-error');
			label.remove();
		},
		errorPlacement : function(error, element) {
			element.parent('div').append(error);
		},
		submitHandler : function(form) {
			$.post(base_url + "/jobgroup/save",  $("#addModal .form").serialize(), function(data, status) {
				if (data.code == "200") {
					$('#addModal').modal('hide');
					layer.open({
						title: '系统提示',
						content: '新增成功',
						icon: '1',
						end: function(layero, index){
							window.location.reload();
						}
					});
				} else {
					layer.open({
						title: '系统提示',
						content: (data.msg || "新增失败"),
						icon: '2'
					});
				}
			});
		}
	});
	$("#addModal").on('hide.bs.modal', function () {
		$("#addModal .form")[0].reset();
		addModalValidate.resetForm();
		$("#addModal .form .form-group").removeClass("has-error");
	});

	// 注册方式，切换
	$("#addModal input[name=addressType], #updateModal input[name=addressType]").click(function(){
		var addressType = $(this).val();
		var $addressList = $(this).parents("form").find("input[name=addressList]");
		if (addressType == 0) {
			$addressList.val("");
			$addressList.attr("readonly","readonly");
		} else {
			$addressList.removeAttr("readonly");
		}
	});

	// update
	$('.update').on('click', function(){
		$("#updateModal .form input[name='id']").val($(this).attr("id"));
		$("#updateModal .form input[name='appName']").val($(this).attr("appName"));
		$("#updateModal .form input[name='title']").val($(this).attr("title"));
		$("#updateModal .form input[name='order']").val($(this).attr("order"));

		// 注册方式
		var addressType = $(this).attr("addressType");
		$("#updateModal .form input[name='addressType']").removeAttr('checked');
		//$("#updateModal .form input[name='addressType'][value='"+ addressType +"']").attr('checked', 'true');
		$("#updateModal .form input[name='addressType'][value='"+ addressType +"']").click();
		// 机器地址
		$("#updateModal .form input[name='addressList']").val($(this).attr("addressList"));

		$('#updateModal').modal({backdrop: false, keyboard: false}).modal('show');
	});
	var updateModalValidate = $("#updateModal .form").validate({
		errorElement : 'span',
		errorClass : 'help-block',
		focusInvalid : true,
		rules : {
			appName : {
				required : true,
				rangelength:[4,64],
				myValid01 : true
			},
			title : {
				required : true,
				rangelength:[4, 12]
			},
			order : {
				required : true,
				digits:true,
				range:[1,1000]
			}
		},
		messages : {
			appName : {
				required :"请输入“AppName”",
				rangelength:"AppName长度限制为4~64",
				myValid01: "限制以小写字母开头，由小写字母、数字和中划线组成"
			},
			title : {
				required :"请输入“执行器名称”",
				rangelength:"长度限制为4~12"
			},
			order : {
				required :"请输入“排序”",
				digits: "请输入整数",
				range: "取值范围为1~1000"
			}
		},
		highlight : function(element) {
			$(element).closest('.form-group').addClass('has-error');
		},
		success : function(label) {
			label.closest('.form-group').removeClass('has-error');
			label.remove();
		},
		errorPlacement : function(error, element) {
			element.parent('div').append(error);
		},
		submitHandler : function(form) {
			$.post(base_url + "/jobgroup/update",  $("#updateModal .form").serialize(), function(data, status) {
				if (data.code == "200") {
					$('#addModal').modal('hide');

					layer.open({
						title: '系统提示',
						content: '更新成功',
						icon: '1',
						end: function(layero, index){
							window.location.reload();
						}
					});
				} else {
					layer.open({
						title: '系统提示',
						content: (data.msg || "更新失败"),
						icon: '2'
					});
				}
			});
		}
	});
	$("#updateModal").on('hide.bs.modal', function () {
		$("#updateModal .form")[0].reset();
		addModalValidate.resetForm();
		$("#updateModal .form .form-group").removeClass("has-error");
	});

	
});
