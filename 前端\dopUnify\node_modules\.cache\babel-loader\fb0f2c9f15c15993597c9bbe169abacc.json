{"remainingRequest": "D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\node_modules\\thread-loader\\dist\\cjs.js!D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\node_modules\\babel-loader\\lib\\index.js!D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\src\\layout\\components\\ChangeColor\\index.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\src\\layout\\components\\ChangeColor\\index.vue", "mtime": 1686019810357}, {"path": "D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\babel.config.js", "mtime": 1686019818359}, {"path": "D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\node_modules\\thread-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1655715099000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:aW1wb3J0IF9zbGljZWRUb0FycmF5IGZyb20gIkQ6LzFfUHJvamVjdC9YWURfUHJvamVjdC9kb3AtNC4wL2RvcC00LjEtcWlhbmR1YW4tdW5pZnkvZG9wVW5pZnkvbm9kZV9tb2R1bGVzL0BiYWJlbC9ydW50aW1lL2hlbHBlcnMvZXNtL3NsaWNlZFRvQXJyYXkuanMiOwppbXBvcnQgX2NyZWF0ZUZvck9mSXRlcmF0b3JIZWxwZXIgZnJvbSAiRDovMV9Qcm9qZWN0L1hZRF9Qcm9qZWN0L2RvcC00LjAvZG9wLTQuMS1xaWFuZHVhbi11bmlmeS9kb3BVbmlmeS9ub2RlX21vZHVsZXMvQGJhYmVsL3J1bnRpbWUvaGVscGVycy9lc20vY3JlYXRlRm9yT2ZJdGVyYXRvckhlbHBlci5qcyI7CmltcG9ydCAiY29yZS1qcy9tb2R1bGVzL2VzLm9iamVjdC50by1zdHJpbmcuanMiOwppbXBvcnQgImNvcmUtanMvbW9kdWxlcy93ZWIuZG9tLWNvbGxlY3Rpb25zLml0ZXJhdG9yLmpzIjsKaW1wb3J0ICJjb3JlLWpzL21vZHVsZXMvZXMuYXJyYXkuZmluZC5qcyI7Ci8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCgppbXBvcnQgeyBzeXN0ZW0gfSBmcm9tICdAL2FwaSc7CnZhciB1cGRhdGVUaGVtZSA9IHN5c3RlbS5TeXNVc2VyLnVwZGF0ZVRoZW1lOwovLyBpbXBvcnQgeyBUaGVtZVBpY2tlciB9IGZyb20gJ0AvY29tcG9uZW50cycKaW1wb3J0IHsgY29tbW9uTXNnU3VjY2VzcyB9IGZyb20gJ0AvdXRpbHMvbWVzc2FnZS5qcyc7IC8vIOaPkOekuuS/oeaBrwpleHBvcnQgZGVmYXVsdCB7CiAgbmFtZTogJ0NoYW5nZUNvbG9yJywKICAvLyBjb21wb25lbnRzOiB7IFRoZW1lUGlja2VyIH0sCiAgZGF0YTogZnVuY3Rpb24gZGF0YSgpIHsKICAgIHJldHVybiB7CiAgICAgIGRpYWxvZ1Zpc2libGU6IGZhbHNlLAogICAgICBsaXN0OiBbCiAgICAgIC8vIOaNouiCpOWIl+ihqAogICAgICB7CiAgICAgICAgdGl0bGU6ICfpu5jorqQnLAogICAgICAgIGZpbGVfY3NzOiAnbWFpbicsCiAgICAgICAgdmFsdWU6ICcjMzc2NGZjJwogICAgICB9LCB7CiAgICAgICAgdGl0bGU6ICfpnZLojYnnu78nLAogICAgICAgIGZpbGVfY3NzOiAnbWFpbkdyZWVuJywKICAgICAgICB2YWx1ZTogJyMwMDgwMDAnCiAgICAgIH0sIHsKICAgICAgICB0aXRsZTogJ+a1t+WkqeiTnScsCiAgICAgICAgZmlsZV9jc3M6ICdtYWluQmx1ZScsCiAgICAgICAgdmFsdWU6ICcjMjY3MEY1JwogICAgICB9LCB7CiAgICAgICAgdGl0bGU6ICfmnY/ku4Hpu4QnLAogICAgICAgIGZpbGVfY3NzOiAnbWFpblllbGxvdycsCiAgICAgICAgdmFsdWU6ICcjRkZCNzRCJwogICAgICB9LCB7CiAgICAgICAgdGl0bGU6ICfog63ohILnuqInLAogICAgICAgIGZpbGVfY3NzOiAnbWFpblJlZCcsCiAgICAgICAgdmFsdWU6ICcjRTgzMTNFJwogICAgICB9XSwKICAgICAgaXNBY3RpdmU6IDAKICAgIH07CiAgfSwKICBtb3VudGVkOiBmdW5jdGlvbiBtb3VudGVkKCkgewogICAgdmFyIHRoZW1lQ29sb3IgPSB0aGlzLiRzdG9yZS5zdGF0ZS5zZXR0aW5ncy50aGVtZTsKICAgIHZhciBfaXRlcmF0b3IgPSBfY3JlYXRlRm9yT2ZJdGVyYXRvckhlbHBlcih0aGlzLmxpc3QuZW50cmllcygpKSwKICAgICAgX3N0ZXA7CiAgICB0cnkgewogICAgICBmb3IgKF9pdGVyYXRvci5zKCk7ICEoX3N0ZXAgPSBfaXRlcmF0b3IubigpKS5kb25lOykgewogICAgICAgIHZhciBfc3RlcCR2YWx1ZSA9IF9zbGljZWRUb0FycmF5KF9zdGVwLnZhbHVlLCAyKSwKICAgICAgICAgIGluZGV4ID0gX3N0ZXAkdmFsdWVbMF0sCiAgICAgICAgICBpID0gX3N0ZXAkdmFsdWVbMV07CiAgICAgICAgaWYgKGkudmFsdWUgPT09IHRoZW1lQ29sb3IpIHsKICAgICAgICAgIHRoaXMuaXNBY3RpdmUgPSBpbmRleDsKICAgICAgICB9CiAgICAgIH0KICAgIH0gY2F0Y2ggKGVycikgewogICAgICBfaXRlcmF0b3IuZShlcnIpOwogICAgfSBmaW5hbGx5IHsKICAgICAgX2l0ZXJhdG9yLmYoKTsKICAgIH0KICB9LAogIG1ldGhvZHM6IHsKICAgIGRpYWxvZ1Nob3c6IGZ1bmN0aW9uIGRpYWxvZ1Nob3coKSB7CiAgICAgIHRoaXMuZGlhbG9nVmlzaWJsZSA9IHRydWU7CiAgICB9LAogICAgLyoqCiAgICAgKiDpgInlj5bnmoTpopzoibLmlLnlj5jml7YKICAgICAqIEBwYXJhbSB7QXJyYXl9IGluZGV4IOS4i+aghwogICAgICogQHBhcmFtIHtBcnJheX0gY29sb3JOYW1lIOminOiJsuWQjeensAogICAgICogQHBhcmFtIHtBcnJheX0gY29sb3Ig6aKc6Imy5YC8CiAgICAgKi8KICAgIHRoZW1lQ2hhbmdlOiBmdW5jdGlvbiB0aGVtZUNoYW5nZShpbmRleCwgY29sb3JOYW1lLCBjb2xvcikgewogICAgICAvLyDmoIfms6jlvZPliY3pgInkuK3popzoibLlnZcKICAgICAgdGhpcy5pc0FjdGl2ZSA9IGluZGV4OwogICAgICAvLyDmmK/lkKbkuLrpu5jorqTphY3oibIKICAgICAgdmFyIGZsYWcgPSBpbmRleCA9PT0gMDsKICAgICAgdGhpcy4kc3RvcmUuY29tbWl0KCdzZXR0aW5ncy9kZWZhdWx0Q29sb3JzJywgZmxhZyk7CiAgICAgIHRoaXMuJHN0b3JlLmRpc3BhdGNoKCdzZXR0aW5ncy9jaGFuZ2VTZXR0aW5nJywgewogICAgICAgIGtleTogJ3RoZW1lJywKICAgICAgICB2YWx1ZTogewogICAgICAgICAgY29sb3JOYW1lOiBjb2xvck5hbWUsCiAgICAgICAgICBjb2xvcjogY29sb3IKICAgICAgICB9CiAgICAgIH0pOwogICAgfSwKICAgIGRlZmF1bHRDb2xvcjogZnVuY3Rpb24gZGVmYXVsdENvbG9yKCkgewogICAgICB0aGlzLiRzdG9yZS5jb21taXQoJ3NldHRpbmdzL2lzRGVmYXVsdENvbG9yJywgdHJ1ZSk7CiAgICB9LAogICAgZGlhbG9nVmlzaWJsZXM6IGZ1bmN0aW9uIGRpYWxvZ1Zpc2libGVzKCkgewogICAgICB2YXIgX3RoaXMgPSB0aGlzOwogICAgICB2YXIgdGhlbWVDb2xvciA9IHRoaXMuJHN0b3JlLnN0YXRlLnNldHRpbmdzLnRoZW1lOwogICAgICB2YXIgY29sb3JOYW1lID0gJyc7CiAgICAgIHZhciBjb2xvck9iaiA9IHRoaXMubGlzdC5maW5kKGZ1bmN0aW9uIChpdGVtKSB7CiAgICAgICAgcmV0dXJuIGl0ZW0udmFsdWUgPT09IHRoZW1lQ29sb3I7CiAgICAgIH0pOwogICAgICBpZiAoY29sb3JPYmogPT09IHVuZGVmaW5lZCkgewogICAgICAgIGNvbG9yTmFtZSA9ICdtYWluJzsKICAgICAgfSBlbHNlIHsKICAgICAgICBjb2xvck5hbWUgPSBjb2xvck9iai5maWxlX2NzczsKICAgICAgfQogICAgICB0aGlzLmRpYWxvZ1Zpc2libGUgPSBmYWxzZTsKICAgICAgdmFyIG1zZyA9IHsKICAgICAgICB1c2VyX25vOiB0aGlzLiRzdG9yZS5nZXR0ZXJzLnVzZXJObywKICAgICAgICBjdXN0b21fYXR0cjogewogICAgICAgICAgdGhlbWU6IGNvbG9yTmFtZQogICAgICAgIH0sCiAgICAgICAgb3Blcl90eXBlOiAndXBkYXRlVGhlbWUnCiAgICAgIH07CiAgICAgIHVwZGF0ZVRoZW1lKG1zZykudGhlbihmdW5jdGlvbiAocmVzKSB7CiAgICAgICAgaWYgKHJlcy5yZXRDb2RlID09PSAnMjAwJykgewogICAgICAgICAgX3RoaXMuJHN0b3JlLmRpc3BhdGNoKCd1c2VyL2NoYW5nZVRoZW1lJywgY29sb3JOYW1lKTsKICAgICAgICAgIGNvbW1vbk1zZ1N1Y2Nlc3MoJ+aNouiCpOaIkOWKnycsIF90aGlzKTsKICAgICAgICB9CiAgICAgIH0pOwogICAgfQogIH0KfTs="}, null]}