{"remainingRequest": "E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1\\数字运营平台-统一门户工程\\dopUnify\\node_modules\\babel-loader\\lib\\index.js!E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1\\数字运营平台-统一门户工程\\dopUnify\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1\\数字运营平台-统一门户工程\\dopUnify\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1\\数字运营平台-统一门户工程\\dopUnify\\src\\views\\system\\outManage\\registration\\component\\table\\index.vue?vue&type=script&lang=js&", "dependencies": [{"path": "E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1\\数字运营平台-统一门户工程\\dopUnify\\src\\views\\system\\outManage\\registration\\component\\table\\index.vue", "mtime": 1716875178718}, {"path": "E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1\\数字运营平台-统一门户工程\\dopUnify\\babel.config.js", "mtime": 1716875191548}, {"path": "E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1\\数字运营平台-统一门户工程\\dopUnify\\.babelrc", "mtime": 1716875191502}, {"path": "E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1\\数字运营平台-统一门户工程\\dopUnify\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1\\数字运营平台-统一门户工程\\dopUnify\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1\\数字运营平台-统一门户工程\\dopUnify\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1\\数字运营平台-统一门户工程\\dopUnify\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1655715099000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA8CA;AACA;;AAEA;AACA;AACA;AACA;EACAA;EACAC;EACAC;IACAC;MACAC;MACAC;QACA;MACA;IACA;EACA;EACAC;IACA;MACAC;MACAC;QACA;QACAC;QAAA;QACAC;QACAC;QAAA;QACAC;QAAA;QACAC;QACAC;UACAR;UAAA;UACAS;UACAC;QACA;;QACAC;UACAC;UACAC;UAAA;UACAC;QACA;;QACAC;MACA;;MACAC;QACAC;QACAC;MACA;;MACAC;MACAC;MAAA;MACAC;IACA;EACA;;EACAC;IACAf;MACA;IACA;EACA;EACAgB;IACAC;EACA;EACAC;IAAA;IACA;MACA;IACA;EACA;EACAC;IACA;AACA;AACA;AACA;AACA;IACAC;MACA;MACA;QACA;QACA;UACA;UACAC;QACA;QACAC;MACA;MACA;IACA;IACA;AACA;IACAC;MAAA;MACA;MACA;QACAC;QACAlB;QACAC;MACA;MACA;MACAkB;QACA;UAAAC;UAAArB;UAAAC;QACA;QACA;QACA;QACA;QACA;UACA;YACA;cACAqB;YACA;UACA;UACA;YACA;cACAA;YACA;UACA;QACA;QACA;MACA;IACA;IACA;AACA;AACA;IACAC;MACA;IACA;IACA;AACA;IACAC;MAAA;MACA;MACAC;QACA;QACA;QACA;MACA;IACA;IACA;AACA;IACAC;MACA;QAAAxB;MACA;MACA;MACA;IACA;IACA;AACA;IACAyB;MACA;IACA;EACA;AACA", "names": ["name", "mixins", "props", "defaultForm", "type", "default", "data", "listLoading", "table", "tableColumns", "ref", "selection", "indexNumber", "loading", "componentProps", "height", "formRow", "pageList", "totalNum", "currentPage", "pageSize", "currentRow", "dialog", "visible", "labelWidth", "labelPosition", "applyText", "resText", "watch", "<PERSON><PERSON><PERSON><PERSON>", "clearTimeout", "created", "methods", "commonChoices", "jsonObj", "jsonArr", "queryList", "parameterList", "query", "returnList", "item", "changeVisible", "rowClick", "timout1", "getList", "showLoading"], "sourceRoot": "src/views/system/outManage/registration/component/table", "sources": ["index.vue"], "sourcesContent": ["<template>\r\n  <div class=\"sun-content\">\r\n    <sun-table :table-config=\"table\" @pagination=\"getList\">\r\n      <template slot=\"tableColumn\">\r\n        <el-table-column\r\n          v-for=\"item in table.tableColumns\"\r\n          :key=\"item.id\"\r\n          :prop=\"item.name\"\r\n          :label=\"item.label\"\r\n          :width=\"item.width\"\r\n        >\r\n          <div slot-scope=\"{ row }\">\r\n            <span\r\n              v-if=\"item.name === 'order_no'\"\r\n              class=\"orderNo\"\r\n              @click=\"rowClick(row)\"\r\n            >{{ row[item.name] }}</span>\r\n            <span v-else-if=\"item.name === 'last_modi_date'\">{{\r\n              row[item.name] | dateTimeFormat\r\n            }}</span>\r\n            <span v-else>{{ row[item.name] }}</span>\r\n          </div>\r\n        </el-table-column>\r\n      </template>\r\n    </sun-table>\r\n    <el-dialog\r\n      :visible.sync=\"dialog.visible\"\r\n      title=\"详情\"\r\n      width=\"70%\"\r\n      @dialogClose=\"changeVisible\"\r\n    >\r\n      <el-form\r\n        :label-position=\"labelPosition\"\r\n        :disabled=\"true\"\r\n        class=\"demo-form-inline\"\r\n      >\r\n        <el-form-item label=\"请求报文\">\r\n          <el-input v-model=\"applyText\" type=\"textarea\" :rows=\"16\" />\r\n        </el-form-item>\r\n        <el-form-item label=\"返回报文\">\r\n          <el-input v-model=\"resText\" type=\"textarea\" :rows=\"16\" />\r\n        </el-form-item>\r\n      </el-form> </el-dialog><!--详情弹出框-->\r\n  </div>\r\n</template>\r\n<script>\r\nimport { configTable } from './info' // 表头、表单配置\r\nimport ResizeMixin from '@/utils/ResizeHandler' // 整体页面是否根据总高配置\r\n\r\nimport { system } from '@/api'\r\nconst { query } = system.SysOutReg\r\nlet timout1\r\nexport default {\r\n  name: 'TableList',\r\n  mixins: [ResizeMixin],\r\n  props: {\r\n    defaultForm: {\r\n      type: Object,\r\n      default: function() {\r\n        return {}\r\n      }\r\n    }\r\n  },\r\n  data() {\r\n    return {\r\n      listLoading: false,\r\n      table: {\r\n        // 表格配置\r\n        tableColumns: configTable(), // 表头配置\r\n        ref: 'tableRef',\r\n        selection: false, // 复选\r\n        indexNumber: true, // 序号\r\n        loading: false,\r\n        componentProps: {\r\n          data: [], // 表格数据\r\n          height: '370px',\r\n          formRow: 1 // 表单行数\r\n        },\r\n        pageList: {\r\n          totalNum: 0,\r\n          currentPage: 1, // 当前页\r\n          pageSize: this.$store.getters.pageSize // 当前页显示条数\r\n        },\r\n        currentRow: [] // 选中行\r\n      },\r\n      dialog: {\r\n        visible: false,\r\n        labelWidth: '15rem' // 当前表单标签宽度配置\r\n      },\r\n      labelPosition: 'top',\r\n      applyText: '', // 请求报文\r\n      resText: '' // 返回报文\r\n    }\r\n  },\r\n  watch: {\r\n    loading(value) {\r\n      this.listLoading = this.loading\r\n    }\r\n  },\r\n  beforeDestroy() {\r\n    clearTimeout(timout1)\r\n  },\r\n  created() {\r\n    this.$nextTick(() => {\r\n      this.queryList()\r\n    })\r\n  },\r\n  methods: {\r\n    /**\r\n     * 多行数据拼写报文的方法\r\n     * @param dataArr\t选择行的数组\r\n     * @param attrArr  放置的参数数组\r\n     */\r\n    commonChoices(dataArr, attrArr) {\r\n      const jsonArr = []\r\n      for (let i = 0; i < dataArr.length; i++) {\r\n        const jsonObj = {}\r\n        for (let j = 0; j < attrArr.length; j++) {\r\n          const name = attrArr[j]\r\n          jsonObj[name] = dataArr[i][name]\r\n        }\r\n        jsonArr.push(jsonObj)\r\n      }\r\n      return jsonArr\r\n    },\r\n    /**\r\n     * 按钮：查询*/\r\n    queryList(currentPage) {\r\n      this.showLoading(true)\r\n      const msg = {\r\n        parameterList: [{ ...this.defaultForm }],\r\n        currentPage: currentPage || this.table.pageList.currentPage,\r\n        pageSize: this.table.pageList.pageSize\r\n      }\r\n      // 查询\r\n      query(msg).then((res) => {\r\n        const { returnList, totalNum, currentPage } = res.retMap\r\n        this.table.componentProps.data = returnList\r\n        this.table.pageList.totalNum = totalNum\r\n        this.table.pageList.currentPage = currentPage\r\n        // 格式化系统标识和接口标识\r\n        this.table.componentProps.data.forEach((item) => {\r\n          this.$store.getters.externalData.ALL_SYS_ID.forEach((item2) => {\r\n            if (item2.value === item.sys_id) {\r\n              item.sys_id = `${item2.value}-${item2.label}`\r\n            }\r\n          })\r\n          this.$store.getters.externalData.TD_NO.forEach((item2) => {\r\n            if (item2.value === item.td_no) {\r\n              item.td_no = `${item2.value}-${item2.label}`\r\n            }\r\n          })\r\n        })\r\n        this.showLoading(false)\r\n      })\r\n    },\r\n    /**\r\n     * 弹出框 - 关闭\r\n     * @param {Boolean} param 弹出框显示隐藏配置*/\r\n    changeVisible(param) {\r\n      this.dialog.visible = false\r\n    },\r\n    /**\r\n     *流水号：详情*/\r\n    rowClick(row) {\r\n      this.dialog.visible = true\r\n      timout1 = setTimeout(() => {\r\n        // 详情弹框赋值\r\n        this.applyText = row.req_msg\r\n        this.resText = row.resp_msg\r\n      }, 100)\r\n    },\r\n    /**\r\n     *页码更新 */\r\n    getList(pageParam) {\r\n      const { currentPage, pageSize } = pageParam\r\n      this.table.pageList.pageSize = pageSize\r\n      this.table.pageList.currentPage = currentPage\r\n      this.queryList()\r\n    },\r\n    /**\r\n     * 加载中动画配置*/\r\n    showLoading() {\r\n      this.listLoading = !this.listLoading\r\n    }\r\n  }\r\n}\r\n</script>\r\n<style lang=\"scss\" scoped>\r\n.orderNo {\r\n  color: #0000ff;\r\n}\r\n.demo-form-inline {\r\n  display: flex;\r\n  justify-content: space-evenly;\r\n}\r\n.el-form-item {\r\n  margin-left: -2px;\r\n  width: 40%;\r\n}\r\n</style>\r\n"]}]}