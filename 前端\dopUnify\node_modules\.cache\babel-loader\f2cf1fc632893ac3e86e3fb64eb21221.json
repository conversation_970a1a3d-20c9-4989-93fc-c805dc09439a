{"remainingRequest": "E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1\\数字运营平台-统一门户工程\\dopUnify\\node_modules\\babel-loader\\lib\\index.js!E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1\\数字运营平台-统一门户工程\\dopUnify\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1\\数字运营平台-统一门户工程\\dopUnify\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1\\数字运营平台-统一门户工程\\dopUnify\\src\\views\\system\\config\\audit\\index.vue?vue&type=script&lang=js&", "dependencies": [{"path": "E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1\\数字运营平台-统一门户工程\\dopUnify\\src\\views\\system\\config\\audit\\index.vue", "mtime": 1716875177465}, {"path": "E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1\\数字运营平台-统一门户工程\\dopUnify\\babel.config.js", "mtime": 1716875191548}, {"path": "E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1\\数字运营平台-统一门户工程\\dopUnify\\.babelrc", "mtime": 1716875191502}, {"path": "E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1\\数字运营平台-统一门户工程\\dopUnify\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1\\数字运营平台-统一门户工程\\dopUnify\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1\\数字运营平台-统一门户工程\\dopUnify\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1\\数字运营平台-统一门户工程\\dopUnify\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1655715099000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KCmltcG9ydCB7IGNvbmZpZyB9IGZyb20gJy4vaW5mbyc7IC8vIOihqOWNlemFjee9rgppbXBvcnQgVGFibGVMaXN0IGZyb20gJy4vY29tcG9uZW50L3RhYmxlL2luZGV4LnZ1ZSc7IC8vIOihqOagvAppbXBvcnQgeyBwZXJtaXNzaW9uc0J0biB9IGZyb20gJ0AvdXRpbHMvcGVybWlzc2lvbnMnOyAvLyDmnYPpmZDphY3nva4KCmV4cG9ydCBkZWZhdWx0IHsKICBuYW1lOiAnQXVkaXQnLAogIGNvbXBvbmVudHM6IHsKICAgIFRhYmxlTGlzdDogVGFibGVMaXN0CiAgfSwKICBkYXRhOiBmdW5jdGlvbiBkYXRhKCkgewogICAgcmV0dXJuIHsKICAgICAgY29uZmlnOiBjb25maWcodGhpcyksCiAgICAgIGRlZmF1bHRGb3JtOiB7CiAgICAgICAgbWVudV9pZDogJycsCiAgICAgICAgbWVudV9uYW1lOiAnJywKICAgICAgICBjaGVja19mbGFnOiAnJwogICAgICB9LAogICAgICBidG5BbGw6IHsKICAgICAgICAvLyDlvZPliY3pobXpnIDopoHphY3nva7mnYPpmZDnmoTmjInpkq4gIOadg+mZkOiOt+WPlgogICAgICAgIGJ0blF1ZXJ5OiBmYWxzZSwKICAgICAgICBidG5BZGQ6IHRydWUsCiAgICAgICAgYnRuTW9kaWZ5OiB0cnVlCiAgICAgIH0KICAgIH07CiAgfSwKICBiZWZvcmVNb3VudDogZnVuY3Rpb24gYmVmb3JlTW91bnQoKSB7CiAgICB0aGlzLmJ0blBlcm1pc3Npb25zKCk7IC8vIOaMiemSruadg+mZkAogIH0sCiAgbW91bnRlZDogZnVuY3Rpb24gbW91bnRlZCgpIHsKICAgIHZhciBfdGhpcyA9IHRoaXM7CiAgICB0aGlzLiRuZXh0VGljaygpLnRoZW4oZnVuY3Rpb24gKCkgewogICAgICBfdGhpcy5xdWVyeUxpc3QoKTsKICAgIH0pOwogIH0sCiAgbWV0aG9kczogewogICAgLyoqDQogICAgICog5oyJ6ZKu5p2D6ZmQ6YWN572uKi8KICAgIGJ0blBlcm1pc3Npb25zOiBmdW5jdGlvbiBidG5QZXJtaXNzaW9ucygpIHsKICAgICAgdGhpcy5idG5BbGwgPSBwZXJtaXNzaW9uc0J0bih0aGlzLiRhdHRycy5idXR0b25faWQsIHRoaXMuYnRuQWxsKTsKICAgIH0sCiAgICAvKioNCiAgICAgKiDmjInpkq7vvJrmn6Xor6IqLwogICAgcXVlcnlMaXN0OiBmdW5jdGlvbiBxdWVyeUxpc3QoKSB7CiAgICAgIHRoaXMuJHJlZnMudGFibGVMaXN0UmVmLnF1ZXJ5TGlzdCgxKTsKICAgIH0KICB9Cn07"}, {"version": 3, "mappings": ";;;;;;;;;;;;;;;;;;;;;;;AAuBA;AACA;AACA;;AAEA;EACAA;EACAC;IAAAC;EAAA;EACAC;IACA;MACAC;MACAC;QACAC;QACAC;QACAC;MACA;MACAC;QACA;QACAC;QACAC;QACAC;MACA;IACA;EACA;EACAC;IACA;EACA;EACAC;IAAA;IACA;MACA;IACA;EACA;EACAC;IACA;AACA;IACAC;MACA;IACA;IACA;AACA;IACAC;MACA;IACA;EACA;AACA", "names": ["name", "components", "TableList", "data", "config", "defaultForm", "menu_id", "menu_name", "check_flag", "btnAll", "btnQuery", "btnAdd", "btnModify", "beforeMount", "mounted", "methods", "btnPermissions", "queryList"], "sourceRoot": "src/views/system/config/audit", "sources": ["index.vue"], "sourcesContent": ["<!-- 菜单审核配置界面 -->\r\n<template>\r\n  <div class=\"app-container\">\r\n    <div class=\"sun-content\">\r\n      <div class=\"filter-container\">\r\n        <sun-form\r\n          :config=\"config\"\r\n          :default-form=\"defaultForm\"\r\n          :query=\"true\"\r\n          :reset=\"true\"\r\n          @query=\"queryList\"\r\n        />\r\n      </div>\r\n    </div>\r\n    <table-list\r\n      ref=\"tableListRef\"\r\n      :default-form=\"defaultForm\"\r\n      :btn-all=\"btnAll\"\r\n    />\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport { config } from './info' // 表单配置\r\nimport TableList from './component/table/index.vue' // 表格\r\nimport { permissionsBtn } from '@/utils/permissions' // 权限配置\r\n\r\nexport default {\r\n  name: 'Audit',\r\n  components: { TableList },\r\n  data() {\r\n    return {\r\n      config: config(this),\r\n      defaultForm: {\r\n        menu_id: '',\r\n        menu_name: '',\r\n        check_flag: ''\r\n      },\r\n      btnAll: {\r\n        // 当前页需要配置权限的按钮  权限获取\r\n        btnQuery: false,\r\n        btnAdd: true,\r\n        btnModify: true\r\n      }\r\n    }\r\n  },\r\n  beforeMount() {\r\n    this.btnPermissions() // 按钮权限\r\n  },\r\n  mounted() {\r\n    this.$nextTick().then(() => {\r\n      this.queryList()\r\n    })\r\n  },\r\n  methods: {\r\n    /**\r\n     * 按钮权限配置*/\r\n    btnPermissions() {\r\n      this.btnAll = permissionsBtn(this.$attrs.button_id, this.btnAll)\r\n    },\r\n    /**\r\n     * 按钮：查询*/\r\n    queryList() {\r\n      this.$refs.tableListRef.queryList(1)\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style scoped lang=\"scss\">\r\n.app-container {\r\n  width: 100%;\r\n  height: 100%;\r\n}\r\n</style>\r\n"]}]}