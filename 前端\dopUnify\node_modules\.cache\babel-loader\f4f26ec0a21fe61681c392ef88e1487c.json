{"remainingRequest": "E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-q<PERSON><PERSON>n\\数字运营平台-统一门户工程\\dopUnify\\node_modules\\babel-loader\\lib\\index.js!E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan\\数字运营平台-统一门户工程\\dopUnify\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan\\数字运营平台-统一门户工程\\dopUnify\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan\\数字运营平台-统一门户工程\\dopUnify\\src\\views\\system\\dailyManage\\timing\\index.vue?vue&type=script&lang=js&", "dependencies": [{"path": "E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-<PERSON><PERSON><PERSON>n\\数字运营平台-统一门户工程\\dopUnify\\src\\views\\system\\dailyManage\\timing\\index.vue", "mtime": 1703583638722}, {"path": "E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-<PERSON><PERSON><PERSON>n\\数字运营平台-统一门户工程\\dopUnify\\babel.config.js", "mtime": 1686019818359}, {"path": "E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-<PERSON><PERSON><PERSON>n\\数字运营平台-统一门户工程\\dopUnify\\.babelrc", "mtime": 1705285078668}, {"path": "E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-<PERSON><PERSON><PERSON>n\\数字运营平台-统一门户工程\\dopUnify\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-<PERSON><PERSON><PERSON>n\\数字运营平台-统一门户工程\\dopUnify\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-<PERSON><PERSON><PERSON>n\\数字运营平台-统一门户工程\\dopUnify\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-<PERSON><PERSON><PERSON>n\\数字运营平台-统一门户工程\\dopUnify\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1655715099000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "mappings": ";;;;;;;;;;;;;;;;;;;;;;;AAsBA;AACA;AACA;AACA;AACA;EACAA;EACAC;IAAAC;EAAA;EACAC;IACA;MACAC;MACAC;QACAC;QACAC;QACAC;QACAC;QACAC;MACA;MACAC;QACA;QACAC;MACA;MACAC;IACA;EACA;;EACAC;IACA;IACAD;MAAA;MACA;QACA;QACA;UACA;UACA;QACA;MACA;IACA;EACA;EACAE;IACAC;EACA;EACAC;IACA;IACA;IACA;EACA;EACAC;IAAA;IACA;MACA;QACA;MACA;IACA;EACA;EACAC;IACA;AACA;IACAC;MACA;IACA;IACA;AACA;IACAC;MAAA;MACA;QACA;MACA;MACAC;QACA;QACA;QACAC;UACA;YACAC;UACA;UACAC;QACA;QACA;MACA;IACA;IACA;AACA;AACA;IACAC;MACA;QACA;MACA;QACA;MACA;IACA;IACA;AACA;IACAC;MACA;IACA;EACA;AACA", "names": ["name", "components", "TableList", "data", "config", "defaultForm", "job_key", "job_server", "job_result", "job_name", "service_module", "btnAll", "btnQuery", "routerServiceId", "watch", "<PERSON><PERSON><PERSON><PERSON>", "clearTimeout", "created", "mounted", "methods", "btnPermissions", "getDispatch", "timout1", "arrayState", "label", "SystemArray", "validateForm", "queryList"], "sourceRoot": "src/views/system/dailyManage/timing", "sources": ["index.vue"], "sourcesContent": ["<template>\r\n  <div class=\"app-container\">\r\n    <div class=\"sun-content\">\r\n      <div class=\"filter-container\">\r\n        <sun-form\r\n          ref=\"formRef\"\r\n          :config=\"config\"\r\n          :default-form=\"defaultForm\"\r\n          :query=\"btnAll.btnQuery\"\r\n          @query=\"queryList\"\r\n          @validateForm=\"validateForm\"\r\n        />\r\n      </div>\r\n    </div>\r\n    <table-list\r\n      ref=\"tableListRef\"\r\n      :default-form=\"defaultForm\"\r\n      :btn-all=\"btnAll\"\r\n    />\r\n  </div>\r\n</template>\r\n<script>\r\nimport { config } from './info' // 表单配置\r\nimport TableList from './component/table' // 表格\r\nimport { permissionsBtn } from '@/utils/permissions' // 权限配置\r\nlet timout1\r\nexport default {\r\n  name: 'Timing',\r\n  components: { TableList },\r\n  data() {\r\n    return {\r\n      config: config(this),\r\n      defaultForm: {\r\n        job_key: '',\r\n        job_server: '',\r\n        job_result: '',\r\n        job_name: '',\r\n        service_module: ''\r\n      },\r\n      btnAll: {\r\n        // 当前页需要配置权限的按钮  权限获取\r\n        btnQuery: true\r\n      },\r\n      routerServiceId: '' // 定时服务配置传参至定时服务登记簿\r\n    }\r\n  },\r\n  watch: {\r\n    // 监听服务id，存在即查询\r\n    routerServiceId(value) {\r\n      if (value) {\r\n        // 待组件加载完全后调查询方法\r\n        this.$nextTick(() => {\r\n          this.defaultForm.job_key = value\r\n          this.$refs.tableListRef.queryList(1, value)\r\n        })\r\n      }\r\n    }\r\n  },\r\n  beforeDestroy() {\r\n    clearTimeout(timout1)\r\n  },\r\n  created() {\r\n    this.btnPermissions()\r\n    this.getDispatch()\r\n    this.routerServiceId = this.$route.params.id // 定时服务配置传参至定时服务登记簿,进行赋值\r\n  },\r\n  mounted() {\r\n    this.$nextTick().then(() => {\r\n      if (this.btnAll.btnQuery) {\r\n        this.queryList()\r\n      }\r\n    })\r\n  },\r\n  methods: {\r\n    /**\r\n     * 按钮权限配置*/\r\n    btnPermissions() {\r\n      this.btnAll = permissionsBtn(this.$attrs.button_id, this.btnAll)\r\n    },\r\n    /**\r\n     * 获取外部字典*/\r\n    getDispatch() {\r\n      if (!this.$store.getters.externalData.SERVER_ID) {\r\n        this.$store.dispatch('common/setExternalData', 'SERVER_ID')\r\n      }\r\n      timout1 = setTimeout(() => {\r\n        const arrayState = this.$store.getters.externalData.SERVER_ID\r\n        const SystemArray = []\r\n        arrayState.map(function(item) {\r\n          const valueS = Object.assign({}, item, {\r\n            label: item.value + '-' + item.label\r\n          })\r\n          SystemArray.push(valueS)\r\n        })\r\n        this.config.job_server.options = SystemArray\r\n      }, 1000)\r\n    },\r\n    /**\r\n     * 表单校验\r\n     * @param {Boolean}valid 校验返回值*/\r\n    validateForm(valid) {\r\n      if (valid) {\r\n        this.$refs.tableListRef.queryList(1)\r\n      } else {\r\n        return false\r\n      }\r\n    },\r\n    /**\r\n     * 按钮：查询*/\r\n    queryList() {\r\n      this.$refs['formRef'].validateForm()\r\n    }\r\n  }\r\n}\r\n</script>\r\n<style scoped>\r\n.app-container {\r\n  width: 100%;\r\n  height: 100%;\r\n}\r\n</style>\r\n"]}]}