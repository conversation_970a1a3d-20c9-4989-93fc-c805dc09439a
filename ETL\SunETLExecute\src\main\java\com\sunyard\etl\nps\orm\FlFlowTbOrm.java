package com.sunyard.etl.nps.orm;

import java.sql.ResultSet;
import java.sql.SQLException;

import com.sunyard.etl.nps.model.FlFlowTb;
import com.sunyard.etl.system.orm.Orm;

public class FlFlowTbOrm implements Orm<FlFlowTb> {

	@Override
	public FlFlowTb orm(ResultSet rs) throws SQLException {
		FlFlowTb flow = new FlFlowTb();
		try {
			flow.setCheckFlag(rs.getString("CHECK_FLAG"));
			flow.setFlowId(rs.getString("FLOW_ID"));
			flow.setLserialNo(rs.getString("LSERIAL_NO"));
			flow.setOccurDate(rs.getString("OCCUR_DATE"));
			flow.setOperatorNo(rs.getString("OPERATOR_NO"));
			flow.setSeqId(rs.getString("SEQ_ID"));
			flow.setSiteNo(rs.getString("SITE_NO"));
		} catch (SQLException e) {
			e.printStackTrace();
		}
		return flow;
	}

}
