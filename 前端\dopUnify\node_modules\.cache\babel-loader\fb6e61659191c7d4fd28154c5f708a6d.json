{"remainingRequest": "D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\node_modules\\thread-loader\\dist\\cjs.js!D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\node_modules\\babel-loader\\lib\\index.js!D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\src\\layout\\components\\info.js", "dependencies": [{"path": "D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\src\\layout\\components\\info.js", "mtime": 1688521791474}, {"path": "D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\babel.config.js", "mtime": 1686019818359}, {"path": "D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\node_modules\\thread-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, null]}