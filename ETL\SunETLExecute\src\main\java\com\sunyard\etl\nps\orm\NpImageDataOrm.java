package com.sunyard.etl.nps.orm;

import java.sql.ResultSet;
import java.sql.SQLException;

import com.sunyard.etl.nps.model.NpImageData;
import com.sunyard.etl.system.orm.Orm;

public class NpImageDataOrm implements Orm<NpImageData>{

	public NpImageData orm(ResultSet rs) {
		NpImageData npImageData = new NpImageData();
		try {
			npImageData.setBackFileName(rs.getString("FILE_NAME"));
			npImageData.setBackImageSize(rs.getInt("BACK_IMAGE_SIZE"));
			npImageData.setBusiDataNo(rs.getString("BUSI_DATA_NO"));
			npImageData.setFileName(rs.getString("FILE_NAME"));
			npImageData.setFormName(rs.getString("FORM_NAME"));
			npImageData.setImageSize(rs.getInt("IMAGE_SIZE"));
			npImageData.setOrderNum(rs.getString("ORDER_NUM"));
			npImageData.setPsLevel(rs.getString("PS_LEVEL"));
			
			
			// 关联查询 非表字段
//			npImageData.setOccurDate(rs.getString("OCCUR_DATE"));
//			npImageData.setSiteNo(rs.getString("SITE_NO"));
//			npImageData.setOperator(rs.getString("OPERATOR_NO"));
//			npImageData.setFlowId(rs.getString("FLOW_ID"));
//			npImageData.setContentId(rs.getString("CONTENT_ID"));
//			npImageData.setIndexValue(rs.getString("INDEX_VALUE"));
//			npImageData.setContentIdNew(rs.getString("CONTENT_ID_NEW"));
			
			
			
		} catch (SQLException e) {
			e.printStackTrace();
		}
		return npImageData;
	}

}
