{"remainingRequest": "E:\\华南区项目\\中国银行\\开发区\\科技公司运营平台\\前端\\dopUnify\\node_modules\\thread-loader\\dist\\cjs.js!E:\\华南区项目\\中国银行\\开发区\\科技公司运营平台\\前端\\dopUnify\\node_modules\\babel-loader\\lib\\index.js!E:\\华南区项目\\中国银行\\开发区\\科技公司运营平台\\前端\\dopUnify\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!E:\\华南区项目\\中国银行\\开发区\\科技公司运营平台\\前端\\dopUnify\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\华南区项目\\中国银行\\开发区\\科技公司运营平台\\前端\\dopUnify\\src\\views\\system\\externalManage\\external\\component\\table\\index.vue?vue&type=script&lang=js&", "dependencies": [{"path": "E:\\华南区项目\\中国银行\\开发区\\科技公司运营平台\\前端\\dopUnify\\src\\views\\system\\externalManage\\external\\component\\table\\index.vue", "mtime": 1721631954662}, {"path": "E:\\华南区项目\\中国银行\\开发区\\科技公司运营平台\\前端\\dopUnify\\babel.config.js", "mtime": 1721631954662}, {"path": "E:\\华南区项目\\中国银行\\开发区\\科技公司运营平台\\前端\\dopUnify\\.babelrc", "mtime": 1721631954662}, {"path": "E:\\华南区项目\\中国银行\\开发区\\科技公司运营平台\\前端\\dopUnify\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "E:\\华南区项目\\中国银行\\开发区\\科技公司运营平台\\前端\\dopUnify\\node_modules\\thread-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "E:\\华南区项目\\中国银行\\开发区\\科技公司运营平台\\前端\\dopUnify\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "E:\\华南区项目\\中国银行\\开发区\\科技公司运营平台\\前端\\dopUnify\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "E:\\华南区项目\\中国银行\\开发区\\科技公司运营平台\\前端\\dopUnify\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1655715099000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, null]}