{"remainingRequest": "E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-q<PERSON><PERSON>n\\数字运营平台-统一门户工程\\dopUnify\\node_modules\\babel-loader\\lib\\index.js!E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan\\数字运营平台-统一门户工程\\dopUnify\\node_modules\\eslint-loader\\index.js??ref--13-0!E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan\\数字运营平台-统一门户工程\\dopUnify\\src\\views\\system\\user\\component\\table\\info.js", "dependencies": [{"path": "E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-<PERSON><PERSON><PERSON>n\\数字运营平台-统一门户工程\\dopUnify\\src\\views\\system\\user\\component\\table\\info.js", "mtime": 1700812742899}, {"path": "E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-<PERSON><PERSON><PERSON>n\\数字运营平台-统一门户工程\\dopUnify\\babel.config.js", "mtime": 1686019818359}, {"path": "E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-<PERSON><PERSON><PERSON>n\\数字运营平台-统一门户工程\\dopUnify\\.babelrc", "mtime": 1705285078668}, {"path": "E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-<PERSON><PERSON><PERSON>n\\数字运营平台-统一门户工程\\dopUnify\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-<PERSON><PERSON><PERSON>n\\数字运营平台-统一门户工程\\dopUnify\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-<PERSON><PERSON><PERSON>n\\数字运营平台-统一门户工程\\dopUnify\\node_modules\\eslint-loader\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["v1", "uuidv1", "dictionaryFieds", "configTable", "that", "name", "label", "id", "width", "config", "organ_no", "component", "colSpan", "rules", "required", "message", "trigger", "componentProps", "clearable", "disabled", "options", "user_no", "min", "max", "<PERSON><PERSON><PERSON>", "tellerlvl", "filterable", "user_name", "right_organ_no", "multiple", "collapseTags", "checkStrictly", "slotObj", "checked", "click", "item", "role_no", "phone", "user_status", "login_mode", "login_state", "single_login", "last_login_time", "labelWidth", "last_logout_time", "email", "pattern", "configBatch", "is_change_children"], "sources": ["E:/1_Project/XYD_Project/dop-4.0/dop-4.1-qianduan/数字运营平台-统一门户工程/dopUnify/src/views/system/user/component/table/info.js"], "sourcesContent": ["import { v1 as uuidv1 } from 'uuid'\nimport { dictionaryFieds } from '@/utils/dictionary' // 字典配置\n// 表头配置\nexport const configTable = (that) => [\n  {\n    name: 'organ_no',\n    label: '所属机构',\n    id: uuidv1()\n  },\n  {\n    name: 'user_no',\n    label: '用户号',\n    width: 200,\n    id: uuidv1()\n  },\n  {\n    name: 'role_no',\n    label: '角色号',\n    // showOverflowTooltip: true,\n    id: uuidv1()\n  },\n  {\n    name: 'user_status',\n    label: '用户状态',\n    width: 80,\n    id: uuidv1()\n  },\n  {\n    name: 'login_mode',\n    label: '登录方式',\n    width: 80,\n    id: uuidv1()\n  },\n  {\n    name: 'login_state',\n    label: '登录状态',\n    width: 80,\n    id: uuidv1()\n  },\n  {\n    name: 'last_modi_date',\n    label: '最后修改时间',\n    width: 160,\n    id: uuidv1()\n  }\n]\n\n// 新增、修改弹出框表单配置\nexport const config = (that) => ({\n  organ_no: {\n    component: 'select-tree',\n    label: '所属机构',\n    colSpan: 8,\n    name: 'organ_no',\n    config: {\n      // form-item 配置\n      rules: [{ required: true, message: '所属机构为必输', trigger: 'blur' }]\n    },\n    componentProps: {\n      // input组件配置\n      clearable: true,\n      disabled: false\n    },\n    options: []\n  },\n  user_no: {\n    component: 'input',\n    label: '用户号',\n    colSpan: 8,\n    name: 'user_no',\n    config: {\n      // form-item 配置\n      rules: [\n        { required: true, message: '用户号为必输' },\n        { min: 1, max: 10, message: '请最多填写10个字符' }\n      ]\n    },\n    componentProps: {\n      // input组件配置\n      placehodler: '',\n      clearable: true,\n      disabled: false\n    }\n  },\n  tellerlvl: {\n    component: 'select',\n    label: '用户级别',\n    colSpan: 8,\n    name: 'tellerlvl',\n    config: {\n      rules: [{ required: true, message: '用户级别为必输' }]\n    },\n    componentProps: {\n      placehodler: '请选择',\n      filterable: true\n    },\n    options: dictionaryFieds('USER_LEVEL')\n  },\n  user_name: {\n    component: 'input',\n    label: '用户名称',\n    colSpan: 8,\n    name: 'user_name',\n    config: {\n      // form-item 配置\n      rules: [\n        { required: true, message: '用户名称为必输' },\n        { min: 0, max: 50, message: '请最多填写50个字符' }\n      ]\n    },\n    componentProps: {\n      // input组件配置\n      placehodler: '',\n      clearable: true\n    }\n  },\n  right_organ_no: {\n    component: 'select-tree',\n    label: '权限机构',\n    colSpan: 8,\n    name: 'right_organ_no',\n    config: {\n      // form-item 配置\n    },\n    componentProps: {\n      // input组件配置\n      clearable: true,\n      multiple: true,\n      // 多选项合并为一行文字\n      collapseTags: true,\n      checkStrictly: false\n    },\n    slotObj: {\n      checked: true,\n      click(item) {\n        item.componentProps.checkStrictly = !item.componentProps.checkStrictly\n      }\n    },\n    options: []\n  },\n  role_no: {\n    component: 'select-tree',\n    label: '角色号',\n    colSpan: 8,\n    name: 'role_no',\n    config: {\n      // form-item 配置\n      rules: [{ required: true, message: '角色号为必选' }]\n    },\n    componentProps: {\n      // input组件配置\n      clearable: true,\n      multiple: true,\n      // 多选项合并为一行文字\n      collapseTags: true\n    },\n    options: []\n  },\n  phone: {\n    component: 'input',\n    label: '联系电话',\n    colSpan: 8,\n    name: 'phone',\n    config: {\n      rules: [{ min: 11, max: 11, message: '请填写11个字符' }]\n    },\n    componentProps: {\n      // input组件配置\n      placehodler: '',\n      clearable: true\n    }\n  },\n  user_status: {\n    component: 'select',\n    label: '用户状态',\n    colSpan: 8,\n    name: 'user_status',\n    config: {\n      rules: [{ required: true, message: '用户状态为必选' }]\n    },\n    componentProps: {\n      placehodler: '请选择',\n      filterable: true,\n      disabled: true\n    },\n    options: dictionaryFieds('USER_STATE')\n  },\n  login_mode: {\n    component: 'select',\n    label: '登录方式',\n    colSpan: 8,\n    name: 'login_mode',\n    config: {\n      rules: [{ required: true, message: '登录方式为必选' }]\n    },\n    componentProps: {\n      placehodler: '请选择',\n      filterable: true\n    },\n    options: dictionaryFieds('USER_LOGIN_TYPE')\n  },\n  login_state: {\n    component: 'select',\n    label: '登录状态',\n    colSpan: 8,\n    name: 'login_state',\n    config: {\n      rules: [{ required: true, message: '登录状态为必选' }]\n    },\n    componentProps: {\n      placehodler: '请选择',\n      filterable: true,\n      disabled: true\n    },\n    options: dictionaryFieds('USER_LOGIN_STATE')\n  },\n  single_login: {\n    component: 'select',\n    label: '单点登录',\n    colSpan: 8,\n    name: 'single_login',\n    config: {\n      rules: [{ required: true, message: '单点登录为必选' }]\n    },\n    componentProps: {\n      placehodler: '请选择',\n      filterable: true\n    },\n    options: dictionaryFieds('IS_OPEN')\n  },\n  last_login_time: {\n    component: 'input',\n    label: '最后登录时间',\n    colSpan: 8,\n    name: 'last_login_time',\n    config: {\n      // form-item 配置\n      labelWidth: '11rem'\n    },\n    componentProps: {\n      // input组件配置\n      placehodler: '',\n      clearable: true,\n      disabled: true\n    }\n  },\n  last_logout_time: {\n    component: 'input',\n    label: '最后登出时间',\n    colSpan: 8,\n    name: 'last_logout_time',\n    config: {\n      // form-item 配置\n      labelWidth: '11rem'\n    },\n    componentProps: {\n      // input组件配置\n      placehodler: '',\n      clearable: true,\n      disabled: true\n    }\n  },\n  email: {\n    component: 'input',\n    label: '邮箱',\n    colSpan: 8,\n    name: 'email',\n    config: {\n      // form-item 配置\n      rules: [\n        {\n          pattern: /^\\w+([-+.']\\w+)*@\\w+([-.]\\w+)*\\.\\w+([-.]\\w+)*$/,\n          message: '请输入正确的邮箱格式'\n        }\n      ]\n    },\n    componentProps: {\n      // input组件配置\n      placehodler: '',\n      clearable: true,\n      disabled: false\n    }\n  }\n})\n\n// 批量配置弹出框表单配置\nexport const configBatch = (that) => ({\n  organ_no: {\n    component: 'select-tree',\n    label: '所属机构',\n    colSpan: 22,\n    name: 'organ_no',\n    config: {\n      // form-item 配置\n      rules: [{ required: true, message: '所属机构为必输', trigger: 'blur' }]\n    },\n    componentProps: {\n      // input组件配置\n      clearable: true\n    },\n    options: []\n  },\n  user_status: {\n    component: 'select',\n    label: '用户状态',\n    colSpan: 22,\n    name: 'user_status',\n    config: {\n      rules: [{ required: true, message: '用户状态为必选' }]\n    },\n    componentProps: {\n      placehodler: '请选择',\n      filterable: true\n    },\n    options: dictionaryFieds('USER_STATE')\n  },\n  is_change_children: {\n    component: 'select',\n    label: '是否联动下级',\n    colSpan: 22,\n    name: 'is_change_children',\n    config: {\n      rules: [{ required: true, message: '是否联动下级为必选' }]\n    },\n    componentProps: {\n      placehodler: '请选择',\n      filterable: true\n    },\n    options: dictionaryFieds('IS_CHANGE_CHILDREN')\n  }\n})\n"], "mappings": "AAAA,SAASA,EAAE,IAAIC,MAAM,QAAQ,MAAM;AACnC,SAASC,eAAe,QAAQ,oBAAoB,EAAC;AACrD;AACA,OAAO,IAAMC,WAAW,GAAG,SAAdA,WAAW,CAAIC,IAAI;EAAA,OAAK,CACnC;IACEC,IAAI,EAAE,UAAU;IAChBC,KAAK,EAAE,MAAM;IACbC,EAAE,EAAEN,MAAM;EACZ,CAAC,EACD;IACEI,IAAI,EAAE,SAAS;IACfC,KAAK,EAAE,KAAK;IACZE,KAAK,EAAE,GAAG;IACVD,EAAE,EAAEN,MAAM;EACZ,CAAC,EACD;IACEI,IAAI,EAAE,SAAS;IACfC,KAAK,EAAE,KAAK;IACZ;IACAC,EAAE,EAAEN,MAAM;EACZ,CAAC,EACD;IACEI,IAAI,EAAE,aAAa;IACnBC,KAAK,EAAE,MAAM;IACbE,KAAK,EAAE,EAAE;IACTD,EAAE,EAAEN,MAAM;EACZ,CAAC,EACD;IACEI,IAAI,EAAE,YAAY;IAClBC,KAAK,EAAE,MAAM;IACbE,KAAK,EAAE,EAAE;IACTD,EAAE,EAAEN,MAAM;EACZ,CAAC,EACD;IACEI,IAAI,EAAE,aAAa;IACnBC,KAAK,EAAE,MAAM;IACbE,KAAK,EAAE,EAAE;IACTD,EAAE,EAAEN,MAAM;EACZ,CAAC,EACD;IACEI,IAAI,EAAE,gBAAgB;IACtBC,KAAK,EAAE,QAAQ;IACfE,KAAK,EAAE,GAAG;IACVD,EAAE,EAAEN,MAAM;EACZ,CAAC,CACF;AAAA;;AAED;AACA,OAAO,IAAMQ,MAAM,GAAG,SAATA,MAAM,CAAIL,IAAI;EAAA,OAAM;IAC/BM,QAAQ,EAAE;MACRC,SAAS,EAAE,aAAa;MACxBL,KAAK,EAAE,MAAM;MACbM,OAAO,EAAE,CAAC;MACVP,IAAI,EAAE,UAAU;MAChBI,MAAM,EAAE;QACN;QACAI,KAAK,EAAE,CAAC;UAAEC,QAAQ,EAAE,IAAI;UAAEC,OAAO,EAAE,SAAS;UAAEC,OAAO,EAAE;QAAO,CAAC;MACjE,CAAC;MACDC,cAAc,EAAE;QACd;QACAC,SAAS,EAAE,IAAI;QACfC,QAAQ,EAAE;MACZ,CAAC;MACDC,OAAO,EAAE;IACX,CAAC;IACDC,OAAO,EAAE;MACPV,SAAS,EAAE,OAAO;MAClBL,KAAK,EAAE,KAAK;MACZM,OAAO,EAAE,CAAC;MACVP,IAAI,EAAE,SAAS;MACfI,MAAM,EAAE;QACN;QACAI,KAAK,EAAE,CACL;UAAEC,QAAQ,EAAE,IAAI;UAAEC,OAAO,EAAE;QAAS,CAAC,EACrC;UAAEO,GAAG,EAAE,CAAC;UAAEC,GAAG,EAAE,EAAE;UAAER,OAAO,EAAE;QAAa,CAAC;MAE9C,CAAC;MACDE,cAAc,EAAE;QACd;QACAO,WAAW,EAAE,EAAE;QACfN,SAAS,EAAE,IAAI;QACfC,QAAQ,EAAE;MACZ;IACF,CAAC;IACDM,SAAS,EAAE;MACTd,SAAS,EAAE,QAAQ;MACnBL,KAAK,EAAE,MAAM;MACbM,OAAO,EAAE,CAAC;MACVP,IAAI,EAAE,WAAW;MACjBI,MAAM,EAAE;QACNI,KAAK,EAAE,CAAC;UAAEC,QAAQ,EAAE,IAAI;UAAEC,OAAO,EAAE;QAAU,CAAC;MAChD,CAAC;MACDE,cAAc,EAAE;QACdO,WAAW,EAAE,KAAK;QAClBE,UAAU,EAAE;MACd,CAAC;MACDN,OAAO,EAAElB,eAAe,CAAC,YAAY;IACvC,CAAC;IACDyB,SAAS,EAAE;MACThB,SAAS,EAAE,OAAO;MAClBL,KAAK,EAAE,MAAM;MACbM,OAAO,EAAE,CAAC;MACVP,IAAI,EAAE,WAAW;MACjBI,MAAM,EAAE;QACN;QACAI,KAAK,EAAE,CACL;UAAEC,QAAQ,EAAE,IAAI;UAAEC,OAAO,EAAE;QAAU,CAAC,EACtC;UAAEO,GAAG,EAAE,CAAC;UAAEC,GAAG,EAAE,EAAE;UAAER,OAAO,EAAE;QAAa,CAAC;MAE9C,CAAC;MACDE,cAAc,EAAE;QACd;QACAO,WAAW,EAAE,EAAE;QACfN,SAAS,EAAE;MACb;IACF,CAAC;IACDU,cAAc,EAAE;MACdjB,SAAS,EAAE,aAAa;MACxBL,KAAK,EAAE,MAAM;MACbM,OAAO,EAAE,CAAC;MACVP,IAAI,EAAE,gBAAgB;MACtBI,MAAM,EAAE;QACN;MAAA,CACD;MACDQ,cAAc,EAAE;QACd;QACAC,SAAS,EAAE,IAAI;QACfW,QAAQ,EAAE,IAAI;QACd;QACAC,YAAY,EAAE,IAAI;QAClBC,aAAa,EAAE;MACjB,CAAC;MACDC,OAAO,EAAE;QACPC,OAAO,EAAE,IAAI;QACbC,KAAK,iBAACC,IAAI,EAAE;UACVA,IAAI,CAAClB,cAAc,CAACc,aAAa,GAAG,CAACI,IAAI,CAAClB,cAAc,CAACc,aAAa;QACxE;MACF,CAAC;MACDX,OAAO,EAAE;IACX,CAAC;IACDgB,OAAO,EAAE;MACPzB,SAAS,EAAE,aAAa;MACxBL,KAAK,EAAE,KAAK;MACZM,OAAO,EAAE,CAAC;MACVP,IAAI,EAAE,SAAS;MACfI,MAAM,EAAE;QACN;QACAI,KAAK,EAAE,CAAC;UAAEC,QAAQ,EAAE,IAAI;UAAEC,OAAO,EAAE;QAAS,CAAC;MAC/C,CAAC;MACDE,cAAc,EAAE;QACd;QACAC,SAAS,EAAE,IAAI;QACfW,QAAQ,EAAE,IAAI;QACd;QACAC,YAAY,EAAE;MAChB,CAAC;MACDV,OAAO,EAAE;IACX,CAAC;IACDiB,KAAK,EAAE;MACL1B,SAAS,EAAE,OAAO;MAClBL,KAAK,EAAE,MAAM;MACbM,OAAO,EAAE,CAAC;MACVP,IAAI,EAAE,OAAO;MACbI,MAAM,EAAE;QACNI,KAAK,EAAE,CAAC;UAAES,GAAG,EAAE,EAAE;UAAEC,GAAG,EAAE,EAAE;UAAER,OAAO,EAAE;QAAW,CAAC;MACnD,CAAC;MACDE,cAAc,EAAE;QACd;QACAO,WAAW,EAAE,EAAE;QACfN,SAAS,EAAE;MACb;IACF,CAAC;IACDoB,WAAW,EAAE;MACX3B,SAAS,EAAE,QAAQ;MACnBL,KAAK,EAAE,MAAM;MACbM,OAAO,EAAE,CAAC;MACVP,IAAI,EAAE,aAAa;MACnBI,MAAM,EAAE;QACNI,KAAK,EAAE,CAAC;UAAEC,QAAQ,EAAE,IAAI;UAAEC,OAAO,EAAE;QAAU,CAAC;MAChD,CAAC;MACDE,cAAc,EAAE;QACdO,WAAW,EAAE,KAAK;QAClBE,UAAU,EAAE,IAAI;QAChBP,QAAQ,EAAE;MACZ,CAAC;MACDC,OAAO,EAAElB,eAAe,CAAC,YAAY;IACvC,CAAC;IACDqC,UAAU,EAAE;MACV5B,SAAS,EAAE,QAAQ;MACnBL,KAAK,EAAE,MAAM;MACbM,OAAO,EAAE,CAAC;MACVP,IAAI,EAAE,YAAY;MAClBI,MAAM,EAAE;QACNI,KAAK,EAAE,CAAC;UAAEC,QAAQ,EAAE,IAAI;UAAEC,OAAO,EAAE;QAAU,CAAC;MAChD,CAAC;MACDE,cAAc,EAAE;QACdO,WAAW,EAAE,KAAK;QAClBE,UAAU,EAAE;MACd,CAAC;MACDN,OAAO,EAAElB,eAAe,CAAC,iBAAiB;IAC5C,CAAC;IACDsC,WAAW,EAAE;MACX7B,SAAS,EAAE,QAAQ;MACnBL,KAAK,EAAE,MAAM;MACbM,OAAO,EAAE,CAAC;MACVP,IAAI,EAAE,aAAa;MACnBI,MAAM,EAAE;QACNI,KAAK,EAAE,CAAC;UAAEC,QAAQ,EAAE,IAAI;UAAEC,OAAO,EAAE;QAAU,CAAC;MAChD,CAAC;MACDE,cAAc,EAAE;QACdO,WAAW,EAAE,KAAK;QAClBE,UAAU,EAAE,IAAI;QAChBP,QAAQ,EAAE;MACZ,CAAC;MACDC,OAAO,EAAElB,eAAe,CAAC,kBAAkB;IAC7C,CAAC;IACDuC,YAAY,EAAE;MACZ9B,SAAS,EAAE,QAAQ;MACnBL,KAAK,EAAE,MAAM;MACbM,OAAO,EAAE,CAAC;MACVP,IAAI,EAAE,cAAc;MACpBI,MAAM,EAAE;QACNI,KAAK,EAAE,CAAC;UAAEC,QAAQ,EAAE,IAAI;UAAEC,OAAO,EAAE;QAAU,CAAC;MAChD,CAAC;MACDE,cAAc,EAAE;QACdO,WAAW,EAAE,KAAK;QAClBE,UAAU,EAAE;MACd,CAAC;MACDN,OAAO,EAAElB,eAAe,CAAC,SAAS;IACpC,CAAC;IACDwC,eAAe,EAAE;MACf/B,SAAS,EAAE,OAAO;MAClBL,KAAK,EAAE,QAAQ;MACfM,OAAO,EAAE,CAAC;MACVP,IAAI,EAAE,iBAAiB;MACvBI,MAAM,EAAE;QACN;QACAkC,UAAU,EAAE;MACd,CAAC;MACD1B,cAAc,EAAE;QACd;QACAO,WAAW,EAAE,EAAE;QACfN,SAAS,EAAE,IAAI;QACfC,QAAQ,EAAE;MACZ;IACF,CAAC;IACDyB,gBAAgB,EAAE;MAChBjC,SAAS,EAAE,OAAO;MAClBL,KAAK,EAAE,QAAQ;MACfM,OAAO,EAAE,CAAC;MACVP,IAAI,EAAE,kBAAkB;MACxBI,MAAM,EAAE;QACN;QACAkC,UAAU,EAAE;MACd,CAAC;MACD1B,cAAc,EAAE;QACd;QACAO,WAAW,EAAE,EAAE;QACfN,SAAS,EAAE,IAAI;QACfC,QAAQ,EAAE;MACZ;IACF,CAAC;IACD0B,KAAK,EAAE;MACLlC,SAAS,EAAE,OAAO;MAClBL,KAAK,EAAE,IAAI;MACXM,OAAO,EAAE,CAAC;MACVP,IAAI,EAAE,OAAO;MACbI,MAAM,EAAE;QACN;QACAI,KAAK,EAAE,CACL;UACEiC,OAAO,EAAE,gDAAgD;UACzD/B,OAAO,EAAE;QACX,CAAC;MAEL,CAAC;MACDE,cAAc,EAAE;QACd;QACAO,WAAW,EAAE,EAAE;QACfN,SAAS,EAAE,IAAI;QACfC,QAAQ,EAAE;MACZ;IACF;EACF,CAAC;AAAA,CAAC;;AAEF;AACA,OAAO,IAAM4B,WAAW,GAAG,SAAdA,WAAW,CAAI3C,IAAI;EAAA,OAAM;IACpCM,QAAQ,EAAE;MACRC,SAAS,EAAE,aAAa;MACxBL,KAAK,EAAE,MAAM;MACbM,OAAO,EAAE,EAAE;MACXP,IAAI,EAAE,UAAU;MAChBI,MAAM,EAAE;QACN;QACAI,KAAK,EAAE,CAAC;UAAEC,QAAQ,EAAE,IAAI;UAAEC,OAAO,EAAE,SAAS;UAAEC,OAAO,EAAE;QAAO,CAAC;MACjE,CAAC;MACDC,cAAc,EAAE;QACd;QACAC,SAAS,EAAE;MACb,CAAC;MACDE,OAAO,EAAE;IACX,CAAC;IACDkB,WAAW,EAAE;MACX3B,SAAS,EAAE,QAAQ;MACnBL,KAAK,EAAE,MAAM;MACbM,OAAO,EAAE,EAAE;MACXP,IAAI,EAAE,aAAa;MACnBI,MAAM,EAAE;QACNI,KAAK,EAAE,CAAC;UAAEC,QAAQ,EAAE,IAAI;UAAEC,OAAO,EAAE;QAAU,CAAC;MAChD,CAAC;MACDE,cAAc,EAAE;QACdO,WAAW,EAAE,KAAK;QAClBE,UAAU,EAAE;MACd,CAAC;MACDN,OAAO,EAAElB,eAAe,CAAC,YAAY;IACvC,CAAC;IACD8C,kBAAkB,EAAE;MAClBrC,SAAS,EAAE,QAAQ;MACnBL,KAAK,EAAE,QAAQ;MACfM,OAAO,EAAE,EAAE;MACXP,IAAI,EAAE,oBAAoB;MAC1BI,MAAM,EAAE;QACNI,KAAK,EAAE,CAAC;UAAEC,QAAQ,EAAE,IAAI;UAAEC,OAAO,EAAE;QAAY,CAAC;MAClD,CAAC;MACDE,cAAc,EAAE;QACdO,WAAW,EAAE,KAAK;QAClBE,UAAU,EAAE;MACd,CAAC;MACDN,OAAO,EAAElB,eAAe,CAAC,oBAAoB;IAC/C;EACF,CAAC;AAAA,CAAC"}]}