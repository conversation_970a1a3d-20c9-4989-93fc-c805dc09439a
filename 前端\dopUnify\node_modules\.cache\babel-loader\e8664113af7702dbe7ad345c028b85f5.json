{"remainingRequest": "E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1\\数字运营平台-统一门户工程\\dopUnify\\node_modules\\babel-loader\\lib\\index.js!E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1\\数字运营平台-统一门户工程\\dopUnify\\node_modules\\eslint-loader\\index.js??ref--13-0!E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1\\数字运营平台-统一门户工程\\dopUnify\\src\\api\\views\\home\\index.js", "dependencies": [{"path": "E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1\\数字运营平台-统一门户工程\\dopUnify\\src\\api\\views\\home\\index.js", "mtime": 1716875179875}, {"path": "E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1\\数字运营平台-统一门户工程\\dopUnify\\babel.config.js", "mtime": 1716875191548}, {"path": "E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1\\数字运营平台-统一门户工程\\dopUnify\\.babelrc", "mtime": 1716875191502}, {"path": "E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1\\数字运营平台-统一门户工程\\dopUnify\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1\\数字运营平台-统一门户工程\\dopUnify\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1\\数字运营平台-统一门户工程\\dopUnify\\node_modules\\eslint-loader\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:aW1wb3J0IF9vYmplY3RTcHJlYWQgZnJvbSAiRTovMV9Qcm9qZWN0L1hZRF9Qcm9qZWN0L2RvcC00LjAvZG9wLTQuMS9cdTY1NzBcdTVCNTdcdThGRDBcdTg0MjVcdTVFNzNcdTUzRjAtXHU3RURGXHU0RTAwXHU5NUU4XHU2MjM3XHU1REU1XHU3QTBCL2RvcFVuaWZ5L25vZGVfbW9kdWxlcy9AYmFiZWwvcnVudGltZS9oZWxwZXJzL2VzbS9vYmplY3RTcHJlYWQyLmpzIjsKaW1wb3J0IHJlcXVlc3QgZnJvbSAnQC91dGlscy9yZXF1ZXN0JzsKaW1wb3J0IGRlZmF1bHRTZXR0aW5ncyBmcm9tICdAL3NldHRpbmdzJzsKdmFyIHN5c3RlbSA9IGRlZmF1bHRTZXR0aW5ncy5zZXJ2aWNlLnN5c3RlbTsKLy8g5Li76aG15qih5Z2XYmVnaW4KaW1wb3J0IHsgTm90aWNlIH0gZnJvbSAnLi9jb21wb25lbnQvbm90aWNlJzsgLy8g5YWs5ZGK5L+h5oGvCmltcG9ydCB7IHRvZG9MaXN0IH0gZnJvbSAnLi9jb21wb25lbnQvdG9kb0xpc3QnOyAvLyDlvoXlip7kuovpobkKaW1wb3J0IHsgdXN1YWxNZW51IH0gZnJvbSAnLi9jb21wb25lbnQvdXN1YWxNZW51JzsgLy8g5bi455So5Yqf6IO9CmltcG9ydCB7IEJ1c2luZXNzIH0gZnJvbSAnLi9jb21wb25lbnQvYnVzaW5lc3MnOyAvLyDkuJrliqHph48KaW1wb3J0IHsgSW1wb3J0YW50IH0gZnJvbSAnLi9jb21wb25lbnQvaW1wb3J0YW50JzsgLy8g6YeN6KaB5L+h5oGvCmltcG9ydCB7IENhbGVuZGFyIH0gZnJvbSAnLi9jb21wb25lbnQvY2FsZW5kYXInOyAvLyDkvr/nrb7ml6XljoYKaW1wb3J0IHsgc2hvcnRjdXRMaXN0IH0gZnJvbSAnLi9jb21wb25lbnQvc2hvcnRjdXRNZW51JzsgLy8g5b+r5o236I+c5Y2VCmltcG9ydCB7IHdvcmtiZW5jaCB9IGZyb20gJy4vY29tcG9uZW50L3dvcmtiZW5jaCc7IC8vIOaIkeeahOW3peS9nOWPsAovLyDkuLvpobXmqKHlnZdlbmQKCmV4cG9ydCB2YXIgSG9tZSA9IF9vYmplY3RTcHJlYWQoX29iamVjdFNwcmVhZChfb2JqZWN0U3ByZWFkKF9vYmplY3RTcHJlYWQoX29iamVjdFNwcmVhZChfb2JqZWN0U3ByZWFkKF9vYmplY3RTcHJlYWQoX29iamVjdFNwcmVhZCh7CiAgLyoqCiAgICog5Li76aG15p+l6K+iCiAgICovCiAgcXVlcnk6IGZ1bmN0aW9uIHF1ZXJ5KGRhdGEpIHsKICAgIHJldHVybiByZXF1ZXN0KHsKICAgICAgdXJsOiBzeXN0ZW0gKyAnL3N5c0RpYWxvZy9xdWVyeS5kbycsCiAgICAgIG1ldGhvZDogJ2dldCcsCiAgICAgIHBhcmFtczogewogICAgICAgIG1lc3NhZ2U6IGRhdGEKICAgICAgfQogICAgfSk7CiAgfSwKICBhY0hvbWVQYWdlSW5pdDogZnVuY3Rpb24gYWNIb21lUGFnZUluaXQoZGF0YSkgewogICAgcmV0dXJuIHJlcXVlc3QoewogICAgICB1cmw6ICcvYWNIb21lUGFnZUluaXQuZG8nLAogICAgICBtZXRob2Q6ICdwb3N0JywKICAgICAgZGF0YTogZGF0YQogICAgfSk7CiAgfQp9LCBOb3RpY2UpLCB0b2RvTGlzdCksIHVzdWFsTWVudSksIEJ1c2luZXNzKSwgSW1wb3J0YW50KSwgQ2FsZW5kYXIpLCBzaG9ydGN1dExpc3QpLCB3b3JrYmVuY2gpOw=="}, {"version": 3, "names": ["request", "defaultSettings", "system", "service", "Notice", "todoList", "usualMenu", "Business", "Important", "Calendar", "shortcutList", "workbench", "Home", "query", "data", "url", "method", "params", "message", "acHomePageInit"], "sources": ["E:/1_Project/XYD_Project/dop-4.0/dop-4.1/数字运营平台-统一门户工程/dopUnify/src/api/views/home/<USER>"], "sourcesContent": ["import request from '@/utils/request'\nimport defaultSettings from '@/settings'\nconst system = defaultSettings.service.system\n// 主页模块begin\nimport { Notice } from './component/notice' // 公告信息\nimport { todoList } from './component/todoList' // 待办事项\nimport { usualMenu } from './component/usualMenu' // 常用功能\nimport { Business } from './component/business' // 业务量\nimport { Important } from './component/important' // 重要信息\nimport { Calendar } from './component/calendar' // 便签日历\nimport { shortcutList } from './component/shortcutMenu' // 快捷菜单\nimport { workbench } from './component/workbench' // 我的工作台\n// 主页模块end\n\nexport const Home = {\n  /**\n   * 主页查询\n   */\n  query(data) {\n    return request({\n      url: system + '/sysDialog/query.do',\n      method: 'get',\n      params: { message: data }\n    })\n  },\n  acHomePageInit(data) {\n    return request({\n      url: '/acHomePageInit.do',\n      method: 'post',\n      data\n    })\n  },\n  ...Notice,\n  ...todoList,\n  ...usualMenu,\n  ...Business,\n  ...Important,\n  ...Calendar,\n  ...shortcutList,\n  ...workbench\n}\n"], "mappings": ";AAAA,OAAOA,OAAO,MAAM,iBAAiB;AACrC,OAAOC,eAAe,MAAM,YAAY;AACxC,IAAMC,MAAM,GAAGD,eAAe,CAACE,OAAO,CAACD,MAAM;AAC7C;AACA,SAASE,MAAM,QAAQ,oBAAoB,EAAC;AAC5C,SAASC,QAAQ,QAAQ,sBAAsB,EAAC;AAChD,SAASC,SAAS,QAAQ,uBAAuB,EAAC;AAClD,SAASC,QAAQ,QAAQ,sBAAsB,EAAC;AAChD,SAASC,SAAS,QAAQ,uBAAuB,EAAC;AAClD,SAASC,QAAQ,QAAQ,sBAAsB,EAAC;AAChD,SAASC,YAAY,QAAQ,0BAA0B,EAAC;AACxD,SAASC,SAAS,QAAQ,uBAAuB,EAAC;AAClD;;AAEA,OAAO,IAAMC,IAAI;EACf;AACF;AACA;EACEC,KAAK,iBAACC,IAAI,EAAE;IACV,OAAOd,OAAO,CAAC;MACbe,GAAG,EAAEb,MAAM,GAAG,qBAAqB;MACnCc,MAAM,EAAE,KAAK;MACbC,MAAM,EAAE;QAAEC,OAAO,EAAEJ;MAAK;IAC1B,CAAC,CAAC;EACJ,CAAC;EACDK,cAAc,0BAACL,IAAI,EAAE;IACnB,OAAOd,OAAO,CAAC;MACbe,GAAG,EAAE,oBAAoB;MACzBC,MAAM,EAAE,MAAM;MACdF,IAAI,EAAJA;IACF,CAAC,CAAC;EACJ;AAAC,GACEV,MAAM,GACNC,QAAQ,GACRC,SAAS,GACTC,QAAQ,GACRC,SAAS,GACTC,QAAQ,GACRC,YAAY,GACZC,SAAS,CACb"}]}