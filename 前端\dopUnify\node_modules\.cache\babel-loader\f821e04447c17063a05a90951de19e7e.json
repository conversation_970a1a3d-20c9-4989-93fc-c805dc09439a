{"remainingRequest": "E:\\华南区项目\\中国银行\\开发区\\科技公司运营平台\\前端\\dopUnify\\node_modules\\thread-loader\\dist\\cjs.js!E:\\华南区项目\\中国银行\\开发区\\科技公司运营平台\\前端\\dopUnify\\node_modules\\babel-loader\\lib\\index.js!E:\\华南区项目\\中国银行\\开发区\\科技公司运营平台\\前端\\dopUnify\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!E:\\华南区项目\\中国银行\\开发区\\科技公司运营平台\\前端\\dopUnify\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\华南区项目\\中国银行\\开发区\\科技公司运营平台\\前端\\dopUnify\\src\\views\\system\\role\\component\\table\\index.vue?vue&type=script&lang=js&", "dependencies": [{"path": "E:\\华南区项目\\中国银行\\开发区\\科技公司运营平台\\前端\\dopUnify\\src\\views\\system\\role\\component\\table\\index.vue", "mtime": 1721631954662}, {"path": "E:\\华南区项目\\中国银行\\开发区\\科技公司运营平台\\前端\\dopUnify\\babel.config.js", "mtime": 1721631954662}, {"path": "E:\\华南区项目\\中国银行\\开发区\\科技公司运营平台\\前端\\dopUnify\\.babelrc", "mtime": 1721631954662}, {"path": "E:\\华南区项目\\中国银行\\开发区\\科技公司运营平台\\前端\\dopUnify\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "E:\\华南区项目\\中国银行\\开发区\\科技公司运营平台\\前端\\dopUnify\\node_modules\\thread-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "E:\\华南区项目\\中国银行\\开发区\\科技公司运营平台\\前端\\dopUnify\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "E:\\华南区项目\\中国银行\\开发区\\科技公司运营平台\\前端\\dopUnify\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "E:\\华南区项目\\中国银行\\开发区\\科技公司运营平台\\前端\\dopUnify\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1655715099000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, null]}