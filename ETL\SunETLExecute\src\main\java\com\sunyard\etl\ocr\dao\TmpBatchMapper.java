package com.sunyard.etl.ocr.dao;

import javax.sql.rowset.CachedRowSet;
import com.sunyard.etl.ocr.bean.TmpBatch;
import com.sunyard.etl.ocr.common.OCRConstants;
import com.xxl.job.core.log.XxlJobLogger;
import org.sunyard.util.dbutil.DBHandler;

import java.sql.SQLException;
import java.util.ArrayList;
import java.util.List;

public class TmpBatchMapper {
	//执行数据源
	private String dataSourceIdStr;

	public TmpBatchMapper(String dataSourceIdStr){
		this.dataSourceIdStr = dataSourceIdStr;
	}

	/**
	 * 更新批次OCR识别状态
	 * @param record
	 * @return
	 * @throws SQLException
	 */
	public int updateByPrimaryKey(TmpBatch record) throws SQLException {
		DBHandler dbHandler = new DBHandler(dataSourceIdStr);
		String sql = "update BP_TMPBATCH_TB set OCR_FACTOR_FLAG = ? where BATCH_ID = ?";
		XxlJobLogger.log("执行sql:{0},参数:{1},{2}" ,sql, record.getOcrFactorFlag(), record.getBatchId());
		return dbHandler.execute(sql, record.getOcrFactorFlag(), record.getBatchId());
	}

	/**
	 * 查询指定批次状态的批次列表
	 * @param record
	 * @return
	 * @throws SQLException
	 */
	public List<TmpBatch> selectByCondition(TmpBatch record) throws SQLException {
		DBHandler dbHandler = new DBHandler(dataSourceIdStr);
		CachedRowSet rs = null;
		List<TmpBatch> list = new ArrayList<TmpBatch>();
		String sql = "SELECT * FROM BP_TMPBATCH_TB WHERE PROGRESS_FLAG = ? AND OCR_FACTOR_FLAG = ? AND IS_INVALID = ? ";
		XxlJobLogger.log("执行sql:{0},参数:{1},{2},{3}",sql, record.getProgressFlag(), record.getOcrFactorFlag(), record.getIsInvalid());
		rs = dbHandler.queryRs(sql, record.getProgressFlag(), record.getOcrFactorFlag(), record.getIsInvalid());
		while (rs.next()) {
			list.add(new TmpBatch(rs));
		}
		return list;
	}
}
