﻿<?xml version="1.0" encoding="UTF-8"?>
<configuration scan="true" scanPeriod="10 seconds" debug="false">

	<!--打印logback初始化日志系统信息，debug="true"是同等效果 -->
	<!--<statusListener class="ch.qos.logback.core.status.OnConsoleStatusListener"/> -->

	<!-- 将日志输出在当前项目的根目录下 -->
	<property name="contextPath" value="d:/log/SunETLConsole"/>
	<!-- 日志的上下文路径 -->
	<property name="logPath" value="${contextPath}" />
	<!-- 配置日志的滚动时间 -->
	<property name="maxHistory" value="90" />


	<!-- 打印日志到控制台 -->
	<appender name="rootConsole" class="ch.qos.logback.core.ConsoleAppender">
		<!-- encoder 在控制台打印日志的格式 -->
		<encoder>
			<pattern>%d{HH:mm:ss.SSS} [%thread] %-5level %logger{36}.%M - %msg%n
			</pattern>
		</encoder>
	</appender>


	<!-- 定义日志的输出方式:输出在文件夹info/root.log文件中 配置所有类INFO级别的滚动日志 -->
	<appender name="rootRollingInfo"
		class="ch.qos.logback.core.rolling.RollingFileAppender">
		<file>${logPath}/info/info.log</file>

		<rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
			<fileNamePattern>${logPath}/info/info.%d{yyyy-MM-dd}.log
			</fileNamePattern>
			<!-- 设置日志的滚动时间 -->
			<maxHistory>${maxHistory}</maxHistory>
		</rollingPolicy>
		<encoder>
			<pattern>%d{HH:mm:ss.SSS} [%thread] %-5level %logger{36}.%M - %msg%n
			</pattern>
			<charset>UTF-8</charset>
		</encoder>
	</appender>


	
    <!--
    	定义日志的输出方式:输出在文件夹warn/root.log文件中
    	配置所有类WARN级别的滚动日志
    -->	
    <appender name="rootRollingWarn" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>${logPath}/warn/warn.log</file>

        <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
            <fileNamePattern>${logPath}/warn/warn.%d{yyyy-MM-dd}.log</fileNamePattern>
            <!-- 设置日志的滚动时间 -->
            <maxHistory>${maxHistory}</maxHistory>
        </rollingPolicy>
        <encoder>
            <pattern>%d{HH:mm:ss.SSS} [%thread] %-5level %logger{36}.%M - %msg%n</pattern>
            <charset>UTF-8</charset>
        </encoder> 
        <!-- warn/root.log文件中的日志级别是 WARN以上的级别  -->
        <filter class="ch.qos.logback.classic.filter.LevelFilter">
            <level>WARN</level>
            <onMatch>ACCEPT</onMatch>
			<onMismatch>DENY</onMismatch>
        </filter>
    </appender>
    <!--
    	定义日志的输出方式:输出在文件夹error/root.log文件中
    	配置所有类ERROR级别的滚动日志
    -->
    <appender name="rootRollingError" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>${logPath}/error/error.log</file>

        <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
            <fileNamePattern>${logPath}/error/error.%d{yyyy-MM-dd}.log</fileNamePattern>
            <!-- 设置日志的滚动时间 -->
            <maxHistory>${maxHistory}</maxHistory>
        </rollingPolicy>
        <encoder>
            <pattern>%d{HH:mm:ss.SSS} [%thread] %-5level %logger{36}.%M - %msg%n</pattern>
            <charset>UTF-8</charset>
        </encoder>
        <!-- error/root.log文件中的日志级别是 ERROR以上的级别 -->
        <filter class="ch.qos.logback.classic.filter.LevelFilter">
            <level>ERROR</level>
            <onMatch>ACCEPT</onMatch>
			<onMismatch>DENY</onMismatch>
        </filter>
    </appender>



	<!-- 定义日志的输出方式:输出在文件夹time/time.log文件中 配置所有类SQL的日志 -->
	<appender name="rootRollingServiceTime"
		class="ch.qos.logback.core.rolling.RollingFileAppender">
		<file>${logPath}/time/service/serviceTime.log</file>

		<rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
			<fileNamePattern>${logPath}/time/service/serviceTime.%d{yyyy-MM-dd}.log
			</fileNamePattern>
			<!-- 设置日志的滚动时间 -->
			<maxHistory>${maxHistory}</maxHistory>
		</rollingPolicy>
		<encoder>
			<pattern>%d{HH:mm:ss.SSS} [%thread] %-5level %logger{36}.%M - %msg%n
			</pattern>
			<charset>UTF-8</charset>
		</encoder>
		<filter class="ch.qos.logback.classic.filter.LevelFilter"><!-- 只打印错误日志 -->
			<level>INFO</level>
			<onMatch>ACCEPT</onMatch>
			<onMismatch>DENY</onMismatch>
		</filter>
	</appender>

	<!-- 定义日志的输出方式:输出在文件夹time/time.log文件中 配置所有类SQL的日志 -->
	<appender name="rootRollingDaoTime"
		class="ch.qos.logback.core.rolling.RollingFileAppender">
		<file>${logPath}/time/dao/daoTime.log</file>

		<rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
			<fileNamePattern>${logPath}/time/dao/daoTime.%d{yyyy-MM-dd}.log
			</fileNamePattern>
			<!-- 设置日志的滚动时间 -->
			<maxHistory>${maxHistory}</maxHistory>
		</rollingPolicy>
		<encoder>
			<pattern>%d{HH:mm:ss.SSS} [%thread] %-5level %logger{36}.%M - %msg%n
			</pattern>
			<charset>UTF-8</charset>
		</encoder>
		<filter class="ch.qos.logback.classic.filter.LevelFilter"><!-- 只打印错误日志 -->
			<level>INFO</level>
			<onMatch>ACCEPT</onMatch>
			<onMismatch>DENY</onMismatch>
		</filter>
	</appender>


	<!-- 定义日志的输出方式:输出在文件夹time/time.log文件中 配置所有类SQL的日志 -->
	<appender name="jobReactiveManageLog"
			  class="ch.qos.logback.core.rolling.RollingFileAppender">
		<file>${logPath}/reactive/reactive/reactive_manage.log</file>

		<rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
			<fileNamePattern>${logPath}/reactive/reactive_manage.%d{yyyy-MM-dd}.log
			</fileNamePattern>
			<!-- 设置日志的滚动时间 -->
			<maxHistory>${maxHistory}</maxHistory>
		</rollingPolicy>
		<encoder>
			<pattern>%d{HH:mm:ss.SSS} [%thread] %-5level %logger{36}.%M - %msg%n
			</pattern>
			<charset>UTF-8</charset>
		</encoder>
		<filter class="ch.qos.logback.classic.filter.LevelFilter"><!-- 只打印错误日志 -->
			<level>INFO</level>
			<onMatch>ACCEPT</onMatch>
			<onMismatch>DENY</onMismatch>
		</filter>
	</appender>



	<logger name="org.springframework.security">
		<level value="INFO" />
	</logger>
	<logger name="com.xxl.job.admin.dao" level="info"/>
	<logger name="org.springframework.scheduling.quartz.LocalDataSourceJobStore" level="error"/>
	<logger name="org.springframework.web.servlet.mvc.method.annotation" level="error"/>


	<!-- 打印项目中com包下的日志到appender-ref指定的appender中 打印级别是debug 这里可以用来专门打印某一类别的日志到某一个特定的文件中. 
		比如:可以打印所有的业务逻辑到业务逻辑文件中;打印所有的controller请求到指定的文件中. -->
	<!-- 打印具体的某个文件中的日志到某个文件夹下. 注意:不是打印com.baihui.LogBackTest2文件夹下的日志,而是LogBackTest2文件的日志 
		addtivity="false" 表示打印的日志不向上传递,如果设置成addtivity="true"会怎么样呢?没错,日志打印了两遍 -->


	<logger name="com.xxl.job.admin.core.thread.JobReactiveManageHeper" level="INFO"
			addtivity="false">
		<appender-ref ref="jobReactiveManageLog" />
	</logger>



	<root>
		<level value="info" />
		<appender-ref ref="rootConsole" />
		<appender-ref ref="rootRollingInfo" />
		<appender-ref ref="rootRollingWarn" />
		<appender-ref ref="rootRollingError" />
	</root>

</configuration>