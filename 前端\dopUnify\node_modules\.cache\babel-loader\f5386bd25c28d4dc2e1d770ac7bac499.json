{"remainingRequest": "E:\\华南区项目\\中国银行\\开发区\\科技公司运营平台\\前端\\dopUnify\\node_modules\\babel-loader\\lib\\index.js!E:\\华南区项目\\中国银行\\开发区\\科技公司运营平台\\前端\\dopUnify\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!E:\\华南区项目\\中国银行\\开发区\\科技公司运营平台\\前端\\dopUnify\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\华南区项目\\中国银行\\开发区\\科技公司运营平台\\前端\\dopUnify\\src\\views\\system\\externalManage\\priv\\component\\tree\\index.vue?vue&type=script&lang=js&", "dependencies": [{"path": "E:\\华南区项目\\中国银行\\开发区\\科技公司运营平台\\前端\\dopUnify\\src\\views\\system\\externalManage\\priv\\component\\tree\\index.vue", "mtime": 1721631954662}, {"path": "E:\\华南区项目\\中国银行\\开发区\\科技公司运营平台\\前端\\dopUnify\\babel.config.js", "mtime": 1721631954662}, {"path": "E:\\华南区项目\\中国银行\\开发区\\科技公司运营平台\\前端\\dopUnify\\.babelrc", "mtime": 1721631954662}, {"path": "E:\\华南区项目\\中国银行\\开发区\\科技公司运营平台\\前端\\dopUnify\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "E:\\华南区项目\\中国银行\\开发区\\科技公司运营平台\\前端\\dopUnify\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "E:\\华南区项目\\中国银行\\开发区\\科技公司运营平台\\前端\\dopUnify\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "E:\\华南区项目\\中国银行\\开发区\\科技公司运营平台\\前端\\dopUnify\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1655715099000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA8BA,SACAA,kBACAC,eACAC,wBACA;AACA;;AAEA;AACA;EAAAC;EAAAC;AACA;EACAC;EACAC;IACAC;MACAC;MACAC;QACA;MACA;IACA;EACA;EACAC;IACA;MACAC;MAAA;MACAC;MAAA;MACAC;MAAA;MACAC;MAAA;MACAC;MAAA;MACAC;QACAC;UACAC;QACA;QACAC;UACAD;QACA;MACA;MACAE;QACAC;QACAC;MACA;IACA;EACA;;EACAC;IACAhB;MACAiB;QACA;MACA;MACAC;IACA;EACA;EACAC;IACA;MACA;IACA;IACA;EACA;EACAC;IAAA;IACA;IACA;MACA;IACA;EACA;;EACAC;IACA;AACA;AACA;AACA;AACA;IACAC;MACA;MACA;MACA;MACA;QACA;QACA;MACA;QACA;UACA;UACA;QACA;MACA;IACA;IACA;AACA;AACA;AACA;AACA;IACAC;MACA;MACA;QACA;MACA;IACA;IACA;AACA;AACA;IACAC;MACA;MACA;QACA;QACA;MACA;IACA;IACA;IACA;AACA;IACAC;MAAA;MACA;MACA;QACAC;QACAC;UACAC;UACAC;QACA;MACA;MACAjC;QACA;QACA;UACA;UACA;QACA;UACA;UAAA,2CACAkC;YAAA;UAAA;YAAA;cAAA;cACAC;YACA;UAAA;YAAA;UAAA;YAAA;UAAA;UACA;QACA;MACA;IACA;IACAC;MACA;IACA;IACAC;MAAA;MACA;MACA;QACAvC;QACA;MACA;MACA;MACAC;QACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;;UAEA;UACA;UACA;UACA;UACA;YACA+B;YACAE;YACAC;UACA;UACA;YACAnC;YACA;YACA;UACA;UACAG,YACAqC;YACAzC;YACA;UACA,GACA0C;YACAzC;UACA;UACA;QACA;MACA;IACA;IACA;AACA;AACA;AACA;AACA;IACA0C;MAAA,4CACAC;QAAA;MAAA;QAAA;UAAA;UACA;YACA,6CACA,iBACA;cACAC;cACAC;cACAC;YACA,GACA;UACA;QACA;MAAA;QAAA;MAAA;QAAA;MAAA;IACA;IACA;AACA;AACA;AACA;IACAC;MACA;MACA;MACA;QACA;MACA;MACA;IACA;IACA;AACA;AACA;AACA;IACAC;MACA;MACA;MAAA,4CACAC;QAAA;MAAA;QAAA;UAAA;UACA;UAAA,4CACAC;YAAA;UAAA;YAAA;cAAA;cACA;gBACAC;gBACA;cACA;YACA;UAAA;YAAA;UAAA;YAAA;UAAA;UACA;YACAC;UACA;QACA;MAAA;QAAA;MAAA;QAAA;MAAA;MACA;IACA;IACA;AACA;IACAC;MACA;IACA;EACA;AACA", "names": ["commonMsgSuccess", "commonMsgWarn", "commonMsgConfirm", "query", "modify", "name", "props", "defaultForm", "type", "default", "data", "treeData", "loading", "defaultId", "oldSelectDatas", "backdata", "btnDatas", "btnSubmit", "show", "btnEmpty", "totalHeight", "height", "overflow", "watch", "handler", "deep", "created", "mounted", "methods", "handleCheckChange", "uniteChildSame", "selected<PERSON><PERSON>nt", "queryList", "parameterList", "sysMap", "oper_type", "system_no", "list", "keyArr", "handleEmpty", "handleSubmit", "then", "catch", "filterData", "arr", "external_system_no", "organ_no", "operNodes", "nodesSpilt", "dataFilter", "one", "two", "flag", "As", "showLoading"], "sourceRoot": "src/views/system/externalManage/priv/component/tree", "sources": ["index.vue"], "sourcesContent": ["<!--\r\n* 关联系统机构权限配置: 机构树\r\n-->\r\n<template>\r\n  <div class=\"sun-content\" :style=\"totalHeight\">\r\n    <div class=\"sun-content-tree\">\r\n      <el-tree\r\n        ref=\"tree\"\r\n        v-loading=\"loading\"\r\n        :data=\"treeData\"\r\n        show-checkbox\r\n        auto-expand-parent\r\n        check-strictly\r\n        node-key=\"id\"\r\n        :default-expanded-keys=\"[defaultId]\"\r\n        :default-checked-keys=\"oldSelectDatas\"\r\n        @check=\"handleCheckChange\"\r\n      />\r\n    </div>\r\n    <div class=\"button-footer\">\r\n      <sun-button\r\n        :btn-datas=\"btnDatas\"\r\n        @handleEmpty=\"handleEmpty\"\r\n        @handleSubmit=\"handleSubmit\"\r\n      />\r\n      <!-- <div class=\"footer-left\">双击按钮节点设置按钮操作页面要素的权限</div> -->\r\n    </div>\r\n  </div>\r\n</template>\r\n<script>\r\nimport {\r\n  commonMsgSuccess,\r\n  commonMsgWarn,\r\n  commonMsgConfirm\r\n} from '@/utils/message.js' // 提示信息\r\nimport { dictionaryGet } from '@/utils/dictionary.js' // 字典常量\r\n\r\nimport { system } from '@/api'\r\nconst { query, modify } = system.SysExterPriv\r\nexport default {\r\n  name: 'TreeList',\r\n  props: {\r\n    defaultForm: {\r\n      type: Object,\r\n      default: function() {\r\n        return {}\r\n      }\r\n    }\r\n  },\r\n  data() {\r\n    return {\r\n      treeData: [], // 树形组件的数据\r\n      loading: false, // 加载动画\r\n      defaultId: '000001', // 默认展开根节点\r\n      oldSelectDatas: [], // 默认选中节点\r\n      backdata: [], // 保存数据合并\r\n      btnDatas: {\r\n        btnSubmit: {\r\n          show: this.$attrs['btn-all'].btnSubmit\r\n        },\r\n        btnEmpty: {\r\n          show: this.$attrs['btn-all'].btnEmpty\r\n        }\r\n      },\r\n      totalHeight: {\r\n        height: 0,\r\n        overflow: 'auto'\r\n      } // 下方组件动态高度\r\n    }\r\n  },\r\n  watch: {\r\n    defaultForm: {\r\n      handler(val) {\r\n        this.queryList()\r\n      },\r\n      deep: true\r\n    }\r\n  },\r\n  created() {\r\n    const minObj = this.$store.getters.organList.reduce((prev, curr) => {\r\n      return Number(prev.reserve) < Number(curr.reserve) ? prev : curr\r\n    })\r\n    this.defaultId = minObj.id\r\n  },\r\n  mounted() {\r\n    this.treeData = this.$store.getters.organTree\r\n    this.$nextTick(() => {\r\n      this.totalHeight.height = this.$attrs.height - 50 + 'px' // 20上下组件之间为边距\r\n    })\r\n  },\r\n  methods: {\r\n    /**\r\n     * 单击树节点: 节点选中状态发生变化时的回调  begin ----------------------------------------------\r\n     * @param {Object}data：currentObj 该节点所对应的对象\r\n     * @param {Boolean}checked：treeStatus 节点本身是否被选中\r\n     */\r\n    handleCheckChange(currentObj, treeStatus) {\r\n      // 用于：父子节点严格互不关联时，父节点勾选变化时通知子节点同步变化，实现单向关联。\r\n      const selected = treeStatus.checkedKeys.indexOf(currentObj.id) // -1未选中\r\n      // 选中\r\n      if (selected !== -1) {\r\n        this.selectedParent(currentObj) // 子节点只要被选中父节点就被选中\r\n        this.uniteChildSame(currentObj, true) // 统一处理子节点为相同的勾选状态\r\n      } else {\r\n        if (currentObj.children.length !== 0) {\r\n          // 未选中 处理子节点全部未选中\r\n          this.uniteChildSame(currentObj, false)\r\n        }\r\n      }\r\n    },\r\n    /**\r\n     * 统一处理子节点为相同的勾选状态\r\n     * @param {Object} treeList 选中节点\r\n     * @param {Boolean} isSelected 选中状态\r\n     * */\r\n    uniteChildSame(treeList, isSelected) {\r\n      this.$refs.tree.setChecked(treeList, isSelected)\r\n      for (let i = 0; i < treeList.children.length; i++) {\r\n        this.uniteChildSame(treeList.children[i], isSelected)\r\n      }\r\n    },\r\n    /**\r\n     * 统一处理父节点为选中\r\n     * @param {Object}currentObj 当前选中节点的父节点*/\r\n    selectedParent(currentObj) {\r\n      const currentNode = this.$refs.tree.getNode(currentObj)\r\n      if (currentNode.parent.key !== undefined) {\r\n        this.$refs.tree.setChecked(currentNode.parent, true)\r\n        this.selectedParent(currentNode.parent)\r\n      }\r\n    },\r\n    /* 单击树节点: 节点选中状态发生变化时的回调  end ----------------------------------------------*/\r\n    /**\r\n     * 按钮：查询*/\r\n    queryList() {\r\n      this.handleEmpty()\r\n      const msg = {\r\n        parameterList: [{}],\r\n        sysMap: {\r\n          oper_type: dictionaryGet('OPERATE_QUERY'),\r\n          system_no: this.defaultForm.system_no\r\n        }\r\n      }\r\n      query(msg).then((response) => {\r\n        const { list } = response.retMap\r\n        if (list.length === 0) {\r\n          this.oldSelectDatas = []\r\n          this.$refs.tree.setCheckedKeys([]) // 清空所有选中\r\n        } else {\r\n          const keyArr = []\r\n          for (const item of list) {\r\n            keyArr.push(item.organ_no)\r\n          }\r\n          this.oldSelectDatas = keyArr\r\n        }\r\n      })\r\n    },\r\n    handleEmpty() {\r\n      this.$refs.tree.setCheckedKeys([]) // 清空所有选中\r\n    },\r\n    handleSubmit() {\r\n      const system_no = this.defaultForm.system_no\r\n      if (!system_no) {\r\n        commonMsgWarn('请选择需要配置权限的系统！', this)\r\n        return\r\n      }\r\n      this.backdata = []\r\n      commonMsgConfirm('是否确认保存？', this, (param) => {\r\n        if (param) {\r\n          this.showLoading()\r\n          const checkedAll = this.$refs.tree.getCheckedKeys() // 获取所有选中nodes\r\n          // 新旧数据格式化\r\n          const oldData = this.nodesSpilt(this.oldSelectDatas)\r\n          const newData = this.nodesSpilt(checkedAll)\r\n          // 新旧数据比对\r\n          const addNodes = this.dataFilter(newData, oldData)\r\n          const deleteNodes = this.dataFilter(oldData, newData)\r\n\r\n          // 传递数据格式化 begin\r\n          this.filterData(system_no, addNodes, 'addNodes')\r\n          this.filterData(system_no, deleteNodes, 'deleteNodes')\r\n          // 操作数据格式化 end\r\n          const msg = {\r\n            parameterList: this.backdata,\r\n            oper_type: dictionaryGet('OPERATE_MODIFY'),\r\n            system_no: system_no\r\n          }\r\n          if (!this.backdata.length) {\r\n            commonMsgWarn('没有变更的权限，请勾选权限！', this)\r\n            this.showLoading()\r\n            return\r\n          }\r\n          modify(msg)\r\n            .then((response) => {\r\n              commonMsgSuccess('权限配置成功！', this)\r\n              this.queryList()\r\n            })\r\n            .catch(() => {\r\n              commonMsgWarn('权限配置错误', this)\r\n            })\r\n          this.showLoading()\r\n        }\r\n      })\r\n    },\r\n    /**\r\n     * 保存：数据格式化\r\n     * @param {String} system_no 系统号\r\n     * @param {Array} arr 格式化数组\r\n     * @param {String} oper_nodes 格式化类型*/\r\n    filterData(system_no, arr, oper_nodes) {\r\n      for (const key of arr) {\r\n        if (key !== '') {\r\n          this.backdata = [\r\n            ...this.backdata,\r\n            {\r\n              external_system_no: system_no,\r\n              organ_no: key,\r\n              operNodes: oper_nodes\r\n            }\r\n          ]\r\n        }\r\n      }\r\n    },\r\n    /**\r\n     * 取nodes的字符串，用逗号拼接机构id\r\n     * 格式为1,2,3\r\n     * @param {Array}str 拼接数组*/\r\n    nodesSpilt(str) {\r\n      const strs = str\r\n      const newStr = []\r\n      if (strs.length === 0) {\r\n        return newStr\r\n      }\r\n      return strs\r\n    },\r\n    /**\r\n     * 编辑数据过滤 : 新增、删除 one有two没有的情况\r\n     * @param {Array}one 数据比对1\r\n     * @param {Array}two 数据比对2 */\r\n    dataFilter(one1, two) {\r\n      const one = one1.sort()\r\n      let As = []\r\n      for (const key of one) {\r\n        let flag = 0\r\n        for (const item of two) {\r\n          if (key.substring(0, 7) === item.substring(0, 7)) {\r\n            flag = 1\r\n            break\r\n          }\r\n        }\r\n        if (flag === 0) {\r\n          As = [...As, key]\r\n        }\r\n      }\r\n      return As\r\n    },\r\n    /**\r\n     * 树加载中动画配置*/\r\n    showLoading() {\r\n      this.loading = !this.loading\r\n    }\r\n  }\r\n}\r\n</script>\r\n<style scoped>\r\n.button-footer {\r\n  position: absolute;\r\n  bottom: 3%;\r\n  right: 4%;\r\n}\r\n</style>\r\n"]}]}