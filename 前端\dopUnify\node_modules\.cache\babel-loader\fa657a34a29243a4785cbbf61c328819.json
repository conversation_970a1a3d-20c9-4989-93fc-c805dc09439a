{"remainingRequest": "D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\node_modules\\babel-loader\\lib\\index.js!D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\src\\components\\Dialog\\SunGeneralDialog\\index.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\src\\components\\Dialog\\SunGeneralDialog\\index.vue", "mtime": 1703583638299}, {"path": "D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\babel.config.js", "mtime": 1686019818359}, {"path": "D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\.babelrc", "mtime": 1705285078668}, {"path": "D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1655715099000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;AAyBA;AACA;EACAA;EACAC;IACAC;MACAC;MACAC;QACA;UACAC;YACA;YACAC;UACA;;UACAC;UAAA;UACAC;QACA;MACA;IACA;EACA;EACAC;IACA;MACAC;MACAC;MACA;MACAC;MAAA;MACAC;MAAA;MACAC;MACAC;IACA;EACA;;EACAC;EACAC;IACAL;MACA;QACA;MACA;IACA;IACAC;MAAA;MACA;QACA;QACA;QACA;UACA;QACA;MACA;IACA;EACA;EACAK;IAAA;IACA;IACA;IACA;MACA;QACA;QACA;MACA;QACA;QACA;QACA;MACA;MACA;IACA;EACA;EACAC;IACA;AACA;AACA;IACAC;MACA;MACA;QACA;QACA;QACA;QACAC;QACA;UACAA;UACAC;UACAnB;UACAY;UACAQ;QACA;QACAC;QACA;QACAC,iCACAC,sEACA;MACA;IACA;IACA;AACA;AACA;IACAC;MAAA;MACAC;QACA;QACA;QACA;UACA;UACA;QACA;UACA;QAAA,CACA,UACAC,0CACAA,wCACA;UACA;QACA;MACA;IACA;IACA;AACA;AACA;IACAC;MACA;MACA;MACA;IACA;IACAC;MACA;IACA;EACA;AACA", "names": ["name", "props", "dialogConfig", "type", "default", "componentProps", "title", "visible", "task_id", "data", "iframeUrl", "extendRef", "is<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "flow_flag", "height", "projectName", "computed", "watch", "mounted", "methods", "postMess", "user", "common", "generalData", "iframeWin", "JSON", "process", "postParam", "window", "evt", "dialogClose", "changeGeneral"], "sourceRoot": "src/components/Dialog/SunGeneralDialog", "sources": ["index.vue"], "sourcesContent": ["<!--\r\n* 基线通用弹窗入口\r\n-->\r\n<template>\r\n  <el-dialog\r\n    ref=\"refDialog\"\r\n    :visible.sync=\"dialogConfig.visible\"\r\n    :before-close=\"dialogClose\"\r\n    :close-on-click-modal=\"false\"\r\n    :append-to-body=\"true\"\r\n    :width=\"dialogConfig.componentProps.width\"\r\n    v-bind=\"dialogConfig.componentProps\"\r\n  >\r\n    <iframe\r\n      :ref=\"extendRef\"\r\n      :v-if=\"dialogConfig.visible\"\r\n      class=\"extendIframe\"\r\n      :src=\"iframeUrl\"\r\n      frameborder=\"0\"\r\n      :style=\"{ height }\"\r\n    />\r\n  </el-dialog>\r\n</template>\r\n\r\n<script>\r\nimport { deepClone } from '@/utils/common'\r\nexport default {\r\n  name: 'SunGeneralDialog',\r\n  props: {\r\n    dialogConfig: {\r\n      type: Object,\r\n      default: () => {\r\n        return {\r\n          componentProps: {\r\n            // 弹出框属性\r\n            title: '' // 弹出框标题 1-本地授权确认 2-远程审核提交\r\n          },\r\n          visible: false, // 显示隐藏配置\r\n          task_id: '' // 流程id\r\n        }\r\n      }\r\n    }\r\n  },\r\n  data() {\r\n    return {\r\n      iframeUrl: '',\r\n      extendRef: 'extendRef' + new Date().getTime(),\r\n      // extendRef: 'extendRef',\r\n      isChildrenReady: false, // 子工程是否准备好\r\n      flow_flag: false, // 子工程弹窗打开状态\r\n      height: 0,\r\n      projectName: '' // 子项目名称\r\n    }\r\n  },\r\n  computed: {},\r\n  watch: {\r\n    isChildrenReady(val) {\r\n      if (val) {\r\n        this.postMess()\r\n      }\r\n    },\r\n    flow_flag(val) {\r\n      if (val) {\r\n        this.dialogClose() // 流程弹窗关闭\r\n        this.$emit('queryList')\r\n        this.$nextTick(() => {\r\n          this.$bus.$emit('moudleSubUpdate', true) // true  // 更新系统消息弹窗的数据/更新系统消息右下角弹窗数据\r\n        })\r\n      }\r\n    }\r\n  },\r\n  mounted() {\r\n    // this.height = document.body.clientHeight - 54 - 64 - 300 + 'px'\r\n    this.height = document.body.clientHeight - 54 - 64 + 'px'\r\n    this.$nextTick().then(() => {\r\n      if (process.env.NODE_ENV !== 'development') {\r\n        // 生产环境\r\n        this.iframeUrl = `${window.location.origin}${window.location.pathname}redirect/SunAOS/iframe.html`\r\n      } else {\r\n        // 开发环境、 本地测试\r\n        // this.iframeUrl = `http://***********/dopUnify/redirect/unify/iframe.html`\r\n        this.iframeUrl = 'http://localhost:5000/SunAOS/iframe.html'\r\n      }\r\n      this.postParam()\r\n    })\r\n  },\r\n  methods: {\r\n    /**\r\n     *向iframe子页面发送消息\r\n     */\r\n    postMess() {\r\n      const mapFrame = this.$refs[this.extendRef]\r\n      if (mapFrame) {\r\n        const iframeWin = mapFrame.contentWindow\r\n        const user = deepClone(this.$store.state.user)\r\n        // user.routeM.path = `http://***********/dopUnify/redirect/unify/iframe.html?/unify/static/html/flow/workflow.html` // 本地测试\r\n        user.routeM.path = `${this.iframeUrl}?/unify/${this.dialogConfig.childFlowUrl}`\r\n        const msg = {\r\n          user: user,\r\n          common: this.$store.state.common,\r\n          type: 'dopUnify',\r\n          projectName: 'initGeneral',\r\n          generalData: this.dialogConfig\r\n        }\r\n        iframeWin.postMessage(\r\n          // 向子工程发送\r\n          JSON.parse(JSON.stringify(msg)),\r\n          process.env.NODE_ENV === 'development' ? '*' : window.location.origin\r\n        )\r\n      }\r\n    },\r\n    /**\r\n     * 接收iframe子页面消息\r\n     */\r\n    postParam() {\r\n      window.addEventListener('message', (evt) => {\r\n        // 接收子工程数据\r\n        // 若子页面已经加载好通知父工程修改了isChildrenReady的状态\r\n        if (evt.data.type === 'dopUnify') {\r\n          this.isChildrenReady = true // true表示子工程已经准备好向父工程发送消息了\r\n          this.projectName = evt.data.projectName\r\n        } else if (evt.data.type === 'changeGeneral') {\r\n          // this.changeGeneral()\r\n        } else if (\r\n          evt.data.type === 'closeGeneralDialog' &&\r\n          evt.data.projectName === 'initGeneral'\r\n        ) {\r\n          this.dialogClose()\r\n        }\r\n      })\r\n    },\r\n    /**\r\n     * 弹出框：关闭\r\n     */\r\n    dialogClose() {\r\n      // this.$store.state.common.flow.module_id = '' // 置空流程ID\r\n      // this.$store.state.common.flow.workflow_dataStr.flow_type = 'add'\r\n      this.$emit('dialogClose', this.extendRef) // this.flowConfig.visible = false父组件流程弹窗关闭\r\n    },\r\n    changeGeneral() {\r\n      this.$emit('changeGeneral', this.dialogConfig.data)\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n:deep(.el-dialog) {\r\n  height: 100%;\r\n  margin-bottom: 0 !important;\r\n}\r\n.extendIframe {\r\n  width: 100%;\r\n  height: 100%;\r\n  top: 5rem;\r\n  bottom: 0;\r\n  overflow: hidden;\r\n}\r\n// ::v-deep {\r\n//   .el-dialog__header {\r\n//     background: #2670f5;\r\n//   }\r\n// }\r\n</style>\r\n"]}]}