package com.sunyard.etl.common.util;

public enum DbTypeEnum {
    ORACLE(new String[]{"oracle.jdbc.OracleDriver", "oracle.jdbc.driver.OracleDriver"}, true),
    DB2(new String[]{"com.ibm.db2.jcc.DB2Driver"}, true),
    MYSQL(new String[]{"com.mysql.jdbc.Driver", "com.mysql.cj.jdbc.Driver"}, false),
    POSTGRESQL(new String[]{"org.postgresql.Driver"}, false),
    OPENGAUSS(new String[]{"org.opengauss.Driver"}, false),
    DM8(new String[]{"dm.jdbc.driver.DmDriver"}, true),
    GBASE(new String[]{"com.gbasedbt.jdbc.Driver"}, false),
    KINGBASE8(new String[]{"com.kingbase8.Driver"}, false),
    OCEAN_BASE(new String[]{"com.alipay.oceanbase.jdbc.Driver", "com.oceanbase.jdbc.Driver"}, true),
    DEFAULT(new String[]{"Unknown Driver"}, true);

    private final String[] driverClassName;
    private final boolean upperCase;

    DbTypeEnum(String[] driverClassName, boolean upperCase) {
        this.driverClassName = driverClassName;
        this.upperCase = upperCase;
    }

    public String[] getDriverClassName() {
        return driverClassName;
    }

    public boolean isUpperCase() {
        return upperCase;
    }

    public boolean isLowerCase() {
        return !upperCase;
    }

    @Override
    public String toString() {
        StringBuilder driverClassStr = new StringBuilder();
        for (String driver : driverClassName) {
            driverClassStr.append(driver).append(",");
        }
        return "DbTypeEnum{driverClassName='" + driverClassStr + "', upperCase=" + upperCase + '}';
    }

    public static DbTypeEnum matchByName(String name) {
        for (DbTypeEnum value : values()) {
            if (value.name().equals(name)) {
                return value;
            }
        }
        throw new RuntimeException("未知的数据库类型: " + name);
    }

    public static DbTypeEnum matchByDriver(String driverClassName) {
        for (DbTypeEnum value : values()) {
            for (String driver : value.driverClassName) {
                if (driver.equals(driverClassName)) {
                    return value;
                }
            }
        }
        return DEFAULT;
    }
}
