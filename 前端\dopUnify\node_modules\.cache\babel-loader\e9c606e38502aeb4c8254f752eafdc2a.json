{"remainingRequest": "E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\node_modules\\babel-loader\\lib\\index.js!E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\node_modules\\qiankun\\es\\loader.js", "dependencies": [{"path": "E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\node_modules\\qiankun\\es\\loader.js", "mtime": 1667130453000}, {"path": "E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\babel.config.js", "mtime": 1686019818359}, {"path": "E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["_objectSpread", "_concat", "_mergeWith2", "_typeof", "_objectWithoutProperties", "_forEach", "_asyncToGenerator", "_excluded", "_regeneratorRuntime", "importEntry", "getAddOns", "QiankunError", "getMicroAppStateActions", "createSandboxContainer", "css", "trustedGlobals", "Deferred", "genAppInstanceIdByName", "getContainer", "getDefaultTplWrapper", "getWrapperId", "isEnableScopedCSS", "performanceGetEntriesByName", "performanceMark", "performanceMeasure", "toArray", "validateExportLifecycle", "assertElementExist", "element", "msg", "execHooks<PERSON>hain", "hooks", "app", "global", "arguments", "length", "undefined", "window", "reduce", "chain", "hook", "then", "Promise", "resolve", "validateSingularMode", "_x", "_x2", "_validateSingularMode", "apply", "mark", "_callee", "validate", "wrap", "_callee$", "_context", "prev", "next", "abrupt", "stop", "supportShadowDOM", "document", "head", "attachShadow", "createShadowRoot", "createElement", "appContent", "strictStyleIsolation", "scopedCSS", "appInstanceId", "containerElement", "innerHTML", "appElement", "<PERSON><PERSON><PERSON><PERSON>", "console", "warn", "shadow", "mode", "attr", "getAttribute", "QiankunCSSRewriteAttr", "setAttribute", "styleNodes", "querySelectorAll", "stylesheetElement", "process", "getAppWrapperGetter", "useLegacyRender", "elementGetter", "appWrapper", "getElementById", "concat", "shadowRoot", "rawAppendChild", "HTMLElement", "prototype", "append<PERSON><PERSON><PERSON>", "rawRemoveChild", "<PERSON><PERSON><PERSON><PERSON>", "getRender", "legacyRender", "render", "_ref", "phase", "loading", "container", "env", "NODE_ENV", "error", "errorMsg", "contains", "call", "getLifecyclesFromExports", "scriptExports", "appName", "globalLatestSetProp", "lifecycles", "globalVariableExports", "prevAppUnmountedDeferred", "loadApp", "_x3", "_loadApp", "_callee17", "_sandboxContainer", "_sandboxContainer$ins", "configuration", "lifeCycles", "entry", "<PERSON><PERSON><PERSON>", "_configuration$singul", "singular", "_configuration$sandbo", "sandbox", "excludeAssetFilter", "_configuration$global", "globalContext", "importEntryOpts", "_yield$importEntry", "template", "execScripts", "assetPublicPath", "initialAppWrapperElement", "initialContainer", "initialAppWrapperGetter", "mountSandbox", "unmountSandbox", "useLooseSandbox", "speedySandbox", "sandboxContainer", "_mergeWith", "_mergeWith$beforeUnmo", "beforeUnmount", "_mergeWith$afterUnmou", "afterUnmount", "_mergeWith$afterMount", "afterMount", "_mergeWith$beforeMoun", "beforeMount", "_mergeWith$beforeLoad", "beforeLoad", "_getLifecyclesFromExp", "bootstrap", "mount", "unmount", "update", "_getMicroAppStateActi", "onGlobalStateChange", "setGlobalState", "offGlobalStateChange", "syncAppWrapperElement2Sandbox", "parcelConfigGetter", "_args17", "_callee17$", "_context17", "name", "sent", "promise", "loose", "speedy", "instance", "proxy", "v1", "v2", "scopedGlobalVariables", "latestSetProp", "remountC<PERSON><PERSON>", "appWrapperElement", "appWrapperGetter", "parcelConfig", "_callee2", "marks", "_callee2$", "_context2", "_callee3", "_callee3$", "_context3", "t0", "_callee4", "_callee4$", "_context4", "_callee5", "useNewContainer", "_callee5$", "_context5", "_callee6", "_callee6$", "_context6", "_ref7", "_callee7", "props", "_callee7$", "_context7", "_x4", "_callee8", "_callee8$", "_context8", "_callee9", "_callee9$", "_context9", "_callee10", "_callee10$", "_context10", "_callee11", "measureName", "_callee11$", "_context11", "_callee12", "_callee12$", "_context12", "_ref13", "_callee13", "_callee13$", "_context13", "_x5", "_callee14", "_callee14$", "_context14", "_callee15", "_callee15$", "_context15", "_callee16", "_callee16$", "_context16"], "sources": ["E:/1_Project/XYD_Project/dop-4.0/dop-4.1-qianduan-unify/dopUnify/node_modules/qiankun/es/loader.js"], "sourcesContent": ["import _objectSpread from \"@babel/runtime/helpers/esm/objectSpread2\";\nimport _concat from \"lodash/concat\";\nimport _mergeWith2 from \"lodash/mergeWith\";\nimport _typeof from \"@babel/runtime/helpers/esm/typeof\";\nimport _objectWithoutProperties from \"@babel/runtime/helpers/esm/objectWithoutProperties\";\nimport _forEach from \"lodash/forEach\";\nimport _asyncToGenerator from \"@babel/runtime/helpers/esm/asyncToGenerator\";\nvar _excluded = [\"singular\", \"sandbox\", \"excludeAssetFilter\", \"globalContext\"];\nimport _regeneratorRuntime from \"@babel/runtime/regenerator\";\n/**\n * <AUTHOR>\n * @since 2020-04-01\n */\nimport { importEntry } from 'import-html-entry';\nimport getAddOns from './addons';\nimport { QiankunError } from './error';\nimport { getMicroAppStateActions } from './globalState';\nimport { createSandboxContainer, css } from './sandbox';\nimport { trustedGlobals } from './sandbox/common';\nimport { Deferred, genAppInstanceIdByName, getContainer, getDefaultTplWrapper, getWrapperId, isEnableScopedCSS, performanceGetEntriesByName, performanceMark, performanceMeasure, toArray, validateExportLifecycle } from './utils';\nfunction assertElementExist(element, msg) {\n  if (!element) {\n    if (msg) {\n      throw new QiankunError(msg);\n    }\n    throw new QiankunError('element not existed!');\n  }\n}\nfunction execHooksChain(hooks, app) {\n  var global = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : window;\n  if (hooks.length) {\n    return hooks.reduce(function (chain, hook) {\n      return chain.then(function () {\n        return hook(app, global);\n      });\n    }, Promise.resolve());\n  }\n  return Promise.resolve();\n}\nfunction validateSingularMode(_x, _x2) {\n  return _validateSingularMode.apply(this, arguments);\n}\nfunction _validateSingularMode() {\n  _validateSingularMode = _asyncToGenerator( /*#__PURE__*/_regeneratorRuntime.mark(function _callee(validate, app) {\n    return _regeneratorRuntime.wrap(function _callee$(_context) {\n      while (1) {\n        switch (_context.prev = _context.next) {\n          case 0:\n            return _context.abrupt(\"return\", typeof validate === 'function' ? validate(app) : !!validate);\n          case 1:\n          case \"end\":\n            return _context.stop();\n        }\n      }\n    }, _callee);\n  }));\n  return _validateSingularMode.apply(this, arguments);\n}\nvar supportShadowDOM = !!document.head.attachShadow || !!document.head.createShadowRoot;\nfunction createElement(appContent, strictStyleIsolation, scopedCSS, appInstanceId) {\n  var containerElement = document.createElement('div');\n  containerElement.innerHTML = appContent;\n  // appContent always wrapped with a singular div\n  var appElement = containerElement.firstChild;\n  if (strictStyleIsolation) {\n    if (!supportShadowDOM) {\n      console.warn('[qiankun]: As current browser not support shadow dom, your strictStyleIsolation configuration will be ignored!');\n    } else {\n      var innerHTML = appElement.innerHTML;\n      appElement.innerHTML = '';\n      var shadow;\n      if (appElement.attachShadow) {\n        shadow = appElement.attachShadow({\n          mode: 'open'\n        });\n      } else {\n        // createShadowRoot was proposed in initial spec, which has then been deprecated\n        shadow = appElement.createShadowRoot();\n      }\n      shadow.innerHTML = innerHTML;\n    }\n  }\n  if (scopedCSS) {\n    var attr = appElement.getAttribute(css.QiankunCSSRewriteAttr);\n    if (!attr) {\n      appElement.setAttribute(css.QiankunCSSRewriteAttr, appInstanceId);\n    }\n    var styleNodes = appElement.querySelectorAll('style') || [];\n    _forEach(styleNodes, function (stylesheetElement) {\n      css.process(appElement, stylesheetElement, appInstanceId);\n    });\n  }\n  return appElement;\n}\n/** generate app wrapper dom getter */\nfunction getAppWrapperGetter(appInstanceId, useLegacyRender, strictStyleIsolation, scopedCSS, elementGetter) {\n  return function () {\n    if (useLegacyRender) {\n      if (strictStyleIsolation) throw new QiankunError('strictStyleIsolation can not be used with legacy render!');\n      if (scopedCSS) throw new QiankunError('experimentalStyleIsolation can not be used with legacy render!');\n      var appWrapper = document.getElementById(getWrapperId(appInstanceId));\n      assertElementExist(appWrapper, \"Wrapper element for \".concat(appInstanceId, \" is not existed!\"));\n      return appWrapper;\n    }\n    var element = elementGetter();\n    assertElementExist(element, \"Wrapper element for \".concat(appInstanceId, \" is not existed!\"));\n    if (strictStyleIsolation && supportShadowDOM) {\n      return element.shadowRoot;\n    }\n    return element;\n  };\n}\nvar rawAppendChild = HTMLElement.prototype.appendChild;\nvar rawRemoveChild = HTMLElement.prototype.removeChild;\n/**\n * Get the render function\n * If the legacy render function is provide, used as it, otherwise we will insert the app element to target container by qiankun\n * @param appInstanceId\n * @param appContent\n * @param legacyRender\n */\nfunction getRender(appInstanceId, appContent, legacyRender) {\n  var render = function render(_ref, phase) {\n    var element = _ref.element,\n      loading = _ref.loading,\n      container = _ref.container;\n    if (legacyRender) {\n      if (process.env.NODE_ENV === 'development') {\n        console.error('[qiankun] Custom rendering function is deprecated and will be removed in 3.0, you can use the container element setting instead!');\n      }\n      return legacyRender({\n        loading: loading,\n        appContent: element ? appContent : ''\n      });\n    }\n    var containerElement = getContainer(container);\n    // The container might have be removed after micro app unmounted.\n    // Such as the micro app unmount lifecycle called by a react componentWillUnmount lifecycle, after micro app unmounted, the react component might also be removed\n    if (phase !== 'unmounted') {\n      var errorMsg = function () {\n        switch (phase) {\n          case 'loading':\n          case 'mounting':\n            return \"Target container with \".concat(container, \" not existed while \").concat(appInstanceId, \" \").concat(phase, \"!\");\n          case 'mounted':\n            return \"Target container with \".concat(container, \" not existed after \").concat(appInstanceId, \" \").concat(phase, \"!\");\n          default:\n            return \"Target container with \".concat(container, \" not existed while \").concat(appInstanceId, \" rendering!\");\n        }\n      }();\n      assertElementExist(containerElement, errorMsg);\n    }\n    if (containerElement && !containerElement.contains(element)) {\n      // clear the container\n      while (containerElement.firstChild) {\n        rawRemoveChild.call(containerElement, containerElement.firstChild);\n      }\n      // append the element to container if it exist\n      if (element) {\n        rawAppendChild.call(containerElement, element);\n      }\n    }\n    return undefined;\n  };\n  return render;\n}\nfunction getLifecyclesFromExports(scriptExports, appName, global, globalLatestSetProp) {\n  if (validateExportLifecycle(scriptExports)) {\n    return scriptExports;\n  }\n  // fallback to sandbox latest set property if it had\n  if (globalLatestSetProp) {\n    var lifecycles = global[globalLatestSetProp];\n    if (validateExportLifecycle(lifecycles)) {\n      return lifecycles;\n    }\n  }\n  if (process.env.NODE_ENV === 'development') {\n    console.warn(\"[qiankun] lifecycle not found from \".concat(appName, \" entry exports, fallback to get from window['\").concat(appName, \"']\"));\n  }\n  // fallback to global variable who named with ${appName} while module exports not found\n  var globalVariableExports = global[appName];\n  if (validateExportLifecycle(globalVariableExports)) {\n    return globalVariableExports;\n  }\n  throw new QiankunError(\"You need to export lifecycle functions in \".concat(appName, \" entry\"));\n}\nvar prevAppUnmountedDeferred;\nexport function loadApp(_x3) {\n  return _loadApp.apply(this, arguments);\n}\nfunction _loadApp() {\n  _loadApp = _asyncToGenerator( /*#__PURE__*/_regeneratorRuntime.mark(function _callee17(app) {\n    var _sandboxContainer, _sandboxContainer$ins;\n    var configuration,\n      lifeCycles,\n      entry,\n      appName,\n      appInstanceId,\n      markName,\n      _configuration$singul,\n      singular,\n      _configuration$sandbo,\n      sandbox,\n      excludeAssetFilter,\n      _configuration$global,\n      globalContext,\n      importEntryOpts,\n      _yield$importEntry,\n      template,\n      execScripts,\n      assetPublicPath,\n      appContent,\n      strictStyleIsolation,\n      scopedCSS,\n      initialAppWrapperElement,\n      initialContainer,\n      legacyRender,\n      render,\n      initialAppWrapperGetter,\n      global,\n      mountSandbox,\n      unmountSandbox,\n      useLooseSandbox,\n      speedySandbox,\n      sandboxContainer,\n      _mergeWith,\n      _mergeWith$beforeUnmo,\n      beforeUnmount,\n      _mergeWith$afterUnmou,\n      afterUnmount,\n      _mergeWith$afterMount,\n      afterMount,\n      _mergeWith$beforeMoun,\n      beforeMount,\n      _mergeWith$beforeLoad,\n      beforeLoad,\n      scriptExports,\n      _getLifecyclesFromExp,\n      bootstrap,\n      mount,\n      unmount,\n      update,\n      _getMicroAppStateActi,\n      onGlobalStateChange,\n      setGlobalState,\n      offGlobalStateChange,\n      syncAppWrapperElement2Sandbox,\n      parcelConfigGetter,\n      _args17 = arguments;\n    return _regeneratorRuntime.wrap(function _callee17$(_context17) {\n      while (1) {\n        switch (_context17.prev = _context17.next) {\n          case 0:\n            configuration = _args17.length > 1 && _args17[1] !== undefined ? _args17[1] : {};\n            lifeCycles = _args17.length > 2 ? _args17[2] : undefined;\n            entry = app.entry, appName = app.name;\n            appInstanceId = genAppInstanceIdByName(appName);\n            markName = \"[qiankun] App \".concat(appInstanceId, \" Loading\");\n            if (process.env.NODE_ENV === 'development') {\n              performanceMark(markName);\n            }\n            _configuration$singul = configuration.singular, singular = _configuration$singul === void 0 ? false : _configuration$singul, _configuration$sandbo = configuration.sandbox, sandbox = _configuration$sandbo === void 0 ? true : _configuration$sandbo, excludeAssetFilter = configuration.excludeAssetFilter, _configuration$global = configuration.globalContext, globalContext = _configuration$global === void 0 ? window : _configuration$global, importEntryOpts = _objectWithoutProperties(configuration, _excluded); // get the entry html content and script executor\n            _context17.next = 9;\n            return importEntry(entry, importEntryOpts);\n          case 9:\n            _yield$importEntry = _context17.sent;\n            template = _yield$importEntry.template;\n            execScripts = _yield$importEntry.execScripts;\n            assetPublicPath = _yield$importEntry.assetPublicPath;\n            _context17.next = 15;\n            return validateSingularMode(singular, app);\n          case 15:\n            if (!_context17.sent) {\n              _context17.next = 18;\n              break;\n            }\n            _context17.next = 18;\n            return prevAppUnmountedDeferred && prevAppUnmountedDeferred.promise;\n          case 18:\n            appContent = getDefaultTplWrapper(appInstanceId)(template);\n            strictStyleIsolation = _typeof(sandbox) === 'object' && !!sandbox.strictStyleIsolation;\n            if (process.env.NODE_ENV === 'development' && strictStyleIsolation) {\n              console.warn(\"[qiankun] strictStyleIsolation configuration will be removed in 3.0, pls don't depend on it or use experimentalStyleIsolation instead!\");\n            }\n            scopedCSS = isEnableScopedCSS(sandbox);\n            initialAppWrapperElement = createElement(appContent, strictStyleIsolation, scopedCSS, appInstanceId);\n            initialContainer = 'container' in app ? app.container : undefined;\n            legacyRender = 'render' in app ? app.render : undefined;\n            render = getRender(appInstanceId, appContent, legacyRender); // 第一次加载设置应用可见区域 dom 结构\n            // 确保每次应用加载前容器 dom 结构已经设置完毕\n            render({\n              element: initialAppWrapperElement,\n              loading: true,\n              container: initialContainer\n            }, 'loading');\n            initialAppWrapperGetter = getAppWrapperGetter(appInstanceId, !!legacyRender, strictStyleIsolation, scopedCSS, function () {\n              return initialAppWrapperElement;\n            });\n            global = globalContext;\n            mountSandbox = function mountSandbox() {\n              return Promise.resolve();\n            };\n            unmountSandbox = function unmountSandbox() {\n              return Promise.resolve();\n            };\n            useLooseSandbox = _typeof(sandbox) === 'object' && !!sandbox.loose;\n            speedySandbox = _typeof(sandbox) === 'object' && !!sandbox.speedy;\n            if (sandbox) {\n              sandboxContainer = createSandboxContainer(appInstanceId,\n              // FIXME should use a strict sandbox logic while remount, see https://github.com/umijs/qiankun/issues/518\n              initialAppWrapperGetter, scopedCSS, useLooseSandbox, excludeAssetFilter, global, speedySandbox);\n              // 用沙箱的代理对象作为接下来使用的全局对象\n              global = sandboxContainer.instance.proxy;\n              mountSandbox = sandboxContainer.mount;\n              unmountSandbox = sandboxContainer.unmount;\n            }\n            _mergeWith = _mergeWith2({}, getAddOns(global, assetPublicPath), lifeCycles, function (v1, v2) {\n              return _concat(v1 !== null && v1 !== void 0 ? v1 : [], v2 !== null && v2 !== void 0 ? v2 : []);\n            }), _mergeWith$beforeUnmo = _mergeWith.beforeUnmount, beforeUnmount = _mergeWith$beforeUnmo === void 0 ? [] : _mergeWith$beforeUnmo, _mergeWith$afterUnmou = _mergeWith.afterUnmount, afterUnmount = _mergeWith$afterUnmou === void 0 ? [] : _mergeWith$afterUnmou, _mergeWith$afterMount = _mergeWith.afterMount, afterMount = _mergeWith$afterMount === void 0 ? [] : _mergeWith$afterMount, _mergeWith$beforeMoun = _mergeWith.beforeMount, beforeMount = _mergeWith$beforeMoun === void 0 ? [] : _mergeWith$beforeMoun, _mergeWith$beforeLoad = _mergeWith.beforeLoad, beforeLoad = _mergeWith$beforeLoad === void 0 ? [] : _mergeWith$beforeLoad;\n            _context17.next = 37;\n            return execHooksChain(toArray(beforeLoad), app, global);\n          case 37:\n            _context17.next = 39;\n            return execScripts(global, sandbox && !useLooseSandbox, {\n              scopedGlobalVariables: speedySandbox ? trustedGlobals : []\n            });\n          case 39:\n            scriptExports = _context17.sent;\n            _getLifecyclesFromExp = getLifecyclesFromExports(scriptExports, appName, global, (_sandboxContainer = sandboxContainer) === null || _sandboxContainer === void 0 ? void 0 : (_sandboxContainer$ins = _sandboxContainer.instance) === null || _sandboxContainer$ins === void 0 ? void 0 : _sandboxContainer$ins.latestSetProp), bootstrap = _getLifecyclesFromExp.bootstrap, mount = _getLifecyclesFromExp.mount, unmount = _getLifecyclesFromExp.unmount, update = _getLifecyclesFromExp.update;\n            _getMicroAppStateActi = getMicroAppStateActions(appInstanceId), onGlobalStateChange = _getMicroAppStateActi.onGlobalStateChange, setGlobalState = _getMicroAppStateActi.setGlobalState, offGlobalStateChange = _getMicroAppStateActi.offGlobalStateChange; // FIXME temporary way\n            syncAppWrapperElement2Sandbox = function syncAppWrapperElement2Sandbox(element) {\n              return initialAppWrapperElement = element;\n            };\n            parcelConfigGetter = function parcelConfigGetter() {\n              var remountContainer = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : initialContainer;\n              var appWrapperElement;\n              var appWrapperGetter;\n              var parcelConfig = {\n                name: appInstanceId,\n                bootstrap: bootstrap,\n                mount: [/*#__PURE__*/_asyncToGenerator( /*#__PURE__*/_regeneratorRuntime.mark(function _callee2() {\n                  var marks;\n                  return _regeneratorRuntime.wrap(function _callee2$(_context2) {\n                    while (1) {\n                      switch (_context2.prev = _context2.next) {\n                        case 0:\n                          if (process.env.NODE_ENV === 'development') {\n                            marks = performanceGetEntriesByName(markName, 'mark'); // mark length is zero means the app is remounting\n                            if (marks && !marks.length) {\n                              performanceMark(markName);\n                            }\n                          }\n                        case 1:\n                        case \"end\":\n                          return _context2.stop();\n                      }\n                    }\n                  }, _callee2);\n                })), /*#__PURE__*/_asyncToGenerator( /*#__PURE__*/_regeneratorRuntime.mark(function _callee3() {\n                  return _regeneratorRuntime.wrap(function _callee3$(_context3) {\n                    while (1) {\n                      switch (_context3.prev = _context3.next) {\n                        case 0:\n                          _context3.next = 2;\n                          return validateSingularMode(singular, app);\n                        case 2:\n                          _context3.t0 = _context3.sent;\n                          if (!_context3.t0) {\n                            _context3.next = 5;\n                            break;\n                          }\n                          _context3.t0 = prevAppUnmountedDeferred;\n                        case 5:\n                          if (!_context3.t0) {\n                            _context3.next = 7;\n                            break;\n                          }\n                          return _context3.abrupt(\"return\", prevAppUnmountedDeferred.promise);\n                        case 7:\n                          return _context3.abrupt(\"return\", undefined);\n                        case 8:\n                        case \"end\":\n                          return _context3.stop();\n                      }\n                    }\n                  }, _callee3);\n                })),\n                /*#__PURE__*/\n                // initial wrapper element before app mount/remount\n                _asyncToGenerator( /*#__PURE__*/_regeneratorRuntime.mark(function _callee4() {\n                  return _regeneratorRuntime.wrap(function _callee4$(_context4) {\n                    while (1) {\n                      switch (_context4.prev = _context4.next) {\n                        case 0:\n                          appWrapperElement = initialAppWrapperElement;\n                          appWrapperGetter = getAppWrapperGetter(appInstanceId, !!legacyRender, strictStyleIsolation, scopedCSS, function () {\n                            return appWrapperElement;\n                          });\n                        case 2:\n                        case \"end\":\n                          return _context4.stop();\n                      }\n                    }\n                  }, _callee4);\n                })),\n                /*#__PURE__*/\n                // 添加 mount hook, 确保每次应用加载前容器 dom 结构已经设置完毕\n                _asyncToGenerator( /*#__PURE__*/_regeneratorRuntime.mark(function _callee5() {\n                  var useNewContainer;\n                  return _regeneratorRuntime.wrap(function _callee5$(_context5) {\n                    while (1) {\n                      switch (_context5.prev = _context5.next) {\n                        case 0:\n                          useNewContainer = remountContainer !== initialContainer;\n                          if (useNewContainer || !appWrapperElement) {\n                            // element will be destroyed after unmounted, we need to recreate it if it not exist\n                            // or we try to remount into a new container\n                            appWrapperElement = createElement(appContent, strictStyleIsolation, scopedCSS, appInstanceId);\n                            syncAppWrapperElement2Sandbox(appWrapperElement);\n                          }\n                          render({\n                            element: appWrapperElement,\n                            loading: true,\n                            container: remountContainer\n                          }, 'mounting');\n                        case 3:\n                        case \"end\":\n                          return _context5.stop();\n                      }\n                    }\n                  }, _callee5);\n                })), mountSandbox,\n                /*#__PURE__*/\n                // exec the chain after rendering to keep the behavior with beforeLoad\n                _asyncToGenerator( /*#__PURE__*/_regeneratorRuntime.mark(function _callee6() {\n                  return _regeneratorRuntime.wrap(function _callee6$(_context6) {\n                    while (1) {\n                      switch (_context6.prev = _context6.next) {\n                        case 0:\n                          return _context6.abrupt(\"return\", execHooksChain(toArray(beforeMount), app, global));\n                        case 1:\n                        case \"end\":\n                          return _context6.stop();\n                      }\n                    }\n                  }, _callee6);\n                })), /*#__PURE__*/function () {\n                  var _ref7 = _asyncToGenerator( /*#__PURE__*/_regeneratorRuntime.mark(function _callee7(props) {\n                    return _regeneratorRuntime.wrap(function _callee7$(_context7) {\n                      while (1) {\n                        switch (_context7.prev = _context7.next) {\n                          case 0:\n                            return _context7.abrupt(\"return\", mount(_objectSpread(_objectSpread({}, props), {}, {\n                              container: appWrapperGetter(),\n                              setGlobalState: setGlobalState,\n                              onGlobalStateChange: onGlobalStateChange\n                            })));\n                          case 1:\n                          case \"end\":\n                            return _context7.stop();\n                        }\n                      }\n                    }, _callee7);\n                  }));\n                  return function (_x4) {\n                    return _ref7.apply(this, arguments);\n                  };\n                }(),\n                /*#__PURE__*/\n                // finish loading after app mounted\n                _asyncToGenerator( /*#__PURE__*/_regeneratorRuntime.mark(function _callee8() {\n                  return _regeneratorRuntime.wrap(function _callee8$(_context8) {\n                    while (1) {\n                      switch (_context8.prev = _context8.next) {\n                        case 0:\n                          return _context8.abrupt(\"return\", render({\n                            element: appWrapperElement,\n                            loading: false,\n                            container: remountContainer\n                          }, 'mounted'));\n                        case 1:\n                        case \"end\":\n                          return _context8.stop();\n                      }\n                    }\n                  }, _callee8);\n                })), /*#__PURE__*/_asyncToGenerator( /*#__PURE__*/_regeneratorRuntime.mark(function _callee9() {\n                  return _regeneratorRuntime.wrap(function _callee9$(_context9) {\n                    while (1) {\n                      switch (_context9.prev = _context9.next) {\n                        case 0:\n                          return _context9.abrupt(\"return\", execHooksChain(toArray(afterMount), app, global));\n                        case 1:\n                        case \"end\":\n                          return _context9.stop();\n                      }\n                    }\n                  }, _callee9);\n                })),\n                /*#__PURE__*/\n                // initialize the unmount defer after app mounted and resolve the defer after it unmounted\n                _asyncToGenerator( /*#__PURE__*/_regeneratorRuntime.mark(function _callee10() {\n                  return _regeneratorRuntime.wrap(function _callee10$(_context10) {\n                    while (1) {\n                      switch (_context10.prev = _context10.next) {\n                        case 0:\n                          _context10.next = 2;\n                          return validateSingularMode(singular, app);\n                        case 2:\n                          if (!_context10.sent) {\n                            _context10.next = 4;\n                            break;\n                          }\n                          prevAppUnmountedDeferred = new Deferred();\n                        case 4:\n                        case \"end\":\n                          return _context10.stop();\n                      }\n                    }\n                  }, _callee10);\n                })), /*#__PURE__*/_asyncToGenerator( /*#__PURE__*/_regeneratorRuntime.mark(function _callee11() {\n                  var measureName;\n                  return _regeneratorRuntime.wrap(function _callee11$(_context11) {\n                    while (1) {\n                      switch (_context11.prev = _context11.next) {\n                        case 0:\n                          if (process.env.NODE_ENV === 'development') {\n                            measureName = \"[qiankun] App \".concat(appInstanceId, \" Loading Consuming\");\n                            performanceMeasure(measureName, markName);\n                          }\n                        case 1:\n                        case \"end\":\n                          return _context11.stop();\n                      }\n                    }\n                  }, _callee11);\n                }))],\n                unmount: [/*#__PURE__*/_asyncToGenerator( /*#__PURE__*/_regeneratorRuntime.mark(function _callee12() {\n                  return _regeneratorRuntime.wrap(function _callee12$(_context12) {\n                    while (1) {\n                      switch (_context12.prev = _context12.next) {\n                        case 0:\n                          return _context12.abrupt(\"return\", execHooksChain(toArray(beforeUnmount), app, global));\n                        case 1:\n                        case \"end\":\n                          return _context12.stop();\n                      }\n                    }\n                  }, _callee12);\n                })), /*#__PURE__*/function () {\n                  var _ref13 = _asyncToGenerator( /*#__PURE__*/_regeneratorRuntime.mark(function _callee13(props) {\n                    return _regeneratorRuntime.wrap(function _callee13$(_context13) {\n                      while (1) {\n                        switch (_context13.prev = _context13.next) {\n                          case 0:\n                            return _context13.abrupt(\"return\", unmount(_objectSpread(_objectSpread({}, props), {}, {\n                              container: appWrapperGetter()\n                            })));\n                          case 1:\n                          case \"end\":\n                            return _context13.stop();\n                        }\n                      }\n                    }, _callee13);\n                  }));\n                  return function (_x5) {\n                    return _ref13.apply(this, arguments);\n                  };\n                }(), unmountSandbox, /*#__PURE__*/_asyncToGenerator( /*#__PURE__*/_regeneratorRuntime.mark(function _callee14() {\n                  return _regeneratorRuntime.wrap(function _callee14$(_context14) {\n                    while (1) {\n                      switch (_context14.prev = _context14.next) {\n                        case 0:\n                          return _context14.abrupt(\"return\", execHooksChain(toArray(afterUnmount), app, global));\n                        case 1:\n                        case \"end\":\n                          return _context14.stop();\n                      }\n                    }\n                  }, _callee14);\n                })), /*#__PURE__*/_asyncToGenerator( /*#__PURE__*/_regeneratorRuntime.mark(function _callee15() {\n                  return _regeneratorRuntime.wrap(function _callee15$(_context15) {\n                    while (1) {\n                      switch (_context15.prev = _context15.next) {\n                        case 0:\n                          render({\n                            element: null,\n                            loading: false,\n                            container: remountContainer\n                          }, 'unmounted');\n                          offGlobalStateChange(appInstanceId);\n                          // for gc\n                          appWrapperElement = null;\n                          syncAppWrapperElement2Sandbox(appWrapperElement);\n                        case 4:\n                        case \"end\":\n                          return _context15.stop();\n                      }\n                    }\n                  }, _callee15);\n                })), /*#__PURE__*/_asyncToGenerator( /*#__PURE__*/_regeneratorRuntime.mark(function _callee16() {\n                  return _regeneratorRuntime.wrap(function _callee16$(_context16) {\n                    while (1) {\n                      switch (_context16.prev = _context16.next) {\n                        case 0:\n                          _context16.next = 2;\n                          return validateSingularMode(singular, app);\n                        case 2:\n                          _context16.t0 = _context16.sent;\n                          if (!_context16.t0) {\n                            _context16.next = 5;\n                            break;\n                          }\n                          _context16.t0 = prevAppUnmountedDeferred;\n                        case 5:\n                          if (!_context16.t0) {\n                            _context16.next = 7;\n                            break;\n                          }\n                          prevAppUnmountedDeferred.resolve();\n                        case 7:\n                        case \"end\":\n                          return _context16.stop();\n                      }\n                    }\n                  }, _callee16);\n                }))]\n              };\n              if (typeof update === 'function') {\n                parcelConfig.update = update;\n              }\n              return parcelConfig;\n            };\n            return _context17.abrupt(\"return\", parcelConfigGetter);\n          case 45:\n          case \"end\":\n            return _context17.stop();\n        }\n      }\n    }, _callee17);\n  }));\n  return _loadApp.apply(this, arguments);\n}"], "mappings": ";;;;AAAA,OAAOA,aAAa,MAAM,0CAA0C;AACpE,OAAOC,OAAO,MAAM,eAAe;AACnC,OAAOC,WAAW,MAAM,kBAAkB;AAC1C,OAAOC,OAAO,MAAM,mCAAmC;AACvD,OAAOC,wBAAwB,MAAM,oDAAoD;AACzF,OAAOC,QAAQ,MAAM,gBAAgB;AACrC,OAAOC,iBAAiB,MAAM,6CAA6C;AAC3E,IAAIC,SAAS,GAAG,CAAC,UAAU,EAAE,SAAS,EAAE,oBAAoB,EAAE,eAAe,CAAC;AAC9E,OAAOC,mBAAmB,MAAM,4BAA4B;AAC5D;AACA;AACA;AACA;AACA,SAASC,WAAW,QAAQ,mBAAmB;AAC/C,OAAOC,SAAS,MAAM,UAAU;AAChC,SAASC,YAAY,QAAQ,SAAS;AACtC,SAASC,uBAAuB,QAAQ,eAAe;AACvD,SAASC,sBAAsB,EAAEC,GAAG,QAAQ,WAAW;AACvD,SAASC,cAAc,QAAQ,kBAAkB;AACjD,SAASC,QAAQ,EAAEC,sBAAsB,EAAEC,YAAY,EAAEC,oBAAoB,EAAEC,YAAY,EAAEC,iBAAiB,EAAEC,2BAA2B,EAAEC,eAAe,EAAEC,kBAAkB,EAAEC,OAAO,EAAEC,uBAAuB,QAAQ,SAAS;AACnO,SAASC,kBAAkB,CAACC,OAAO,EAAEC,GAAG,EAAE;EACxC,IAAI,CAACD,OAAO,EAAE;IACZ,IAAIC,GAAG,EAAE;MACP,MAAM,IAAIlB,YAAY,CAACkB,GAAG,CAAC;IAC7B;IACA,MAAM,IAAIlB,YAAY,CAAC,sBAAsB,CAAC;EAChD;AACF;AACA,SAASmB,cAAc,CAACC,KAAK,EAAEC,GAAG,EAAE;EAClC,IAAIC,MAAM,GAAGC,SAAS,CAACC,MAAM,GAAG,CAAC,IAAID,SAAS,CAAC,CAAC,CAAC,KAAKE,SAAS,GAAGF,SAAS,CAAC,CAAC,CAAC,GAAGG,MAAM;EACvF,IAAIN,KAAK,CAACI,MAAM,EAAE;IAChB,OAAOJ,KAAK,CAACO,MAAM,CAAC,UAAUC,KAAK,EAAEC,IAAI,EAAE;MACzC,OAAOD,KAAK,CAACE,IAAI,CAAC,YAAY;QAC5B,OAAOD,IAAI,CAACR,GAAG,EAAEC,MAAM,CAAC;MAC1B,CAAC,CAAC;IACJ,CAAC,EAAES,OAAO,CAACC,OAAO,EAAE,CAAC;EACvB;EACA,OAAOD,OAAO,CAACC,OAAO,EAAE;AAC1B;AACA,SAASC,oBAAoB,CAACC,EAAE,EAAEC,GAAG,EAAE;EACrC,OAAOC,qBAAqB,CAACC,KAAK,CAAC,IAAI,EAAEd,SAAS,CAAC;AACrD;AACA,SAASa,qBAAqB,GAAG;EAC/BA,qBAAqB,GAAGzC,iBAAiB,EAAE,aAAaE,mBAAmB,CAACyC,IAAI,CAAC,SAASC,OAAO,CAACC,QAAQ,EAAEnB,GAAG,EAAE;IAC/G,OAAOxB,mBAAmB,CAAC4C,IAAI,CAAC,SAASC,QAAQ,CAACC,QAAQ,EAAE;MAC1D,OAAO,CAAC,EAAE;QACR,QAAQA,QAAQ,CAACC,IAAI,GAAGD,QAAQ,CAACE,IAAI;UACnC,KAAK,CAAC;YACJ,OAAOF,QAAQ,CAACG,MAAM,CAAC,QAAQ,EAAE,OAAON,QAAQ,KAAK,UAAU,GAAGA,QAAQ,CAACnB,GAAG,CAAC,GAAG,CAAC,CAACmB,QAAQ,CAAC;UAC/F,KAAK,CAAC;UACN,KAAK,KAAK;YACR,OAAOG,QAAQ,CAACI,IAAI,EAAE;QAAC;MAE7B;IACF,CAAC,EAAER,OAAO,CAAC;EACb,CAAC,CAAC,CAAC;EACH,OAAOH,qBAAqB,CAACC,KAAK,CAAC,IAAI,EAAEd,SAAS,CAAC;AACrD;AACA,IAAIyB,gBAAgB,GAAG,CAAC,CAACC,QAAQ,CAACC,IAAI,CAACC,YAAY,IAAI,CAAC,CAACF,QAAQ,CAACC,IAAI,CAACE,gBAAgB;AACvF,SAASC,aAAa,CAACC,UAAU,EAAEC,oBAAoB,EAAEC,SAAS,EAAEC,aAAa,EAAE;EACjF,IAAIC,gBAAgB,GAAGT,QAAQ,CAACI,aAAa,CAAC,KAAK,CAAC;EACpDK,gBAAgB,CAACC,SAAS,GAAGL,UAAU;EACvC;EACA,IAAIM,UAAU,GAAGF,gBAAgB,CAACG,UAAU;EAC5C,IAAIN,oBAAoB,EAAE;IACxB,IAAI,CAACP,gBAAgB,EAAE;MACrBc,OAAO,CAACC,IAAI,CAAC,gHAAgH,CAAC;IAChI,CAAC,MAAM;MACL,IAAIJ,SAAS,GAAGC,UAAU,CAACD,SAAS;MACpCC,UAAU,CAACD,SAAS,GAAG,EAAE;MACzB,IAAIK,MAAM;MACV,IAAIJ,UAAU,CAACT,YAAY,EAAE;QAC3Ba,MAAM,GAAGJ,UAAU,CAACT,YAAY,CAAC;UAC/Bc,IAAI,EAAE;QACR,CAAC,CAAC;MACJ,CAAC,MAAM;QACL;QACAD,MAAM,GAAGJ,UAAU,CAACR,gBAAgB,EAAE;MACxC;MACAY,MAAM,CAACL,SAAS,GAAGA,SAAS;IAC9B;EACF;EACA,IAAIH,SAAS,EAAE;IACb,IAAIU,IAAI,GAAGN,UAAU,CAACO,YAAY,CAAChE,GAAG,CAACiE,qBAAqB,CAAC;IAC7D,IAAI,CAACF,IAAI,EAAE;MACTN,UAAU,CAACS,YAAY,CAAClE,GAAG,CAACiE,qBAAqB,EAAEX,aAAa,CAAC;IACnE;IACA,IAAIa,UAAU,GAAGV,UAAU,CAACW,gBAAgB,CAAC,OAAO,CAAC,IAAI,EAAE;IAC3D7E,QAAQ,CAAC4E,UAAU,EAAE,UAAUE,iBAAiB,EAAE;MAChDrE,GAAG,CAACsE,OAAO,CAACb,UAAU,EAAEY,iBAAiB,EAAEf,aAAa,CAAC;IAC3D,CAAC,CAAC;EACJ;EACA,OAAOG,UAAU;AACnB;AACA;AACA,SAASc,mBAAmB,CAACjB,aAAa,EAAEkB,eAAe,EAAEpB,oBAAoB,EAAEC,SAAS,EAAEoB,aAAa,EAAE;EAC3G,OAAO,YAAY;IACjB,IAAID,eAAe,EAAE;MACnB,IAAIpB,oBAAoB,EAAE,MAAM,IAAIvD,YAAY,CAAC,0DAA0D,CAAC;MAC5G,IAAIwD,SAAS,EAAE,MAAM,IAAIxD,YAAY,CAAC,gEAAgE,CAAC;MACvG,IAAI6E,UAAU,GAAG5B,QAAQ,CAAC6B,cAAc,CAACrE,YAAY,CAACgD,aAAa,CAAC,CAAC;MACrEzC,kBAAkB,CAAC6D,UAAU,EAAE,sBAAsB,CAACE,MAAM,CAACtB,aAAa,EAAE,kBAAkB,CAAC,CAAC;MAChG,OAAOoB,UAAU;IACnB;IACA,IAAI5D,OAAO,GAAG2D,aAAa,EAAE;IAC7B5D,kBAAkB,CAACC,OAAO,EAAE,sBAAsB,CAAC8D,MAAM,CAACtB,aAAa,EAAE,kBAAkB,CAAC,CAAC;IAC7F,IAAIF,oBAAoB,IAAIP,gBAAgB,EAAE;MAC5C,OAAO/B,OAAO,CAAC+D,UAAU;IAC3B;IACA,OAAO/D,OAAO;EAChB,CAAC;AACH;AACA,IAAIgE,cAAc,GAAGC,WAAW,CAACC,SAAS,CAACC,WAAW;AACtD,IAAIC,cAAc,GAAGH,WAAW,CAACC,SAAS,CAACG,WAAW;AACtD;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,SAAS,CAAC9B,aAAa,EAAEH,UAAU,EAAEkC,YAAY,EAAE;EAC1D,IAAIC,MAAM,GAAG,SAASA,MAAM,CAACC,IAAI,EAAEC,KAAK,EAAE;IACxC,IAAI1E,OAAO,GAAGyE,IAAI,CAACzE,OAAO;MACxB2E,OAAO,GAAGF,IAAI,CAACE,OAAO;MACtBC,SAAS,GAAGH,IAAI,CAACG,SAAS;IAC5B,IAAIL,YAAY,EAAE;MAChB,IAAIf,OAAO,CAACqB,GAAG,CAACC,QAAQ,KAAK,aAAa,EAAE;QAC1CjC,OAAO,CAACkC,KAAK,CAAC,kIAAkI,CAAC;MACnJ;MACA,OAAOR,YAAY,CAAC;QAClBI,OAAO,EAAEA,OAAO;QAChBtC,UAAU,EAAErC,OAAO,GAAGqC,UAAU,GAAG;MACrC,CAAC,CAAC;IACJ;IACA,IAAII,gBAAgB,GAAGnD,YAAY,CAACsF,SAAS,CAAC;IAC9C;IACA;IACA,IAAIF,KAAK,KAAK,WAAW,EAAE;MACzB,IAAIM,QAAQ,GAAG,YAAY;QACzB,QAAQN,KAAK;UACX,KAAK,SAAS;UACd,KAAK,UAAU;YACb,OAAO,wBAAwB,CAACZ,MAAM,CAACc,SAAS,EAAE,qBAAqB,CAAC,CAACd,MAAM,CAACtB,aAAa,EAAE,GAAG,CAAC,CAACsB,MAAM,CAACY,KAAK,EAAE,GAAG,CAAC;UACxH,KAAK,SAAS;YACZ,OAAO,wBAAwB,CAACZ,MAAM,CAACc,SAAS,EAAE,qBAAqB,CAAC,CAACd,MAAM,CAACtB,aAAa,EAAE,GAAG,CAAC,CAACsB,MAAM,CAACY,KAAK,EAAE,GAAG,CAAC;UACxH;YACE,OAAO,wBAAwB,CAACZ,MAAM,CAACc,SAAS,EAAE,qBAAqB,CAAC,CAACd,MAAM,CAACtB,aAAa,EAAE,aAAa,CAAC;QAAC;MAEpH,CAAC,EAAE;MACHzC,kBAAkB,CAAC0C,gBAAgB,EAAEuC,QAAQ,CAAC;IAChD;IACA,IAAIvC,gBAAgB,IAAI,CAACA,gBAAgB,CAACwC,QAAQ,CAACjF,OAAO,CAAC,EAAE;MAC3D;MACA,OAAOyC,gBAAgB,CAACG,UAAU,EAAE;QAClCwB,cAAc,CAACc,IAAI,CAACzC,gBAAgB,EAAEA,gBAAgB,CAACG,UAAU,CAAC;MACpE;MACA;MACA,IAAI5C,OAAO,EAAE;QACXgE,cAAc,CAACkB,IAAI,CAACzC,gBAAgB,EAAEzC,OAAO,CAAC;MAChD;IACF;IACA,OAAOQ,SAAS;EAClB,CAAC;EACD,OAAOgE,MAAM;AACf;AACA,SAASW,wBAAwB,CAACC,aAAa,EAAEC,OAAO,EAAEhF,MAAM,EAAEiF,mBAAmB,EAAE;EACrF,IAAIxF,uBAAuB,CAACsF,aAAa,CAAC,EAAE;IAC1C,OAAOA,aAAa;EACtB;EACA;EACA,IAAIE,mBAAmB,EAAE;IACvB,IAAIC,UAAU,GAAGlF,MAAM,CAACiF,mBAAmB,CAAC;IAC5C,IAAIxF,uBAAuB,CAACyF,UAAU,CAAC,EAAE;MACvC,OAAOA,UAAU;IACnB;EACF;EACA,IAAI/B,OAAO,CAACqB,GAAG,CAACC,QAAQ,KAAK,aAAa,EAAE;IAC1CjC,OAAO,CAACC,IAAI,CAAC,qCAAqC,CAACgB,MAAM,CAACuB,OAAO,EAAE,+CAA+C,CAAC,CAACvB,MAAM,CAACuB,OAAO,EAAE,IAAI,CAAC,CAAC;EAC5I;EACA;EACA,IAAIG,qBAAqB,GAAGnF,MAAM,CAACgF,OAAO,CAAC;EAC3C,IAAIvF,uBAAuB,CAAC0F,qBAAqB,CAAC,EAAE;IAClD,OAAOA,qBAAqB;EAC9B;EACA,MAAM,IAAIzG,YAAY,CAAC,4CAA4C,CAAC+E,MAAM,CAACuB,OAAO,EAAE,QAAQ,CAAC,CAAC;AAChG;AACA,IAAII,wBAAwB;AAC5B,OAAO,SAASC,OAAO,CAACC,GAAG,EAAE;EAC3B,OAAOC,QAAQ,CAACxE,KAAK,CAAC,IAAI,EAAEd,SAAS,CAAC;AACxC;AACA,SAASsF,QAAQ,GAAG;EAClBA,QAAQ,GAAGlH,iBAAiB,EAAE,aAAaE,mBAAmB,CAACyC,IAAI,CAAC,SAASwE,SAAS,CAACzF,GAAG,EAAE;IAC1F,IAAI0F,iBAAiB,EAAEC,qBAAqB;IAC5C,IAAIC,aAAa;MACfC,UAAU;MACVC,KAAK;MACLb,OAAO;MACP7C,aAAa;MACb2D,QAAQ;MACRC,qBAAqB;MACrBC,QAAQ;MACRC,qBAAqB;MACrBC,OAAO;MACPC,kBAAkB;MAClBC,qBAAqB;MACrBC,aAAa;MACbC,eAAe;MACfC,kBAAkB;MAClBC,QAAQ;MACRC,WAAW;MACXC,eAAe;MACf1E,UAAU;MACVC,oBAAoB;MACpBC,SAAS;MACTyE,wBAAwB;MACxBC,gBAAgB;MAChB1C,YAAY;MACZC,MAAM;MACN0C,uBAAuB;MACvB7G,MAAM;MACN8G,YAAY;MACZC,cAAc;MACdC,eAAe;MACfC,aAAa;MACbC,gBAAgB;MAChBC,UAAU;MACVC,qBAAqB;MACrBC,aAAa;MACbC,qBAAqB;MACrBC,YAAY;MACZC,qBAAqB;MACrBC,UAAU;MACVC,qBAAqB;MACrBC,WAAW;MACXC,qBAAqB;MACrBC,UAAU;MACV9C,aAAa;MACb+C,qBAAqB;MACrBC,SAAS;MACTC,KAAK;MACLC,OAAO;MACPC,MAAM;MACNC,qBAAqB;MACrBC,mBAAmB;MACnBC,cAAc;MACdC,oBAAoB;MACpBC,6BAA6B;MAC7BC,kBAAkB;MAClBC,OAAO,GAAGxI,SAAS;IACrB,OAAO1B,mBAAmB,CAAC4C,IAAI,CAAC,SAASuH,UAAU,CAACC,UAAU,EAAE;MAC9D,OAAO,CAAC,EAAE;QACR,QAAQA,UAAU,CAACrH,IAAI,GAAGqH,UAAU,CAACpH,IAAI;UACvC,KAAK,CAAC;YACJoE,aAAa,GAAG8C,OAAO,CAACvI,MAAM,GAAG,CAAC,IAAIuI,OAAO,CAAC,CAAC,CAAC,KAAKtI,SAAS,GAAGsI,OAAO,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;YAChF7C,UAAU,GAAG6C,OAAO,CAACvI,MAAM,GAAG,CAAC,GAAGuI,OAAO,CAAC,CAAC,CAAC,GAAGtI,SAAS;YACxD0F,KAAK,GAAG9F,GAAG,CAAC8F,KAAK,EAAEb,OAAO,GAAGjF,GAAG,CAAC6I,IAAI;YACrCzG,aAAa,GAAGnD,sBAAsB,CAACgG,OAAO,CAAC;YAC/Cc,QAAQ,GAAG,gBAAgB,CAACrC,MAAM,CAACtB,aAAa,EAAE,UAAU,CAAC;YAC7D,IAAIgB,OAAO,CAACqB,GAAG,CAACC,QAAQ,KAAK,aAAa,EAAE;cAC1CnF,eAAe,CAACwG,QAAQ,CAAC;YAC3B;YACAC,qBAAqB,GAAGJ,aAAa,CAACK,QAAQ,EAAEA,QAAQ,GAAGD,qBAAqB,KAAK,KAAK,CAAC,GAAG,KAAK,GAAGA,qBAAqB,EAAEE,qBAAqB,GAAGN,aAAa,CAACO,OAAO,EAAEA,OAAO,GAAGD,qBAAqB,KAAK,KAAK,CAAC,GAAG,IAAI,GAAGA,qBAAqB,EAAEE,kBAAkB,GAAGR,aAAa,CAACQ,kBAAkB,EAAEC,qBAAqB,GAAGT,aAAa,CAACU,aAAa,EAAEA,aAAa,GAAGD,qBAAqB,KAAK,KAAK,CAAC,GAAGhG,MAAM,GAAGgG,qBAAqB,EAAEE,eAAe,GAAGnI,wBAAwB,CAACwH,aAAa,EAAErH,SAAS,CAAC,CAAC,CAAC;YAC5fqK,UAAU,CAACpH,IAAI,GAAG,CAAC;YACnB,OAAO/C,WAAW,CAACqH,KAAK,EAAES,eAAe,CAAC;UAC5C,KAAK,CAAC;YACJC,kBAAkB,GAAGoC,UAAU,CAACE,IAAI;YACpCrC,QAAQ,GAAGD,kBAAkB,CAACC,QAAQ;YACtCC,WAAW,GAAGF,kBAAkB,CAACE,WAAW;YAC5CC,eAAe,GAAGH,kBAAkB,CAACG,eAAe;YACpDiC,UAAU,CAACpH,IAAI,GAAG,EAAE;YACpB,OAAOZ,oBAAoB,CAACqF,QAAQ,EAAEjG,GAAG,CAAC;UAC5C,KAAK,EAAE;YACL,IAAI,CAAC4I,UAAU,CAACE,IAAI,EAAE;cACpBF,UAAU,CAACpH,IAAI,GAAG,EAAE;cACpB;YACF;YACAoH,UAAU,CAACpH,IAAI,GAAG,EAAE;YACpB,OAAO6D,wBAAwB,IAAIA,wBAAwB,CAAC0D,OAAO;UACrE,KAAK,EAAE;YACL9G,UAAU,GAAG9C,oBAAoB,CAACiD,aAAa,CAAC,CAACqE,QAAQ,CAAC;YAC1DvE,oBAAoB,GAAG/D,OAAO,CAACgI,OAAO,CAAC,KAAK,QAAQ,IAAI,CAAC,CAACA,OAAO,CAACjE,oBAAoB;YACtF,IAAIkB,OAAO,CAACqB,GAAG,CAACC,QAAQ,KAAK,aAAa,IAAIxC,oBAAoB,EAAE;cAClEO,OAAO,CAACC,IAAI,CAAC,wIAAwI,CAAC;YACxJ;YACAP,SAAS,GAAG9C,iBAAiB,CAAC8G,OAAO,CAAC;YACtCS,wBAAwB,GAAG5E,aAAa,CAACC,UAAU,EAAEC,oBAAoB,EAAEC,SAAS,EAAEC,aAAa,CAAC;YACpGyE,gBAAgB,GAAG,WAAW,IAAI7G,GAAG,GAAGA,GAAG,CAACwE,SAAS,GAAGpE,SAAS;YACjE+D,YAAY,GAAG,QAAQ,IAAInE,GAAG,GAAGA,GAAG,CAACoE,MAAM,GAAGhE,SAAS;YACvDgE,MAAM,GAAGF,SAAS,CAAC9B,aAAa,EAAEH,UAAU,EAAEkC,YAAY,CAAC,CAAC,CAAC;YAC7D;YACAC,MAAM,CAAC;cACLxE,OAAO,EAAEgH,wBAAwB;cACjCrC,OAAO,EAAE,IAAI;cACbC,SAAS,EAAEqC;YACb,CAAC,EAAE,SAAS,CAAC;YACbC,uBAAuB,GAAGzD,mBAAmB,CAACjB,aAAa,EAAE,CAAC,CAAC+B,YAAY,EAAEjC,oBAAoB,EAAEC,SAAS,EAAE,YAAY;cACxH,OAAOyE,wBAAwB;YACjC,CAAC,CAAC;YACF3G,MAAM,GAAGqG,aAAa;YACtBS,YAAY,GAAG,SAASA,YAAY,GAAG;cACrC,OAAOrG,OAAO,CAACC,OAAO,EAAE;YAC1B,CAAC;YACDqG,cAAc,GAAG,SAASA,cAAc,GAAG;cACzC,OAAOtG,OAAO,CAACC,OAAO,EAAE;YAC1B,CAAC;YACDsG,eAAe,GAAG9I,OAAO,CAACgI,OAAO,CAAC,KAAK,QAAQ,IAAI,CAAC,CAACA,OAAO,CAAC6C,KAAK;YAClE9B,aAAa,GAAG/I,OAAO,CAACgI,OAAO,CAAC,KAAK,QAAQ,IAAI,CAAC,CAACA,OAAO,CAAC8C,MAAM;YACjE,IAAI9C,OAAO,EAAE;cACXgB,gBAAgB,GAAGtI,sBAAsB,CAACuD,aAAa;cACvD;cACA0E,uBAAuB,EAAE3E,SAAS,EAAE8E,eAAe,EAAEb,kBAAkB,EAAEnG,MAAM,EAAEiH,aAAa,CAAC;cAC/F;cACAjH,MAAM,GAAGkH,gBAAgB,CAAC+B,QAAQ,CAACC,KAAK;cACxCpC,YAAY,GAAGI,gBAAgB,CAACc,KAAK;cACrCjB,cAAc,GAAGG,gBAAgB,CAACe,OAAO;YAC3C;YACAd,UAAU,GAAGlJ,WAAW,CAAC,CAAC,CAAC,EAAEQ,SAAS,CAACuB,MAAM,EAAE0G,eAAe,CAAC,EAAEd,UAAU,EAAE,UAAUuD,EAAE,EAAEC,EAAE,EAAE;cAC7F,OAAOpL,OAAO,CAACmL,EAAE,KAAK,IAAI,IAAIA,EAAE,KAAK,KAAK,CAAC,GAAGA,EAAE,GAAG,EAAE,EAAEC,EAAE,KAAK,IAAI,IAAIA,EAAE,KAAK,KAAK,CAAC,GAAGA,EAAE,GAAG,EAAE,CAAC;YAChG,CAAC,CAAC,EAAEhC,qBAAqB,GAAGD,UAAU,CAACE,aAAa,EAAEA,aAAa,GAAGD,qBAAqB,KAAK,KAAK,CAAC,GAAG,EAAE,GAAGA,qBAAqB,EAAEE,qBAAqB,GAAGH,UAAU,CAACI,YAAY,EAAEA,YAAY,GAAGD,qBAAqB,KAAK,KAAK,CAAC,GAAG,EAAE,GAAGA,qBAAqB,EAAEE,qBAAqB,GAAGL,UAAU,CAACM,UAAU,EAAEA,UAAU,GAAGD,qBAAqB,KAAK,KAAK,CAAC,GAAG,EAAE,GAAGA,qBAAqB,EAAEE,qBAAqB,GAAGP,UAAU,CAACQ,WAAW,EAAEA,WAAW,GAAGD,qBAAqB,KAAK,KAAK,CAAC,GAAG,EAAE,GAAGA,qBAAqB,EAAEE,qBAAqB,GAAGT,UAAU,CAACU,UAAU,EAAEA,UAAU,GAAGD,qBAAqB,KAAK,KAAK,CAAC,GAAG,EAAE,GAAGA,qBAAqB;YACrnBe,UAAU,CAACpH,IAAI,GAAG,EAAE;YACpB,OAAO1B,cAAc,CAACL,OAAO,CAACqI,UAAU,CAAC,EAAE9H,GAAG,EAAEC,MAAM,CAAC;UACzD,KAAK,EAAE;YACL2I,UAAU,CAACpH,IAAI,GAAG,EAAE;YACpB,OAAOkF,WAAW,CAACzG,MAAM,EAAEkG,OAAO,IAAI,CAACc,eAAe,EAAE;cACtDqC,qBAAqB,EAAEpC,aAAa,GAAGnI,cAAc,GAAG;YAC1D,CAAC,CAAC;UACJ,KAAK,EAAE;YACLiG,aAAa,GAAG4D,UAAU,CAACE,IAAI;YAC/Bf,qBAAqB,GAAGhD,wBAAwB,CAACC,aAAa,EAAEC,OAAO,EAAEhF,MAAM,EAAE,CAACyF,iBAAiB,GAAGyB,gBAAgB,MAAM,IAAI,IAAIzB,iBAAiB,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAG,CAACC,qBAAqB,GAAGD,iBAAiB,CAACwD,QAAQ,MAAM,IAAI,IAAIvD,qBAAqB,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,qBAAqB,CAAC4D,aAAa,CAAC,EAAEvB,SAAS,GAAGD,qBAAqB,CAACC,SAAS,EAAEC,KAAK,GAAGF,qBAAqB,CAACE,KAAK,EAAEC,OAAO,GAAGH,qBAAqB,CAACG,OAAO,EAAEC,MAAM,GAAGJ,qBAAqB,CAACI,MAAM;YAC/dC,qBAAqB,GAAGxJ,uBAAuB,CAACwD,aAAa,CAAC,EAAEiG,mBAAmB,GAAGD,qBAAqB,CAACC,mBAAmB,EAAEC,cAAc,GAAGF,qBAAqB,CAACE,cAAc,EAAEC,oBAAoB,GAAGH,qBAAqB,CAACG,oBAAoB,CAAC,CAAC;YAC3PC,6BAA6B,GAAG,SAASA,6BAA6B,CAAC5I,OAAO,EAAE;cAC9E,OAAOgH,wBAAwB,GAAGhH,OAAO;YAC3C,CAAC;YACD6I,kBAAkB,GAAG,SAASA,kBAAkB,GAAG;cACjD,IAAIe,gBAAgB,GAAGtJ,SAAS,CAACC,MAAM,GAAG,CAAC,IAAID,SAAS,CAAC,CAAC,CAAC,KAAKE,SAAS,GAAGF,SAAS,CAAC,CAAC,CAAC,GAAG2G,gBAAgB;cAC3G,IAAI4C,iBAAiB;cACrB,IAAIC,gBAAgB;cACpB,IAAIC,YAAY,GAAG;gBACjBd,IAAI,EAAEzG,aAAa;gBACnB4F,SAAS,EAAEA,SAAS;gBACpBC,KAAK,EAAE,CAAC,aAAa3J,iBAAiB,EAAE,aAAaE,mBAAmB,CAACyC,IAAI,CAAC,SAAS2I,QAAQ,GAAG;kBAChG,IAAIC,KAAK;kBACT,OAAOrL,mBAAmB,CAAC4C,IAAI,CAAC,SAAS0I,SAAS,CAACC,SAAS,EAAE;oBAC5D,OAAO,CAAC,EAAE;sBACR,QAAQA,SAAS,CAACxI,IAAI,GAAGwI,SAAS,CAACvI,IAAI;wBACrC,KAAK,CAAC;0BACJ,IAAI4B,OAAO,CAACqB,GAAG,CAACC,QAAQ,KAAK,aAAa,EAAE;4BAC1CmF,KAAK,GAAGvK,2BAA2B,CAACyG,QAAQ,EAAE,MAAM,CAAC,CAAC,CAAC;4BACvD,IAAI8D,KAAK,IAAI,CAACA,KAAK,CAAC1J,MAAM,EAAE;8BAC1BZ,eAAe,CAACwG,QAAQ,CAAC;4BAC3B;0BACF;wBACF,KAAK,CAAC;wBACN,KAAK,KAAK;0BACR,OAAOgE,SAAS,CAACrI,IAAI,EAAE;sBAAC;oBAE9B;kBACF,CAAC,EAAEkI,QAAQ,CAAC;gBACd,CAAC,CAAC,CAAC,EAAE,aAAatL,iBAAiB,EAAE,aAAaE,mBAAmB,CAACyC,IAAI,CAAC,SAAS+I,QAAQ,GAAG;kBAC7F,OAAOxL,mBAAmB,CAAC4C,IAAI,CAAC,SAAS6I,SAAS,CAACC,SAAS,EAAE;oBAC5D,OAAO,CAAC,EAAE;sBACR,QAAQA,SAAS,CAAC3I,IAAI,GAAG2I,SAAS,CAAC1I,IAAI;wBACrC,KAAK,CAAC;0BACJ0I,SAAS,CAAC1I,IAAI,GAAG,CAAC;0BAClB,OAAOZ,oBAAoB,CAACqF,QAAQ,EAAEjG,GAAG,CAAC;wBAC5C,KAAK,CAAC;0BACJkK,SAAS,CAACC,EAAE,GAAGD,SAAS,CAACpB,IAAI;0BAC7B,IAAI,CAACoB,SAAS,CAACC,EAAE,EAAE;4BACjBD,SAAS,CAAC1I,IAAI,GAAG,CAAC;4BAClB;0BACF;0BACA0I,SAAS,CAACC,EAAE,GAAG9E,wBAAwB;wBACzC,KAAK,CAAC;0BACJ,IAAI,CAAC6E,SAAS,CAACC,EAAE,EAAE;4BACjBD,SAAS,CAAC1I,IAAI,GAAG,CAAC;4BAClB;0BACF;0BACA,OAAO0I,SAAS,CAACzI,MAAM,CAAC,QAAQ,EAAE4D,wBAAwB,CAAC0D,OAAO,CAAC;wBACrE,KAAK,CAAC;0BACJ,OAAOmB,SAAS,CAACzI,MAAM,CAAC,QAAQ,EAAErB,SAAS,CAAC;wBAC9C,KAAK,CAAC;wBACN,KAAK,KAAK;0BACR,OAAO8J,SAAS,CAACxI,IAAI,EAAE;sBAAC;oBAE9B;kBACF,CAAC,EAAEsI,QAAQ,CAAC;gBACd,CAAC,CAAC,CAAC,EACH;gBACA;gBACA1L,iBAAiB,EAAE,aAAaE,mBAAmB,CAACyC,IAAI,CAAC,SAASmJ,QAAQ,GAAG;kBAC3E,OAAO5L,mBAAmB,CAAC4C,IAAI,CAAC,SAASiJ,SAAS,CAACC,SAAS,EAAE;oBAC5D,OAAO,CAAC,EAAE;sBACR,QAAQA,SAAS,CAAC/I,IAAI,GAAG+I,SAAS,CAAC9I,IAAI;wBACrC,KAAK,CAAC;0BACJiI,iBAAiB,GAAG7C,wBAAwB;0BAC5C8C,gBAAgB,GAAGrG,mBAAmB,CAACjB,aAAa,EAAE,CAAC,CAAC+B,YAAY,EAAEjC,oBAAoB,EAAEC,SAAS,EAAE,YAAY;4BACjH,OAAOsH,iBAAiB;0BAC1B,CAAC,CAAC;wBACJ,KAAK,CAAC;wBACN,KAAK,KAAK;0BACR,OAAOa,SAAS,CAAC5I,IAAI,EAAE;sBAAC;oBAE9B;kBACF,CAAC,EAAE0I,QAAQ,CAAC;gBACd,CAAC,CAAC,CAAC,EACH;gBACA;gBACA9L,iBAAiB,EAAE,aAAaE,mBAAmB,CAACyC,IAAI,CAAC,SAASsJ,QAAQ,GAAG;kBAC3E,IAAIC,eAAe;kBACnB,OAAOhM,mBAAmB,CAAC4C,IAAI,CAAC,SAASqJ,SAAS,CAACC,SAAS,EAAE;oBAC5D,OAAO,CAAC,EAAE;sBACR,QAAQA,SAAS,CAACnJ,IAAI,GAAGmJ,SAAS,CAAClJ,IAAI;wBACrC,KAAK,CAAC;0BACJgJ,eAAe,GAAGhB,gBAAgB,KAAK3C,gBAAgB;0BACvD,IAAI2D,eAAe,IAAI,CAACf,iBAAiB,EAAE;4BACzC;4BACA;4BACAA,iBAAiB,GAAGzH,aAAa,CAACC,UAAU,EAAEC,oBAAoB,EAAEC,SAAS,EAAEC,aAAa,CAAC;4BAC7FoG,6BAA6B,CAACiB,iBAAiB,CAAC;0BAClD;0BACArF,MAAM,CAAC;4BACLxE,OAAO,EAAE6J,iBAAiB;4BAC1BlF,OAAO,EAAE,IAAI;4BACbC,SAAS,EAAEgF;0BACb,CAAC,EAAE,UAAU,CAAC;wBAChB,KAAK,CAAC;wBACN,KAAK,KAAK;0BACR,OAAOkB,SAAS,CAAChJ,IAAI,EAAE;sBAAC;oBAE9B;kBACF,CAAC,EAAE6I,QAAQ,CAAC;gBACd,CAAC,CAAC,CAAC,EAAExD,YAAY,EACjB;gBACA;gBACAzI,iBAAiB,EAAE,aAAaE,mBAAmB,CAACyC,IAAI,CAAC,SAAS0J,QAAQ,GAAG;kBAC3E,OAAOnM,mBAAmB,CAAC4C,IAAI,CAAC,SAASwJ,SAAS,CAACC,SAAS,EAAE;oBAC5D,OAAO,CAAC,EAAE;sBACR,QAAQA,SAAS,CAACtJ,IAAI,GAAGsJ,SAAS,CAACrJ,IAAI;wBACrC,KAAK,CAAC;0BACJ,OAAOqJ,SAAS,CAACpJ,MAAM,CAAC,QAAQ,EAAE3B,cAAc,CAACL,OAAO,CAACmI,WAAW,CAAC,EAAE5H,GAAG,EAAEC,MAAM,CAAC,CAAC;wBACtF,KAAK,CAAC;wBACN,KAAK,KAAK;0BACR,OAAO4K,SAAS,CAACnJ,IAAI,EAAE;sBAAC;oBAE9B;kBACF,CAAC,EAAEiJ,QAAQ,CAAC;gBACd,CAAC,CAAC,CAAC,EAAE,aAAa,YAAY;kBAC5B,IAAIG,KAAK,GAAGxM,iBAAiB,EAAE,aAAaE,mBAAmB,CAACyC,IAAI,CAAC,SAAS8J,QAAQ,CAACC,KAAK,EAAE;oBAC5F,OAAOxM,mBAAmB,CAAC4C,IAAI,CAAC,SAAS6J,SAAS,CAACC,SAAS,EAAE;sBAC5D,OAAO,CAAC,EAAE;wBACR,QAAQA,SAAS,CAAC3J,IAAI,GAAG2J,SAAS,CAAC1J,IAAI;0BACrC,KAAK,CAAC;4BACJ,OAAO0J,SAAS,CAACzJ,MAAM,CAAC,QAAQ,EAAEwG,KAAK,CAACjK,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAEgN,KAAK,CAAC,EAAE,CAAC,CAAC,EAAE;8BAClFxG,SAAS,EAAEkF,gBAAgB,EAAE;8BAC7BpB,cAAc,EAAEA,cAAc;8BAC9BD,mBAAmB,EAAEA;4BACvB,CAAC,CAAC,CAAC,CAAC;0BACN,KAAK,CAAC;0BACN,KAAK,KAAK;4BACR,OAAO6C,SAAS,CAACxJ,IAAI,EAAE;wBAAC;sBAE9B;oBACF,CAAC,EAAEqJ,QAAQ,CAAC;kBACd,CAAC,CAAC,CAAC;kBACH,OAAO,UAAUI,GAAG,EAAE;oBACpB,OAAOL,KAAK,CAAC9J,KAAK,CAAC,IAAI,EAAEd,SAAS,CAAC;kBACrC,CAAC;gBACH,CAAC,EAAE,EACH;gBACA;gBACA5B,iBAAiB,EAAE,aAAaE,mBAAmB,CAACyC,IAAI,CAAC,SAASmK,QAAQ,GAAG;kBAC3E,OAAO5M,mBAAmB,CAAC4C,IAAI,CAAC,SAASiK,SAAS,CAACC,SAAS,EAAE;oBAC5D,OAAO,CAAC,EAAE;sBACR,QAAQA,SAAS,CAAC/J,IAAI,GAAG+J,SAAS,CAAC9J,IAAI;wBACrC,KAAK,CAAC;0BACJ,OAAO8J,SAAS,CAAC7J,MAAM,CAAC,QAAQ,EAAE2C,MAAM,CAAC;4BACvCxE,OAAO,EAAE6J,iBAAiB;4BAC1BlF,OAAO,EAAE,KAAK;4BACdC,SAAS,EAAEgF;0BACb,CAAC,EAAE,SAAS,CAAC,CAAC;wBAChB,KAAK,CAAC;wBACN,KAAK,KAAK;0BACR,OAAO8B,SAAS,CAAC5J,IAAI,EAAE;sBAAC;oBAE9B;kBACF,CAAC,EAAE0J,QAAQ,CAAC;gBACd,CAAC,CAAC,CAAC,EAAE,aAAa9M,iBAAiB,EAAE,aAAaE,mBAAmB,CAACyC,IAAI,CAAC,SAASsK,QAAQ,GAAG;kBAC7F,OAAO/M,mBAAmB,CAAC4C,IAAI,CAAC,SAASoK,SAAS,CAACC,SAAS,EAAE;oBAC5D,OAAO,CAAC,EAAE;sBACR,QAAQA,SAAS,CAAClK,IAAI,GAAGkK,SAAS,CAACjK,IAAI;wBACrC,KAAK,CAAC;0BACJ,OAAOiK,SAAS,CAAChK,MAAM,CAAC,QAAQ,EAAE3B,cAAc,CAACL,OAAO,CAACiI,UAAU,CAAC,EAAE1H,GAAG,EAAEC,MAAM,CAAC,CAAC;wBACrF,KAAK,CAAC;wBACN,KAAK,KAAK;0BACR,OAAOwL,SAAS,CAAC/J,IAAI,EAAE;sBAAC;oBAE9B;kBACF,CAAC,EAAE6J,QAAQ,CAAC;gBACd,CAAC,CAAC,CAAC,EACH;gBACA;gBACAjN,iBAAiB,EAAE,aAAaE,mBAAmB,CAACyC,IAAI,CAAC,SAASyK,SAAS,GAAG;kBAC5E,OAAOlN,mBAAmB,CAAC4C,IAAI,CAAC,SAASuK,UAAU,CAACC,UAAU,EAAE;oBAC9D,OAAO,CAAC,EAAE;sBACR,QAAQA,UAAU,CAACrK,IAAI,GAAGqK,UAAU,CAACpK,IAAI;wBACvC,KAAK,CAAC;0BACJoK,UAAU,CAACpK,IAAI,GAAG,CAAC;0BACnB,OAAOZ,oBAAoB,CAACqF,QAAQ,EAAEjG,GAAG,CAAC;wBAC5C,KAAK,CAAC;0BACJ,IAAI,CAAC4L,UAAU,CAAC9C,IAAI,EAAE;4BACpB8C,UAAU,CAACpK,IAAI,GAAG,CAAC;4BACnB;0BACF;0BACA6D,wBAAwB,GAAG,IAAIrG,QAAQ,EAAE;wBAC3C,KAAK,CAAC;wBACN,KAAK,KAAK;0BACR,OAAO4M,UAAU,CAAClK,IAAI,EAAE;sBAAC;oBAE/B;kBACF,CAAC,EAAEgK,SAAS,CAAC;gBACf,CAAC,CAAC,CAAC,EAAE,aAAapN,iBAAiB,EAAE,aAAaE,mBAAmB,CAACyC,IAAI,CAAC,SAAS4K,SAAS,GAAG;kBAC9F,IAAIC,WAAW;kBACf,OAAOtN,mBAAmB,CAAC4C,IAAI,CAAC,SAAS2K,UAAU,CAACC,UAAU,EAAE;oBAC9D,OAAO,CAAC,EAAE;sBACR,QAAQA,UAAU,CAACzK,IAAI,GAAGyK,UAAU,CAACxK,IAAI;wBACvC,KAAK,CAAC;0BACJ,IAAI4B,OAAO,CAACqB,GAAG,CAACC,QAAQ,KAAK,aAAa,EAAE;4BAC1CoH,WAAW,GAAG,gBAAgB,CAACpI,MAAM,CAACtB,aAAa,EAAE,oBAAoB,CAAC;4BAC1E5C,kBAAkB,CAACsM,WAAW,EAAE/F,QAAQ,CAAC;0BAC3C;wBACF,KAAK,CAAC;wBACN,KAAK,KAAK;0BACR,OAAOiG,UAAU,CAACtK,IAAI,EAAE;sBAAC;oBAE/B;kBACF,CAAC,EAAEmK,SAAS,CAAC;gBACf,CAAC,CAAC,CAAC,CAAC;gBACJ3D,OAAO,EAAE,CAAC,aAAa5J,iBAAiB,EAAE,aAAaE,mBAAmB,CAACyC,IAAI,CAAC,SAASgL,SAAS,GAAG;kBACnG,OAAOzN,mBAAmB,CAAC4C,IAAI,CAAC,SAAS8K,UAAU,CAACC,UAAU,EAAE;oBAC9D,OAAO,CAAC,EAAE;sBACR,QAAQA,UAAU,CAAC5K,IAAI,GAAG4K,UAAU,CAAC3K,IAAI;wBACvC,KAAK,CAAC;0BACJ,OAAO2K,UAAU,CAAC1K,MAAM,CAAC,QAAQ,EAAE3B,cAAc,CAACL,OAAO,CAAC6H,aAAa,CAAC,EAAEtH,GAAG,EAAEC,MAAM,CAAC,CAAC;wBACzF,KAAK,CAAC;wBACN,KAAK,KAAK;0BACR,OAAOkM,UAAU,CAACzK,IAAI,EAAE;sBAAC;oBAE/B;kBACF,CAAC,EAAEuK,SAAS,CAAC;gBACf,CAAC,CAAC,CAAC,EAAE,aAAa,YAAY;kBAC5B,IAAIG,MAAM,GAAG9N,iBAAiB,EAAE,aAAaE,mBAAmB,CAACyC,IAAI,CAAC,SAASoL,SAAS,CAACrB,KAAK,EAAE;oBAC9F,OAAOxM,mBAAmB,CAAC4C,IAAI,CAAC,SAASkL,UAAU,CAACC,UAAU,EAAE;sBAC9D,OAAO,CAAC,EAAE;wBACR,QAAQA,UAAU,CAAChL,IAAI,GAAGgL,UAAU,CAAC/K,IAAI;0BACvC,KAAK,CAAC;4BACJ,OAAO+K,UAAU,CAAC9K,MAAM,CAAC,QAAQ,EAAEyG,OAAO,CAAClK,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAEgN,KAAK,CAAC,EAAE,CAAC,CAAC,EAAE;8BACrFxG,SAAS,EAAEkF,gBAAgB;4BAC7B,CAAC,CAAC,CAAC,CAAC;0BACN,KAAK,CAAC;0BACN,KAAK,KAAK;4BACR,OAAO6C,UAAU,CAAC7K,IAAI,EAAE;wBAAC;sBAE/B;oBACF,CAAC,EAAE2K,SAAS,CAAC;kBACf,CAAC,CAAC,CAAC;kBACH,OAAO,UAAUG,GAAG,EAAE;oBACpB,OAAOJ,MAAM,CAACpL,KAAK,CAAC,IAAI,EAAEd,SAAS,CAAC;kBACtC,CAAC;gBACH,CAAC,EAAE,EAAE8G,cAAc,EAAE,aAAa1I,iBAAiB,EAAE,aAAaE,mBAAmB,CAACyC,IAAI,CAAC,SAASwL,SAAS,GAAG;kBAC9G,OAAOjO,mBAAmB,CAAC4C,IAAI,CAAC,SAASsL,UAAU,CAACC,UAAU,EAAE;oBAC9D,OAAO,CAAC,EAAE;sBACR,QAAQA,UAAU,CAACpL,IAAI,GAAGoL,UAAU,CAACnL,IAAI;wBACvC,KAAK,CAAC;0BACJ,OAAOmL,UAAU,CAAClL,MAAM,CAAC,QAAQ,EAAE3B,cAAc,CAACL,OAAO,CAAC+H,YAAY,CAAC,EAAExH,GAAG,EAAEC,MAAM,CAAC,CAAC;wBACxF,KAAK,CAAC;wBACN,KAAK,KAAK;0BACR,OAAO0M,UAAU,CAACjL,IAAI,EAAE;sBAAC;oBAE/B;kBACF,CAAC,EAAE+K,SAAS,CAAC;gBACf,CAAC,CAAC,CAAC,EAAE,aAAanO,iBAAiB,EAAE,aAAaE,mBAAmB,CAACyC,IAAI,CAAC,SAAS2L,SAAS,GAAG;kBAC9F,OAAOpO,mBAAmB,CAAC4C,IAAI,CAAC,SAASyL,UAAU,CAACC,UAAU,EAAE;oBAC9D,OAAO,CAAC,EAAE;sBACR,QAAQA,UAAU,CAACvL,IAAI,GAAGuL,UAAU,CAACtL,IAAI;wBACvC,KAAK,CAAC;0BACJ4C,MAAM,CAAC;4BACLxE,OAAO,EAAE,IAAI;4BACb2E,OAAO,EAAE,KAAK;4BACdC,SAAS,EAAEgF;0BACb,CAAC,EAAE,WAAW,CAAC;0BACfjB,oBAAoB,CAACnG,aAAa,CAAC;0BACnC;0BACAqH,iBAAiB,GAAG,IAAI;0BACxBjB,6BAA6B,CAACiB,iBAAiB,CAAC;wBAClD,KAAK,CAAC;wBACN,KAAK,KAAK;0BACR,OAAOqD,UAAU,CAACpL,IAAI,EAAE;sBAAC;oBAE/B;kBACF,CAAC,EAAEkL,SAAS,CAAC;gBACf,CAAC,CAAC,CAAC,EAAE,aAAatO,iBAAiB,EAAE,aAAaE,mBAAmB,CAACyC,IAAI,CAAC,SAAS8L,SAAS,GAAG;kBAC9F,OAAOvO,mBAAmB,CAAC4C,IAAI,CAAC,SAAS4L,UAAU,CAACC,UAAU,EAAE;oBAC9D,OAAO,CAAC,EAAE;sBACR,QAAQA,UAAU,CAAC1L,IAAI,GAAG0L,UAAU,CAACzL,IAAI;wBACvC,KAAK,CAAC;0BACJyL,UAAU,CAACzL,IAAI,GAAG,CAAC;0BACnB,OAAOZ,oBAAoB,CAACqF,QAAQ,EAAEjG,GAAG,CAAC;wBAC5C,KAAK,CAAC;0BACJiN,UAAU,CAAC9C,EAAE,GAAG8C,UAAU,CAACnE,IAAI;0BAC/B,IAAI,CAACmE,UAAU,CAAC9C,EAAE,EAAE;4BAClB8C,UAAU,CAACzL,IAAI,GAAG,CAAC;4BACnB;0BACF;0BACAyL,UAAU,CAAC9C,EAAE,GAAG9E,wBAAwB;wBAC1C,KAAK,CAAC;0BACJ,IAAI,CAAC4H,UAAU,CAAC9C,EAAE,EAAE;4BAClB8C,UAAU,CAACzL,IAAI,GAAG,CAAC;4BACnB;0BACF;0BACA6D,wBAAwB,CAAC1E,OAAO,EAAE;wBACpC,KAAK,CAAC;wBACN,KAAK,KAAK;0BACR,OAAOsM,UAAU,CAACvL,IAAI,EAAE;sBAAC;oBAE/B;kBACF,CAAC,EAAEqL,SAAS,CAAC;gBACf,CAAC,CAAC,CAAC;cACL,CAAC;cACD,IAAI,OAAO5E,MAAM,KAAK,UAAU,EAAE;gBAChCwB,YAAY,CAACxB,MAAM,GAAGA,MAAM;cAC9B;cACA,OAAOwB,YAAY;YACrB,CAAC;YACD,OAAOf,UAAU,CAACnH,MAAM,CAAC,QAAQ,EAAEgH,kBAAkB,CAAC;UACxD,KAAK,EAAE;UACP,KAAK,KAAK;YACR,OAAOG,UAAU,CAAClH,IAAI,EAAE;QAAC;MAE/B;IACF,CAAC,EAAE+D,SAAS,CAAC;EACf,CAAC,CAAC,CAAC;EACH,OAAOD,QAAQ,CAACxE,KAAK,CAAC,IAAI,EAAEd,SAAS,CAAC;AACxC"}]}