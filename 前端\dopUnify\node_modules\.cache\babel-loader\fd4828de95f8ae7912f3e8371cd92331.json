{"remainingRequest": "E:\\华南区项目\\中国银行\\开发区\\科技公司运营平台\\前端\\dopUnify\\node_modules\\babel-loader\\lib\\index.js!E:\\华南区项目\\中国银行\\开发区\\科技公司运营平台\\前端\\dopUnify\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!E:\\华南区项目\\中国银行\\开发区\\科技公司运营平台\\前端\\dopUnify\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\华南区项目\\中国银行\\开发区\\科技公司运营平台\\前端\\dopUnify\\src\\views\\system\\config\\timingService\\index.vue?vue&type=script&lang=js&", "dependencies": [{"path": "E:\\华南区项目\\中国银行\\开发区\\科技公司运营平台\\前端\\dopUnify\\src\\views\\system\\config\\timingService\\index.vue", "mtime": 1721631954662}, {"path": "E:\\华南区项目\\中国银行\\开发区\\科技公司运营平台\\前端\\dopUnify\\babel.config.js", "mtime": 1721631954662}, {"path": "E:\\华南区项目\\中国银行\\开发区\\科技公司运营平台\\前端\\dopUnify\\.babelrc", "mtime": 1721631954662}, {"path": "E:\\华南区项目\\中国银行\\开发区\\科技公司运营平台\\前端\\dopUnify\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "E:\\华南区项目\\中国银行\\开发区\\科技公司运营平台\\前端\\dopUnify\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "E:\\华南区项目\\中国银行\\开发区\\科技公司运营平台\\前端\\dopUnify\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "E:\\华南区项目\\中国银行\\开发区\\科技公司运营平台\\前端\\dopUnify\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1655715099000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;AAwBA;AACA;AACA;;AAEA;EACAA;EACAC;IAAAC;EAAA;EACAC;IACA;MACAC;MACAC;QACAC;QACAC;QACAC;QACAC;QACAC;QACAC;MACA;MACAC;QACA;QACAC;QACAC;QACAC;QACAC;QACAC;QACAC;QACAC;QACAC;MACA;MACAC;IACA;EACA;EACAC;IACA;IACA;EACA;EACAC;IAAA;IACA;MACA;MACA;MACA;IACA;EACA;;EACAC;IACA;AACA;IACAC;MACA;IACA;IACA;AACA;IACAC;MAAA;MACA;MACA,YACAC,gDACAC;QACA;QACAC;UACA;YACAC;UACA;UACAC;QACA;QACA;QACA;MACA;IACA;IACA;AACA;AACA;IACAC;MACA;QACA;MACA;QACA;MACA;IACA;IACA;AACA;IACAC;MACA;IACA;EACA;AACA", "names": ["name", "components", "TableList", "data", "config", "defaultForm", "job_key", "job_name", "cron_expression", "job_status", "job_server", "service_module", "btnAll", "btnQuery", "btnAdd", "btnDelete", "btnModify", "btnExecute", "btnStart", "btnShowLogs", "btnStop", "jobServer", "created", "mounted", "methods", "btnPermissions", "getDispatch", "dispatch", "then", "res", "label", "SystemArray", "validateForm", "queryList"], "sourceRoot": "src/views/system/config/timingService", "sources": ["index.vue"], "sourcesContent": ["<template>\r\n  <div class=\"app-container\">\r\n    <div class=\"sun-content\">\r\n      <div class=\"filter-container\">\r\n        <sun-form\r\n          ref=\"formRef\"\r\n          :config=\"config\"\r\n          :label-width=\"'11rem'\"\r\n          :default-form=\"defaultForm\"\r\n          :query=\"btnAll.btnQuery\"\r\n          @query=\"queryList\"\r\n          @validateForm=\"validateForm\"\r\n        />\r\n      </div>\r\n    </div>\r\n    <table-list\r\n      ref=\"tableListRef\"\r\n      :default-form=\"defaultForm\"\r\n      :btn-all=\"btnAll\"\r\n      :job-server=\"jobServer\"\r\n    />\r\n  </div>\r\n</template>\r\n<script>\r\nimport { config } from './info' // 表单配置\r\nimport TableList from './component/table' // 表格\r\nimport { permissionsBtn } from '@/utils/permissions' // 权限配置\r\n\r\nexport default {\r\n  name: 'TimingService',\r\n  components: { TableList },\r\n  data() {\r\n    return {\r\n      config: config(this),\r\n      defaultForm: {\r\n        job_key: '',\r\n        job_name: '',\r\n        cron_expression: '',\r\n        job_status: '',\r\n        job_server: '',\r\n        service_module: ''\r\n      },\r\n      btnAll: {\r\n        // 当前页需要配置权限的按钮  权限获取\r\n        btnQuery: true,\r\n        btnAdd: true,\r\n        btnDelete: true,\r\n        btnModify: true,\r\n        btnExecute: true,\r\n        btnStart: true,\r\n        btnShowLogs: true,\r\n        btnStop: true\r\n      },\r\n      jobServer: [] // 外表数据源-应用服务id\r\n    }\r\n  },\r\n  created() {\r\n    this.btnPermissions()\r\n    this.getDispatch()\r\n  },\r\n  mounted() {\r\n    this.$nextTick().then(() => {\r\n      // if (this.btnAll.btnQuery) {\r\n      this.queryList()\r\n      // }\r\n    })\r\n  },\r\n  methods: {\r\n    /**\r\n     * 按钮权限配置*/\r\n    btnPermissions() {\r\n      this.btnAll = permissionsBtn(this.$attrs.button_id, this.btnAll)\r\n    },\r\n    /**\r\n     * 获取外部字典*/\r\n    getDispatch() {\r\n      // 处理系统编号\r\n      this.$store\r\n        .dispatch('common/setExternalData', 'SERVER_ID')\r\n        .then((res) => {\r\n          const SystemArray = []\r\n          res.map(function(item) {\r\n            const valueS = Object.assign({}, item, {\r\n              label: item.value + '-' + item.label\r\n            })\r\n            SystemArray.push(valueS)\r\n          })\r\n          this.config.job_server.options = SystemArray\r\n          this.jobServer = SystemArray\r\n        })\r\n    },\r\n    /**\r\n     * 表单校验\r\n     * @param {Boolean}valid 校验返回值*/\r\n    validateForm(valid) {\r\n      if (valid) {\r\n        this.$refs.tableListRef.queryList(1)\r\n      } else {\r\n        return false\r\n      }\r\n    },\r\n    /**\r\n     * 按钮：查询*/\r\n    queryList() {\r\n      this.$refs['formRef'].validateForm()\r\n    }\r\n  }\r\n}\r\n</script>\r\n<style scoped>\r\n.app-container {\r\n  width: 100%;\r\n  height: 100%;\r\n}\r\n</style>\r\n"]}]}