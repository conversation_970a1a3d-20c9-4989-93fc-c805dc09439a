{"remainingRequest": "E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1\\数字运营平台-统一门户工程\\dopUnify\\node_modules\\thread-loader\\dist\\cjs.js!E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1\\数字运营平台-统一门户工程\\dopUnify\\node_modules\\babel-loader\\lib\\index.js!E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1\\数字运营平台-统一门户工程\\dopUnify\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1\\数字运营平台-统一门户工程\\dopUnify\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1\\数字运营平台-统一门户工程\\dopUnify\\src\\views\\system\\config\\message\\msgCard\\index.vue?vue&type=script&lang=js&", "dependencies": [{"path": "E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1\\数字运营平台-统一门户工程\\dopUnify\\src\\views\\system\\config\\message\\msgCard\\index.vue", "mtime": 1719481421125}, {"path": "E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1\\数字运营平台-统一门户工程\\dopUnify\\babel.config.js", "mtime": 1716875191548}, {"path": "E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1\\数字运营平台-统一门户工程\\dopUnify\\.babelrc", "mtime": 1716875191502}, {"path": "E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1\\数字运营平台-统一门户工程\\dopUnify\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1\\数字运营平台-统一门户工程\\dopUnify\\node_modules\\thread-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1\\数字运营平台-统一门户工程\\dopUnify\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1\\数字运营平台-统一门户工程\\dopUnify\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1\\数字运营平台-统一门户工程\\dopUnify\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1655715099000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, null]}