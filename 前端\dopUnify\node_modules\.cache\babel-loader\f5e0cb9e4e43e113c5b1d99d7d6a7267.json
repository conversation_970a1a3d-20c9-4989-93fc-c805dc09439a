{"remainingRequest": "E:\\华南区项目\\中国银行\\开发区\\科技公司运营平台\\前端\\dopUnify\\node_modules\\thread-loader\\dist\\cjs.js!E:\\华南区项目\\中国银行\\开发区\\科技公司运营平台\\前端\\dopUnify\\node_modules\\babel-loader\\lib\\index.js!E:\\华南区项目\\中国银行\\开发区\\科技公司运营平台\\前端\\dopUnify\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!E:\\华南区项目\\中国银行\\开发区\\科技公司运营平台\\前端\\dopUnify\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\华南区项目\\中国银行\\开发区\\科技公司运营平台\\前端\\dopUnify\\node_modules\\sunui\\src\\components\\SunForm\\index.vue?vue&type=script&lang=js&", "dependencies": [{"path": "E:\\华南区项目\\中国银行\\开发区\\科技公司运营平台\\前端\\dopUnify\\node_modules\\sunui\\src\\components\\SunForm\\index.vue", "mtime": 499162500000}, {"path": "E:\\华南区项目\\中国银行\\开发区\\科技公司运营平台\\前端\\dopUnify\\babel.config.js", "mtime": 1721631954662}, {"path": "E:\\华南区项目\\中国银行\\开发区\\科技公司运营平台\\前端\\dopUnify\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "E:\\华南区项目\\中国银行\\开发区\\科技公司运营平台\\前端\\dopUnify\\node_modules\\thread-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "E:\\华南区项目\\中国银行\\开发区\\科技公司运营平台\\前端\\dopUnify\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "E:\\华南区项目\\中国银行\\开发区\\科技公司运营平台\\前端\\dopUnify\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "E:\\华南区项目\\中国银行\\开发区\\科技公司运营平台\\前端\\dopUnify\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1655715099000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, null]}