{"remainingRequest": "D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\node_modules\\babel-loader\\lib\\index.js!D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\node_modules\\qiankun\\es\\addons\\runtimePublicPath.js", "dependencies": [{"path": "D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\node_modules\\qiankun\\es\\addons\\runtimePublicPath.js", "mtime": 1667130453000}, {"path": "D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\babel.config.js", "mtime": 1686019818359}, {"path": "D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:aW1wb3J0IF9hc3luY1RvR2VuZXJhdG9yIGZyb20gIkBiYWJlbC9ydW50aW1lL2hlbHBlcnMvZXNtL2FzeW5jVG9HZW5lcmF0b3IiOwppbXBvcnQgX3JlZ2VuZXJhdG9yUnVudGltZSBmcm9tICJAYmFiZWwvcnVudGltZS9yZWdlbmVyYXRvciI7CnZhciByYXdQdWJsaWNQYXRoID0gd2luZG93Ll9fSU5KRUNURURfUFVCTElDX1BBVEhfQllfUUlBTktVTl9fOwpleHBvcnQgZGVmYXVsdCBmdW5jdGlvbiBnZXRBZGRPbihnbG9iYWwpIHsKICB2YXIgcHVibGljUGF0aCA9IGFyZ3VtZW50cy5sZW5ndGggPiAxICYmIGFyZ3VtZW50c1sxXSAhPT0gdW5kZWZpbmVkID8gYXJndW1lbnRzWzFdIDogJy8nOwogIHZhciBoYXNNb3VudGVkT25jZSA9IGZhbHNlOwogIHJldHVybiB7CiAgICBiZWZvcmVMb2FkOiBmdW5jdGlvbiBiZWZvcmVMb2FkKCkgewogICAgICByZXR1cm4gX2FzeW5jVG9HZW5lcmF0b3IoIC8qI19fUFVSRV9fKi9fcmVnZW5lcmF0b3JSdW50aW1lLm1hcmsoZnVuY3Rpb24gX2NhbGxlZSgpIHsKICAgICAgICByZXR1cm4gX3JlZ2VuZXJhdG9yUnVudGltZS53cmFwKGZ1bmN0aW9uIF9jYWxsZWUkKF9jb250ZXh0KSB7CiAgICAgICAgICB3aGlsZSAoMSkgewogICAgICAgICAgICBzd2l0Y2ggKF9jb250ZXh0LnByZXYgPSBfY29udGV4dC5uZXh0KSB7CiAgICAgICAgICAgICAgY2FzZSAwOgogICAgICAgICAgICAgICAgLy8gZXNsaW50LWRpc2FibGUtbmV4dC1saW5lIG5vLXBhcmFtLXJlYXNzaWduCiAgICAgICAgICAgICAgICBnbG9iYWwuX19JTkpFQ1RFRF9QVUJMSUNfUEFUSF9CWV9RSUFOS1VOX18gPSBwdWJsaWNQYXRoOwogICAgICAgICAgICAgIGNhc2UgMToKICAgICAgICAgICAgICBjYXNlICJlbmQiOgogICAgICAgICAgICAgICAgcmV0dXJuIF9jb250ZXh0LnN0b3AoKTsKICAgICAgICAgICAgfQogICAgICAgICAgfQogICAgICAgIH0sIF9jYWxsZWUpOwogICAgICB9KSkoKTsKICAgIH0sCiAgICBiZWZvcmVNb3VudDogZnVuY3Rpb24gYmVmb3JlTW91bnQoKSB7CiAgICAgIHJldHVybiBfYXN5bmNUb0dlbmVyYXRvciggLyojX19QVVJFX18qL19yZWdlbmVyYXRvclJ1bnRpbWUubWFyayhmdW5jdGlvbiBfY2FsbGVlMigpIHsKICAgICAgICByZXR1cm4gX3JlZ2VuZXJhdG9yUnVudGltZS53cmFwKGZ1bmN0aW9uIF9jYWxsZWUyJChfY29udGV4dDIpIHsKICAgICAgICAgIHdoaWxlICgxKSB7CiAgICAgICAgICAgIHN3aXRjaCAoX2NvbnRleHQyLnByZXYgPSBfY29udGV4dDIubmV4dCkgewogICAgICAgICAgICAgIGNhc2UgMDoKICAgICAgICAgICAgICAgIGlmIChoYXNNb3VudGVkT25jZSkgewogICAgICAgICAgICAgICAgICAvLyBlc2xpbnQtZGlzYWJsZS1uZXh0LWxpbmUgbm8tcGFyYW0tcmVhc3NpZ24KICAgICAgICAgICAgICAgICAgZ2xvYmFsLl9fSU5KRUNURURfUFVCTElDX1BBVEhfQllfUUlBTktVTl9fID0gcHVibGljUGF0aDsKICAgICAgICAgICAgICAgIH0KICAgICAgICAgICAgICBjYXNlIDE6CiAgICAgICAgICAgICAgY2FzZSAiZW5kIjoKICAgICAgICAgICAgICAgIHJldHVybiBfY29udGV4dDIuc3RvcCgpOwogICAgICAgICAgICB9CiAgICAgICAgICB9CiAgICAgICAgfSwgX2NhbGxlZTIpOwogICAgICB9KSkoKTsKICAgIH0sCiAgICBiZWZvcmVVbm1vdW50OiBmdW5jdGlvbiBiZWZvcmVVbm1vdW50KCkgewogICAgICByZXR1cm4gX2FzeW5jVG9HZW5lcmF0b3IoIC8qI19fUFVSRV9fKi9fcmVnZW5lcmF0b3JSdW50aW1lLm1hcmsoZnVuY3Rpb24gX2NhbGxlZTMoKSB7CiAgICAgICAgcmV0dXJuIF9yZWdlbmVyYXRvclJ1bnRpbWUud3JhcChmdW5jdGlvbiBfY2FsbGVlMyQoX2NvbnRleHQzKSB7CiAgICAgICAgICB3aGlsZSAoMSkgewogICAgICAgICAgICBzd2l0Y2ggKF9jb250ZXh0My5wcmV2ID0gX2NvbnRleHQzLm5leHQpIHsKICAgICAgICAgICAgICBjYXNlIDA6CiAgICAgICAgICAgICAgICBpZiAocmF3UHVibGljUGF0aCA9PT0gdW5kZWZpbmVkKSB7CiAgICAgICAgICAgICAgICAgIC8vIGVzbGludC1kaXNhYmxlLW5leHQtbGluZSBuby1wYXJhbS1yZWFzc2lnbgogICAgICAgICAgICAgICAgICBkZWxldGUgZ2xvYmFsLl9fSU5KRUNURURfUFVCTElDX1BBVEhfQllfUUlBTktVTl9fOwogICAgICAgICAgICAgICAgfSBlbHNlIHsKICAgICAgICAgICAgICAgICAgLy8gZXNsaW50LWRpc2FibGUtbmV4dC1saW5lIG5vLXBhcmFtLXJlYXNzaWduCiAgICAgICAgICAgICAgICAgIGdsb2JhbC5fX0lOSkVDVEVEX1BVQkxJQ19QQVRIX0JZX1FJQU5LVU5fXyA9IHJhd1B1YmxpY1BhdGg7CiAgICAgICAgICAgICAgICB9CiAgICAgICAgICAgICAgICBoYXNNb3VudGVkT25jZSA9IHRydWU7CiAgICAgICAgICAgICAgY2FzZSAyOgogICAgICAgICAgICAgIGNhc2UgImVuZCI6CiAgICAgICAgICAgICAgICByZXR1cm4gX2NvbnRleHQzLnN0b3AoKTsKICAgICAgICAgICAgfQogICAgICAgICAgfQogICAgICAgIH0sIF9jYWxsZWUzKTsKICAgICAgfSkpKCk7CiAgICB9CiAgfTsKfQ=="}, {"version": 3, "names": ["_asyncToGenerator", "_regeneratorRuntime", "rawPublicPath", "window", "__INJECTED_PUBLIC_PATH_BY_QIANKUN__", "getAddOn", "global", "publicPath", "arguments", "length", "undefined", "hasMountedOnce", "beforeLoad", "mark", "_callee", "wrap", "_callee$", "_context", "prev", "next", "stop", "beforeMount", "_callee2", "_callee2$", "_context2", "beforeUnmount", "_callee3", "_callee3$", "_context3"], "sources": ["D:/1_Project/XYD_Project/dop-4.0/dop-4.1-qianduan-unify/dopUnify/node_modules/qiankun/es/addons/runtimePublicPath.js"], "sourcesContent": ["import _asyncToGenerator from \"@babel/runtime/helpers/esm/asyncToGenerator\";\nimport _regeneratorRuntime from \"@babel/runtime/regenerator\";\nvar rawPublicPath = window.__INJECTED_PUBLIC_PATH_BY_QIANKUN__;\nexport default function getAddOn(global) {\n  var publicPath = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : '/';\n  var hasMountedOnce = false;\n  return {\n    beforeLoad: function beforeLoad() {\n      return _asyncToGenerator( /*#__PURE__*/_regeneratorRuntime.mark(function _callee() {\n        return _regeneratorRuntime.wrap(function _callee$(_context) {\n          while (1) {\n            switch (_context.prev = _context.next) {\n              case 0:\n                // eslint-disable-next-line no-param-reassign\n                global.__INJECTED_PUBLIC_PATH_BY_QIANKUN__ = publicPath;\n              case 1:\n              case \"end\":\n                return _context.stop();\n            }\n          }\n        }, _callee);\n      }))();\n    },\n    beforeMount: function beforeMount() {\n      return _asyncToGenerator( /*#__PURE__*/_regeneratorRuntime.mark(function _callee2() {\n        return _regeneratorRuntime.wrap(function _callee2$(_context2) {\n          while (1) {\n            switch (_context2.prev = _context2.next) {\n              case 0:\n                if (hasMountedOnce) {\n                  // eslint-disable-next-line no-param-reassign\n                  global.__INJECTED_PUBLIC_PATH_BY_QIANKUN__ = publicPath;\n                }\n              case 1:\n              case \"end\":\n                return _context2.stop();\n            }\n          }\n        }, _callee2);\n      }))();\n    },\n    beforeUnmount: function beforeUnmount() {\n      return _asyncToGenerator( /*#__PURE__*/_regeneratorRuntime.mark(function _callee3() {\n        return _regeneratorRuntime.wrap(function _callee3$(_context3) {\n          while (1) {\n            switch (_context3.prev = _context3.next) {\n              case 0:\n                if (rawPublicPath === undefined) {\n                  // eslint-disable-next-line no-param-reassign\n                  delete global.__INJECTED_PUBLIC_PATH_BY_QIANKUN__;\n                } else {\n                  // eslint-disable-next-line no-param-reassign\n                  global.__INJECTED_PUBLIC_PATH_BY_QIANKUN__ = rawPublicPath;\n                }\n                hasMountedOnce = true;\n              case 2:\n              case \"end\":\n                return _context3.stop();\n            }\n          }\n        }, _callee3);\n      }))();\n    }\n  };\n}"], "mappings": "AAAA,OAAOA,iBAAiB,MAAM,6CAA6C;AAC3E,OAAOC,mBAAmB,MAAM,4BAA4B;AAC5D,IAAIC,aAAa,GAAGC,MAAM,CAACC,mCAAmC;AAC9D,eAAe,SAASC,QAAQ,CAACC,MAAM,EAAE;EACvC,IAAIC,UAAU,GAAGC,SAAS,CAACC,MAAM,GAAG,CAAC,IAAID,SAAS,CAAC,CAAC,CAAC,KAAKE,SAAS,GAAGF,SAAS,CAAC,CAAC,CAAC,GAAG,GAAG;EACxF,IAAIG,cAAc,GAAG,KAAK;EAC1B,OAAO;IACLC,UAAU,EAAE,SAASA,UAAU,GAAG;MAChC,OAAOZ,iBAAiB,EAAE,aAAaC,mBAAmB,CAACY,IAAI,CAAC,SAASC,OAAO,GAAG;QACjF,OAAOb,mBAAmB,CAACc,IAAI,CAAC,SAASC,QAAQ,CAACC,QAAQ,EAAE;UAC1D,OAAO,CAAC,EAAE;YACR,QAAQA,QAAQ,CAACC,IAAI,GAAGD,QAAQ,CAACE,IAAI;cACnC,KAAK,CAAC;gBACJ;gBACAb,MAAM,CAACF,mCAAmC,GAAGG,UAAU;cACzD,KAAK,CAAC;cACN,KAAK,KAAK;gBACR,OAAOU,QAAQ,CAACG,IAAI,EAAE;YAAC;UAE7B;QACF,CAAC,EAAEN,OAAO,CAAC;MACb,CAAC,CAAC,CAAC,EAAE;IACP,CAAC;IACDO,WAAW,EAAE,SAASA,WAAW,GAAG;MAClC,OAAOrB,iBAAiB,EAAE,aAAaC,mBAAmB,CAACY,IAAI,CAAC,SAASS,QAAQ,GAAG;QAClF,OAAOrB,mBAAmB,CAACc,IAAI,CAAC,SAASQ,SAAS,CAACC,SAAS,EAAE;UAC5D,OAAO,CAAC,EAAE;YACR,QAAQA,SAAS,CAACN,IAAI,GAAGM,SAAS,CAACL,IAAI;cACrC,KAAK,CAAC;gBACJ,IAAIR,cAAc,EAAE;kBAClB;kBACAL,MAAM,CAACF,mCAAmC,GAAGG,UAAU;gBACzD;cACF,KAAK,CAAC;cACN,KAAK,KAAK;gBACR,OAAOiB,SAAS,CAACJ,IAAI,EAAE;YAAC;UAE9B;QACF,CAAC,EAAEE,QAAQ,CAAC;MACd,CAAC,CAAC,CAAC,EAAE;IACP,CAAC;IACDG,aAAa,EAAE,SAASA,aAAa,GAAG;MACtC,OAAOzB,iBAAiB,EAAE,aAAaC,mBAAmB,CAACY,IAAI,CAAC,SAASa,QAAQ,GAAG;QAClF,OAAOzB,mBAAmB,CAACc,IAAI,CAAC,SAASY,SAAS,CAACC,SAAS,EAAE;UAC5D,OAAO,CAAC,EAAE;YACR,QAAQA,SAAS,CAACV,IAAI,GAAGU,SAAS,CAACT,IAAI;cACrC,KAAK,CAAC;gBACJ,IAAIjB,aAAa,KAAKQ,SAAS,EAAE;kBAC/B;kBACA,OAAOJ,MAAM,CAACF,mCAAmC;gBACnD,CAAC,MAAM;kBACL;kBACAE,MAAM,CAACF,mCAAmC,GAAGF,aAAa;gBAC5D;gBACAS,cAAc,GAAG,IAAI;cACvB,KAAK,CAAC;cACN,KAAK,KAAK;gBACR,OAAOiB,SAAS,CAACR,IAAI,EAAE;YAAC;UAE9B;QACF,CAAC,EAAEM,QAAQ,CAAC;MACd,CAAC,CAAC,CAAC,EAAE;IACP;EACF,CAAC;AACH"}]}