{"remainingRequest": "D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\node_modules\\babel-loader\\lib\\index.js!D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\node_modules\\eslint-loader\\index.js??ref--13-0!D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\src\\utils\\flowPath.js", "dependencies": [{"path": "D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\src\\utils\\flowPath.js", "mtime": 1686019809779}, {"path": "D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\babel.config.js", "mtime": 1686019818359}, {"path": "D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\.babelrc", "mtime": 1705285078668}, {"path": "D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\node_modules\\eslint-loader\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["MessageBox", "Common", "store", "DataAuditing", "operationRequest", "sysMessageShowQueryDealed", "approvalDetail", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "selectTaskNo", "querystatus", "taskDetail", "taskCheckDetail", "taskHoDetail", "taskHoAuditDetail", "FlowPath", "releaseTask", "selectModule", "selectFlowDetailData", "selectAuthorRequestApprovalSystem", "query", "SysApproval", "commonBlank", "releaseFlowTask", "bindTaskId", "msg", "parameterList", "sysMap", "task_id", "user_no", "getters", "userNo", "then", "res", "retCode", "getTaskNodePath", "data", "msg_parameter", "inst_id", "item_id", "module_id", "module_path", "deal_state", "node_id", "retMap", "flow_node_config", "combine_form_type", "node_config", "JSON", "parse", "key", "node_msg_list", "node_name", "node_module_path", "alert", "openTaskPageByNodePath", "show_url", "sysMessageOperationRequestShow", "openNoticeAppro", "sysMsgSublicenseMan", "openRiskPrecludeDialog", "openRiskTaskDialog", "openRiskPrecludeHoTaskDialog", "openRiskApprovalHoTaskDialog", "title", "confirmButtonText", "organ_no", "organNo", "organ_type", "organLevel", "role_no", "roleNo", "currentPage", "method", "pageSize", "response", "list", "quaryType", "length", "sysMsgSublicenseMOudle", "list_data", "sublicense_id", "detailArr", "push", "select_inst_id", "sysMessageOperationRequestDeatilShow", "approve_agree", "haveRight", "commonAOSJudgeRightForButton", "menuId", "buttonId", "varMapList", "btnAuditType", "menuAuditData", "i", "task_no_list", "msg2", "task_no", "response2", "msg3", "taskNo", "response3", "retMsg", "flag"], "sources": ["D:/1_Project/XYD_Project/dop-4.0/dop-4.1-qianduan-unify/dopUnify/src/utils/flowPath.js"], "sourcesContent": ["/**\r\n *系统消息、数据审核工作台、菜单审核配置、流程相关公共方法\r\n */\r\nimport { MessageBox } from 'element-ui'\r\nimport { Common } from '@/api'\r\nimport store from '@/store'\r\nconst {\r\n  operationRequest,\r\n  sysMessageShowQueryDealed,\r\n  approvalDetail,\r\n  judgeRightForButton,\r\n  selectTaskNo,\r\n  querystatus,\r\n  taskDetail,\r\n  taskCheckDetail,\r\n  taskHoDetail,\r\n  taskHoAuditDetail\r\n} = Common.DataAuditing\r\n\r\nconst {\r\n  releaseTask,\r\n  selectModule,\r\n  selectFlowDetailData,\r\n  selectAuthorRequestApprovalSystem\r\n} = Common.FlowPath\r\nconst { query } = Common.SysApproval\r\n\r\nimport { commonBlank } from '@/utils/common'\r\n\r\n/**\r\n * 释放处理中流程任务，解绑处理人\r\n * @param bindTaskId 处理中绑定的任务编号\r\n */\r\nexport function releaseFlowTask(bindTaskId) {\r\n  const msg = {\r\n    parameterList: [],\r\n    sysMap: {\r\n      task_id: bindTaskId,\r\n      user_no: store.getters.userNo\r\n    }\r\n  }\r\n  releaseTask(msg).then((res) => {\r\n    if (res.retCode === '200') {\r\n      // console.log('任务编号【' + bindTaskId + '】释放任务成功！')\r\n    } else {\r\n      // console.log('任务编号【' + bindTaskId + '】释放任务失败：' + res.retMsg)\r\n    }\r\n  })\r\n}\r\n\r\n/**\r\n * 获取任务节点路径\r\n * @param data 流程待审批参数\r\n */\r\nexport async function getTaskNodePath(data) {\r\n  const { inst_id, item_id, module_id, module_path } = data.msg_parameter\r\n  var msg = {\r\n    parameterList: [{}],\r\n    sysMap: {\r\n      user_no: store.getters.userNo,\r\n      inst_id: inst_id, // 流水id\r\n      task_id: item_id, // 明细id\r\n      module_id: module_id, // 模板id\r\n      deal_state: data.deal_state // 处理状态\r\n    }\r\n  }\r\n\r\n  try {\r\n    const res = await selectModule(msg)\r\n    if (res.retCode === '200') {\r\n      // 当前节点号\r\n      const node_id = res.retMap.node_id\r\n      // 节点表单配置信息\r\n      const flow_node_config = res.retMap.flow_node_config\r\n      // 关联表单方式\r\n      const combine_form_type = res.retMap.combine_form_type\r\n\r\n      if (!commonBlank(flow_node_config)) {\r\n        const node_config = JSON.parse(flow_node_config)\r\n        // 流程节点信息数组\r\n        // let node_msg_list\r\n        // 遍历json 获取对应的key所对应的值\r\n        for (const key in node_config) {\r\n          if (key === node_id) {\r\n            const node_msg_list = node_config[key]\r\n            // 判断是否取不到对应信息 取不到给默认\r\n            if (!commonBlank(node_msg_list)) {\r\n              // 流程节点名称\r\n              data.node_name = node_msg_list[0]\r\n              // 流程节点模板路径\r\n              data.node_module_path = node_msg_list[1]\r\n            } else {\r\n              data.node_module_path = 'default'\r\n            }\r\n            // 为null或default则给默认模板路径\r\n            if (\r\n              commonBlank(data.node_module_path) ||\r\n              data.node_module_path === 'default'\r\n            ) {\r\n              data.node_module_path = module_path\r\n            }\r\n          }\r\n        }\r\n      }\r\n      // 根据关联表单方式打开\r\n      if (combine_form_type === '1') {\r\n        alert('自定义表单相关功能待开发！')\r\n      }\r\n    }\r\n  } catch (err) {\r\n    data = {}\r\n  }\r\n  return data\r\n}\r\n\r\n/** 系统消息页面\r\n * 非流程插件审批/回显处理\r\n * 根据配置节点路径进行指定审批/回显操作\r\n * @param data 流程数据( 当前行数据)\r\n * @param deal_state 0-未处理/1-已处理\r\n * ps: 关闭界面时需要清除流程缓存 localObject.workflow_dataStr = null;\r\n */\r\nexport function openTaskPageByNodePath(data, deal_state) {\r\n  // 查看操作详情--非流程插件审批/查看\r\n  // 流水号\r\n  const inst_id = data.msg_parameter.inst_id\r\n  // 节点模板路径\r\n  // const show_url = data.msg_parameter.module_path\r\n  const show_url = data.node_module_path\r\n  // 节点名称\r\n  // const node_name = data.node_name // 或者data.msg_parameter.node_name目前无测试数据不知道是哪一个值\r\n  /** 开户相关审批、查看界面 */\r\n  if (\r\n    show_url === 'static/html/account/open/dialog/dialogAccReview.html' || // 复审不通过审批\r\n    show_url === 'static/html/account/open/dialog/dialogAccRecord.html' // 补录\r\n  ) {\r\n    // 打开开户审核页面\r\n    // 开户审核\r\n    // openCheckDialog(data, deal_state, show_url, node_name)\r\n    // console.log(222)\r\n  } else if (\r\n    /** 数据审核审批、查看界面 */\r\n    show_url === 'static/html/account/open/dialog/dialogAccAudit.html'\r\n  ) {\r\n    // console.log(222)\r\n  } else if (\r\n    /** 数据审核审批、查看界面 */\r\n    show_url ===\r\n    'static/html/system/operationRequest/operationRequestWorkbench.html'\r\n  ) {\r\n    // 打开数据审核页面  // 未处理页面/回显页面\r\n    return sysMessageOperationRequestShow(inst_id, deal_state)\r\n  } else if (\r\n    /** 公告审批、查看界面 */\r\n    show_url === 'static/js/action/system/noticeManage/noticeDialogApprove.js'\r\n  ) {\r\n    // 调用公告弹窗方法\r\n    // 查询公告明细\r\n    return openNoticeAppro(\r\n      data.msg_parameter.inst_id,\r\n      data.msg_parameter.item_id\r\n    )\r\n  } else if (\r\n    /** 转授权审批、查看界面 */\r\n    show_url === 'static/js/action/index/author/authorRequestDialog.js'\r\n  ) {\r\n    // alert('转授权流程接口后台未提供')\r\n    // 打开数据审核页面 // 加载转授权审批/回显页面\r\n    return sysMsgSublicenseMan(inst_id, deal_state)\r\n  } else if (\r\n    /** 风险排查网点核查，弹框页面 */\r\n    show_url === 'static/js/action/account/risk/riskPrecludeDialog.js'\r\n  ) {\r\n    return openRiskPrecludeDialog(inst_id, deal_state)\r\n  } else if (\r\n    /** 风险排查网点审核，弹框页面 */\r\n    show_url === 'static/js/action/account/risk/riskTasklistApprovalDialog.js'\r\n  ) {\r\n    return openRiskTaskDialog(inst_id, deal_state)\r\n  } else if (\r\n    /** 风险排查总行核查，弹框页面 */\r\n    show_url === 'static/js/action/account/risk/riskPrecludeHoCheckDialog.js'\r\n  ) {\r\n    return openRiskPrecludeHoTaskDialog(inst_id, deal_state)\r\n  } else if (\r\n    /** 风险排查总行审核，弹框页面 */\r\n    show_url === 'static/js/action/account/risk/riskApprovalHoDialog.js'\r\n  ) {\r\n    return openRiskApprovalHoTaskDialog(inst_id, deal_state)\r\n  } else {\r\n    MessageBox.alert('当前配置界面暂不支持审批,请检查配置！', {\r\n      title: '提示',\r\n      confirmButtonText: '确定'\r\n    })\r\n    return true\r\n  }\r\n}\r\n/**\r\n * 打开公告弹窗审批- 申请处理界面\r\n * @param inst_id 申请流水id\r\n * @param item_id  任务明细id\r\n */\r\nexport async function openNoticeAppro(inst_id, item_id) {\r\n  const msg = {\r\n    parameterList: [],\r\n    organ_no: store.getters.organNo,\r\n    organ_type: store.getters.organLevel, // 机构级别\r\n    user_no: store.getters.userNo,\r\n    role_no: store.getters.roleNo,\r\n    inst_id: inst_id,\r\n    item_id: item_id,\r\n    currentPage: 1,\r\n    method: 'getTask', // 固定字段值\r\n    pageSize: 10\r\n  }\r\n  const response = await query(msg)\r\n  // query(msg).then((response) => {\r\n  const { list } = response.retMap\r\n  return list\r\n}\r\n\r\n/**\r\n * 转授权- 打开审批/查看操作申请处理界面 判断处理状态\r\n * @param select_inst_id 申请流水号\r\n * @param deal_state  处理状态\r\n */\r\nexport async function sysMsgSublicenseMan(inst_id, deal_state) {\r\n  let quaryType = ''\r\n  if (deal_state === '0') {\r\n    quaryType = '1' // 处理状态是未处理是查询类型是1-我的代办\r\n  } else {\r\n    quaryType = '2' // 处理状态是已处理是查询类型是2-我的已办\r\n  }\r\n  const msg = {\r\n    parameterList: [],\r\n    sysMap: { inst_id: inst_id, deal_state: deal_state }\r\n  }\r\n  const res = await selectAuthorRequestApprovalSystem(msg)\r\n  if (res.retMap.list.length > 0) {\r\n    // 操作详情方法调用\r\n    return sysMsgSublicenseMOudle(res.retMap.list[0], quaryType)\r\n  }\r\n}\r\n\r\n/**\r\n * 打开公告弹窗审批- 申请处理界面\r\n * @param list_data 操作详情数据(数据审核工作台当前行row的数据，系统消息数据是sysMessageOperationRequestShow方法请求回来的list)\r\n * @param quaryType 查询类型 0-我的申请  1-我的代办  2-我的已办\r\n */\r\nexport async function sysMsgSublicenseMOudle(list_data, quaryType) {\r\n  const msg = {\r\n    parameterList: [],\r\n    sysMap: { inst_id: list_data.sublicense_id }\r\n  }\r\n  const res = await selectFlowDetailData(msg)\r\n  const { list } = res.retMap\r\n  const detailArr = []\r\n  detailArr.push(list_data, quaryType, '', list, true) // list弹框表格处理明细\r\n  return detailArr\r\n}\r\n\r\n/**\r\n *增删改查导入导出操作- 打开审批/查看操作申请处理界面\r\n * @param select_inst_id 申请流水号\r\n * @param deal_state  处理状态\r\n */\r\nexport async function sysMessageOperationRequestShow(\r\n  select_inst_id,\r\n  deal_state\r\n) {\r\n  // 系统消息-操作内容-操作详情（流程事项-模板为'static/js/action/system/noticeManage/noticeDialogApprove.js）\r\n  // 先发请求 请求回来表单的数据  再调用方法\r\n  let quaryType = ''\r\n  if (deal_state === '0') {\r\n    quaryType = '1' // 处理状态是未处理是查询类型是1-我的代办\r\n  } else {\r\n    quaryType = '2' // 处理状态是已处理是查询类型是2-我的已办\r\n  }\r\n  const msg = {\r\n    parameterList: [],\r\n    sysMap: {\r\n      inst_id: select_inst_id\r\n    }\r\n  }\r\n  let list = []\r\n  if (deal_state === '0') {\r\n    const res = await operationRequest(msg)\r\n    list = res.retMap.list\r\n  } else {\r\n    const res = await sysMessageShowQueryDealed(msg)\r\n    list = res.retMap.list\r\n  }\r\n  if (list.length > 0) {\r\n    // 操作详情方法调用\r\n    return sysMessageOperationRequestDeatilShow(list[0], quaryType)\r\n  }\r\n}\r\n\r\n/**\r\n * 展示操作申请审批/详情框\r\n * @param list_data 操作详情数据(数据审核工作台当前行row的数据，系统消息数据是sysMessageOperationRequestShow方法请求回来的list)\r\n * @param quaryType 查询类型 0-我的申请  1-我的代办  2-我的已办\r\n */\r\nexport async function sysMessageOperationRequestDeatilShow(\r\n  list_data,\r\n  quaryType\r\n) {\r\n  // 数据审核工作台页面-流水号-操作详情页面直接调用sysMessageOperationRequestDeatilShow方法--------------------\r\n  let bindTaskId = ''\r\n  const detailArr = []\r\n  if (list_data.approve_agree === '2' && quaryType === '1') {\r\n    bindTaskId = list_data.item_id // 绑定任务明细id\r\n  } else {\r\n    bindTaskId = ''\r\n  }\r\n  const msg = {\r\n    parameterList: [],\r\n    sysMap: {\r\n      inst_id: list_data.inst_id,\r\n      bindTaskId: bindTaskId\r\n    }\r\n  }\r\n  const res = await approvalDetail(msg)\r\n  const { haveRight, list } = res.retMap\r\n  detailArr.push(list_data, quaryType, haveRight, list) // list弹框表格处理明细\r\n  return detailArr\r\n}\r\n\r\n/**\r\n * 菜单审核配置匹配页面按钮\r\n * 按钮功能准入禁止判断-----------------------------------------------------------------------------------------\r\n * 缓存存到vuex\r\n * @param   menuId 菜单id\r\n * @param   buttonId 按钮id\r\n * @param   varMapList 进行增删改等操作的当前行数据\r\n */\r\nexport async function commonAOSJudgeRightForButton(\r\n  menuId,\r\n  buttonId,\r\n  varMapList\r\n) {\r\n  var msg = {\r\n    parameterList: [],\r\n    sysMap: {\r\n      menuId: menuId,\r\n      buttonId: buttonId,\r\n      varMapList: varMapList\r\n    }\r\n  }\r\n  const { retCode } = await judgeRightForButton(msg)\r\n  return retCode === '200'\r\n}\r\n\r\n/**\r\n * 获取当前菜单的按钮审核方式\r\n * @param   menuId 菜单id\r\n * @param   buttonId 按钮id\r\n */\r\nexport function btnAuditType(menuId, buttonId) {\r\n  const menuAuditData = store.getters.menuAuditData\r\n  for (const key in menuAuditData) {\r\n    if (key === menuId) {\r\n      for (const i in menuAuditData[key]) {\r\n        if (i === buttonId) {\r\n          return menuAuditData[key][i]\r\n        }\r\n      }\r\n    }\r\n  }\r\n}\r\n/**\r\n * 风险排查网点核查，弹框页面\r\n * @param inst_id 申请流水id\r\n * @param item_id  任务明细id\r\n */\r\nexport async function openRiskPrecludeDialog(inst_id, item_id) {\r\n  const msg = {\r\n    parameterList: [{}],\r\n    inst_id: inst_id\r\n  }\r\n  const response = await selectTaskNo(msg)\r\n  const { task_no_list } = response.retMap\r\n  if (commonBlank(task_no_list)) {\r\n    MessageBox.alert('请选择核实数据!', {\r\n      title: '提示',\r\n      confirmButtonText: '确定'\r\n    })\r\n    return true\r\n  } else {\r\n    const msg2 = {\r\n      parameterList: [{}],\r\n      task_no: task_no_list[0] // 明细id\r\n    }\r\n    const response2 = await querystatus(msg2)\r\n    if (response2.retCode !== '200') {\r\n      MessageBox.alert('此任务已被处理，请重新选择!', {\r\n        title: '提示',\r\n        confirmButtonText: '确定'\r\n      })\r\n      return true\r\n    }\r\n    const msg3 = {\r\n      parameterList: [\r\n        {\r\n          taskNo: task_no_list[0]\r\n        }\r\n      ]\r\n    }\r\n    const response3 = await taskDetail(msg3)\r\n    if (response3.retCode === '200') {\r\n      return [response3.retMap, task_no_list]\r\n    } else {\r\n      MessageBox.alert(response3.retMsg, {\r\n        title: '提示',\r\n        confirmButtonText: '确定'\r\n      })\r\n      return true\r\n    }\r\n  }\r\n}\r\n/**\r\n * 风险排查网点审核，弹框页面\r\n * @param inst_id 申请流水id\r\n * @param item_id  任务明细id\r\n */\r\nexport async function openRiskTaskDialog(inst_id, item_id) {\r\n  const msg = {\r\n    parameterList: [{}],\r\n    inst_id: inst_id\r\n  }\r\n  const response = await selectTaskNo(msg)\r\n  const { task_no_list } = response.retMap\r\n  if (commonBlank(task_no_list)) {\r\n    MessageBox.alert('请选择核实数据!', {\r\n      title: '提示',\r\n      confirmButtonText: '确定'\r\n    })\r\n    return true\r\n  } else {\r\n    const msg2 = {\r\n      parameterList: [{}],\r\n      task_no: task_no_list[0] // 明细id\r\n    }\r\n    const response2 = await querystatus(msg2)\r\n    if (response2.retCode !== '200') {\r\n      MessageBox.alert('此任务已被处理，请重新选择!', {\r\n        title: '提示',\r\n        confirmButtonText: '确定'\r\n      })\r\n      return true\r\n    }\r\n    const msg3 = {\r\n      parameterList: [{}],\r\n      task_no: task_no_list[0]\r\n    }\r\n    const response3 = await taskCheckDetail(msg3)\r\n    if (response3.retCode === '200') {\r\n      return [response3.retMap, task_no_list]\r\n    } else {\r\n      MessageBox.alert(response3.retMsg, {\r\n        title: '提示',\r\n        confirmButtonText: '确定'\r\n      })\r\n      return true\r\n    }\r\n  }\r\n}\r\n/**\r\n * 风险排查总行核查，弹框页面\r\n * @param inst_id 申请流水id\r\n * @param item_id  任务明细id\r\n */\r\nexport async function openRiskPrecludeHoTaskDialog(inst_id, item_id) {\r\n  const msg = {\r\n    parameterList: [{}],\r\n    inst_id: inst_id\r\n  }\r\n  const response = await selectTaskNo(msg)\r\n  const { task_no_list } = response.retMap\r\n  if (commonBlank(task_no_list)) {\r\n    MessageBox.alert('请选择核实数据!', {\r\n      title: '提示',\r\n      confirmButtonText: '确定'\r\n    })\r\n    return true\r\n  } else {\r\n    const msg2 = {\r\n      parameterList: [{}],\r\n      task_no: task_no_list[0] // 明细id\r\n    }\r\n    const response2 = await querystatus(msg2)\r\n    if (response2.retCode !== '200') {\r\n      MessageBox.alert('此任务已被处理，请重新选择!', {\r\n        title: '提示',\r\n        confirmButtonText: '确定'\r\n      })\r\n      return true\r\n    }\r\n    const msg3 = {\r\n      parameterList: [{ taskNo: task_no_list[0] }],\r\n      task_no: task_no_list[0]\r\n    }\r\n    const response3 = await taskHoDetail(msg3)\r\n    if (response3.retMap.flag !== true) {\r\n      MessageBox.alert('此任务已有人处理，请重新选择！', {\r\n        title: '提示',\r\n        confirmButtonText: '确定'\r\n      })\r\n      return true\r\n    }\r\n    if (response3.retCode === '200') {\r\n      return [response3.retMap, task_no_list]\r\n    } else {\r\n      MessageBox.alert(response3.retMsg, {\r\n        title: '提示',\r\n        confirmButtonText: '确定'\r\n      })\r\n      return true\r\n    }\r\n  }\r\n}\r\n/**\r\n * 风险排查总行审核，弹框页面\r\n * @param inst_id 申请流水id\r\n * @param item_id  任务明细id\r\n */\r\nexport async function openRiskApprovalHoTaskDialog(inst_id, item_id) {\r\n  const msg = {\r\n    parameterList: [{}],\r\n    inst_id: inst_id\r\n  }\r\n  const response = await selectTaskNo(msg)\r\n  const { task_no_list } = response.retMap\r\n  if (commonBlank(task_no_list)) {\r\n    MessageBox.alert('请选择核实数据!', {\r\n      title: '提示',\r\n      confirmButtonText: '确定'\r\n    })\r\n    return true\r\n  } else {\r\n    const msg2 = {\r\n      parameterList: [{}],\r\n      task_no: task_no_list[0] // 明细id\r\n    }\r\n    const response2 = await querystatus(msg2)\r\n    if (response2.retCode !== '200') {\r\n      MessageBox.alert('此任务已被处理，请重新选择!', {\r\n        title: '提示',\r\n        confirmButtonText: '确定'\r\n      })\r\n      return true\r\n    }\r\n    const msg3 = {\r\n      parameterList: [{ taskNo: task_no_list[0] }],\r\n      task_no: task_no_list[0]\r\n    }\r\n    const response3 = await taskHoAuditDetail(msg3)\r\n    if (response3.retMap.flag !== true) {\r\n      MessageBox.alert('此任务已有人处理，请重新选择！', {\r\n        title: '提示',\r\n        confirmButtonText: '确定'\r\n      })\r\n      return true\r\n    }\r\n    if (response3.retCode === '200') {\r\n      return [response3.retMap, task_no_list]\r\n    } else {\r\n      MessageBox.alert(response3.retMsg, {\r\n        title: '提示',\r\n        confirmButtonText: '确定'\r\n      })\r\n      return true\r\n    }\r\n  }\r\n}\r\n"], "mappings": ";;AAAA;AACA;AACA;AACA,SAASA,UAAU,QAAQ,YAAY;AACvC,SAASC,MAAM,QAAQ,OAAO;AAC9B,OAAOC,KAAK,MAAM,SAAS;AAC3B,2BAWID,MAAM,CAACE,YAAY;EAVrBC,gBAAgB,wBAAhBA,gBAAgB;EAChBC,yBAAyB,wBAAzBA,yBAAyB;EACzBC,cAAc,wBAAdA,cAAc;EACdC,mBAAmB,wBAAnBA,mBAAmB;EACnBC,YAAY,wBAAZA,YAAY;EACZC,WAAW,wBAAXA,WAAW;EACXC,UAAU,wBAAVA,UAAU;EACVC,eAAe,wBAAfA,eAAe;EACfC,YAAY,wBAAZA,YAAY;EACZC,iBAAiB,wBAAjBA,iBAAiB;AAGnB,uBAKIZ,MAAM,CAACa,QAAQ;EAJjBC,WAAW,oBAAXA,WAAW;EACXC,YAAY,oBAAZA,YAAY;EACZC,oBAAoB,oBAApBA,oBAAoB;EACpBC,iCAAiC,oBAAjCA,iCAAiC;AAEnC,IAAQC,KAAK,GAAKlB,MAAM,CAACmB,WAAW,CAA5BD,KAAK;AAEb,SAASE,WAAW,QAAQ,gBAAgB;;AAE5C;AACA;AACA;AACA;AACA,OAAO,SAASC,eAAe,CAACC,UAAU,EAAE;EAC1C,IAAMC,GAAG,GAAG;IACVC,aAAa,EAAE,EAAE;IACjBC,MAAM,EAAE;MACNC,OAAO,EAAEJ,UAAU;MACnBK,OAAO,EAAE1B,KAAK,CAAC2B,OAAO,CAACC;IACzB;EACF,CAAC;EACDf,WAAW,CAACS,GAAG,CAAC,CAACO,IAAI,CAAC,UAACC,GAAG,EAAK;IAC7B,IAAIA,GAAG,CAACC,OAAO,KAAK,KAAK,EAAE;MACzB;IAAA,CACD,MAAM;MACL;IAAA;EAEJ,CAAC,CAAC;AACJ;;AAEA;AACA;AACA;AACA;AACA,gBAAsBC,eAAe;EAAA;AAAA;;AA6DrC;AACA;AACA;AACA;AACA;AACA;AACA;AANA;EAAA,8EA7DO,iBAA+BC,IAAI;IAAA;IAAA;MAAA;QAAA;UAAA;YAAA,sBACaA,IAAI,CAACC,aAAa,EAA/DC,OAAO,uBAAPA,OAAO,EAAEC,OAAO,uBAAPA,OAAO,EAAEC,SAAS,uBAATA,SAAS,EAAEC,WAAW,uBAAXA,WAAW;YAC5ChB,GAAG,GAAG;cACRC,aAAa,EAAE,CAAC,CAAC,CAAC,CAAC;cACnBC,MAAM,EAAE;gBACNE,OAAO,EAAE1B,KAAK,CAAC2B,OAAO,CAACC,MAAM;gBAC7BO,OAAO,EAAEA,OAAO;gBAAE;gBAClBV,OAAO,EAAEW,OAAO;gBAAE;gBAClBC,SAAS,EAAEA,SAAS;gBAAE;gBACtBE,UAAU,EAAEN,IAAI,CAACM,UAAU,CAAC;cAC9B;YACF,CAAC;YAAA;YAAA;YAAA,OAGmBzB,YAAY,CAACQ,GAAG,CAAC;UAAA;YAA7BQ,GAAG;YACT,IAAIA,GAAG,CAACC,OAAO,KAAK,KAAK,EAAE;cACzB;cACMS,OAAO,GAAGV,GAAG,CAACW,MAAM,CAACD,OAAO,EAClC;cACME,gBAAgB,GAAGZ,GAAG,CAACW,MAAM,CAACC,gBAAgB,EACpD;cACMC,iBAAiB,GAAGb,GAAG,CAACW,MAAM,CAACE,iBAAiB;cAEtD,IAAI,CAACxB,WAAW,CAACuB,gBAAgB,CAAC,EAAE;gBAC5BE,WAAW,GAAGC,IAAI,CAACC,KAAK,CAACJ,gBAAgB,CAAC,EAChD;gBACA;gBACA;gBACA,KAAWK,GAAG,IAAIH,WAAW,EAAE;kBAC7B,IAAIG,GAAG,KAAKP,OAAO,EAAE;oBACbQ,aAAa,GAAGJ,WAAW,CAACG,GAAG,CAAC,EACtC;oBACA,IAAI,CAAC5B,WAAW,CAAC6B,aAAa,CAAC,EAAE;sBAC/B;sBACAf,IAAI,CAACgB,SAAS,GAAGD,aAAa,CAAC,CAAC,CAAC;sBACjC;sBACAf,IAAI,CAACiB,gBAAgB,GAAGF,aAAa,CAAC,CAAC,CAAC;oBAC1C,CAAC,MAAM;sBACLf,IAAI,CAACiB,gBAAgB,GAAG,SAAS;oBACnC;oBACA;oBACA,IACE/B,WAAW,CAACc,IAAI,CAACiB,gBAAgB,CAAC,IAClCjB,IAAI,CAACiB,gBAAgB,KAAK,SAAS,EACnC;sBACAjB,IAAI,CAACiB,gBAAgB,GAAGZ,WAAW;oBACrC;kBACF;gBACF;cACF;cACA;cACA,IAAIK,iBAAiB,KAAK,GAAG,EAAE;gBAC7BQ,KAAK,CAAC,eAAe,CAAC;cACxB;YACF;YAAC;YAAA;UAAA;YAAA;YAAA;YAEDlB,IAAI,GAAG,CAAC,CAAC;UAAA;YAAA,iCAEJA,IAAI;UAAA;UAAA;YAAA;QAAA;MAAA;IAAA;EAAA,CACZ;EAAA;AAAA;AASD,OAAO,SAASmB,sBAAsB,CAACnB,IAAI,EAAEM,UAAU,EAAE;EACvD;EACA;EACA,IAAMJ,OAAO,GAAGF,IAAI,CAACC,aAAa,CAACC,OAAO;EAC1C;EACA;EACA,IAAMkB,QAAQ,GAAGpB,IAAI,CAACiB,gBAAgB;EACtC;EACA;EACA;EACA,IACEG,QAAQ,KAAK,sDAAsD;EAAI;EACvEA,QAAQ,KAAK,sDAAsD,CAAC;EAAA,EACpE;IACA;IACA;IACA;IACA;EAAA,CACD,MAAM,KACL;EACAA,QAAQ,KAAK,qDAAqD,EAClE;IACA;EAAA,CACD,MAAM,KACL;EACAA,QAAQ,KACR,oEAAoE,EACpE;IACA;IACA,OAAOC,8BAA8B,CAACnB,OAAO,EAAEI,UAAU,CAAC;EAC5D,CAAC,MAAM,KACL;EACAc,QAAQ,KAAK,6DAA6D,EAC1E;IACA;IACA;IACA,OAAOE,eAAe,CACpBtB,IAAI,CAACC,aAAa,CAACC,OAAO,EAC1BF,IAAI,CAACC,aAAa,CAACE,OAAO,CAC3B;EACH,CAAC,MAAM,KACL;EACAiB,QAAQ,KAAK,sDAAsD,EACnE;IACA;IACA;IACA,OAAOG,mBAAmB,CAACrB,OAAO,EAAEI,UAAU,CAAC;EACjD,CAAC,MAAM,KACL;EACAc,QAAQ,KAAK,qDAAqD,EAClE;IACA,OAAOI,sBAAsB,CAACtB,OAAO,EAAEI,UAAU,CAAC;EACpD,CAAC,MAAM,KACL;EACAc,QAAQ,KAAK,6DAA6D,EAC1E;IACA,OAAOK,kBAAkB,CAACvB,OAAO,EAAEI,UAAU,CAAC;EAChD,CAAC,MAAM,KACL;EACAc,QAAQ,KAAK,4DAA4D,EACzE;IACA,OAAOM,4BAA4B,CAACxB,OAAO,EAAEI,UAAU,CAAC;EAC1D,CAAC,MAAM,KACL;EACAc,QAAQ,KAAK,uDAAuD,EACpE;IACA,OAAOO,4BAA4B,CAACzB,OAAO,EAAEI,UAAU,CAAC;EAC1D,CAAC,MAAM;IACLzC,UAAU,CAACqD,KAAK,CAAC,qBAAqB,EAAE;MACtCU,KAAK,EAAE,IAAI;MACXC,iBAAiB,EAAE;IACrB,CAAC,CAAC;IACF,OAAO,IAAI;EACb;AACF;AACA;AACA;AACA;AACA;AACA;AACA,gBAAsBP,eAAe;EAAA;AAAA;;AAmBrC;AACA;AACA;AACA;AACA;AAJA;EAAA,8EAnBO,kBAA+BpB,OAAO,EAAEC,OAAO;IAAA;IAAA;MAAA;QAAA;UAAA;YAC9Cd,GAAG,GAAG;cACVC,aAAa,EAAE,EAAE;cACjBwC,QAAQ,EAAE/D,KAAK,CAAC2B,OAAO,CAACqC,OAAO;cAC/BC,UAAU,EAAEjE,KAAK,CAAC2B,OAAO,CAACuC,UAAU;cAAE;cACtCxC,OAAO,EAAE1B,KAAK,CAAC2B,OAAO,CAACC,MAAM;cAC7BuC,OAAO,EAAEnE,KAAK,CAAC2B,OAAO,CAACyC,MAAM;cAC7BjC,OAAO,EAAEA,OAAO;cAChBC,OAAO,EAAEA,OAAO;cAChBiC,WAAW,EAAE,CAAC;cACdC,MAAM,EAAE,SAAS;cAAE;cACnBC,QAAQ,EAAE;YACZ,CAAC;YAAA;YAAA,OACsBtD,KAAK,CAACK,GAAG,CAAC;UAAA;YAA3BkD,QAAQ;YACd;YACQC,IAAI,GAAKD,QAAQ,CAAC/B,MAAM,CAAxBgC,IAAI;YAAA,kCACLA,IAAI;UAAA;UAAA;YAAA;QAAA;MAAA;IAAA;EAAA,CACZ;EAAA;AAAA;AAOD,gBAAsBjB,mBAAmB;EAAA;AAAA;;AAkBzC;AACA;AACA;AACA;AACA;AAJA;EAAA,kFAlBO,kBAAmCrB,OAAO,EAAEI,UAAU;IAAA;IAAA;MAAA;QAAA;UAAA;YACvDmC,SAAS,GAAG,EAAE;YAClB,IAAInC,UAAU,KAAK,GAAG,EAAE;cACtBmC,SAAS,GAAG,GAAG,EAAC;YAClB,CAAC,MAAM;cACLA,SAAS,GAAG,GAAG,EAAC;YAClB;YACMpD,GAAG,GAAG;cACVC,aAAa,EAAE,EAAE;cACjBC,MAAM,EAAE;gBAAEW,OAAO,EAAEA,OAAO;gBAAEI,UAAU,EAAEA;cAAW;YACrD,CAAC;YAAA;YAAA,OACiBvB,iCAAiC,CAACM,GAAG,CAAC;UAAA;YAAlDQ,GAAG;YAAA,MACLA,GAAG,CAACW,MAAM,CAACgC,IAAI,CAACE,MAAM,GAAG,CAAC;cAAA;cAAA;YAAA;YAAA,kCAErBC,sBAAsB,CAAC9C,GAAG,CAACW,MAAM,CAACgC,IAAI,CAAC,CAAC,CAAC,EAAEC,SAAS,CAAC;UAAA;UAAA;YAAA;QAAA;MAAA;IAAA;EAAA,CAE/D;EAAA;AAAA;AAOD,gBAAsBE,sBAAsB;EAAA;AAAA;;AAY5C;AACA;AACA;AACA;AACA;AAJA;EAAA,qFAZO,kBAAsCC,SAAS,EAAEH,SAAS;IAAA;IAAA;MAAA;QAAA;UAAA;YACzDpD,GAAG,GAAG;cACVC,aAAa,EAAE,EAAE;cACjBC,MAAM,EAAE;gBAAEW,OAAO,EAAE0C,SAAS,CAACC;cAAc;YAC7C,CAAC;YAAA;YAAA,OACiB/D,oBAAoB,CAACO,GAAG,CAAC;UAAA;YAArCQ,GAAG;YACD2C,IAAI,GAAK3C,GAAG,CAACW,MAAM,CAAnBgC,IAAI;YACNM,SAAS,GAAG,EAAE;YACpBA,SAAS,CAACC,IAAI,CAACH,SAAS,EAAEH,SAAS,EAAE,EAAE,EAAED,IAAI,EAAE,IAAI,CAAC,EAAC;YAAA,kCAC9CM,SAAS;UAAA;UAAA;YAAA;QAAA;MAAA;IAAA;EAAA,CACjB;EAAA;AAAA;AAOD,gBAAsBzB,8BAA8B;EAAA;AAAA;;AAgCpD;AACA;AACA;AACA;AACA;AAJA;EAAA,6FAhCO,kBACL2B,cAAc,EACd1C,UAAU;IAAA;IAAA;MAAA;QAAA;UAAA;YAEV;YACA;YACImC,SAAS,GAAG,EAAE;YAClB,IAAInC,UAAU,KAAK,GAAG,EAAE;cACtBmC,SAAS,GAAG,GAAG,EAAC;YAClB,CAAC,MAAM;cACLA,SAAS,GAAG,GAAG,EAAC;YAClB;YACMpD,GAAG,GAAG;cACVC,aAAa,EAAE,EAAE;cACjBC,MAAM,EAAE;gBACNW,OAAO,EAAE8C;cACX;YACF,CAAC;YACGR,IAAI,GAAG,EAAE;YAAA,MACTlC,UAAU,KAAK,GAAG;cAAA;cAAA;YAAA;YAAA;YAAA,OACFrC,gBAAgB,CAACoB,GAAG,CAAC;UAAA;YAAjCQ,GAAG;YACT2C,IAAI,GAAG3C,GAAG,CAACW,MAAM,CAACgC,IAAI;YAAA;YAAA;UAAA;YAAA;YAAA,OAEJtE,yBAAyB,CAACmB,GAAG,CAAC;UAAA;YAA1CQ,IAAG;YACT2C,IAAI,GAAG3C,IAAG,CAACW,MAAM,CAACgC,IAAI;UAAA;YAAA,MAEpBA,IAAI,CAACE,MAAM,GAAG,CAAC;cAAA;cAAA;YAAA;YAAA,kCAEVO,oCAAoC,CAACT,IAAI,CAAC,CAAC,CAAC,EAAEC,SAAS,CAAC;UAAA;UAAA;YAAA;QAAA;MAAA;IAAA;EAAA,CAElE;EAAA;AAAA;AAOD,gBAAsBQ,oCAAoC;EAAA;AAAA;;AAyB1D;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAPA;EAAA,mGAzBO,kBACLL,SAAS,EACTH,SAAS;IAAA;IAAA;MAAA;QAAA;UAAA;YAET;YACIrD,UAAU,GAAG,EAAE;YACb0D,SAAS,GAAG,EAAE;YACpB,IAAIF,SAAS,CAACM,aAAa,KAAK,GAAG,IAAIT,SAAS,KAAK,GAAG,EAAE;cACxDrD,UAAU,GAAGwD,SAAS,CAACzC,OAAO,EAAC;YACjC,CAAC,MAAM;cACLf,UAAU,GAAG,EAAE;YACjB;YACMC,GAAG,GAAG;cACVC,aAAa,EAAE,EAAE;cACjBC,MAAM,EAAE;gBACNW,OAAO,EAAE0C,SAAS,CAAC1C,OAAO;gBAC1Bd,UAAU,EAAEA;cACd;YACF,CAAC;YAAA;YAAA,OACiBjB,cAAc,CAACkB,GAAG,CAAC;UAAA;YAA/BQ,GAAG;YAAA,cACmBA,GAAG,CAACW,MAAM,EAA9B2C,SAAS,eAATA,SAAS,EAAEX,IAAI,eAAJA,IAAI;YACvBM,SAAS,CAACC,IAAI,CAACH,SAAS,EAAEH,SAAS,EAAEU,SAAS,EAAEX,IAAI,CAAC,EAAC;YAAA,kCAC/CM,SAAS;UAAA;UAAA;YAAA;QAAA;MAAA;IAAA;EAAA,CACjB;EAAA;AAAA;AAUD,gBAAsBM,4BAA4B;EAAA;AAAA;;AAiBlD;AACA;AACA;AACA;AACA;AAJA;EAAA,2FAjBO,kBACLC,MAAM,EACNC,QAAQ,EACRC,UAAU;IAAA;IAAA;MAAA;QAAA;UAAA;YAENlE,GAAG,GAAG;cACRC,aAAa,EAAE,EAAE;cACjBC,MAAM,EAAE;gBACN8D,MAAM,EAAEA,MAAM;gBACdC,QAAQ,EAAEA,QAAQ;gBAClBC,UAAU,EAAEA;cACd;YACF,CAAC;YAAA;YAAA,OACyBnF,mBAAmB,CAACiB,GAAG,CAAC;UAAA;YAAA;YAA1CS,OAAO,yBAAPA,OAAO;YAAA,kCACRA,OAAO,KAAK,KAAK;UAAA;UAAA;YAAA;QAAA;MAAA;IAAA;EAAA,CACzB;EAAA;AAAA;AAOD,OAAO,SAAS0D,YAAY,CAACH,MAAM,EAAEC,QAAQ,EAAE;EAC7C,IAAMG,aAAa,GAAG1F,KAAK,CAAC2B,OAAO,CAAC+D,aAAa;EACjD,KAAK,IAAM3C,GAAG,IAAI2C,aAAa,EAAE;IAC/B,IAAI3C,GAAG,KAAKuC,MAAM,EAAE;MAClB,KAAK,IAAMK,CAAC,IAAID,aAAa,CAAC3C,GAAG,CAAC,EAAE;QAClC,IAAI4C,CAAC,KAAKJ,QAAQ,EAAE;UAClB,OAAOG,aAAa,CAAC3C,GAAG,CAAC,CAAC4C,CAAC,CAAC;QAC9B;MACF;IACF;EACF;AACF;AACA;AACA;AACA;AACA;AACA;AACA,gBAAsBlC,sBAAsB;EAAA;AAAA;AA6C5C;AACA;AACA;AACA;AACA;AAJA;EAAA,qFA7CO,kBAAsCtB,OAAO,EAAEC,OAAO;IAAA;IAAA;MAAA;QAAA;UAAA;YACrDd,GAAG,GAAG;cACVC,aAAa,EAAE,CAAC,CAAC,CAAC,CAAC;cACnBY,OAAO,EAAEA;YACX,CAAC;YAAA;YAAA,OACsB7B,YAAY,CAACgB,GAAG,CAAC;UAAA;YAAlCkD,QAAQ;YACNoB,YAAY,GAAKpB,QAAQ,CAAC/B,MAAM,CAAhCmD,YAAY;YAAA,KAChBzE,WAAW,CAACyE,YAAY,CAAC;cAAA;cAAA;YAAA;YAC3B9F,UAAU,CAACqD,KAAK,CAAC,UAAU,EAAE;cAC3BU,KAAK,EAAE,IAAI;cACXC,iBAAiB,EAAE;YACrB,CAAC,CAAC;YAAA,kCACK,IAAI;UAAA;YAEL+B,IAAI,GAAG;cACXtE,aAAa,EAAE,CAAC,CAAC,CAAC,CAAC;cACnBuE,OAAO,EAAEF,YAAY,CAAC,CAAC,CAAC,CAAC;YAC3B,CAAC;YAAA;YAAA,OACuBrF,WAAW,CAACsF,IAAI,CAAC;UAAA;YAAnCE,SAAS;YAAA,MACXA,SAAS,CAAChE,OAAO,KAAK,KAAK;cAAA;cAAA;YAAA;YAC7BjC,UAAU,CAACqD,KAAK,CAAC,gBAAgB,EAAE;cACjCU,KAAK,EAAE,IAAI;cACXC,iBAAiB,EAAE;YACrB,CAAC,CAAC;YAAA,kCACK,IAAI;UAAA;YAEPkC,IAAI,GAAG;cACXzE,aAAa,EAAE,CACb;gBACE0E,MAAM,EAAEL,YAAY,CAAC,CAAC;cACxB,CAAC;YAEL,CAAC;YAAA;YAAA,OACuBpF,UAAU,CAACwF,IAAI,CAAC;UAAA;YAAlCE,SAAS;YAAA,MACXA,SAAS,CAACnE,OAAO,KAAK,KAAK;cAAA;cAAA;YAAA;YAAA,kCACtB,CAACmE,SAAS,CAACzD,MAAM,EAAEmD,YAAY,CAAC;UAAA;YAEvC9F,UAAU,CAACqD,KAAK,CAAC+C,SAAS,CAACC,MAAM,EAAE;cACjCtC,KAAK,EAAE,IAAI;cACXC,iBAAiB,EAAE;YACrB,CAAC,CAAC;YAAA,kCACK,IAAI;UAAA;UAAA;YAAA;QAAA;MAAA;IAAA;EAAA,CAGhB;EAAA;AAAA;AAMD,gBAAsBJ,kBAAkB;EAAA;AAAA;AA0CxC;AACA;AACA;AACA;AACA;AAJA;EAAA,iFA1CO,kBAAkCvB,OAAO,EAAEC,OAAO;IAAA;IAAA;MAAA;QAAA;UAAA;YACjDd,GAAG,GAAG;cACVC,aAAa,EAAE,CAAC,CAAC,CAAC,CAAC;cACnBY,OAAO,EAAEA;YACX,CAAC;YAAA;YAAA,OACsB7B,YAAY,CAACgB,GAAG,CAAC;UAAA;YAAlCkD,QAAQ;YACNoB,YAAY,GAAKpB,QAAQ,CAAC/B,MAAM,CAAhCmD,YAAY;YAAA,KAChBzE,WAAW,CAACyE,YAAY,CAAC;cAAA;cAAA;YAAA;YAC3B9F,UAAU,CAACqD,KAAK,CAAC,UAAU,EAAE;cAC3BU,KAAK,EAAE,IAAI;cACXC,iBAAiB,EAAE;YACrB,CAAC,CAAC;YAAA,kCACK,IAAI;UAAA;YAEL+B,IAAI,GAAG;cACXtE,aAAa,EAAE,CAAC,CAAC,CAAC,CAAC;cACnBuE,OAAO,EAAEF,YAAY,CAAC,CAAC,CAAC,CAAC;YAC3B,CAAC;YAAA;YAAA,OACuBrF,WAAW,CAACsF,IAAI,CAAC;UAAA;YAAnCE,SAAS;YAAA,MACXA,SAAS,CAAChE,OAAO,KAAK,KAAK;cAAA;cAAA;YAAA;YAC7BjC,UAAU,CAACqD,KAAK,CAAC,gBAAgB,EAAE;cACjCU,KAAK,EAAE,IAAI;cACXC,iBAAiB,EAAE;YACrB,CAAC,CAAC;YAAA,kCACK,IAAI;UAAA;YAEPkC,IAAI,GAAG;cACXzE,aAAa,EAAE,CAAC,CAAC,CAAC,CAAC;cACnBuE,OAAO,EAAEF,YAAY,CAAC,CAAC;YACzB,CAAC;YAAA;YAAA,OACuBnF,eAAe,CAACuF,IAAI,CAAC;UAAA;YAAvCE,SAAS;YAAA,MACXA,SAAS,CAACnE,OAAO,KAAK,KAAK;cAAA;cAAA;YAAA;YAAA,kCACtB,CAACmE,SAAS,CAACzD,MAAM,EAAEmD,YAAY,CAAC;UAAA;YAEvC9F,UAAU,CAACqD,KAAK,CAAC+C,SAAS,CAACC,MAAM,EAAE;cACjCtC,KAAK,EAAE,IAAI;cACXC,iBAAiB,EAAE;YACrB,CAAC,CAAC;YAAA,kCACK,IAAI;UAAA;UAAA;YAAA;QAAA;MAAA;IAAA;EAAA,CAGhB;EAAA;AAAA;AAMD,gBAAsBH,4BAA4B;EAAA;AAAA;AAiDlD;AACA;AACA;AACA;AACA;AAJA;EAAA,2FAjDO,mBAA4CxB,OAAO,EAAEC,OAAO;IAAA;IAAA;MAAA;QAAA;UAAA;YAC3Dd,GAAG,GAAG;cACVC,aAAa,EAAE,CAAC,CAAC,CAAC,CAAC;cACnBY,OAAO,EAAEA;YACX,CAAC;YAAA;YAAA,OACsB7B,YAAY,CAACgB,GAAG,CAAC;UAAA;YAAlCkD,QAAQ;YACNoB,YAAY,GAAKpB,QAAQ,CAAC/B,MAAM,CAAhCmD,YAAY;YAAA,KAChBzE,WAAW,CAACyE,YAAY,CAAC;cAAA;cAAA;YAAA;YAC3B9F,UAAU,CAACqD,KAAK,CAAC,UAAU,EAAE;cAC3BU,KAAK,EAAE,IAAI;cACXC,iBAAiB,EAAE;YACrB,CAAC,CAAC;YAAA,mCACK,IAAI;UAAA;YAEL+B,IAAI,GAAG;cACXtE,aAAa,EAAE,CAAC,CAAC,CAAC,CAAC;cACnBuE,OAAO,EAAEF,YAAY,CAAC,CAAC,CAAC,CAAC;YAC3B,CAAC;YAAA;YAAA,OACuBrF,WAAW,CAACsF,IAAI,CAAC;UAAA;YAAnCE,SAAS;YAAA,MACXA,SAAS,CAAChE,OAAO,KAAK,KAAK;cAAA;cAAA;YAAA;YAC7BjC,UAAU,CAACqD,KAAK,CAAC,gBAAgB,EAAE;cACjCU,KAAK,EAAE,IAAI;cACXC,iBAAiB,EAAE;YACrB,CAAC,CAAC;YAAA,mCACK,IAAI;UAAA;YAEPkC,IAAI,GAAG;cACXzE,aAAa,EAAE,CAAC;gBAAE0E,MAAM,EAAEL,YAAY,CAAC,CAAC;cAAE,CAAC,CAAC;cAC5CE,OAAO,EAAEF,YAAY,CAAC,CAAC;YACzB,CAAC;YAAA;YAAA,OACuBlF,YAAY,CAACsF,IAAI,CAAC;UAAA;YAApCE,SAAS;YAAA,MACXA,SAAS,CAACzD,MAAM,CAAC2D,IAAI,KAAK,IAAI;cAAA;cAAA;YAAA;YAChCtG,UAAU,CAACqD,KAAK,CAAC,iBAAiB,EAAE;cAClCU,KAAK,EAAE,IAAI;cACXC,iBAAiB,EAAE;YACrB,CAAC,CAAC;YAAA,mCACK,IAAI;UAAA;YAAA,MAEToC,SAAS,CAACnE,OAAO,KAAK,KAAK;cAAA;cAAA;YAAA;YAAA,mCACtB,CAACmE,SAAS,CAACzD,MAAM,EAAEmD,YAAY,CAAC;UAAA;YAEvC9F,UAAU,CAACqD,KAAK,CAAC+C,SAAS,CAACC,MAAM,EAAE;cACjCtC,KAAK,EAAE,IAAI;cACXC,iBAAiB,EAAE;YACrB,CAAC,CAAC;YAAA,mCACK,IAAI;UAAA;UAAA;YAAA;QAAA;MAAA;IAAA;EAAA,CAGhB;EAAA;AAAA;AAMD,gBAAsBF,4BAA4B;EAAA;AAAA;AAgDjD;EAAA,2FAhDM,mBAA4CzB,OAAO,EAAEC,OAAO;IAAA;IAAA;MAAA;QAAA;UAAA;YAC3Dd,GAAG,GAAG;cACVC,aAAa,EAAE,CAAC,CAAC,CAAC,CAAC;cACnBY,OAAO,EAAEA;YACX,CAAC;YAAA;YAAA,OACsB7B,YAAY,CAACgB,GAAG,CAAC;UAAA;YAAlCkD,QAAQ;YACNoB,YAAY,GAAKpB,QAAQ,CAAC/B,MAAM,CAAhCmD,YAAY;YAAA,KAChBzE,WAAW,CAACyE,YAAY,CAAC;cAAA;cAAA;YAAA;YAC3B9F,UAAU,CAACqD,KAAK,CAAC,UAAU,EAAE;cAC3BU,KAAK,EAAE,IAAI;cACXC,iBAAiB,EAAE;YACrB,CAAC,CAAC;YAAA,mCACK,IAAI;UAAA;YAEL+B,IAAI,GAAG;cACXtE,aAAa,EAAE,CAAC,CAAC,CAAC,CAAC;cACnBuE,OAAO,EAAEF,YAAY,CAAC,CAAC,CAAC,CAAC;YAC3B,CAAC;YAAA;YAAA,OACuBrF,WAAW,CAACsF,IAAI,CAAC;UAAA;YAAnCE,SAAS;YAAA,MACXA,SAAS,CAAChE,OAAO,KAAK,KAAK;cAAA;cAAA;YAAA;YAC7BjC,UAAU,CAACqD,KAAK,CAAC,gBAAgB,EAAE;cACjCU,KAAK,EAAE,IAAI;cACXC,iBAAiB,EAAE;YACrB,CAAC,CAAC;YAAA,mCACK,IAAI;UAAA;YAEPkC,IAAI,GAAG;cACXzE,aAAa,EAAE,CAAC;gBAAE0E,MAAM,EAAEL,YAAY,CAAC,CAAC;cAAE,CAAC,CAAC;cAC5CE,OAAO,EAAEF,YAAY,CAAC,CAAC;YACzB,CAAC;YAAA;YAAA,OACuBjF,iBAAiB,CAACqF,IAAI,CAAC;UAAA;YAAzCE,SAAS;YAAA,MACXA,SAAS,CAACzD,MAAM,CAAC2D,IAAI,KAAK,IAAI;cAAA;cAAA;YAAA;YAChCtG,UAAU,CAACqD,KAAK,CAAC,iBAAiB,EAAE;cAClCU,KAAK,EAAE,IAAI;cACXC,iBAAiB,EAAE;YACrB,CAAC,CAAC;YAAA,mCACK,IAAI;UAAA;YAAA,MAEToC,SAAS,CAACnE,OAAO,KAAK,KAAK;cAAA;cAAA;YAAA;YAAA,mCACtB,CAACmE,SAAS,CAACzD,MAAM,EAAEmD,YAAY,CAAC;UAAA;YAEvC9F,UAAU,CAACqD,KAAK,CAAC+C,SAAS,CAACC,MAAM,EAAE;cACjCtC,KAAK,EAAE,IAAI;cACXC,iBAAiB,EAAE;YACrB,CAAC,CAAC;YAAA,mCACK,IAAI;UAAA;UAAA;YAAA;QAAA;MAAA;IAAA;EAAA,CAGhB;EAAA;AAAA"}]}