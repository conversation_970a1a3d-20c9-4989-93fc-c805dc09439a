{"remainingRequest": "E:\\华南区项目\\中国银行\\开发区\\科技公司运营平台\\前端\\dopUnify\\node_modules\\thread-loader\\dist\\cjs.js!E:\\华南区项目\\中国银行\\开发区\\科技公司运营平台\\前端\\dopUnify\\node_modules\\babel-loader\\lib\\index.js!E:\\华南区项目\\中国银行\\开发区\\科技公司运营平台\\前端\\dopUnify\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!E:\\华南区项目\\中国银行\\开发区\\科技公司运营平台\\前端\\dopUnify\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\华南区项目\\中国银行\\开发区\\科技公司运营平台\\前端\\dopUnify\\src\\views\\login\\index.vue?vue&type=script&lang=js&", "dependencies": [{"path": "E:\\华南区项目\\中国银行\\开发区\\科技公司运营平台\\前端\\dopUnify\\src\\views\\login\\index.vue", "mtime": 1752803049169}, {"path": "E:\\华南区项目\\中国银行\\开发区\\科技公司运营平台\\前端\\dopUnify\\babel.config.js", "mtime": 1721631954662}, {"path": "E:\\华南区项目\\中国银行\\开发区\\科技公司运营平台\\前端\\dopUnify\\.babelrc", "mtime": 1721631954662}, {"path": "E:\\华南区项目\\中国银行\\开发区\\科技公司运营平台\\前端\\dopUnify\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "E:\\华南区项目\\中国银行\\开发区\\科技公司运营平台\\前端\\dopUnify\\node_modules\\thread-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "E:\\华南区项目\\中国银行\\开发区\\科技公司运营平台\\前端\\dopUnify\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "E:\\华南区项目\\中国银行\\开发区\\科技公司运营平台\\前端\\dopUnify\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "E:\\华南区项目\\中国银行\\开发区\\科技公司运营平台\\前端\\dopUnify\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1655715099000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, null]}