{"remainingRequest": "E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1\\数字运营平台-统一门户工程\\dopUnify\\node_modules\\babel-loader\\lib\\index.js!E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1\\数字运营平台-统一门户工程\\dopUnify\\node_modules\\eslint-loader\\index.js??ref--13-0!E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1\\数字运营平台-统一门户工程\\dopUnify\\src\\views\\system\\outManage\\system\\info.js", "dependencies": [{"path": "E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1\\数字运营平台-统一门户工程\\dopUnify\\src\\views\\system\\outManage\\system\\info.js", "mtime": 1716875178858}, {"path": "E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1\\数字运营平台-统一门户工程\\dopUnify\\babel.config.js", "mtime": 1716875191548}, {"path": "E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1\\数字运营平台-统一门户工程\\dopUnify\\.babelrc", "mtime": 1716875191502}, {"path": "E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1\\数字运营平台-统一门户工程\\dopUnify\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1\\数字运营平台-统一门户工程\\dopUnify\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1\\数字运营平台-统一门户工程\\dopUnify\\node_modules\\eslint-loader\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:aW1wb3J0IHsgZGljdGlvbmFyeUZpZWRzIH0gZnJvbSAnQC91dGlscy9kaWN0aW9uYXJ5JzsgLy8g5a2X5YW46YWN572uCgovLyDooajljZUKZXhwb3J0IHZhciBjb25maWcgPSBmdW5jdGlvbiBjb25maWcodGhhdCkgewogIHJldHVybiB7CiAgICBzeXNfbmFtZTogewogICAgICBjb21wb25lbnQ6ICdpbnB1dCcsCiAgICAgIGxhYmVsOiAn57O757uf5ZCN56ewJywKICAgICAgY29sU3BhbjogOCwKICAgICAgbmFtZTogJ3N5c19uYW1lJywKICAgICAgY29uZmlnOiB7CiAgICAgICAgcnVsZXM6IFt7CiAgICAgICAgICBtaW46IDAsCiAgICAgICAgICBtYXg6IDMwLAogICAgICAgICAgbWVzc2FnZTogJ+ivt+acgOWkmuWhq+WGmTMw5Liq5a2X56ymJwogICAgICAgIH1dCiAgICAgIH0sCiAgICAgIGNvbXBvbmVudFByb3BzOiB7CiAgICAgICAgLy8gaW5wdXTnu4Tku7bphY3nva4KICAgICAgICBjbGVhcmFibGU6IHRydWUsCiAgICAgICAgcGxhY2Vob2xkZXI6ICfmlK/mjIHns7vnu5/lkI3np7DmqKHns4rmn6Xor6InCiAgICAgIH0KICAgIH0sCiAgICBzeXNfaWQ6IHsKICAgICAgY29tcG9uZW50OiAnaW5wdXQnLAogICAgICBsYWJlbDogJ+ezu+e7n+agh+ivhicsCiAgICAgIGNvbFNwYW46IDgsCiAgICAgIG5hbWU6ICdzeXNfaWQnLAogICAgICBjb21wb25lbnRQcm9wczogewogICAgICAgIGNsZWFyYWJsZTogdHJ1ZSwKICAgICAgICBwbGFjZWhvbGRlcjogJ+aUr+aMgeezu+e7n+agh+ivhuaooeeziuafpeivoicKICAgICAgfQogICAgfSwKICAgIGlzX29wZW46IHsKICAgICAgY29tcG9uZW50OiAnc2VsZWN0JywKICAgICAgbGFiZWw6ICflkK/nlKjmoIflv5cnLAogICAgICBjb2xTcGFuOiA4LAogICAgICBuYW1lOiAnaXNfb3BlbicsCiAgICAgIGNvbmZpZzogewogICAgICAgIHJ1bGVzOiBbewogICAgICAgICAgbWluOiAwLAogICAgICAgICAgbWF4OiAxLAogICAgICAgICAgbWVzc2FnZTogJ+ivt+acgOWkmuWhq+WGmTHkuKrlrZfnrKYnCiAgICAgICAgfV0KICAgICAgfSwKICAgICAgY29tcG9uZW50UHJvcHM6IHsKICAgICAgICBwbGFjZWhvbGRlcjogJ+ivt+mAieaLqScsCiAgICAgICAgY2xlYXJhYmxlOiB0cnVlCiAgICAgIH0sCiAgICAgIG9wdGlvbnM6IGRpY3Rpb25hcnlGaWVkcygnSVNfT1BFTicpCiAgICB9CiAgfTsKfTs="}, {"version": 3, "names": ["dictionaryFieds", "config", "that", "sys_name", "component", "label", "colSpan", "name", "rules", "min", "max", "message", "componentProps", "clearable", "placeholder", "sys_id", "is_open", "options"], "sources": ["E:/1_Project/XYD_Project/dop-4.0/dop-4.1/数字运营平台-统一门户工程/dopUnify/src/views/system/outManage/system/info.js"], "sourcesContent": ["import { dictionaryFieds } from '@/utils/dictionary' // 字典配置\r\n\r\n// 表单\r\nexport const config = (that) => ({\r\n  sys_name: {\r\n    component: 'input',\r\n    label: '系统名称',\r\n    colSpan: 8,\r\n    name: 'sys_name',\r\n    config: {\r\n      rules: [{ min: 0, max: 30, message: '请最多填写30个字符' }]\r\n    },\r\n    componentProps: {\r\n      // input组件配置\r\n      clearable: true,\r\n      placeholder: '支持系统名称模糊查询'\r\n    }\r\n  },\r\n  sys_id: {\r\n    component: 'input',\r\n    label: '系统标识',\r\n    colSpan: 8,\r\n    name: 'sys_id',\r\n    componentProps: {\r\n      clearable: true,\r\n      placeholder: '支持系统标识模糊查询'\r\n    }\r\n  },\r\n  is_open: {\r\n    component: 'select',\r\n    label: '启用标志',\r\n    colSpan: 8,\r\n    name: 'is_open',\r\n    config: {\r\n      rules: [{ min: 0, max: 1, message: '请最多填写1个字符' }]\r\n    },\r\n    componentProps: {\r\n      placeholder: '请选择',\r\n      clearable: true\r\n    },\r\n    options: dictionaryFieds('IS_OPEN')\r\n  }\r\n})\r\n"], "mappings": "AAAA,SAASA,eAAe,QAAQ,oBAAoB,EAAC;;AAErD;AACA,OAAO,IAAMC,MAAM,GAAG,SAATA,MAAM,CAAIC,IAAI;EAAA,OAAM;IAC/BC,QAAQ,EAAE;MACRC,SAAS,EAAE,OAAO;MAClBC,KAAK,EAAE,MAAM;MACbC,OAAO,EAAE,CAAC;MACVC,IAAI,EAAE,UAAU;MAChBN,MAAM,EAAE;QACNO,KAAK,EAAE,CAAC;UAAEC,GAAG,EAAE,CAAC;UAAEC,GAAG,EAAE,EAAE;UAAEC,OAAO,EAAE;QAAa,CAAC;MACpD,CAAC;MACDC,cAAc,EAAE;QACd;QACAC,SAAS,EAAE,IAAI;QACfC,WAAW,EAAE;MACf;IACF,CAAC;IACDC,MAAM,EAAE;MACNX,SAAS,EAAE,OAAO;MAClBC,KAAK,EAAE,MAAM;MACbC,OAAO,EAAE,CAAC;MACVC,IAAI,EAAE,QAAQ;MACdK,cAAc,EAAE;QACdC,SAAS,EAAE,IAAI;QACfC,WAAW,EAAE;MACf;IACF,CAAC;IACDE,OAAO,EAAE;MACPZ,SAAS,EAAE,QAAQ;MACnBC,KAAK,EAAE,MAAM;MACbC,OAAO,EAAE,CAAC;MACVC,IAAI,EAAE,SAAS;MACfN,MAAM,EAAE;QACNO,KAAK,EAAE,CAAC;UAAEC,GAAG,EAAE,CAAC;UAAEC,GAAG,EAAE,CAAC;UAAEC,OAAO,EAAE;QAAY,CAAC;MAClD,CAAC;MACDC,cAAc,EAAE;QACdE,WAAW,EAAE,KAAK;QAClBD,SAAS,EAAE;MACb,CAAC;MACDI,OAAO,EAAEjB,eAAe,CAAC,SAAS;IACpC;EACF,CAAC;AAAA,CAAC"}]}