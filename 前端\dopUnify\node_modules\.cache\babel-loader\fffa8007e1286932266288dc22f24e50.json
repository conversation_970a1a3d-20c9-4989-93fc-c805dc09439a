{"remainingRequest": "E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\node_modules\\babel-loader\\lib\\index.js!E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\node_modules\\eslint-loader\\index.js??ref--13-0!E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\src\\views\\system\\outManage\\permissions\\info.js", "dependencies": [{"path": "E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\src\\views\\system\\outManage\\permissions\\info.js", "mtime": 1686019809248}, {"path": "E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\babel.config.js", "mtime": 1686019818359}, {"path": "E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\.babelrc", "mtime": 1705285078668}, {"path": "E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\node_modules\\eslint-loader\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:aW1wb3J0IHsgZGljdGlvbmFyeUZpZWRzIH0gZnJvbSAnQC91dGlscy9kaWN0aW9uYXJ5JzsgLy8g5a2X5YW46YWN572uCgovLyDooajljZUKZXhwb3J0IHZhciBjb25maWcgPSBmdW5jdGlvbiBjb25maWcodGhhdCkgewogIHJldHVybiB7CiAgICBzeXNfaWQ6IHsKICAgICAgY29tcG9uZW50OiAnc2VsZWN0JywKICAgICAgbGFiZWw6ICfns7vnu5/moIfor4YnLAogICAgICBjb2xTcGFuOiA4LAogICAgICBjb25maWc6IHsKICAgICAgICAvLyBmb3JtLWl0ZW0g6YWN572uCiAgICAgICAgcnVsZXM6IFt7CiAgICAgICAgICBtaW46IDAsCiAgICAgICAgICBtYXg6IDEwLAogICAgICAgICAgbWVzc2FnZTogJ+ivt+acgOWkmuWhq+WGmTEw5Liq5a2X56ymJwogICAgICAgIH1dCiAgICAgIH0sCiAgICAgIG5hbWU6ICdzeXNfaWQnLAogICAgICBjb21wb25lbnRQcm9wczogewogICAgICAgIC8vIGlucHV057uE5Lu26YWN572uCiAgICAgICAgY2xlYXJhYmxlOiB0cnVlLAogICAgICAgIHBsYWNlaG9sZGVyOiAn6K+36YCJ5oupJwogICAgICB9LAogICAgICBvcHRpb25zOiBbXQogICAgfSwKICAgIHRkX25vOiB7CiAgICAgIGNvbXBvbmVudDogJ3NlbGVjdCcsCiAgICAgIGxhYmVsOiAn5o6l5Y+j5qCH6K+GJywKICAgICAgY29sU3BhbjogOCwKICAgICAgY29uZmlnOiB7CiAgICAgICAgLy8gZm9ybS1pdGVtIOmFjee9rgogICAgICAgIHJ1bGVzOiBbewogICAgICAgICAgbWluOiAwLAogICAgICAgICAgbWF4OiAxMCwKICAgICAgICAgIG1lc3NhZ2U6ICfor7fmnIDlpJrloavlhpkxMOS4quWtl+espicKICAgICAgICB9XQogICAgICB9LAogICAgICBuYW1lOiAndGRfbm8nLAogICAgICBjb21wb25lbnRQcm9wczogewogICAgICAgIHBsYWNlaG9sZGVyOiAn6K+36YCJ5oupJywKICAgICAgICBjbGVhcmFibGU6IHRydWUKICAgICAgfSwKICAgICAgb3B0aW9uczogW10KICAgIH0sCiAgICBpc19vcGVuOiB7CiAgICAgIGNvbXBvbmVudDogJ3NlbGVjdCcsCiAgICAgIGxhYmVsOiAn5ZCv55So5qCH5b+XJywKICAgICAgY29uZmlnOiB7CiAgICAgICAgLy8gZm9ybS1pdGVtIOmFjee9rgogICAgICAgIHJ1bGVzOiBbewogICAgICAgICAgbWluOiAwLAogICAgICAgICAgbWF4OiAxLAogICAgICAgICAgbWVzc2FnZTogJ+ivt+acgOWkmuWhq+WGmTHkuKrlrZfnrKYnCiAgICAgICAgfV0KICAgICAgfSwKICAgICAgY29sU3BhbjogOCwKICAgICAgbmFtZTogJ2lzX29wZW4nLAogICAgICBjb21wb25lbnRQcm9wczogewogICAgICAgIHBsYWNlaG9sZGVyOiAn6K+36YCJ5oupJywKICAgICAgICBjbGVhcmFibGU6IHRydWUKICAgICAgfSwKICAgICAgb3B0aW9uczogZGljdGlvbmFyeUZpZWRzKCdJU19PUEVOJykKICAgIH0KICB9Owp9Ow=="}, {"version": 3, "names": ["dictionaryFieds", "config", "that", "sys_id", "component", "label", "colSpan", "rules", "min", "max", "message", "name", "componentProps", "clearable", "placeholder", "options", "td_no", "is_open"], "sources": ["E:/1_Project/XYD_Project/dop-4.0/dop-4.1-qianduan-unify/dopUnify/src/views/system/outManage/permissions/info.js"], "sourcesContent": ["import { dictionaryFieds } from '@/utils/dictionary' // 字典配置\r\n\r\n// 表单\r\nexport const config = (that) => ({\r\n  sys_id: {\r\n    component: 'select',\r\n    label: '系统标识',\r\n    colSpan: 8,\r\n    config: {\r\n      // form-item 配置\r\n      rules: [{ min: 0, max: 10, message: '请最多填写10个字符' }]\r\n    },\r\n    name: 'sys_id',\r\n    componentProps: {\r\n      // input组件配置\r\n      clearable: true,\r\n      placeholder: '请选择'\r\n    },\r\n    options: []\r\n  },\r\n  td_no: {\r\n    component: 'select',\r\n    label: '接口标识',\r\n    colSpan: 8,\r\n    config: {\r\n      // form-item 配置\r\n      rules: [{ min: 0, max: 10, message: '请最多填写10个字符' }]\r\n    },\r\n    name: 'td_no',\r\n    componentProps: {\r\n      placeholder: '请选择',\r\n      clearable: true\r\n    },\r\n    options: []\r\n  },\r\n  is_open: {\r\n    component: 'select',\r\n    label: '启用标志',\r\n    config: {\r\n      // form-item 配置\r\n      rules: [{ min: 0, max: 1, message: '请最多填写1个字符' }]\r\n    },\r\n    colSpan: 8,\r\n    name: 'is_open',\r\n    componentProps: {\r\n      placeholder: '请选择',\r\n      clearable: true\r\n    },\r\n    options: dictionaryFieds('IS_OPEN')\r\n  }\r\n})\r\n"], "mappings": "AAAA,SAASA,eAAe,QAAQ,oBAAoB,EAAC;;AAErD;AACA,OAAO,IAAMC,MAAM,GAAG,SAATA,MAAM,CAAIC,IAAI;EAAA,OAAM;IAC/BC,MAAM,EAAE;MACNC,SAAS,EAAE,QAAQ;MACnBC,KAAK,EAAE,MAAM;MACbC,OAAO,EAAE,CAAC;MACVL,MAAM,EAAE;QACN;QACAM,KAAK,EAAE,CAAC;UAAEC,GAAG,EAAE,CAAC;UAAEC,GAAG,EAAE,EAAE;UAAEC,OAAO,EAAE;QAAa,CAAC;MACpD,CAAC;MACDC,IAAI,EAAE,QAAQ;MACdC,cAAc,EAAE;QACd;QACAC,SAAS,EAAE,IAAI;QACfC,WAAW,EAAE;MACf,CAAC;MACDC,OAAO,EAAE;IACX,CAAC;IACDC,KAAK,EAAE;MACLZ,SAAS,EAAE,QAAQ;MACnBC,KAAK,EAAE,MAAM;MACbC,OAAO,EAAE,CAAC;MACVL,MAAM,EAAE;QACN;QACAM,KAAK,EAAE,CAAC;UAAEC,GAAG,EAAE,CAAC;UAAEC,GAAG,EAAE,EAAE;UAAEC,OAAO,EAAE;QAAa,CAAC;MACpD,CAAC;MACDC,IAAI,EAAE,OAAO;MACbC,cAAc,EAAE;QACdE,WAAW,EAAE,KAAK;QAClBD,SAAS,EAAE;MACb,CAAC;MACDE,OAAO,EAAE;IACX,CAAC;IACDE,OAAO,EAAE;MACPb,SAAS,EAAE,QAAQ;MACnBC,KAAK,EAAE,MAAM;MACbJ,MAAM,EAAE;QACN;QACAM,KAAK,EAAE,CAAC;UAAEC,GAAG,EAAE,CAAC;UAAEC,GAAG,EAAE,CAAC;UAAEC,OAAO,EAAE;QAAY,CAAC;MAClD,CAAC;MACDJ,OAAO,EAAE,CAAC;MACVK,IAAI,EAAE,SAAS;MACfC,cAAc,EAAE;QACdE,WAAW,EAAE,KAAK;QAClBD,SAAS,EAAE;MACb,CAAC;MACDE,OAAO,EAAEf,eAAe,CAAC,SAAS;IACpC;EACF,CAAC;AAAA,CAAC"}]}