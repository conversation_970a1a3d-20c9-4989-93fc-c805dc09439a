{"remainingRequest": "E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan\\数字运营平台-统一门户工程\\dopUnify\\node_modules\\babel-loader\\lib\\index.js!E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan\\数字运营平台-统一门户工程\\dopUnify\\node_modules\\qiankun\\es\\prefetch.js", "dependencies": [{"path": "E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan\\数字运营平台-统一门户工程\\dopUnify\\node_modules\\qiankun\\es\\prefetch.js", "mtime": 1667130453000}, {"path": "E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-<PERSON><PERSON><PERSON>n\\数字运营平台-统一门户工程\\dopUnify\\babel.config.js", "mtime": 1686019818359}, {"path": "E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-<PERSON><PERSON><PERSON>n\\数字运营平台-统一门户工程\\dopUnify\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-<PERSON><PERSON><PERSON>n\\数字运营平台-统一门户工程\\dopUnify\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, null]}