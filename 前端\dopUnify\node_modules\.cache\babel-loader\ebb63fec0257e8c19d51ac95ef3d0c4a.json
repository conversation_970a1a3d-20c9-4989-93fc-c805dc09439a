{"remainingRequest": "D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\node_modules\\babel-loader\\lib\\index.js!D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\src\\views\\system\\externalManage\\priv\\index.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\src\\views\\system\\externalManage\\priv\\index.vue", "mtime": 1686019809154}, {"path": "D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\babel.config.js", "mtime": 1686019818359}, {"path": "D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\.babelrc", "mtime": 1705285078668}, {"path": "D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1655715099000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;AA2BA;AACA;AACA;;AAEA;AACA;AACA;EACAA;EACAC;IAAAC;EAAA;EACAC;IACA;MACAC;MACAC;QACAC;QACAC;MACA;MACAC;QACA;QACAC;QACAC;QACAC;MACA;MACAC;IACA;EACA;EACAC;IACA;EACA;EACAC;IAAA;IACA;IACA;MACA;QACA;QACA;MACA;IACA;EACA;EACAC;IACA;AACA;IACAC;MACA;IACA;IACA;AACA;IACAC;MAAA;MACA;MACA,YACAC,yDACAC;QACA;QACAC;UACA;YACAC;UACA;UACAC;QACA;QACA;MACA;IACA;IACA;AACA;AACA;IACAC;MACA;QACA;MACA;QACA;MACA;IACA;IACA;AACA;IACAC;MACA;IACA;IACA;IACAC;MACA,sBACA;MACA;IACA;EACA;AACA", "names": ["name", "components", "TreeList", "data", "config", "defaultForm", "external_system_no", "user_name", "btnAll", "btnQuery", "btnEmpty", "btnSubmit", "boxSecondHeight", "created", "mounted", "methods", "btnPermissions", "getDispatch", "dispatch", "then", "res", "label", "SystemArray", "validateForm", "queryList", "getHeight"], "sourceRoot": "src/views/system/externalManage/priv", "sources": ["index.vue"], "sourcesContent": ["<!--\r\n* 关联系统机构权限配置\r\n-->\r\n<template>\r\n  <div ref=\"boxRef\" class=\"app-container\">\r\n    <div ref=\"boxFirstRef\" class=\"sun-content\">\r\n      <div class=\"filter-container\">\r\n        <sun-form\r\n          ref=\"formRef\"\r\n          :config=\"config\"\r\n          :default-form=\"defaultForm\"\r\n          label-width=\"14rem\"\r\n          :query=\"btnAll.btnQuery\"\r\n          @query=\"queryList\"\r\n          @validateForm=\"validateForm\"\r\n        />\r\n      </div>\r\n    </div>\r\n    <TreeList\r\n      ref=\"treeRef\"\r\n      :btn-all=\"btnAll\"\r\n      :default-form=\"defaultForm\"\r\n      :height=\"boxSecondHeight\"\r\n    />\r\n  </div>\r\n</template>\r\n<script>\r\nimport { config } from './info' // 表单配置\r\nimport TreeList from './component/tree' // 表格\r\nimport { permissionsBtn } from '@/utils/permissions' // 权限配置\r\n\r\n// import { SysSystem } from '@/api'\r\n// const { systemController } = SysSystem\r\nexport default {\r\n  name: 'ManagePriv',\r\n  components: { TreeList },\r\n  data() {\r\n    return {\r\n      config: config(this),\r\n      defaultForm: {\r\n        external_system_no: '',\r\n        user_name: ''\r\n      },\r\n      btnAll: {\r\n        // 当前页需要配置权限的按钮  权限获取\r\n        btnQuery: false,\r\n        btnEmpty: true,\r\n        btnSubmit: true\r\n      },\r\n      boxSecondHeight: 0 // 下方容器高度\r\n    }\r\n  },\r\n  created() {\r\n    this.btnPermissions()\r\n  },\r\n  mounted() {\r\n    this.getHeight()\r\n    this.$nextTick().then(() => {\r\n      if (this.btnAll.btnQuery) {\r\n        this.queryList()\r\n        this.getDispatch()\r\n      }\r\n    })\r\n  },\r\n  methods: {\r\n    /**\r\n     * 按钮权限配置*/\r\n    btnPermissions() {\r\n      this.btnAll = permissionsBtn(this.$attrs.button_id, this.btnAll)\r\n    },\r\n    /**\r\n     * 获取外部字典*/\r\n    getDispatch() {\r\n      // 处理系统编号\r\n      this.$store\r\n        .dispatch('common/setExternalData', 'EXTERNAL_SYSTEM_NO')\r\n        .then((res) => {\r\n          const SystemArray = []\r\n          res.map(function(item) {\r\n            const valueS = Object.assign({}, item, {\r\n              label: item.value + '-' + item.label\r\n            })\r\n            SystemArray.push(valueS)\r\n          })\r\n          this.config.system_no.options = SystemArray\r\n        })\r\n    },\r\n    /**\r\n     * 表单校验\r\n     * @param {Boolean}valid 校验返回值*/\r\n    validateForm(valid) {\r\n      if (valid) {\r\n        this.$refs.treeRef.queryList(1)\r\n      } else {\r\n        return false\r\n      }\r\n    },\r\n    /**\r\n     * 按钮：查询*/\r\n    queryList() {\r\n      this.$refs['formRef'].validateForm()\r\n    },\r\n    // 获取容器高度\r\n    getHeight() {\r\n      const boxSecondHeight =\r\n        this.$refs.boxRef.clientHeight - this.$refs.boxFirstRef.clientHeight\r\n      this.boxSecondHeight = boxSecondHeight\r\n    }\r\n  }\r\n}\r\n</script>\r\n<style scoped>\r\n.app-container {\r\n  width: 100%;\r\n  height: 100%;\r\n}\r\n</style>\r\n"]}]}