{"remainingRequest": "D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\node_modules\\babel-loader\\lib\\index.js!D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\src\\views\\home\\component\\business\\index.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\src\\views\\home\\component\\business\\index.vue", "mtime": 1686019809466}, {"path": "D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\babel.config.js", "mtime": 1686019818359}, {"path": "D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1655715099000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAiDA;AACA;AACA;AACA;AACA;EACAA;EACAC;IAAAC;EAAA;EACAC;IACA;MACAC;MAAA;MACAC;QACAC;QAAA;QACAC;QAAA;QACAC;QAAA;QACAC;QAAA;QACAC;QAAA;QACAC;MACA;;MAAA;MACAC;QACAC;QAAA;QACAC;QAAA;QACAC;QAAA;QACAC;QAAA;QACAC;UACA;UACAb;UAAA;UACAG;QACA;;QACAW;UACA;UACAC;UACAC;UAAA;UACAC;UAAA;UACAC;UAAA;UACAC;UAAA;UACAC;YACA;YACAC;YAAA;YACAC;YAAA;YACAC;UACA;;UACAV;YACA;YACAd;YAAA;YACAK;UACA;QACA;MACA;IACA;EACA;EACAoB;IACAC;MACA;IAAA;EAEA;EACAC;EACAC;IACA;IACA;IACA;EACA;EACAC;IACA;EAAA,CACA;EACAC;IACAC;MACA;IACA;IACA;AACA;AACA;IACAC;MAAA;MACA;QACAC;QACAC;MACA;MACAC;QACA;QACA;UACA;UACA;UACA;UACA;UAAA,2CACAC;YAAA;UAAA;YAAA;cAAA;cACA9B;cACA;cACAC;cACA;cACAC;YACA;UAAA;YAAA;UAAA;YAAA;UAAA;UACA;UACA;UACA;QACA;UACA;UACA;QACA;MACA;IACA;IACA;AACA;IACA6B;MAAA;MACA;MACA;QACA;MACA;IACA;IACA;AACA;AACA;IACAC;MACA;IACA;EACA;AACA", "names": ["name", "components", "BusinessChart", "data", "title", "business", "id", "width", "height", "organNos", "threeSeason", "hoal<PERSON>ear", "dialog", "hiddenPage", "visible", "btnSubmit", "btnCancle", "componentProps", "tableConfig", "ref", "columns", "selection", "indexNumber", "loading", "pageList", "totalNum", "currentPage", "pageSize", "watch", "text", "beforeMount", "mounted", "<PERSON><PERSON><PERSON><PERSON>", "methods", "changeSize", "queryList", "page_no", "page_size", "businessQuery", "result", "viewAll", "changeVisible"], "sourceRoot": "src/views/home/<USER>/business", "sources": ["index.vue"], "sourcesContent": ["<!--\r\n* 首页-业务量\r\n-->\r\n<template>\r\n  <div class=\"homeContent\">\r\n    <div class=\"homePageTitle\">\r\n      {{ title }}\r\n      <span class=\"InfoMore\" @click=\"viewAll\">查看全部 ></span>\r\n    </div>\r\n    <div />\r\n    <div class=\"homePageBox\">\r\n      <business-chart\r\n        :id=\"business.id\"\r\n        ref=\"businessRef\"\r\n        :width=\"business.width\"\r\n        :height=\"business.height\"\r\n        :business=\"business\"\r\n      />\r\n    </div>\r\n    <!-- object区域 -->\r\n    <object\r\n      ref=\"objectRef\"\r\n      tabindex=\"-1\"\r\n      type=\"text/html\"\r\n      aria-hidden=\"true\"\r\n      data=\"about:blank\"\r\n      style=\"\r\n        display: block;\r\n        position: absolute;\r\n        top: 0px;\r\n        left: 0px;\r\n        width: 100%;\r\n        height: 100%;\r\n        border: none;\r\n        padding: 0px;\r\n        margin: 0px;\r\n        opacity: 0;\r\n        z-index: -1000;\r\n        pointer-events: none;\r\n      \"\r\n    />\r\n    <sun-table-dialog\r\n      :dialog-config=\"dialog\"\r\n      @dialogClose=\"changeVisible\"\r\n    /><!--新增、修改弹出框-->\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport BusinessChart from '@/components/Charts/Business.vue' // ECharts图标组件\r\nimport { config } from './info' // 表头、表单配置\r\nimport { Home } from '@/api'\r\nconst { businessQuery } = Home\r\nexport default {\r\n  name: 'Business',\r\n  components: { BusinessChart },\r\n  data() {\r\n    return {\r\n      title: '业务量', // 标题\r\n      business: {\r\n        id: 'chart', // ECharts图形id\r\n        width: '100%', // 图形宽度\r\n        height: '100%', // 图形高度\r\n        organNos: [], // 横轴机构列表\r\n        threeSeason: [], // 本季度业务量\r\n        hoalYear: [] // 本年度业务量\r\n      }, // 业务量总对象\r\n      dialog: {\r\n        hiddenPage: true, // 隐藏分页\r\n        visible: false, // 显示隐藏配置\r\n        btnSubmit: false, // 确定按钮\r\n        btnCancle: false, // 取消按钮\r\n        componentProps: {\r\n          // 弹出框属性\r\n          title: '业务量报表', // 弹出框标题\r\n          width: '80%' // 当前弹出框宽度 默认80%\r\n        },\r\n        tableConfig: {\r\n          // 表格属性\r\n          ref: 'tableRef',\r\n          columns: config(this), // 表头\r\n          selection: false, // 复选\r\n          indexNumber: false, // 序号\r\n          loading: false, // 等待加载中\r\n          pageList: {\r\n            // 页码\r\n            totalNum: 0, // 总页数\r\n            currentPage: 1, // 当前页\r\n            pageSize: this.$store.getters.pageSize // 当前页显示条数\r\n          },\r\n          componentProps: {\r\n            // 表格属性配置\r\n            data: [], // 表数据\r\n            height: '40rem'\r\n          }\r\n        }\r\n      }\r\n    }\r\n  },\r\n  watch: {\r\n    text(val) {\r\n      // console.log(val)\r\n    }\r\n  },\r\n  beforeMount() {},\r\n  mounted() {\r\n    this.queryList(0)\r\n    // 监听chart尺寸改动\r\n    // this.$refs['objectRef'].contentDocument.defaultView.addEventListener('resize', this.changeSize)\r\n  },\r\n  beforeDestroy() {\r\n    // this.$refs['objectRef'].contentDocument.defaultView.removeEventListener('resize', this.changeSize)\r\n  },\r\n  methods: {\r\n    changeSize() {\r\n      this.$refs.businessRef.chart.resize()\r\n    },\r\n    /**\r\n     * 查询-业务量信息\r\n     * @param {Number} page 当前一级菜单 */\r\n    queryList(page) {\r\n      const msg = {\r\n        page_no: page,\r\n        page_size: 10\r\n      }\r\n      businessQuery(msg).then((response) => {\r\n        const { result } = response.retMap\r\n        if (page === 0) {\r\n          // page为0时，查询ECharts图形数据\r\n          let organNos = [] // 横轴机构列表\r\n          let threeSeason = [] // 本季度业务量\r\n          let hoalYear = [] // 本年度业务量\r\n          for (const item of result) {\r\n            organNos = [...organNos, item.organ_no]\r\n            const seasonNumber = parseInt(item.three_season.replaceAll(',', ''))\r\n            threeSeason = [...threeSeason, seasonNumber]\r\n            const yearNumber = parseInt(item.hoal_year.replaceAll(',', ''))\r\n            hoalYear = [...hoalYear, yearNumber]\r\n          }\r\n          this.business.organNos = organNos\r\n          this.business.threeSeason = threeSeason\r\n          this.business.hoalYear = hoalYear\r\n        } else {\r\n          // page为-1时，查询弹窗表格数据\r\n          this.dialog.tableConfig.componentProps.data = result\r\n        }\r\n      })\r\n    },\r\n    /**\r\n     * 查看全部-业务量信息 */\r\n    viewAll() {\r\n      this.dialog.visible = true\r\n      this.$nextTick(() => {\r\n        this.queryList(-1)\r\n      })\r\n    },\r\n    /**\r\n     * 弹出框 - 关闭\r\n     * @param {Boolean} param 弹出框显示隐藏配置*/\r\n    changeVisible(param) {\r\n      this.dialog.visible = param\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style scoped lang=\"scss\">\r\n@import '~@/assets/scss/common/variable/variable/size.scss';\r\n.InfoMore {\r\n  float: right;\r\n  color: gray;\r\n  font-size: $noteFomt;\r\n  cursor: pointer;\r\n}\r\n// .moveUl {\r\n//   position: absolute;\r\n//   border: 1px solid;\r\n//   border-radius: 8px;\r\n//   padding: 1rem;\r\n//   margin: 0 !important;\r\n//   list-style-type: none;\r\n//   li {\r\n//     width: 14rem;\r\n//     height: 2.5rem;\r\n//   }\r\n// }\r\n</style>\r\n"]}]}