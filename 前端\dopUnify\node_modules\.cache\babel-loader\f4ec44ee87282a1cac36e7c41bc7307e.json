{"remainingRequest": "E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1\\数字运营平台-统一门户工程\\dopUnify\\node_modules\\babel-loader\\lib\\index.js!E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1\\数字运营平台-统一门户工程\\dopUnify\\node_modules\\eslint-loader\\index.js??ref--13-0!E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1\\数字运营平台-统一门户工程\\dopUnify\\src\\polyfills.js", "dependencies": [{"path": "E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1\\数字运营平台-统一门户工程\\dopUnify\\src\\polyfills.js", "mtime": 1716875186063}, {"path": "E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1\\数字运营平台-统一门户工程\\dopUnify\\babel.config.js", "mtime": 1716875191548}, {"path": "E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1\\数字运营平台-统一门户工程\\dopUnify\\.babelrc", "mtime": 1716875191502}, {"path": "E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1\\数字运营平台-统一门户工程\\dopUnify\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1\\数字运营平台-统一门户工程\\dopUnify\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1\\数字运营平台-统一门户工程\\dopUnify\\node_modules\\eslint-loader\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:LyoqDQoqIGll5LiN5YW85a6577yaZWxlbWVudC11aeeahE5hdk1lbnXlnKjmipjlj6DkuYvlkI4s6byg5qCH5ruR6L+HbWVudeWcqElF5LiLLOaKpemUmS5FcnJvciBpbiB2LW9uIGhhbmRsZXI6ICJUeXBlRXJyb3I6IOWvueixoeS4jeaUr+aMgeatpOaTjeS9nCINCiog6Kej5YazYmVnaW4gKi8KKGZ1bmN0aW9uICh3aW5kb3cpIHsKICB0cnkgewogICAgbmV3IE1vdXNlRXZlbnQoJ3Rlc3QnKTsKICAgIHJldHVybiBmYWxzZTsgLy8gTm8gbmVlZCB0byBwb2x5ZmlsbAogIH0gY2F0Y2ggKGUpIHsKICAgIC8vIE5lZWQgdG8gcG9seWZpbGwgLSBmYWxsIHRocm91Z2gKICB9CgogIC8vIFBvbHlmaWxscyBET000IE1vdXNlRXZlbnQKICB2YXIgTW91c2VFdmVudFBvbHlmaWxsID0gZnVuY3Rpb24gTW91c2VFdmVudFBvbHlmaWxsKGV2ZW50VHlwZSwgcGFyYW1zKSB7CiAgICBwYXJhbXMgPSBwYXJhbXMgfHwgewogICAgICBidWJibGVzOiBmYWxzZSwKICAgICAgY2FuY2VsYWJsZTogZmFsc2UKICAgIH07CiAgICB2YXIgbW91c2VFdmVudCA9IGRvY3VtZW50LmNyZWF0ZUV2ZW50KCdNb3VzZUV2ZW50Jyk7CiAgICBtb3VzZUV2ZW50LmluaXRNb3VzZUV2ZW50KGV2ZW50VHlwZSwgcGFyYW1zLmJ1YmJsZXMsIHBhcmFtcy5jYW5jZWxhYmxlLCB3aW5kb3csIDAsIHBhcmFtcy5zY3JlZW5YIHx8IDAsIHBhcmFtcy5zY3JlZW5ZIHx8IDAsIHBhcmFtcy5jbGllbnRYIHx8IDAsIHBhcmFtcy5jbGllbnRZIHx8IDAsIHBhcmFtcy5jdHJsS2V5IHx8IGZhbHNlLCBwYXJhbXMuYWx0S2V5IHx8IGZhbHNlLCBwYXJhbXMuc2hpZnRLZXkgfHwgZmFsc2UsIHBhcmFtcy5tZXRhS2V5IHx8IGZhbHNlLCBwYXJhbXMuYnV0dG9uIHx8IDAsIHBhcmFtcy5yZWxhdGVkVGFyZ2V0IHx8IG51bGwpOwogICAgcmV0dXJuIG1vdXNlRXZlbnQ7CiAgfTsKICBNb3VzZUV2ZW50UG9seWZpbGwucHJvdG90eXBlID0gRXZlbnQucHJvdG90eXBlOwogIHdpbmRvdy5Nb3VzZUV2ZW50ID0gTW91c2VFdmVudFBvbHlmaWxsOwp9KSh3aW5kb3cpOwovKioNCiogaWXkuI3lhbzlrrnvvJplbGVtZW50LXVp55qETmF2TWVudeWcqOaKmOWPoOS5i+WQjizpvKDmoIfmu5Hov4dtZW515ZyoSUXkuIss5oql6ZSZLkVycm9yIGluIHYtb24gaGFuZGxlcjogIlR5cGVFcnJvcjog5a+56LGh5LiN5pSv5oyB5q2k5pON5L2cIg0KKiDop6PlhrNlbmQgKi8="}, {"version": 3, "names": ["window", "MouseEvent", "e", "MouseEventPolyfill", "eventType", "params", "bubbles", "cancelable", "mouseEvent", "document", "createEvent", "initMouseEvent", "screenX", "screenY", "clientX", "clientY", "ctrl<PERSON>ey", "altKey", "shift<PERSON>ey", "metaKey", "button", "relatedTarget", "prototype", "Event"], "sources": ["E:/1_Project/XYD_Project/dop-4.0/dop-4.1/数字运营平台-统一门户工程/dopUnify/src/polyfills.js"], "sourcesContent": ["/**\r\n* ie不兼容：element-ui的NavMenu在折叠之后,鼠标滑过menu在IE下,报错.Error in v-on handler: \"TypeError: 对象不支持此操作\"\r\n* 解决begin */\r\n(function(window) {\r\n  try {\r\n    new MouseEvent('test')\r\n    return false // No need to polyfill\r\n  } catch (e) {\r\n    // Need to polyfill - fall through\r\n  }\r\n\r\n  // Polyfills DOM4 MouseEvent\r\n  var MouseEventPolyfill = function(eventType, params) {\r\n    params = params || { bubbles: false, cancelable: false }\r\n    var mouseEvent = document.createEvent('MouseEvent')\r\n    mouseEvent.initMouseEvent(\r\n      eventType,\r\n      params.bubbles,\r\n      params.cancelable,\r\n      window,\r\n      0,\r\n      params.screenX || 0,\r\n      params.screenY || 0,\r\n      params.clientX || 0,\r\n      params.clientY || 0,\r\n      params.ctrlKey || false,\r\n      params.altKey || false,\r\n      params.shiftKey || false,\r\n      params.metaKey || false,\r\n      params.button || 0,\r\n      params.relatedTarget || null\r\n    )\r\n\r\n    return mouseEvent\r\n  }\r\n\r\n  MouseEventPolyfill.prototype = Event.prototype\r\n\r\n  window.MouseEvent = MouseEventPolyfill\r\n})(window)\r\n/**\r\n* ie不兼容：element-ui的NavMenu在折叠之后,鼠标滑过menu在IE下,报错.Error in v-on handler: \"TypeError: 对象不支持此操作\"\r\n* 解决end */\r\n"], "mappings": "AAAA;AACA;AACA;AACA,CAAC,UAASA,MAAM,EAAE;EAChB,IAAI;IACF,IAAIC,UAAU,CAAC,MAAM,CAAC;IACtB,OAAO,KAAK,EAAC;EACf,CAAC,CAAC,OAAOC,CAAC,EAAE;IACV;EAAA;;EAGF;EACA,IAAIC,kBAAkB,GAAG,SAArBA,kBAAkB,CAAYC,SAAS,EAAEC,MAAM,EAAE;IACnDA,MAAM,GAAGA,MAAM,IAAI;MAAEC,OAAO,EAAE,KAAK;MAAEC,UAAU,EAAE;IAAM,CAAC;IACxD,IAAIC,UAAU,GAAGC,QAAQ,CAACC,WAAW,CAAC,YAAY,CAAC;IACnDF,UAAU,CAACG,cAAc,CACvBP,SAAS,EACTC,MAAM,CAACC,OAAO,EACdD,MAAM,CAACE,UAAU,EACjBP,MAAM,EACN,CAAC,EACDK,MAAM,CAACO,OAAO,IAAI,CAAC,EACnBP,MAAM,CAACQ,OAAO,IAAI,CAAC,EACnBR,MAAM,CAACS,OAAO,IAAI,CAAC,EACnBT,MAAM,CAACU,OAAO,IAAI,CAAC,EACnBV,MAAM,CAACW,OAAO,IAAI,KAAK,EACvBX,MAAM,CAACY,MAAM,IAAI,KAAK,EACtBZ,MAAM,CAACa,QAAQ,IAAI,KAAK,EACxBb,MAAM,CAACc,OAAO,IAAI,KAAK,EACvBd,MAAM,CAACe,MAAM,IAAI,CAAC,EAClBf,MAAM,CAACgB,aAAa,IAAI,IAAI,CAC7B;IAED,OAAOb,UAAU;EACnB,CAAC;EAEDL,kBAAkB,CAACmB,SAAS,GAAGC,KAAK,CAACD,SAAS;EAE9CtB,MAAM,CAACC,UAAU,GAAGE,kBAAkB;AACxC,CAAC,EAAEH,MAAM,CAAC;AACV;AACA;AACA"}]}