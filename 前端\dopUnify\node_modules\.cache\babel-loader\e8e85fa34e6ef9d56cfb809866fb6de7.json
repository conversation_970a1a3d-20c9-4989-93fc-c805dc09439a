{"remainingRequest": "E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-q<PERSON><PERSON>n\\数字运营平台-统一门户工程\\dopUnify\\node_modules\\babel-loader\\lib\\index.js!E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan\\数字运营平台-统一门户工程\\dopUnify\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan\\数字运营平台-统一门户工程\\dopUnify\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan\\数字运营平台-统一门户工程\\dopUnify\\node_modules\\sunui\\src\\components\\Dialog\\SunTableDialog\\index.vue?vue&type=script&lang=js&", "dependencies": [{"path": "E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-<PERSON><PERSON><PERSON><PERSON>\\数字运营平台-统一门户工程\\dopUnify\\node_modules\\sunui\\src\\components\\Dialog\\SunTableDialog\\index.vue", "mtime": 499162500000}, {"path": "E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-<PERSON><PERSON><PERSON>n\\数字运营平台-统一门户工程\\dopUnify\\babel.config.js", "mtime": 1686019818359}, {"path": "E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-<PERSON><PERSON><PERSON>n\\数字运营平台-统一门户工程\\dopUnify\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-<PERSON><PERSON><PERSON>n\\数字运营平台-统一门户工程\\dopUnify\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-<PERSON><PERSON><PERSON>n\\数字运营平台-统一门户工程\\dopUnify\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-<PERSON><PERSON><PERSON>n\\数字运营平台-统一门户工程\\dopUnify\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1655715099000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA4EA;AACA;AACA;AACA;EACAA;EACAC;IAAAC;EAAA;EACAC;IAAAC;EAAA;EACA;EACAC;IACAC;MACAC;MACAC;QACA;UACAC;UAAA;UACAC;UAAA;UACAC;UAAA;UACAC;YACA;YACAC;YAAA;YACAC;UACA;;UACAC;YACA;YACAC;YACAC;YAAA;YACAC;YAAA;YACAC;YAAA;YACAC;YAAA;YACAC;cACA;cACAC;cAAA;cACAC;cAAA;cACAC;YACA;;YACAZ;cACA;cACAa;cAAA;cACAC;YACA;UACA;QACA;MACA;IACA;EACA;EACAD;IACA;MACAE;IACA;EACA;EACAC;IACA;MACA;MACAC;QACA;UACA;QACA;MACA;MACAC;IACA;EACA;EACAC;IACAC;EACA;EACAC;IACAD;EACA;EACAE;IAAA;IACA;MACA;IACA;EACA;EACAC;IACA;AACA;IACAC;MACA;QACA;QACA;UACA,yDACAC;QACA;UACA,yDACAA;QACA;QACA;MACA;IACA;IACA;AACA;AACA;IACAC;MAAA;MACA;QACA;MACA;IACA;IACA;AACA;IACAC;MACA;IACA;IACAC;MACA;IACA;IACA;AACA;IACAC;MAAA;QAAAC;MACAC;IACA;IACA;AACA;IACAC;MACA;IACA;IACA;AACA;AACA;AACA;IACAC;MACA;IACA;EACA;AACA", "names": ["name", "directives", "elDragDialog", "components", "SunTable", "props", "dialogConfig", "type", "default", "visible", "btnSubmit", "btnCancle", "componentProps", "title", "width", "tableConfig", "ref", "columns", "selection", "indexNumber", "loading", "pageList", "totalNum", "currentPage", "pageSize", "data", "height", "btn", "watch", "handler", "deep", "beforeMount", "window", "<PERSON><PERSON><PERSON><PERSON>", "mounted", "methods", "resize<PERSON><PERSON>ler", "screenHeight", "dialogClose", "currentChange", "selectionChange", "rowClassName", "rowIndex", "row", "dialogSubmit", "pagination"], "sourceRoot": "node_modules/sunui/src/components/Dialog/SunTableDialog", "sources": ["index.vue"], "sourcesContent": ["<!--\n* 弹出框：单个表格\n-->\n<template>\n  <el-dialog\n    v-el-drag-dialog\n    :visible.sync=\"dialogConfig.visible\"\n    :append-to-body=\"true\"\n    :close-on-click-modal=\"false\"\n    :before-close=\"dialogClose\"\n    v-bind=\"dialogConfig.componentProps\"\n    top=\"5%\"\n  >\n    <div ref=\"tableContent\" class=\"tableContent\">\n      <sun-table\n        ref=\"dialogTable\"\n        class=\"tableLayout\"\n        :show-page=\"dialogConfig.hiddenPage ? false : true\"\n        :table-config=\"dialogConfig.tableConfig\"\n        @current-change=\"currentChange\"\n        @selection-change=\"selectionChange\"\n        @pagination=\"pagination\"\n      >\n        <template v-if=\"!$scopedSlots.default\" slot=\"tableColumn\">\n          <el-table-column\n            v-for=\"item in dialogConfig.tableConfig.columns\"\n            :key=\"item.id\"\n            :prop=\"item.name\"\n            :label=\"item.label\"\n            :width=\"item.width\"\n          >\n            <!-- <slot :name=\"item.name\" /> -->\n          </el-table-column>\n        </template>\n        <template v-else slot=\"tableColumn\">\n          <el-table-column\n            v-for=\"item in dialogConfig.tableConfig.columns\"\n            :key=\"item.id\"\n            :prop=\"item.name\"\n            :label=\"item.label\"\n            :width=\"item.width\"\n          >\n            <template slot-scope=\"{ row }\">\n              <slot :item=\"item\" :row=\"row\" />\n            </template>\n          </el-table-column>\n        </template>\n      </sun-table>\n\n      <!-- </el-table> -->\n      <div slot=\"footer\" class=\"dialog-footer\">\n        <el-row :gutter=\"0\">\n          <el-col :span=\"12\" class=\"leftBtn\">\n            <!--左侧按钮-->\n            <slot name=\"leftBtn\" />\n          </el-col>\n          <el-col :span=\"$scopedSlots.leftBtn ? 12 : 24\" class=\"rightBtn\">\n            <!--右侧按钮-->\n            <slot name=\"rightBtn\" />\n            <el-button\n              v-if=\"dialogConfig.btnCancle === false ? false : true\"\n              @click=\"dialogClose\"\n            >取 消</el-button>\n            <el-button\n              v-if=\"dialogConfig.btnSubmit === false ? false : true\"\n              type=\"primary\"\n              @click=\"dialogSubmit\"\n            >确 定</el-button>\n          </el-col>\n        </el-row>\n      </div>\n    </div>\n  </el-dialog>\n</template>\n\n<script>\nimport SunTable from '../../SunTable' // 按钮\nimport elDragDialog from '../../../directive/el-drag-dialog' // base on element-ui\n// import store from '@/store'\nexport default {\n  name: 'SunTableDialog',\n  directives: { elDragDialog },\n  components: { SunTable },\n  // inheritAttrs: false,\n  props: {\n    dialogConfig: {\n      type: Object,\n      default: () => {\n        return {\n          visible: false, // 显示隐藏配置\n          btnSubmit: true, // 确定按钮\n          btnCancle: true, // 取消按钮\n          componentProps: {\n            // 弹出框属性\n            title: '表格弹出框', // 弹出框标题\n            width: '' // 当前弹出框宽度 默认80%\n          },\n          tableConfig: {\n            // 表格属性\n            ref: 'tableRef',\n            columns: {}, // 表头\n            selection: false, // 复选\n            indexNumber: false, // 序号\n            loading: false, // 等待加载中\n            pageList: {\n              // 页码\n              totalNum: 0, // 总页数\n              currentPage: 1, // 当前页\n              pageSize: 10 // 当前页显示条数\n            },\n            componentProps: {\n              // 表格属性配置\n              data: [], // 表数据\n              height: '500px'\n            }\n          }\n        }\n      }\n    }\n  },\n  data() {\n    return {\n      btn: false\n    }\n  },\n  watch: {\n    'dialogConfig.visible': {\n      // 打开、关闭弹出框\n      handler(val) {\n        if (val) {\n          this.resizeHandler()\n        }\n      },\n      deep: true\n    }\n  },\n  beforeMount() {\n    window.addEventListener('resize', this.resizeHandler)\n  },\n  beforeDestroy() {\n    window.removeEventListener('resize', this.resizeHandler)\n  },\n  mounted() {\n    this.$nextTick(() => {\n      this.resizeHandler()\n    })\n  },\n  methods: {\n    /**\n     * 页面尺寸配置*/\n    resizeHandler() {\n      if (this.$refs.tableContent) {\n        const screenHeight = this.$refs.tableContent.offsetHeight // 父节点高\n        if (this.dialogConfig.btnCancle || this.dialogConfig.btnSubmit) {\n          this.dialogConfig.tableConfig.componentProps['height'] =\n            screenHeight - 102 + 'px'\n        } else {\n          this.dialogConfig.tableConfig.componentProps['height'] =\n            screenHeight - 62 + 'px'\n        }\n        this.dialogConfig.tableConfig = { ...this.dialogConfig.tableConfig } // 数据监听\n      }\n    },\n    /**\n     * 弹出框关闭\n     */\n    dialogClose() {\n      this.$nextTick(() => {\n        this.$emit('dialogClose', false)\n      })\n    },\n    /**\n     * 选中行*/\n    currentChange(val) {\n      this.$emit('currentChange', val)\n    },\n    selectionChange(val) {\n      this.$emit('selectionChange', val)\n    },\n    /**\n     * 行的 className 的回调方法，也可以使用字符串为所有行设置一个固定的 className*/\n    rowClassName({ row, rowIndex }) {\n      row.index = rowIndex // 将索引放置到row数据中\n    },\n    /**\n     * 确定*/\n    dialogSubmit() {\n      this.$emit('dialogSubmit')\n    },\n    /**\n     * 页码跳转\n     * @param {Object}param 页码信息 { currentPage: val, pageSize: this.pageSize }\n     */\n    pagination(param) {\n      this.$emit('pagination', param)\n    }\n  }\n}\n</script>\n\n<style lang=\"scss\" scoped>\n.tableContent {\n  height: 100%;\n}\n.tableLayout {\n  // height: calc(100vh - 32rem);\n  overflow: auto;\n}\n.el-dialog__wrapper {\n  overflow: hidden;\n}\n.el-dialog__body {\n  padding: 10px 20px;\n}\n.rightBtn {\n  // display: flex;\n  // justify-content: flex-end;\n  text-align: right;\n}\n.leftBtn {\n  text-align: left;\n  // display: flex;\n  // justify-content: flex-start;\n}\n.dialog-footer {\n  // 弹出框底部\n  margin-top: 1rem;\n}\n</style>\n"]}]}