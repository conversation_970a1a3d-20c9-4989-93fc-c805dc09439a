{"remainingRequest": "E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1\\数字运营平台-统一门户工程\\dopUnify\\node_modules\\thread-loader\\dist\\cjs.js!E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1\\数字运营平台-统一门户工程\\dopUnify\\node_modules\\babel-loader\\lib\\index.js!E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1\\数字运营平台-统一门户工程\\dopUnify\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1\\数字运营平台-统一门户工程\\dopUnify\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1\\数字运营平台-统一门户工程\\dopUnify\\src\\views\\home\\component\\shortcutMenu\\index.vue?vue&type=script&lang=js&", "dependencies": [{"path": "E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1\\数字运营平台-统一门户工程\\dopUnify\\src\\views\\home\\component\\shortcutMenu\\index.vue", "mtime": 1718240855080}, {"path": "E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1\\数字运营平台-统一门户工程\\dopUnify\\babel.config.js", "mtime": 1716875191548}, {"path": "E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1\\数字运营平台-统一门户工程\\dopUnify\\.babelrc", "mtime": 1716875191502}, {"path": "E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1\\数字运营平台-统一门户工程\\dopUnify\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1\\数字运营平台-统一门户工程\\dopUnify\\node_modules\\thread-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1\\数字运营平台-统一门户工程\\dopUnify\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1\\数字运营平台-统一门户工程\\dopUnify\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1\\数字运营平台-统一门户工程\\dopUnify\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1655715099000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, null]}