{"remainingRequest": "D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\node_modules\\babel-loader\\lib\\index.js!D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\node_modules\\qiankun\\es\\effects.js", "dependencies": [{"path": "D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\node_modules\\qiankun\\es\\effects.js", "mtime": 1667130453000}, {"path": "D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\babel.config.js", "mtime": 1686019818359}, {"path": "D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:LyoqCiAqIEBhdXRob3IgS3VpdG9zCiAqIEBzaW5jZSAyMDE5LTAyLTE5CiAqLwppbXBvcnQgeyBnZXRNb3VudGVkQXBwcywgbmF2aWdhdGVUb1VybCB9IGZyb20gJ3NpbmdsZS1zcGEnOwp2YXIgZmlyc3RNb3VudExvZ0xhYmVsID0gJ1txaWFua3VuXSBmaXJzdCBhcHAgbW91bnRlZCc7CmlmIChwcm9jZXNzLmVudi5OT0RFX0VOViA9PT0gJ2RldmVsb3BtZW50JykgewogIGNvbnNvbGUudGltZShmaXJzdE1vdW50TG9nTGFiZWwpOwp9CmV4cG9ydCBmdW5jdGlvbiBzZXREZWZhdWx0TW91bnRBcHAoZGVmYXVsdEFwcExpbmspIHsKICAvLyBjYW4gbm90IHVzZSBhZGRFdmVudExpc3RlbmVyIG9uY2Ugb3B0aW9uIGZvciBpZSBzdXBwb3J0CiAgd2luZG93LmFkZEV2ZW50TGlzdGVuZXIoJ3NpbmdsZS1zcGE6bm8tYXBwLWNoYW5nZScsIGZ1bmN0aW9uIGxpc3RlbmVyKCkgewogICAgdmFyIG1vdW50ZWRBcHBzID0gZ2V0TW91bnRlZEFwcHMoKTsKICAgIGlmICghbW91bnRlZEFwcHMubGVuZ3RoKSB7CiAgICAgIG5hdmlnYXRlVG9VcmwoZGVmYXVsdEFwcExpbmspOwogICAgfQogICAgd2luZG93LnJlbW92ZUV2ZW50TGlzdGVuZXIoJ3NpbmdsZS1zcGE6bm8tYXBwLWNoYW5nZScsIGxpc3RlbmVyKTsKICB9KTsKfQpleHBvcnQgZnVuY3Rpb24gcnVuRGVmYXVsdE1vdW50RWZmZWN0cyhkZWZhdWx0QXBwTGluaykgewogIGNvbnNvbGUud2FybignW3FpYW5rdW5dIHJ1bkRlZmF1bHRNb3VudEVmZmVjdHMgd2lsbCBiZSByZW1vdmVkIGluIG5leHQgdmVyc2lvbiwgcGxlYXNlIHVzZSBzZXREZWZhdWx0TW91bnRBcHAgaW5zdGVhZCcpOwogIHNldERlZmF1bHRNb3VudEFwcChkZWZhdWx0QXBwTGluayk7Cn0KZXhwb3J0IGZ1bmN0aW9uIHJ1bkFmdGVyRmlyc3RNb3VudGVkKGVmZmVjdCkgewogIC8vIGNhbiBub3QgdXNlIGFkZEV2ZW50TGlzdGVuZXIgb25jZSBvcHRpb24gZm9yIGllIHN1cHBvcnQKICB3aW5kb3cuYWRkRXZlbnRMaXN0ZW5lcignc2luZ2xlLXNwYTpmaXJzdC1tb3VudCcsIGZ1bmN0aW9uIGxpc3RlbmVyKCkgewogICAgaWYgKHByb2Nlc3MuZW52Lk5PREVfRU5WID09PSAnZGV2ZWxvcG1lbnQnKSB7CiAgICAgIGNvbnNvbGUudGltZUVuZChmaXJzdE1vdW50TG9nTGFiZWwpOwogICAgfQogICAgZWZmZWN0KCk7CiAgICB3aW5kb3cucmVtb3ZlRXZlbnRMaXN0ZW5lcignc2luZ2xlLXNwYTpmaXJzdC1tb3VudCcsIGxpc3RlbmVyKTsKICB9KTsKfQ=="}, {"version": 3, "names": ["getMountedApps", "navigateToUrl", "firstMountLogLabel", "process", "env", "NODE_ENV", "console", "time", "setDefaultMountApp", "defaultAppLink", "window", "addEventListener", "listener", "mountedApps", "length", "removeEventListener", "runDefaultMountEffects", "warn", "runAfterFirstMounted", "effect", "timeEnd"], "sources": ["D:/1_Project/XYD_Project/dop-4.0/dop-4.1-qianduan-unify/dopUnify/node_modules/qiankun/es/effects.js"], "sourcesContent": ["/**\n * <AUTHOR>\n * @since 2019-02-19\n */\nimport { getMountedApps, navigateToUrl } from 'single-spa';\nvar firstMountLogLabel = '[qiankun] first app mounted';\nif (process.env.NODE_ENV === 'development') {\n  console.time(firstMountLogLabel);\n}\nexport function setDefaultMountApp(defaultAppLink) {\n  // can not use addEventListener once option for ie support\n  window.addEventListener('single-spa:no-app-change', function listener() {\n    var mountedApps = getMountedApps();\n    if (!mountedApps.length) {\n      navigateToUrl(defaultAppLink);\n    }\n    window.removeEventListener('single-spa:no-app-change', listener);\n  });\n}\nexport function runDefaultMountEffects(defaultAppLink) {\n  console.warn('[qiankun] runDefaultMountEffects will be removed in next version, please use setDefaultMountApp instead');\n  setDefaultMountApp(defaultAppLink);\n}\nexport function runAfterFirstMounted(effect) {\n  // can not use addEventListener once option for ie support\n  window.addEventListener('single-spa:first-mount', function listener() {\n    if (process.env.NODE_ENV === 'development') {\n      console.timeEnd(firstMountLogLabel);\n    }\n    effect();\n    window.removeEventListener('single-spa:first-mount', listener);\n  });\n}"], "mappings": "AAAA;AACA;AACA;AACA;AACA,SAASA,cAAc,EAAEC,aAAa,QAAQ,YAAY;AAC1D,IAAIC,kBAAkB,GAAG,6BAA6B;AACtD,IAAIC,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,aAAa,EAAE;EAC1CC,OAAO,CAACC,IAAI,CAACL,kBAAkB,CAAC;AAClC;AACA,OAAO,SAASM,kBAAkB,CAACC,cAAc,EAAE;EACjD;EACAC,MAAM,CAACC,gBAAgB,CAAC,0BAA0B,EAAE,SAASC,QAAQ,GAAG;IACtE,IAAIC,WAAW,GAAGb,cAAc,EAAE;IAClC,IAAI,CAACa,WAAW,CAACC,MAAM,EAAE;MACvBb,aAAa,CAACQ,cAAc,CAAC;IAC/B;IACAC,MAAM,CAACK,mBAAmB,CAAC,0BAA0B,EAAEH,QAAQ,CAAC;EAClE,CAAC,CAAC;AACJ;AACA,OAAO,SAASI,sBAAsB,CAACP,cAAc,EAAE;EACrDH,OAAO,CAACW,IAAI,CAAC,yGAAyG,CAAC;EACvHT,kBAAkB,CAACC,cAAc,CAAC;AACpC;AACA,OAAO,SAASS,oBAAoB,CAACC,MAAM,EAAE;EAC3C;EACAT,MAAM,CAACC,gBAAgB,CAAC,wBAAwB,EAAE,SAASC,QAAQ,GAAG;IACpE,IAAIT,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,aAAa,EAAE;MAC1CC,OAAO,CAACc,OAAO,CAAClB,kBAAkB,CAAC;IACrC;IACAiB,MAAM,EAAE;IACRT,MAAM,CAACK,mBAAmB,CAAC,wBAAwB,EAAEH,QAAQ,CAAC;EAChE,CAAC,CAAC;AACJ"}]}