{"remainingRequest": "E:\\华南区项目\\中国银行\\开发区\\科技公司运营平台\\前端\\dopUnify\\node_modules\\babel-loader\\lib\\index.js!E:\\华南区项目\\中国银行\\开发区\\科技公司运营平台\\前端\\dopUnify\\node_modules\\eslint-loader\\index.js??ref--13-0!E:\\华南区项目\\中国银行\\开发区\\科技公司运营平台\\前端\\dopUnify\\src\\store\\modules\\settings.js", "dependencies": [{"path": "E:\\华南区项目\\中国银行\\开发区\\科技公司运营平台\\前端\\dopUnify\\src\\store\\modules\\settings.js", "mtime": 1721631954662}, {"path": "E:\\华南区项目\\中国银行\\开发区\\科技公司运营平台\\前端\\dopUnify\\babel.config.js", "mtime": 1721631954662}, {"path": "E:\\华南区项目\\中国银行\\开发区\\科技公司运营平台\\前端\\dopUnify\\.babelrc", "mtime": 1721631954662}, {"path": "E:\\华南区项目\\中国银行\\开发区\\科技公司运营平台\\前端\\dopUnify\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "E:\\华南区项目\\中国银行\\开发区\\科技公司运营平台\\前端\\dopUnify\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "E:\\华南区项目\\中国银行\\开发区\\科技公司运营平台\\前端\\dopUnify\\node_modules\\eslint-loader\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["variables", "defaultSettings", "commonBlank", "updateThemeColor", "tagsView", "fixedHeader", "sidebarLogo", "state", "defaultCorlor", "theme", "isDefault", "themeName", "mutations", "CHANGE_SETTING", "key", "value", "valueTheme", "color", "colorName", "hasOwnProperty", "isDefaultColor", "val", "defaultColors", "actions", "changeSetting", "data", "commit", "namespaced"], "sources": ["E:/华南区项目/中国银行/开发区/科技公司运营平台/前端/dopUnify/src/store/modules/settings.js"], "sourcesContent": ["import variables from '@/assets/scss/common/element-ui/element-variables.scss'\nimport defaultSettings from '@/settings'\nimport { commonBlank } from '@/utils/common'\nimport { updateThemeColor } from '@/components/Layout/ThemePicker/indexColor.js' // 配置整体风格\n\nconst { tagsView, fixedHeader, sidebarLogo } = defaultSettings\nconst state = {\n  defaultCorlor: variables.theme,\n  theme: '',\n  tagsView: tagsView,\n  fixedHeader: fixedHeader,\n  sidebarLogo: sidebarLogo,\n  isDefault: false,\n  themeName: ''\n}\n\nconst mutations = {\n  CHANGE_SETTING: (state, { key, value }) => {\n    let valueTheme = value\n    if (commonBlank(valueTheme)) {\n      valueTheme = {\n        color: '#2670F5',\n        colorName: 'mainBlue'\n      }\n    }\n    // eslint-disable-next-line no-prototype-builtins\n    if (state.hasOwnProperty(key)) {\n      state[key] = valueTheme.color\n      state.themeName = valueTheme.colorName\n    }\n    updateThemeColor(valueTheme.color)\n  },\n  isDefaultColor(state, val) {\n    state.isDefault = val\n    state.theme = variables.theme\n  },\n  defaultColors(state, val) {\n    state.isDefault = val\n  }\n}\n\nconst actions = {\n  changeSetting({ commit }, data) {\n    commit('CHANGE_SETTING', data)\n  }\n}\n\nexport default {\n  namespaced: true,\n  state,\n  mutations,\n  actions\n}\n"], "mappings": "AAAA,OAAOA,SAAS,MAAM,wDAAwD;AAC9E,OAAOC,eAAe,MAAM,YAAY;AACxC,SAASC,WAAW,QAAQ,gBAAgB;AAC5C,SAASC,gBAAgB,QAAQ,+CAA+C,EAAC;;AAEjF,IAAQC,QAAQ,GAA+BH,eAAe,CAAtDG,QAAQ;EAAEC,WAAW,GAAkBJ,eAAe,CAA5CI,WAAW;EAAEC,WAAW,GAAKL,eAAe,CAA/BK,WAAW;AAC1C,IAAMC,KAAK,GAAG;EACZC,aAAa,EAAER,SAAS,CAACS,KAAK;EAC9BA,KAAK,EAAE,EAAE;EACTL,QAAQ,EAAEA,QAAQ;EAClBC,WAAW,EAAEA,WAAW;EACxBC,WAAW,EAAEA,WAAW;EACxBI,SAAS,EAAE,KAAK;EAChBC,SAAS,EAAE;AACb,CAAC;AAED,IAAMC,SAAS,GAAG;EAChBC,cAAc,EAAE,wBAACN,KAAK,QAAqB;IAAA,IAAjBO,GAAG,QAAHA,GAAG;MAAEC,KAAK,QAALA,KAAK;IAClC,IAAIC,UAAU,GAAGD,KAAK;IACtB,IAAIb,WAAW,CAACc,UAAU,CAAC,EAAE;MAC3BA,UAAU,GAAG;QACXC,KAAK,EAAE,SAAS;QAChBC,SAAS,EAAE;MACb,CAAC;IACH;IACA;IACA,IAAIX,KAAK,CAACY,cAAc,CAACL,GAAG,CAAC,EAAE;MAC7BP,KAAK,CAACO,GAAG,CAAC,GAAGE,UAAU,CAACC,KAAK;MAC7BV,KAAK,CAACI,SAAS,GAAGK,UAAU,CAACE,SAAS;IACxC;IACAf,gBAAgB,CAACa,UAAU,CAACC,KAAK,CAAC;EACpC,CAAC;EACDG,cAAc,0BAACb,KAAK,EAAEc,GAAG,EAAE;IACzBd,KAAK,CAACG,SAAS,GAAGW,GAAG;IACrBd,KAAK,CAACE,KAAK,GAAGT,SAAS,CAACS,KAAK;EAC/B,CAAC;EACDa,aAAa,yBAACf,KAAK,EAAEc,GAAG,EAAE;IACxBd,KAAK,CAACG,SAAS,GAAGW,GAAG;EACvB;AACF,CAAC;AAED,IAAME,OAAO,GAAG;EACdC,aAAa,gCAAaC,IAAI,EAAE;IAAA,IAAhBC,MAAM,SAANA,MAAM;IACpBA,MAAM,CAAC,gBAAgB,EAAED,IAAI,CAAC;EAChC;AACF,CAAC;AAED,eAAe;EACbE,UAAU,EAAE,IAAI;EAChBpB,KAAK,EAALA,KAAK;EACLK,SAAS,EAATA,SAAS;EACTW,OAAO,EAAPA;AACF,CAAC"}]}