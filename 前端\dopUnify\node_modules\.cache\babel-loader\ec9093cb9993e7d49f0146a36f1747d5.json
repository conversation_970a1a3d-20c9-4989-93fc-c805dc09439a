{"remainingRequest": "D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\node_modules\\babel-loader\\lib\\index.js!D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\src\\views\\home\\component\\shortcutMenu\\index.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\src\\views\\home\\component\\shortcutMenu\\index.vue", "mtime": 1686019809498}, {"path": "D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\babel.config.js", "mtime": 1686019818359}, {"path": "D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1655715099000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA2BA;AACA;AACA;AACA;AACA;EACAA;EACAC;EACAC;IACA;MACAC;MACAC;IACA;EACA;EACAC;IAAA;IACA;IACA;IACA;MACA;QACA;MACA;IACA;EACA;EACAC;IACA;EACA;EACAC;IACAC;MAAA;MACA;QACAC;QACAC;MACA;MACAC;QACA;QACA;QACAC;UACA;YACAC;YACAC;YACAC;YACAC;YACAC;UACA;UACAC;QACA;QACA;UACA;QACA;QACA;MACA;IACA;IACAC;MACA;IACA;IACAC;MACA;QAAAC;MAAA;IACA;EACA;AACA", "names": ["name", "components", "data", "title", "selectData", "mounted", "<PERSON><PERSON><PERSON><PERSON>", "methods", "initMenu", "parameterList", "sysMap", "queryPortal", "returnlist", "id", "label", "path", "class", "order", "list", "edit", "getMenu", "to"], "sourceRoot": "src/views/home/<USER>/shortcutMenu", "sources": ["index.vue"], "sourcesContent": ["<!--\n* 首页-快捷菜单\n-->\n<template>\n  <div class=\"homeContent\">\n    <div class=\"homePageTitle\">\n      {{ title }} <span class=\"InfoMore\" @click=\"edit\">进入编辑 ></span>\n    </div>\n    <div class=\"homePageBox\">\n      <div class=\"menuBox\">\n        <router-link\n          v-for=\"item in selectData\"\n          :key=\"item.id\"\n          class=\"menuItem\"\n          v-bind=\"getMenu(item.path)\"\n        >\n          <div class=\"menuItemIcon\">\n            <sun-svg-icon :icon-class=\"'color-' + item.class\" />\n          </div>\n          <span class=\"homeTwoTitle\" :title=\"item.label\">{{ item.label }}</span>\n        </router-link>\n      </div>\n    </div>\n  </div>\n</template>\n\n<script>\nimport { Home } from '@/api'\nconst { queryPortal } = Home\n// import { ShortcutMenu } from '@/api'\n// const { query } = ShortcutMenu\nexport default {\n  name: 'ShortcutMenu',\n  components: {},\n  data() {\n    return {\n      title: '快捷菜单',\n      selectData: []\n    }\n  },\n  mounted() {\n    this.initMenu()\n    // 首页刷新：事件总线\n    this.$bus.$on('shortMenuFlushed', (params) => {\n      if (params) {\n        this.initMenu()\n      }\n    })\n  },\n  beforeDestroy() {\n    this.$bus.$off('shortMenuFlushed')\n  },\n  methods: {\n    initMenu() {\n      const msg = {\n        parameterList: [],\n        sysMap: {}\n      }\n      queryPortal(msg).then((res) => {\n        const { returnlist } = res.retMap\n        const list = []\n        returnlist.map((item) => {\n          const node = {\n            id: item.MENU_ID,\n            label: item.MENU_LABEL,\n            path: item.MENU_URL,\n            class: item.ICON_SRC,\n            order: item.ICON_ORDER\n          }\n          list.push(node)\n        })\n        this.selectData = list.sort(function(a, b) {\n          return b - a\n        })\n        // console.log(this.selectData)\n      })\n    },\n    edit() {\n      this.$router.push('/shortcutMenuEdit')\n    },\n    getMenu(path) {\n      return { to: path }\n    }\n  }\n}\n</script>\n\n<style scoped lang=\"scss\">\n@import '~@/assets/scss/common/variable/variable/size.scss';\n.InfoMore {\n  float: right;\n  color: gray;\n  font-size: $noteFomt;\n  cursor: pointer;\n}\n</style>\n"]}]}