package com.sunyard.etl.ocr.bean;

import com.xxl.job.core.log.XxlJobLogger;

import java.math.BigDecimal;
import java.sql.ResultSet;

public class TmpBatch {

    public TmpBatch(){}

    public TmpBatch(ResultSet rs) {
        try {
            batchCommit = rs.getString("BATCH_COMMIT");//批次扫描提交状态（批次全部图像提交）  0 未提交  1 已提交
            batchId = rs.getString("BATCH_ID");//批次号
            batchLock = rs.getString("BATCH_LOCK");//批次锁定标志（中心应用能否读取批次锁） 0未读取（默认） 1已读取
            batchTotalPage = rs.getString("BATCH_TOTAL_PAGE");//批次扫描图像总数
            businessId = rs.getShort("BUSINESS_ID");//业务类型
            imageStatus = rs.getString("IMAGE_STATUS");//图像状态1在临时库,2-已归档，3-已清理
            inputDate = rs.getString("INPUT_DATE");//扫描录入日期
            inputTime = rs.getString("INPUT_TIME");//扫描提交精确时间
            inputWorker = rs.getString("INPUT_WORKER");//扫描录入人员
            isInvalid = rs.getString("IS_INVALID");//批次是否有效 0 无效 (作废)    1 有效
            machineId = rs.getString("MACHINE_ID");//扫描机器号
            needProcess = rs.getString("NEED_PROCESS");//是否需要处理标志（扫描时指定） 0 不需处理（默认）  1需要处理
            occurDate = rs.getString("OCCUR_DATE");//业务日期
            ocrFactorFlag = rs.getString("OCR_FACTOR_FLAG");//要素识别标志0-未处理，1-队列中，2-已处理
            operatorNo = rs.getString("OPERATOR_NO");//柜员ID
            priorityLevel = rs.getString("PRIORITY_LEVEL");//优先级字段
            procinstid = rs.getBigDecimal("PROCINSTID");//工作流实例id
            progressFlag = rs.getString("PROGRESS_FLAG");//处理标志 10等待OCR识别（默认）20等待人工补录  30等待业务审核  40等待核实(中心应用不读取)     98处理完成  99索引更新完成
            siteNo = rs.getString("SITE_NO");//交易机构
            tempDataTable = rs.getString("TEMP_DATA_TABLE");//临时数据表名
            worker = rs.getString("WORKER");//null
            workTime = rs.getString("WORK_TIME");//null
            contentId = rs.getString("CONTENT_ID");//ECM批次号
        } catch (Exception e) {
            // TODO: handle exception
            XxlJobLogger.log("批次数据实例化时发生异常:{0}", e);
        }
    }

    private String batchId;

    private String batchLock;

    private String batchTotalPage;

    private Short businessId;

    private String batchCommit;

    private String inputDate;

    private String tempDataTable;

    private String inputWorker;

    private String machineId;

    private String needProcess;

    private String occurDate;

    private String operatorNo;

    private String progressFlag;

    private String siteNo;

    private String inputTime;

    private String imageStatus;

    private String isInvalid;

    private String priorityLevel;

    private String worker;

    private String workTime;

    private String ocrFactorFlag;

    private BigDecimal procinstid;

    private String contentId;

    public String getContentId() {
        return contentId;
    }

    public void setContentId(String contentId) {
        this.contentId = contentId;
    }

    public String getBatchId() {
        return batchId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column BP_TMPBATCH_TB.BATCH_ID
     *
     * @param batchId the value for BP_TMPBATCH_TB.BATCH_ID
     *
     * @mbggenerated
     */
    public void setBatchId(String batchId) {
        this.batchId = batchId == null ? null : batchId.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column BP_TMPBATCH_TB.BATCH_LOCK
     *
     * @return the value of BP_TMPBATCH_TB.BATCH_LOCK
     *
     * @mbggenerated
     */
    public String getBatchLock() {
        return batchLock;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column BP_TMPBATCH_TB.BATCH_LOCK
     *
     * @param batchLock the value for BP_TMPBATCH_TB.BATCH_LOCK
     *
     * @mbggenerated
     */
    public void setBatchLock(String batchLock) {
        this.batchLock = batchLock == null ? null : batchLock.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column BP_TMPBATCH_TB.BATCH_TOTAL_PAGE
     *
     * @return the value of BP_TMPBATCH_TB.BATCH_TOTAL_PAGE
     *
     * @mbggenerated
     */
    public String getBatchTotalPage() {
        return batchTotalPage;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column BP_TMPBATCH_TB.BATCH_TOTAL_PAGE
     *
     * @param batchTotalPage the value for BP_TMPBATCH_TB.BATCH_TOTAL_PAGE
     *
     * @mbggenerated
     */
    public void setBatchTotalPage(String batchTotalPage) {
        this.batchTotalPage = batchTotalPage == null ? null : batchTotalPage.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column BP_TMPBATCH_TB.BUSINESS_ID
     *
     * @return the value of BP_TMPBATCH_TB.BUSINESS_ID
     *
     * @mbggenerated
     */
    public Short getBusinessId() {
        return businessId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column BP_TMPBATCH_TB.BUSINESS_ID
     *
     * @param businessId the value for BP_TMPBATCH_TB.BUSINESS_ID
     *
     * @mbggenerated
     */
    public void setBusinessId(Short businessId) {
        this.businessId = businessId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column BP_TMPBATCH_TB.BATCH_COMMIT
     *
     * @return the value of BP_TMPBATCH_TB.BATCH_COMMIT
     *
     * @mbggenerated
     */
    public String getBatchCommit() {
        return batchCommit;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column BP_TMPBATCH_TB.BATCH_COMMIT
     *
     * @param batchCommit the value for BP_TMPBATCH_TB.BATCH_COMMIT
     *
     * @mbggenerated
     */
    public void setBatchCommit(String batchCommit) {
        this.batchCommit = batchCommit == null ? null : batchCommit.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column BP_TMPBATCH_TB.INPUT_DATE
     *
     * @return the value of BP_TMPBATCH_TB.INPUT_DATE
     *
     * @mbggenerated
     */
    public String getInputDate() {
        return inputDate;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column BP_TMPBATCH_TB.INPUT_DATE
     *
     * @param inputDate the value for BP_TMPBATCH_TB.INPUT_DATE
     *
     * @mbggenerated
     */
    public void setInputDate(String inputDate) {
        this.inputDate = inputDate == null ? null : inputDate.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column BP_TMPBATCH_TB.TEMP_DATA_TABLE
     *
     * @return the value of BP_TMPBATCH_TB.TEMP_DATA_TABLE
     *
     * @mbggenerated
     */
    public String getTempDataTable() {
        return tempDataTable;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column BP_TMPBATCH_TB.TEMP_DATA_TABLE
     *
     * @param tempDataTable the value for BP_TMPBATCH_TB.TEMP_DATA_TABLE
     *
     * @mbggenerated
     */
    public void setTempDataTable(String tempDataTable) {
        this.tempDataTable = tempDataTable == null ? null : tempDataTable.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column BP_TMPBATCH_TB.INPUT_WORKER
     *
     * @return the value of BP_TMPBATCH_TB.INPUT_WORKER
     *
     * @mbggenerated
     */
    public String getInputWorker() {
        return inputWorker;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column BP_TMPBATCH_TB.INPUT_WORKER
     *
     * @param inputWorker the value for BP_TMPBATCH_TB.INPUT_WORKER
     *
     * @mbggenerated
     */
    public void setInputWorker(String inputWorker) {
        this.inputWorker = inputWorker == null ? null : inputWorker.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column BP_TMPBATCH_TB.MACHINE_ID
     *
     * @return the value of BP_TMPBATCH_TB.MACHINE_ID
     *
     * @mbggenerated
     */
    public String getMachineId() {
        return machineId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column BP_TMPBATCH_TB.MACHINE_ID
     *
     * @param machineId the value for BP_TMPBATCH_TB.MACHINE_ID
     *
     * @mbggenerated
     */
    public void setMachineId(String machineId) {
        this.machineId = machineId == null ? null : machineId.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column BP_TMPBATCH_TB.NEED_PROCESS
     *
     * @return the value of BP_TMPBATCH_TB.NEED_PROCESS
     *
     * @mbggenerated
     */
    public String getNeedProcess() {
        return needProcess;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column BP_TMPBATCH_TB.NEED_PROCESS
     *
     * @param needProcess the value for BP_TMPBATCH_TB.NEED_PROCESS
     *
     * @mbggenerated
     */
    public void setNeedProcess(String needProcess) {
        this.needProcess = needProcess == null ? null : needProcess.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column BP_TMPBATCH_TB.OCCUR_DATE
     *
     * @return the value of BP_TMPBATCH_TB.OCCUR_DATE
     *
     * @mbggenerated
     */
    public String getOccurDate() {
        return occurDate;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column BP_TMPBATCH_TB.OCCUR_DATE
     *
     * @param occurDate the value for BP_TMPBATCH_TB.OCCUR_DATE
     *
     * @mbggenerated
     */
    public void setOccurDate(String occurDate) {
        this.occurDate = occurDate == null ? null : occurDate.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column BP_TMPBATCH_TB.OPERATOR_NO
     *
     * @return the value of BP_TMPBATCH_TB.OPERATOR_NO
     *
     * @mbggenerated
     */
    public String getOperatorNo() {
        return operatorNo;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column BP_TMPBATCH_TB.OPERATOR_NO
     *
     * @param operatorNo the value for BP_TMPBATCH_TB.OPERATOR_NO
     *
     * @mbggenerated
     */
    public void setOperatorNo(String operatorNo) {
        this.operatorNo = operatorNo == null ? null : operatorNo.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column BP_TMPBATCH_TB.PROGRESS_FLAG
     *
     * @return the value of BP_TMPBATCH_TB.PROGRESS_FLAG
     *
     * @mbggenerated
     */
    public String getProgressFlag() {
        return progressFlag;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column BP_TMPBATCH_TB.PROGRESS_FLAG
     *
     * @param progressFlag the value for BP_TMPBATCH_TB.PROGRESS_FLAG
     *
     * @mbggenerated
     */
    public void setProgressFlag(String progressFlag) {
        this.progressFlag = progressFlag == null ? null : progressFlag.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column BP_TMPBATCH_TB.SITE_NO
     *
     * @return the value of BP_TMPBATCH_TB.SITE_NO
     *
     * @mbggenerated
     */
    public String getSiteNo() {
        return siteNo;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column BP_TMPBATCH_TB.SITE_NO
     *
     * @param siteNo the value for BP_TMPBATCH_TB.SITE_NO
     *
     * @mbggenerated
     */
    public void setSiteNo(String siteNo) {
        this.siteNo = siteNo == null ? null : siteNo.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column BP_TMPBATCH_TB.INPUT_TIME
     *
     * @return the value of BP_TMPBATCH_TB.INPUT_TIME
     *
     * @mbggenerated
     */
    public String getInputTime() {
        return inputTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column BP_TMPBATCH_TB.INPUT_TIME
     *
     * @param inputTime the value for BP_TMPBATCH_TB.INPUT_TIME
     *
     * @mbggenerated
     */
    public void setInputTime(String inputTime) {
        this.inputTime = inputTime == null ? null : inputTime.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column BP_TMPBATCH_TB.IMAGE_STATUS
     *
     * @return the value of BP_TMPBATCH_TB.IMAGE_STATUS
     *
     * @mbggenerated
     */
    public String getImageStatus() {
        return imageStatus;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column BP_TMPBATCH_TB.IMAGE_STATUS
     *
     * @param imageStatus the value for BP_TMPBATCH_TB.IMAGE_STATUS
     *
     * @mbggenerated
     */
    public void setImageStatus(String imageStatus) {
        this.imageStatus = imageStatus == null ? null : imageStatus.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column BP_TMPBATCH_TB.IS_INVALID
     *
     * @return the value of BP_TMPBATCH_TB.IS_INVALID
     *
     * @mbggenerated
     */
    public String getIsInvalid() {
        return isInvalid;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column BP_TMPBATCH_TB.IS_INVALID
     *
     * @param isInvalid the value for BP_TMPBATCH_TB.IS_INVALID
     *
     * @mbggenerated
     */
    public void setIsInvalid(String isInvalid) {
        this.isInvalid = isInvalid == null ? null : isInvalid.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column BP_TMPBATCH_TB.PRIORITY_LEVEL
     *
     * @return the value of BP_TMPBATCH_TB.PRIORITY_LEVEL
     *
     * @mbggenerated
     */
    public String getPriorityLevel() {
        return priorityLevel;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column BP_TMPBATCH_TB.PRIORITY_LEVEL
     *
     * @param priorityLevel the value for BP_TMPBATCH_TB.PRIORITY_LEVEL
     *
     * @mbggenerated
     */
    public void setPriorityLevel(String priorityLevel) {
        this.priorityLevel = priorityLevel == null ? null : priorityLevel.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column BP_TMPBATCH_TB.WORKER
     *
     * @return the value of BP_TMPBATCH_TB.WORKER
     *
     * @mbggenerated
     */
    public String getWorker() {
        return worker;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column BP_TMPBATCH_TB.WORKER
     *
     * @param worker the value for BP_TMPBATCH_TB.WORKER
     *
     * @mbggenerated
     */
    public void setWorker(String worker) {
        this.worker = worker == null ? null : worker.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column BP_TMPBATCH_TB.WORK_TIME
     *
     * @return the value of BP_TMPBATCH_TB.WORK_TIME
     *
     * @mbggenerated
     */
    public String getWorkTime() {
        return workTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column BP_TMPBATCH_TB.WORK_TIME
     *
     * @param workTime the value for BP_TMPBATCH_TB.WORK_TIME
     *
     * @mbggenerated
     */
    public void setWorkTime(String workTime) {
        this.workTime = workTime == null ? null : workTime.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column BP_TMPBATCH_TB.OCR_FACTOR_FLAG
     *
     * @return the value of BP_TMPBATCH_TB.OCR_FACTOR_FLAG
     *
     * @mbggenerated
     */
    public String getOcrFactorFlag() {
        return ocrFactorFlag;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column BP_TMPBATCH_TB.OCR_FACTOR_FLAG
     *
     * @param ocrFactorFlag the value for BP_TMPBATCH_TB.OCR_FACTOR_FLAG
     *
     * @mbggenerated
     */
    public void setOcrFactorFlag(String ocrFactorFlag) {
        this.ocrFactorFlag = ocrFactorFlag == null ? null : ocrFactorFlag.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column BP_TMPBATCH_TB.PROCINSTID
     *
     * @return the value of BP_TMPBATCH_TB.PROCINSTID
     *
     * @mbggenerated
     */
    public BigDecimal getProcinstid() {
        return procinstid;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column BP_TMPBATCH_TB.PROCINSTID
     *
     * @param procinstid the value for BP_TMPBATCH_TB.PROCINSTID
     *
     * @mbggenerated
     */
    public void setProcinstid(BigDecimal procinstid) {
        this.procinstid = procinstid;
    }
}
