{"remainingRequest": "E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1\\数字运营平台-统一门户工程\\dopUnify\\node_modules\\babel-loader\\lib\\index.js!E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1\\数字运营平台-统一门户工程\\dopUnify\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1\\数字运营平台-统一门户工程\\dopUnify\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1\\数字运营平台-统一门户工程\\dopUnify\\src\\views\\home\\component\\important\\index.vue?vue&type=script&lang=js&", "dependencies": [{"path": "E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1\\数字运营平台-统一门户工程\\dopUnify\\src\\views\\home\\component\\important\\index.vue", "mtime": 1716875179005}, {"path": "E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1\\数字运营平台-统一门户工程\\dopUnify\\babel.config.js", "mtime": 1716875191548}, {"path": "E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1\\数字运营平台-统一门户工程\\dopUnify\\.babelrc", "mtime": 1716875191502}, {"path": "E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1\\数字运营平台-统一门户工程\\dopUnify\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1\\数字运营平台-统一门户工程\\dopUnify\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1\\数字运营平台-统一门户工程\\dopUnify\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1\\数字运营平台-统一门户工程\\dopUnify\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1655715099000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, null]}