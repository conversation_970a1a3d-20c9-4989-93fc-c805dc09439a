{"remainingRequest": "D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\node_modules\\babel-loader\\lib\\index.js!D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\node_modules\\eslint-loader\\index.js??ref--13-0!D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\src\\directive\\waves\\waves.js", "dependencies": [{"path": "D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\src\\directive\\waves\\waves.js", "mtime": 1686019818250}, {"path": "D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\babel.config.js", "mtime": 1686019818359}, {"path": "D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\.babelrc", "mtime": 1705285078668}, {"path": "D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\node_modules\\eslint-loader\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["context", "handleClick", "el", "binding", "handle", "e", "customOpts", "Object", "assign", "value", "opts", "ele", "type", "color", "target", "style", "position", "overflow", "rect", "getBoundingClientRect", "ripple", "querySelector", "document", "createElement", "className", "height", "width", "Math", "max", "append<PERSON><PERSON><PERSON>", "top", "offsetHeight", "left", "offsetWidth", "pageY", "documentElement", "scrollTop", "body", "pageX", "scrollLeft", "backgroundColor", "<PERSON><PERSON><PERSON><PERSON>", "bind", "addEventListener", "update", "removeEventListener", "unbind"], "sources": ["D:/1_Project/XYD_Project/dop-4.0/dop-4.1-qianduan-unify/dopUnify/src/directive/waves/waves.js"], "sourcesContent": ["import './waves.css'\n\nconst context = '@@wavesContext'\n\nfunction handleClick(el, binding) {\n  function handle(e) {\n    const customOpts = Object.assign({}, binding.value)\n    const opts = Object.assign({\n      ele: el, // 波纹作用元素\n      type: 'hit', // hit 点击位置扩散 center中心点扩展\n      color: 'rgba(0, 0, 0, 0.15)' // 波纹颜色\n    },\n    customOpts\n    )\n    const target = opts.ele\n    if (target) {\n      target.style.position = 'relative'\n      target.style.overflow = 'hidden'\n      const rect = target.getBoundingClientRect()\n      let ripple = target.querySelector('.waves-ripple')\n      if (!ripple) {\n        ripple = document.createElement('span')\n        ripple.className = 'waves-ripple'\n        ripple.style.height = ripple.style.width = Math.max(rect.width, rect.height) + 'px'\n        target.appendChild(ripple)\n      } else {\n        ripple.className = 'waves-ripple'\n      }\n      switch (opts.type) {\n        case 'center':\n          ripple.style.top = rect.height / 2 - ripple.offsetHeight / 2 + 'px'\n          ripple.style.left = rect.width / 2 - ripple.offsetWidth / 2 + 'px'\n          break\n        default:\n          ripple.style.top =\n            (e.pageY - rect.top - ripple.offsetHeight / 2 - document.documentElement.scrollTop ||\n              document.body.scrollTop) + 'px'\n          ripple.style.left =\n            (e.pageX - rect.left - ripple.offsetWidth / 2 - document.documentElement.scrollLeft ||\n              document.body.scrollLeft) + 'px'\n      }\n      ripple.style.backgroundColor = opts.color\n      ripple.className = 'waves-ripple z-active'\n      return false\n    }\n  }\n\n  if (!el[context]) {\n    el[context] = {\n      removeHandle: handle\n    }\n  } else {\n    el[context].removeHandle = handle\n  }\n\n  return handle\n}\n\nexport default {\n  bind(el, binding) {\n    el.addEventListener('click', handleClick(el, binding), false)\n  },\n  update(el, binding) {\n    el.removeEventListener('click', el[context].removeHandle, false)\n    el.addEventListener('click', handleClick(el, binding), false)\n  },\n  unbind(el) {\n    el.removeEventListener('click', el[context].removeHandle, false)\n    el[context] = null\n    delete el[context]\n  }\n}\n"], "mappings": "AAAA,OAAO,aAAa;AAEpB,IAAMA,OAAO,GAAG,gBAAgB;AAEhC,SAASC,WAAW,CAACC,EAAE,EAAEC,OAAO,EAAE;EAChC,SAASC,MAAM,CAACC,CAAC,EAAE;IACjB,IAAMC,UAAU,GAAGC,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,EAAEL,OAAO,CAACM,KAAK,CAAC;IACnD,IAAMC,IAAI,GAAGH,MAAM,CAACC,MAAM,CAAC;MACzBG,GAAG,EAAET,EAAE;MAAE;MACTU,IAAI,EAAE,KAAK;MAAE;MACbC,KAAK,EAAE,qBAAqB,CAAC;IAC/B,CAAC,EACDP,UAAU,CACT;IACD,IAAMQ,MAAM,GAAGJ,IAAI,CAACC,GAAG;IACvB,IAAIG,MAAM,EAAE;MACVA,MAAM,CAACC,KAAK,CAACC,QAAQ,GAAG,UAAU;MAClCF,MAAM,CAACC,KAAK,CAACE,QAAQ,GAAG,QAAQ;MAChC,IAAMC,IAAI,GAAGJ,MAAM,CAACK,qBAAqB,EAAE;MAC3C,IAAIC,MAAM,GAAGN,MAAM,CAACO,aAAa,CAAC,eAAe,CAAC;MAClD,IAAI,CAACD,MAAM,EAAE;QACXA,MAAM,GAAGE,QAAQ,CAACC,aAAa,CAAC,MAAM,CAAC;QACvCH,MAAM,CAACI,SAAS,GAAG,cAAc;QACjCJ,MAAM,CAACL,KAAK,CAACU,MAAM,GAAGL,MAAM,CAACL,KAAK,CAACW,KAAK,GAAGC,IAAI,CAACC,GAAG,CAACV,IAAI,CAACQ,KAAK,EAAER,IAAI,CAACO,MAAM,CAAC,GAAG,IAAI;QACnFX,MAAM,CAACe,WAAW,CAACT,MAAM,CAAC;MAC5B,CAAC,MAAM;QACLA,MAAM,CAACI,SAAS,GAAG,cAAc;MACnC;MACA,QAAQd,IAAI,CAACE,IAAI;QACf,KAAK,QAAQ;UACXQ,MAAM,CAACL,KAAK,CAACe,GAAG,GAAGZ,IAAI,CAACO,MAAM,GAAG,CAAC,GAAGL,MAAM,CAACW,YAAY,GAAG,CAAC,GAAG,IAAI;UACnEX,MAAM,CAACL,KAAK,CAACiB,IAAI,GAAGd,IAAI,CAACQ,KAAK,GAAG,CAAC,GAAGN,MAAM,CAACa,WAAW,GAAG,CAAC,GAAG,IAAI;UAClE;QACF;UACEb,MAAM,CAACL,KAAK,CAACe,GAAG,GACd,CAACzB,CAAC,CAAC6B,KAAK,GAAGhB,IAAI,CAACY,GAAG,GAAGV,MAAM,CAACW,YAAY,GAAG,CAAC,GAAGT,QAAQ,CAACa,eAAe,CAACC,SAAS,IAChFd,QAAQ,CAACe,IAAI,CAACD,SAAS,IAAI,IAAI;UACnChB,MAAM,CAACL,KAAK,CAACiB,IAAI,GACf,CAAC3B,CAAC,CAACiC,KAAK,GAAGpB,IAAI,CAACc,IAAI,GAAGZ,MAAM,CAACa,WAAW,GAAG,CAAC,GAAGX,QAAQ,CAACa,eAAe,CAACI,UAAU,IACjFjB,QAAQ,CAACe,IAAI,CAACE,UAAU,IAAI,IAAI;MAAA;MAExCnB,MAAM,CAACL,KAAK,CAACyB,eAAe,GAAG9B,IAAI,CAACG,KAAK;MACzCO,MAAM,CAACI,SAAS,GAAG,uBAAuB;MAC1C,OAAO,KAAK;IACd;EACF;EAEA,IAAI,CAACtB,EAAE,CAACF,OAAO,CAAC,EAAE;IAChBE,EAAE,CAACF,OAAO,CAAC,GAAG;MACZyC,YAAY,EAAErC;IAChB,CAAC;EACH,CAAC,MAAM;IACLF,EAAE,CAACF,OAAO,CAAC,CAACyC,YAAY,GAAGrC,MAAM;EACnC;EAEA,OAAOA,MAAM;AACf;AAEA,eAAe;EACbsC,IAAI,gBAACxC,EAAE,EAAEC,OAAO,EAAE;IAChBD,EAAE,CAACyC,gBAAgB,CAAC,OAAO,EAAE1C,WAAW,CAACC,EAAE,EAAEC,OAAO,CAAC,EAAE,KAAK,CAAC;EAC/D,CAAC;EACDyC,MAAM,kBAAC1C,EAAE,EAAEC,OAAO,EAAE;IAClBD,EAAE,CAAC2C,mBAAmB,CAAC,OAAO,EAAE3C,EAAE,CAACF,OAAO,CAAC,CAACyC,YAAY,EAAE,KAAK,CAAC;IAChEvC,EAAE,CAACyC,gBAAgB,CAAC,OAAO,EAAE1C,WAAW,CAACC,EAAE,EAAEC,OAAO,CAAC,EAAE,KAAK,CAAC;EAC/D,CAAC;EACD2C,MAAM,kBAAC5C,EAAE,EAAE;IACTA,EAAE,CAAC2C,mBAAmB,CAAC,OAAO,EAAE3C,EAAE,CAACF,OAAO,CAAC,CAACyC,YAAY,EAAE,KAAK,CAAC;IAChEvC,EAAE,CAACF,OAAO,CAAC,GAAG,IAAI;IAClB,OAAOE,EAAE,CAACF,OAAO,CAAC;EACpB;AACF,CAAC"}]}