{"remainingRequest": "E:\\华南区项目\\中国银行\\开发区\\科技公司运营平台\\前端\\dopUnify\\node_modules\\babel-loader\\lib\\index.js!E:\\华南区项目\\中国银行\\开发区\\科技公司运营平台\\前端\\dopUnify\\node_modules\\eslint-loader\\index.js??ref--13-0!E:\\华南区项目\\中国银行\\开发区\\科技公司运营平台\\前端\\dopUnify\\src\\views\\system\\externalManage\\user\\info.js", "dependencies": [{"path": "E:\\华南区项目\\中国银行\\开发区\\科技公司运营平台\\前端\\dopUnify\\src\\views\\system\\externalManage\\user\\info.js", "mtime": 1721631954662}, {"path": "E:\\华南区项目\\中国银行\\开发区\\科技公司运营平台\\前端\\dopUnify\\babel.config.js", "mtime": 1721631954662}, {"path": "E:\\华南区项目\\中国银行\\开发区\\科技公司运营平台\\前端\\dopUnify\\.babelrc", "mtime": 1721631954662}, {"path": "E:\\华南区项目\\中国银行\\开发区\\科技公司运营平台\\前端\\dopUnify\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "E:\\华南区项目\\中国银行\\开发区\\科技公司运营平台\\前端\\dopUnify\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "E:\\华南区项目\\中国银行\\开发区\\科技公司运营平台\\前端\\dopUnify\\node_modules\\eslint-loader\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8gaW1wb3J0IHsgZGljdGlvbmFyeUZpZWRzIH0gZnJvbSAnQC91dGlscy9kaWN0aW9uYXJ5JyAvLyDlrZflhbgKLy8g6KGo5Y2VCmV4cG9ydCB2YXIgY29uZmlnID0gZnVuY3Rpb24gY29uZmlnKHRoYXQpIHsKICByZXR1cm4gewogICAgZXh0ZXJuYWxfc3lzdGVtX25vOiB7CiAgICAgIGNvbXBvbmVudDogJ3NlbGVjdCcsCiAgICAgIGxhYmVsOiAn57O757uf57yW5Y+3JywKICAgICAgY29sU3BhbjogOCwKICAgICAgbmFtZTogJ2V4dGVybmFsX3N5c3RlbV9ubycsCiAgICAgIGNvbmZpZzogewogICAgICAgIC8vIGZvcm0taXRlbSDphY3nva4KICAgICAgfSwKICAgICAgY29tcG9uZW50UHJvcHM6IHsKICAgICAgICAvLyBpbnB1dOe7hOS7tumFjee9rgogICAgICAgIHBsYWNlaG9sZGVyOiAn6K+36YCJ5oupJywKICAgICAgICBjbGVhcmFibGU6IHRydWUKICAgICAgfSwKICAgICAgb3B0aW9uczogW10KICAgIH0sCiAgICB1c2VyX25hbWU6IHsKICAgICAgY29tcG9uZW50OiAnaW5wdXQnLAogICAgICBsYWJlbDogJ+eUqOaIt+WQjS/nlKjmiLfnvJblj7cnLAogICAgICBjb2xTcGFuOiA4LAogICAgICBuYW1lOiAndXNlcl9uYW1lJywKICAgICAgY29uZmlnOiB7CiAgICAgICAgcnVsZXM6IFt7CiAgICAgICAgICBtaW46IDAsCiAgICAgICAgICBtYXg6IDUwLAogICAgICAgICAgbWVzc2FnZTogJ+ivt+acgOWkmuWhq+WGmTUw5Liq5a2X56ymJwogICAgICAgIH1dCiAgICAgIH0sCiAgICAgIGNvbXBvbmVudFByb3BzOiB7CiAgICAgICAgcGxhY2Vob2xkZXI6ICfmlK/mjIHmjInnlKjmiLflkI3jgIHnlKjmiLfnvJblj7fmqKHns4rmn6Xor6InLAogICAgICAgIGZpbHRlcmFibGU6IHRydWUsCiAgICAgICAgY2xlYXJhYmxlOiB0cnVlCiAgICAgIH0KICAgIH0KICB9Owp9Ow=="}, {"version": 3, "names": ["config", "that", "external_system_no", "component", "label", "colSpan", "name", "componentProps", "placeholder", "clearable", "options", "user_name", "rules", "min", "max", "message", "filterable"], "sources": ["E:/华南区项目/中国银行/开发区/科技公司运营平台/前端/dopUnify/src/views/system/externalManage/user/info.js"], "sourcesContent": ["// import { dictionaryFieds } from '@/utils/dictionary' // 字典\r\n// 表单\r\nexport const config = (that) => ({\r\n  external_system_no: {\r\n    component: 'select',\r\n    label: '系统编号',\r\n    colSpan: 8,\r\n    name: 'external_system_no',\r\n    config: {\r\n      // form-item 配置\r\n    },\r\n    componentProps: {\r\n      // input组件配置\r\n      placeholder: '请选择',\r\n      clearable: true\r\n    },\r\n    options: []\r\n  },\r\n  user_name: {\r\n    component: 'input',\r\n    label: '用户名/用户编号',\r\n    colSpan: 8,\r\n    name: 'user_name',\r\n    config: {\r\n      rules: [\r\n        { min: 0, max: 50, message: '请最多填写50个字符' }\r\n      ]\r\n    },\r\n    componentProps: {\r\n      placeholder: '支持按用户名、用户编号模糊查询',\r\n      filterable: true,\r\n      clearable: true\r\n    }\r\n  }\r\n})\r\n"], "mappings": "AAAA;AACA;AACA,OAAO,IAAMA,MAAM,GAAG,SAATA,MAAM,CAAIC,IAAI;EAAA,OAAM;IAC/BC,kBAAkB,EAAE;MAClBC,SAAS,EAAE,QAAQ;MACnBC,KAAK,EAAE,MAAM;MACbC,OAAO,EAAE,CAAC;MACVC,IAAI,EAAE,oBAAoB;MAC1BN,MAAM,EAAE;QACN;MAAA,CACD;MACDO,cAAc,EAAE;QACd;QACAC,WAAW,EAAE,KAAK;QAClBC,SAAS,EAAE;MACb,CAAC;MACDC,OAAO,EAAE;IACX,CAAC;IACDC,SAAS,EAAE;MACTR,SAAS,EAAE,OAAO;MAClBC,KAAK,EAAE,UAAU;MACjBC,OAAO,EAAE,CAAC;MACVC,IAAI,EAAE,WAAW;MACjBN,MAAM,EAAE;QACNY,KAAK,EAAE,CACL;UAAEC,GAAG,EAAE,CAAC;UAAEC,GAAG,EAAE,EAAE;UAAEC,OAAO,EAAE;QAAa,CAAC;MAE9C,CAAC;MACDR,cAAc,EAAE;QACdC,WAAW,EAAE,iBAAiB;QAC9BQ,UAAU,EAAE,IAAI;QAChBP,SAAS,EAAE;MACb;IACF;EACF,CAAC;AAAA,CAAC"}]}