{"remainingRequest": "D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\node_modules\\babel-loader\\lib\\index.js!D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\node_modules\\eslint-loader\\index.js??ref--13-0!D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\src\\store\\modules\\app.js", "dependencies": [{"path": "D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\src\\store\\modules\\app.js", "mtime": 1700812742843}, {"path": "D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\babel.config.js", "mtime": 1686019818359}, {"path": "D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\.babelrc", "mtime": 1705285078668}, {"path": "D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\node_modules\\eslint-loader\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["Cookies", "state", "sidebar", "opened", "get", "withoutAnimation", "device", "loading", "mutations", "TOGGLE_SIDEBAR", "set", "CLOSE_SIDEBAR", "TOGGLE_DEVICE", "CHANGE_LOADING", "flag", "actions", "toggleSideBar", "commit", "closeSideBar", "toggleDevice", "namespaced"], "sources": ["D:/1_Project/XYD_Project/dop-4.0/dop-4.1-qianduan-unify/dopUnify/src/store/modules/app.js"], "sourcesContent": ["import Cookies from 'js-cookie'\n\nconst state = {\n  sidebar: {\n    opened: Cookies.get('sidebarStatus')\n      ? !!+Cookies.get('sidebarStatus')\n      : true,\n    withoutAnimation: false\n  },\n  device: 'desktop',\n  loading: false // 跳转iframe页面的遮罩\n  // size: Cookies.get('size') || 'medium'\n}\n\nconst mutations = {\n  TOGGLE_SIDEBAR: (state) => {\n    state.sidebar.opened = !state.sidebar.opened\n    state.sidebar.withoutAnimation = false\n    if (state.sidebar.opened) {\n      Cookies.set('sidebarStatus', 1)\n    } else {\n      Cookies.set('sidebarStatus', 0)\n    }\n  },\n  CLOSE_SIDEBAR: (state, withoutAnimation) => {\n    Cookies.set('sidebarStatus', 0)\n    state.sidebar.opened = false\n    state.sidebar.withoutAnimation = withoutAnimation\n  },\n  TOGGLE_DEVICE: (state, device) => {\n    state.device = device\n    // },\n    // SET_SIZE: (state, size) => {\n    //   state.size = size\n    //   Cookies.set('size', size)\n  },\n  CHANGE_LOADING: (state, flag) => {\n    state.loading = flag\n  }\n}\n\nconst actions = {\n  toggleSideBar({ commit }) {\n    commit('TOGGLE_SIDEBAR')\n  },\n  closeSideBar({ commit }, { withoutAnimation }) {\n    commit('CLOSE_SIDEBAR', withoutAnimation)\n  },\n  toggleDevice({ commit }, device) {\n    commit('TOGGLE_DEVICE', device)\n    // },\n    // setSize({ commit }, size) {\n    //   commit('SET_SIZE', size)\n  }\n}\n\nexport default {\n  namespaced: true,\n  state,\n  mutations,\n  actions\n}\n"], "mappings": "AAAA,OAAOA,OAAO,MAAM,WAAW;AAE/B,IAAMC,KAAK,GAAG;EACZC,OAAO,EAAE;IACPC,MAAM,EAAEH,OAAO,CAACI,GAAG,CAAC,eAAe,CAAC,GAChC,CAAC,CAAC,CAACJ,OAAO,CAACI,GAAG,CAAC,eAAe,CAAC,GAC/B,IAAI;IACRC,gBAAgB,EAAE;EACpB,CAAC;EACDC,MAAM,EAAE,SAAS;EACjBC,OAAO,EAAE,KAAK,CAAC;EACf;AACF,CAAC;;AAED,IAAMC,SAAS,GAAG;EAChBC,cAAc,EAAE,wBAACR,KAAK,EAAK;IACzBA,KAAK,CAACC,OAAO,CAACC,MAAM,GAAG,CAACF,KAAK,CAACC,OAAO,CAACC,MAAM;IAC5CF,KAAK,CAACC,OAAO,CAACG,gBAAgB,GAAG,KAAK;IACtC,IAAIJ,KAAK,CAACC,OAAO,CAACC,MAAM,EAAE;MACxBH,OAAO,CAACU,GAAG,CAAC,eAAe,EAAE,CAAC,CAAC;IACjC,CAAC,MAAM;MACLV,OAAO,CAACU,GAAG,CAAC,eAAe,EAAE,CAAC,CAAC;IACjC;EACF,CAAC;EACDC,aAAa,EAAE,uBAACV,KAAK,EAAEI,gBAAgB,EAAK;IAC1CL,OAAO,CAACU,GAAG,CAAC,eAAe,EAAE,CAAC,CAAC;IAC/BT,KAAK,CAACC,OAAO,CAACC,MAAM,GAAG,KAAK;IAC5BF,KAAK,CAACC,OAAO,CAACG,gBAAgB,GAAGA,gBAAgB;EACnD,CAAC;EACDO,aAAa,EAAE,uBAACX,KAAK,EAAEK,MAAM,EAAK;IAChCL,KAAK,CAACK,MAAM,GAAGA,MAAM;IACrB;IACA;IACA;IACA;EACF,CAAC;;EACDO,cAAc,EAAE,wBAACZ,KAAK,EAAEa,IAAI,EAAK;IAC/Bb,KAAK,CAACM,OAAO,GAAGO,IAAI;EACtB;AACF,CAAC;AAED,IAAMC,OAAO,GAAG;EACdC,aAAa,+BAAa;IAAA,IAAVC,MAAM,QAANA,MAAM;IACpBA,MAAM,CAAC,gBAAgB,CAAC;EAC1B,CAAC;EACDC,YAAY,sCAAmC;IAAA,IAAhCD,MAAM,SAANA,MAAM;IAAA,IAAMZ,gBAAgB,SAAhBA,gBAAgB;IACzCY,MAAM,CAAC,eAAe,EAAEZ,gBAAgB,CAAC;EAC3C,CAAC;EACDc,YAAY,+BAAab,MAAM,EAAE;IAAA,IAAlBW,MAAM,SAANA,MAAM;IACnBA,MAAM,CAAC,eAAe,EAAEX,MAAM,CAAC;IAC/B;IACA;IACA;EACF;AACF,CAAC;;AAED,eAAe;EACbc,UAAU,EAAE,IAAI;EAChBnB,KAAK,EAALA,KAAK;EACLO,SAAS,EAATA,SAAS;EACTO,OAAO,EAAPA;AACF,CAAC"}]}