{"remainingRequest": "D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\node_modules\\thread-loader\\dist\\cjs.js!D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\node_modules\\babel-loader\\lib\\index.js!D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\src\\views\\system\\user\\component\\table\\index.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\src\\views\\system\\user\\component\\table\\index.vue", "mtime": 1687679873352}, {"path": "D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\babel.config.js", "mtime": 1686019818359}, {"path": "D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\node_modules\\thread-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1655715099000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, null]}