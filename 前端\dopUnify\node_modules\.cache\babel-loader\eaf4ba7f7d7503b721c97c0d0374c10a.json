{"remainingRequest": "D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\node_modules\\babel-loader\\lib\\index.js!D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\src\\layout\\components\\Sidebar\\index.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\src\\layout\\components\\Sidebar\\index.vue", "mtime": 1686019810310}, {"path": "D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\babel.config.js", "mtime": 1686019818359}, {"path": "D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1655715099000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAgFA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;EACAA;IAAAC;IAAAC;EAAA;EACAC;IACA;MACA;MACAC;MACAC;QACAC,iBACAF;QACAG;MACA;MACAC;MACAC;MACAC;IACA;EACA;EACAC,0CACAC;IACAC;MACA;MACA;QAAAC;MACA;MACA;QACA;MACA;MACA;IACA;IACAC;MACA;IACA;IACAC;MACA;IACA;IACAC;MACA;IACA;EAAA,EACA;EACAC;IAAA;IACA;MAAA,2CACA;QAAA;MAAA;QAAA;UAAA;UACA;UACA;QACA;MAAA;QAAA;MAAA;QAAA;MAAA;MACA;MACA;IACA;IACA;IACA;MACA;IACA;MACA;IACA;IACA;EACA;EACA;EACAC;IACA;EACA;EACAC;IACAC;MACA;IACA;EACA;AACA", "names": ["components", "SidebarItem", "Logo", "data", "menuDir", "backImg", "backgroundImage", "backgroundSize", "showMoreM", "collapseT", "loading", "computed", "mapGetters", "activeMenu", "path", "showLogo", "variables", "isCollapse", "mounted", "<PERSON><PERSON><PERSON><PERSON>", "methods", "showMoreMClick"], "sourceRoot": "src/layout/components/Sidebar", "sources": ["index.vue"], "sourcesContent": ["<!--左侧标题、导航区域-->\n<template>\n  <div :class=\"{ 'has-logo': showLogo }\" :style=\"backImg\">\n    <logo v-if=\"showLogo\" :collapse=\"isCollapse\" />\n    <el-scrollbar\n      v-if=\"menuDir !== 'top'\"\n      wrap-class=\"scrollbar-wrapper\"\n      class=\"el-scrollbar-top\"\n    >\n      <!--滚动条组件 左侧菜单栏-->\n      <el-menu\n        :v-loading=\"true\"\n        :default-active=\"activeMenu\"\n        :collapse=\"isCollapse\"\n        :class=\"collapseT ? 'aaa' : 'el-menu-demo'\"\n        :text-color=\"variables.menuText\"\n        :unique-opened=\"false\"\n        :active-text-color=\"theme\"\n        :collapse-transition=\"collapseT\"\n        mode=\"vertical\"\n      >\n        <sidebar-item\n          v-for=\"route in permission_routes\"\n          ref=\"sidebarRef\"\n          :key=\"route.path\"\n          :item=\"route\"\n          :base-path=\"route.path\"\n        />\n      </el-menu>\n    </el-scrollbar>\n    <div v-else class=\"horizontal_box\">\n      <!-- 顶部菜单栏 -->\n      <el-menu\n        :default-active=\"activeMenu\"\n        :collapse=\"isCollapse\"\n        class=\"el-menu-demo\"\n        :text-color=\"variables.menuText\"\n        :unique-opened=\"false\"\n        :active-text-color=\"theme\"\n        :collapse-transition=\"collapseT\"\n        mode=\"horizontal\"\n      >\n        <sidebar-item\n          v-for=\"route in permission_routes\"\n          ref=\"sidebarRef\"\n          :key=\"route.path\"\n          :item=\"route\"\n          :base-path=\"route.path\"\n        />\n      </el-menu>\n      <div class=\"morebox\">\n        <div class=\"morebutton\">\n          <i class=\"el-icon-more\" @click=\"showMoreMClick\" />\n        </div>\n        <div v-show=\"showMoreM\" class=\"menu-hor-box\">\n          <el-menu\n            :default-active=\"activeMenu\"\n            :collapse=\"isCollapse\"\n            class=\"el-menu-demo\"\n            :text-color=\"variables.menuText\"\n            :unique-opened=\"false\"\n            :active-text-color=\"theme\"\n            :collapse-transition=\"collapseT\"\n            mode=\"horizontal\"\n          >\n            <sidebar-item\n              v-for=\"route in permission_routes\"\n              ref=\"sidebarRef\"\n              :key=\"route.path\"\n              :item=\"route\"\n              :base-path=\"route.path\"\n            />\n          </el-menu>\n        </div>\n      </div>\n    </div>\n  </div>\n</template>\n\n<script>\nimport { mapGetters } from 'vuex'\nimport Logo from './Logo'\nimport SidebarItem from './SidebarItem'\nimport variables from '@/assets/scss/common/variable/variables.scss'\nimport bgc from '@/assets/img/newVersion/bg.png'\nimport bgc_top from '@/assets/img/newVersion/bg_top.png'\nimport { menuDir } from '@/settings'\n\nexport default {\n  components: { SidebarItem, Logo },\n  data() {\n    return {\n      // menuFlag: true\n      menuDir,\n      backImg: {\n        backgroundImage:\n          menuDir === 'top' ? 'url(' + bgc_top + ')' : 'url(' + bgc + ')',\n        backgroundSize: '100% 100%'\n      },\n      showMoreM: false,\n      collapseT: false,\n      loading: ''\n    }\n  },\n  computed: {\n    ...mapGetters(['permission_routes', 'sidebar', 'theme']),\n    activeMenu() {\n      const route = this.$route\n      const { meta, path } = route\n      // 如果设置了路径，侧边栏将突出显示您设置的路径\n      if (meta.activeMenu) {\n        return meta.activeMenu\n      }\n      return path\n    },\n    showLogo() {\n      return this.$store.state.settings.sidebarLogo\n    },\n    variables() {\n      return variables\n    },\n    isCollapse() {\n      return !this.sidebar.opened\n    }\n  },\n  mounted() {\n    this.$bus.$on('updateTime', (data) => {\n      for (const index of this.$refs.sidebarRef.keys()) {\n        this.$refs.sidebarRef[index].menuCount = {}\n        this.$refs.sidebarRef[index].menuExist = []\n      }\n      this.$store.commit('flowPath/GET_MENU_COUNT', {})\n      this.$store.commit('flowPath/GET_MENU_EXIST', [])\n    })\n    // ie浏览器适配菜单收缩 begin\n    if (!!window.ActiveXObject || 'ActiveXObject' in window) {\n      this.collapseT = true\n    } else {\n      this.collapseT = false\n    }\n    // ie浏览器适配菜单收缩 end\n  },\n  // 销毁对应自定义事件\n  beforeDestroy() {\n    this.$bus.$off('updateTime')\n  },\n  methods: {\n    showMoreMClick() {\n      this.showMoreM = !this.showMoreM\n    }\n  }\n}\n</script>\n<style lang=\"scss\" scoped>\n@import '~@/assets/scss/common/index.scss';\n.aaa {\n  transition: width 0.05s;\n  -webkit-transition: width 0.05s;\n  -moz-transition: width 0.05s;\n  -webkit-transition: width 0.05s;\n  -o-transition: width 0.5s;\n}\n\n.sidebar-logo-container.collapse {\n  display: block;\n}\n.horizontal_box {\n  position: absolute;\n  top: 0;\n  left: 20rem;\n  right: 26rem;\n  height: 5rem;\n  ::v-deep .el-menu {\n    background: none;\n    .sun-svg-icon {\n      margin-right: 0.6rem;\n    }\n    .el-submenu__icon-arrow {\n      right: 4px;\n      margin-top: -5px;\n    }\n    .el-submenu__title:hover {\n      background: none;\n      span {\n        color: $primary;\n      }\n    }\n  }\n  .morebox {\n    position: fixed;\n    top: 1.6rem;\n    right: 14rem;\n    width: 20rem;\n    .morebutton {\n      width: 100%;\n      height: 3rem;\n      padding-left: 5rem;\n\n      i {\n        cursor: pointer;\n        color: #999;\n        font-size: 1.8rem;\n      }\n    }\n    .menu-hor-box {\n      height: 90vh;\n      padding: 1rem 0;\n      position: absolute;\n      top: 4rem;\n      left: 0;\n      overflow-y: auto;\n      background: #fff;\n      border-radius: 1rem;\n      box-shadow: 0 2px 4px rgba(0, 0, 0, 0.12), 0 0 6px rgba(0, 0, 0, 0.04);\n      .el-menu--horizontal {\n        .is-horizontal {\n          width: 100%;\n        }\n\n        ::v-deep .el-submenu__title {\n          height: 36px;\n          line-height: 36px;\n          span {\n            margin-left: 1rem;\n          }\n        }\n        ::v-deep .el-menu-item {\n          height: 36px;\n          line-height: 36px;\n          span {\n            margin-left: 1rem;\n          }\n        }\n      }\n    }\n  }\n}\n</style>\n<style lang=\"scss\">\n.is-horizontal {\n  .el-menu-item,\n  .el-submenu__title {\n    height: 5rem;\n    line-height: 5rem;\n  }\n}\n</style>\n"]}]}