package com.sunyard.etl.custom.handler;

import java.io.BufferedReader;
import java.io.File;
import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.InputStreamReader;
import java.io.OutputStreamWriter;
import java.io.PrintWriter;
import java.text.SimpleDateFormat;

import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import com.sunyard.etl.system.common.Constants;
import com.sunyard.etl.system.dao.DataDateDAO;
import com.sunyard.etl.system.dao.impl.DataDateDAOImpl;
import com.sunyard.etl.tms.dao.DEPDAnalyzeDao;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.enums.JobStartEnum;
import com.xxl.job.core.handler.IJobHandler;
import com.xxl.job.core.handler.annotation.JobHandler;
import com.xxl.job.core.log.XxlJobLogger;

@JobHandler(value = "FileFormat_CORD0050", name = "CORD0050文件格式化")
@Service
public class FileFormat_CORD0050 extends IJobHandler {

	private static final long serialVersionUID = 1L;

	private static DataDateDAO dateDao = new DataDateDAOImpl();
	private DEPDAnalyzeDao DEPDAnalyzeDao = new DEPDAnalyzeDao("tableName") ;

	@Override
	public ReturnT<String> execute(String jobId, String... arg1) throws Exception {
		XxlJobLogger.log("开始CORD0050文件格式化...");
		String jobDate = dateDao.getDataDate();
		if (null != arg1[0]) {
			String dirPath = arg1[0].toString().replace("@", jobDate);
			File dir = new File(dirPath);
			if (!dir.isDirectory()) {
				XxlJobLogger.log("INFO: 资源不足，目录不存在：" + dir, jobId + "");
				return new ReturnT<String>(ReturnT.FAIL_CODE, JobStartEnum.TASK_STATE_NO_RESOURCE.getCode(),
						"文件目录" + dir.getPath() + "不存在");
			}
			File preFile = new File(dir, "CORD0050_"+jobDate+".txt");
			File file = new File(dir, "CORD0050_"+jobDate+"_proc.txt");
			if (file.exists()) {
				XxlJobLogger.log("INFO: 文件已存在，先删除后再转换：" + file.getPath(), jobId + "");
				file.delete();
			}
			PrintWriter pw;
			pw = new PrintWriter(new OutputStreamWriter(new FileOutputStream(file), "GBK"));
			if (preFile.length()==0) {
				pw.print("|||");
				pw.flush();
				pw.close();
				XxlJobLogger.log("INFO: 源文件不存在，生成转换文件：" + preFile.getPath() + " >> " + file.getPath(), jobId + "");
				return ReturnT.SUCCESS;
			}
			BufferedReader br = new BufferedReader(new InputStreamReader(new FileInputStream(preFile), "GBK"));
			
			String line = null;
			String siteNo = "";
			String siteName = "";
			String occurDate = "";
			String tellerNo = "";
			String tellerName = "";
			try {
				while ((line = br.readLine()) != null) {
					if(line.contains("1                 ") && line.contains("-CORD0050")){
						siteNo = line.substring(line.indexOf("( ")+"( ".length(),line.indexOf("-CORD0050"));
						continue;
					} 
					if(line.contains("机构 :") && line.contains("日期 :") && line.contains("页码 :")){
						siteName = line.substring(line.indexOf("机构 :  ")+"机构 :  ".length(),line.indexOf("日期 : ")).trim();
						occurDate = line.substring(line.indexOf("日期 : ")+"日期 : ".length(),line.indexOf("日期 : ")+"日期 : ".length()+10).trim();
						occurDate = new SimpleDateFormat("yyyyMMdd").format(new SimpleDateFormat("yyyy/MM/dd").parse(occurDate));
						continue;
					}
					if(line.contains("柜员编号 : ") && line.contains("柜员姓名 : ")){
						tellerNo = line.substring(line.indexOf("柜员编号 : ")+"柜员编号 : ".length(),line.indexOf("柜员姓名 :")).trim();
						tellerName = line.substring(line.indexOf("柜员姓名 : ")+"柜员姓名 : ".length()).trim();
						continue;
					}else if (line.endsWith("柜员姓名 :")) {
						tellerNo = line.substring(line.indexOf("柜员编号 : ")+"柜员编号 : ".length(),line.indexOf("柜员姓名 :")).trim();
						if (StringUtils.isNotBlank(tellerNo)) {//如果柜员不为空，并且姓名不存在，则去数据库查询对应中文姓名
							tellerName = DEPDAnalyzeDao.getNameByTellerNo(tellerNo) ;
							XxlJobLogger.log("INFO: 根据tellerNo：" + tellerNo+"找到对应的名字为："+tellerName, jobId + "");
						}
						if (StringUtils.isBlank(tellerName)) {
							tellerName = " ";
						}
						continue;
					}
					if("".equals(line.trim())||(line.contains("后台流水号") && line.contains("交易代码") && line.contains("交易金额 ( 转出 )"))||line.contains("====================")||"柜员金融交易流水".equals(line.trim())||line.contains("当页笔数 :")||line.contains("合计笔数 :")){
						continue;
					}
					
					if (StringUtils.isNotBlank(tellerNo) && "9933050".equals(tellerNo.trim())) {//源文件CORD0050文件，把柜员号为9933050的流水过滤掉 20221124hwj
						continue;
					}
					
					if(line.length()<115){
						line = String.format("%-115s", line);
					}
					String newLine = siteNo+""+siteName+"|"+occurDate+"|"+tellerNo+"|"+tellerName+"|"//在此处需要再拼接。然后再表结构上增加字段。
							+line.substring(2,14).trim()+"|"	//后台流水号
							+line.substring(14,24).trim()+"|"	//交易代码
							+line.substring(24,34).trim()+"|"	//货币 ( 转出 )
							+line.substring(34,56).trim()+"|"	//账号 ( 转出 )
							+line.substring(56,81).trim().replace(",", "")+"|"	//交易金额 ( 转出 )
							+line.substring(81,90).trim()+"|"	//货币 ( 转入 ) 
							+line.substring(90,115).trim()+"|" //账号 ( 转入 )
							+line.substring(115).trim().replace(",", "")+"|";	//交易金额 ( 转入 )
					pw.print(newLine+System.getProperty("line.separator"));
				}
			} catch (IOException e) {
				e.printStackTrace();
			}finally{
				pw.flush();
				pw.close();
			}
			XxlJobLogger.log("INFO: 文件转换成功：" + file.getPath(), jobId + "");
			return ReturnT.SUCCESS;
		}
		return ReturnT.FAIL;
	}

	public static void main(String[] args) {
		FileFormat_CORD0050 ff = new FileFormat_CORD0050();
		try {
			ff.execute("9", "D:\\data\\@");
		} catch (Exception e) {
			// TODO Auto-generated catch block
			e.printStackTrace();
		}
	}
}
