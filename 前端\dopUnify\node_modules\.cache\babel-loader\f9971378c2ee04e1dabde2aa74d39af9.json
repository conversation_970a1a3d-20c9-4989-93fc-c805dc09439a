{"remainingRequest": "E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-q<PERSON><PERSON>n\\数字运营平台-统一门户工程\\dopUnify\\node_modules\\thread-loader\\dist\\cjs.js!E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan\\数字运营平台-统一门户工程\\dopUnify\\node_modules\\babel-loader\\lib\\index.js!E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan\\数字运营平台-统一门户工程\\dopUnify\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan\\数字运营平台-统一门户工程\\dopUnify\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-q<PERSON>duan\\数字运营平台-统一门户工程\\dopUnify\\src\\views\\system\\user\\component\\table\\index.vue?vue&type=script&lang=js&", "dependencies": [{"path": "E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-<PERSON><PERSON><PERSON>n\\数字运营平台-统一门户工程\\dopUnify\\src\\views\\system\\user\\component\\table\\index.vue", "mtime": 1711070571723}, {"path": "E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-<PERSON><PERSON><PERSON>n\\数字运营平台-统一门户工程\\dopUnify\\babel.config.js", "mtime": 1686019818359}, {"path": "E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-<PERSON><PERSON><PERSON>n\\数字运营平台-统一门户工程\\dopUnify\\.babelrc", "mtime": 1705285078668}, {"path": "E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-<PERSON><PERSON><PERSON>n\\数字运营平台-统一门户工程\\dopUnify\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-<PERSON><PERSON><PERSON>n\\数字运营平台-统一门户工程\\dopUnify\\node_modules\\thread-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-<PERSON><PERSON><PERSON>n\\数字运营平台-统一门户工程\\dopUnify\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-<PERSON><PERSON><PERSON>n\\数字运营平台-统一门户工程\\dopUnify\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-<PERSON><PERSON><PERSON>n\\数字运营平台-统一门户工程\\dopUnify\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1655715099000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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**************************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"}, null]}