{"remainingRequest": "D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\node_modules\\thread-loader\\dist\\cjs.js!D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\node_modules\\babel-loader\\lib\\index.js!D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\node_modules\\qiankun\\es\\loader.js", "dependencies": [{"path": "D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\node_modules\\qiankun\\es\\loader.js", "mtime": 1667130453000}, {"path": "D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\babel.config.js", "mtime": 1686019818359}, {"path": "D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\node_modules\\thread-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, null]}