{"remainingRequest": "E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\node_modules\\babel-loader\\lib\\index.js!E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\src\\views\\system\\externalManage\\post\\component\\table\\index.vue?vue&type=script&lang=js&", "dependencies": [{"path": "E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\src\\views\\system\\externalManage\\post\\component\\table\\index.vue", "mtime": 1703583638555}, {"path": "E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\babel.config.js", "mtime": 1686019818359}, {"path": "E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\.babelrc", "mtime": 1705285078668}, {"path": "E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1655715099000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAgDA,SACAA,kBACAC,eACAC,wBACA;AACA;AACA;;AAEA;;AAEA;AACA;EAAAC;EAAAC;EAAAC;EAAAC;EAAAC;AACA;AACA;AACA;EACAC;EACAC;IACA;AACA;AACA;IACAC;MACA;MACAC;QACA;UACAC;QACA;MACA;MACA;IACA;IACA;AACA;AACA;IACAC;MACA;MACAF;QACA;UACAC;QACA;MACA;MACA;IACA;EACA;EACAE;EACAC;IACAC;MACAC;MACAC;QACA;MACA;IACA;IACAC;MACAF;MACAC;QACA;MACA;IACA;IACAE;MACAH;MACAC;QACA;MACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;EACA;EACAG;IACA;MACAC;QACAC;QACAC;QACAC;QACAC;QAAA;QACAC;QAAA;QACAC;UACAP;UAAA;UACAQ;UACAC;QACA;;QACAC;QAAA;QACAC;UACAC;UACAC;UAAA;UACAC;QACA;MACA;;MACAC;QACAC;UACAC;QACA;QACAC;UACAD;QACA;QACAE;UACAF;QACA;MACA;MACAG;QACAC;QACAC;QACAf;UACA;UACAgB;UACAC;QACA;;QACAC;UACAC;UACAC;UACAhC;YACAiC;YACAC;YACAC;UACA;QACA;MACA;MACAC;IACA;EACA;;EACAC;IACA;MACAC;QACA;QACA;QAAA,2CACA;UAAA;QAAA;UAAA;YAAA;YACA;cACA;gBACA;kBAAA,4CACAC;oBAAA;kBAAA;oBAAA;sBAAA;sBACA;sBACAC;sBACAA;sBACAC;oBACA;kBAAA;oBAAA;kBAAA;oBAAA;kBAAA;kBACA;kBACA;gBACA;kBACAxD;kBACA;kBACA;gBACA;cACA;gBACAA;gBACA;gBACA;cACA;YACA;UACA;QAAA;UAAA;QAAA;UAAA;QAAA;MACA;MACAyD;IACA;EACA;EACAC;IACAhD;EACA;EACAiD;IACAC;EACA;EACAC;IACA;EACA;EACAC;IAAA;IACA;IACA;IACA;MACA;MACA;IACA;EACA;EACAC;IACA;IACAC;MACA;MACA;QACAlC;UACA;QACA;MACA;;MACA;IACA;IACA;AACA;IACAmC;MAAA;QAAAC;MACAC;IACA;IACA;AACA;IACAC;MACA;IACA;IACA;AACA;IACAC;MAAA;MACA;MACA;QACAC;QACAC;QACAvB;QAAA;QACAC;QAAA;QACAuB;QACAvC;QACAC;MACA;MACAhC,WACAuE;QACA;UAAAC;UAAAC;UAAAC;QACA;QACA;QACA;QACA;MACA,GACAC;QACA;MACA;IACA;IACA;IACAC;MAAA;MACA;QACAR;QACAS;QACAR;MACA;MACApE;QACA;QACA;MACA;IACA;IACA;AACA;IACA6E;MACA;MACA;MACA;MACA;MACA;MACA;MACA;IACA;IAAA;IACA;AACA;IACAC;MAAA;MACA;MACA;QACAjF;QACA;MACA;MACA;QACAA;QACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;QACA;QACA,+CACA,IACA,2BACA;QACAkF;UACA;UACA;QACA;MACA;IACA;IACA;AACA;IACAC;MAAA;MACA;MACA;QACAnF;QACA;MACA;MACA;MACA;MACA;QACAoF,oCACAA,QACA;UACAC;QACA,GACA;MACA;;MACApF;QACA;QACA;UACA;YACAsE;YACAD;YACAgB;YACAC;YACAC;UACA;UACA;UACAlF,SACAmE;YACA1E;YACA;UACA,GACA8E;QACA;QACA;MACA;IACA;IACA;AACA;AACA;IACAY;MACA;IACA;IACA;AACA;IACAC;MACA;MACA;QACA;MACA;QACA;MACA;IACA;IACA;AACA;IACAC;MAAA;MACA;MACA;MACAC;MACAA;MACA;MACA;QACAtB;QACAC;QACAgB;QACAM;MACA;MACAzF,WACAqE;QACA;QACA;QACA1E;MACA,GACA8E;QACA;MACA;MACA;IACA;IACA;AACA;AACA;IACA;IAEA;IACA;AACA;IACAiB;MAAA;MACA7F;QACA;UACA;UACA2F;UACAA;UACA;UACA;YACAtB;YACAC;YACAgB;YACAM;UACA;UACAxF,YACAoE;YACA1E;YACA;YACA;UACA,GACA8E;YACA;UACA;UACA;QACA;MACA;IACA;IACA;IACAkB;MACA;MACA;MACA;MACAC;QACA;UACAC;QACA;QACAC;MACA;MACA;MACA;MACA;MACA;MACA;MACAC;QACA;UACAF;QACA;QACA9E;MACA;MACA;IACA;IACA;AACA;AACA;IACAiF;MACA;IACA;EACA;AACA", "names": ["commonMsgSuccess", "commonMsgWarn", "commonMsgConfirm", "query", "queryUser", "add", "modify", "del", "name", "filters", "externalSystemTpye", "that", "valueS", "externalPostNo", "mixins", "props", "defaultForm", "type", "default", "systemArray", "postNoArr", "data", "table", "columns", "ref", "loading", "selection", "indexNumber", "componentProps", "height", "formRow", "currentRow", "pageList", "totalNum", "currentPage", "pageSize", "btnDatas", "btnAdd", "show", "btnDelete", "btnModify", "dialog", "visible", "oprate", "title", "width", "form", "labelWidth", "config", "post_no", "external_system_no", "role_no", "externalUser", "watch", "handler", "item", "role", "roleOption", "deep", "beforeCreate", "<PERSON><PERSON><PERSON><PERSON>", "clearTimeout", "created", "mounted", "methods", "handleSelectionChange", "rowClassName", "rowIndex", "row", "getList", "queryList", "parameterList", "oper_type", "init", "then", "returnList", "allRow", "pageNum", "catch", "externalUserFlowDetailInit", "organ_no", "handleAdd", "handleModify", "timout1", "handleDelete", "dels", "external_post_id", "operation_value", "post_external_old", "data_type", "changeVisible", "dialogSumbit", "dialogAddSubmit", "formData1", "user_no", "dialogEditSubmit", "getExternalData", "arrayState", "label", "SystemArray", "postNo", "showLoading"], "sourceRoot": "src/views/system/externalManage/post/component/table", "sources": ["index.vue"], "sourcesContent": ["<!--\r\n* 关联系统运营岗位配置: 表格\r\n-->\r\n<template>\r\n  <div class=\"sun-content\">\r\n    <sun-table\r\n      :table-config=\"table\"\r\n      @selection-change=\"handleSelectionChange\"\r\n      @pagination=\"getList\"\r\n    >\r\n      <template slot=\"tableColumn\">\r\n        <el-table-column\r\n          v-for=\"item in table.columns\"\r\n          :key=\"item.id\"\r\n          :prop=\"item.name\"\r\n          :label=\"item.label\"\r\n          :width=\"item.width\"\r\n        >\r\n          <div slot-scope=\"{ row }\">\r\n            <span v-if=\"item.name === 'post_no'\">{{\r\n              row[item.name] | externalPostNo\r\n            }}</span>\r\n            <span v-else-if=\"item.name === 'external_system_no'\">{{\r\n              row[item.name] | externalSystemTpye\r\n            }}</span>\r\n            <span v-else class=\"textOverflow\" :title=\"row[item.name]\">{{\r\n              row[item.name]\r\n            }}</span>\r\n          </div>\r\n        </el-table-column>\r\n      </template>\r\n      <template slot=\"customButton\">\r\n        <sun-button\r\n          :btn-datas=\"btnDatas\"\r\n          @handleAdd=\"handleAdd\"\r\n          @handleModify=\"handleModify\"\r\n          @handleDelete=\"handleDelete\"\r\n        />\r\n      </template>\r\n    </sun-table>\r\n    <sun-form-dialog\r\n      :dialog-config=\"dialog\"\r\n      @dialogClose=\"changeVisible\"\r\n      @dialogSubmit=\"dialogSumbit\"\r\n    /><!--新增、修改弹出框-->\r\n  </div>\r\n</template>\r\n<script>\r\nimport {\r\n  commonMsgSuccess,\r\n  commonMsgWarn,\r\n  commonMsgConfirm\r\n} from '@/utils/message.js' // 提示信息\r\nimport { dictionaryGet } from '@/utils/dictionary.js' // 字典常量\r\nimport ResizeMixin from '@/utils/ResizeHandler' // 整体页面是否根据总高配置\r\n\r\nimport { config, configTable } from './info' // 表头、表单配置\r\n\r\nimport { system } from '@/api'\r\nconst { query, queryUser, add, modify, del } = system.SysExterPost\r\nlet that\r\nlet timout1\r\nexport default {\r\n  name: 'TableList',\r\n  filters: {\r\n    /**\r\n     * 自定义外表数据源\r\n     * @param {String} string 弹出框显示隐藏配置*/\r\n    externalSystemTpye(string) {\r\n      let valueS = ''\r\n      that.systemArray.map(function(item) {\r\n        if (item.value === string) {\r\n          valueS = item.value + '-' + item.label\r\n        }\r\n      })\r\n      return valueS\r\n    },\r\n    /**\r\n     * 自定义外表数据源\r\n     * @param {String} string 弹出框显示隐藏配置*/\r\n    externalPostNo(string) {\r\n      let valueS = ''\r\n      that.postNoArr.map(function(item) {\r\n        if (item.value === string) {\r\n          valueS = item.value + '-' + item.label\r\n        }\r\n      })\r\n      return valueS\r\n    }\r\n  },\r\n  mixins: [ResizeMixin],\r\n  props: {\r\n    defaultForm: {\r\n      type: Object,\r\n      default: function() {\r\n        return {}\r\n      }\r\n    },\r\n    systemArray: {\r\n      type: Array,\r\n      default: function() {\r\n        return []\r\n      }\r\n    },\r\n    postNoArr: {\r\n      type: Array,\r\n      default: function() {\r\n        return []\r\n      }\r\n    }\r\n    // rolelist: {\r\n    //   type: Array,\r\n    //   default: function() {\r\n    //     return []\r\n    //   }\r\n    // }\r\n  },\r\n  data() {\r\n    return {\r\n      table: {\r\n        columns: configTable(),\r\n        ref: 'tableRef',\r\n        loading: false,\r\n        selection: true, // 复选\r\n        indexNumber: true, // 序号\r\n        componentProps: {\r\n          data: [], // 表格数据\r\n          height: '100px',\r\n          formRow: 0 // 表单行数\r\n        },\r\n        currentRow: [], // 选中行\r\n        pageList: {\r\n          totalNum: 0,\r\n          currentPage: 1, // 当前页\r\n          pageSize: this.$store.getters.pageSize // 当前页显示条数\r\n        }\r\n      },\r\n      btnDatas: {\r\n        btnAdd: {\r\n          show: this.$attrs['btn-all'].btnAdd\r\n        },\r\n        btnDelete: {\r\n          show: this.$attrs['btn-all'].btnDelete\r\n        },\r\n        btnModify: {\r\n          show: this.$attrs['btn-all'].btnModify\r\n        }\r\n      },\r\n      dialog: {\r\n        visible: false,\r\n        oprate: 'add',\r\n        componentProps: {\r\n          // 弹出框配置属性\r\n          title: '新增',\r\n          width: '50rem' // 当前弹出框宽度\r\n        },\r\n        form: {\r\n          labelWidth: '12rem',\r\n          config: config(this),\r\n          defaultForm: {\r\n            post_no: '',\r\n            external_system_no: '',\r\n            role_no: ''\r\n          }\r\n        }\r\n      },\r\n      externalUser: [] // 关联用户号\r\n    }\r\n  },\r\n  watch: {\r\n    'dialog.form.defaultForm.external_system_no': {\r\n      handler(val) {\r\n        // 角色类型映射\r\n        const roleOption = []\r\n        for (const item of this.externalUser) {\r\n          if (item.value === val) {\r\n            if (item.external_roles) {\r\n              if (item.external_roles.length > 0) {\r\n                for (const roles of item.external_roles) {\r\n                  const role = {}\r\n                  role['value'] = roles.value + '-' + roles.name\r\n                  role['label'] = roles.value + '-' + roles.name\r\n                  roleOption.push(role)\r\n                }\r\n                this.dialog.form.config.role_no.options = roleOption\r\n                this.dialog.form.defaultForm.role_no = ''\r\n              } else {\r\n                commonMsgWarn('系统无角色数据', this)\r\n                this.dialog.form.config.role_no.options = []\r\n                this.dialog.form.defaultForm.role_no = ''\r\n              }\r\n            } else {\r\n              commonMsgWarn('系统无角色数据', this)\r\n              this.dialog.form.config.role_no.options = []\r\n              this.dialog.form.defaultForm.role_no = ''\r\n            }\r\n          }\r\n        }\r\n      },\r\n      deep: true\r\n    }\r\n  },\r\n  beforeCreate() {\r\n    that = this\r\n  },\r\n  beforeDestroy() {\r\n    clearTimeout(timout1)\r\n  },\r\n  created() {\r\n    this.listLoading = this.loading\r\n  },\r\n  mounted() {\r\n    // 查询关联角色\r\n    this.externalUserFlowDetailInit()\r\n    this.$nextTick(() => {\r\n      this.$store.dispatch('common/setExternalData', 'EXTERNAL_SYSTEM_NO')\r\n      this.$store.dispatch('common/setExternalData', 'POST_NO')\r\n    })\r\n  },\r\n  methods: {\r\n    // 表格选择多行\r\n    handleSelectionChange(val) {\r\n      const currentRow = val\r\n      if (currentRow.length > 1) {\r\n        currentRow.sort(function(a, b) {\r\n          return a.index - b.index\r\n        }) // 选中行排序\r\n      }\r\n      this.table.currentRow = val\r\n    },\r\n    /**\r\n     * 行的 className 的回调方法，也可以使用字符串为所有行设置一个固定的 className*/\r\n    rowClassName({ row, rowIndex }) {\r\n      row.index = rowIndex // 将索引放置到row数据中\r\n    },\r\n    /**\r\n     *页码更新 */\r\n    getList(param) {\r\n      this.queryList(param.currentPage)\r\n    },\r\n    /**\r\n     * 按钮：查询*/\r\n    queryList(currentPage) {\r\n      this.showLoading()\r\n      const msg = {\r\n        parameterList: [{}],\r\n        oper_type: dictionaryGet('OPERATE_QUERY'),\r\n        post_no: this.defaultForm.post_no, // 运营岗位\r\n        external_system_no: this.defaultForm.external_system_no, // 系统编号\r\n        init: false,\r\n        currentPage: currentPage || this.table.pageList.currentPage,\r\n        pageSize: this.table.pageList.pageSize\r\n      }\r\n      query(msg)\r\n        .then((response) => {\r\n          const { returnList, allRow, pageNum } = response.retMap\r\n          this.table.componentProps.data = returnList\r\n          this.table.pageList.totalNum = allRow\r\n          this.table.pageList.currentPage = pageNum\r\n          this.showLoading()\r\n        })\r\n        .catch(() => {\r\n          this.showLoading()\r\n        })\r\n    },\r\n    // 关联角色号初始化\r\n    externalUserFlowDetailInit() {\r\n      const msg = {\r\n        parameterList: [{}],\r\n        organ_no: this.$store.getters.organNo,\r\n        oper_type: 'externalUserFlowDetailInit'\r\n      }\r\n      queryUser(msg).then((response) => {\r\n        const { result } = response.retMap\r\n        this.externalUser = result\r\n      })\r\n    },\r\n    /**\r\n     * btn - 新增*/\r\n    handleAdd() {\r\n      // 初始化表格标题与类型\r\n      this.dialog.title = '新增'\r\n      this.dialog.oprate = 'add'\r\n      // 获取外部字典，给弹窗 系统号 赋值\r\n      this.getExternalData()\r\n      // 打开弹窗\r\n      this.changeVisible(true)\r\n    }, // handleAdd\r\n    /**\r\n     * btn - 编辑*/\r\n    handleModify() {\r\n      const rows = this.table.currentRow.length\r\n      if (rows === 0) {\r\n        commonMsgWarn('请选择要修改的行', this)\r\n        return\r\n      }\r\n      if (rows >= 2) {\r\n        commonMsgWarn('不支持多行修改，请重新选择', this)\r\n        return\r\n      }\r\n      this.dialog.oprate = 'edit'\r\n      this.dialog.componentProps.title = '编辑'\r\n      // 获取外部字典，给弹窗 系统号 赋值\r\n      this.getExternalData()\r\n      // 打开弹窗\r\n      this.changeVisible(true)\r\n      this.$nextTick(() => {\r\n        // 弹出框加载完成后赋值\r\n        this.dialog.form.defaultForm = Object.assign(\r\n          {},\r\n          this.table.currentRow[0]\r\n        )\r\n        timout1 = setTimeout(() => {\r\n          const role_no = this.table.currentRow[0].role_no\r\n          this.dialog.form.defaultForm.role_no = role_no\r\n        }, 100)\r\n      })\r\n    },\r\n    /**\r\n     * btn - 删除*/\r\n    handleDelete() {\r\n      const rows = this.table.currentRow\r\n      if (rows.length === 0) {\r\n        commonMsgWarn('请选择要删除的行', this)\r\n        return\r\n      }\r\n      // 多条数据删除\r\n      let dels = []\r\n      for (let i = 0; i < rows.length; i++) {\r\n        dels = [\r\n          ...dels,\r\n          {\r\n            external_post_id: rows[i].external_post_id // 选中行的角色id\r\n          }\r\n        ]\r\n      }\r\n      commonMsgConfirm('是否确认删除当前选中信息？', this, (param) => {\r\n        this.showLoading(true)\r\n        if (param) {\r\n          const msg = {\r\n            oper_type: dictionaryGet('OPERATE_DELETE'),\r\n            parameterList: [],\r\n            operation_value: dels,\r\n            post_external_old: rows,\r\n            data_type: 0\r\n          }\r\n          // del\r\n          del(msg)\r\n            .then((response) => {\r\n              commonMsgSuccess(response.retMsg, this)\r\n              this.queryList(1)\r\n            })\r\n            .catch(() => {})\r\n        }\r\n        this.showLoading(false)\r\n      })\r\n    },\r\n    /**\r\n     * 弹出框 - 关闭\r\n     * @param {Boolean} param 弹出框显示隐藏配置*/\r\n    changeVisible(param) {\r\n      this.dialog.visible = param\r\n    },\r\n    /**\r\n     * 弹出框 - 确认*/\r\n    dialogSumbit() {\r\n      const param = this.dialog.oprate\r\n      if (param === 'add') {\r\n        this.dialogAddSubmit()\r\n      } else {\r\n        this.dialogEditSubmit()\r\n      }\r\n    },\r\n    /**\r\n     * 弹出框 - 确认 - 新增*/\r\n    dialogAddSubmit() {\r\n      const formData1 = Object.assign({}, this.dialog.form.defaultForm)\r\n      // 新增时，为空\r\n      formData1.post_external_old = {}\r\n      formData1.user_no = this.$store.getters.userNo\r\n      this.showLoading()\r\n      const param = {\r\n        parameterList: [formData1],\r\n        oper_type: dictionaryGet('OPERATE_ADD'),\r\n        post_external_old: {},\r\n        user_no: this.$store.getters.userNo\r\n      }\r\n      add(param)\r\n        .then((response) => {\r\n          this.queryList(1) // 重新查询\r\n          this.showLoading()\r\n          commonMsgSuccess(response.retMsg, this)\r\n        })\r\n        .catch(() => {\r\n          this.showLoading()\r\n        })\r\n      this.changeVisible(false)\r\n    },\r\n    /**\r\n     * 角色编号变化事件\r\n     * @param {String} param 角色编号*/\r\n    // roleNoChange(param) {\r\n\r\n    // },\r\n    /**\r\n     * 弹出框 - 确认 - 编辑*/\r\n    dialogEditSubmit() {\r\n      commonMsgConfirm('是否确认提交当前数据？', this, (param) => {\r\n        if (param) {\r\n          const formData1 = Object.assign({}, this.dialog.form.defaultForm)\r\n          formData1.post_external_old = this.table.currentRow[0]\r\n          formData1.user_no = this.$store.getters.userNo\r\n          this.showLoading()\r\n          const msg = {\r\n            parameterList: [formData1],\r\n            oper_type: dictionaryGet('OPERATE_MODIFY'),\r\n            post_external_old: this.table.currentRow[0],\r\n            user_no: this.$store.getters.userNo\r\n          }\r\n          modify(msg)\r\n            .then((response) => {\r\n              commonMsgSuccess(response.retMsg, this)\r\n              this.queryList(1)\r\n              this.showLoading()\r\n            })\r\n            .catch(() => {\r\n              this.showLoading()\r\n            })\r\n          this.changeVisible(false) // 弹出框关闭\r\n        }\r\n      })\r\n    },\r\n    // 获取外部字典\r\n    getExternalData() {\r\n      // 处理外部字典label值\r\n      const arrayState = this.$store.getters.externalData.EXTERNAL_SYSTEM_NO\r\n      const SystemArray = []\r\n      arrayState.map(function(item) {\r\n        const valueS = Object.assign({}, item, {\r\n          label: item.value + '-' + item.label\r\n        })\r\n        SystemArray.push(valueS)\r\n      })\r\n      // 获取外部字典\r\n      this.dialog.form.config.external_system_no.options = SystemArray\r\n      // 处理运营岗位\r\n      const postNo = this.$store.getters.externalData.POST_NO\r\n      const postNoArr = []\r\n      postNo.map(function(item) {\r\n        const valueS = Object.assign({}, item, {\r\n          label: item.value + '-' + item.label\r\n        })\r\n        postNoArr.push(valueS)\r\n      })\r\n      this.dialog.form.config.post_no.options = postNoArr\r\n    },\r\n    /**\r\n     * 加载中动画配置\r\n     * @param {Boolean}param 当前加载显示状态*/\r\n    showLoading(param) {\r\n      this.table.loading = param\r\n    }\r\n  }\r\n}\r\n</script>\r\n<style lang=\"scss\" scoped></style>\r\n"]}]}