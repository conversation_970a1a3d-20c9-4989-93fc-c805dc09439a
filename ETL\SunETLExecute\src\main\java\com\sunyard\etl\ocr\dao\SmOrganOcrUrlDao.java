package com.sunyard.etl.ocr.dao;

import javax.sql.rowset.CachedRowSet;

import com.sunyard.etl.ocr.common.OCRConstants;
import com.xxl.job.core.log.XxlJobLogger;
import org.sunyard.util.dbutil.DBHandler;

import java.sql.SQLException;

/**
 * <AUTHOR>
 */
public class SmOrganOcrUrlDao {
	//执行数据源
	private String dataSourceIdStr;

	public SmOrganOcrUrlDao(String dataSourceIdStr){
		this.dataSourceIdStr = dataSourceIdStr;
	}
	/**
	 * 根据机构号查询使用的识别服务地址
	 * @param parentOrgans
	 * @return
	 * @throws SQLException
	 */
	public String getOrganOcrUrl(String parentOrgans) throws SQLException {
		DBHandler dbHandler = new DBHandler(dataSourceIdStr);
		String ocrUrl = null;
		String sql = "select url from sm_organ_ocr_url_tb where organ_no in ("+parentOrgans+")";
		XxlJobLogger.log("执行sql：" + sql);
		CachedRowSet rs = dbHandler.queryRs(sql);
		while (rs.next()) {
			ocrUrl = rs.getString("URL");
		}
		return ocrUrl;
	}
}
