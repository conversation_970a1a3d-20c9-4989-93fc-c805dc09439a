{"remainingRequest": "E:\\华南区项目\\中国银行\\开发区\\科技公司运营平台\\前端\\dopUnify\\node_modules\\babel-loader\\lib\\index.js!E:\\华南区项目\\中国银行\\开发区\\科技公司运营平台\\前端\\dopUnify\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!E:\\华南区项目\\中国银行\\开发区\\科技公司运营平台\\前端\\dopUnify\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\华南区项目\\中国银行\\开发区\\科技公司运营平台\\前端\\dopUnify\\src\\views\\system\\config\\dictionary\\index.vue?vue&type=script&lang=js&", "dependencies": [{"path": "E:\\华南区项目\\中国银行\\开发区\\科技公司运营平台\\前端\\dopUnify\\src\\views\\system\\config\\dictionary\\index.vue", "mtime": 1721631954662}, {"path": "E:\\华南区项目\\中国银行\\开发区\\科技公司运营平台\\前端\\dopUnify\\babel.config.js", "mtime": 1721631954662}, {"path": "E:\\华南区项目\\中国银行\\开发区\\科技公司运营平台\\前端\\dopUnify\\.babelrc", "mtime": 1721631954662}, {"path": "E:\\华南区项目\\中国银行\\开发区\\科技公司运营平台\\前端\\dopUnify\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "E:\\华南区项目\\中国银行\\开发区\\科技公司运营平台\\前端\\dopUnify\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "E:\\华南区项目\\中国银行\\开发区\\科技公司运营平台\\前端\\dopUnify\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "E:\\华南区项目\\中国银行\\开发区\\科技公司运营平台\\前端\\dopUnify\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1655715099000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:aW1wb3J0IF9yZWdlbmVyYXRvclJ1bnRpbWUgZnJvbSAiRTovXHU1MzRFXHU1MzU3XHU1MzNBXHU5ODc5XHU3NkVFL1x1NEUyRFx1NTZGRFx1OTRGNlx1ODg0Qy9cdTVGMDBcdTUzRDFcdTUzM0EvXHU3OUQxXHU2MjgwXHU1MTZDXHU1M0Y4XHU4RkQwXHU4NDI1XHU1RTczXHU1M0YwL1x1NTI0RFx1N0FFRi9kb3BVbmlmeS9ub2RlX21vZHVsZXMvQGJhYmVsL3J1bnRpbWUvaGVscGVycy9lc20vcmVnZW5lcmF0b3JSdW50aW1lLmpzIjsKaW1wb3J0IF9hc3luY1RvR2VuZXJhdG9yIGZyb20gIkU6L1x1NTM0RVx1NTM1N1x1NTMzQVx1OTg3OVx1NzZFRS9cdTRFMkRcdTU2RkRcdTk0RjZcdTg4NEMvXHU1RjAwXHU1M0QxXHU1MzNBL1x1NzlEMVx1NjI4MFx1NTE2Q1x1NTNGOFx1OEZEMFx1ODQyNVx1NUU3M1x1NTNGMC9cdTUyNERcdTdBRUYvZG9wVW5pZnkvbm9kZV9tb2R1bGVzL0BiYWJlbC9ydW50aW1lL2hlbHBlcnMvZXNtL2FzeW5jVG9HZW5lcmF0b3IuanMiOwppbXBvcnQgX2RlZmluZVByb3BlcnR5IGZyb20gIkU6L1x1NTM0RVx1NTM1N1x1NTMzQVx1OTg3OVx1NzZFRS9cdTRFMkRcdTU2RkRcdTk0RjZcdTg4NEMvXHU1RjAwXHU1M0QxXHU1MzNBL1x1NzlEMVx1NjI4MFx1NTE2Q1x1NTNGOFx1OEZEMFx1ODQyNVx1NUU3M1x1NTNGMC9cdTUyNERcdTdBRUYvZG9wVW5pZnkvbm9kZV9tb2R1bGVzL0BiYWJlbC9ydW50aW1lL2hlbHBlcnMvZXNtL2RlZmluZVByb3BlcnR5LmpzIjsKaW1wb3J0IF9vYmplY3RTcHJlYWQgZnJvbSAiRTovXHU1MzRFXHU1MzU3XHU1MzNBXHU5ODc5XHU3NkVFL1x1NEUyRFx1NTZGRFx1OTRGNlx1ODg0Qy9cdTVGMDBcdTUzRDFcdTUzM0EvXHU3OUQxXHU2MjgwXHU1MTZDXHU1M0Y4XHU4RkQwXHU4NDI1XHU1RTczXHU1M0YwL1x1NTI0RFx1N0FFRi9kb3BVbmlmeS9ub2RlX21vZHVsZXMvQGJhYmVsL3J1bnRpbWUvaGVscGVycy9lc20vb2JqZWN0U3ByZWFkMi5qcyI7CmltcG9ydCBfdG9Db25zdW1hYmxlQXJyYXkgZnJvbSAiRTovXHU1MzRFXHU1MzU3XHU1MzNBXHU5ODc5XHU3NkVFL1x1NEUyRFx1NTZGRFx1OTRGNlx1ODg0Qy9cdTVGMDBcdTUzRDFcdTUzM0EvXHU3OUQxXHU2MjgwXHU1MTZDXHU1M0Y4XHU4RkQwXHU4NDI1XHU1RTczXHU1M0YwL1x1NTI0RFx1N0FFRi9kb3BVbmlmeS9ub2RlX21vZHVsZXMvQGJhYmVsL3J1bnRpbWUvaGVscGVycy9lc20vdG9Db25zdW1hYmxlQXJyYXkuanMiOwppbXBvcnQgImNvcmUtanMvbW9kdWxlcy9lcy5vYmplY3QudG8tc3RyaW5nLmpzIjsKaW1wb3J0ICJjb3JlLWpzL21vZHVsZXMvd2ViLmRvbS1jb2xsZWN0aW9ucy5mb3ItZWFjaC5qcyI7CmltcG9ydCAiY29yZS1qcy9tb2R1bGVzL2VzLmZ1bmN0aW9uLm5hbWUuanMiOwppbXBvcnQgImNvcmUtanMvbW9kdWxlcy9lcy5hcnJheS5maWx0ZXIuanMiOwppbXBvcnQgImNvcmUtanMvbW9kdWxlcy9lcy5hcnJheS5tYXAuanMiOwppbXBvcnQgImNvcmUtanMvbW9kdWxlcy9lcy5qc29uLnN0cmluZ2lmeS5qcyI7Ci8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCgppbXBvcnQgeyBjb21tb25Nc2dDb25maXJtLCBjb21tb25Nc2dTdWNjZXNzLCBjb21tb25Nc2dXYXJuIH0gZnJvbSAnQC91dGlscy9tZXNzYWdlJzsKaW1wb3J0IHsgY29tbW9uQmxhbmsgfSBmcm9tICdAL3V0aWxzL2NvbW1vbic7CmltcG9ydCB7IGNvbmZpZyB9IGZyb20gJy4vaW5mbyc7CmltcG9ydCB7IGRhdGVUaW1lRm9ybWF0IH0gZnJvbSAnQC9maWx0ZXJzJzsKaW1wb3J0IHsgcGVybWlzc2lvbnNCdG4gfSBmcm9tICdAL3V0aWxzL3Blcm1pc3Npb25zJzsgLy8g5p2D6ZmQ6YWN572uCmltcG9ydCB7IHN5c3RlbSwgQ29tbW9uIH0gZnJvbSAnQC9hcGknOwp2YXIgX3N5c3RlbSRTeXNEaWN0aW9uYXJ5ID0gc3lzdGVtLlN5c0RpY3Rpb25hcnksCiAgZ2V0VHJlZURhdGEgPSBfc3lzdGVtJFN5c0RpY3Rpb25hcnkuZ2V0VHJlZURhdGEsCiAgc2VhcmNoRGljdCA9IF9zeXN0ZW0kU3lzRGljdGlvbmFyeS5zZWFyY2hEaWN0LAogIGFkZERpY3QgPSBfc3lzdGVtJFN5c0RpY3Rpb25hcnkuYWRkRGljdCwKICBtb2RpZnlEaWN0ID0gX3N5c3RlbSRTeXNEaWN0aW9uYXJ5Lm1vZGlmeURpY3QsCiAgZGVsZXRlRGljdCA9IF9zeXN0ZW0kU3lzRGljdGlvbmFyeS5kZWxldGVEaWN0Owp2YXIgZmllbGRDb250cm9sbGVyID0gQ29tbW9uLmZpZWxkQ29udHJvbGxlcjsKdmFyIHRpbWVvdXQxOwp2YXIgdGltZW91dDI7CnZhciB0aW1lb3V0MzsKZXhwb3J0IGRlZmF1bHQgewogIG5hbWU6ICdEaWN0aW9uYXJ5JywKICBkYXRhOiBmdW5jdGlvbiBkYXRhKCkgewogICAgcmV0dXJuIHsKICAgICAgbm90U2F2ZTogdHJ1ZSwKICAgICAgLy8g5LiN5L+d5a2YICAg5L+d5a2Y5oyJ6ZKu5LiN5Ye6546wCiAgICAgIHNlYXJjaFZhbHVlOiAnJywKICAgICAgLy8g5pCc57Si5qGG55qE5YC8CiAgICAgIHNlYXJjaFJlczogZmFsc2UsCiAgICAgIC8vIOaQnOe0oue7k+aenOS4jeaYvuekugogICAgICBsZW5ndGg6IDEsCiAgICAgIC8vIOaQnOe0oue7k+aenOS4uuW9k+WJjeesrOWHoOS4qgogICAgICB0b3RhbE51bTogJycsCiAgICAgIC8vIOaQnOe0oue7k+aenOaAu+adoeaVsAogICAgICAvLyAgaXNGaXJzdDogdHJ1ZSwgLy/mmK/lkKbmmK/mkJzntKLnmoTnrKzkuIDmnaHmlbDmja4KICAgICAgaXNsYXN0OiBmYWxzZSwKICAgICAgLy8g5piv5ZCm5piv5pCc57Si55qE56ys5LiA5p2h5pWw5o2uCiAgICAgIGJhY2tEaXNhYmxlZDogZmFsc2UsCiAgICAgIC8vIOS4iuS4gOatpeaMiemSruS4jeemgeeUqAogICAgICBuZXh0RGlzYWJsZWQ6IGZhbHNlLAogICAgICAvLyDkuIvkuIDmraXmjInpkq7kuI3npoHnlKgKICAgICAgZmllTm86IDAsCiAgICAgIC8vIOesrOS4gOasoeaQnOe0ogogICAgICBzZWFyY2hmbGFnOiAnMScsCiAgICAgIC8vIOesrOS4gOasoeaQnOe0ogogICAgICBvcGVyX3R5cGU6ICcnLAogICAgICAvLyDmk43kvZznsbvlnosKICAgICAgZXhwYW5kZWRrZXlzOiBbJzAwMDAwMCddLAogICAgICAvLyDpu5jorqTlsZXlvIDpobkKICAgICAgY29uZmlnOiBjb25maWcodGhpcywgewogICAgICAgIGRpc2FibGVDb25maWc6IHsKICAgICAgICAgIHBhcmVudF9maWVsZDogdHJ1ZSwKICAgICAgICAgIGZpZWxkX25vOiB0cnVlLAogICAgICAgICAgZWxzZTogdHJ1ZQogICAgICAgIH0KICAgICAgfSksCiAgICAgIHByZURlZmF1bHRGb3JtOiB7fSwKICAgICAgLy8g6KGo5Y2V6buY6K6k5YC85aSH5Lu9CiAgICAgIHNlbGVjdGVkTm9kZToge30sCiAgICAgIC8vIOeCueWHu+mAieS4reeahOiKgueCuQogICAgICBzZWxlY3RlZE5vZGUyOiBudWxsLAogICAgICBkZWZhdWx0Rm9ybTogewogICAgICAgIGZpZWxkTm86ICcnLAogICAgICAgIGZpZWxkVHlwZTogJycsCiAgICAgICAgZmllbGREZXNjOiBudWxsLAogICAgICAgIGZpZWxkTW9kZTogJ3ZhcmNoYXInLAogICAgICAgIGZpZWxkSWQ6ICcnLAogICAgICAgIGZpZWxkU2l6ZTogMCwKICAgICAgICBwYXJlbnRGaWVsZDogJzAwMDAwMCcsCiAgICAgICAgbGFzdE1vZGlEYXRlOiAnJywKICAgICAgICBmaWVsZE5hbWU6ICcnLAogICAgICAgIGZpZWxkTGVuZ3RoOiAwCiAgICAgIH0sCiAgICAgIHByb3BzOiB7CiAgICAgICAgbGFiZWw6ICduYW1lJywKICAgICAgICBjaGlsZHJlbjogJ3pvbmVzJywKICAgICAgICBpc0xlYWY6ICdsZWFmJwogICAgICB9LAogICAgICBidG5EYXRhczoge30sCiAgICAgIGJ0bkRhdGFzQWxsOiB7CiAgICAgICAgLy8g6YWN572u5omA5pyJ5oyJ6ZKuCiAgICAgICAgYnRuUXVlcnk6IHsKICAgICAgICAgIHNob3c6IGZhbHNlCiAgICAgICAgfSwKICAgICAgICBidG5BZGQ6IHsKICAgICAgICAgIHNob3c6IHRydWUKICAgICAgICB9LAogICAgICAgIGJ0bk1vZGlmeTogewogICAgICAgICAgc2hvdzogdHJ1ZQogICAgICAgIH0sCiAgICAgICAgYnRuRGVsZXRlOiB7CiAgICAgICAgICBzaG93OiB0cnVlCiAgICAgICAgfSwKICAgICAgICBidG5TYXZlOiB7CiAgICAgICAgICBzaG93OiB0cnVlCiAgICAgICAgfSwKICAgICAgICBidG5DYW5jbGU6IHsKICAgICAgICAgIHNob3c6IHRydWUKICAgICAgICB9CiAgICAgIH0sCiAgICAgIG9sZFN5c3RlbU5vOiAnJywKICAgICAgLy8g5L+d5a2Y5LiK5LiA5Lu957O757uf5Y+3CiAgICAgIG5ld1N5c3RlbU5vOiAnJyAvLyDmlrDns7vnu5/lj7cKICAgIH07CiAgfSwKCiAgd2F0Y2g6IHsKICAgIHNlYXJjaFZhbHVlOiBmdW5jdGlvbiBzZWFyY2hWYWx1ZSh2YWwpIHsKICAgICAgaWYgKGNvbW1vbkJsYW5rKHZhbCkpIHsKICAgICAgICB0aGlzLnNlYXJjaFJlcyA9IGZhbHNlOwogICAgICB9CiAgICB9CiAgfSwKICBjcmVhdGVkOiBmdW5jdGlvbiBjcmVhdGVkKCkge30sCiAgYmVmb3JlRGVzdHJveTogZnVuY3Rpb24gYmVmb3JlRGVzdHJveSgpIHsKICAgIGNsZWFyVGltZW91dCh0aW1lb3V0MSk7CiAgICBjbGVhclRpbWVvdXQodGltZW91dDIpOwogICAgY2xlYXJUaW1lb3V0KHRpbWVvdXQzKTsKICB9LAogIG1vdW50ZWQ6IGZ1bmN0aW9uIG1vdW50ZWQoKSB7CiAgICB2YXIgX3RoaXMgPSB0aGlzOwogICAgdGhpcy4kbmV4dFRpY2soKS50aGVuKGZ1bmN0aW9uICgpIHsKICAgICAgX3RoaXMuYnRuUGVybWlzc2lvbnMoKTsKICAgIH0pOwogIH0sCiAgbWV0aG9kczogewogICAgLyoqCiAgICAgKiDmjInpkq7mnYPpmZDphY3nva4qLwogICAgYnRuUGVybWlzc2lvbnM6IGZ1bmN0aW9uIGJ0blBlcm1pc3Npb25zKCkgewogICAgICB0aGlzLmJ0bkRhdGFzQWxsID0gcGVybWlzc2lvbnNCdG4odGhpcy4kYXR0cnMuYnV0dG9uX2lkLCB0aGlzLmJ0bkRhdGFzQWxsKTsKICAgIH0sCiAgICAvKioKICAgICAqIOaMiemSruaYvuekuumFjee9rgogICAgICogQHBhcmFtIHtCb29sZWFufSBwYXJhbSB0cnVl5pi+56S65aKe5Yig5pS577yMZmFsc2XmmL7npLrkv53lrZjjgIHlj5bmtogKICAgICAqLwogICAgLyoqCiAgICAgKiDoioLngrnlsZXlvIAKICAgICAqIEBwYXJhbSB7T2JqZWN0fW5vZGUKICAgICAqIEBwYXJhbSB7T2JqZWN0fW5vZGUyCiAgICAgKi8KICAgIG5vZGVFeHBhbmQ6IGZ1bmN0aW9uIG5vZGVFeHBhbmQobm9kZSwgbm9kZTIpIHsKICAgICAgdGhpcy5zZWxlY3RlZE5vZGUgPSBub2RlOwogICAgICB0aGlzLnNlbGVjdGVkTm9kZTIgPSBub2RlMjsKICAgIH0sCiAgICAvKioKICAgICAqIOiKgueCueWNleWHuwogICAgICogQHBhcmFtIHtPYmplY3R9bm9kZQogICAgICogQHBhcmFtIHtPYmplY3R9bm9kZTIKICAgICAqLwogICAgbm9kZUNsaWNrOiBmdW5jdGlvbiBub2RlQ2xpY2sobm9kZSwgbm9kZTIpIHsKICAgICAgdGhpcy5zZWxlY3RlZE5vZGUgPSBub2RlOwogICAgICB0aGlzLnNlbGVjdGVkTm9kZTIgPSBub2RlMjsKICAgICAgdGhpcy5kZWZhdWx0Rm9ybSA9IHsKICAgICAgICBmaWVsZE5vOiBub2RlLmZpZWxkX25vLAogICAgICAgIGZpZWxkVHlwZTogbm9kZS5maWVsZF90eXBlLAogICAgICAgIGZpZWxkRGVzYzogbm9kZS5maWVsZF9kZXNjLAogICAgICAgIGZpZWxkTW9kZTogbm9kZS5maWVsZF9tb2RlLAogICAgICAgIGZpZWxkSWQ6IG5vZGUuZmllbGRfaWQsCiAgICAgICAgZmllbGRTaXplOiBub2RlLmZpZWxkX3NpemUsCiAgICAgICAgcGFyZW50RmllbGQ6IG5vZGUucGFyZW50X2ZpZWxkLAogICAgICAgIGxhc3RNb2RpRGF0ZTogZGF0ZVRpbWVGb3JtYXQobm9kZS5sYXN0X21vZGlfZGF0ZSksCiAgICAgICAgZmllbGROYW1lOiBub2RlLmZpZWxkX25hbWUsCiAgICAgICAgZmllbGRMZW5ndGg6IG5vZGUuZmllbGRfbGVuZ3RoLAogICAgICAgIHN5c3RlbU5vOiBub2RlLnN5c3RlbV9ubyAvLyDmlrDlop7lj4LmlbDvvJrns7vnu5/lj7cKICAgICAgfTsKICAgICAgLy8g5aaC5p6c6L+b6KGM5LqG5aKe5Yig5pS55pON5L2cICAgIOS/neWtmOaMiemSruaYvuekugogICAgICBpZiAodGhpcy5vcGVyX3R5cGUgIT09ICcnKSB7CiAgICAgICAgdGhpcy5ub3RTYXZlID0gdHJ1ZTsgLy8g5L+d5a2Y5oyJ5LiN5pi+56S6CiAgICAgICAgdGhpcy5jb25maWcgPSBjb25maWcodGhpcywgewogICAgICAgICAgZGlzYWJsZUNvbmZpZzogewogICAgICAgICAgICBwYXJlbnRfZmllbGQ6IHRydWUsCiAgICAgICAgICAgIGZpZWxkX25vOiB0cnVlLAogICAgICAgICAgICBlbHNlOiB0cnVlCiAgICAgICAgICB9CiAgICAgICAgfSk7CiAgICAgIH0KICAgIH0sCiAgICAvKioKICAgICAqIOWKoOi9veagkQogICAgICogQHBhcmFtIHtPYmplY3R9bm9kZQogICAgICogQHBhcmFtIHt9cmVzb2x2ZQogICAgICovCiAgICBsb2FkTm9kZTogZnVuY3Rpb24gbG9hZE5vZGUobm9kZSwgcmVzb2x2ZSkgewogICAgICBjb25zb2xlLmxvZyhub2RlKTsKICAgICAgaWYgKG5vZGUubGV2ZWwgPT09IDApIHsKICAgICAgICAvLyDojrflj5bmoJHliJ3lp4vmlbDmja4KICAgICAgICB2YXIgbXNnID0gewogICAgICAgICAgcGFyYW1ldGVyTGlzdDogW3sKICAgICAgICAgICAgcGFyZW50RmllbGQ6ICcwMDAwMDAnCiAgICAgICAgICB9XSwKICAgICAgICAgIHN5c3RlbU5vOiAnVU5JRlknCiAgICAgICAgfTsKICAgICAgICBnZXRUcmVlRGF0YShtc2cpLnRoZW4oZnVuY3Rpb24gKHJlcykgewogICAgICAgICAgdmFyIGxpc3QgPSByZXMucmV0TWFwLmxpc3Q7CiAgICAgICAgICAvLyBsaXN0LnNoaWZ0KCkKICAgICAgICAgIGxpc3QuZm9yRWFjaChmdW5jdGlvbiAoaXRlbSkgewogICAgICAgICAgICBpdGVtWyduYW1lJ10gPSBpdGVtLmZpZWxkX25hbWU7CiAgICAgICAgICAgIGl0ZW1bJ2ZpZWxkX2lkX2NvcHknXSA9IGl0ZW0uZmllbGRfaWQgKyBpdGVtLnN5c3RlbV9ubzsKICAgICAgICAgIH0pOwogICAgICAgICAgdmFyIHRyZWVMaXN0ID0gbGlzdC5maWx0ZXIoZnVuY3Rpb24gKGl0ZW0pIHsKICAgICAgICAgICAgcmV0dXJuIGl0ZW0uZmllbGRfdHlwZSA9PT0gJzAwJzsKICAgICAgICAgIH0pOwogICAgICAgICAgcmVzb2x2ZSh0cmVlTGlzdCk7CiAgICAgICAgfSk7CiAgICAgIH0KICAgICAgaWYgKG5vZGUubGV2ZWwgPT09IDEpIHsKICAgICAgICAvLyDojrflj5bmoJHliJ3lp4vmlbDmja4KICAgICAgICB2YXIgX21zZyA9IHsKICAgICAgICAgIHBhcmFtZXRlckxpc3Q6IFt7CiAgICAgICAgICAgIHBhcmVudEZpZWxkOiBub2RlLmRhdGEuZmllbGRfaWQKICAgICAgICAgIH1dLAogICAgICAgICAgb3Blcl90eXBlOiAnYXN5c2MnLAogICAgICAgICAgc3lzdGVtTm86IG5vZGUuZGF0YS5zeXN0ZW1fbm8KICAgICAgICB9OwogICAgICAgIGdldFRyZWVEYXRhKF9tc2cpLnRoZW4oZnVuY3Rpb24gKHJlcykgewogICAgICAgICAgdmFyIGxpc3QgPSByZXMucmV0TWFwLmxpc3Q7CiAgICAgICAgICAvLyBsaXN0LnNoaWZ0KCkKICAgICAgICAgIGxpc3QuZm9yRWFjaChmdW5jdGlvbiAoaXRlbSkgewogICAgICAgICAgICBpdGVtWyduYW1lJ10gPSBpdGVtLmZpZWxkX25hbWU7CiAgICAgICAgICAgIGl0ZW1bJ2ZpZWxkX2lkX2NvcHknXSA9IGl0ZW0uZmllbGRfaWQgKyBpdGVtLnN5c3RlbV9ubzsKICAgICAgICAgIH0pOwogICAgICAgICAgdmFyIHRyZWVMaXN0ID0gbGlzdC5maWx0ZXIoZnVuY3Rpb24gKGl0ZW0pIHsKICAgICAgICAgICAgcmV0dXJuIGl0ZW0uc3lzdGVtX25vID09PSBub2RlLmRhdGEuc3lzdGVtX25vICYmIGl0ZW0uZmllbGRfdHlwZSAhPT0gJzAwJzsKICAgICAgICAgIH0pOwogICAgICAgICAgcmV0dXJuIHJlc29sdmUodHJlZUxpc3QpOwogICAgICAgIH0pOwogICAgICB9CiAgICAgIGlmIChub2RlLmxldmVsID4gMSkgewogICAgICAgIHZhciBfbXNnMiA9IHsKICAgICAgICAgIHBhcmFtZXRlckxpc3Q6IFt7CiAgICAgICAgICAgIHBhcmVudEZpZWxkOiBub2RlLmRhdGEuZmllbGRfaWQKICAgICAgICAgIH1dLAogICAgICAgICAgb3Blcl90eXBlOiAnYXN5c2MnLAogICAgICAgICAgc3lzdGVtTm86IG5vZGUuZGF0YS5zeXN0ZW1fbm8KICAgICAgICB9OwogICAgICAgIGdldFRyZWVEYXRhKF9tc2cyKS50aGVuKGZ1bmN0aW9uIChyZXMpIHsKICAgICAgICAgIHZhciBsaXN0ID0gcmVzLnJldE1hcC5saXN0OwogICAgICAgICAgbGlzdC5mb3JFYWNoKGZ1bmN0aW9uIChpdGVtKSB7CiAgICAgICAgICAgIGlmIChpdGVtLmlzX3BhcmVudCA9PT0gJzAnKSB7CiAgICAgICAgICAgICAgaXRlbVsnbGVhZiddID0gdHJ1ZTsKICAgICAgICAgICAgfQogICAgICAgICAgICBpdGVtWyduYW1lJ10gPSBpdGVtLmZpZWxkX25hbWU7CiAgICAgICAgICAgIGl0ZW1bJ2ZpZWxkX2lkX2NvcHknXSA9IGl0ZW0uZmllbGRfaWQgKyBpdGVtLnN5c3RlbV9ubzsKICAgICAgICAgIH0pOwogICAgICAgICAgdmFyIHRyZWVMaXN0ID0gbGlzdC5maWx0ZXIoZnVuY3Rpb24gKGl0ZW0pIHsKICAgICAgICAgICAgcmV0dXJuIGl0ZW0uZmllbGRfdHlwZSAhPT0gJzAwJzsKICAgICAgICAgIH0pOwogICAgICAgICAgcmV0dXJuIHJlc29sdmUodHJlZUxpc3QpOwogICAgICAgIH0pOwogICAgICAgIHJldHVybjsKICAgICAgfQogICAgfSwKICAgIC8qKgogICAgICog5oyJ6ZKu77ya5pCc57SiCiAgICAgKi8KICAgIG9uU2VhcmNoOiBmdW5jdGlvbiBvblNlYXJjaCgpIHsKICAgICAgdmFyIF90aGlzMiA9IHRoaXM7CiAgICAgIGlmICh0aGlzLnNlYXJjaFZhbHVlID09PSAnJykgewogICAgICAgIHJldHVybjsKICAgICAgfQogICAgICAvLyDnrKzkuIDmrKHmkJzntKLlj4LmlbAKICAgICAgdmFyIG1zZyA9IHsKICAgICAgICBwYXJhbWV0ZXJMaXN0OiBbXSwKICAgICAgICBvcGVyX3R5cGU6ICdzZWFyY2hQYXJlbnRTb24nLAogICAgICAgIGZpZU5vOiAwLAogICAgICAgIC8vIDAKICAgICAgICBmaWVsZElkT3JOYW1lOiB0aGlzLnNlYXJjaFZhbHVlLAogICAgICAgIGZsYWc6ICcxJyAvLyAnMScKICAgICAgfTsKCiAgICAgIHNlYXJjaERpY3QobXNnKS50aGVuKGZ1bmN0aW9uIChyZXMpIHsKICAgICAgICBpZiAoIWNvbW1vbkJsYW5rKHJlcy5yZXRNYXApKSB7CiAgICAgICAgICB2YXIgX3RoaXMyJGV4cGFuZGVka2V5czsKICAgICAgICAgIHZhciBfcmVzJHJldE1hcCA9IHJlcy5yZXRNYXAsCiAgICAgICAgICAgIGZpZWxkX2lkID0gX3JlcyRyZXRNYXAuZmllbGRfaWQsCiAgICAgICAgICAgIGxpc3RQYXJlbnRJZCA9IF9yZXMkcmV0TWFwLmxpc3RQYXJlbnRJZCwKICAgICAgICAgICAgbGVuID0gX3JlcyRyZXRNYXAubGVuLAogICAgICAgICAgICBzeXN0ZW1ObyA9IF9yZXMkcmV0TWFwLnN5c3RlbU5vOwogICAgICAgICAgX3RoaXMyLnRvdGFsTnVtID0gbGVuOyAvLyDmgLvmnaHmlbDlpI3liLYKICAgICAgICAgIF90aGlzMi5sZW5ndGggPSAxOyAvLyDph43mlrDmn6Xor6Lml7bliJ3lp4vljJYg5b2T5YmN5Li656ysMeS4que7k+aenAogICAgICAgICAgX3RoaXMyLmZpZU5vID0gMDsgLy8g6YeN5paw5p+l6K+i5pe25Yid5aeL5YyW56ysMeS4que7k+aenOS4i+agh+S4ujAKICAgICAgICAgIC8vIOeCueWHu+aQnOe0oiAg5LiK5LiA5p2h44CB5LiL5LiA5p2h5Ye6546wCiAgICAgICAgICBfdGhpczIuc2VhcmNoUmVzID0gdHJ1ZTsKICAgICAgICAgIF90aGlzMi5vbGRTeXN0ZW1ObyA9IHN5c3RlbU5vOwogICAgICAgICAgaWYgKF90aGlzMi50b3RhbE51bSA9PT0gMSkgewogICAgICAgICAgICBfdGhpczIuYmFja0Rpc2FibGVkID0gdHJ1ZTsgLy8g5aaC5p6c5piv56ys5LiA5p2hICDkuIrkuIDmnaHmjInpkq7npoHnlKgKICAgICAgICAgICAgX3RoaXMyLm5leHREaXNhYmxlZCA9IHRydWU7IC8vIOmHjeaWsOafpeivouaXtuWIneWni+WMliDkuIvkuIDmraXnpoHnlKjmjInpkq4KICAgICAgICAgIH0gZWxzZSB7CiAgICAgICAgICAgIF90aGlzMi5iYWNrRGlzYWJsZWQgPSB0cnVlOyAvLyDlpoLmnpzmmK/nrKzkuIDmnaEgIOS4iuS4gOadoeaMiemSruemgeeUqAogICAgICAgICAgICBfdGhpczIubmV4dERpc2FibGVkID0gZmFsc2U7IC8vIOmHjeaWsOafpeivouaXtuWIneWni+WMliDkuIvkuIDmraXnpoHnlKjmjInpkq4KICAgICAgICAgIH0KICAgICAgICAgIC8vIOWxleW8gOmhuQogICAgICAgICAgLy8gY29uc3QgbGlzdFBhcmVudElkQ29weSA9IGxpc3RQYXJlbnRJZC5tYXAoKGl0ZW0pID0+IHsKICAgICAgICAgIC8vICAgaXRlbSA9IGl0ZW0gKyBzeXN0ZW1ObwogICAgICAgICAgLy8gICByZXR1cm4gaXRlbQogICAgICAgICAgLy8gfSkKICAgICAgICAgIChfdGhpczIkZXhwYW5kZWRrZXlzID0gX3RoaXMyLmV4cGFuZGVka2V5cykucHVzaC5hcHBseShfdGhpczIkZXhwYW5kZWRrZXlzLCBfdG9Db25zdW1hYmxlQXJyYXkobGlzdFBhcmVudElkKSk7CiAgICAgICAgICBfdGhpczIuZXhwYW5kZWRrZXlzID0gX3RoaXMyLmV4cGFuZGVka2V5cy5tYXAoZnVuY3Rpb24gKGl0ZW0pIHsKICAgICAgICAgICAgaXRlbSA9IGl0ZW0gKyBfdGhpczIub2xkU3lzdGVtTm87CiAgICAgICAgICAgIHJldHVybiBpdGVtOwogICAgICAgICAgfSk7CiAgICAgICAgICAvLyBjb25zb2xlLmxvZyg3Nzc3NywgJ+aQnOe0ouWxleW8gOmhuScsIHRoaXMuZXhwYW5kZWRrZXlzKQogICAgICAgICAgX3RoaXMyLnNldEN1cnJlbnROb2RlKGZpZWxkX2lkICsgc3lzdGVtTm8pOwogICAgICAgIH0gZWxzZSB7CiAgICAgICAgICBjb21tb25Nc2dXYXJuKCfmlbDmja7lrZflhbjooajkuK3mnKrljLnphY3liLDor6XmnaHmlbDmja4nLCBfdGhpczIpOwogICAgICAgICAgLy8g54K55Ye75pCc57SiICDkuIrkuIDmnaHjgIHkuIvkuIDmnaHpmpDol48KICAgICAgICAgIF90aGlzMi5zZWFyY2hSZXMgPSBmYWxzZTsKICAgICAgICB9CiAgICAgIH0pOwogICAgfSwKICAgIC8qKgogICAgICog5oyJ6ZKu77ya5LiK5LiA5p2hCiAgICAgKi8KICAgIGJhY2s6IGZ1bmN0aW9uIGJhY2soKSB7CiAgICAgIHZhciBfdGhpczMgPSB0aGlzOwogICAgICBpZiAodGhpcy5maWVObyA9PT0gMSkgewogICAgICAgIC8vIOesrOS4gOadoeaVsOaNriAg5LiK5LiA5q2l5oyJ6ZKu56aB55SoICAg56ys5LiA5qyh5p+l6K+idGhpcy5zZWFyY2hmbGFnPScxJwogICAgICAgIHRoaXMubGVuZ3RoID0gMTsKICAgICAgICB0aGlzLmZpZU5vID0gMDsKICAgICAgICB0aGlzLnNlYXJjaGZsYWcgPSAnMSc7IC8vIOeCueWHu+S4i+S4gOatpSAgIOmdnuesrOS4gOadoeafpeivouaVsOaNrgogICAgICAgIHRoaXMuYmFja0Rpc2FibGVkID0gdHJ1ZTsKICAgICAgICB0aGlzLm9uU2VhcmNoKCk7IC8vIOS4iuS4gOatpeWIsOesrOS4gOadoeaVsOaNruebuOW9k+S6juebtOaOpeiwg+eUqOafpeivouaWueazlQogICAgICB9IGVsc2UgewogICAgICAgIC8vIGZpZW5vIOS4i+aghy0xICDnrKzlh6DmnaHmlbDmja5sZW5ndGgtMSAgKOmdnuesrOS4gOadoeaVsOaNrikKICAgICAgICB0aGlzLm5leHREaXNhYmxlZCA9IGZhbHNlOwogICAgICAgIHRoaXMubGVuZ3RoID0gdGhpcy5sZW5ndGggLSAxOyAvLyDnrKzlh6DmnaHmlbDmja4tMQogICAgICAgIHRoaXMuZmllTm8gPSB0aGlzLmZpZU5vIC0gMTsgLy8g5pWw5o2u5LiL5qCHCiAgICAgICAgdmFyIG1zZyA9IHsKICAgICAgICAgIHBhcmFtZXRlckxpc3Q6IFtdLAogICAgICAgICAgb3Blcl90eXBlOiAnc2VhcmNoUGFyZW50U29uJywKICAgICAgICAgIGZpZU5vOiB0aGlzLmZpZU5vLAogICAgICAgICAgLy8gMSwyLDMsNC4uLi4uLgogICAgICAgICAgZmllbGRJZE9yTmFtZTogdGhpcy5zZWFyY2hWYWx1ZSwKICAgICAgICAgIGZsYWc6IHRoaXMuc2VhcmNoZmxhZyAvLyAnMicKICAgICAgICB9OwoKICAgICAgICBzZWFyY2hEaWN0KG1zZykudGhlbihmdW5jdGlvbiAocmVzKSB7CiAgICAgICAgICB2YXIgX3RoaXMzJGV4cGFuZGVka2V5czsKICAgICAgICAgIHZhciBfcmVzJHJldE1hcDIgPSByZXMucmV0TWFwLAogICAgICAgICAgICBmaWVsZF9pZCA9IF9yZXMkcmV0TWFwMi5maWVsZF9pZCwKICAgICAgICAgICAgbGlzdFBhcmVudElkID0gX3JlcyRyZXRNYXAyLmxpc3RQYXJlbnRJZCwKICAgICAgICAgICAgbGVuID0gX3JlcyRyZXRNYXAyLmxlbiwKICAgICAgICAgICAgc3lzdGVtTm8gPSBfcmVzJHJldE1hcDIuc3lzdGVtTm87CiAgICAgICAgICBfdGhpczMudG90YWxOdW0gPSBsZW47IC8vIOaAu+adoeaVsOWkjeWItgogICAgICAgICAgLy8g5bGV5byA6aG5CiAgICAgICAgICAoX3RoaXMzJGV4cGFuZGVka2V5cyA9IF90aGlzMy5leHBhbmRlZGtleXMpLnB1c2guYXBwbHkoX3RoaXMzJGV4cGFuZGVka2V5cywgX3RvQ29uc3VtYWJsZUFycmF5KGxpc3RQYXJlbnRJZCkpOwogICAgICAgICAgX3RoaXMzLmV4cGFuZGVka2V5cyA9IF90aGlzMy5leHBhbmRlZGtleXMubWFwKGZ1bmN0aW9uIChpdGVtKSB7CiAgICAgICAgICAgIGl0ZW0gPSBpdGVtICsgc3lzdGVtTm87CiAgICAgICAgICAgIHJldHVybiBpdGVtOwogICAgICAgICAgfSk7CiAgICAgICAgICAvLyBjb25zb2xlLmxvZygn5LiK5LiA5p2h5bGV5byA6aG5JywgdGhpcy5leHBhbmRlZGtleXMpCiAgICAgICAgICBfdGhpczMuc2V0Q3VycmVudE5vZGUoZmllbGRfaWQgKyBzeXN0ZW1Obyk7CiAgICAgICAgfSk7CiAgICAgIH0KICAgIH0sCiAgICAvKioKICAgICAqIOaMiemSru+8muS4i+S4gOadoQogICAgICovCiAgICBuZXh0OiBmdW5jdGlvbiBuZXh0KCkgewogICAgICB2YXIgX3RoaXM0ID0gdGhpczsKICAgICAgdGhpcy5iYWNrRGlzYWJsZWQgPSBmYWxzZTsgLy8g5LiK5LiA5p2h5oyJ6ZKu5Y+v55SoCiAgICAgIHRoaXMubGVuZ3RoID0gdGhpcy5sZW5ndGggKyAxOyAvLyDnrKzlh6DmnaHmlbDmja4rMQogICAgICAvLyB0aGlzLmlzRmlyc3QgPSBmYWxzZTsgLy/ngrnlh7vkuIvkuIDmraUgICDpnZ7nrKzkuIDmnaHmn6Xor6LmlbDmja4KICAgICAgdGhpcy5maWVObyA9IHRoaXMuZmllTm8gKyAxOyAvLyDmlbDmja7kuIvmoIcKICAgICAgaWYgKHRoaXMuZmllTm8gKyAxID09PSB0aGlzLnRvdGFsTnVtKSB7CiAgICAgICAgdGhpcy5uZXh0RGlzYWJsZWQgPSB0cnVlOyAvLyDlpoLmnpzmmK/mnIDlkI7kuIDmnaEgIOS4i+S4gOatpeaMiemSruemgeeUqAogICAgICB9CgogICAgICB0aGlzLnNlYXJjaGZsYWcgPSAnMic7CiAgICAgIHZhciBtc2cgPSB7CiAgICAgICAgcGFyYW1ldGVyTGlzdDogW10sCiAgICAgICAgb3Blcl90eXBlOiAnc2VhcmNoUGFyZW50U29uJywKICAgICAgICBmaWVObzogdGhpcy5maWVObywKICAgICAgICAvLyAxLDIsMyw0Li4uLi4uCiAgICAgICAgZmllbGRJZE9yTmFtZTogdGhpcy5zZWFyY2hWYWx1ZSwKICAgICAgICBmbGFnOiB0aGlzLnNlYXJjaGZsYWcgLy8gJzInCiAgICAgIH07CgogICAgICBzZWFyY2hEaWN0KG1zZykudGhlbihmdW5jdGlvbiAocmVzKSB7CiAgICAgICAgdmFyIF90aGlzNCRleHBhbmRlZGtleXM7CiAgICAgICAgdmFyIF9yZXMkcmV0TWFwMyA9IHJlcy5yZXRNYXAsCiAgICAgICAgICBmaWVsZF9pZCA9IF9yZXMkcmV0TWFwMy5maWVsZF9pZCwKICAgICAgICAgIGxpc3RQYXJlbnRJZCA9IF9yZXMkcmV0TWFwMy5saXN0UGFyZW50SWQsCiAgICAgICAgICBsZW4gPSBfcmVzJHJldE1hcDMubGVuLAogICAgICAgICAgc3lzdGVtTm8gPSBfcmVzJHJldE1hcDMuc3lzdGVtTm87CiAgICAgICAgX3RoaXM0LnRvdGFsTnVtID0gbGVuOyAvLyDmgLvmnaHmlbDlpI3liLYKCiAgICAgICAgaWYgKHN5c3RlbU5vICE9PSBfdGhpczQub2xkU3lzdGVtTm8pIHsKICAgICAgICAgIF90aGlzNC5leHBhbmRlZGtleXMucHVzaCgnMDAwMDAwJyArIHN5c3RlbU5vKTsKICAgICAgICAgIF90aGlzNC5vbGRTeXN0ZW1ObyA9IHN5c3RlbU5vOwogICAgICAgIH0KCiAgICAgICAgLy8g5bGV5byA6aG5CiAgICAgICAgdmFyIGxpc3RQYXJlbnRJZENvcHkgPSBsaXN0UGFyZW50SWQubWFwKGZ1bmN0aW9uIChpdGVtKSB7CiAgICAgICAgICBpdGVtID0gaXRlbSArIHN5c3RlbU5vOwogICAgICAgICAgcmV0dXJuIGl0ZW07CiAgICAgICAgfSk7CiAgICAgICAgKF90aGlzNCRleHBhbmRlZGtleXMgPSBfdGhpczQuZXhwYW5kZWRrZXlzKS5wdXNoLmFwcGx5KF90aGlzNCRleHBhbmRlZGtleXMsIF90b0NvbnN1bWFibGVBcnJheShsaXN0UGFyZW50SWRDb3B5KSk7IC8vIOayoeacieaJp+ihjOWxleW8gOmhuQogICAgICAgIC8vIGNvbnNvbGUubG9nKCc2NjY2NuS4i+S4gOadoeWxleW8gOmhuScsIHRoaXMuZXhwYW5kZWRrZXlzKQogICAgICAgIF90aGlzNC5zZXRDdXJyZW50Tm9kZShmaWVsZF9pZCArIHN5c3RlbU5vKTsKICAgICAgfSk7CiAgICB9LAogICAgLyoqCiAgICAgKiDlrprkvY3lt6bkvqfmoJHlvZPliY3oioLngrnlkozpq5jkuq7oioLngrkKICAgICAqIEBwYXJhbSB7U3RyaW5nfSBmaWVsZF9pZF9jb3B5IOW9k+WJjeiKgueCuUlECiAgICAgKi8KICAgIHNldEN1cnJlbnROb2RlOiBmdW5jdGlvbiBzZXRDdXJyZW50Tm9kZShmaWVsZF9pZF9jb3B5KSB7CiAgICAgIHZhciBfdGhpczUgPSB0aGlzOwogICAgICAvLyBjb25zb2xlLmxvZygnNTU1NTU6JywgZmllbGRfaWRfY29weSkKICAgICAgLy8g5aaC5p6c6K+l57O757uf5Y+355qE5pWw5o2u5rKh5pyJ6L+b6KGM5oeS5Yqg6L2977yM5YiZ6L+b6KGM5oeS5Yqg6L295ZCO5YaN6L+b6KGM54K55Ye75a6a5L2N562J5pON5L2cCiAgICAgIHRpbWVvdXQxID0gc2V0VGltZW91dChmdW5jdGlvbiAoKSB7CiAgICAgICAgX3RoaXM1LiRyZWZzLnRyZWUuc2V0Q3VycmVudEtleShmaWVsZF9pZF9jb3B5KTsKICAgICAgICAvLyB0aGlzLiRuZXh0VGljaygoKSA9PiB7CiAgICAgICAgdmFyIG5vZGUgPSBfdGhpczUuJHJlZnMudHJlZS5nZXROb2RlKGZpZWxkX2lkX2NvcHkpOyAvLyBub2Rl5Li656m6LS0tLS0tLS0tLS3otbDkuI3ov5vliKTmlq0gKOW8guatpeivt+axginjgIHmoJHmh5LliqDovb0gIOW8guatpeivt+axggogICAgICAgIC8vIH0pCiAgICAgICAgLy8gY29uc29sZS5sb2coJ25vZGUtLScsIG5vZGUpCiAgICAgICAgaWYgKCFjb21tb25CbGFuayhub2RlKSkgewogICAgICAgICAgX3RoaXM1Lm5vZGVDbGljayhub2RlLmRhdGEpOwogICAgICAgICAgX3RoaXM1LiRuZXh0VGljayhmdW5jdGlvbiAoKSB7CiAgICAgICAgICAgIHZhciBkb20gPSBkb2N1bWVudC5xdWVyeVNlbGVjdG9yKCcuaXMtY3VycmVudCcpOyAvLyDlvZPliY3pq5jkuq7oioLngrnnm5LlrZAKICAgICAgICAgICAgdmFyIGRvbTIgPSBkb2N1bWVudC5xdWVyeVNlbGVjdG9yKCcuZGljdGlvbmFyeS1sZWZ0Jyk7IC8vIHRyZWXlpKfnm5LlrZAKICAgICAgICAgICAgZG9tMi5zY3JvbGxUb3AgPSBkb20ub2Zmc2V0VG9wIC0gMjAwOyAvLyDnu5l0cmVl55uS5a2Q6K6+572uc2Nyb2xUb3DlrprkvY3liLDlvZPliY3pq5jkuq7oioLngrkKICAgICAgICAgIH0pOwogICAgICAgIH0KICAgICAgfSwgMTAwMCk7CiAgICB9LAogICAgLyoqCiAgICAgKiDmjInpkq7vvJrmlrDlop4KICAgICAqLwogICAgaGFuZGxlQWRkOiBmdW5jdGlvbiBoYW5kbGVBZGQoKSB7CiAgICAgIGlmIChjb21tb25CbGFuayh0aGlzLnNlbGVjdGVkTm9kZSkpIHsKICAgICAgICB0aGlzLiRtZXNzYWdlKHsKICAgICAgICAgIG1lc3NhZ2U6ICfor7fpgInmi6nniLboioLngrknLAogICAgICAgICAgdHlwZTogJ3dhcm5pbmcnCiAgICAgICAgfSk7CiAgICAgICAgcmV0dXJuOwogICAgICB9CiAgICAgIHRoaXMubm90U2F2ZSA9IGZhbHNlOwogICAgICB0aGlzLm9wZXJfdHlwZSA9ICdpbnNlcnQnOwogICAgICB0aGlzLnByZURlZmF1bHRGb3JtID0gdGhpcy5kZWZhdWx0Rm9ybTsKICAgICAgdGhpcy5kZWZhdWx0Rm9ybSA9IHsKICAgICAgICBmaWVsZE5vOiAnJywKICAgICAgICBmaWVsZFR5cGU6ICcnLAogICAgICAgIGZpZWxkRGVzYzogJycsCiAgICAgICAgZmllbGRNb2RlOiAnJywKICAgICAgICBmaWVsZElkOiB0aGlzLnNlbGVjdGVkTm9kZS5maWVsZF9pZCAhPT0gJzAwMDAwMCcgPyAn5ZCO5Y+w55Sf5oiQJyA6ICcnLAogICAgICAgIGZpZWxkU2l6ZTogJycsCiAgICAgICAgcGFyZW50RmllbGQ6IHRoaXMuc2VsZWN0ZWROb2RlLmZpZWxkX2lkICE9PSAnMDAwMDAwJyA/IHRoaXMuc2VsZWN0ZWROb2RlLmZpZWxkX2lkIDogJzAwMDAwMCcsCiAgICAgICAgbGFzdE1vZGlEYXRlOiAn5b2T5YmN5pWw5o2u5bqT5pe26Ze0JywKICAgICAgICBmaWVsZE5hbWU6ICcnLAogICAgICAgIGZpZWxkTGVuZ3RoOiAnJywKICAgICAgICBzeXN0ZW1ObzogdGhpcy5zZWxlY3RlZE5vZGUuc3lzdGVtX25vIC8vIOaWsOWinuWPguaVsO+8muezu+e7n+WPtwogICAgICB9OwoKICAgICAgdGhpcy5jb25maWcgPSBjb25maWcodGhpcywgewogICAgICAgIGRpc2FibGVDb25maWc6IHsKICAgICAgICAgIHBhcmVudF9maWVsZDogdHJ1ZSwKICAgICAgICAgIGZpZWxkX25vOiB0aGlzLnNlbGVjdGVkTm9kZS5maWVsZF9pZCAhPT0gJzAwMDAwMCcsCiAgICAgICAgICBlbHNlOiBmYWxzZQogICAgICAgIH0KICAgICAgfSk7CiAgICB9LAogICAgLyoqCiAgICAgKiDmjInpkq7vvJrkv67mlLkKICAgICAqLwogICAgaGFuZGxlTW9kaWZ5OiBmdW5jdGlvbiBoYW5kbGVNb2RpZnkoKSB7CiAgICAgIHZhciBub2RlID0gdGhpcy4kcmVmcy50cmVlLmdldEN1cnJlbnROb2RlKCk7CiAgICAgIGlmIChub2RlLmZpZWxkX3R5cGUgPT09ICcwMCcpIHsKICAgICAgICB0aGlzLiRtZXNzYWdlKHsKICAgICAgICAgIG1lc3NhZ2U6ICfml6Dms5Xkv67mlLnmoLnoioLngrnvvIzor7fph43mlrDpgInmi6knLAogICAgICAgICAgdHlwZTogJ3dhcm5pbmcnCiAgICAgICAgfSk7CiAgICAgICAgcmV0dXJuOwogICAgICB9CiAgICAgIGlmICghbm9kZSkgewogICAgICAgIC8vIGNvbW1vbk1zZ1dhcm4oJ+ivt+mAieaLqeWIoOmZpOeahOiKgueCuScsIHRoYXQpCiAgICAgICAgdGhpcy4kbWVzc2FnZSh7CiAgICAgICAgICBtZXNzYWdlOiAn6K+36YCJ5oup5L+u5pS555qE6IqC54K5JywKICAgICAgICAgIHR5cGU6ICd3YXJuaW5nJwogICAgICAgIH0pOwogICAgICAgIHJldHVybjsKICAgICAgfQogICAgICB0aGlzLm5vdFNhdmUgPSBmYWxzZTsKICAgICAgdGhpcy5vcGVyX3R5cGUgPSAnbW9kaWZ5JzsKICAgICAgdGhpcy5kZWZhdWx0Rm9ybSA9IF9vYmplY3RTcHJlYWQoX29iamVjdFNwcmVhZCh7fSwgdGhpcy5kZWZhdWx0Rm9ybSksIHt9LCB7CiAgICAgICAgc3lzdGVtTm86IHRoaXMuc2VsZWN0ZWROb2RlLnN5c3RlbV9ubyAvLyDkv67mlLnlj4LmlbDvvJrns7vnu5/lj7cg6Ziy5q2i57O757uf5Y+35pWw5o2u5Lii5aSxCiAgICAgIH0pOwoKICAgICAgdGhpcy5wcmVEZWZhdWx0Rm9ybSA9IHRoaXMuZGVmYXVsdEZvcm07CiAgICAgIHRoaXMuY29uZmlnID0gY29uZmlnKHRoaXMsIHsKICAgICAgICBkaXNhYmxlQ29uZmlnOiB7CiAgICAgICAgICBwYXJlbnRfZmllbGQ6IHRydWUsCiAgICAgICAgICBmaWVsZF9ubzogdHJ1ZSwKICAgICAgICAgIGVsc2U6IGZhbHNlCiAgICAgICAgfQogICAgICB9KTsKICAgIH0sCiAgICAvKioKICAgICAqIOaMiemSru+8muWIoOmZpAogICAgICovCiAgICBoYW5kbGVEZWxldGU6IGZ1bmN0aW9uIGhhbmRsZURlbGV0ZSgpIHsKICAgICAgdmFyIHRoYXQgPSB0aGlzOwogICAgICB2YXIgbm9kZSA9IHRoaXMuJHJlZnMudHJlZS5nZXRDdXJyZW50Tm9kZSgpOwogICAgICBpZiAoIW5vZGUpIHsKICAgICAgICAvLyBjb21tb25Nc2dXYXJuKCfor7fpgInmi6nliKDpmaTnmoToioLngrknLCB0aGF0KQogICAgICAgIHRoaXMuJG1lc3NhZ2UoewogICAgICAgICAgbWVzc2FnZTogJ+ivt+mAieaLqeWIoOmZpOeahOiKgueCuScsCiAgICAgICAgICB0eXBlOiAnaW5mbycKICAgICAgICB9KTsKICAgICAgICByZXR1cm47CiAgICAgIH0KICAgICAgdmFyIGZpZWxkSWQgPSBub2RlLmZpZWxkX2lkOwogICAgICB2YXIgcGFyZW50RmllbGQgPSBub2RlLnBhcmVudF9maWVsZDsKICAgICAgdmFyIHN5c3RlbU5vID0gbm9kZS5zeXN0ZW1fbm87CiAgICAgIGZ1bmN0aW9uIGRlbChmbGFnKSB7CiAgICAgICAgaWYgKCFmbGFnKSB7CiAgICAgICAgICByZXR1cm47CiAgICAgICAgfQogICAgICAgIHZhciBtc2cgPSB7CiAgICAgICAgICBwYXJhbWV0ZXJMaXN0OiBbewogICAgICAgICAgICBmaWVsZElkOiBmaWVsZElkLAogICAgICAgICAgICBwYXJlbnRGaWVsZDogcGFyZW50RmllbGQsCiAgICAgICAgICAgIHN5c3RlbU5vOiBzeXN0ZW1ObwogICAgICAgICAgfV0sCiAgICAgICAgICBvcGVyX3R5cGU6ICdkZWxldGUnCiAgICAgICAgfTsKICAgICAgICBkZWxldGVEaWN0KG1zZykudGhlbihmdW5jdGlvbiAocmVzKSB7CiAgICAgICAgICBpZiAocmVzLnJldENvZGUgPT09ICcyMDAnKSB7CiAgICAgICAgICAgIGNvbW1vbk1zZ1N1Y2Nlc3MocmVzLnJldE1zZywgdGhhdCk7CiAgICAgICAgICAgIHRoYXQuJHJlZnMudHJlZS5yZW1vdmUobm9kZSk7CiAgICAgICAgICAgIC8vIOa4heepuumAieS4reiKgueCuQogICAgICAgICAgICB0aGF0LnNlbGVjdGVkTm9kZSA9ICcnOwogICAgICAgICAgICB2YXIgbXNnMSA9IHsKICAgICAgICAgICAgICBmaWVsZFRpbWU6ICcnLAogICAgICAgICAgICAgIG9wZXJUeXBlOiAnMScsCiAgICAgICAgICAgICAgLy8g6Zeo5oi35pON5L2c5qCH6K+GCiAgICAgICAgICAgICAgc3lzdGVtTm86IHN5c3RlbU5vCiAgICAgICAgICAgIH07CiAgICAgICAgICAgIGZpZWxkQ29udHJvbGxlcihtc2cxKS50aGVuKGZ1bmN0aW9uIChyZXMpIHsKICAgICAgICAgICAgICBpZiAoc3lzdGVtTm8gPT09ICdVTklGWScpIHsKICAgICAgICAgICAgICAgIGxvY2FsU3RvcmFnZS5zZXRJdGVtKHN5c3RlbU5vLCBKU09OLnN0cmluZ2lmeShyZXMucmV0TWFwW3N5c3RlbU5vXSkpOwogICAgICAgICAgICAgICAgbG9jYWxTdG9yYWdlLnNldEl0ZW0oJ1VOSUZZZGljdGlvbmFyeScsIEpTT04uc3RyaW5naWZ5KHsKICAgICAgICAgICAgICAgICAgVU5JRlk6IHJlcy5yZXRNYXAuZGljdGlvbmFyeVtzeXN0ZW1Ob10KICAgICAgICAgICAgICAgIH0pKTsKICAgICAgICAgICAgICB9IGVsc2UgewogICAgICAgICAgICAgICAgbG9jYWxTdG9yYWdlLnNldEl0ZW0oc3lzdGVtTm8sIEpTT04uc3RyaW5naWZ5KHJlcy5yZXRNYXBbc3lzdGVtTm9dKSk7CiAgICAgICAgICAgICAgICBsb2NhbFN0b3JhZ2Uuc2V0SXRlbShzeXN0ZW1ObyArICdkaWN0aW9uYXJ5JywgSlNPTi5zdHJpbmdpZnkoX2RlZmluZVByb3BlcnR5KHt9LCBzeXN0ZW1ObywgcmVzLnJldE1hcC5kaWN0aW9uYXJ5W3N5c3RlbU5vXSkpKTsKICAgICAgICAgICAgICB9CiAgICAgICAgICAgICAgbG9jYWxTdG9yYWdlLnNldEl0ZW0oJ2ZpZWxkVGltZScsIHJlcy5yZXRNYXAuZmllbGRUaW1lKTsgLy8g5a2Y5YKo5pWw5o2u5a2X5YW46I635Y+W5pe26Ze0CiAgICAgICAgICAgICAgLy8g5ZCM5q2l6IezdnVlWAogICAgICAgICAgICAgIHRoYXQuJHN0b3JlLmNvbW1pdCgnY29tbW9uL0FERF9ESUNUSU9OQVJZTEVUJywgewogICAgICAgICAgICAgICAgc3lzdGVtTm86IHN5c3RlbU5vLAogICAgICAgICAgICAgICAgdmFsdWU6IHJlcy5yZXRNYXBbc3lzdGVtTm9dLAogICAgICAgICAgICAgICAgZGljdGlvbmFyeTogX2RlZmluZVByb3BlcnR5KHt9LCBzeXN0ZW1ObywgcmVzLnJldE1hcC5kaWN0aW9uYXJ5W3N5c3RlbU5vXSkKICAgICAgICAgICAgICB9KTsKICAgICAgICAgICAgfSk7CiAgICAgICAgICAgIHRoYXQuZGVmYXVsdEZvcm0gPSB7CiAgICAgICAgICAgICAgZmllbGRObzogJycsCiAgICAgICAgICAgICAgZmllbGRUeXBlOiAnJywKICAgICAgICAgICAgICBmaWVsZERlc2M6IG51bGwsCiAgICAgICAgICAgICAgZmllbGRNb2RlOiAndmFyY2hhcicsCiAgICAgICAgICAgICAgZmllbGRJZDogJycsCiAgICAgICAgICAgICAgZmllbGRTaXplOiAwLAogICAgICAgICAgICAgIHBhcmVudEZpZWxkOiAnMDAwMDAwJywKICAgICAgICAgICAgICBsYXN0TW9kaURhdGU6ICcnLAogICAgICAgICAgICAgIGZpZWxkTmFtZTogJycsCiAgICAgICAgICAgICAgZmllbGRMZW5ndGg6IDAKICAgICAgICAgICAgfTsKICAgICAgICAgICAgdGhhdC4kcmVmc1sncmVmRm9ybU5vZGUnXS5yZXNldEZvcm0oKTsKICAgICAgICAgIH0KICAgICAgICB9KS5jYXRjaChmdW5jdGlvbiAoZXJyKSB7CiAgICAgICAgICBpZiAoZXJyLm1lc3NhZ2UuaW5kZXhPZignaGFzQ2hpbGRyZW4nKSAhPT0gLTEpIHsKICAgICAgICAgICAgdGhhdC4kbWVzc2FnZSh7CiAgICAgICAgICAgICAgbWVzc2FnZTogJ+acieWtkOiKgueCuSwg5LiN6IO95Yig6Zmk77yBJywKICAgICAgICAgICAgICB0eXBlOiAnaW5mbycKICAgICAgICAgICAgfSk7CiAgICAgICAgICB9CiAgICAgICAgfSk7CiAgICAgIH0KICAgICAgY29tbW9uTXNnQ29uZmlybSgn5piv5ZCm56Gu6K6k5Yig6Zmk77yfJywgdGhpcywgZGVsKTsKICAgIH0sCiAgICAvKioKICAgICAqIOaMiemSru+8muS/neWtmOihqOWNleagoemqjAogICAgICovCiAgICBoYW5kbGVTYXZlOiBmdW5jdGlvbiBoYW5kbGVTYXZlKCkgewogICAgICB0aGlzLiRyZWZzWydyZWZGb3JtTm9kZSddLnZhbGlkYXRlRm9ybSgpOwogICAgfSwKICAgIC8qKgogICAgICog5oyJ6ZKu77ya5paw5aKe5L+u5pS55L+d5a2Y6K+35rGCb25TYXZlCiAgICAgKi8KICAgIG9uU2F2ZTogZnVuY3Rpb24gb25TYXZlKCkgewogICAgICB2YXIgX3RoaXM2ID0gdGhpczsKICAgICAgdmFyIG1zZyA9IHsKICAgICAgICBwYXJhbWV0ZXJMaXN0OiBbX29iamVjdFNwcmVhZChfb2JqZWN0U3ByZWFkKHt9LCB0aGlzLmRlZmF1bHRGb3JtKSwge30sIHsKICAgICAgICAgIGZpZWxkU2l6ZTogdGhpcy5kZWZhdWx0Rm9ybS5maWVsZFNpemUgKyAnJywKICAgICAgICAgIGZpZWxkTGVuZ3RoOiB0aGlzLmRlZmF1bHRGb3JtLmZpZWxkTGVuZ3RoICsgJycKICAgICAgICB9KV0sCiAgICAgICAgb3Blcl90eXBlOiB0aGlzLm9wZXJfdHlwZSA9PT0gJ2luc2VydCcgPyAnaW5zZXJ0JyA6ICd1cGRhdGUnLAogICAgICAgIG9sZEZpZWxkOiB7CiAgICAgICAgICBpZDogdGhpcy5zZWxlY3RlZE5vZGUuZmllbGRfaWQsCiAgICAgICAgICBuYW1lOiB0aGlzLnNlbGVjdGVkTm9kZS5uYW1lLAogICAgICAgICAgZmllbGRfZGVzYzogdGhpcy5zZWxlY3RlZE5vZGUuZmllbGRfZGVzYywKICAgICAgICAgIGZpZWxkX25vOiB0aGlzLnNlbGVjdGVkTm9kZS5maWVsZF9ubywKICAgICAgICAgIGZpZWxkX25hbWU6IHRoaXMuc2VsZWN0ZWROb2RlLmZpZWxkX25hbWUsCiAgICAgICAgICBmaWVsZF9sZW5ndGg6IHRoaXMuc2VsZWN0ZWROb2RlLmZpZWxkX2xlbmd0aCwKICAgICAgICAgIGZpZWxkX3NpemU6IHRoaXMuc2VsZWN0ZWROb2RlLmZpZWxkX3NpemUsCiAgICAgICAgICBmaWVsZF9tb2RlOiB0aGlzLnNlbGVjdGVkTm9kZS5maWVsZF9tb2RlLAogICAgICAgICAgZmllbGRfdHlwZTogdGhpcy5zZWxlY3RlZE5vZGUuZmllbGRfdHlwZSwKICAgICAgICAgIHN5c3RlbV9ubzogdGhpcy5wcmVEZWZhdWx0Rm9ybS5zeXN0ZW1fbm8sCiAgICAgICAgICAvLyDnlKh0aGlzLnByZURlZmF1bHRGb3JtIOeahHN5c3RlbV9ub++8jOmYsuatouS/ruaUueWQjnN5c3RlbV9ub+S4ouWksQogICAgICAgICAgbGFzdF9tb2RpX2RhdGU6IHRoaXMuc2VsZWN0ZWROb2RlLmxhc3RfbW9kaV9kYXRlCiAgICAgICAgfQogICAgICB9OwogICAgICBpZiAodGhpcy5vcGVyX3R5cGUgPT09ICdpbnNlcnQnKSB7CiAgICAgICAgLy8g5paw5aKe6IqC54K55L+d5a2YCiAgICAgICAgdGhpcy5ub3RTYXZlID0gdHJ1ZTsKICAgICAgICBhZGREaWN0KG1zZykudGhlbiggLyojX19QVVJFX18qL2Z1bmN0aW9uICgpIHsKICAgICAgICAgIHZhciBfcmVmID0gX2FzeW5jVG9HZW5lcmF0b3IoIC8qI19fUFVSRV9fKi9fcmVnZW5lcmF0b3JSdW50aW1lKCkubWFyayhmdW5jdGlvbiBfY2FsbGVlKHJlcykgewogICAgICAgICAgICB2YXIgbm9kZURhdGE7CiAgICAgICAgICAgIHJldHVybiBfcmVnZW5lcmF0b3JSdW50aW1lKCkud3JhcChmdW5jdGlvbiBfY2FsbGVlJChfY29udGV4dCkgewogICAgICAgICAgICAgIHdoaWxlICgxKSB7CiAgICAgICAgICAgICAgICBzd2l0Y2ggKF9jb250ZXh0LnByZXYgPSBfY29udGV4dC5uZXh0KSB7CiAgICAgICAgICAgICAgICAgIGNhc2UgMDoKICAgICAgICAgICAgICAgICAgICAvLyBjb21tb25Nc2dTdWNjZXNzKHJlcy5yZXRNc2csIHRoaXMpCiAgICAgICAgICAgICAgICAgICAgbm9kZURhdGEgPSB7CiAgICAgICAgICAgICAgICAgICAgICBuYW1lOiBfdGhpczYuZGVmYXVsdEZvcm0uZmllbGROYW1lLAogICAgICAgICAgICAgICAgICAgICAgZmllbGRfbm86IF90aGlzNi5kZWZhdWx0Rm9ybS5maWVsZE5vLAogICAgICAgICAgICAgICAgICAgICAgZmllbGRfdHlwZTogX3RoaXM2LmRlZmF1bHRGb3JtLmZpZWxkVHlwZSwKICAgICAgICAgICAgICAgICAgICAgIGZpZWxkX2Rlc2M6IF90aGlzNi5kZWZhdWx0Rm9ybS5maWVsZERlc2MsCiAgICAgICAgICAgICAgICAgICAgICBmaWVsZF9tb2RlOiBfdGhpczYuZGVmYXVsdEZvcm0uZmllbGRNb2RlLAogICAgICAgICAgICAgICAgICAgICAgZmllbGRfc2l6ZTogX3RoaXM2LmRlZmF1bHRGb3JtLmZpZWxkU2l6ZSwKICAgICAgICAgICAgICAgICAgICAgIHBhcmVudF9maWVsZDogX3RoaXM2LmRlZmF1bHRGb3JtLnBhcmVudEZpZWxkLAogICAgICAgICAgICAgICAgICAgICAgbGFzdF9tb2RpX2RhdGU6IF90aGlzNi5kZWZhdWx0Rm9ybS5sYXN0TW9kaURhdGUsCiAgICAgICAgICAgICAgICAgICAgICBmaWVsZF9uYW1lOiBfdGhpczYuZGVmYXVsdEZvcm0uZmllbGROYW1lLAogICAgICAgICAgICAgICAgICAgICAgZmllbGRfbGVuZ3RoOiBfdGhpczYuZGVmYXVsdEZvcm0uZmllbGRMZW5ndGgsCiAgICAgICAgICAgICAgICAgICAgICBpc19wYXJlbnQ6ICcwJywKICAgICAgICAgICAgICAgICAgICAgIGZpZWxkX2lkOiByZXMucmV0TWFwLmZpZWxkX2lkLAogICAgICAgICAgICAgICAgICAgICAgc3lzdGVtX25vOiBfdGhpczYuZGVmYXVsdEZvcm0uc3lzdGVtTm8sCiAgICAgICAgICAgICAgICAgICAgICBmaWVsZF9pZF9jb3B5OiByZXMucmV0TWFwLmZpZWxkX2lkICsgX3RoaXM2LmRlZmF1bHRGb3JtLnN5c3RlbU5vCiAgICAgICAgICAgICAgICAgICAgfTsgLy8gdGhpcy5zZWxlY3RlZE5vZGUyLmRvQ3JlYXRlQ2hpbGRyZW4oW25vZGVEYXRhXSkKICAgICAgICAgICAgICAgICAgICBfdGhpczYuZXhwYW5kZWRrZXlzLnB1c2goX3RoaXM2LmRlZmF1bHRGb3JtLnBhcmVudEZpZWxkKTsKICAgICAgICAgICAgICAgICAgICAvLyDliqjmgIHmt7vliqDoioLngrkKCiAgICAgICAgICAgICAgICAgICAgaWYgKF90aGlzNi5zZWxlY3RlZE5vZGUyLmlzTGVhZikgewogICAgICAgICAgICAgICAgICAgICAgX3RoaXM2LnNlbGVjdGVkTm9kZTIuaXNMZWFmID0gZmFsc2U7CiAgICAgICAgICAgICAgICAgICAgfQogICAgICAgICAgICAgICAgICAgIGNvbnNvbGUubG9nKCdzZWxlY3RlZE5vZGUyOicsIF90aGlzNi5zZWxlY3RlZE5vZGUyKTsKICAgICAgICAgICAgICAgICAgICBfdGhpczYuJHJlZnMudHJlZS5hcHBlbmQobm9kZURhdGEsIF90aGlzNi5zZWxlY3RlZE5vZGUyKTsKICAgICAgICAgICAgICAgICAgICBfdGhpczYuJG5leHRUaWNrKGZ1bmN0aW9uICgpIHsKICAgICAgICAgICAgICAgICAgICAgIF90aGlzNi5zZWxlY3RlZE5vZGUyLmV4cGFuZGVkID0gdHJ1ZTsKICAgICAgICAgICAgICAgICAgICAgIC8vIHRoaXMuJHJlZnMudHJlZS5nZXRDdXJyZW50Tm9kZSgpCiAgICAgICAgICAgICAgICAgICAgICB0aW1lb3V0MiA9IHNldFRpbWVvdXQoZnVuY3Rpb24gKCkgewogICAgICAgICAgICAgICAgICAgICAgICAvLyDorr7nva7pgInkuK3nirbmgIEKICAgICAgICAgICAgICAgICAgICAgICAgX3RoaXM2LiRyZWZzLnRyZWUuc2V0Q3VycmVudEtleShub2RlRGF0YS5maWVsZF9pZF9jb3B5KTsKICAgICAgICAgICAgICAgICAgICAgICAgLy8g6I635Y+W6YCJ5Lit6IqC54K5CiAgICAgICAgICAgICAgICAgICAgICAgIHZhciBub2RlID0gX3RoaXM2LiRyZWZzLnRyZWUuZ2V0Tm9kZShub2RlRGF0YS5maWVsZF9pZF9jb3B5KTsKICAgICAgICAgICAgICAgICAgICAgICAgLy8g6Kem5Y+R6IqC54K554K55Ye75pa55rOVCiAgICAgICAgICAgICAgICAgICAgICAgIF90aGlzNi5ub2RlQ2xpY2sobm9kZS5kYXRhLCBub2RlKTsKICAgICAgICAgICAgICAgICAgICAgICAgdmFyIHN5c3RlbU5vID0gX3RoaXM2LmRlZmF1bHRGb3JtLnN5c3RlbU5vOyAvLyDkv67mlLnnmoTlrZflhbjnmoTns7vnu5/lj7cKICAgICAgICAgICAgICAgICAgICAgICAgdmFyIG1zZzEgPSB7CiAgICAgICAgICAgICAgICAgICAgICAgICAgZmllbGRUaW1lOiAnJywKICAgICAgICAgICAgICAgICAgICAgICAgICBvcGVyVHlwZTogJzEnLAogICAgICAgICAgICAgICAgICAgICAgICAgIC8vIOmXqOaIt+aTjeS9nOagh+ivhgogICAgICAgICAgICAgICAgICAgICAgICAgIHN5c3RlbU5vOiBzeXN0ZW1ObwogICAgICAgICAgICAgICAgICAgICAgICB9OwogICAgICAgICAgICAgICAgICAgICAgICBmaWVsZENvbnRyb2xsZXIobXNnMSkudGhlbihmdW5jdGlvbiAocmVzKSB7CiAgICAgICAgICAgICAgICAgICAgICAgICAgaWYgKHN5c3RlbU5vID09PSAnVU5JRlknKSB7CiAgICAgICAgICAgICAgICAgICAgICAgICAgICBsb2NhbFN0b3JhZ2Uuc2V0SXRlbShzeXN0ZW1ObywgSlNPTi5zdHJpbmdpZnkocmVzLnJldE1hcFtzeXN0ZW1Ob10pKTsKICAgICAgICAgICAgICAgICAgICAgICAgICAgIGxvY2FsU3RvcmFnZS5zZXRJdGVtKCdVTklGWWRpY3Rpb25hcnknLCBKU09OLnN0cmluZ2lmeSh7CiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIFVOSUZZOiByZXMucmV0TWFwLmRpY3Rpb25hcnlbc3lzdGVtTm9dCiAgICAgICAgICAgICAgICAgICAgICAgICAgICB9KSk7CiAgICAgICAgICAgICAgICAgICAgICAgICAgfSBlbHNlIHsKICAgICAgICAgICAgICAgICAgICAgICAgICAgIGxvY2FsU3RvcmFnZS5zZXRJdGVtKHN5c3RlbU5vLCBKU09OLnN0cmluZ2lmeShyZXMucmV0TWFwW3N5c3RlbU5vXSkpOwogICAgICAgICAgICAgICAgICAgICAgICAgICAgbG9jYWxTdG9yYWdlLnNldEl0ZW0oc3lzdGVtTm8gKyAnZGljdGlvbmFyeScsIEpTT04uc3RyaW5naWZ5KF9kZWZpbmVQcm9wZXJ0eSh7fSwgc3lzdGVtTm8sIHJlcy5yZXRNYXAuZGljdGlvbmFyeVtzeXN0ZW1Ob10pKSk7CiAgICAgICAgICAgICAgICAgICAgICAgICAgfQogICAgICAgICAgICAgICAgICAgICAgICAgIGxvY2FsU3RvcmFnZS5zZXRJdGVtKCdmaWVsZFRpbWUnLCByZXMucmV0TWFwLmZpZWxkVGltZSk7IC8vIOWtmOWCqOaVsOaNruWtl+WFuOiOt+WPluaXtumXtAogICAgICAgICAgICAgICAgICAgICAgICAgIC8vIOWQjOatpeiHs3Z1ZVgKICAgICAgICAgICAgICAgICAgICAgICAgICBfdGhpczYuJHN0b3JlLmNvbW1pdCgnY29tbW9uL0FERF9ESUNUSU9OQVJZTEVUJywgewogICAgICAgICAgICAgICAgICAgICAgICAgICAgc3lzdGVtTm86IHN5c3RlbU5vLAogICAgICAgICAgICAgICAgICAgICAgICAgICAgdmFsdWU6IHJlcy5yZXRNYXBbc3lzdGVtTm9dLAogICAgICAgICAgICAgICAgICAgICAgICAgICAgZGljdGlvbmFyeTogX2RlZmluZVByb3BlcnR5KHt9LCBzeXN0ZW1ObywgcmVzLnJldE1hcC5kaWN0aW9uYXJ5W3N5c3RlbU5vXSkKICAgICAgICAgICAgICAgICAgICAgICAgICB9KTsKICAgICAgICAgICAgICAgICAgICAgICAgICBjb21tb25Nc2dTdWNjZXNzKCfmlbDmja7lrZflhbjlkIzmraXmk43kvZzmiJDlip/vvIEnLCBfdGhpczYpOwogICAgICAgICAgICAgICAgICAgICAgICB9KTsKICAgICAgICAgICAgICAgICAgICAgIH0sIDMwMCk7CiAgICAgICAgICAgICAgICAgICAgfSk7CiAgICAgICAgICAgICAgICAgIGNhc2UgNjoKICAgICAgICAgICAgICAgICAgY2FzZSAiZW5kIjoKICAgICAgICAgICAgICAgICAgICByZXR1cm4gX2NvbnRleHQuc3RvcCgpOwogICAgICAgICAgICAgICAgfQogICAgICAgICAgICAgIH0KICAgICAgICAgICAgfSwgX2NhbGxlZSk7CiAgICAgICAgICB9KSk7CiAgICAgICAgICByZXR1cm4gZnVuY3Rpb24gKF94KSB7CiAgICAgICAgICAgIHJldHVybiBfcmVmLmFwcGx5KHRoaXMsIGFyZ3VtZW50cyk7CiAgICAgICAgICB9OwogICAgICAgIH0oKSk7CiAgICAgIH0gZWxzZSB7CiAgICAgICAgLy8g5L+u5pS56IqC54K55L+d5a2YCiAgICAgICAgbW9kaWZ5RGljdChtc2cpLnRoZW4oZnVuY3Rpb24gKHJlcykgewogICAgICAgICAgLy8gY29tbW9uTXNnU3VjY2VzcyhyZXMucmV0TXNnLCB0aGlzKQogICAgICAgICAgdmFyIG5vZGVlZGl0RGF0YSA9IHsKICAgICAgICAgICAgbmFtZTogX3RoaXM2LmRlZmF1bHRGb3JtLmZpZWxkTmFtZSwKICAgICAgICAgICAgZmllbGRfbm86IF90aGlzNi5kZWZhdWx0Rm9ybS5maWVsZE5vLAogICAgICAgICAgICBmaWVsZF90eXBlOiBfdGhpczYuZGVmYXVsdEZvcm0uZmllbGRUeXBlLAogICAgICAgICAgICBmaWVsZF9kZXNjOiBfdGhpczYuZGVmYXVsdEZvcm0uZmllbGREZXNjLAogICAgICAgICAgICBmaWVsZF9tb2RlOiBfdGhpczYuZGVmYXVsdEZvcm0uZmllbGRNb2RlLAogICAgICAgICAgICBmaWVsZF9zaXplOiBfdGhpczYuZGVmYXVsdEZvcm0uZmllbGRTaXplLAogICAgICAgICAgICBwYXJlbnRfZmllbGQ6IF90aGlzNi5kZWZhdWx0Rm9ybS5wYXJlbnRGaWVsZCwKICAgICAgICAgICAgbGFzdF9tb2RpX2RhdGU6IHJlcy5yZXRNYXAubGFzdF9tb2RpX2RhdGUsCiAgICAgICAgICAgIGZpZWxkX25hbWU6IF90aGlzNi5kZWZhdWx0Rm9ybS5maWVsZE5hbWUsCiAgICAgICAgICAgIGZpZWxkX2xlbmd0aDogX3RoaXM2LmRlZmF1bHRGb3JtLmZpZWxkTGVuZ3RoLAogICAgICAgICAgICBmaWVsZF9pZDogX3RoaXM2LnNlbGVjdGVkTm9kZS5maWVsZF9pZCwKICAgICAgICAgICAgc3lzdGVtX25vOiBfdGhpczYucHJlRGVmYXVsdEZvcm0uc3lzdGVtTm8sCiAgICAgICAgICAgIGZpZWxkX2lkX2NvcHk6IF90aGlzNi5zZWxlY3RlZE5vZGUuZmllbGRfaWQgKyBfdGhpczYuZGVmYXVsdEZvcm0uc3lzdGVtTm8KICAgICAgICAgIH07CiAgICAgICAgICAvLyDmm7TmlrDoioLngrnmlbDmja4KICAgICAgICAgIF90aGlzNi5zZWxlY3RlZE5vZGUyLnNldERhdGEobm9kZWVkaXREYXRhKTsgLy8gc2V0RGF0YeaWueazleiwg+eUqOWQju+8jOiHquW3seeahOWtkOiKgueCuei+ieS4ouWksQogICAgICAgICAgLy8g5a+56K+l6IqC54K55LiL5a2Q6IqC54K56L+b6KGM5pu05pawCiAgICAgICAgICBfdGhpczYuc2VsZWN0ZWROb2RlMi5sb2FkZWQgPSBmYWxzZTsKICAgICAgICAgIF90aGlzNi5zZWxlY3RlZE5vZGUyLmV4cGFuZCgpOyAvLyDkuLvliqjosIPnlKjlsZXlvIDoioLngrnmlrnms5XvvIzph43mlrDmn6Xor6Lor6XoioLngrnkuIvnmoTmiYDmnInlrZDoioLngrkKICAgICAgICAgIF90aGlzNi5ub3RTYXZlID0gdHJ1ZTsKICAgICAgICAgIF90aGlzNi4kbmV4dFRpY2soZnVuY3Rpb24gKCkgewogICAgICAgICAgICB0aW1lb3V0MiA9IHNldFRpbWVvdXQoIC8qI19fUFVSRV9fKi9fYXN5bmNUb0dlbmVyYXRvciggLyojX19QVVJFX18qL19yZWdlbmVyYXRvclJ1bnRpbWUoKS5tYXJrKGZ1bmN0aW9uIF9jYWxsZWUyKCkgewogICAgICAgICAgICAgIHZhciBzeXN0ZW1ObywgbXNnMTsKICAgICAgICAgICAgICByZXR1cm4gX3JlZ2VuZXJhdG9yUnVudGltZSgpLndyYXAoZnVuY3Rpb24gX2NhbGxlZTIkKF9jb250ZXh0MikgewogICAgICAgICAgICAgICAgd2hpbGUgKDEpIHsKICAgICAgICAgICAgICAgICAgc3dpdGNoIChfY29udGV4dDIucHJldiA9IF9jb250ZXh0Mi5uZXh0KSB7CiAgICAgICAgICAgICAgICAgICAgY2FzZSAwOgogICAgICAgICAgICAgICAgICAgICAgLy8g6Kem5Y+R6IqC54K554K55Ye75pa55rOVCiAgICAgICAgICAgICAgICAgICAgICBfdGhpczYubm9kZUNsaWNrKF90aGlzNi5zZWxlY3RlZE5vZGUyLmRhdGEsIF90aGlzNi5zZWxlY3RlZE5vZGUyKTsKICAgICAgICAgICAgICAgICAgICAgIHN5c3RlbU5vID0gX3RoaXM2LmRlZmF1bHRGb3JtLnN5c3RlbU5vOyAvLyDkv67mlLnnmoTlrZflhbjnmoTns7vnu5/lj7cKICAgICAgICAgICAgICAgICAgICAgIG1zZzEgPSB7CiAgICAgICAgICAgICAgICAgICAgICAgIGZpZWxkVGltZTogJycsCiAgICAgICAgICAgICAgICAgICAgICAgIG9wZXJUeXBlOiAnMScsCiAgICAgICAgICAgICAgICAgICAgICAgIC8vIOmXqOaIt+aTjeS9nOagh+ivhgogICAgICAgICAgICAgICAgICAgICAgICBzeXN0ZW1Obzogc3lzdGVtTm8KICAgICAgICAgICAgICAgICAgICAgIH07CiAgICAgICAgICAgICAgICAgICAgICBmaWVsZENvbnRyb2xsZXIobXNnMSkudGhlbihmdW5jdGlvbiAocmVzKSB7CiAgICAgICAgICAgICAgICAgICAgICAgIGlmIChzeXN0ZW1ObyA9PT0gJ1VOSUZZJykgewogICAgICAgICAgICAgICAgICAgICAgICAgIGxvY2FsU3RvcmFnZS5zZXRJdGVtKHN5c3RlbU5vLCBKU09OLnN0cmluZ2lmeShyZXMucmV0TWFwW3N5c3RlbU5vXSkpOwogICAgICAgICAgICAgICAgICAgICAgICAgIGxvY2FsU3RvcmFnZS5zZXRJdGVtKCdVTklGWWRpY3Rpb25hcnknLCBKU09OLnN0cmluZ2lmeSh7CiAgICAgICAgICAgICAgICAgICAgICAgICAgICBVTklGWTogcmVzLnJldE1hcC5kaWN0aW9uYXJ5W3N5c3RlbU5vXQogICAgICAgICAgICAgICAgICAgICAgICAgIH0pKTsKICAgICAgICAgICAgICAgICAgICAgICAgfSBlbHNlIHsKICAgICAgICAgICAgICAgICAgICAgICAgICBsb2NhbFN0b3JhZ2Uuc2V0SXRlbShzeXN0ZW1ObywgSlNPTi5zdHJpbmdpZnkocmVzLnJldE1hcFtzeXN0ZW1Ob10pKTsKICAgICAgICAgICAgICAgICAgICAgICAgICBsb2NhbFN0b3JhZ2Uuc2V0SXRlbShzeXN0ZW1ObyArICdkaWN0aW9uYXJ5JywgSlNPTi5zdHJpbmdpZnkoX2RlZmluZVByb3BlcnR5KHt9LCBzeXN0ZW1ObywgcmVzLnJldE1hcC5kaWN0aW9uYXJ5W3N5c3RlbU5vXSkpKTsKICAgICAgICAgICAgICAgICAgICAgICAgfQogICAgICAgICAgICAgICAgICAgICAgICBsb2NhbFN0b3JhZ2Uuc2V0SXRlbSgnZmllbGRUaW1lJywgcmVzLnJldE1hcC5maWVsZFRpbWUpOyAvLyDlrZjlgqjmlbDmja7lrZflhbjojrflj5bml7bpl7QKICAgICAgICAgICAgICAgICAgICAgICAgLy8g5ZCM5q2l6IezdnVlWAogICAgICAgICAgICAgICAgICAgICAgICBfdGhpczYuJHN0b3JlLmNvbW1pdCgnY29tbW9uL0FERF9ESUNUSU9OQVJZTEVUJywgewogICAgICAgICAgICAgICAgICAgICAgICAgIHN5c3RlbU5vOiBzeXN0ZW1ObywKICAgICAgICAgICAgICAgICAgICAgICAgICB2YWx1ZTogcmVzLnJldE1hcFtzeXN0ZW1Ob10sCiAgICAgICAgICAgICAgICAgICAgICAgICAgZGljdGlvbmFyeTogX2RlZmluZVByb3BlcnR5KHt9LCBzeXN0ZW1ObywgcmVzLnJldE1hcC5kaWN0aW9uYXJ5W3N5c3RlbU5vXSkKICAgICAgICAgICAgICAgICAgICAgICAgfSk7CiAgICAgICAgICAgICAgICAgICAgICAgIGNvbW1vbk1zZ1N1Y2Nlc3MoJ+aVsOaNruWtl+WFuOWQjOatpeaTjeS9nOaIkOWKn++8gScsIF90aGlzNik7CiAgICAgICAgICAgICAgICAgICAgICB9KTsKICAgICAgICAgICAgICAgICAgICBjYXNlIDQ6CiAgICAgICAgICAgICAgICAgICAgY2FzZSAiZW5kIjoKICAgICAgICAgICAgICAgICAgICAgIHJldHVybiBfY29udGV4dDIuc3RvcCgpOwogICAgICAgICAgICAgICAgICB9CiAgICAgICAgICAgICAgICB9CiAgICAgICAgICAgICAgfSwgX2NhbGxlZTIpOwogICAgICAgICAgICB9KSksIDMwMCk7CiAgICAgICAgICB9KTsKICAgICAgICB9KTsKICAgICAgfQogICAgfSwKICAgIC8qKgogICAgICog5oyJ6ZKu77ya5Y+W5raICiAgICAgKi8KICAgIGhhbmRsZUNhbmNsZTogZnVuY3Rpb24gaGFuZGxlQ2FuY2xlKCkgewogICAgICB0aGlzLmRlZmF1bHRGb3JtID0gdGhpcy5wcmVEZWZhdWx0Rm9ybTsKICAgICAgdGhpcy5jb25maWcgPSBjb25maWcodGhpcywgewogICAgICAgIGRpc2FibGVDb25maWc6IHsKICAgICAgICAgIHBhcmVudF9maWVsZDogdHJ1ZSwKICAgICAgICAgIGZpZWxkX25vOiB0cnVlLAogICAgICAgICAgZWxzZTogdHJ1ZQogICAgICAgIH0KICAgICAgfSk7CiAgICAgIHRoaXMubm90U2F2ZSA9IHRydWU7CiAgICB9LAogICAgLyoqCiAgICAgKiDooajljZXmoKHpqowKICAgICAqIEBwYXJhbSB7Qm9vbGVhbn12YWxpZCDmoKHpqozov5Tlm57lgLwqLwogICAgdmFsaWRhdGVGb3JtOiBmdW5jdGlvbiB2YWxpZGF0ZUZvcm0odmFsdWUpIHsKICAgICAgaWYgKHZhbHVlKSB7CiAgICAgICAgdGhpcy5vblNhdmUoKTsKICAgICAgfSBlbHNlIHsKICAgICAgICByZXR1cm4gZmFsc2U7CiAgICAgIH0KICAgIH0sCiAgICAvKioKICAgICAqIOiOt+WPluaVsOaNruWtl+WFuAogICAgICovCiAgICBnZXREaWN0aW9uYXJ5RGF0YTogZnVuY3Rpb24gZ2V0RGljdGlvbmFyeURhdGEoc3lzdGVtTm8pIHsKICAgICAgdmFyIF90aGlzNyA9IHRoaXM7CiAgICAgIHJldHVybiBuZXcgUHJvbWlzZShmdW5jdGlvbiAocmVzb2x2ZSkgewogICAgICAgIHZhciBtc2cgPSB7CiAgICAgICAgICBwYXJhbWV0ZXJMaXN0OiBbJyddLAogICAgICAgICAgZmllbGRUaW1lOiAnJywKICAgICAgICAgIG9wZXJUeXBlOiAnMScsCiAgICAgICAgICAvLyDpl6jmiLfmk43kvZzmoIfor4YKICAgICAgICAgIHN5c3RlbU5vOiBzeXN0ZW1ObwogICAgICAgIH07CiAgICAgICAgZmllbGRDb250cm9sbGVyKG1zZykudGhlbihmdW5jdGlvbiAocmVzKSB7CiAgICAgICAgICAvLyDmlrDlop7lpJbns7vnu5/lrZflhbgKICAgICAgICAgIGxvY2FsU3RvcmFnZS5zZXRJdGVtKCdmaWVsZFRpbWUnLCByZXMucmV0TWFwLmZpZWxkVGltZSk7IC8vIOWtmOWCqOaVsOaNruWtl+WFuOiOt+WPluaXtumXtAogICAgICAgICAgLy8g5a2X5YW46K+35rGC5pe26Ze05Li656m65pe2CiAgICAgICAgICBsb2NhbFN0b3JhZ2Uuc2V0SXRlbShzeXN0ZW1ObywgSlNPTi5zdHJpbmdpZnkocmVzLnJldE1hcFtzeXN0ZW1Ob10pKTsKICAgICAgICAgIGxvY2FsU3RvcmFnZS5zZXRJdGVtKHN5c3RlbU5vICsgJ2RpY3Rpb25hcnknLCBKU09OLnN0cmluZ2lmeShyZXMucmV0TWFwLmRpY3Rpb25hcnkpKTsKICAgICAgICAgIF90aGlzNy4kc3RvcmUuY29tbWl0KCdjb21tb24vQUREX0RJQ1RJT05BUllMRVQnLCB7CiAgICAgICAgICAgIHN5c3RlbU5vOiBzeXN0ZW1ObywKICAgICAgICAgICAgdmFsdWU6IHJlcy5yZXRNYXBbc3lzdGVtTm9dLAogICAgICAgICAgICBkaWN0aW9uYXJ5OiByZXMucmV0TWFwLmRpY3Rpb25hcnkKICAgICAgICAgIH0pOwogICAgICAgICAgcmVzb2x2ZSgpOwogICAgICAgIH0pOwogICAgICB9KTsKICAgIH0KICB9Cn07"}, {"version": 3, "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA8GA,SACAA,kBACAC,kBACAC,qBACA;AACA;AACA;AACA;AACA;AACA;AACA,4BACAC;EADAC;EAAAC;EAAAC;EAAAC;EAAAC;AAEA;AACA;AACA;AACA;AACA;EACAC;EACAC;IACA;MACAC;MAAA;MACAC;MAAA;MACAC;MAAA;MACAC;MAAA;MACAC;MAAA;MACA;MACAC;MAAA;MACAC;MAAA;MACAC;MAAA;MACAC;MAAA;MACAC;MAAA;MACAC;MAAA;MACAC;MAAA;MACAC;QACAC;UACAC;UACAC;UACAC;QACA;MACA;MACAC;MAAA;MACAC;MAAA;MACAC;MACAC;QACAC;QACAC;QACAC;QACAC;QACAC;QACAC;QACAC;QACAC;QACAC;QACAC;MACA;MACAC;QACAC;QACAC;QACAC;MACA;MACAC;MACAC;QACA;QACAC;UACAC;QACA;QACAC;UACAD;QACA;QACAE;UACAF;QACA;QACAG;UACAH;QACA;QACAI;UACAJ;QACA;QACAK;UACAL;QACA;MACA;MACAM;MAAA;MACAC;IACA;EACA;;EACAC;IACA7C;MACA;QACA;MACA;IACA;EACA;EACA8C;EACAC;IACAC;IACAA;IACAA;EACA;EACAC;IAAA;IACA;MACA;IACA;EACA;EACAC;IACA;AACA;IACAC;MACA;IACA;IACA;AACA;AACA;AACA;IACA;AACA;AACA;AACA;AACA;IACAC;MACA;MACA;IACA;IACA;AACA;AACA;AACA;AACA;IACAC;MACA;MACA;MACA;QACAjC;QACAC;QACAC;QACAC;QACAC;QACAC;QACAC;QACAC;QACAC;QACAC;QACAyB;MACA;MACA;MACA;QACA;QACA;UACA1C;YACAC;YACAC;YACAC;UACA;QACA;MACA;IACA;IACA;AACA;AACA;AACA;AACA;IACAwC;MACAC;MACA;QACA;QACA;UACAC;YAAA/B;UAAA;UACA4B;QACA;QACA9D;UACA;UACA;UACAkE;YACAC;YACAA;UACA;UACA;YAAA;UAAA;UACAC;QACA;MACA;MACA;QACA;QACA;UACAH;YAAA/B;UAAA;UACAjB;UACA6C;QACA;QACA9D;UACA;UACA;UACAkE;YACAC;YACAA;UACA;UACA,2BACA;YAAA,OACAA;UAAA,EACA;UACA;QACA;MACA;MACA;QACA;UACAF;YAAA/B;UAAA;UACAjB;UACA6C;QACA;QACA9D;UACA;UACAkE;YACA;cACAC;YACA;YACAA;YACAA;UACA;UACA;YAAA;UAAA;UACA;QACA;QACA;MACA;IACA;IACA;AACA;AACA;IACAE;MAAA;MACA;QACA;MACA;MACA;MACA;QACAJ;QACAhD;QACAF;QAAA;QACAuD;QACAC;MACA;;MACAtE;QACA;UAAA;UACA;YAAAuE;YAAAC;YAAAC;YAAAZ;UACA;UACA;UACA;UACA;UACA;UAEA;UAEA;YACA;YACA;UACA;YACA;YACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;YACAK;YACA;UACA;UACA;UACA;QACA;UACArE;UACA;UACA;QACA;MACA;IACA;IACA;AACA;AACA;IACA6E;MAAA;MACA;QACA;QACA;QACA;QACA;QACA;QACA;MACA;QACA;QACA;QACA;QACA;QACA;UACAV;UACAhD;UACAF;UAAA;UACAuD;UACAC;QACA;;QACAtE;UAAA;UACA;YAAAuE;YAAAC;YAAAC;YAAAZ;UACA;UACA;UACA;UACA;YACAK;YACA;UACA;UACA;UACA;QACA;MACA;IACA;IACA;AACA;AACA;IACAS;MAAA;MACA;MACA;MACA;MACA;MACA;QACA;MACA;;MACA;MACA;QACAX;QACAhD;QACAF;QAAA;QACAuD;QACAC;MACA;;MACAtE;QAAA;QACA;UAAAuE;UAAAC;UAAAC;UAAAZ;QACA;;QAEA;UACA;UACA;QACA;;QAEA;QACA;UACAK;UACA;QACA;QACA;QACA;QACA;MACA;IACA;IAEA;AACA;AACA;AACA;IACAU;MAAA;MACA;MACA;MACAC;QACA;QACA;QACA;QACA;QACA;QACA;UACA;UACA;YACA;YACA;YACAC;UACA;QACA;MACA;IACA;IACA;AACA;AACA;IACAC;MACA;QACA;UACAC;UACAC;QACA;QACA;MACA;MACA;MACA;MACA;MACA;QACAtD;QACAC;QACAC;QACAC;QACAC;QACAC;QACAC,aACA,0CACA,6BACA;QACAC;QACAC;QACAC;QACAyB;MACA;;MACA;QACA1C;UACAC;UACAC;UACAC;QACA;MACA;IACA;IACA;AACA;AACA;IACA4D;MACA;MACA;QACA;UACAF;UACAC;QACA;QACA;MACA;MACA;QACA;QACA;UACAD;UACAC;QACA;QACA;MACA;MACA;MACA;MACA,mDACA;QACApB;MAAA,EACA;;MACA;MACA;QACA1C;UACAC;UACAC;UACAC;QACA;MACA;IACA;IACA;AACA;AACA;IACA6D;MACA;MACA;MACA;QACA;QACA;UACAH;UACAC;QACA;QACA;MACA;MACA;MACA;MACA;MACA;QACA;UACA;QACA;QACA;UACAjB,gBACA;YACAjC;YACAE;YACA4B;UACA,EACA;UACA7C;QACA;QACAb,gBACAiF;UACA;YACAxF;YACAyF;YACA;YACAA;YACA;cACAC;cACAC;cAAA;cACA1B;YACA;YACA2B;cACA;gBACAC,qBACA5B,UACA6B,qCACA;gBACAD,qBACA,mBACAC;kBAAAC;gBAAA,GACA;cACA;gBACAF,qBACA5B,UACA6B,qCACA;gBACAD,qBACA5B,yBACA6B,+EACA;cACA;cACAD;cACA;cACAJ;gBACAxB;gBACA+B;gBACAC;cACA;YACA;YACAR;cACA1D;cACAC;cACAC;cACAC;cACAC;cACAC;cACAC;cACAC;cACAC;cACAC;YACA;YACAiD;UACA;QACA,GACAS;UACA;YACAT;cACAL;cACAC;YACA;UACA;QACA;MACA;MACAtF;IACA;IACA;AACA;AACA;IACAoG;MACA;IACA;IACA;AACA;AACA;IACAC;MAAA;MACA;QACAhC,gDAEA;UACAhC;UACAI;QAAA,GAEA;QACApB;QACAiF;UACAC;UACA9F;UACA+F;UACA9E;UACA+E;UACAC;UACAC;UACAC;UACAC;UACAC;UAAA;UACAC;QACA;MACA;MACA;QACA;QACA;QACAzG;UAAA;YAAA;YAAA;cAAA;gBAAA;kBAAA;oBACA;oBACA0G;sBACAvG;sBACAiB;sBACAmF;sBACAL;sBACAI;sBACAD;sBACAlF;sBACAsF;sBACAN;sBACAC;sBACAO;sBACArC;sBACAkC;sBACAI;oBACA,GACA;oBACA;oBACA;;oBAEA;sBACA;oBACA;oBAEA9C;oBACA;oBACA;sBACA;sBACA;sBACA+C;wBACA;wBACA;wBACA;wBACA;wBACA;wBACA;wBAEA;wBACA;0BACAxB;0BACAC;0BAAA;0BACA1B;wBACA;wBACA2B;0BACA;4BACAC,qBACA5B,UACA6B,qCACA;4BACAD,qBACA,mBACAC;8BAAAC;4BAAA,GACA;0BACA;4BACAF,qBACA5B,UACA6B,qCACA;4BACAD,qBACA5B,yBACA6B,+EACA;0BACA;0BACAD;0BACA;0BACA;4BACA5B;4BACA+B;4BACAC;0BACA;0BACAjG;wBACA;sBACA;oBACA;kBAAA;kBAAA;oBAAA;gBAAA;cAAA;YAAA;UAAA,CACA;UAAA;YAAA;UAAA;QAAA;MACA;QACA;QACAM;UACA;UACA;YACAE;YACAiB;YACAmF;YACAL;YACAI;YACAD;YACAlF;YACAsF;YACAN;YACAC;YACA9B;YACAkC;YACAI,eACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;YACAC;cAAA;cAAA;gBAAA;kBAAA;oBAAA;sBACA;sBACA;sBACAjD;sBACAkD;wBACAzB;wBACAC;wBAAA;wBACA1B;sBACA;sBACA2B;wBACA;0BACAC,qBACA5B,UACA6B,qCACA;0BACAD,qBACA,mBACAC;4BAAAC;0BAAA,GACA;wBACA;0BACAF,qBACA5B,UACA6B,qCACA;0BACAD,qBACA5B,yBACA6B,+EACA;wBACA;wBACAD;wBACA;wBACA;0BACA5B;0BACA+B;0BACAC;wBACA;wBACAjG;sBACA;oBAAA;oBAAA;sBAAA;kBAAA;gBAAA;cAAA;YAAA,CACA;UACA;QACA;MACA;IACA;IACA;AACA;AACA;IACAoH;MACA;MACA;QACA7F;UACAC;UACAC;UACAC;QACA;MACA;MACA;IACA;IACA;AACA;AACA;IACA2F;MACA;QACA;MACA;QACA;MACA;IACA;IACA;AACA;AACA;IACAC;MAAA;MACA;QACA;UACAlD;UACAsB;UACAC;UAAA;UACA1B;QACA;QACA2B;UACA;UACAC;UACA;UACAA;UACAA,qBACA5B,yBACA6B,sCACA;UACA;YACA7B;YACA+B;YACAC;UACA;UACA1B;QACA;MACA;IACA;EACA;AACA", "names": ["commonMsgConfirm", "commonMsgSuccess", "commonMsgWarn", "system", "getTreeData", "searchDict", "addDict", "modifyDict", "deleteDict", "name", "data", "notSave", "searchValue", "searchRes", "length", "totalNum", "islast", "backDisabled", "nextDisabled", "fieNo", "searchflag", "oper_type", "expandedkeys", "config", "disableConfig", "parent_field", "field_no", "else", "preDefaultForm", "selectedNode", "selectedNode2", "defaultForm", "fieldNo", "fieldType", "fieldDesc", "fieldMode", "fieldId", "fieldSize", "parentField", "lastModiDate", "fieldName", "<PERSON><PERSON><PERSON><PERSON>", "props", "label", "children", "<PERSON><PERSON><PERSON><PERSON>", "btnDatas", "btnDatasAll", "btnQuery", "show", "btnAdd", "btnModify", "btnDelete", "btnSave", "btnCancle", "oldSystemNo", "newSystemNo", "watch", "created", "<PERSON><PERSON><PERSON><PERSON>", "clearTimeout", "mounted", "methods", "btnPermissions", "nodeExpand", "nodeClick", "systemNo", "loadNode", "console", "parameterList", "list", "item", "resolve", "onSearch", "fieldIdOrName", "flag", "field_id", "listParentId", "len", "back", "next", "setCurrentNode", "timeout1", "dom2", "handleAdd", "message", "type", "handleModify", "handleDelete", "then", "that", "fieldTime", "operType", "fieldController", "localStorage", "JSON", "UNIFY", "value", "dictionary", "catch", "handleSave", "onSave", "<PERSON><PERSON><PERSON>", "id", "field_desc", "field_name", "field_length", "field_size", "field_mode", "field_type", "system_no", "last_modi_date", "nodeData", "is_parent", "field_id_copy", "timeout2", "msg1", "handleCancle", "validateForm", "getDictionaryData"], "sourceRoot": "src/views/system/config/dictionary", "sources": ["index.vue"], "sourcesContent": ["<!--参数配置页面  -->\n<template>\n  <div class=\"dictionary app-container\">\n    <div class=\"dictionary-box\">\n      <div class=\"dictionary-left\">\n        <div class=\"title\">数据字典</div>\n        <el-tree\n          ref=\"tree\"\n          :props=\"props\"\n          :load=\"loadNode\"\n          node-key=\"field_id_copy\"\n          :highlight-current=\"true\"\n          lazy\n          accordion\n          :expand-on-click-node=\"false\"\n          :default-expanded-keys=\"expandedkeys\"\n          @node-click=\"nodeClick\"\n          @node-expand=\"nodeExpand\"\n        />\n      </div>\n      <div class=\"dictionary-right\">\n        <div class=\"title\">字典查询</div>\n\n        <el-form :inline=\"true\" class=\"demo-form-inline\" @submit.native.prevent>\n          <el-form-item label=\"字典号/字典名\">\n            <el-input\n              v-model=\"searchValue\"\n              placeholder=\"支持字典号/字典名模糊查询\"\n              @keyup.enter.native=\"onSearch\"\n            />\n          </el-form-item>\n          <el-form-item>\n            <sun-button\n              :btn-datas=\"{\n                btnQuery: {\n                  show: btnDatasAll.btnQuery\n                }\n              }\"\n              @handleQuery=\"onSearch\"\n            />\n          </el-form-item>\n        </el-form>\n        <div v-show=\"searchRes\" class=\"searchRes\">\n          <span>当前第 {{ length }} / {{ totalNum }} 个结果</span>\n          <el-button\n            type=\"primary\"\n            :disabled=\"backDisabled\"\n            size=\"mini\"\n            @click=\"back\"\n          >上一条</el-button>\n          <el-button\n            type=\"primary\"\n            :disabled=\"nextDisabled\"\n            size=\"mini\"\n            @click=\"next\"\n          >下一条</el-button>\n        </div>\n        <div>\n          <div class=\"title\">字典配置</div>\n          <sun-form\n            ref=\"refFormNode\"\n            label-width=\"12rem\"\n            :config=\"config\"\n            :default-form=\"defaultForm\"\n            :query=\"false\"\n            :reset=\"false\"\n            @validateForm=\"validateForm\"\n          >\n            <template v-slot:footer>\n              <div class=\"button-line\">\n                <SunButton\n                  v-if=\"notSave\"\n                  :btn-datas=\"{\n                    btnAdd: {\n                      show: btnDatasAll.btnAdd\n                    },\n                    btnModify: {\n                      show: btnDatasAll.btnModify\n                    },\n                    btnDelete: {\n                      show: btnDatasAll.btnDelete\n                    }\n                  }\"\n                  @handleAdd=\"handleAdd\"\n                  @handleModify=\"handleModify\"\n                  @handleDelete=\"handleDelete\"\n                />\n                <SunButton\n                  v-else\n                  :btn-datas=\"{\n                    btnSave: {\n                      show: btnDatasAll.btnSave\n                    },\n                    btnCancle: {\n                      show: btnDatasAll.btnCancle\n                    }\n                  }\"\n                  @handleSave=\"handleSave\"\n                  @handleCancle=\"handleCancle\"\n                />\n              </div>\n            </template>\n          </sun-form>\n        </div>\n      </div>\n    </div>\n  </div>\n</template>\n\n<script>\nimport {\n  commonMsgConfirm,\n  commonMsgSuccess,\n  commonMsgWarn\n} from '@/utils/message'\nimport { commonBlank } from '@/utils/common'\nimport { config } from './info'\nimport { dateTimeFormat } from '@/filters'\nimport { permissionsBtn } from '@/utils/permissions' // 权限配置\nimport { system, Common } from '@/api'\nconst { getTreeData, searchDict, addDict, modifyDict, deleteDict } =\n  system.SysDictionary\nconst { fieldController } = Common\nlet timeout1\nlet timeout2\nlet timeout3\nexport default {\n  name: 'Dictionary',\n  data() {\n    return {\n      notSave: true, // 不保存   保存按钮不出现\n      searchValue: '', // 搜索框的值\n      searchRes: false, // 搜索结果不显示\n      length: 1, // 搜索结果为当前第几个\n      totalNum: '', // 搜索结果总条数\n      //  isFirst: true, //是否是搜索的第一条数据\n      islast: false, // 是否是搜索的第一条数据\n      backDisabled: false, // 上一步按钮不禁用\n      nextDisabled: false, // 下一步按钮不禁用\n      fieNo: 0, // 第一次搜索\n      searchflag: '1', // 第一次搜索\n      oper_type: '', // 操作类型\n      expandedkeys: ['000000'], // 默认展开项\n      config: config(this, {\n        disableConfig: {\n          parent_field: true,\n          field_no: true,\n          else: true\n        }\n      }),\n      preDefaultForm: {}, // 表单默认值备份\n      selectedNode: {}, // 点击选中的节点\n      selectedNode2: null,\n      defaultForm: {\n        fieldNo: '',\n        fieldType: '',\n        fieldDesc: null,\n        fieldMode: 'varchar',\n        fieldId: '',\n        fieldSize: 0,\n        parentField: '000000',\n        lastModiDate: '',\n        fieldName: '',\n        fieldLength: 0\n      },\n      props: {\n        label: 'name',\n        children: 'zones',\n        isLeaf: 'leaf'\n      },\n      btnDatas: {},\n      btnDatasAll: {\n        // 配置所有按钮\n        btnQuery: {\n          show: false\n        },\n        btnAdd: {\n          show: true\n        },\n        btnModify: {\n          show: true\n        },\n        btnDelete: {\n          show: true\n        },\n        btnSave: {\n          show: true\n        },\n        btnCancle: {\n          show: true\n        }\n      },\n      oldSystemNo: '', // 保存上一份系统号\n      newSystemNo: '' // 新系统号\n    }\n  },\n  watch: {\n    searchValue(val) {\n      if (commonBlank(val)) {\n        this.searchRes = false\n      }\n    }\n  },\n  created() {},\n  beforeDestroy() {\n    clearTimeout(timeout1)\n    clearTimeout(timeout2)\n    clearTimeout(timeout3)\n  },\n  mounted() {\n    this.$nextTick().then(() => {\n      this.btnPermissions()\n    })\n  },\n  methods: {\n    /**\n     * 按钮权限配置*/\n    btnPermissions() {\n      this.btnDatasAll = permissionsBtn(this.$attrs.button_id, this.btnDatasAll)\n    },\n    /**\n     * 按钮显示配置\n     * @param {Boolean} param true显示增删改，false显示保存、取消\n     */\n    /**\n     * 节点展开\n     * @param {Object}node\n     * @param {Object}node2\n     */\n    nodeExpand(node, node2) {\n      this.selectedNode = node\n      this.selectedNode2 = node2\n    },\n    /**\n     * 节点单击\n     * @param {Object}node\n     * @param {Object}node2\n     */\n    nodeClick(node, node2) {\n      this.selectedNode = node\n      this.selectedNode2 = node2\n      this.defaultForm = {\n        fieldNo: node.field_no,\n        fieldType: node.field_type,\n        fieldDesc: node.field_desc,\n        fieldMode: node.field_mode,\n        fieldId: node.field_id,\n        fieldSize: node.field_size,\n        parentField: node.parent_field,\n        lastModiDate: dateTimeFormat(node.last_modi_date),\n        fieldName: node.field_name,\n        fieldLength: node.field_length,\n        systemNo: node.system_no // 新增参数：系统号\n      }\n      // 如果进行了增删改操作    保存按钮显示\n      if (this.oper_type !== '') {\n        this.notSave = true // 保存按不显示\n        this.config = config(this, {\n          disableConfig: {\n            parent_field: true,\n            field_no: true,\n            else: true\n          }\n        })\n      }\n    },\n    /**\n     * 加载树\n     * @param {Object}node\n     * @param {}resolve\n     */\n    loadNode(node, resolve) {\n      console.log(node)\n      if (node.level === 0) {\n        // 获取树初始数据\n        const msg = {\n          parameterList: [{ parentField: '000000' }],\n          systemNo: 'UNIFY'\n        }\n        getTreeData(msg).then((res) => {\n          const { list } = res.retMap\n          // list.shift()\n          list.forEach((item) => {\n            item['name'] = item.field_name\n            item['field_id_copy'] = item.field_id + item.system_no\n          })\n          const treeList = list.filter((item) => item.field_type === '00')\n          resolve(treeList)\n        })\n      }\n      if (node.level === 1) {\n        // 获取树初始数据\n        const msg = {\n          parameterList: [{ parentField: node.data.field_id }],\n          oper_type: 'asysc',\n          systemNo: node.data.system_no\n        }\n        getTreeData(msg).then((res) => {\n          const { list } = res.retMap\n          // list.shift()\n          list.forEach((item) => {\n            item['name'] = item.field_name\n            item['field_id_copy'] = item.field_id + item.system_no\n          })\n          const treeList = list.filter(\n            (item) =>\n              item.system_no === node.data.system_no && item.field_type !== '00'\n          )\n          return resolve(treeList)\n        })\n      }\n      if (node.level > 1) {\n        const msg = {\n          parameterList: [{ parentField: node.data.field_id }],\n          oper_type: 'asysc',\n          systemNo: node.data.system_no\n        }\n        getTreeData(msg).then((res) => {\n          const { list } = res.retMap\n          list.forEach((item) => {\n            if (item.is_parent === '0') {\n              item['leaf'] = true\n            }\n            item['name'] = item.field_name\n            item['field_id_copy'] = item.field_id + item.system_no\n          })\n          const treeList = list.filter((item) => item.field_type !== '00')\n          return resolve(treeList)\n        })\n        return\n      }\n    },\n    /**\n     * 按钮：搜索\n     */\n    onSearch() {\n      if (this.searchValue === '') {\n        return\n      }\n      // 第一次搜索参数\n      const msg = {\n        parameterList: [],\n        oper_type: 'searchParentSon',\n        fieNo: 0, // 0\n        fieldIdOrName: this.searchValue,\n        flag: '1' // '1'\n      }\n      searchDict(msg).then((res) => {\n        if (!commonBlank(res.retMap)) {\n          const { field_id, listParentId, len, systemNo } = res.retMap\n          this.totalNum = len // 总条数复制\n          this.length = 1 // 重新查询时初始化 当前为第1个结果\n          this.fieNo = 0 // 重新查询时初始化第1个结果下标为0\n          // 点击搜索  上一条、下一条出现\n          this.searchRes = true\n\n          this.oldSystemNo = systemNo\n\n          if (this.totalNum === 1) {\n            this.backDisabled = true // 如果是第一条  上一条按钮禁用\n            this.nextDisabled = true // 重新查询时初始化 下一步禁用按钮\n          } else {\n            this.backDisabled = true // 如果是第一条  上一条按钮禁用\n            this.nextDisabled = false // 重新查询时初始化 下一步禁用按钮\n          }\n          // 展开项\n          // const listParentIdCopy = listParentId.map((item) => {\n          //   item = item + systemNo\n          //   return item\n          // })\n          this.expandedkeys.push(...listParentId)\n          this.expandedkeys = this.expandedkeys.map((item) => {\n            item = item + this.oldSystemNo\n            return item\n          })\n          // console.log(77777, '搜索展开项', this.expandedkeys)\n          this.setCurrentNode(field_id + systemNo)\n        } else {\n          commonMsgWarn('数据字典表中未匹配到该条数据', this)\n          // 点击搜索  上一条、下一条隐藏\n          this.searchRes = false\n        }\n      })\n    },\n    /**\n     * 按钮：上一条\n     */\n    back() {\n      if (this.fieNo === 1) {\n        // 第一条数据  上一步按钮禁用   第一次查询this.searchflag='1'\n        this.length = 1\n        this.fieNo = 0\n        this.searchflag = '1' // 点击下一步   非第一条查询数据\n        this.backDisabled = true\n        this.onSearch() // 上一步到第一条数据相当于直接调用查询方法\n      } else {\n        // fieno 下标-1  第几条数据length-1  (非第一条数据)\n        this.nextDisabled = false\n        this.length = this.length - 1 // 第几条数据-1\n        this.fieNo = this.fieNo - 1 // 数据下标\n        const msg = {\n          parameterList: [],\n          oper_type: 'searchParentSon',\n          fieNo: this.fieNo, // 1,2,3,4......\n          fieldIdOrName: this.searchValue,\n          flag: this.searchflag // '2'\n        }\n        searchDict(msg).then((res) => {\n          const { field_id, listParentId, len, systemNo } = res.retMap\n          this.totalNum = len // 总条数复制\n          // 展开项\n          this.expandedkeys.push(...listParentId)\n          this.expandedkeys = this.expandedkeys.map((item) => {\n            item = item + systemNo\n            return item\n          })\n          // console.log('上一条展开项', this.expandedkeys)\n          this.setCurrentNode(field_id + systemNo)\n        })\n      }\n    },\n    /**\n     * 按钮：下一条\n     */\n    next() {\n      this.backDisabled = false // 上一条按钮可用\n      this.length = this.length + 1 // 第几条数据+1\n      // this.isFirst = false; //点击下一步   非第一条查询数据\n      this.fieNo = this.fieNo + 1 // 数据下标\n      if (this.fieNo + 1 === this.totalNum) {\n        this.nextDisabled = true // 如果是最后一条  下一步按钮禁用\n      }\n      this.searchflag = '2'\n      const msg = {\n        parameterList: [],\n        oper_type: 'searchParentSon',\n        fieNo: this.fieNo, // 1,2,3,4......\n        fieldIdOrName: this.searchValue,\n        flag: this.searchflag // '2'\n      }\n      searchDict(msg).then((res) => {\n        const { field_id, listParentId, len, systemNo } = res.retMap\n        this.totalNum = len // 总条数复制\n\n        if (systemNo !== this.oldSystemNo) {\n          this.expandedkeys.push('000000' + systemNo)\n          this.oldSystemNo = systemNo\n        }\n\n        // 展开项\n        const listParentIdCopy = listParentId.map((item) => {\n          item = item + systemNo\n          return item\n        })\n        this.expandedkeys.push(...listParentIdCopy) // 没有执行展开项\n        // console.log('66666下一条展开项', this.expandedkeys)\n        this.setCurrentNode(field_id + systemNo)\n      })\n    },\n\n    /**\n     * 定位左侧树当前节点和高亮节点\n     * @param {String} field_id_copy 当前节点ID\n     */\n    setCurrentNode(field_id_copy) {\n      // console.log('55555:', field_id_copy)\n      // 如果该系统号的数据没有进行懒加载，则进行懒加载后再进行点击定位等操作\n      timeout1 = setTimeout(() => {\n        this.$refs.tree.setCurrentKey(field_id_copy)\n        // this.$nextTick(() => {\n        const node = this.$refs.tree.getNode(field_id_copy) // node为空-----------走不进判断 (异步请求)、树懒加载  异步请求\n        // })\n        // console.log('node--', node)\n        if (!commonBlank(node)) {\n          this.nodeClick(node.data)\n          this.$nextTick(() => {\n            const dom = document.querySelector('.is-current') // 当前高亮节点盒子\n            const dom2 = document.querySelector('.dictionary-left') // tree大盒子\n            dom2.scrollTop = dom.offsetTop - 200 // 给tree盒子设置scrolTop定位到当前高亮节点\n          })\n        }\n      }, 1000)\n    },\n    /**\n     * 按钮：新增\n     */\n    handleAdd() {\n      if (commonBlank(this.selectedNode)) {\n        this.$message({\n          message: '请选择父节点',\n          type: 'warning'\n        })\n        return\n      }\n      this.notSave = false\n      this.oper_type = 'insert'\n      this.preDefaultForm = this.defaultForm\n      this.defaultForm = {\n        fieldNo: '',\n        fieldType: '',\n        fieldDesc: '',\n        fieldMode: '',\n        fieldId: this.selectedNode.field_id !== '000000' ? '后台生成' : '',\n        fieldSize: '',\n        parentField:\n          this.selectedNode.field_id !== '000000'\n            ? this.selectedNode.field_id\n            : '000000',\n        lastModiDate: '当前数据库时间',\n        fieldName: '',\n        fieldLength: '',\n        systemNo: this.selectedNode.system_no // 新增参数：系统号\n      }\n      this.config = config(this, {\n        disableConfig: {\n          parent_field: true,\n          field_no: this.selectedNode.field_id !== '000000',\n          else: false\n        }\n      })\n    },\n    /**\n     * 按钮：修改\n     */\n    handleModify() {\n      const node = this.$refs.tree.getCurrentNode()\n      if (node.field_type === '00') {\n        this.$message({\n          message: '无法修改根节点，请重新选择',\n          type: 'warning'\n        })\n        return\n      }\n      if (!node) {\n        // commonMsgWarn('请选择删除的节点', that)\n        this.$message({\n          message: '请选择修改的节点',\n          type: 'warning'\n        })\n        return\n      }\n      this.notSave = false\n      this.oper_type = 'modify'\n      this.defaultForm = {\n        ...this.defaultForm,\n        systemNo: this.selectedNode.system_no // 修改参数：系统号 防止系统号数据丢失\n      }\n      this.preDefaultForm = this.defaultForm\n      this.config = config(this, {\n        disableConfig: {\n          parent_field: true,\n          field_no: true,\n          else: false\n        }\n      })\n    },\n    /**\n     * 按钮：删除\n     */\n    handleDelete() {\n      const that = this\n      const node = this.$refs.tree.getCurrentNode()\n      if (!node) {\n        // commonMsgWarn('请选择删除的节点', that)\n        this.$message({\n          message: '请选择删除的节点',\n          type: 'info'\n        })\n        return\n      }\n      const fieldId = node.field_id\n      const parentField = node.parent_field\n      const systemNo = node.system_no\n      function del(flag) {\n        if (!flag) {\n          return\n        }\n        const msg = {\n          parameterList: [\n            {\n              fieldId,\n              parentField,\n              systemNo\n            }\n          ],\n          oper_type: 'delete'\n        }\n        deleteDict(msg)\n          .then((res) => {\n            if (res.retCode === '200') {\n              commonMsgSuccess(res.retMsg, that)\n              that.$refs.tree.remove(node)\n              // 清空选中节点\n              that.selectedNode = ''\n              const msg1 = {\n                fieldTime: '',\n                operType: '1', // 门户操作标识\n                systemNo: systemNo\n              }\n              fieldController(msg1).then((res) => {\n                if (systemNo === 'UNIFY') {\n                  localStorage.setItem(\n                    systemNo,\n                    JSON.stringify(res.retMap[systemNo])\n                  )\n                  localStorage.setItem(\n                    'UNIFYdictionary',\n                    JSON.stringify({ UNIFY: res.retMap.dictionary[systemNo] })\n                  )\n                } else {\n                  localStorage.setItem(\n                    systemNo,\n                    JSON.stringify(res.retMap[systemNo])\n                  )\n                  localStorage.setItem(\n                    systemNo + 'dictionary',\n                    JSON.stringify({ [systemNo]: res.retMap.dictionary[systemNo] })\n                  )\n                }\n                localStorage.setItem('fieldTime', res.retMap.fieldTime) // 存储数据字典获取时间\n                // 同步至vueX\n                that.$store.commit('common/ADD_DICTIONARYLET', {\n                  systemNo: systemNo,\n                  value: res.retMap[systemNo],\n                  dictionary: { [systemNo]: res.retMap.dictionary[systemNo] }\n                })\n              })\n              that.defaultForm = {\n                fieldNo: '',\n                fieldType: '',\n                fieldDesc: null,\n                fieldMode: 'varchar',\n                fieldId: '',\n                fieldSize: 0,\n                parentField: '000000',\n                lastModiDate: '',\n                fieldName: '',\n                fieldLength: 0\n              }\n              that.$refs['refFormNode'].resetForm()\n            }\n          })\n          .catch((err) => {\n            if (err.message.indexOf('hasChildren') !== -1) {\n              that.$message({\n                message: '有子节点, 不能删除！',\n                type: 'info'\n              })\n            }\n          })\n      }\n      commonMsgConfirm('是否确认删除？', this, del)\n    },\n    /**\n     * 按钮：保存表单校验\n     */\n    handleSave() {\n      this.$refs['refFormNode'].validateForm()\n    },\n    /**\n     * 按钮：新增修改保存请求onSave\n     */\n    onSave() {\n      const msg = {\n        parameterList: [\n          {\n            ...this.defaultForm,\n            fieldSize: this.defaultForm.fieldSize + '',\n            fieldLength: this.defaultForm.fieldLength + ''\n          }\n        ],\n        oper_type: this.oper_type === 'insert' ? 'insert' : 'update',\n        oldField: {\n          id: this.selectedNode.field_id,\n          name: this.selectedNode.name,\n          field_desc: this.selectedNode.field_desc,\n          field_no: this.selectedNode.field_no,\n          field_name: this.selectedNode.field_name,\n          field_length: this.selectedNode.field_length,\n          field_size: this.selectedNode.field_size,\n          field_mode: this.selectedNode.field_mode,\n          field_type: this.selectedNode.field_type,\n          system_no: this.preDefaultForm.system_no, // 用this.preDefaultForm 的system_no，防止修改后system_no丢失\n          last_modi_date: this.selectedNode.last_modi_date\n        }\n      }\n      if (this.oper_type === 'insert') {\n        // 新增节点保存\n        this.notSave = true\n        addDict(msg).then(async(res) => {\n          // commonMsgSuccess(res.retMsg, this)\n          const nodeData = {\n            name: this.defaultForm.fieldName,\n            field_no: this.defaultForm.fieldNo,\n            field_type: this.defaultForm.fieldType,\n            field_desc: this.defaultForm.fieldDesc,\n            field_mode: this.defaultForm.fieldMode,\n            field_size: this.defaultForm.fieldSize,\n            parent_field: this.defaultForm.parentField,\n            last_modi_date: this.defaultForm.lastModiDate,\n            field_name: this.defaultForm.fieldName,\n            field_length: this.defaultForm.fieldLength,\n            is_parent: '0',\n            field_id: res.retMap.field_id,\n            system_no: this.defaultForm.systemNo,\n            field_id_copy: res.retMap.field_id + this.defaultForm.systemNo\n          }\n          // this.selectedNode2.doCreateChildren([nodeData])\n          this.expandedkeys.push(this.defaultForm.parentField)\n          // 动态添加节点\n\n          if (this.selectedNode2.isLeaf) {\n            this.selectedNode2.isLeaf = false\n          }\n\n          console.log('selectedNode2:', this.selectedNode2)\n          this.$refs.tree.append(nodeData, this.selectedNode2)\n          this.$nextTick(() => {\n            this.selectedNode2.expanded = true\n            // this.$refs.tree.getCurrentNode()\n            timeout2 = setTimeout(() => {\n              // 设置选中状态\n              this.$refs.tree.setCurrentKey(nodeData.field_id_copy)\n              // 获取选中节点\n              const node = this.$refs.tree.getNode(nodeData.field_id_copy)\n              // 触发节点点击方法\n              this.nodeClick(node.data, node)\n\n              const systemNo = this.defaultForm.systemNo // 修改的字典的系统号\n              const msg1 = {\n                fieldTime: '',\n                operType: '1', // 门户操作标识\n                systemNo: systemNo\n              }\n              fieldController(msg1).then((res) => {\n                if (systemNo === 'UNIFY') {\n                  localStorage.setItem(\n                    systemNo,\n                    JSON.stringify(res.retMap[systemNo])\n                  )\n                  localStorage.setItem(\n                    'UNIFYdictionary',\n                    JSON.stringify({ UNIFY: res.retMap.dictionary[systemNo] })\n                  )\n                } else {\n                  localStorage.setItem(\n                    systemNo,\n                    JSON.stringify(res.retMap[systemNo])\n                  )\n                  localStorage.setItem(\n                    systemNo + 'dictionary',\n                    JSON.stringify({ [systemNo]: res.retMap.dictionary[systemNo] })\n                  )\n                }\n                localStorage.setItem('fieldTime', res.retMap.fieldTime) // 存储数据字典获取时间\n                // 同步至vueX\n                this.$store.commit('common/ADD_DICTIONARYLET', {\n                  systemNo: systemNo,\n                  value: res.retMap[systemNo],\n                  dictionary: { [systemNo]: res.retMap.dictionary[systemNo] }\n                })\n                commonMsgSuccess('数据字典同步操作成功！', this)\n              })\n            }, 300)\n          })\n        })\n      } else {\n        // 修改节点保存\n        modifyDict(msg).then((res) => {\n          // commonMsgSuccess(res.retMsg, this)\n          const nodeeditData = {\n            name: this.defaultForm.fieldName,\n            field_no: this.defaultForm.fieldNo,\n            field_type: this.defaultForm.fieldType,\n            field_desc: this.defaultForm.fieldDesc,\n            field_mode: this.defaultForm.fieldMode,\n            field_size: this.defaultForm.fieldSize,\n            parent_field: this.defaultForm.parentField,\n            last_modi_date: res.retMap.last_modi_date,\n            field_name: this.defaultForm.fieldName,\n            field_length: this.defaultForm.fieldLength,\n            field_id: this.selectedNode.field_id,\n            system_no: this.preDefaultForm.systemNo,\n            field_id_copy:\n              this.selectedNode.field_id + this.defaultForm.systemNo\n          }\n          // 更新节点数据\n          this.selectedNode2.setData(nodeeditData) // setData方法调用后，自己的子节点辉丢失\n          // 对该节点下子节点进行更新\n          this.selectedNode2.loaded = false\n          this.selectedNode2.expand() // 主动调用展开节点方法，重新查询该节点下的所有子节点\n          this.notSave = true\n          this.$nextTick(() => {\n            timeout2 = setTimeout(async() => {\n              // 触发节点点击方法\n              this.nodeClick(this.selectedNode2.data, this.selectedNode2)\n              const systemNo = this.defaultForm.systemNo // 修改的字典的系统号\n              const msg1 = {\n                fieldTime: '',\n                operType: '1', // 门户操作标识\n                systemNo: systemNo\n              }\n              fieldController(msg1).then((res) => {\n                if (systemNo === 'UNIFY') {\n                  localStorage.setItem(\n                    systemNo,\n                    JSON.stringify(res.retMap[systemNo])\n                  )\n                  localStorage.setItem(\n                    'UNIFYdictionary',\n                    JSON.stringify({ UNIFY: res.retMap.dictionary[systemNo] })\n                  )\n                } else {\n                  localStorage.setItem(\n                    systemNo,\n                    JSON.stringify(res.retMap[systemNo])\n                  )\n                  localStorage.setItem(\n                    systemNo + 'dictionary',\n                    JSON.stringify({ [systemNo]: res.retMap.dictionary[systemNo] })\n                  )\n                }\n                localStorage.setItem('fieldTime', res.retMap.fieldTime) // 存储数据字典获取时间\n                // 同步至vueX\n                this.$store.commit('common/ADD_DICTIONARYLET', {\n                  systemNo: systemNo,\n                  value: res.retMap[systemNo],\n                  dictionary: { [systemNo]: res.retMap.dictionary[systemNo] }\n                })\n                commonMsgSuccess('数据字典同步操作成功！', this)\n              })\n            }, 300)\n          })\n        })\n      }\n    },\n    /**\n     * 按钮：取消\n     */\n    handleCancle() {\n      this.defaultForm = this.preDefaultForm\n      this.config = config(this, {\n        disableConfig: {\n          parent_field: true,\n          field_no: true,\n          else: true\n        }\n      })\n      this.notSave = true\n    },\n    /**\n     * 表单校验\n     * @param {Boolean}valid 校验返回值*/\n    validateForm(value) {\n      if (value) {\n        this.onSave()\n      } else {\n        return false\n      }\n    },\n    /**\n     * 获取数据字典\n     */\n    getDictionaryData(systemNo) {\n      return new Promise((resolve) => {\n        const msg = {\n          parameterList: [''],\n          fieldTime: '',\n          operType: '1', // 门户操作标识\n          systemNo: systemNo\n        }\n        fieldController(msg).then((res) => {\n          // 新增外系统字典\n          localStorage.setItem('fieldTime', res.retMap.fieldTime) // 存储数据字典获取时间\n          // 字典请求时间为空时\n          localStorage.setItem(systemNo, JSON.stringify(res.retMap[systemNo]))\n          localStorage.setItem(\n            systemNo + 'dictionary',\n            JSON.stringify(res.retMap.dictionary)\n          )\n          this.$store.commit('common/ADD_DICTIONARYLET', {\n            systemNo,\n            value: res.retMap[systemNo],\n            dictionary: res.retMap.dictionary\n          })\n          resolve()\n        })\n      })\n    }\n  }\n}\n</script>\n\n<style lang=\"scss\" scoped>\n@import '@/assets/scss/main.scss';\n.dictionary {\n  .dictionary-box {\n    background-color: #fff;\n    padding: 30px;\n    height: 100%;\n    border-radius: $pageRadius;\n    display: flex;\n    justify-content: flex-start;\n    .title {\n      font-size: $titleFont;\n      // padding: 2rem 0;\n      padding: 2rem 0 1rem;\n      font-weight: bold;\n    }\n    & > div {\n      height: 100%;\n    }\n    .dictionary-left {\n      overflow: auto;\n      width: 300px;\n      margin-right: 30px;\n      border-right: 1px solid #f1f1f1;\n    }\n    .dictionary-right {\n      display: flex;\n      flex-direction: column;\n      width: calc(100% - #{330px});\n      .searchRes {\n        color: #767e91;\n        font-size: 12px;\n        span {\n          margin-right: 16rem;\n        }\n        .el-button--mini {\n          padding: 0.5rem;\n        }\n      }\n      .dictConfig {\n        overflow: auto;\n      }\n      .button-line {\n        display: flex;\n        padding-top: 2rem;\n        justify-content: center;\n      }\n    }\n  }\n}\n::v-deep .el-tree--highlight-current {\n  .el-tree-node.is-current {\n    > .el-tree-node__content {\n      background-color: #f1f1f1;\n    }\n  }\n}\n</style>\n"]}]}