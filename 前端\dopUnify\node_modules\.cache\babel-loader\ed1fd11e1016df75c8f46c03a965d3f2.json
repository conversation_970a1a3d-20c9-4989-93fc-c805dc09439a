{"remainingRequest": "E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-q<PERSON><PERSON>n\\数字运营平台-统一门户工程\\dopUnify\\node_modules\\babel-loader\\lib\\index.js!E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan\\数字运营平台-统一门户工程\\dopUnify\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan\\数字运营平台-统一门户工程\\dopUnify\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan\\数字运营平台-统一门户工程\\dopUnify\\src\\views\\system\\externalManage\\user\\component\\table\\index.vue?vue&type=script&lang=js&", "dependencies": [{"path": "E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-<PERSON><PERSON><PERSON>n\\数字运营平台-统一门户工程\\dopUnify\\src\\views\\system\\externalManage\\user\\component\\table\\index.vue", "mtime": 1686019809091}, {"path": "E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-<PERSON><PERSON><PERSON>n\\数字运营平台-统一门户工程\\dopUnify\\babel.config.js", "mtime": 1686019818359}, {"path": "E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-<PERSON><PERSON><PERSON>n\\数字运营平台-统一门户工程\\dopUnify\\.babelrc", "mtime": 1705285078668}, {"path": "E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-<PERSON><PERSON><PERSON>n\\数字运营平台-统一门户工程\\dopUnify\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-<PERSON><PERSON><PERSON>n\\数字运营平台-统一门户工程\\dopUnify\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-<PERSON><PERSON><PERSON>n\\数字运营平台-统一门户工程\\dopUnify\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-<PERSON><PERSON><PERSON>n\\数字运营平台-统一门户工程\\dopUnify\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1655715099000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, null]}