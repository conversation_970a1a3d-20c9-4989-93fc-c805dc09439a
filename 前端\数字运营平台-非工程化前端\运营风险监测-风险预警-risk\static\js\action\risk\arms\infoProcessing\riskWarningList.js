/**
 * 风险预警信息查询页面
 */
var risk_warning_list = new Vue({
    el: "#risk-warning-list",//页面整体 ID
    data: {
        'form':{
            'form_id': 'risk-warning-fm',//表单ID
            'form_class':'ars-form',//表单class
            'form_data': [
                {
                    'type':'select',
                    'name': '预警模型',
                    'selected':'',
                    'id':'risk-warning-model',
                    'blank':true,
                    'data':[],
                    'alias':'model_id',
                    'method':'getRelatingModelId()',
                },{
                    'type':'input_group',
                    'name': '交易机构',
                    'id':'risk-warning-organNo',
                    'btn_id':'risk-warning-choice-btn',
                    'btn_name':'选择',
                    'method':'organTree(1)',
                    'value':'',
                    'alias':'site_no'
                },{
                    'type':'input',
                    'input':'date_two',
                    'date':'datepicker',
                    'name': '交易日期',
                    'id':'risk-warning-start-date',
                    'two_id':'risk-warning-end-date',
                    'method':'startVal()',//值发生改变时触发
                    'method2':'endVal()',//值发生改变时触发
                    'value1':commonOneMonthAgo(),
                    'value2':commonYesterday(),
                    'placeholder1':'开始日期',
                    'placeholder2':'结束日期',
                    'alias':'date'
                }
            ],//表单配置
            'buttons': [//按钮配置
                {'type': 'button','name': '查询','icon': 'search', 'id':'risk-warning-query-btn','class': 'ars-btn btn-primary','method': 'query()'}, //查询按钮
                {'type': 'button','name': '批量通过','icon': 'check', 'id':'risk-warning-batch-pass-btn','class': 'ars-btn btn-info','method': 'batchPass()'}, //批量通过按钮
                // {'type': 'button','name': '获取随机任务','icon': 'random', 'id':'risk-warning-random-task-btn','class': 'ars-btn btn-warning','method': 'getRandomTask()'} //获取随机任务按钮
            ],
        },
        'table': {
            'table_id': 'risk-warning-table', //当前页表格ID
            'page_id': 'risk-warning-page', //表格页码ID
            'table_column': [
                {
                    name : 'num',
                    label : '序号',
                    align : 'center',
                    width : 10,
                    finalWidth : true,
                    render : function(value) {
                        return risk_warning_list.count++;
                    }
                },
                {
                    name : 'MODEL_NAME',
                    label : '风险预警模型名称',
                    align : 'left',
                    width : 60,
                    finalWidth : true,
                    render : function(value,row) {
                        return value;
                    }
                },
                {
                    name : 'NOT_DEALED_COUNT',
                    label : '未处理(已退回)',
                    align : 'center',
                    width : 30,
                    finalWidth : true,
                    render : function(value,row) {
                        var thisModelId = row.MODEL_ID;
                        var backCount = row.BACKCOUNT || 0; // 处理undefined情况
                        if(value == 0)
                            return "0(" + backCount + ")";
                        risk_warning_list.variable.SUM_NODEAL += parseInt(value) || 0;
                        return "<a class='color-red' onclick='risk_warning_list.exhibitData(\""+thisModelId+"\",\"0\")'>"+value+"(" + backCount + ")</a>";
                    }
                },{
                    name : 'HAVE_DEALED_COUNT',
                    label : '已处理',
                    align : 'center',
                    width : 30,
                    finalWidth : true,
                    render : function(value,row) {
                        var thisModelId = row.MODEL_ID;
                        if(value == 0)
                            return 0;
                        risk_warning_list.variable.SUM_DEALED += parseInt(value) || 0;
                        return "<a class='color-red' onclick='risk_warning_list.exhibitData(\""+thisModelId+"\",\"1|2\")'>"+value+"</a>";
                    }
                },
                {
                    name : 'SUPERVISE_PASS_COUNT',
                    label : '通过',
                    align : 'center',
                    width : 30,
                    finalWidth : true,
                    render : function(value,row) {
                        var thisModelId = row.MODEL_ID;
                        if(value == 0)
                            return 0;
                        risk_warning_list.variable.SUM_PASS += parseInt(value) || 0;
                        return "<a class='color-red' onclick='risk_warning_list.exhibitData(\""+thisModelId+"\",\"1\")'>"+value+"</a>";
                    }
                },
                {
                    name : 'SUPERVISE_SLIP_COUNT',
                    label : '下发差错',
                    align : 'center',
                    width : 30,
                    finalWidth : true,
                    render : function(value,row) {
                        var thisModelId = row.MODEL_ID;
                        if(value == 0)
                            return 0;
                        risk_warning_list.variable.SUM_SLIP += parseInt(value) || 0;
                        return "<a class='color-red' onclick='risk_warning_list.exhibitData(\""+thisModelId+"\",\"2\")'>"+value+"</a>";
                    }
                },
                {
                    name : 'TOTAL',
                    label : '总计',
                    align : 'center',
                    width : 30,
                    finalWidth : true,
                    render : function(value,row) {
                        var nodeal = parseInt(row.NOT_DEALED_COUNT) || 0;
                        var dealed = parseInt(row.HAVE_DEALED_COUNT) || 0;
                        var total = nodeal + dealed;
                        risk_warning_list.variable.SUM_TOTAL += total;
                        return total;
                    }
                },
                /*{
                    name : 'ACTION',
                    label : '操作',
                    align : 'center',
                    width : 30,
                    finalWidth : true,
                    render : function(value,row) {
                        return "<button class='btn btn-xs btn-blue' onclick='risk_warning_list.getTask("+row.MODEL_ID+")'>获取指定模型任务</button>";
                    }
                }*/
            ], //表格字段名
            'table_data': [], //表格数据
            'index': null //当前操作行
        },
        'count':1,
        'variable': { //变量配置
            'SUM_NODEAL':0,//未处理
            'SUM_DEALED':0,//已处理
            'SUM_PASS':0,//通过
            'SUM_SLIP':0,//差错
            'SUM_TOTAL':0,//总计
            'field':[],//当页查询字段保存
            'isInit':true,//判断是初始化表格还是重置
            'relatingModelId':{}//关联模型ID
        }
    },
    methods: {
        /**
         * 将字符串转换为函数
         * @param item:函数字符串*/
        callFn:function(item) {
            var reg1 = /^\w+/g;
            var reg2 = /\(((.|)+?)\)/; //取小括号中的参数
            var fn = item.match(reg1)[0];
            var args = item.match(reg2)[1];
            if(commonBlank(args)){ //函数无参数
                this[fn].apply(this); //调用函数
            }else { //函数有参数
                this[fn].apply(this,args.split(',')); //调用函数
            }
        }, //callFn
        /**
         * date: 开始日期发生改变时执行*/
        startVal:function(){
            if(this.form.form_data[2].value1 > this.form.form_data[2].value2){
                this.form.form_data[2].value2=this.form.form_data[2].value1;
            }
        },//startVal
        /**
         * date: 结束日期发生改变时执行*/
        endVal:function(){
            if(this.form.form_data[2].value2 < this.form.form_data[2].value1){
                this.form.form_data[2].value1=this.form.form_data[2].value2;
            }
        },//endVal
        /**
         * init table: 表格数据初始化*/
        initTable:function (){//tableId为表格id,columnsData为列表头，dataList为加载数据
            commonInitDatagrid(this.table.table_id,{//表格初始化
                dialogFilterW:10,//当表格宽度小于设定值时，将表头的快速筛选更换为dialog模式。当值设为0时，表头将固定为dialog模式
                showCheckboxcol:false,// 是否显示行复选框
                columns:this.table.table_column,
                data:risk_warning_list.table.table_data
            });
            this.variable.isInit = false;
        },//initTable
        /**
         * init: 初始化预警模型下拉列表
         */
        initModelList: function() {
            var modelList = [];
            modelList.push({value:'',name:'全部'});

            var msg = {
                "parameterList" : [{}],
                "sysMap" : {
                    "oper_type" :"getAllModelInfos"
                }
            };
            var modelInfos = commonGet(commonConst("SUNDA_RISK")+"/armsExhibitDataController/getRiskWarningModelList.do", $.toJSON(msg)).retMap.modelList;

            // 将完整的模型信息存储到arms_main中，供详情页使用
            arms_main.variable.modelInfoList = modelInfos;

            if (!commonBlank(modelInfos)) {
                for(var i=0; i<modelInfos.length; i++){
                    if(modelInfos[i].is_relate_model == 0){ // 只展示风险预警模型
                        modelList.push({
                            value:modelInfos[i].model_id,
                            name:modelInfos[i].model_name
                        });

                        // 存储关联模型ID
                        if(modelInfos[i].RELATING_MODEL_ID){
                            this.variable.relatingModelId[modelInfos[i].model_id] = modelInfos[i].RELATING_MODEL_ID;
                        }
                    }
                }
                this.form.form_data[0].data = modelList;
            }
        },//initModelList
        /**
         * 获取关联模型id
         */
        getRelatingModelId: function(){
            return this.variable.relatingModelId[this.form.form_data[0].selected] || 0;
        },
        /**
         * btn: 查询
         */
        query: function () {
            $('#'+this.form.form_id).isValid(function () {
                // 重置计数
                risk_warning_list.variable.SUM_NODEAL = 0;
                risk_warning_list.variable.SUM_DEALED = 0;
                risk_warning_list.variable.SUM_PASS = 0;
                risk_warning_list.variable.SUM_SLIP = 0;
                risk_warning_list.variable.SUM_TOTAL = 0;
                risk_warning_list.count = 1;

                risk_warning_list.variable.field = [{
                    "model_id": '',
                    "site_no": '',
                    "start_date": '',
                    "end_date": ''
                }];

                // 获取表单值
                risk_warning_list.form.form_data.forEach(function (item) {
                    if(item.alias == 'date'){
                        risk_warning_list.variable.field[0]['start_date'] = item.value1.replace(new RegExp('-',"g"),'');
                        risk_warning_list.variable.field[0]['end_date'] = item.value2.replace(new RegExp('-',"g"),'');
                    } else if(item.type == 'select'){
                        risk_warning_list.variable.field[0][item.alias] = item.selected;
                    } else if(item.type == 'input_group'){
                        risk_warning_list.variable.field[0][item.alias] = $('#'+item.id).val();
                    }
                });

                var msg = {
                    "parameterList": risk_warning_list.variable.field,
                    "sysMap": {}
                };

                var response = commonGet(commonConst("SUNDA_RISK")+"/armsExhibitDataController/getRiskWarningStatistics.do", $.toJSON(msg));
                if(response.retCode == commonConst('HANDLE_SUCCESS')){
                    // 处理返回数据，为缺失字段设置默认值
                    var riskWarningStats = response.retMap.riskWarningStats || [];

                    // 处理每行数据
                    for(var i=0; i<riskWarningStats.length; i++){
                        // 如果没有BACKCOUNT字段，设置为0
                        if(riskWarningStats[i].BACKCOUNT === undefined){
                            riskWarningStats[i].BACKCOUNT = 0;
                        }

                        // 确保字段都有数值
                        riskWarningStats[i].NOT_DEALED_COUNT = parseInt(riskWarningStats[i].NOT_DEALED_COUNT) || 0;
                        riskWarningStats[i].HAVE_DEALED_COUNT = parseInt(riskWarningStats[i].HAVE_DEALED_COUNT) || 0;
                        riskWarningStats[i].SUPERVISE_PASS_COUNT = parseInt(riskWarningStats[i].SUPERVISE_PASS_COUNT) || 0;
                        riskWarningStats[i].SUPERVISE_SLIP_COUNT = parseInt(riskWarningStats[i].SUPERVISE_SLIP_COUNT) || 0;

                        // 确保MODEL_NAME字段有值
                        if(!riskWarningStats[i].MODEL_NAME && riskWarningStats[i].MODEL_ID) {
                            // 从模型列表中查找对应的模型名称
                            for(var j=0; j<risk_warning_list.form.form_data[0].data.length; j++) {
                                if(risk_warning_list.form.form_data[0].data[j].value == riskWarningStats[i].MODEL_ID) {
                                    riskWarningStats[i].MODEL_NAME = risk_warning_list.form.form_data[0].data[j].name;
                                    break;
                                }
                            }
                        }
                    }

                    risk_warning_list.table.table_data = riskWarningStats;

                    if (risk_warning_list.variable.isInit){
                        risk_warning_list.initTable();
                        $('#'+risk_warning_list.table.table_id+' tbody').append(
                            "<tr><td colspan='2' align='center'><strong>总计</strong></td>" +
                            "<td align='center'>"+risk_warning_list.variable.SUM_NODEAL+"</td>" +
                            "<td align='center'>"+risk_warning_list.variable.SUM_DEALED+"</td>" +
                            "<td align='center'>"+risk_warning_list.variable.SUM_PASS+"</td>" +
                            "<td align='center'>"+risk_warning_list.variable.SUM_SLIP+"</td>" +
                            "<td align='center'>"+risk_warning_list.variable.SUM_TOTAL+"</td></tr>"
                        );
                    } else {
                        $('#'+risk_warning_list.table.table_id).datagrid('reload', {data:$.toJSON(risk_warning_list.table.table_data)});
                        $('#'+risk_warning_list.table.table_id+' tbody').append(
                            "<tr><td colspan='2' align='center'><strong>总计</strong></td>" +
                            "<td align='center'>"+risk_warning_list.variable.SUM_NODEAL+"</td>" +
                            "<td align='center'>"+risk_warning_list.variable.SUM_DEALED+"</td>" +
                            "<td align='center'>"+risk_warning_list.variable.SUM_PASS+"</td>" +
                            "<td align='center'>"+risk_warning_list.variable.SUM_SLIP+"</td>" +
                            "<td align='center'>"+risk_warning_list.variable.SUM_TOTAL+"</td></tr>"
                        );
                    }
                } else {
                    commonError(response.retMsg);
                }
            });
        },//query
        /**
         * 获取随机任务
         */
        getRandomTask: function(){
            var modelId = this.form.form_data[0].selected;
            if(!modelId){
                commonError("请先选择预警模型");
                return;
            }

            var msg = {
                "parameterList": [{
                    "model_id": modelId
                }],
                "sysMap": {}
            };

            var response = commonGet(commonConst("SUNDA_RISK")+"/armsTaskLockController/getRandomTask.do", $.toJSON(msg));
            if(response.retCode == commonConst('HANDLE_SUCCESS')){
                if(response.retMap.result === '0'){
                    commonError("未找到符合条件的任务");
                    return;
                }
                var taskInfo = response.retMap.taskInfo.split(",");
                this.navigateToDetail(taskInfo[0], "0", taskInfo[1]);
            } else {
                commonError(response.retMsg);
            }
        },//getRandomTask
        /**
         * 获取指定模型任务
         */
        getTask: function(modelId){
            var msg = {
                "parameterList": [{
                    "model_id": modelId
                }],
                "sysMap": {}
            };

            var response = commonGet(commonConst("SUNDA_RISK")+"/armsTaskLockController/getRandomTask.do", $.toJSON(msg));
            if(response.retCode == commonConst('HANDLE_SUCCESS')){
                if(response.retMap.result === '0'){
                    commonError("未找到符合条件的任务");
                    return;
                }
                var taskInfo = response.retMap.taskInfo.split(",");
                this.navigateToDetail(taskInfo[0], "0", taskInfo[1]);
            } else {
                commonError(response.retMsg);
            }
        },//getTask
        /**
         * 批量通过
         */
        batchPass: function(){
            var modelId = this.form.form_data[0].selected;
            var siteNo = $('#'+this.form.form_data[1].id).val();

            if(!modelId){
                commonError("请先选择预警模型");
                return;
            }

            if(!siteNo){
                commonError("请先选择交易机构");
                return;
            }

            var startDate = this.form.form_data[2].value1.replace(new RegExp('-',"g"),'');
            var endDate = this.form.form_data[2].value2.replace(new RegExp('-',"g"),'');

            // 获取表名和模型信息
            var tableName = '';
            var modelInfo = null;
            if (arms_main.variable.modelInfoList && arms_main.variable.modelInfoList.length > 0) {
                for (var i = 0; i < arms_main.variable.modelInfoList.length; i++) {
                    if (arms_main.variable.modelInfoList[i].model_id == modelId) {
                        tableName = arms_main.variable.modelInfoList[i].table_name || '';
                        // 构建模型信息对象
                        modelInfo = {
                            modelId: modelId,
                            modelName: arms_main.variable.modelInfoList[i].model_name || '',
                            modelType: arms_main.variable.modelInfoList[i].model_type || '1',
                            tableName: tableName
                        };
                        break;
                    }
                }
            }

            if(!modelInfo || !tableName){
                commonError("无法获取模型信息，请刷新页面后重试");
                return;
            }

            // 先获取预警明细数据，从中提取modelrow_ids
            var detailParams = {
                "model_id": modelId,
                "ishandle": "0", // 只获取未处理的数据
                "is_history": "0", // 非历史数据
                "site_no": siteNo,
                "start_date": startDate,
                "end_date": endDate,
                "modelInfo": modelInfo // 添加模型信息
            };

            var detailMsg = {
                "parameterList": [detailParams],
                "sysMap": {}
            };

            // 获取预警明细数据
            var detailResponse = commonGet(commonConst("SUNDA_RISK")+"/armsExhibitDataController/getRiskWarningDetailData.do", $.toJSON(detailMsg));
            if(detailResponse.retCode != commonConst('HANDLE_SUCCESS')){
                commonError("获取预警明细数据失败：" + detailResponse.retMsg);
                return;
            }

            // 从明细数据中提取modelrow_ids
            var detailData = detailResponse.retMap.detailData || [];
            if(detailData.length === 0){
                commonError("没有符合条件的未处理预警数据");
                return;
            }

            // 提取所有的modelrow_ids
            var modelrow_ids = [];
            for(var i=0; i<detailData.length; i++){
                if(detailData[i].MODELROW_ID){
                    modelrow_ids.push(detailData[i].MODELROW_ID);
                }
            }

            if(modelrow_ids.length === 0){
                commonError("没有有效的预警数据行ID");
                return;
            }

            // 确认弹窗
            BJUI.alertmsg('confirm', '确定要批量通过选择的模型和交易机构的预警信息吗?共 ' + modelrow_ids.length + ' 条数据', {
                okCall: function() {
                    var msg = {
                        "parameterList": [{
                            "model_id": modelId,
                            "modelrow_ids": modelrow_ids,
                            "site_no": siteNo,
                            "start_date": startDate,
                            "end_date": endDate
                        }],
                        "sysMap": {
                            "table_name": tableName
                        }
                    };

                    var response = commonAjax(commonConst("SUNDA_RISK")+"/armsExhibitDataController/batchPassRiskWarning.do", $.toJSON(msg));
                    if(response.retCode == commonConst('HANDLE_SUCCESS')){
                        BJUI.alertmsg('ok', '批量通过成功!');
                        risk_warning_list.query(); // 刷新数据
                    } else {
                        commonError(response.retMsg);
                    }
                }
            });
        },//batchPass
        /**
         * 展示数据
         */
        exhibitData: function(modelId, ishandle){
            // 存储查询条件，用于返回时恢复
            arms_main.variable.riskWarningQueryParams = this.variable.field;

            // 设置展示参数
            arms_main.variable.show_modelId = modelId;
            arms_main.variable.base_arms_ishandle = ishandle;

            // 跳转到数据展示页面
            BJUI.ajax('doload', {
                url: 'static/html/risk/arms/infoProcessing/riskWarningDetail.html',
                target: arms_main.main_nav.target
            });
        },//exhibitData
        /**
         * 跳转到详情页
         */
        navigateToDetail: function(modelId, ishandle, modelRowId){
            // 存储查询条件，用于返回时恢复
            arms_main.variable.riskWarningQueryParams = this.variable.field;

            // 设置展示参数
            arms_main.variable.show_modelId = modelId;
            arms_main.variable.base_arms_ishandle = ishandle;
            arms_main.variable.model_row_id = modelRowId;

            // 跳转到数据展示页面
            BJUI.ajax('doload', {
                url: 'static/html/risk/arms/riskWarningDetail.html',
                target: arms_main.main_nav.target
            });
        },//navigateToDetail
        /**
         * tree：单击 "机构号" '选择' 机构树调用
         * @param para:当前输入区域在form.form_data中的索引
         */
        organTree:function(para){
            organTree($('#'+this.form.form_data[parseInt(para)].id)[0], true, false); // true 为单选，false 为显示所有级别
        }//organTree
    },
    mounted: function () {//页面元素加载完成后执行
        this.$nextTick(function () {//整个视图都已渲染完毕
            try {
                mainCont.subPageSize($(this.$options.el));// 重置页面布局
                this.initModelList(); // 初始化预警模型列表

                // 如果有上一次查询参数，恢复查询条件
                if(arms_main.variable.riskWarningQueryParams && arms_main.variable.riskWarningQueryParams.length > 0){
                    var params = arms_main.variable.riskWarningQueryParams[0];
                    this.form.form_data[0].selected = params.model_id;
                    $('#'+this.form.form_data[1].id).val(params.site_no);

                    // 兼容新旧两种参数名称
                    var startDate = params.start_date || params.armsStartDate || '';
                    var endDate = params.end_date || params.armsEndDate || '';

                    this.form.form_data[2].value1 = commonFormatDate(startDate);
                    this.form.form_data[2].value2 = commonFormatDate(endDate);
                    this.query(); // 执行查询
                }

                // 默认加载最近一个月的数据
                //this.query();
            } catch (err) {
                commonError(err.name + '：' + err.message, '前台执行异常');
            }
        });
    }
});

/**
 * 格式化日期：YYYYMMDD -> YYYY-MM-DD
 */
function commonFormatDate(dateStr) {
    if(!dateStr || dateStr.length != 8) return '';
    return dateStr.substring(0,4) + '-' + dateStr.substring(4,6) + '-' + dateStr.substring(6,8);
}

/**
 * 获取一个月前的日期（YYYY-MM-DD格式）
 */
function commonOneMonthAgo() {
    var date = new Date();
    date.setDate(date.getDate() - 1); // 先减去1天（昨天）
    date.setMonth(date.getMonth() - 1); // 再减去1个月
    var y = date.getFullYear();
    var m = date.getMonth() + 1;
    var d = date.getDate();
    return y + '-' + (m < 10 ? '0' + m : m) + '-' + (d < 10 ? '0' + d : d);
}
