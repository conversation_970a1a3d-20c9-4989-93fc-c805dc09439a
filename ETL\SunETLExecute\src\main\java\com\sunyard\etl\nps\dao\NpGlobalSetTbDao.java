package com.sunyard.etl.nps.dao;

import java.sql.SQLException;

import org.sunyard.util.dbutil.DBHandler;

import com.sun.rowset.CachedRowSetImpl;
import com.sunyard.etl.system.common.Constants;
import com.xxl.job.core.log.XxlJobLogger;


public class NpGlobalSetTbDao {
	
	/*
	 * 通过KEY_NAME获取KEY_VALUE
	 */
	public String getKeyValueByKeyName(String KeyName) throws SQLException{
		DBHandler dbHandler = new DBHandler(Constants.CONN_POOL_NAME);
		CachedRowSetImpl rs = null;
		String KeyValue = "";
		String sql = "SELECT * FROM NP_GLOBAL_SET_TB T WHERE T.KEY_NAME = ?";
		XxlJobLogger.log("获取服务ID:", sql);
		rs = dbHandler.queryRs(sql, KeyName);
		while(rs.next()){
			KeyValue = rs.getString("KEY_VALUE");
		}
		return KeyValue;
	}
}
