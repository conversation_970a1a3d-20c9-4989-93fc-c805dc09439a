{"remainingRequest": "D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\node_modules\\thread-loader\\dist\\cjs.js!D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\node_modules\\babel-loader\\lib\\index.js!D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\src\\views\\system\\externalManage\\post\\component\\table\\index.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\src\\views\\system\\externalManage\\post\\component\\table\\index.vue", "mtime": 1703583638555}, {"path": "D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\babel.config.js", "mtime": 1686019818359}, {"path": "D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\node_modules\\thread-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1655715099000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, null]}