{"remainingRequest": "E:\\华南区项目\\中国银行\\开发区\\科技公司运营平台\\前端\\dopUnify\\node_modules\\thread-loader\\dist\\cjs.js!E:\\华南区项目\\中国银行\\开发区\\科技公司运营平台\\前端\\dopUnify\\node_modules\\babel-loader\\lib\\index.js!E:\\华南区项目\\中国银行\\开发区\\科技公司运营平台\\前端\\dopUnify\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!E:\\华南区项目\\中国银行\\开发区\\科技公司运营平台\\前端\\dopUnify\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\华南区项目\\中国银行\\开发区\\科技公司运营平台\\前端\\dopUnify\\src\\views\\system\\config\\encryp\\component\\table\\index.vue?vue&type=script&lang=js&", "dependencies": [{"path": "E:\\华南区项目\\中国银行\\开发区\\科技公司运营平台\\前端\\dopUnify\\src\\views\\system\\config\\encryp\\component\\table\\index.vue", "mtime": 1721631954662}, {"path": "E:\\华南区项目\\中国银行\\开发区\\科技公司运营平台\\前端\\dopUnify\\babel.config.js", "mtime": 1721631954662}, {"path": "E:\\华南区项目\\中国银行\\开发区\\科技公司运营平台\\前端\\dopUnify\\.babelrc", "mtime": 1721631954662}, {"path": "E:\\华南区项目\\中国银行\\开发区\\科技公司运营平台\\前端\\dopUnify\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "E:\\华南区项目\\中国银行\\开发区\\科技公司运营平台\\前端\\dopUnify\\node_modules\\thread-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "E:\\华南区项目\\中国银行\\开发区\\科技公司运营平台\\前端\\dopUnify\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "E:\\华南区项目\\中国银行\\开发区\\科技公司运营平台\\前端\\dopUnify\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "E:\\华南区项目\\中国银行\\开发区\\科技公司运营平台\\前端\\dopUnify\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1655715099000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, null]}