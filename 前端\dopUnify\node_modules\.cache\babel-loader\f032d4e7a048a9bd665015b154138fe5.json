{"remainingRequest": "E:\\华南区项目\\中国银行\\开发区\\科技公司运营平台\\前端\\dopUnify\\node_modules\\babel-loader\\lib\\index.js!E:\\华南区项目\\中国银行\\开发区\\科技公司运营平台\\前端\\dopUnify\\node_modules\\eslint-loader\\index.js??ref--13-0!E:\\华南区项目\\中国银行\\开发区\\科技公司运营平台\\前端\\dopUnify\\src\\store\\index.js", "dependencies": [{"path": "E:\\华南区项目\\中国银行\\开发区\\科技公司运营平台\\前端\\dopUnify\\src\\store\\index.js", "mtime": 1721631954662}, {"path": "E:\\华南区项目\\中国银行\\开发区\\科技公司运营平台\\前端\\dopUnify\\babel.config.js", "mtime": 1721631954662}, {"path": "E:\\华南区项目\\中国银行\\开发区\\科技公司运营平台\\前端\\dopUnify\\.babelrc", "mtime": 1721631954662}, {"path": "E:\\华南区项目\\中国银行\\开发区\\科技公司运营平台\\前端\\dopUnify\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "E:\\华南区项目\\中国银行\\开发区\\科技公司运营平台\\前端\\dopUnify\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "E:\\华南区项目\\中国银行\\开发区\\科技公司运营平台\\前端\\dopUnify\\node_modules\\eslint-loader\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:aW1wb3J0ICJjb3JlLWpzL21vZHVsZXMvZXMuYXJyYXkucmVkdWNlLmpzIjsKaW1wb3J0ICJjb3JlLWpzL21vZHVsZXMvZXMub2JqZWN0LnRvLXN0cmluZy5qcyI7CmltcG9ydCAiY29yZS1qcy9tb2R1bGVzL3dlYi5kb20tY29sbGVjdGlvbnMuaXRlcmF0b3IuanMiOwppbXBvcnQgImNvcmUtanMvbW9kdWxlcy9lcy5yZWdleHAuZXhlYy5qcyI7CmltcG9ydCAiY29yZS1qcy9tb2R1bGVzL2VzLnN0cmluZy5yZXBsYWNlLmpzIjsKaW1wb3J0IFZ1ZSBmcm9tICd2dWUnOwppbXBvcnQgVnVleCBmcm9tICd2dWV4JzsKaW1wb3J0IGdldHRlcnMgZnJvbSAnLi9nZXR0ZXJzJzsgLy8g55So5LqO5bCGc3RhdGXkuK3nmoTmlbDmja7ov5vooYzliqDlt6UKClZ1ZS51c2UoVnVleCk7Ci8vIOS8mumBjeWOhuaWh+S7tuWkueS4reeahOaMh+WumuaWh+S7tiznhLblkI7oh6rliqjlr7zlhaUs5L2/5b6X5LiN6ZyA6KaB5q+P5qyh5pi+5byP55qE6LCD55SoaW1wb3J05a+85YWl5qih5Z2XCnZhciBtb2R1bGVzRmlsZXMgPSByZXF1aXJlLmNvbnRleHQoJy4vbW9kdWxlcycsIHRydWUsIC9cLmpzJC8pOwoKLy8g5L2g5LiN6ZyA6KaBJyBpbXBvcnQgYXBwIGZyb20gJy4vbW9kdWxlcy9hcHAnCi8vIOWug+WwhuiHquWKqOimgeaxguaJgOaciXZ1ZXjmqKHlnZfku47mqKHlnZfmlofku7blvJXlhaUKdmFyIG1vZHVsZXMgPSBtb2R1bGVzRmlsZXMua2V5cygpLnJlZHVjZShmdW5jdGlvbiAobW9kdWxlcywgbW9kdWxlUGF0aCkgewogIC8vIHNldCAnLi9hcHAuanMnID0+ICdhcHAnCiAgdmFyIG1vZHVsZU5hbWUgPSBtb2R1bGVQYXRoLnJlcGxhY2UoL15cLlwvKC4qKVwuXHcrJC8sICckMScpOwogIHZhciB2YWx1ZSA9IG1vZHVsZXNGaWxlcyhtb2R1bGVQYXRoKTsKICBtb2R1bGVzW21vZHVsZU5hbWVdID0gdmFsdWUuZGVmYXVsdDsKICByZXR1cm4gbW9kdWxlczsKfSwge30pOwp2YXIgc3RvcmUgPSBuZXcgVnVleC5TdG9yZSh7CiAgbW9kdWxlczogbW9kdWxlcywKICAvLyDmqKHlnZfnrqHnkIYKICBnZXR0ZXJzOiBnZXR0ZXJzCn0pOwpleHBvcnQgZGVmYXVsdCBzdG9yZTs="}, {"version": 3, "names": ["<PERSON><PERSON>", "Vuex", "getters", "use", "modulesFiles", "require", "context", "modules", "keys", "reduce", "modulePath", "moduleName", "replace", "value", "default", "store", "Store"], "sources": ["E:/华南区项目/中国银行/开发区/科技公司运营平台/前端/dopUnify/src/store/index.js"], "sourcesContent": ["import Vue from 'vue'\nimport Vuex from 'vuex'\nimport getters from './getters' // 用于将state中的数据进行加工\n\nVue.use(Vuex)\n// 会遍历文件夹中的指定文件,然后自动导入,使得不需要每次显式的调用import导入模块\nconst modulesFiles = require.context('./modules', true, /\\.js$/)\n\n// 你不需要' import app from './modules/app'\n// 它将自动要求所有vuex模块从模块文件引入\nconst modules = modulesFiles.keys().reduce((modules, modulePath) => {\n  // set './app.js' => 'app'\n  const moduleName = modulePath.replace(/^\\.\\/(.*)\\.\\w+$/, '$1')\n  const value = modulesFiles(modulePath)\n  modules[moduleName] = value.default\n  return modules\n}, {})\n\nconst store = new Vuex.Store({\n  modules, // 模块管理\n  getters\n})\n\nexport default store\n"], "mappings": ";;;;;AAAA,OAAOA,GAAG,MAAM,KAAK;AACrB,OAAOC,IAAI,MAAM,MAAM;AACvB,OAAOC,OAAO,MAAM,WAAW,EAAC;;AAEhCF,GAAG,CAACG,GAAG,CAACF,IAAI,CAAC;AACb;AACA,IAAMG,YAAY,GAAGC,OAAO,CAACC,OAAO,CAAC,WAAW,EAAE,IAAI,EAAE,OAAO,CAAC;;AAEhE;AACA;AACA,IAAMC,OAAO,GAAGH,YAAY,CAACI,IAAI,EAAE,CAACC,MAAM,CAAC,UAACF,OAAO,EAAEG,UAAU,EAAK;EAClE;EACA,IAAMC,UAAU,GAAGD,UAAU,CAACE,OAAO,CAAC,iBAAiB,EAAE,IAAI,CAAC;EAC9D,IAAMC,KAAK,GAAGT,YAAY,CAACM,UAAU,CAAC;EACtCH,OAAO,CAACI,UAAU,CAAC,GAAGE,KAAK,CAACC,OAAO;EACnC,OAAOP,OAAO;AAChB,CAAC,EAAE,CAAC,CAAC,CAAC;AAEN,IAAMQ,KAAK,GAAG,IAAId,IAAI,CAACe,KAAK,CAAC;EAC3BT,OAAO,EAAPA,OAAO;EAAE;EACTL,OAAO,EAAPA;AACF,CAAC,CAAC;AAEF,eAAea,KAAK"}]}