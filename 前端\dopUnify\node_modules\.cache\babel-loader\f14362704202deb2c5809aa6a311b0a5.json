{"remainingRequest": "E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1\\数字运营平台-统一门户工程\\dopUnify\\node_modules\\thread-loader\\dist\\cjs.js!E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1\\数字运营平台-统一门户工程\\dopUnify\\node_modules\\babel-loader\\lib\\index.js!E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1\\数字运营平台-统一门户工程\\dopUnify\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1\\数字运营平台-统一门户工程\\dopUnify\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1\\数字运营平台-统一门户工程\\dopUnify\\src\\views\\system\\config\\menu\\index.vue?vue&type=script&lang=js&", "dependencies": [{"path": "E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1\\数字运营平台-统一门户工程\\dopUnify\\src\\views\\system\\config\\menu\\index.vue", "mtime": 1716875177281}, {"path": "E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1\\数字运营平台-统一门户工程\\dopUnify\\babel.config.js", "mtime": 1716875191548}, {"path": "E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1\\数字运营平台-统一门户工程\\dopUnify\\.babelrc", "mtime": 1716875191502}, {"path": "E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1\\数字运营平台-统一门户工程\\dopUnify\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1\\数字运营平台-统一门户工程\\dopUnify\\node_modules\\thread-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1\\数字运营平台-统一门户工程\\dopUnify\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1\\数字运营平台-统一门户工程\\dopUnify\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1\\数字运营平台-统一门户工程\\dopUnify\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1655715099000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, null]}