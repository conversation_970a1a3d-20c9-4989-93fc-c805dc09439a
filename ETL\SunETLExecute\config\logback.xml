<?xml version="1.0" encoding="UTF-8"?>
<configuration debug="false" scan="true" scanPeriod="1 seconds">

	<contextName>logback</contextName>
	<property name="log.path" value="D:/SunETL/SunETLExecute/sunetl-executor-example.log" />
	<property name="special.log.path" value="D:/ErrorLog/error.log" />

	<!-- 定义一个特殊的 Appender，用于输出标记的日志内容到文件 -->
	<appender name="taskMonitorAppender" class="ch.qos.logback.core.rolling.RollingFileAppender">
		<file>${special.log.path}</file>
		<rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
			<fileNamePattern>${special.log.path}.%d{yyyy-MM-dd}.zip</fileNamePattern>
		</rollingPolicy>
		<encoder>
			<pattern>%date %level [%thread] %logger{36} [%file : %line] %msg%n</pattern>
		</encoder>
	</appender>

	<logger name="taskMonitorLogger">
		<appender-ref ref="taskMonitorAppender" />
	</logger>

	<!-- 现有的 console Appender -->
	<appender name="console" class="ch.qos.logback.core.ConsoleAppender">
		<encoder>
			<pattern>%d{HH:mm:ss.SSS} %contextName [%thread] %-5level %logger{36} - %msg%n</pattern>
		</encoder>
	</appender>

	<!-- 现有的 file Appender -->
	<appender name="file" class="ch.qos.logback.core.rolling.RollingFileAppender">
		<file>${log.path}</file>
		<rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
			<fileNamePattern>${log.path}.%d{yyyy-MM-dd}.zip</fileNamePattern>
		</rollingPolicy>
		<encoder>
			<pattern>%date %level [%thread] %logger{36} [%file : %line] %msg%n</pattern>
		</encoder>
	</appender>

	<!-- 根日志配置 -->
	<root level="info">
		<appender-ref ref="console" />
		<appender-ref ref="file" />
		<!--<appender-ref ref="specialFile" />-->
	</root>

</configuration>
