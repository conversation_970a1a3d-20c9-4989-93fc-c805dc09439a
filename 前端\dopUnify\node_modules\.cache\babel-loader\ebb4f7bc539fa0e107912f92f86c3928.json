{"remainingRequest": "D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\node_modules\\babel-loader\\lib\\index.js!D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\node_modules\\eslint-loader\\index.js??ref--13-0!D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\src\\api\\views\\home\\component\\shortcutMenu.js", "dependencies": [{"path": "D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\src\\api\\views\\home\\component\\shortcutMenu.js", "mtime": 1686019810888}, {"path": "D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\babel.config.js", "mtime": 1686019818359}, {"path": "D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\.babelrc", "mtime": 1705285078668}, {"path": "D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\node_modules\\eslint-loader\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:aW1wb3J0IHJlcXVlc3QgZnJvbSAnQC91dGlscy9yZXF1ZXN0JzsKaW1wb3J0IGRlZmF1bHRTZXR0aW5ncyBmcm9tICdAL3NldHRpbmdzJzsKdmFyIHByZWZpeCA9IGRlZmF1bHRTZXR0aW5ncy5zZXJ2aWNlLnN5c3RlbTsgLy8g5YmN57yA5YWs5YWx6Lev55SxCgpleHBvcnQgdmFyIHNob3J0Y3V0TGlzdCA9IHsKICAvKioNCiAgICog5Li76aG15p+l6K+iDQogICAqLwogIC8vIOW/q+aNt+iPnOWNleafpeivogogIHF1ZXJ5UG9ydGFsOiBmdW5jdGlvbiBxdWVyeVBvcnRhbChkYXRhKSB7CiAgICByZXR1cm4gcmVxdWVzdCh7CiAgICAgIHVybDogcHJlZml4ICsgJy9wb3J0YWxNZW51L3F1ZXJ5LmRvJywKICAgICAgbWV0aG9kOiAnZ2V0JywKICAgICAgcGFyYW1zOiB7CiAgICAgICAgbWVzc2FnZTogZGF0YQogICAgICB9CiAgICB9KTsKICB9LAogIC8vIOW/q+aNt+iPnOWNleS/ruaUuQogIG1vZGlmeVBvcnRhbDogZnVuY3Rpb24gbW9kaWZ5UG9ydGFsKGRhdGEpIHsKICAgIHJldHVybiByZXF1ZXN0KHsKICAgICAgdXJsOiBwcmVmaXggKyAnL3BvcnRhbE1lbnUvYWRkLmRvJywKICAgICAgbWV0aG9kOiAncG9zdCcsCiAgICAgIGRhdGE6IGRhdGEKICAgIH0pOwogIH0KfTs="}, {"version": 3, "names": ["request", "defaultSettings", "prefix", "service", "system", "shortcutList", "queryPortal", "data", "url", "method", "params", "message", "modifyPortal"], "sources": ["D:/1_Project/XYD_Project/dop-4.0/dop-4.1-qian<PERSON>n-unify/dopUnify/src/api/views/home/<USER>/shortcutMenu.js"], "sourcesContent": ["import request from '@/utils/request'\r\nimport defaultSettings from '@/settings'\r\nconst prefix = defaultSettings.service.system // 前缀公共路由\r\n\r\nexport const shortcutList = {\r\n  /**\r\n   * 主页查询\r\n   */\r\n  // 快捷菜单查询\r\n  queryPortal(data) {\r\n    return request({\r\n      url: prefix + '/portalMenu/query.do',\r\n      method: 'get',\r\n      params: { message: data }\r\n    })\r\n  },\r\n  // 快捷菜单修改\r\n  modifyPortal(data) {\r\n    return request({\r\n      url: prefix + '/portalMenu/add.do',\r\n      method: 'post',\r\n      data\r\n    })\r\n  }\r\n}\r\n"], "mappings": "AAAA,OAAOA,OAAO,MAAM,iBAAiB;AACrC,OAAOC,eAAe,MAAM,YAAY;AACxC,IAAMC,MAAM,GAAGD,eAAe,CAACE,OAAO,CAACC,MAAM,EAAC;;AAE9C,OAAO,IAAMC,YAAY,GAAG;EAC1B;AACF;AACA;EACE;EACAC,WAAW,uBAACC,IAAI,EAAE;IAChB,OAAOP,OAAO,CAAC;MACbQ,GAAG,EAAEN,MAAM,GAAG,sBAAsB;MACpCO,MAAM,EAAE,KAAK;MACbC,MAAM,EAAE;QAAEC,OAAO,EAAEJ;MAAK;IAC1B,CAAC,CAAC;EACJ,CAAC;EACD;EACAK,YAAY,wBAACL,IAAI,EAAE;IACjB,OAAOP,OAAO,CAAC;MACbQ,GAAG,EAAEN,MAAM,GAAG,oBAAoB;MAClCO,MAAM,EAAE,MAAM;MACdF,IAAI,EAAJA;IACF,CAAC,CAAC;EACJ;AACF,CAAC"}]}