{"remainingRequest": "D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\node_modules\\babel-loader\\lib\\index.js!D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\node_modules\\sunui\\srcobf\\components\\Dialog\\SunFormTransferDialog\\index.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\node_modules\\sunui\\srcobf\\components\\Dialog\\SunFormTransferDialog\\index.vue", "mtime": 499162500000}, {"path": "D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\babel.config.js", "mtime": 1686019818359}, {"path": "D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1655715099000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA4DA;AACA;AACA;EACAA;EACAC;IAAAC;EAAA;EACAC;IAAAC;EAAA;EACAC;EACAC;IACAC;MACAC;MACAC;QACA;UACAC;UAAA;UACAC;UAAA;UACAC;YACA;YACAC;YAAA;YACAC;YAAA;YACAC;UACA;;UACAC;YACA;YACAC;YAAA;YACAC;YAAA;YACAC;UACA;QACA;MACA;IACA;;IACAC;MACAZ;MACAC;IACA;EACA;EACAY;IAAA;IACA;MACA;MACA;QACAA;UACAC;UACAC;UACAC;QACA;MACA;MACA;IACA;IACA;MACAC;MACAC;MAAA;MACAC;MAAA;MACAN;MACAO,WACA;QACAL;QACAM,WACA;UACAN;UACAM,WACA;YACAN;UACA;QAEA;MAEA,GACA;QACAA;QACAM,WACA;UACAN;UACAM,WACA;YACAN;UACA;QAEA,GACA;UACAA;UACAM,WACA;YACAN;UACA;QAEA;MAEA,GACA;QACAA;QACAM,WACA;UACAN;UACAM,WACA;YACAN;UACA;QAEA,GACA;UACAA;UACAM,WACA;YACAN;UACA;QAEA;MAEA,EACA;MACAO;MACAC;MACAC;QACA;UAAA,SACA;QAAA,IACAC;MAGA;IACA;EACA;EACAC;IACA;MACA;MACAC;QAAA;QACA;UACA;YACA;UACA;QACA;UACA;QACA;MACA;MACAC;IACA;EACA;EACAC;IACAC;EACA;EACAC;IACAD;EACA;EACAE;IACA;IACAC,iDACA;IACA;AACA;AACA;IACAC;MAAA;MACA;MACA;QACA;MACA;IACA;IACA;AACA;IACAC;MACA;IACA;IACA;AACA;AACA;IACAC;MACA;QACA;QACA;MACA;QACA;MACA;IACA;IACA;IACAC;MAAA;MACA;QACA;QACA;QACA;QACA;QACA;UACA;UACA;UACA;UACA;UACA;UACA;QACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA,mEACA;UACA;UACA;UACA;UACA;UACA;QACA;MACA;IACA;EACA;AACA", "names": ["name", "components", "SunForm", "directives", "elDragDialog", "inheritAttrs", "props", "dialogConfig", "type", "default", "customButton", "visible", "componentProps", "title", "width", "destroyOnClose", "form", "config", "defaultForm", "labelWidth", "nowFunction", "data", "key", "label", "disabled", "btn", "windowHeight", "dialogHeight", "treeData", "children", "value", "value4", "renderFunc", "option", "watch", "handler", "deep", "mounted", "window", "<PERSON><PERSON><PERSON><PERSON>", "methods", "handleNodeClick", "dialogClose", "dialogSubmit", "validateForm", "getHeight"], "sourceRoot": "node_modules/sunui/srcobf/components/Dialog/SunFormTransferDialog", "sources": ["index.vue"], "sourcesContent": ["<!--\n* 弹出框：单个表单\n-->\n<template>\n  <el-dialog\n    ref=\"refDialog\"\n    v-el-drag-dialog\n    :visible.sync=\"dialogConfig.visible\"\n    :before-close=\"dialogClose\"\n    :close-on-click-modal=\"false\"\n    :append-to-body=\"true\"\n    :destroy-on-close=\"true\"\n    v-bind=\"dialogConfig.componentProps\"\n  >\n    <sun-form\n      ref=\"refFormDialog\"\n      :query=\"btn\"\n      :reset=\"btn\"\n      v-bind=\"dialogConfig.form\"\n      @validateForm=\"validateForm\"\n    >\n      <!-- <template slot=\"header\">\n      </template> -->\n    </sun-form>\n    <div style=\"box\">\n      <div style=\"float: left\">\n        <el-tree :data=\"treeData\" @node-click=\"handleNodeClick\" />\n      </div>\n      <!-- 穿梭框---begin -->\n      <div style=\"text-align: right\">\n        <el-transfer\n          v-model=\"value\"\n          style=\"text-align: left; display: inline-block\"\n          filterable\n          :left-default-checked=\"[2, 3]\"\n          :right-default-checked=\"[1]\"\n          :render-content=\"renderFunc\"\n          :titles=\"['Source', 'Target']\"\n          :format=\"{\n            noChecked: '${total}',\n            hasChecked: '${checked}/${total}'\n          }\"\n          :data=\"data\"\n        />\n      </div>\n      <!-- 穿梭框-----end -->\n    </div>\n    <div slot=\"footer\" class=\"dialog-footer\">\n      <div class=\"footerRightBtn\">\n        <slot name=\"rightBtn\" />\n      </div>\n      <div v-if=\"!dialogConfig.customButton\">\n        <el-button @click=\"dialogClose\">取 消</el-button>\n        <el-button type=\"primary\" @click=\"dialogSubmit\">确 定</el-button>\n      </div>\n    </div>\n  </el-dialog>\n</template>\n\n<script>\nimport elDragDialog from '../../../directive/el-drag-dialog' // 弹出框可拖动\nimport SunForm from '../../SunForm' // 表单\nexport default {\n  name: 'SunFormTransferDialog',\n  components: { SunForm },\n  directives: { elDragDialog },\n  inheritAttrs: false,\n  props: {\n    dialogConfig: {\n      type: Object,\n      default: () => {\n        return {\n          customButton: false, // 是否需要默认的弹窗按钮\n          visible: false, // 显示隐藏配置\n          componentProps: {\n            // 弹出框属性\n            title: '表单弹出框', // 弹出框标题\n            width: '', // 当前弹出框宽度 默认80%\n            destroyOnClose: true // 关闭时销毁 Dialog 中的元素\n          },\n          form: {\n            // 表单属性\n            config: {}, // 表单项配置\n            defaultForm: {}, // 默认值配置\n            labelWidth: '10rem' // 当前表单标签宽度配置\n          }\n        }\n      }\n    },\n    nowFunction: {\n      type: String,\n      default: 'dialogSubmit'\n    }\n  },\n  data() {\n    const generateData = (_) => {\n      const data = []\n      for (let i = 1; i <= 15; i++) {\n        data.push({\n          key: i,\n          label: `备选项 ${i}`,\n          disabled: i % 4 === 0\n        })\n      }\n      return data\n    }\n    return {\n      btn: false,\n      windowHeight: '', // 浏览器的高度\n      dialogHeight: '', // 弹窗的高度\n      data: generateData(),\n      treeData: [\n        {\n          label: '一级 1',\n          children: [\n            {\n              label: '二级 1-1',\n              children: [\n                {\n                  label: '三级 1-1-1'\n                }\n              ]\n            }\n          ]\n        },\n        {\n          label: '一级 2',\n          children: [\n            {\n              label: '二级 2-1',\n              children: [\n                {\n                  label: '三级 2-1-1'\n                }\n              ]\n            },\n            {\n              label: '二级 2-2',\n              children: [\n                {\n                  label: '三级 2-2-1'\n                }\n              ]\n            }\n          ]\n        },\n        {\n          label: '一级 3',\n          children: [\n            {\n              label: '二级 3-1',\n              children: [\n                {\n                  label: '三级 3-1-1'\n                }\n              ]\n            },\n            {\n              label: '二级 3-2',\n              children: [\n                {\n                  label: '三级 3-2-1'\n                }\n              ]\n            }\n          ]\n        }\n      ],\n      value: [1],\n      value4: [1],\n      renderFunc(h, option) {\n        return (\n          <div style='border-bottom:1px solid rgb(230, 233, 239);width:12rem'>\n            {option.key} - {option.label}\n          </div>\n        )\n      }\n    }\n  },\n  watch: {\n    'dialogConfig.visible': {\n      // 当前选中组件改变，重新获取当前表单组件中所有的 字段标识 name\n      handler(val) {\n        if (val === false) {\n          this.$nextTick(() => {\n            this.$refs['refFormDialog'].resetForm()\n          })\n        } else {\n          this.getHeight()\n        }\n      },\n      deep: true\n    }\n  },\n  mounted() {\n    window.addEventListener('resize', this.getHeight)\n  },\n  beforeDestroy() {\n    window.removeEventListener('resize', this.getHeight)\n  },\n  methods: {\n    // 树节点单击事件\n    handleNodeClick(data) {\n    },\n    /**\n     * 弹出框：关闭\n     */\n    dialogClose() {\n      this.$refs['refFormDialog'].resetForm()\n      this.$nextTick(() => {\n        this.$emit('dialogClose', false)\n      })\n    },\n    /**\n     * 确定*/\n    dialogSubmit() {\n      this.$refs['refFormDialog'].validateForm()\n    },\n    /**\n     * 表单校验\n     * @param {Boolean}valid 校验返回值*/\n    validateForm(valid) {\n      if (valid) {\n        this.$emit('dialogSubmit', this.dialogConfig.form.defaultForm)\n        // this.dialogClose()\n      } else {\n        return false\n      }\n    },\n    // 获取浏览器窗口高度与弹窗高度\n    getHeight() {\n      this.$nextTick(() => {\n        this.windowHeight = window.innerHeight\n        this.dialogHeight = this.$refs.refDialog.$refs.dialog.offsetHeight\n        // 判断二者之间大小关系，做出相应操作\n        // 当浏览器窗口>弹窗高度\n        if (this.windowHeight > this.dialogHeight) {\n          const dialogTop = this.windowHeight - this.dialogHeight\n          // 设置弹窗上外边距\n          // this.$refs.refDialog.$refs.dialog.style.marginTop =\n          //   dialogTop / 2 + 'px'\n          const marginTop = dialogTop / 2 + 'px'\n          this.$refs.refDialog.$refs.dialog.style.margin = `${marginTop} auto 0`\n        } else if (this.windowHeight < this.dialogHeight) {\n          // 当浏览器窗口<弹窗高度\n          // 弹窗总高度\n          this.$refs.refDialog.$refs.dialog.style.height = '83%'\n          // 获取更改后的总高度\n          const dialogHeight = this.$refs.refDialog.$refs.dialog.offsetHeight\n          // 弹窗body区域百分比高度\n          this.$refs.refDialog.$refs.dialog.childNodes[1].style.height = '83%'\n          this.$refs.refDialog.$refs.dialog.childNodes[1].style.overflow =\n            'auto'\n          // 设置弹窗上外边距\n          // this.$refs.refDialog.$refs.dialog.style.marginTop =\n          //   (this.windowHeight - dialogHeight) / 2 + 'px'\n          const marginTop = (this.windowHeight - dialogHeight) / 2 + 'px'\n          this.$refs.refDialog.$refs.dialog.style.margin = `${marginTop} auto 0`\n        }\n      })\n    }\n  }\n}\n</script>\n\n<style lang=\"scss\" scoped>\n.box {\n  display: flex;\n}\n.dialog-footer {\n  display: flex;\n  justify-content: flex-end;\n  .footerRightBtn {\n    margin-right: 10px;\n    .rightBtn {\n      margin: 0rem 20rem 0rem 2rem;\n      position: absolute;\n      right: 2rem;\n    }\n  }\n}\n</style>\n"]}]}