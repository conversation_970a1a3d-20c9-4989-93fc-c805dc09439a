{"remainingRequest": "D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\node_modules\\babel-loader\\lib\\index.js!D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\node_modules\\eslint-loader\\index.js??ref--13-0!D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\src\\views\\system\\config\\menu\\component\\dialog\\info.js", "dependencies": [{"path": "D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\src\\views\\system\\config\\menu\\component\\dialog\\info.js", "mtime": 1686019807826}, {"path": "D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\babel.config.js", "mtime": 1686019818359}, {"path": "D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\.babelrc", "mtime": 1705285078668}, {"path": "D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\node_modules\\eslint-loader\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["v1", "uuidv1", "btnDatas", "getDataValue", "configTable", "that", "name", "label", "id", "config", "btn_type", "component", "colSpan", "componentProps", "placeholder", "methods", "change", "value", "dialogFormType", "options", "text", "btn_sys", "hidden", "rules", "required", "message", "filterable", "clearable", "listAdd", "btn_id", "min", "max", "btn_name", "btn_url"], "sources": ["D:/1_Project/XYD_Project/dop-4.0/dop-4.1-qian<PERSON>n-unify/dopUnify/src/views/system/config/menu/component/dialog/info.js"], "sourcesContent": ["import { v1 as uuidv1 } from 'uuid'\nimport { btnDatas, getDataValue } from '@/utils/dictionary' // 字典配置\n// 表头\nexport const configTable = (that) => [\n  {\n    name: 'buttonId',\n    label: '按钮标识',\n    id: uuidv1()\n  },\n  {\n    name: 'buttonName',\n    label: '按钮名称',\n    id: uuidv1()\n  },\n  {\n    name: 'reqUrl',\n    label: '按钮请求接口',\n    id: uuidv1()\n  }\n]\n\n// 表单\nexport const config = (that) => ({\n  btn_type: {\n    component: 'radio',\n    label: '按钮配置类型',\n    colSpan: 20,\n    name: 'btn_type',\n    componentProps: {\n      // 属性名\n      placeholder: '请选择'\n    },\n    methods: {\n      // 方法名\n      change: (value) => {\n        if (value === '0') {\n          that.dialogFormType(false)\n        } else {\n          that.dialogFormType(true)\n        }\n      }\n    },\n    options: [\n      {\n        label: '0',\n        text: '选择按钮'\n      },\n      {\n        label: '1',\n        text: '自定义按钮'\n      }\n    ]\n  },\n  btn_sys: {\n    component: 'select',\n    label: '按钮选择',\n    hidden: false,\n    colSpan: 20,\n    name: 'btn_sys',\n    config: {\n      // form-item 配置\n      rules: [{ required: true, message: '按钮为必选' }]\n    },\n    componentProps: {\n      // 属性名\n      placeholder: '请选择',\n      filterable: true,\n      clearable: true\n    },\n    methods: {\n      // 方法名\n      change: (value) => {\n        that.listAdd = getDataValue(btnDatas, value)\n      }\n    },\n    options: btnDatas\n  },\n  btn_id: {\n    component: 'input',\n    label: '按钮id',\n    hidden: true,\n    colSpan: 20,\n    name: 'btn_id',\n    config: {\n      // form-item 配置\n      rules: [\n        { required: true, message: '按钮id为必输' },\n        { min: 0, max: 60, message: '请最多填写60个字符' }\n      ]\n    },\n    componentProps: {\n      // 属性名\n      placeholder: '请输入',\n      clearable: true\n    }\n  },\n  btn_name: {\n    component: 'input',\n    label: '按钮名称',\n    hidden: true,\n    colSpan: 20,\n    name: 'btn_name',\n    config: {\n      // form-item 配置\n      rules: [\n        { required: true, message: '按钮名称为必输' },\n        { min: 0, max: 30, message: '请最多填写30个字符' }\n      ]\n    },\n    componentProps: {\n      // 属性名\n      placeholder: '请输入',\n      clearable: true\n    }\n  },\n  btn_url: {\n    component: 'input',\n    label: '按钮请求',\n    // hidden: false,\n    colSpan: 20,\n    name: 'btn_url',\n    config: {\n      // form-item 配置\n      // rules: [\n      //    { required: true, message: '按钮名称为必输' }\n      // ]\n    },\n    componentProps: {\n      // 属性名\n      placeholder: '请输入',\n      clearable: true\n    }\n  }\n})\n"], "mappings": "AAAA,SAASA,EAAE,IAAIC,MAAM,QAAQ,MAAM;AACnC,SAASC,QAAQ,EAAEC,YAAY,QAAQ,oBAAoB,EAAC;AAC5D;AACA,OAAO,IAAMC,WAAW,GAAG,SAAdA,WAAW,CAAIC,IAAI;EAAA,OAAK,CACnC;IACEC,IAAI,EAAE,UAAU;IAChBC,KAAK,EAAE,MAAM;IACbC,EAAE,EAAEP,MAAM;EACZ,CAAC,EACD;IACEK,IAAI,EAAE,YAAY;IAClBC,KAAK,EAAE,MAAM;IACbC,EAAE,EAAEP,MAAM;EACZ,CAAC,EACD;IACEK,IAAI,EAAE,QAAQ;IACdC,KAAK,EAAE,QAAQ;IACfC,EAAE,EAAEP,MAAM;EACZ,CAAC,CACF;AAAA;;AAED;AACA,OAAO,IAAMQ,MAAM,GAAG,SAATA,MAAM,CAAIJ,IAAI;EAAA,OAAM;IAC/BK,QAAQ,EAAE;MACRC,SAAS,EAAE,OAAO;MAClBJ,KAAK,EAAE,QAAQ;MACfK,OAAO,EAAE,EAAE;MACXN,IAAI,EAAE,UAAU;MAChBO,cAAc,EAAE;QACd;QACAC,WAAW,EAAE;MACf,CAAC;MACDC,OAAO,EAAE;QACP;QACAC,MAAM,EAAE,gBAACC,KAAK,EAAK;UACjB,IAAIA,KAAK,KAAK,GAAG,EAAE;YACjBZ,IAAI,CAACa,cAAc,CAAC,KAAK,CAAC;UAC5B,CAAC,MAAM;YACLb,IAAI,CAACa,cAAc,CAAC,IAAI,CAAC;UAC3B;QACF;MACF,CAAC;MACDC,OAAO,EAAE,CACP;QACEZ,KAAK,EAAE,GAAG;QACVa,IAAI,EAAE;MACR,CAAC,EACD;QACEb,KAAK,EAAE,GAAG;QACVa,IAAI,EAAE;MACR,CAAC;IAEL,CAAC;IACDC,OAAO,EAAE;MACPV,SAAS,EAAE,QAAQ;MACnBJ,KAAK,EAAE,MAAM;MACbe,MAAM,EAAE,KAAK;MACbV,OAAO,EAAE,EAAE;MACXN,IAAI,EAAE,SAAS;MACfG,MAAM,EAAE;QACN;QACAc,KAAK,EAAE,CAAC;UAAEC,QAAQ,EAAE,IAAI;UAAEC,OAAO,EAAE;QAAQ,CAAC;MAC9C,CAAC;MACDZ,cAAc,EAAE;QACd;QACAC,WAAW,EAAE,KAAK;QAClBY,UAAU,EAAE,IAAI;QAChBC,SAAS,EAAE;MACb,CAAC;MACDZ,OAAO,EAAE;QACP;QACAC,MAAM,EAAE,gBAACC,KAAK,EAAK;UACjBZ,IAAI,CAACuB,OAAO,GAAGzB,YAAY,CAACD,QAAQ,EAAEe,KAAK,CAAC;QAC9C;MACF,CAAC;MACDE,OAAO,EAAEjB;IACX,CAAC;IACD2B,MAAM,EAAE;MACNlB,SAAS,EAAE,OAAO;MAClBJ,KAAK,EAAE,MAAM;MACbe,MAAM,EAAE,IAAI;MACZV,OAAO,EAAE,EAAE;MACXN,IAAI,EAAE,QAAQ;MACdG,MAAM,EAAE;QACN;QACAc,KAAK,EAAE,CACL;UAAEC,QAAQ,EAAE,IAAI;UAAEC,OAAO,EAAE;QAAU,CAAC,EACtC;UAAEK,GAAG,EAAE,CAAC;UAAEC,GAAG,EAAE,EAAE;UAAEN,OAAO,EAAE;QAAa,CAAC;MAE9C,CAAC;MACDZ,cAAc,EAAE;QACd;QACAC,WAAW,EAAE,KAAK;QAClBa,SAAS,EAAE;MACb;IACF,CAAC;IACDK,QAAQ,EAAE;MACRrB,SAAS,EAAE,OAAO;MAClBJ,KAAK,EAAE,MAAM;MACbe,MAAM,EAAE,IAAI;MACZV,OAAO,EAAE,EAAE;MACXN,IAAI,EAAE,UAAU;MAChBG,MAAM,EAAE;QACN;QACAc,KAAK,EAAE,CACL;UAAEC,QAAQ,EAAE,IAAI;UAAEC,OAAO,EAAE;QAAU,CAAC,EACtC;UAAEK,GAAG,EAAE,CAAC;UAAEC,GAAG,EAAE,EAAE;UAAEN,OAAO,EAAE;QAAa,CAAC;MAE9C,CAAC;MACDZ,cAAc,EAAE;QACd;QACAC,WAAW,EAAE,KAAK;QAClBa,SAAS,EAAE;MACb;IACF,CAAC;IACDM,OAAO,EAAE;MACPtB,SAAS,EAAE,OAAO;MAClBJ,KAAK,EAAE,MAAM;MACb;MACAK,OAAO,EAAE,EAAE;MACXN,IAAI,EAAE,SAAS;MACfG,MAAM,EAAE;QACN;QACA;QACA;QACA;MAAA,CACD;MACDI,cAAc,EAAE;QACd;QACAC,WAAW,EAAE,KAAK;QAClBa,SAAS,EAAE;MACb;IACF;EACF,CAAC;AAAA,CAAC"}]}