package com.sunyard.etl.custom.handler;

import java.io.BufferedReader;
import java.io.File;
import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.InputStreamReader;
import java.io.OutputStreamWriter;
import java.io.PrintWriter;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.List;
import com.sunyard.etl.system.common.Constants;
import org.springframework.stereotype.Service;

import com.sunyard.etl.system.dao.DataDateDAO;
import com.sunyard.etl.system.dao.impl.DataDateDAOImpl;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.enums.JobStartEnum;
import com.xxl.job.core.handler.IJobHandler;
import com.xxl.job.core.handler.annotation.JobHandler;
import com.xxl.job.core.log.XxlJobLogger;

@JobHandler(value = "FileFormat_COSD0090", name = "COSD0090文件格式化")
@Service
public class FileFormat_COSD0090 extends IJobHandler {

	private static final long serialVersionUID = 1L;

	private static DataDateDAO dateDao = new DataDateDAOImpl();

	@Override
	public ReturnT<String> execute(String jobId, String... arg1) throws Exception {
		XxlJobLogger.log("开始COSD0090文件格式化...");
		String jobDate = dateDao.getDataDate();
		if (null != arg1[0]) {
			String dirPath = arg1[0].toString().replace("@", jobDate);
			File dir = new File(dirPath);
			if (!dir.exists()) {
				XxlJobLogger.log("INFO: 资源不足，目录不存在：" + dir, jobId + "");
				return new ReturnT<String>(ReturnT.FAIL_CODE, JobStartEnum.TASK_STATE_NO_RESOURCE.getCode(),
						"文件目录" + dir.getPath() + "不存在");
			}
			File preFile = new File(dir, "COSD0090_" + jobDate + ".txt");
			File newFile = new File(dir, "COSD0090_" + jobDate + "_proc.txt");
			if (newFile.exists()) {
				XxlJobLogger.log("INFO: 文件已存在，删除后再转换：" + newFile.getPath(), jobId + "");
				newFile.delete();
			}
			PrintWriter pw;
			pw = new PrintWriter(new OutputStreamWriter(new FileOutputStream(newFile), "GBK"));
			if (preFile.length()==0) {
				pw.print("|||");
				pw.flush();
				pw.close();
				XxlJobLogger.log("INFO: 源文件不存在，生成转换文件：" + preFile.getPath() + " >> " + newFile.getPath(), jobId + "");
				return ReturnT.SUCCESS;
			}
			BufferedReader br = new BufferedReader(new InputStreamReader(new FileInputStream(preFile), "GBK"));
			
			String line = "";
			String siteNo = "";
			String occurDate = "";
			String btLine = "";
			String[] strArr = null;
			List list = new ArrayList();
			try {
				while ((line = br.readLine()) != null) {
					if (line.contains("1                 ") && line.contains("-COSD0090")) {
						siteNo = line.substring(line.indexOf("（") + "（".length(), line.indexOf("-COSD0090"));
						continue;
					}
					if (line.contains("机构编号：") && line.contains("日期：")) {
						siteNo = line.substring(line.indexOf("机构编号： ") + "机构编号： ".length(), line.indexOf("日期： "))
								.trim();
						occurDate = line.substring(line.indexOf("日期： ") + "日期： ".length(),
								line.indexOf("日期： ") + "日期： ".length() + 10).trim();
						occurDate = new SimpleDateFormat("yyyyMMdd")
								.format(new SimpleDateFormat("yyyy/MM/dd").parse(occurDate));
						continue;
					}
					if (line.contains("所属行行号") && line.contains("核心流水号") && line.contains("付款人账号")) {
						btLine = line;
						strArr = new String[] { btLine.substring(0, btLine.indexOf("所属行行号 ")),
								btLine.substring(btLine.indexOf("所属行行号 "), btLine.indexOf("发起网点号 ")),
								btLine.substring(btLine.indexOf("发起网点号 "), btLine.indexOf("发起柜员号 ")),
								btLine.substring(btLine.indexOf("发起柜员号 "), btLine.indexOf("业务编码 ")),
								btLine.substring(btLine.indexOf("业务编码 "), btLine.indexOf("核心流水号 ")),
								btLine.substring(btLine.indexOf("核心流水号 "), btLine.indexOf("业务类型 ")),
								btLine.substring(btLine.indexOf("业务类型 "), btLine.indexOf("票据号码 ")),
								btLine.substring(btLine.indexOf("票据号码 "), btLine.indexOf("出票日期 ")),
								btLine.substring(btLine.indexOf("出票日期 "), btLine.indexOf("付款人账号 ")),
								btLine.substring(btLine.indexOf("付款人账号 "), btLine.indexOf("付款人名称 ")),
								btLine.substring(btLine.indexOf("付款人名称 "), btLine.indexOf("收款人账号 ")),
								btLine.substring(btLine.indexOf("收款人账号 "), btLine.indexOf("收款人名称")),
								btLine.substring(btLine.indexOf("收款人名称"), btLine.indexOf("金额 ")),
								btLine.substring(btLine.indexOf("金额 "), btLine.indexOf("业务状态")).replace(",", ""),
								btLine.substring(btLine.indexOf("业务状态")) };
						int index = 0;
						list = new ArrayList();
						for (String str : strArr) {
							list.add(new int[] { index, str.getBytes("GBK").length });
							index += str.getBytes("GBK").length;
						}
						continue;
					}
					if ("".equals(line.trim()) || line.contains("========================================")
							|| "中国银行集中作业系统业务交易明细表".equals(line.trim())) {
						continue;
					}
					//System.out.println(line.length());
					if (line.length()<300) {
						continue;
					}
					
					String tjzx = line.substring(14,30).trim();
					if ("37223".equals(tjzx)||"15707".equals(tjzx)||"16075".equals(tjzx)) {
						continue;
					}
					
					
					StringBuffer sb = new StringBuffer(siteNo + "|" + occurDate + "|");
					byte[] arr = line.getBytes("GBK");
					for (int i = 1; i < list.size(); i++) {
						int offset = ((int[]) list.get(i))[0];
						int length = ((int[]) list.get(i))[1];
						String tmp = new String(arr, offset, length, "GBK");
						sb.append(tmp).append("|");

					}
					pw.print(sb.toString() + System.getProperty("line.separator"));

				}
			} catch (IOException e) {
				e.printStackTrace();
			} finally {
				pw.flush();
				pw.close();
			}
			XxlJobLogger.log("INFO: 文件转换成功：" + newFile.getPath(), jobId + "");
			return ReturnT.SUCCESS;
		}
		return ReturnT.FAIL;
	}

	public static void main(String[] args) {
		FileFormat_COSD0090 ff = new FileFormat_COSD0090();
		try {
			ff.execute("8", "D:\\data\\@");
		} catch (Exception e) {
			// TODO Auto-generated catch block
			e.printStackTrace();
		}
	}
}
