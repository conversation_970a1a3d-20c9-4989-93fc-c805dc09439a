{"remainingRequest": "E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-q<PERSON><PERSON>n\\数字运营平台-统一门户工程\\dopUnify\\node_modules\\babel-loader\\lib\\index.js!E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan\\数字运营平台-统一门户工程\\dopUnify\\node_modules\\eslint-loader\\index.js??ref--13-0!E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan\\数字运营平台-统一门户工程\\dopUnify\\src\\api\\views\\system\\config\\application\\index.js", "dependencies": [{"path": "E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-<PERSON><PERSON><PERSON>n\\数字运营平台-统一门户工程\\dopUnify\\src\\api\\views\\system\\config\\application\\index.js", "mtime": 1686019810544}, {"path": "E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-<PERSON><PERSON><PERSON>n\\数字运营平台-统一门户工程\\dopUnify\\babel.config.js", "mtime": 1686019818359}, {"path": "E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-<PERSON><PERSON><PERSON>n\\数字运营平台-统一门户工程\\dopUnify\\.babelrc", "mtime": 1705285078668}, {"path": "E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-<PERSON><PERSON><PERSON>n\\数字运营平台-统一门户工程\\dopUnify\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": ************}, {"path": "E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-<PERSON><PERSON><PERSON>n\\数字运营平台-统一门户工程\\dopUnify\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-<PERSON><PERSON><PERSON>n\\数字运营平台-统一门户工程\\dopUnify\\node_modules\\eslint-loader\\index.js", "mtime": ************}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:aW1wb3J0IHJlcXVlc3QgZnJvbSAnQC91dGlscy9yZXF1ZXN0JzsKaW1wb3J0IGRlZmF1bHRTZXR0aW5ncyBmcm9tICdAL3NldHRpbmdzJzsKdmFyIHByZWZpeCA9IGRlZmF1bHRTZXR0aW5ncy5zZXJ2aWNlLnN5c3RlbSArICcvc2VydmVyJzsKCi8vIOW6lOeUqOacjeWKoemFjee9ruebuOWFs+aOpeWPowpleHBvcnQgdmFyIFN5c0FwcGxpY2F0aW9uID0gewogIC8vIOafpeivogogIHF1ZXJ5OiBmdW5jdGlvbiBxdWVyeShkYXRhKSB7CiAgICByZXR1cm4gcmVxdWVzdCh7CiAgICAgIHVybDogcHJlZml4ICsgJy9xdWVyeS5kbycsCiAgICAgIG1ldGhvZDogJ2dldCcsCiAgICAgIHBhcmFtczogewogICAgICAgIG1lc3NhZ2U6IGRhdGEKICAgICAgfQogICAgfSk7CiAgfSwKICAvLyDmlrDlop4KICBhZGRBcHBseTogZnVuY3Rpb24gYWRkQXBwbHkoZGF0YSkgewogICAgcmV0dXJuIHJlcXVlc3QoewogICAgICB1cmw6IHByZWZpeCArICcvYWRkLmRvJywKICAgICAgbWV0aG9kOiAncG9zdCcsCiAgICAgIGRhdGE6IGRhdGEKICAgIH0pOwogIH0sCiAgLy8g5L+u5pS5CiAgbW9kaWZ5QXBwbHk6IGZ1bmN0aW9uIG1vZGlmeUFwcGx5KGRhdGEpIHsKICAgIHJldHVybiByZXF1ZXN0KHsKICAgICAgdXJsOiBwcmVmaXggKyAnL21vZGlmeS5kbycsCiAgICAgIG1ldGhvZDogJ3Bvc3QnLAogICAgICBkYXRhOiBkYXRhCiAgICB9KTsKICB9LAogIC8vIOWIoOmZpAogIGRlbGV0ZUFwcGx5OiBmdW5jdGlvbiBkZWxldGVBcHBseShkYXRhKSB7CiAgICByZXR1cm4gcmVxdWVzdCh7CiAgICAgIHVybDogcHJlZml4ICsgJy9kZWxldGUuZG8nLAogICAgICBtZXRob2Q6ICdkZWxldGUnLAogICAgICBkYXRhOiBkYXRhCiAgICB9KTsKICB9Cn07"}, {"version": 3, "names": ["request", "defaultSettings", "prefix", "service", "system", "SysApplication", "query", "data", "url", "method", "params", "message", "addApply", "modifyApply", "deleteApply"], "sources": ["E:/1_Project/XYD_Project/dop-4.0/dop-4.1-q<PERSON><PERSON>n/数字运营平台-统一门户工程/dopUnify/src/api/views/system/config/application/index.js"], "sourcesContent": ["import request from '@/utils/request'\nimport defaultSettings from '@/settings'\nconst prefix = defaultSettings.service.system + '/server'\n\n// 应用服务配置相关接口\nexport const SysApplication = {\n  // 查询\n  query(data) {\n    return request({\n      url: prefix + '/query.do',\n      method: 'get',\n      params: { message: data }\n    })\n  },\n  // 新增\n  addApply(data) {\n    return request({\n      url: prefix + '/add.do',\n      method: 'post',\n      data\n    })\n  },\n  // 修改\n  modifyApply(data) {\n    return request({\n      url: prefix + '/modify.do',\n      method: 'post',\n      data\n    })\n  },\n  // 删除\n  deleteApply(data) {\n    return request({\n      url: prefix + '/delete.do',\n      method: 'delete',\n      data\n    })\n  }\n}\n"], "mappings": "AAAA,OAAOA,OAAO,MAAM,iBAAiB;AACrC,OAAOC,eAAe,MAAM,YAAY;AACxC,IAAMC,MAAM,GAAGD,eAAe,CAACE,OAAO,CAACC,MAAM,GAAG,SAAS;;AAEzD;AACA,OAAO,IAAMC,cAAc,GAAG;EAC5B;EACAC,KAAK,iBAACC,IAAI,EAAE;IACV,OAAOP,OAAO,CAAC;MACbQ,GAAG,EAAEN,MAAM,GAAG,WAAW;MACzBO,MAAM,EAAE,KAAK;MACbC,MAAM,EAAE;QAAEC,OAAO,EAAEJ;MAAK;IAC1B,CAAC,CAAC;EACJ,CAAC;EACD;EACAK,QAAQ,oBAACL,IAAI,EAAE;IACb,OAAOP,OAAO,CAAC;MACbQ,GAAG,EAAEN,MAAM,GAAG,SAAS;MACvBO,MAAM,EAAE,MAAM;MACdF,IAAI,EAAJA;IACF,CAAC,CAAC;EACJ,CAAC;EACD;EACAM,WAAW,uBAACN,IAAI,EAAE;IAChB,OAAOP,OAAO,CAAC;MACbQ,GAAG,EAAEN,MAAM,GAAG,YAAY;MAC1BO,MAAM,EAAE,MAAM;MACdF,IAAI,EAAJA;IACF,CAAC,CAAC;EACJ,CAAC;EACD;EACAO,WAAW,uBAACP,IAAI,EAAE;IAChB,OAAOP,OAAO,CAAC;MACbQ,GAAG,EAAEN,MAAM,GAAG,YAAY;MAC1BO,MAAM,EAAE,QAAQ;MAChBF,IAAI,EAAJA;IACF,CAAC,CAAC;EACJ;AACF,CAAC"}]}