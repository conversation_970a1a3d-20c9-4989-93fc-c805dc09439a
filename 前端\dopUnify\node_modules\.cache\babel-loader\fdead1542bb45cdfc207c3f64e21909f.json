{"remainingRequest": "E:\\华南区项目\\中国银行\\开发区\\科技公司运营平台\\前端\\dopUnify\\node_modules\\babel-loader\\lib\\index.js!E:\\华南区项目\\中国银行\\开发区\\科技公司运营平台\\前端\\dopUnify\\node_modules\\eslint-loader\\index.js??ref--13-0!E:\\华南区项目\\中国银行\\开发区\\科技公司运营平台\\前端\\dopUnify\\src\\permission.js", "dependencies": [{"path": "E:\\华南区项目\\中国银行\\开发区\\科技公司运营平台\\前端\\dopUnify\\src\\permission.js", "mtime": 1721631954662}, {"path": "E:\\华南区项目\\中国银行\\开发区\\科技公司运营平台\\前端\\dopUnify\\babel.config.js", "mtime": 1721631954662}, {"path": "E:\\华南区项目\\中国银行\\开发区\\科技公司运营平台\\前端\\dopUnify\\.babelrc", "mtime": 1721631954662}, {"path": "E:\\华南区项目\\中国银行\\开发区\\科技公司运营平台\\前端\\dopUnify\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "E:\\华南区项目\\中国银行\\开发区\\科技公司运营平台\\前端\\dopUnify\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "E:\\华南区项目\\中国银行\\开发区\\科技公司运营平台\\前端\\dopUnify\\node_modules\\eslint-loader\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["router", "store", "Message", "NProgress", "defaultSettings", "childUrl", "errorRouter", "configure", "showSpinner", "whiteList", "beforeEach", "to", "from", "next", "start", "title", "titleDoc", "meta", "document", "hasToken", "getters", "token", "path", "done", "hasRoles", "roles", "length", "dispatch", "accessRoutes", "i", "element", "addRoute", "j", "replace", "error", "indexOf", "after<PERSON>ach"], "sources": ["E:/华南区项目/中国银行/开发区/科技公司运营平台/前端/dopUnify/src/permission.js"], "sourcesContent": ["import router from './router'\nimport store from './store'\nimport { Message } from 'element-ui'\nimport NProgress from 'nprogress' // 进度条插件\nimport 'nprogress/nprogress.css' // 进度条样式\n// import { getToken } from '@/utils/auth' // 从cookie中获取token\nimport defaultSettings from '@/settings'\nimport { childUrl } from '@/start' // 父子工程跳转配置\n// import { updateThemeColor } from '@/components/Layout/ThemePicker/indexColor.js' // 配置整体风格\n\nimport errorRouter from '@/router/modules/error.js' // 404等路由\n\nNProgress.configure({ showSpinner: false }) // 进度条 配置\n\nconst whiteList = ['/login', '/error', '/error2'] // 没有重定向白名单 , 访问拦截\n\n// 导航守卫 - “导航”表示路由正在发生改变\nrouter.beforeEach(async(to, from, next) => {\n  // if (window.location.hash.indexOf('tellerno') !== -1) {\n  //   console.log('免密登录')\n  // } else {\n  // 开始进度条\n  NProgress.start()\n\n  // 设置页面标题 begin\n  const title = defaultSettings.title\n  let titleDoc = title\n  if (to.meta.title) {\n    titleDoc = `${to.meta.title} - ${title}`\n  }\n  document.title = titleDoc\n  // 设置页面标题 end\n\n  // 判断用户是否已经登录\n  const hasToken = store.getters.token\n  if (hasToken) {\n    if (to.path === '/login') {\n      // if is logged in, redirect to the home page\n      next('/')\n      NProgress.done() // 进度条结束\n    } else {\n      // 判断用户是否通过getInfo获得了权限角色\n      const hasRoles = store.getters.roles && store.getters.roles.length > 0\n      if (hasRoles) {\n        next()\n      } else {\n        try {\n          // 获取用户信息\n          // 注意:角色必须是一个对象数组!例如:['admin']或['developer'，'editor']\n          const { roles } = await store.dispatch('user/getInfo')\n\n          // 生成基于角色的可访问路由图 - 异步路由\n          const accessRoutes = await store.dispatch(\n            'permission/generateRoutes',\n            { roles: roles, token: hasToken }\n          ) // 获取所有路由\n\n          // console.log('accessRoutes', accessRoutes)\n\n          // 动态添加可访问路由\n          for (let i = 0, length = accessRoutes.length; i < length; i += 1) {\n            const element = accessRoutes[i]\n            router.addRoute(element)\n          }\n          // 添加 404等路由\n          for (let j = 0, length = errorRouter.length; j < length; j += 1) {\n            const element = errorRouter[j]\n            router.addRoute(element)\n          }\n          // router.addRoute(accessRoutes)\n          // router.addRoute(errorRouter)\n\n          // hack方法来确保addroues是完整的\n          // 设置 replace: true, 这样导航就不会留下历史记录\n          next({ ...to, replace: true })\n          // if (!hasToken) {\n          store.dispatch('common/commonInit') // 获取机构树\n        } catch (error) {\n          // 移除token，到登录页面重新登录\n          await store.dispatch('user/resetToken')\n          Message.error(error || 'Has Error')\n          // next(`/login?redirect=${to.path}`)\n          next(`/login`)\n          NProgress.done()\n        }\n      }\n    }\n  } else {\n    // 获取从子页面返回的参数 begin------------\n    childUrl()\n    // 获取从子页面返回的参数 end----------\n\n    /* 无 token*/\n    if (whiteList.indexOf(to.path) !== -1) {\n      next()\n    } else {\n      // 其他没有访问权限的页面被重定向到登录页面。\n      // next(`/login?redirect=${to.path}`)\n      next(`/login`)\n      NProgress.done() // 关闭进度条\n    }\n  }\n  // }\n})\n\n// 全局后置钩子\nrouter.afterEach(() => {\n  // finish progress bar\n  NProgress.done() // 关闭进度条\n})\n"], "mappings": ";;;;AAAA,OAAOA,MAAM,MAAM,UAAU;AAC7B,OAAOC,KAAK,MAAM,SAAS;AAC3B,SAASC,OAAO,QAAQ,YAAY;AACpC,OAAOC,SAAS,MAAM,WAAW,EAAC;AAClC,OAAO,yBAAyB,EAAC;AACjC;AACA,OAAOC,eAAe,MAAM,YAAY;AACxC,SAASC,QAAQ,QAAQ,SAAS,EAAC;AACnC;;AAEA,OAAOC,WAAW,MAAM,2BAA2B,EAAC;;AAEpDH,SAAS,CAACI,SAAS,CAAC;EAAEC,WAAW,EAAE;AAAM,CAAC,CAAC,EAAC;;AAE5C,IAAMC,SAAS,GAAG,CAAC,QAAQ,EAAE,QAAQ,EAAE,SAAS,CAAC,EAAC;;AAElD;AACAT,MAAM,CAACU,UAAU;EAAA,sEAAC,iBAAMC,EAAE,EAAEC,IAAI,EAAEC,IAAI;IAAA;IAAA;MAAA;QAAA;UAAA;YACpC;YACA;YACA;YACA;YACAV,SAAS,CAACW,KAAK,EAAE;;YAEjB;YACMC,KAAK,GAAGX,eAAe,CAACW,KAAK;YAC/BC,QAAQ,GAAGD,KAAK;YACpB,IAAIJ,EAAE,CAACM,IAAI,CAACF,KAAK,EAAE;cACjBC,QAAQ,aAAML,EAAE,CAACM,IAAI,CAACF,KAAK,gBAAMA,KAAK,CAAE;YAC1C;YACAG,QAAQ,CAACH,KAAK,GAAGC,QAAQ;YACzB;;YAEA;YACMG,QAAQ,GAAGlB,KAAK,CAACmB,OAAO,CAACC,KAAK;YAAA,KAChCF,QAAQ;cAAA;cAAA;YAAA;YAAA,MACNR,EAAE,CAACW,IAAI,KAAK,QAAQ;cAAA;cAAA;YAAA;YACtB;YACAT,IAAI,CAAC,GAAG,CAAC;YACTV,SAAS,CAACoB,IAAI,EAAE,EAAC;YAAA;YAAA;UAAA;YAEjB;YACMC,QAAQ,GAAGvB,KAAK,CAACmB,OAAO,CAACK,KAAK,IAAIxB,KAAK,CAACmB,OAAO,CAACK,KAAK,CAACC,MAAM,GAAG,CAAC;YAAA,KAClEF,QAAQ;cAAA;cAAA;YAAA;YACVX,IAAI,EAAE;YAAA;YAAA;UAAA;YAAA;YAAA;YAAA,OAKoBZ,KAAK,CAAC0B,QAAQ,CAAC,cAAc,CAAC;UAAA;YAAA;YAA9CF,KAAK,yBAALA,KAAK;YAAA;YAAA,OAGcxB,KAAK,CAAC0B,QAAQ,CACvC,2BAA2B,EAC3B;cAAEF,KAAK,EAAEA,KAAK;cAAEJ,KAAK,EAAEF;YAAS,CAAC,CAClC;UAAA;YAHKS,YAAY;YAGhB;;YAEF;;YAEA;YACA,KAASC,CAAC,GAAG,CAAC,EAAEH,MAAM,GAAGE,YAAY,CAACF,MAAM,EAAEG,CAAC,GAAGH,MAAM,EAAEG,CAAC,IAAI,CAAC,EAAE;cAC1DC,OAAO,GAAGF,YAAY,CAACC,CAAC,CAAC;cAC/B7B,MAAM,CAAC+B,QAAQ,CAACD,OAAO,CAAC;YAC1B;YACA;YACA,KAASE,CAAC,GAAG,CAAC,EAAEN,OAAM,GAAGpB,WAAW,CAACoB,MAAM,EAAEM,CAAC,GAAGN,OAAM,EAAEM,CAAC,IAAI,CAAC,EAAE;cACzDF,QAAO,GAAGxB,WAAW,CAAC0B,CAAC,CAAC;cAC9BhC,MAAM,CAAC+B,QAAQ,CAACD,QAAO,CAAC;YAC1B;YACA;YACA;;YAEA;YACA;YACAjB,IAAI,iCAAMF,EAAE;cAAEsB,OAAO,EAAE;YAAI,GAAG;YAC9B;YACAhC,KAAK,CAAC0B,QAAQ,CAAC,mBAAmB,CAAC,EAAC;YAAA;YAAA;UAAA;YAAA;YAAA;YAAA;YAAA,OAG9B1B,KAAK,CAAC0B,QAAQ,CAAC,iBAAiB,CAAC;UAAA;YACvCzB,OAAO,CAACgC,KAAK,CAAC,eAAS,WAAW,CAAC;YACnC;YACArB,IAAI,UAAU;YACdV,SAAS,CAACoB,IAAI,EAAE;UAAA;YAAA;YAAA;UAAA;YAKtB;YACAlB,QAAQ,EAAE;YACV;;YAEA;YACA,IAAII,SAAS,CAAC0B,OAAO,CAACxB,EAAE,CAACW,IAAI,CAAC,KAAK,CAAC,CAAC,EAAE;cACrCT,IAAI,EAAE;YACR,CAAC,MAAM;cACL;cACA;cACAA,IAAI,UAAU;cACdV,SAAS,CAACoB,IAAI,EAAE,EAAC;YACnB;UAAC;UAAA;YAAA;QAAA;MAAA;IAAA;EAAA,CAGJ;EAAA;IAAA;EAAA;AAAA,IAAC;;AAEF;AACAvB,MAAM,CAACoC,SAAS,CAAC,YAAM;EACrB;EACAjC,SAAS,CAACoB,IAAI,EAAE,EAAC;AACnB,CAAC,CAAC"}]}