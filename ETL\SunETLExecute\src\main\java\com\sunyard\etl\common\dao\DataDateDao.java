package com.sunyard.etl.common.dao;

import com.sun.rowset.CachedRowSetImpl;
import com.sunyard.etl.system.common.Constants;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.sunyard.util.dbutil.DBHandler;

import java.sql.SQLException;

public class DataDateDao {
    protected final Logger log = LoggerFactory.getLogger(getClass());
    protected final Logger logger = LoggerFactory.getLogger("ocrTaskSendLogger");

    /**
     * 获取最大的JOB_DATE
     * @return 最大的JOB_DATE
     */
    public String getDataDate() {
        String maxDate = null;
        CachedRowSetImpl rs = null;
        DBHandler dbHandler = new DBHandler(Constants.CONN_POOL_NAME);
        try {
            String sql = "SELECT MAX(JOB_DATE) FROM QRTZ_JOB_DATE";
            rs = dbHandler.queryRs(sql);
            if (rs.next()) {
                maxDate = rs.getString(1);
            }
        } catch (SQLException e) {
            logger.error("获取最大JOB_DATE时发生异常", e);
        }
        return maxDate;
    }

    /**
     * 更新JOB_DATE
     * @param date 新的JOB_DATE
     * @return 更新是否成功
     */
    public boolean updateDate(String date) {
        DBHandler dbHandler = new DBHandler(Constants.CONN_POOL_NAME);
        try {
            String sql = "UPDATE QRTZ_JOB_DATE SET JOB_DATE = ?";
            int rowsAffected = dbHandler.execute(sql, date);
            return rowsAffected > 0;
        } catch (SQLException e) {
            logger.error("更新JOB_DATE时发生异常", e);
            return false;
        }
    }
}
