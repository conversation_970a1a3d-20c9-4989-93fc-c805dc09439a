package com.sunyard.etl.nps.handler;


import com.xxl.job.core.handler.annotation.JobHandler;

import org.springframework.stereotype.Service;

import com.sunyard.etl.nps.common.NPSContants;
import com.sunyard.etl.nps.common.SystemInit;
import com.sunyard.etl.nps.service.business.ImageQueryService;
import com.sunyard.etl.system.dao.JobParamDao;
import com.sunyard.etl.system.dao.impl.JobParamDaoImpl;
import com.sunyard.etl.system.model.JobParam;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.IJobHandler;
import com.xxl.job.core.log.XxlJobLogger;


@JobHandler(value="ImageQuery",name = "影像查询/下载任务")
@Service
public  class ImageQuery extends IJobHandler{
	private static final long serialVersionUID = 1L;
	private String tableName = "ImageQuery";
	
	public ReturnT<String> execute(String jobId,String... params) throws Exception {
		if (!NPSContants.SYSTEM_INIT) {
			XxlJobLogger.log("正在进行初始化...", tableName);
			String result = SystemInit.serviceInit();
			if (result.contains("SUCCESS")) {
				NPSContants.SYSTEM_INIT = true;
			} else {
				XxlJobLogger.log("系统初始化失败", tableName);
				return ReturnT.FAIL;
			}
		}	
		
		JobParamDao jobParamDao = new JobParamDaoImpl();
		JobParam jobParam = jobParamDao.JobParam(Integer.parseInt(jobId));
		if( null == jobParam ){
			XxlJobLogger.log("JOBID:" + jobId + "未配置参数",tableName);
			return ReturnT.FAIL;	
		}
		ImageQueryService ser = new ImageQueryService();
		String initResult = ser.init(jobParam);
		if(initResult.contains("FAIL")){
			XxlJobLogger.log("服务初始化失败：" + initResult,tableName);
			return ReturnT.FAIL;
		}
		String queryresult = ser.queryECM();
		XxlJobLogger.log("查询ECM结果：" + queryresult,tableName);
		
		if(queryresult.contains("FAIL")){
			XxlJobLogger.log("查询ECM任务失败" ,tableName);
			return ReturnT.FAIL;
		}
		
		ser.generateBatchId(jobParam.getDataSourceId());
		
		XxlJobLogger.log("本次任务完成",tableName);
		return ReturnT.SUCCESS;	
	}
	
//	public static void main(String[] args) {
//		ImageQuery a = new ImageQuery();
//		try {
//			a.execute("39", "");
//		} catch (Exception e) {
//			e.printStackTrace();
//		}
//	}

}
