{"remainingRequest": "E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-q<PERSON><PERSON>n\\数字运营平台-统一门户工程\\dopUnify\\node_modules\\babel-loader\\lib\\index.js!E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan\\数字运营平台-统一门户工程\\dopUnify\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan\\数字运营平台-统一门户工程\\dopUnify\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan\\数字运营平台-统一门户工程\\dopUnify\\src\\views\\system\\externalManage\\external\\component\\table\\index.vue?vue&type=script&lang=js&", "dependencies": [{"path": "E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-<PERSON><PERSON><PERSON>n\\数字运营平台-统一门户工程\\dopUnify\\src\\views\\system\\externalManage\\external\\component\\table\\index.vue", "mtime": 1703583638535}, {"path": "E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-<PERSON><PERSON><PERSON>n\\数字运营平台-统一门户工程\\dopUnify\\babel.config.js", "mtime": 1686019818359}, {"path": "E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-<PERSON><PERSON><PERSON>n\\数字运营平台-统一门户工程\\dopUnify\\.babelrc", "mtime": 1705285078668}, {"path": "E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-<PERSON><PERSON><PERSON>n\\数字运营平台-统一门户工程\\dopUnify\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-<PERSON><PERSON><PERSON>n\\数字运营平台-统一门户工程\\dopUnify\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-<PERSON><PERSON><PERSON>n\\数字运营平台-统一门户工程\\dopUnify\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-<PERSON><PERSON><PERSON>n\\数字运营平台-统一门户工程\\dopUnify\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1655715099000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:aW1wb3J0IF90b0NvbnN1bWFibGVBcnJheSBmcm9tICJFOi8xX1Byb2plY3QvWFlEX1Byb2plY3QvZG9wLTQuMC9kb3AtNC4xLXFpYW5kdWFuL1x1NjU3MFx1NUI1N1x1OEZEMFx1ODQyNVx1NUU3M1x1NTNGMC1cdTdFREZcdTRFMDBcdTk1RThcdTYyMzdcdTVERTVcdTdBMEIvZG9wVW5pZnkvbm9kZV9tb2R1bGVzL0BiYWJlbC9ydW50aW1lL2hlbHBlcnMvZXNtL3RvQ29uc3VtYWJsZUFycmF5LmpzIjsKaW1wb3J0IF9jcmVhdGVGb3JPZkl0ZXJhdG9ySGVscGVyIGZyb20gIkU6LzFfUHJvamVjdC9YWURfUHJvamVjdC9kb3AtNC4wL2RvcC00LjEtcWlhbmR1YW4vXHU2NTcwXHU1QjU3XHU4RkQwXHU4NDI1XHU1RTczXHU1M0YwLVx1N0VERlx1NEUwMFx1OTVFOFx1NjIzN1x1NURFNVx1N0EwQi9kb3BVbmlmeS9ub2RlX21vZHVsZXMvQGJhYmVsL3J1bnRpbWUvaGVscGVycy9lc20vY3JlYXRlRm9yT2ZJdGVyYXRvckhlbHBlci5qcyI7CmltcG9ydCAiY29yZS1qcy9tb2R1bGVzL2VzLmFycmF5Lm1hcC5qcyI7CmltcG9ydCAiY29yZS1qcy9tb2R1bGVzL2VzLmFycmF5LnNvcnQuanMiOwppbXBvcnQgImNvcmUtanMvbW9kdWxlcy9lcy5hcnJheS5jb25jYXQuanMiOwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwoKaW1wb3J0IHsgY29tbW9uTXNnU3VjY2VzcywgY29tbW9uTXNnV2FybiwgY29tbW9uTXNnQ29uZmlybSB9IGZyb20gJ0AvdXRpbHMvbWVzc2FnZS5qcyc7IC8vIOaPkOekuuS/oeaBrwppbXBvcnQgeyBkaWN0aW9uYXJ5R2V0IH0gZnJvbSAnQC91dGlscy9kaWN0aW9uYXJ5LmpzJzsgLy8g5a2X5YW45bi46YePCmltcG9ydCBSZXNpemVNaXhpbiBmcm9tICdAL3V0aWxzL1Jlc2l6ZUhhbmRsZXInOyAvLyDmlbTkvZPpobXpnaLmmK/lkKbmoLnmja7mgLvpq5jphY3nva4KaW1wb3J0IHsgY29tbW9uQmxhbmsgfSBmcm9tICdAL3V0aWxzL2NvbW1vbic7CmltcG9ydCB7IGNvbmZpZywgY29uZmlnVGFibGUgfSBmcm9tICcuL2luZm8nOyAvLyDooajlpLTjgIHooajljZXphY3nva4KCmltcG9ydCB7IG9yZ2FuTm9Gb3JtYXQgfSBmcm9tICdAL2ZpbHRlcnMnOyAvLyDmjIfku6QKCmltcG9ydCB7IHN5c3RlbSB9IGZyb20gJ0AvYXBpJzsKdmFyIF9zeXN0ZW0kU3lzRXh0ZXJFeHRlciA9IHN5c3RlbS5TeXNFeHRlckV4dGVybmFsLAogIHF1ZXJ5ID0gX3N5c3RlbSRTeXNFeHRlckV4dGVyLnF1ZXJ5LAogIHF1ZXJ5VXNlciA9IF9zeXN0ZW0kU3lzRXh0ZXJFeHRlci5xdWVyeVVzZXIsCiAgYWRkID0gX3N5c3RlbSRTeXNFeHRlckV4dGVyLmFkZCwKICBtb2RpZnkgPSBfc3lzdGVtJFN5c0V4dGVyRXh0ZXIubW9kaWZ5LAogIGRlbCA9IF9zeXN0ZW0kU3lzRXh0ZXJFeHRlci5kZWw7CnZhciB0aGF0Owp2YXIgdGltb3V0MTsKZXhwb3J0IGRlZmF1bHQgewogIG5hbWU6ICdUYWJsZUxpc3QnLAogIGZpbHRlcnM6IHsKICAgIC8qKg0KICAgICAqIOiHquWumuS5ieWkluihqOaVsOaNrua6kA0KICAgICAqIEBwYXJhbSB7U3RyaW5nfSBzdHJpbmcg5pWw5o2u5rqQ55qE5YC8Ki8KICAgIGV4dGVybmFsU3lzdGVtVHB5ZTogZnVuY3Rpb24gZXh0ZXJuYWxTeXN0ZW1UcHllKHN0cmluZykgewogICAgICB2YXIgdmFsdWVTID0gJyc7CiAgICAgIHRoYXQuc3lzdGVtQXJyYXkubWFwKGZ1bmN0aW9uIChpdGVtKSB7CiAgICAgICAgaWYgKGl0ZW0udmFsdWUgPT09IHN0cmluZykgewogICAgICAgICAgdmFsdWVTID0gaXRlbS5sYWJlbDsKICAgICAgICB9CiAgICAgIH0pOwogICAgICByZXR1cm4gdmFsdWVTOwogICAgfSwKICAgIC8qKg0KICAgICAqIOiHquWumuS5ieWkluihqOaVsOaNrua6kA0KICAgICAqIEBwYXJhbSB7U3RyaW5nfSBzdHJpbmcg5pWw5o2u5rqQ55qE5YC8Ki8KICAgIHVzZXJOb1RweWU6IGZ1bmN0aW9uIHVzZXJOb1RweWUoc3RyaW5nKSB7CiAgICAgIHZhciB2YWx1ZVMgPSAnJzsKICAgICAgdGhhdC51c2VyTm9BcnIubWFwKGZ1bmN0aW9uIChpdGVtKSB7CiAgICAgICAgaWYgKGl0ZW0udmFsdWUgPT09IHN0cmluZykgewogICAgICAgICAgdmFsdWVTID0gaXRlbS52YWx1ZSArICctJyArIGl0ZW0ubGFiZWw7CiAgICAgICAgfQogICAgICB9KTsKICAgICAgcmV0dXJuIHZhbHVlUzsKICAgIH0KICB9LAogIG1peGluczogW1Jlc2l6ZU1peGluXSwKICBwcm9wczogewogICAgZGVmYXVsdEZvcm06IHsKICAgICAgdHlwZTogT2JqZWN0LAogICAgICBkZWZhdWx0OiBmdW5jdGlvbiBfZGVmYXVsdCgpIHsKICAgICAgICByZXR1cm4ge307CiAgICAgIH0KICAgIH0sCiAgICBzeXN0ZW1BcnJheTogewogICAgICB0eXBlOiBBcnJheSwKICAgICAgZGVmYXVsdDogZnVuY3Rpb24gX2RlZmF1bHQoKSB7CiAgICAgICAgcmV0dXJuIFtdOwogICAgICB9CiAgICB9LAogICAgdXNlck5vQXJyOiB7CiAgICAgIHR5cGU6IEFycmF5LAogICAgICBkZWZhdWx0OiBmdW5jdGlvbiBfZGVmYXVsdCgpIHsKICAgICAgICByZXR1cm4gW107CiAgICAgIH0KICAgIH0KICB9LAogIGRhdGE6IGZ1bmN0aW9uIGRhdGEoKSB7CiAgICByZXR1cm4gewogICAgICB0YWJsZTogewogICAgICAgIGNvbHVtbnM6IGNvbmZpZ1RhYmxlKCksCiAgICAgICAgcmVmOiAndGFibGVSZWYnLAogICAgICAgIGxvYWRpbmc6IGZhbHNlLAogICAgICAgIHNlbGVjdGlvbjogdHJ1ZSwKICAgICAgICAvLyDlpI3pgIkKICAgICAgICBpbmRleE51bWJlcjogdHJ1ZSwKICAgICAgICAvLyDluo/lj7cKICAgICAgICBjb21wb25lbnRQcm9wczogewogICAgICAgICAgZGF0YTogW10sCiAgICAgICAgICAvLyDooajmoLzmlbDmja4KICAgICAgICAgIGhlaWdodDogJzEwMHB4JywKICAgICAgICAgIGZvcm1Sb3c6IDEgLy8g6KGo5Y2V6KGM5pWwCiAgICAgICAgfSwKCiAgICAgICAgY3VycmVudFJvdzogW10sCiAgICAgICAgLy8g6YCJ5Lit6KGMCiAgICAgICAgcGFnZUxpc3Q6IHsKICAgICAgICAgIHRvdGFsTnVtOiAwLAogICAgICAgICAgY3VycmVudFBhZ2U6IDEsCiAgICAgICAgICAvLyDlvZPliY3pobUKICAgICAgICAgIHBhZ2VTaXplOiB0aGlzLiRzdG9yZS5nZXR0ZXJzLnBhZ2VTaXplIC8vIOW9k+WJjemhteaYvuekuuadoeaVsAogICAgICAgIH0KICAgICAgfSwKCiAgICAgIGJ0bkRhdGFzOiB7CiAgICAgICAgYnRuQWRkOiB7CiAgICAgICAgICBzaG93OiB0aGlzLiRhdHRyc1snYnRuLWFsbCddLmJ0bkFkZAogICAgICAgIH0sCiAgICAgICAgYnRuRGVsZXRlOiB7CiAgICAgICAgICBzaG93OiB0aGlzLiRhdHRyc1snYnRuLWFsbCddLmJ0bkRlbGV0ZQogICAgICAgIH0sCiAgICAgICAgYnRuTW9kaWZ5OiB7CiAgICAgICAgICBzaG93OiB0aGlzLiRhdHRyc1snYnRuLWFsbCddLmJ0bk1vZGlmeQogICAgICAgIH0KICAgICAgfSwKICAgICAgZGlhbG9nOiB7CiAgICAgICAgdmlzaWJsZTogZmFsc2UsCiAgICAgICAgb3ByYXRlOiAnYWRkJywKICAgICAgICBjb21wb25lbnRQcm9wczogewogICAgICAgICAgLy8g5by55Ye65qGG6YWN572u5bGe5oCnCiAgICAgICAgICB0aXRsZTogJ+aWsOWinicsCiAgICAgICAgICB3aWR0aDogJzUwJScgLy8g5b2T5YmN5by55Ye65qGG5a695bqmCiAgICAgICAgfSwKCiAgICAgICAgZm9ybTogewogICAgICAgICAgbGFiZWxXaWR0aDogJzEycmVtJywKICAgICAgICAgIGNvbmZpZzogY29uZmlnKHRoaXMpLAogICAgICAgICAgZGVmYXVsdEZvcm06IHsKICAgICAgICAgICAgb3JnYW5fbm86ICcnLAogICAgICAgICAgICBvcmdhbl9uYW1lOiAnJywKICAgICAgICAgICAgdXNlcl9ubzogW10sCiAgICAgICAgICAgIHVzZXJfbmFtZTogJycsCiAgICAgICAgICAgIGV4dGVybmFsX3N5c3RlbV9ubzogW10sCiAgICAgICAgICAgIGRhdGFfdHlwZTogJzAnLAogICAgICAgICAgICBleHRlcm5hbF9kYXRhX25vOiBbXSwKICAgICAgICAgICAgZXh0ZXJuYWxfZGF0YV9uYW1lOiAnJwogICAgICAgICAgfQogICAgICAgIH0KICAgICAgfSwKICAgICAgdXNlckxpc3Q6IFtdLAogICAgICAvLyDnlKjmiLfliJfooagKICAgICAgcm9sZUxpc3Q6IFtdIC8vIOezu+e7n+inkuiJsuWIl+ihqAogICAgfTsKICB9LAoKICB3YXRjaDogewogICAgLy8g5py65p6E5Y+36IGU5YqoIOacuuaehOWQjeensOOAgeeUqOaIt+e8luWPtwogICAgJ2RpYWxvZy5mb3JtLmRlZmF1bHRGb3JtLm9yZ2FuX25vJzogewogICAgICBoYW5kbGVyOiBmdW5jdGlvbiBoYW5kbGVyKHZhbCkgewogICAgICAgIHRoaXMuZGlhbG9nLmZvcm0uZGVmYXVsdEZvcm0ub3JnYW5fbmFtZSA9IG9yZ2FuTm9Gb3JtYXQodmFsKTsKICAgICAgICAvLyDpgY3ljobnlKjmiLfliJfooagKICAgICAgICB2YXIgdXNlckFyciA9IFtdOwogICAgICAgIHZhciBfaXRlcmF0b3IgPSBfY3JlYXRlRm9yT2ZJdGVyYXRvckhlbHBlcih0aGlzLnVzZXJMaXN0KSwKICAgICAgICAgIF9zdGVwOwogICAgICAgIHRyeSB7CiAgICAgICAgICBmb3IgKF9pdGVyYXRvci5zKCk7ICEoX3N0ZXAgPSBfaXRlcmF0b3IubigpKS5kb25lOykgewogICAgICAgICAgICB2YXIgaXRlbSA9IF9zdGVwLnZhbHVlOwogICAgICAgICAgICBpZiAoaXRlbS5vcmdhbl9ubyA9PT0gdmFsKSB7CiAgICAgICAgICAgICAgdmFyIHVzZXJfaW5mbyA9IHt9OwogICAgICAgICAgICAgIHVzZXJfaW5mb1sndmFsdWUnXSA9IGl0ZW0udXNlcl9ubzsKICAgICAgICAgICAgICB1c2VyX2luZm9bJ2xhYmVsJ10gPSBpdGVtLnVzZXJfbm8gKyAnLScgKyBpdGVtLnVzZXJfbmFtZTsKICAgICAgICAgICAgICB1c2VyQXJyLnB1c2godXNlcl9pbmZvKTsKICAgICAgICAgICAgfQogICAgICAgICAgfQogICAgICAgIH0gY2F0Y2ggKGVycikgewogICAgICAgICAgX2l0ZXJhdG9yLmUoZXJyKTsKICAgICAgICB9IGZpbmFsbHkgewogICAgICAgICAgX2l0ZXJhdG9yLmYoKTsKICAgICAgICB9CiAgICAgICAgdGhpcy5kaWFsb2cuZm9ybS5jb25maWcudXNlcl9uby5vcHRpb25zID0gdXNlckFycjsKICAgICAgICAvLyDogZTliqjml7botYvliJ3lp4vlgLwKICAgICAgICBpZiAodXNlckFyci5sZW5ndGggPiAwKSB7CiAgICAgICAgICB0aGlzLmRpYWxvZy5mb3JtLmRlZmF1bHRGb3JtLnVzZXJfbm8gPSB1c2VyQXJyWzBdLnZhbHVlOwogICAgICAgIH0KICAgICAgfSwKICAgICAgZGVlcDogdHJ1ZSAvLyDmt7Hluqbnm5HlkKwKICAgIH0sCgogICAgLy8g55So5oi35Y+36IGU5YqoIOeUqOaIt+WQjQogICAgJ2RpYWxvZy5mb3JtLmRlZmF1bHRGb3JtLnVzZXJfbm8nOiB7CiAgICAgIGhhbmRsZXI6IGZ1bmN0aW9uIGhhbmRsZXIodmFsKSB7CiAgICAgICAgdmFyIF9pdGVyYXRvcjIgPSBfY3JlYXRlRm9yT2ZJdGVyYXRvckhlbHBlcih0aGlzLnVzZXJMaXN0KSwKICAgICAgICAgIF9zdGVwMjsKICAgICAgICB0cnkgewogICAgICAgICAgZm9yIChfaXRlcmF0b3IyLnMoKTsgIShfc3RlcDIgPSBfaXRlcmF0b3IyLm4oKSkuZG9uZTspIHsKICAgICAgICAgICAgdmFyIGl0ZW0gPSBfc3RlcDIudmFsdWU7CiAgICAgICAgICAgIGlmIChpdGVtLnVzZXJfbm8gPT09IHZhbCkgewogICAgICAgICAgICAgIHRoaXMuZGlhbG9nLmZvcm0uZGVmYXVsdEZvcm0udXNlcl9uYW1lID0gaXRlbS51c2VyX25hbWU7CiAgICAgICAgICAgIH0KICAgICAgICAgIH0KICAgICAgICB9IGNhdGNoIChlcnIpIHsKICAgICAgICAgIF9pdGVyYXRvcjIuZShlcnIpOwogICAgICAgIH0gZmluYWxseSB7CiAgICAgICAgICBfaXRlcmF0b3IyLmYoKTsKICAgICAgICB9CiAgICAgIH0sCiAgICAgIGRlZXA6IHRydWUgLy8g5rex5bqm55uR5ZCsCiAgICB9LAoKICAgIC8vIOezu+e7n+e8luWPt+iBlOWKqCDns7vnu5/mlbDmja7nvJblj7cKICAgICdkaWFsb2cuZm9ybS5kZWZhdWx0Rm9ybS5leHRlcm5hbF9zeXN0ZW1fbm8nOiB7CiAgICAgIGhhbmRsZXI6IGZ1bmN0aW9uIGhhbmRsZXIodmFsKSB7CiAgICAgICAgdmFyIHJvbGVBcnIgPSBbXTsKICAgICAgICBpZiAodmFsKSB7CiAgICAgICAgICAvLyDpgY3ljobop5LoibLliJfooagKICAgICAgICAgIGlmICghY29tbW9uQmxhbmsodGhpcy5yb2xlTGlzdCkpIHsKICAgICAgICAgICAgdmFyIF9pdGVyYXRvcjMgPSBfY3JlYXRlRm9yT2ZJdGVyYXRvckhlbHBlcih0aGlzLnJvbGVMaXN0KSwKICAgICAgICAgICAgICBfc3RlcDM7CiAgICAgICAgICAgIHRyeSB7CiAgICAgICAgICAgICAgZm9yIChfaXRlcmF0b3IzLnMoKTsgIShfc3RlcDMgPSBfaXRlcmF0b3IzLm4oKSkuZG9uZTspIHsKICAgICAgICAgICAgICAgIHZhciBpdGVtID0gX3N0ZXAzLnZhbHVlOwogICAgICAgICAgICAgICAgaWYgKGl0ZW0uZXh0ZXJuYWxfc3lzdGVtX25vID09PSB2YWwpIHsKICAgICAgICAgICAgICAgICAgdmFyIHJvbGVfaW5mbyA9IHt9OwogICAgICAgICAgICAgICAgICByb2xlX2luZm9bJ3ZhbHVlJ10gPSBpdGVtLnJvbGVfbm87CiAgICAgICAgICAgICAgICAgIHJvbGVfaW5mb1snbGFiZWwnXSA9IGl0ZW0ucm9sZV9ubyArICctJyArIGl0ZW0ucm9sZV9uYW1lOwogICAgICAgICAgICAgICAgICByb2xlQXJyLnB1c2gocm9sZV9pbmZvKTsKICAgICAgICAgICAgICAgIH0KICAgICAgICAgICAgICB9CiAgICAgICAgICAgIH0gY2F0Y2ggKGVycikgewogICAgICAgICAgICAgIF9pdGVyYXRvcjMuZShlcnIpOwogICAgICAgICAgICB9IGZpbmFsbHkgewogICAgICAgICAgICAgIF9pdGVyYXRvcjMuZigpOwogICAgICAgICAgICB9CiAgICAgICAgICB9CiAgICAgICAgICB0aGlzLmRpYWxvZy5mb3JtLmNvbmZpZy5leHRlcm5hbF9kYXRhX25vLm9wdGlvbnMgPSByb2xlQXJyOwogICAgICAgICAgLy8g5q+P5qyh5L+u5pS557O757uf57yW5Y+35pe277yM6YO96KaB5riF56m6ZXh0ZXJuYWxfZGF0YV9ub+eahOWAvAogICAgICAgICAgdGhpcy5kaWFsb2cuZm9ybS5kZWZhdWx0Rm9ybS5leHRlcm5hbF9kYXRhX25vID0gJyc7CiAgICAgICAgICB0aGlzLmRpYWxvZy5mb3JtLmRlZmF1bHRGb3JtLmV4dGVybmFsX2RhdGFfbmFtZSA9ICcnOwogICAgICAgIH0KICAgICAgfSwKICAgICAgZGVlcDogdHJ1ZSAvLyDmt7Hluqbnm5HlkKwKICAgIH0sCgogICAgLy8g57O757uf5pWw5o2u57yW5Y+36IGU5YqoIOezu+e7n+aVsOaNruWQjQogICAgJ2RpYWxvZy5mb3JtLmRlZmF1bHRGb3JtLmV4dGVybmFsX2RhdGFfbm8nOiB7CiAgICAgIGhhbmRsZXI6IGZ1bmN0aW9uIGhhbmRsZXIodmFsKSB7CiAgICAgICAgaWYgKCFjb21tb25CbGFuayh0aGlzLnJvbGVMaXN0KSkgewogICAgICAgICAgdmFyIF9pdGVyYXRvcjQgPSBfY3JlYXRlRm9yT2ZJdGVyYXRvckhlbHBlcih0aGlzLnJvbGVMaXN0KSwKICAgICAgICAgICAgX3N0ZXA0OwogICAgICAgICAgdHJ5IHsKICAgICAgICAgICAgZm9yIChfaXRlcmF0b3I0LnMoKTsgIShfc3RlcDQgPSBfaXRlcmF0b3I0Lm4oKSkuZG9uZTspIHsKICAgICAgICAgICAgICB2YXIgaXRlbSA9IF9zdGVwNC52YWx1ZTsKICAgICAgICAgICAgICBpZiAoaXRlbS5yb2xlX25vID09PSB2YWwpIHsKICAgICAgICAgICAgICAgIHRoaXMuZGlhbG9nLmZvcm0uZGVmYXVsdEZvcm0uZXh0ZXJuYWxfZGF0YV9uYW1lID0gaXRlbS5yb2xlX25hbWU7CiAgICAgICAgICAgICAgfQogICAgICAgICAgICB9CiAgICAgICAgICB9IGNhdGNoIChlcnIpIHsKICAgICAgICAgICAgX2l0ZXJhdG9yNC5lKGVycik7CiAgICAgICAgICB9IGZpbmFsbHkgewogICAgICAgICAgICBfaXRlcmF0b3I0LmYoKTsKICAgICAgICAgIH0KICAgICAgICB9CiAgICAgIH0sCiAgICAgIGRlZXA6IHRydWUgLy8g5rex5bqm55uR5ZCsCiAgICB9CiAgfSwKICBiZWZvcmVDcmVhdGU6IGZ1bmN0aW9uIGJlZm9yZUNyZWF0ZSgpIHsKICAgIHRoYXQgPSB0aGlzOwogIH0sCiAgYmVmb3JlRGVzdHJveTogZnVuY3Rpb24gYmVmb3JlRGVzdHJveSgpIHsKICAgIGNsZWFyVGltZW91dCh0aW1vdXQxKTsKICB9LAogIGNyZWF0ZWQ6IGZ1bmN0aW9uIGNyZWF0ZWQoKSB7CiAgICB0aGlzLmxpc3RMb2FkaW5nID0gdGhpcy5sb2FkaW5nOwogIH0sCiAgbW91bnRlZDogZnVuY3Rpb24gbW91bnRlZCgpIHsKICAgIC8vIOWIneWni+WMlueUqOaIt+WIl+ihqAogICAgdGhpcy5xdWVyeVVzZXJMaXN0KCk7CiAgfSwKICBtZXRob2RzOiB7CiAgICAvLyDooajmoLzpgInmi6nlpJrooYwKICAgIGhhbmRsZVNlbGVjdGlvbkNoYW5nZTogZnVuY3Rpb24gaGFuZGxlU2VsZWN0aW9uQ2hhbmdlKHZhbCkgewogICAgICB2YXIgY3VycmVudFJvdyA9IHZhbDsKICAgICAgaWYgKGN1cnJlbnRSb3cubGVuZ3RoID4gMSkgewogICAgICAgIGN1cnJlbnRSb3cuc29ydChmdW5jdGlvbiAoYSwgYikgewogICAgICAgICAgcmV0dXJuIGEuaW5kZXggLSBiLmluZGV4OwogICAgICAgIH0pOyAvLyDpgInkuK3ooYzmjpLluo8KICAgICAgfQoKICAgICAgdGhpcy50YWJsZS5jdXJyZW50Um93ID0gdmFsOwogICAgfSwKICAgIC8qKg0KICAgICAqIOihjOeahCBjbGFzc05hbWUg55qE5Zue6LCD5pa55rOV77yM5Lmf5Y+v5Lul5L2/55So5a2X56ym5Liy5Li65omA5pyJ6KGM6K6+572u5LiA5Liq5Zu65a6a55qEIGNsYXNzTmFtZSovCiAgICByb3dDbGFzc05hbWU6IGZ1bmN0aW9uIHJvd0NsYXNzTmFtZShfcmVmKSB7CiAgICAgIHZhciByb3cgPSBfcmVmLnJvdywKICAgICAgICByb3dJbmRleCA9IF9yZWYucm93SW5kZXg7CiAgICAgIHJvdy5pbmRleCA9IHJvd0luZGV4OyAvLyDlsIbntKLlvJXmlL7nva7liLByb3fmlbDmja7kuK0KICAgIH0sCiAgICAvKioNCiAgICAgKumhteeggeabtOaWsCAqLwogICAgZ2V0TGlzdDogZnVuY3Rpb24gZ2V0TGlzdChwYXJhbSkgewogICAgICB0aGlzLnF1ZXJ5TGlzdChwYXJhbS5jdXJyZW50UGFnZSk7CiAgICB9LAogICAgLyoqDQogICAgICog5oyJ6ZKu77ya5p+l6K+iKi8KICAgIHF1ZXJ5TGlzdDogZnVuY3Rpb24gcXVlcnlMaXN0KGN1cnJlbnRQYWdlKSB7CiAgICAgIHZhciBfdGhpcyA9IHRoaXM7CiAgICAgIHRoaXMuc2hvd0xvYWRpbmcoKTsKICAgICAgdmFyIG9yZ2FuX25vID0gdGhpcy5kZWZhdWx0Rm9ybS5vcmdhbl9ubzsKICAgICAgLy8g6buY6K6k5p+l6K+i5b2T5YmN5py65p6E5Lul5Y+K5omA5pyJ5LiL6L6W5py65p6E5pWw5o2uCiAgICAgIGlmICh0aGlzLmRlZmF1bHRGb3JtLm9yZ2FuX25vID09PSAnJyB8fCB0aGlzLmRlZmF1bHRGb3JtLm9yZ2FuX25vID09PSBudWxsKSB7CiAgICAgICAgb3JnYW5fbm8gPSAnIycgKyB0aGlzLiRzdG9yZS5nZXR0ZXJzLm9yZ2FuTm87CiAgICAgIH0KICAgICAgdmFyIG1zZyA9IHsKICAgICAgICBwYXJhbWV0ZXJMaXN0OiBbe31dLAogICAgICAgIG9wZXJfdHlwZTogZGljdGlvbmFyeUdldCgnT1BFUkFURV9RVUVSWScpLAogICAgICAgIGN1cnJlbnRQYWdlOiBjdXJyZW50UGFnZSB8fCB0aGlzLnRhYmxlLnBhZ2VMaXN0LmN1cnJlbnRQYWdlLAogICAgICAgIHBhZ2VTaXplOiB0aGlzLnRhYmxlLnBhZ2VMaXN0LnBhZ2VTaXplLAogICAgICAgIC8vIOWPkeW4g+aXpeacn+WkhOeQhgogICAgICAgIGxhc3RfbW9kaV9kYXRlMTogdGhpcy5kZWZhdWx0Rm9ybS5sYXN0X3RpbWUgIT09IG51bGwgPyB0aGlzLmRlZmF1bHRGb3JtLmxhc3RfdGltZVswXSA9PT0gdW5kZWZpbmVkID8gJycgOiB0aGlzLmRlZmF1bHRGb3JtLmxhc3RfdGltZVswXSArICcwMDAwMDAnIC8vIOagvOW8j+WMliB5eXl5TU1kZEhIbW1zcwogICAgICAgIDogJycsCiAgICAgICAgbGFzdF9tb2RpX2RhdGUyOiB0aGlzLmRlZmF1bHRGb3JtLmxhc3RfdGltZSAhPT0gbnVsbCA/IHRoaXMuZGVmYXVsdEZvcm0ubGFzdF90aW1lWzFdID09PSB1bmRlZmluZWQgPyAnJyA6IHRoaXMuZGVmYXVsdEZvcm0ubGFzdF90aW1lWzFdICsgJzI0MDAwMCcgLy8g5qC85byP5YyWIHl5eXlNTWRkSEhtbXNzCiAgICAgICAgOiAnJywKICAgICAgICB1c2VyX25vOiB0aGlzLmRlZmF1bHRGb3JtLnVzZXJfbm8sCiAgICAgICAgZXh0ZXJuYWxfc3lzdGVtX25vOiB0aGlzLmRlZmF1bHRGb3JtLmV4dGVybmFsX3N5c3RlbV9ubywKICAgICAgICBvcmdhbl9ubzogb3JnYW5fbm8sCiAgICAgICAgaW5pdDogdHJ1ZQogICAgICB9OwogICAgICBxdWVyeShtc2cpLnRoZW4oZnVuY3Rpb24gKHJlc3BvbnNlKSB7CiAgICAgICAgdmFyIF9yZXNwb25zZSRyZXRNYXAgPSByZXNwb25zZS5yZXRNYXAsCiAgICAgICAgICByZXR1cm5MaXN0ID0gX3Jlc3BvbnNlJHJldE1hcC5yZXR1cm5MaXN0LAogICAgICAgICAgYWxsUm93ID0gX3Jlc3BvbnNlJHJldE1hcC5hbGxSb3csCiAgICAgICAgICBwYWdlTnVtID0gX3Jlc3BvbnNlJHJldE1hcC5wYWdlTnVtLAogICAgICAgICAgZXh0ZXJuYWxSb2xlTGlzdCA9IF9yZXNwb25zZSRyZXRNYXAuZXh0ZXJuYWxSb2xlTGlzdDsKICAgICAgICBfdGhpcy5yb2xlTGlzdCA9IGV4dGVybmFsUm9sZUxpc3Q7IC8vIOezu+e7n+inkuiJsuWIl+ihqAogICAgICAgIF90aGlzLnRhYmxlLmNvbXBvbmVudFByb3BzLmRhdGEgPSByZXR1cm5MaXN0OwogICAgICAgIF90aGlzLnRhYmxlLnBhZ2VMaXN0LnRvdGFsTnVtID0gYWxsUm93OwogICAgICAgIF90aGlzLnRhYmxlLnBhZ2VMaXN0LmN1cnJlbnRQYWdlID0gcGFnZU51bTsKICAgICAgICBfdGhpcy5zaG93TG9hZGluZygpOwogICAgICB9KS5jYXRjaChmdW5jdGlvbiAoKSB7CiAgICAgICAgX3RoaXMuc2hvd0xvYWRpbmcoKTsKICAgICAgfSk7CiAgICB9LAogICAgLyoqDQogICAgICogYnRuIC0g5paw5aKeKi8KICAgIGhhbmRsZUFkZDogZnVuY3Rpb24gaGFuZGxlQWRkKCkgewogICAgICAvLyDliJ3lp4vljJbooajmoLzmoIfpopjkuI7nsbvlnosKICAgICAgdGhpcy5kaWFsb2cudGl0bGUgPSAn5paw5aKeJzsKICAgICAgdGhpcy5kaWFsb2cub3ByYXRlID0gJ2FkZCc7CiAgICAgIC8vIOiOt+WPluWklumDqOWtl+WFuO+8jOe7meW8ueeqlyDns7vnu5/lj7cg6LWL5YC8CiAgICAgIHRoaXMuZ2V0RXh0ZXJuYWxEYXRhKCk7CiAgICAgIC8vIOaJk+W8gOW8ueeqlwogICAgICB0aGlzLmNoYW5nZVZpc2libGUodHJ1ZSk7CiAgICAgIC8vIOaWsOWinuaXtu+8jOacuuaehOWPt+OAgeeUqOaIt+e8luWPt+S4uuWPr+aTjeS9nAogICAgICB0aGlzLmRpYWxvZy5mb3JtLmNvbmZpZy5vcmdhbl9uby5jb21wb25lbnRQcm9wcy5kaXNhYmxlZCA9IGZhbHNlOwogICAgICB0aGlzLmRpYWxvZy5mb3JtLmNvbmZpZy51c2VyX25vLmNvbXBvbmVudFByb3BzLmRpc2FibGVkID0gZmFsc2U7CiAgICB9LAogICAgLy8gaGFuZGxlQWRkCiAgICAvKioNCiAgICAgKiBidG4gLSDnvJbovpEqLwogICAgaGFuZGxlTW9kaWZ5OiBmdW5jdGlvbiBoYW5kbGVNb2RpZnkoKSB7CiAgICAgIHZhciBfdGhpczIgPSB0aGlzOwogICAgICB2YXIgcm93cyA9IHRoaXMudGFibGUuY3VycmVudFJvdy5sZW5ndGg7CiAgICAgIGlmIChyb3dzID09PSAwKSB7CiAgICAgICAgY29tbW9uTXNnV2Fybign6K+36YCJ5oup6KaB5L+u5pS555qE6KGMJywgdGhpcyk7CiAgICAgICAgcmV0dXJuOwogICAgICB9CiAgICAgIGlmIChyb3dzID49IDIpIHsKICAgICAgICBjb21tb25Nc2dXYXJuKCfkuI3mlK/mjIHlpJrooYzkv67mlLnvvIzor7fph43mlrDpgInmi6knLCB0aGlzKTsKICAgICAgICByZXR1cm47CiAgICAgIH0KICAgICAgdGhpcy5kaWFsb2cub3ByYXRlID0gJ2VkaXQnOwogICAgICB0aGlzLmRpYWxvZy5jb21wb25lbnRQcm9wcy50aXRsZSA9ICfnvJbovpEnOwogICAgICAvLyDojrflj5blpJbpg6jlrZflhbjvvIznu5nlvLnnqpcg57O757uf5Y+3IOi1i+WAvAogICAgICB0aGlzLmdldEV4dGVybmFsRGF0YSgpOwogICAgICAvLyDmiZPlvIDlvLnnqpcKICAgICAgdGhpcy5jaGFuZ2VWaXNpYmxlKHRydWUpOwogICAgICAvLyDmlrDlop7ml7bvvIzmnLrmnoTlj7fjgIHnlKjmiLfnvJblj7fkuLrkuI3lj6/mk43kvZwKICAgICAgdGhpcy5kaWFsb2cuZm9ybS5jb25maWcub3JnYW5fbm8uY29tcG9uZW50UHJvcHMuZGlzYWJsZWQgPSB0cnVlOwogICAgICB0aGlzLmRpYWxvZy5mb3JtLmNvbmZpZy51c2VyX25vLmNvbXBvbmVudFByb3BzLmRpc2FibGVkID0gdHJ1ZTsKICAgICAgdGhpcy4kbmV4dFRpY2soZnVuY3Rpb24gKCkgewogICAgICAgIC8vIOW8ueWHuuahhuWKoOi9veWujOaIkOWQjui1i+WAvAogICAgICAgIF90aGlzMi5kaWFsb2cuZm9ybS5kZWZhdWx0Rm9ybSA9IE9iamVjdC5hc3NpZ24oe30sIF90aGlzMi50YWJsZS5jdXJyZW50Um93WzBdKTsKICAgICAgICB0aW1vdXQxID0gc2V0VGltZW91dChmdW5jdGlvbiAoKSB7CiAgICAgICAgICB2YXIgZXh0ZXJuYWxfZGF0YV9uYW1lID0gX3RoaXMyLnRhYmxlLmN1cnJlbnRSb3dbMF0uZXh0ZXJuYWxfZGF0YV9uYW1lOwogICAgICAgICAgdmFyIF9pdGVyYXRvcjUgPSBfY3JlYXRlRm9yT2ZJdGVyYXRvckhlbHBlcihfdGhpczIucm9sZUxpc3QpLAogICAgICAgICAgICBfc3RlcDU7CiAgICAgICAgICB0cnkgewogICAgICAgICAgICBmb3IgKF9pdGVyYXRvcjUucygpOyAhKF9zdGVwNSA9IF9pdGVyYXRvcjUubigpKS5kb25lOykgewogICAgICAgICAgICAgIHZhciBpdGVtID0gX3N0ZXA1LnZhbHVlOwogICAgICAgICAgICAgIGlmIChpdGVtLnJvbGVfbmFtZSA9PT0gZXh0ZXJuYWxfZGF0YV9uYW1lKSB7CiAgICAgICAgICAgICAgICBfdGhpczIuZGlhbG9nLmZvcm0uZGVmYXVsdEZvcm0uZXh0ZXJuYWxfZGF0YV9ubyA9IGl0ZW0ucm9sZV9ubzsKICAgICAgICAgICAgICAgIF90aGlzMi5kaWFsb2cuZm9ybS5kZWZhdWx0Rm9ybS5leHRlcm5hbF9kYXRhX25hbWUgPSBleHRlcm5hbF9kYXRhX25hbWU7CiAgICAgICAgICAgICAgfQogICAgICAgICAgICB9CiAgICAgICAgICB9IGNhdGNoIChlcnIpIHsKICAgICAgICAgICAgX2l0ZXJhdG9yNS5lKGVycik7CiAgICAgICAgICB9IGZpbmFsbHkgewogICAgICAgICAgICBfaXRlcmF0b3I1LmYoKTsKICAgICAgICAgIH0KICAgICAgICB9LCAxMDApOwogICAgICB9KTsKICAgIH0sCiAgICAvKioNCiAgICAgKiBidG4gLSDliKDpmaQqLwogICAgaGFuZGxlRGVsZXRlOiBmdW5jdGlvbiBoYW5kbGVEZWxldGUoKSB7CiAgICAgIHZhciBfdGhpczMgPSB0aGlzOwogICAgICB2YXIgcm93cyA9IHRoaXMudGFibGUuY3VycmVudFJvdzsKICAgICAgaWYgKHJvd3MubGVuZ3RoID09PSAwKSB7CiAgICAgICAgY29tbW9uTXNnV2Fybign6K+36YCJ5oup6KaB5Yig6Zmk55qE6KGMJywgdGhpcyk7CiAgICAgICAgcmV0dXJuOwogICAgICB9CiAgICAgIC8vIOWkmuadoeaVsOaNruWIoOmZpAogICAgICB2YXIgZGVscyA9IFtdOwogICAgICBmb3IgKHZhciBpID0gMDsgaSA8IHJvd3MubGVuZ3RoOyBpKyspIHsKICAgICAgICBkZWxzID0gW10uY29uY2F0KF90b0NvbnN1bWFibGVBcnJheShkZWxzKSwgW3sKICAgICAgICAgIGV4dGVybmFsX3N5c3RlbV9ubzogcm93c1tpXS5pZCAvLyDpgInkuK3ooYznmoTop5LoibJpZAogICAgICAgIH1dKTsKICAgICAgfQoKICAgICAgY29tbW9uTXNnQ29uZmlybSgn5piv5ZCm56Gu6K6k5Yig6Zmk5b2T5YmN6YCJ5Lit5L+h5oGv77yfJywgdGhpcywgZnVuY3Rpb24gKHBhcmFtKSB7CiAgICAgICAgX3RoaXMzLnNob3dMb2FkaW5nKHRydWUpOwogICAgICAgIGlmIChwYXJhbSkgewogICAgICAgICAgdmFyIG1zZyA9IHsKICAgICAgICAgICAgb3Blcl90eXBlOiBkaWN0aW9uYXJ5R2V0KCdPUEVSQVRFX0RFTEVURScpLAogICAgICAgICAgICBvcGVyYXRpb25fdmFsdWU6IGRlbHMsCiAgICAgICAgICAgIHVzZXJfZXh0ZXJuYWxfZGF0YV9vbGQ6IHJvd3MKICAgICAgICAgIH07CiAgICAgICAgICAvLyBkZWwKICAgICAgICAgIGRlbChtc2cpLnRoZW4oZnVuY3Rpb24gKHJlc3BvbnNlKSB7CiAgICAgICAgICAgIGNvbW1vbk1zZ1N1Y2Nlc3MocmVzcG9uc2UucmV0TXNnLCBfdGhpczMpOwogICAgICAgICAgICBfdGhpczMucXVlcnlMaXN0KDEpOwogICAgICAgICAgfSkuY2F0Y2goZnVuY3Rpb24gKCkge30pOwogICAgICAgIH0KICAgICAgICBfdGhpczMuc2hvd0xvYWRpbmcoZmFsc2UpOwogICAgICB9KTsKICAgIH0sCiAgICAvKioNCiAgICAgKiDlvLnlh7rmoYYgLSDlhbPpl60NCiAgICAgKiBAcGFyYW0ge0Jvb2xlYW59IHBhcmFtIOW8ueWHuuahhuaYvuekuumakOiXj+mFjee9riovCiAgICBjaGFuZ2VWaXNpYmxlOiBmdW5jdGlvbiBjaGFuZ2VWaXNpYmxlKHBhcmFtKSB7CiAgICAgIHRoaXMuZGlhbG9nLnZpc2libGUgPSBwYXJhbTsKICAgIH0sCiAgICAvKioNCiAgICAgKiDlvLnlh7rmoYYgLSDnoa7orqQqLwogICAgZGlhbG9nU3VtYml0OiBmdW5jdGlvbiBkaWFsb2dTdW1iaXQoKSB7CiAgICAgIHZhciBwYXJhbSA9IHRoaXMuZGlhbG9nLm9wcmF0ZTsKICAgICAgaWYgKHBhcmFtID09PSAnYWRkJykgewogICAgICAgIHRoaXMuZGlhbG9nQWRkU3VibWl0KCk7CiAgICAgIH0gZWxzZSB7CiAgICAgICAgdGhpcy5kaWFsb2dFZGl0U3VibWl0KCk7CiAgICAgIH0KICAgIH0sCiAgICAvKioNCiAgICAgKiDlvLnlh7rmoYYgLSDnoa7orqQgLSDmlrDlop4qLwogICAgZGlhbG9nQWRkU3VibWl0OiBmdW5jdGlvbiBkaWFsb2dBZGRTdWJtaXQoKSB7CiAgICAgIHZhciBfdGhpczQgPSB0aGlzOwogICAgICB2YXIgZm9ybURhdGExID0gT2JqZWN0LmFzc2lnbih7fSwgdGhpcy5kaWFsb2cuZm9ybS5kZWZhdWx0Rm9ybSk7CiAgICAgIC8vIOaWsOWinuaXtu+8jOabtOaWsOWJjeeahOaVsOaNruS4uuepugogICAgICBmb3JtRGF0YTEudXNlcl9leHRlcm5hbF9kYXRhX29sZCA9IHt9OwogICAgICB0aGlzLnNob3dMb2FkaW5nKCk7CiAgICAgIHZhciBwYXJhbSA9IHsKICAgICAgICBwYXJhbWV0ZXJMaXN0OiBbXSwKICAgICAgICB1c2VyX2V4dGVybmFsX2RhdGFfbmV3OiBmb3JtRGF0YTEsCiAgICAgICAgdXNlcl9ubzogdGhpcy4kc3RvcmUuZ2V0dGVycy51c2VyTm8sCiAgICAgICAgb3Blcl90eXBlOiBkaWN0aW9uYXJ5R2V0KCdPUEVSQVRFX0FERCcpCiAgICAgIH07CiAgICAgIGFkZChwYXJhbSkudGhlbihmdW5jdGlvbiAocmVzcG9uc2UpIHsKICAgICAgICBfdGhpczQucXVlcnlMaXN0KDEpOyAvLyDph43mlrDmn6Xor6IKICAgICAgICBfdGhpczQuc2hvd0xvYWRpbmcoKTsKICAgICAgICBjb21tb25Nc2dTdWNjZXNzKHJlc3BvbnNlLnJldE1zZywgX3RoaXM0KTsKICAgICAgfSkuY2F0Y2goZnVuY3Rpb24gKCkgewogICAgICAgIF90aGlzNC5zaG93TG9hZGluZygpOwogICAgICB9KTsKICAgICAgdGhpcy5jaGFuZ2VWaXNpYmxlKGZhbHNlKTsKICAgIH0sCiAgICAvKioNCiAgICAgKiDlvLnlh7rmoYYgLSDnoa7orqQgLSDnvJbovpEqLwogICAgZGlhbG9nRWRpdFN1Ym1pdDogZnVuY3Rpb24gZGlhbG9nRWRpdFN1Ym1pdCgpIHsKICAgICAgdmFyIF90aGlzNSA9IHRoaXM7CiAgICAgIGNvbW1vbk1zZ0NvbmZpcm0oJ+aYr+WQpuehruiupOaPkOS6pOW9k+WJjeaVsOaNru+8nycsIHRoaXMsIGZ1bmN0aW9uIChwYXJhbSkgewogICAgICAgIGlmIChwYXJhbSkgewogICAgICAgICAgdmFyIGZvcm1EYXRhMSA9IE9iamVjdC5hc3NpZ24oe30sIF90aGlzNS5kaWFsb2cuZm9ybS5kZWZhdWx0Rm9ybSk7CiAgICAgICAgICBmb3JtRGF0YTEudXNlcl9leHRlcm5hbF9kYXRhX29sZCA9IF90aGlzNS50YWJsZS5jdXJyZW50Um93WzBdOwogICAgICAgICAgX3RoaXM1LnNob3dMb2FkaW5nKCk7CiAgICAgICAgICB2YXIgbXNnID0gewogICAgICAgICAgICBwYXJhbWV0ZXJMaXN0OiBbXSwKICAgICAgICAgICAgb3Blcl90eXBlOiBkaWN0aW9uYXJ5R2V0KCdPUEVSQVRFX01PRElGWScpLAogICAgICAgICAgICB1c2VyX25vOiBfdGhpczUuJHN0b3JlLmdldHRlcnMudXNlck5vLAogICAgICAgICAgICB1c2VyX2V4dGVybmFsX2RhdGFfbmV3OiBmb3JtRGF0YTEKICAgICAgICAgIH07CiAgICAgICAgICBtb2RpZnkobXNnKS50aGVuKGZ1bmN0aW9uIChyZXNwb25zZSkgewogICAgICAgICAgICBjb21tb25Nc2dTdWNjZXNzKHJlc3BvbnNlLnJldE1zZywgX3RoaXM1KTsKICAgICAgICAgICAgX3RoaXM1LnF1ZXJ5TGlzdCgxKTsKICAgICAgICAgICAgX3RoaXM1LnNob3dMb2FkaW5nKCk7CiAgICAgICAgICB9KS5jYXRjaChmdW5jdGlvbiAoKSB7CiAgICAgICAgICAgIF90aGlzNS5zaG93TG9hZGluZygpOwogICAgICAgICAgfSk7CiAgICAgICAgICBfdGhpczUuY2hhbmdlVmlzaWJsZShmYWxzZSk7IC8vIOW8ueWHuuahhuWFs+mXrQogICAgICAgIH0KICAgICAgfSk7CiAgICB9LAogICAgLy8g6I635Y+W5aSW6YOo5a2X5YW4CiAgICBnZXRFeHRlcm5hbERhdGE6IGZ1bmN0aW9uIGdldEV4dGVybmFsRGF0YSgpIHsKICAgICAgLy8g5aSE55CG5aSW6YOo5a2X5YW4bGFiZWzlgLwKICAgICAgdmFyIGFycmF5U3RhdGUgPSB0aGlzLiRzdG9yZS5nZXR0ZXJzLmV4dGVybmFsRGF0YS5FWFRFUk5BTF9TWVNURU1fTk87CiAgICAgIC8vIGNvbnN0IFVTRVJfTk8gPSB0aGlzLiRzdG9yZS5nZXR0ZXJzLmV4dGVybmFsRGF0YS5VU0VSX05PCiAgICAgIHZhciBTeXN0ZW1BcnJheSA9IFtdOwogICAgICBhcnJheVN0YXRlLm1hcChmdW5jdGlvbiAoaXRlbSkgewogICAgICAgIHZhciB2YWx1ZVMgPSBPYmplY3QuYXNzaWduKHt9LCBpdGVtLCB7CiAgICAgICAgICBsYWJlbDogaXRlbS52YWx1ZSArICctJyArIGl0ZW0ubGFiZWwKICAgICAgICB9KTsKICAgICAgICBTeXN0ZW1BcnJheS5wdXNoKHZhbHVlUyk7CiAgICAgIH0pOwogICAgICAvLyDojrflj5blpJbpg6jlrZflhbgKICAgICAgdGhpcy5kaWFsb2cuZm9ybS5jb25maWcuZXh0ZXJuYWxfc3lzdGVtX25vLm9wdGlvbnMgPSBTeXN0ZW1BcnJheTsKICAgIH0sCiAgICAvKioNCiAgICAgKiDliJ3lp4vljJbnlKjmiLfliJfooagNCiAgICAgKi8KICAgIHF1ZXJ5VXNlckxpc3Q6IGZ1bmN0aW9uIHF1ZXJ5VXNlckxpc3QoKSB7CiAgICAgIHZhciBfdGhpczYgPSB0aGlzOwogICAgICB2YXIgbXNnID0gewogICAgICAgIHBhcmFtZXRlckxpc3Q6IFtdLAogICAgICAgIG9wZXJfdHlwZTogZGljdGlvbmFyeUdldCgnT1BFUkFURV9RVUVSWScpLAogICAgICAgIG1ldGhvZDogJ3F1ZXJ5VXNlckxpc3QnCiAgICAgIH07CiAgICAgIHF1ZXJ5VXNlcihtc2cpLnRoZW4oZnVuY3Rpb24gKHJlc3BvbnNlKSB7CiAgICAgICAgdmFyIHVzZXJMaXN0ID0gcmVzcG9uc2UucmV0TWFwLnVzZXJMaXN0OwogICAgICAgIF90aGlzNi51c2VyTGlzdCA9IHVzZXJMaXN0OwogICAgICB9KTsKICAgIH0sCiAgICAvKioNCiAgICAgKiDliqDovb3kuK3liqjnlLvphY3nva4NCiAgICAgKiBAcGFyYW0ge0Jvb2xlYW59cGFyYW0g5b2T5YmN5Yqg6L295pi+56S654q25oCBKi8KICAgIHNob3dMb2FkaW5nOiBmdW5jdGlvbiBzaG93TG9hZGluZyhwYXJhbSkgewogICAgICB0aGlzLnRhYmxlLmxvYWRpbmcgPSBwYXJhbTsKICAgIH0KICB9Cn07"}, {"version": 3, "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA0DA,SACAA,kBACAC,eACAC,wBACA;AACA;AACA;AACA;AAEA;;AAEA;;AAEA;AACA;EAAAC;EAAAC;EAAAC;EAAAC;EAAAC;AACA;AACA;AACA;EACAC;EACAC;IACA;AACA;AACA;IACAC;MACA;MACAC;QACA;UACAC;QACA;MACA;MACA;IACA;IACA;AACA;AACA;IACAC;MACA;MACAF;QACA;UACAC;QACA;MACA;MACA;IACA;EACA;EACAE;EACAC;IACAC;MACAC;MACAC;QACA;MACA;IACA;IACAC;MACAF;MACAC;QACA;MACA;IACA;IACAE;MACAH;MACAC;QACA;MACA;IACA;EACA;EACAG;IACA;MACAC;QACAC;QACAC;QACAC;QACAC;QAAA;QACAC;QAAA;QACAC;UACAP;UAAA;UACAQ;UACAC;QACA;;QACAC;QAAA;QACAC;UACAC;UACAC;UAAA;UACAC;QACA;MACA;;MACAC;QACAC;UACAC;QACA;QACAC;UACAD;QACA;QACAE;UACAF;QACA;MACA;MACAG;QACAC;QACAC;QACAf;UACA;UACAgB;UACAC;QACA;;QACAC;UACAC;UACAC;UACAhC;YACAiC;YACAC;YACAC;YACAC;YACAC;YACAC;YACAC;YACAC;UACA;QACA;MACA;MACAC;MAAA;MACAC;IACA;EACA;;EACAC;IACA;IACA;MACAC;QACA;QACA;QACA;QAAA,2CACA;UAAA;QAAA;UAAA;YAAA;YACA;cACA;cACAC;cACAA;cACAC;YACA;UACA;QAAA;UAAA;QAAA;UAAA;QAAA;QACA;QACA;QACA;UACA;QACA;MACA;MACAC;IACA;;IACA;IACA;MACAH;QAAA,4CACA;UAAA;QAAA;UAAA;YAAA;YACA;cACA;YACA;UACA;QAAA;UAAA;QAAA;UAAA;QAAA;MACA;MACAG;IACA;;IACA;IACA;MACAH;QACA;QACA;UACA;UACA;YAAA,4CACA;cAAA;YAAA;cAAA;gBAAA;gBACA;kBACA;kBACAI;kBACAA;kBACAC;gBACA;cACA;YAAA;cAAA;YAAA;cAAA;YAAA;UACA;UACA;UACA;UACA;UACA;QACA;MACA;MACAF;IACA;;IACA;IACA;MACAH;QACA;UAAA,4CACA;YAAA;UAAA;YAAA;cAAA;cACA;gBACA;cACA;YACA;UAAA;YAAA;UAAA;YAAA;UAAA;QACA;MACA;MACAG;IACA;EACA;EACAG;IACAvD;EACA;EACAwD;IACAC;EACA;EACAC;IACA;EACA;EACAC;IACA;IACA;EACA;EACAC;IACA;IACAC;MACA;MACA;QACAzC;UACA;QACA;MACA;;MACA;IACA;IACA;AACA;IACA0C;MAAA;QAAAC;MACAC;IACA;IACA;AACA;IACAC;MACA;IACA;IACA;AACA;IACAC;MAAA;MACA;MACA;MACA;MACA,IACA,oCACA,oCACA;QACA5B;MACA;MACA;QACA6B;QACAC;QACA7C;QACAC;QACA;QACA6C,iBACA,sCACA,8CACA,KACA;QAAA,EACA;QACAC,iBACA,sCACA,8CACA,KACA;QAAA,EACA;QACA9B;QACAE;QACAJ;QACAiC;MACA;MACA/E,WACAgF;QACA,uBACAC;UADAC;UAAAC;UAAAC;UAAAC;QAEA;QACA;QACA;QACA;QACA;MACA,GACAC;QACA;MACA;IACA;IACA;AACA;IACAC;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;IACA;IAAA;IACA;AACA;IACAC;MAAA;MACA;MACA;QACA1F;QACA;MACA;MACA;QACAA;QACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;QACA;QACA,+CACA,IACA,2BACA;QACA2F;UACA;UAAA,4CACA;YAAA;UAAA;YAAA;cAAA;cACA;gBACA;gBACA,oDACApC;cACA;YACA;UAAA;YAAA;UAAA;YAAA;UAAA;QACA;MACA;IACA;IACA;AACA;IACAqC;MAAA;MACA;MACA;QACA5F;QACA;MACA;MACA;MACA;MACA;QACA6F,oCACAA,QACA;UACAzC;QACA,GACA;MACA;;MACAnD;QACA;QACA;UACA;YACA6E;YACAgB;YACAC;UACA;UACA;UACAzF,SACA4E;YACAnF;YACA;UACA,GACAyF;QACA;QACA;MACA;IACA;IACA;AACA;AACA;IACAQ;MACA;IACA;IACA;AACA;IACAC;MACA;MACA;QACA;MACA;QACA;MACA;IACA;IACA;AACA;IACAC;MAAA;MACA;MACA;MACAC;MACA;MACA;QACAtB;QACAuB;QACAlD;QACA4B;MACA;MACA1E,WACA8E;QACA;QACA;QACAnF;MACA,GACAyF;QACA;MACA;MACA;IACA;IACA;AACA;IACAa;MAAA;MACApG;QACA;UACA;UACAkG;UACA;UACA;YACAtB;YACAC;YACA5B;YACAkD;UACA;UACA/F,YACA6E;YACAnF;YACA;YACA;UACA,GACAyF;YACA;UACA;UACA;QACA;MACA;IACA;IACA;IACAc;MACA;MACA;MACA;MACA;MACAC;QACA;UACAC;QACA;QACAC;MACA;MACA;MACA;IACA;IACA;AACA;AACA;IACAC;MAAA;MACA;QACA7B;QACAC;QACA6B;MACA;MACAxG;QACA;QACA;MACA;IACA;IACA;AACA;AACA;IACAyG;MACA;IACA;EACA;AACA", "names": ["commonMsgSuccess", "commonMsgWarn", "commonMsgConfirm", "query", "queryUser", "add", "modify", "del", "name", "filters", "externalSystemTpye", "that", "valueS", "userNoTpye", "mixins", "props", "defaultForm", "type", "default", "systemArray", "userNoArr", "data", "table", "columns", "ref", "loading", "selection", "indexNumber", "componentProps", "height", "formRow", "currentRow", "pageList", "totalNum", "currentPage", "pageSize", "btnDatas", "btnAdd", "show", "btnDelete", "btnModify", "dialog", "visible", "oprate", "title", "width", "form", "labelWidth", "config", "organ_no", "organ_name", "user_no", "user_name", "external_system_no", "data_type", "external_data_no", "external_data_name", "userList", "roleList", "watch", "handler", "user_info", "userArr", "deep", "role_info", "roleArr", "beforeCreate", "<PERSON><PERSON><PERSON><PERSON>", "clearTimeout", "created", "mounted", "methods", "handleSelectionChange", "rowClassName", "rowIndex", "row", "getList", "queryList", "parameterList", "oper_type", "last_modi_date1", "last_modi_date2", "init", "then", "response", "returnList", "allRow", "pageNum", "externalRoleList", "catch", "handleAdd", "handleModify", "timout1", "handleDelete", "dels", "operation_value", "user_external_data_old", "changeVisible", "dialogSumbit", "dialogAddSubmit", "formData1", "user_external_data_new", "dialogEditSubmit", "getExternalData", "arrayState", "label", "SystemArray", "queryUserList", "method", "showLoading"], "sourceRoot": "src/views/system/externalManage/external/component/table", "sources": ["index.vue"], "sourcesContent": ["<!--\r\n* 用户关联系统管理: 表格\r\n-->\r\n<template>\r\n  <div class=\"sun-content\">\r\n    <sun-table\r\n      :table-config=\"table\"\r\n      @selection-change=\"handleSelectionChange\"\r\n      @pagination=\"getList\"\r\n    >\r\n      <template slot=\"tableColumn\">\r\n        <el-table-column\r\n          v-for=\"item in table.columns\"\r\n          :key=\"item.id\"\r\n          :prop=\"item.name\"\r\n          :label=\"item.label\"\r\n          :width=\"item.width\"\r\n        >\r\n          <div slot-scope=\"{ row }\">\r\n            <span\r\n              v-if=\"item.name === 'external_system_no'\"\r\n              class=\"textOverflow\"\r\n              :title=\"row[item.name] | externalSystemTpye\"\r\n            >{{ row[item.name] | externalSystemTpye }}</span>\r\n            <span v-else-if=\"item.name === 'user_no'\">{{\r\n              row[item.name] | userNoTpye\r\n            }}</span>\r\n            <span\r\n              v-else-if=\"item.name === 'organ_no'\"\r\n              class=\"textOverflow\"\r\n              :title=\"row[item.name] | organNameFormat\"\r\n            >{{ row[item.name] | organNameFormat }}</span>\r\n            <span\r\n              v-else-if=\"item.name === 'last_modi_date'\"\r\n              class=\"textOverflow\"\r\n              :title=\"row[item.name] | dateTimeFormat\"\r\n            >{{ row[item.name] | dateTimeFormat }}</span>\r\n            <span v-else>{{ row[item.name] }}</span>\r\n          </div>\r\n        </el-table-column>\r\n      </template>\r\n      <template slot=\"customButton\">\r\n        <sun-button\r\n          :btn-datas=\"btnDatas\"\r\n          @handleAdd=\"handleAdd\"\r\n          @handleModify=\"handleModify\"\r\n          @handleDelete=\"handleDelete\"\r\n        />\r\n      </template>\r\n    </sun-table>\r\n    <sun-form-dialog\r\n      :dialog-config=\"dialog\"\r\n      @dialogClose=\"changeVisible\"\r\n      @dialogSubmit=\"dialogSumbit\"\r\n    /><!--新增、修改弹出框-->\r\n  </div>\r\n</template>\r\n<script>\r\nimport {\r\n  commonMsgSuccess,\r\n  commonMsgWarn,\r\n  commonMsgConfirm\r\n} from '@/utils/message.js' // 提示信息\r\nimport { dictionaryGet } from '@/utils/dictionary.js' // 字典常量\r\nimport ResizeMixin from '@/utils/ResizeHandler' // 整体页面是否根据总高配置\r\nimport { commonBlank } from '@/utils/common'\r\n\r\nimport { config, configTable } from './info' // 表头、表单配置\r\n\r\nimport { organNoFormat } from '@/filters' // 指令\r\n\r\nimport { system } from '@/api'\r\nconst { query, queryUser, add, modify, del } = system.SysExterExternal\r\nlet that\r\nlet timout1\r\nexport default {\r\n  name: 'TableList',\r\n  filters: {\r\n    /**\r\n     * 自定义外表数据源\r\n     * @param {String} string 数据源的值*/\r\n    externalSystemTpye(string) {\r\n      let valueS = ''\r\n      that.systemArray.map(function(item) {\r\n        if (item.value === string) {\r\n          valueS = item.label\r\n        }\r\n      })\r\n      return valueS\r\n    },\r\n    /**\r\n     * 自定义外表数据源\r\n     * @param {String} string 数据源的值*/\r\n    userNoTpye(string) {\r\n      let valueS = ''\r\n      that.userNoArr.map(function(item) {\r\n        if (item.value === string) {\r\n          valueS = item.value + '-' + item.label\r\n        }\r\n      })\r\n      return valueS\r\n    }\r\n  },\r\n  mixins: [ResizeMixin],\r\n  props: {\r\n    defaultForm: {\r\n      type: Object,\r\n      default: function() {\r\n        return {}\r\n      }\r\n    },\r\n    systemArray: {\r\n      type: Array,\r\n      default: function() {\r\n        return []\r\n      }\r\n    },\r\n    userNoArr: {\r\n      type: Array,\r\n      default: function() {\r\n        return []\r\n      }\r\n    }\r\n  },\r\n  data() {\r\n    return {\r\n      table: {\r\n        columns: configTable(),\r\n        ref: 'tableRef',\r\n        loading: false,\r\n        selection: true, // 复选\r\n        indexNumber: true, // 序号\r\n        componentProps: {\r\n          data: [], // 表格数据\r\n          height: '100px',\r\n          formRow: 1 // 表单行数\r\n        },\r\n        currentRow: [], // 选中行\r\n        pageList: {\r\n          totalNum: 0,\r\n          currentPage: 1, // 当前页\r\n          pageSize: this.$store.getters.pageSize // 当前页显示条数\r\n        }\r\n      },\r\n      btnDatas: {\r\n        btnAdd: {\r\n          show: this.$attrs['btn-all'].btnAdd\r\n        },\r\n        btnDelete: {\r\n          show: this.$attrs['btn-all'].btnDelete\r\n        },\r\n        btnModify: {\r\n          show: this.$attrs['btn-all'].btnModify\r\n        }\r\n      },\r\n      dialog: {\r\n        visible: false,\r\n        oprate: 'add',\r\n        componentProps: {\r\n          // 弹出框配置属性\r\n          title: '新增',\r\n          width: '50%' // 当前弹出框宽度\r\n        },\r\n        form: {\r\n          labelWidth: '12rem',\r\n          config: config(this),\r\n          defaultForm: {\r\n            organ_no: '',\r\n            organ_name: '',\r\n            user_no: [],\r\n            user_name: '',\r\n            external_system_no: [],\r\n            data_type: '0',\r\n            external_data_no: [],\r\n            external_data_name: ''\r\n          }\r\n        }\r\n      },\r\n      userList: [], // 用户列表\r\n      roleList: [] // 系统角色列表\r\n    }\r\n  },\r\n  watch: {\r\n    // 机构号联动 机构名称、用户编号\r\n    'dialog.form.defaultForm.organ_no': {\r\n      handler(val) {\r\n        this.dialog.form.defaultForm.organ_name = organNoFormat(val)\r\n        // 遍历用户列表\r\n        const userArr = []\r\n        for (const item of this.userList) {\r\n          if (item.organ_no === val) {\r\n            const user_info = {}\r\n            user_info['value'] = item.user_no\r\n            user_info['label'] = item.user_no + '-' + item.user_name\r\n            userArr.push(user_info)\r\n          }\r\n        }\r\n        this.dialog.form.config.user_no.options = userArr\r\n        // 联动时赋初始值\r\n        if (userArr.length > 0) {\r\n          this.dialog.form.defaultForm.user_no = userArr[0].value\r\n        }\r\n      },\r\n      deep: true // 深度监听\r\n    },\r\n    // 用户号联动 用户名\r\n    'dialog.form.defaultForm.user_no': {\r\n      handler(val) {\r\n        for (const item of this.userList) {\r\n          if (item.user_no === val) {\r\n            this.dialog.form.defaultForm.user_name = item.user_name\r\n          }\r\n        }\r\n      },\r\n      deep: true // 深度监听\r\n    },\r\n    // 系统编号联动 系统数据编号\r\n    'dialog.form.defaultForm.external_system_no': {\r\n      handler(val) {\r\n        const roleArr = []\r\n        if (val) {\r\n          // 遍历角色列表\r\n          if (!commonBlank(this.roleList)) {\r\n            for (const item of this.roleList) {\r\n              if (item.external_system_no === val) {\r\n                const role_info = {}\r\n                role_info['value'] = item.role_no\r\n                role_info['label'] = item.role_no + '-' + item.role_name\r\n                roleArr.push(role_info)\r\n              }\r\n            }\r\n          }\r\n          this.dialog.form.config.external_data_no.options = roleArr\r\n          // 每次修改系统编号时，都要清空external_data_no的值\r\n          this.dialog.form.defaultForm.external_data_no = ''\r\n          this.dialog.form.defaultForm.external_data_name = ''\r\n        }\r\n      },\r\n      deep: true // 深度监听\r\n    },\r\n    // 系统数据编号联动 系统数据名\r\n    'dialog.form.defaultForm.external_data_no': {\r\n      handler(val) {\r\n        if (!commonBlank(this.roleList)) {\r\n          for (const item of this.roleList) {\r\n            if (item.role_no === val) {\r\n              this.dialog.form.defaultForm.external_data_name = item.role_name\r\n            }\r\n          }\r\n        }\r\n      },\r\n      deep: true // 深度监听\r\n    }\r\n  },\r\n  beforeCreate() {\r\n    that = this\r\n  },\r\n  beforeDestroy() {\r\n    clearTimeout(timout1)\r\n  },\r\n  created() {\r\n    this.listLoading = this.loading\r\n  },\r\n  mounted() {\r\n    // 初始化用户列表\r\n    this.queryUserList()\r\n  },\r\n  methods: {\r\n    // 表格选择多行\r\n    handleSelectionChange(val) {\r\n      const currentRow = val\r\n      if (currentRow.length > 1) {\r\n        currentRow.sort(function(a, b) {\r\n          return a.index - b.index\r\n        }) // 选中行排序\r\n      }\r\n      this.table.currentRow = val\r\n    },\r\n    /**\r\n     * 行的 className 的回调方法，也可以使用字符串为所有行设置一个固定的 className*/\r\n    rowClassName({ row, rowIndex }) {\r\n      row.index = rowIndex // 将索引放置到row数据中\r\n    },\r\n    /**\r\n     *页码更新 */\r\n    getList(param) {\r\n      this.queryList(param.currentPage)\r\n    },\r\n    /**\r\n     * 按钮：查询*/\r\n    queryList(currentPage) {\r\n      this.showLoading()\r\n      let organ_no = this.defaultForm.organ_no\r\n      // 默认查询当前机构以及所有下辖机构数据\r\n      if (\r\n        this.defaultForm.organ_no === '' ||\r\n        this.defaultForm.organ_no === null\r\n      ) {\r\n        organ_no = '#' + this.$store.getters.organNo\r\n      }\r\n      const msg = {\r\n        parameterList: [{}],\r\n        oper_type: dictionaryGet('OPERATE_QUERY'),\r\n        currentPage: currentPage || this.table.pageList.currentPage,\r\n        pageSize: this.table.pageList.pageSize,\r\n        // 发布日期处理\r\n        last_modi_date1:\r\n          this.defaultForm.last_time !== null\r\n            ? this.defaultForm.last_time[0] === undefined\r\n              ? ''\r\n              : this.defaultForm.last_time[0] + '000000' // 格式化 yyyyMMddHHmmss\r\n            : '',\r\n        last_modi_date2:\r\n          this.defaultForm.last_time !== null\r\n            ? this.defaultForm.last_time[1] === undefined\r\n              ? ''\r\n              : this.defaultForm.last_time[1] + '240000' // 格式化 yyyyMMddHHmmss\r\n            : '',\r\n        user_no: this.defaultForm.user_no,\r\n        external_system_no: this.defaultForm.external_system_no,\r\n        organ_no: organ_no,\r\n        init: true\r\n      }\r\n      query(msg)\r\n        .then((response) => {\r\n          const { returnList, allRow, pageNum, externalRoleList } =\r\n            response.retMap\r\n          this.roleList = externalRoleList // 系统角色列表\r\n          this.table.componentProps.data = returnList\r\n          this.table.pageList.totalNum = allRow\r\n          this.table.pageList.currentPage = pageNum\r\n          this.showLoading()\r\n        })\r\n        .catch(() => {\r\n          this.showLoading()\r\n        })\r\n    },\r\n    /**\r\n     * btn - 新增*/\r\n    handleAdd() {\r\n      // 初始化表格标题与类型\r\n      this.dialog.title = '新增'\r\n      this.dialog.oprate = 'add'\r\n      // 获取外部字典，给弹窗 系统号 赋值\r\n      this.getExternalData()\r\n      // 打开弹窗\r\n      this.changeVisible(true)\r\n      // 新增时，机构号、用户编号为可操作\r\n      this.dialog.form.config.organ_no.componentProps.disabled = false\r\n      this.dialog.form.config.user_no.componentProps.disabled = false\r\n    }, // handleAdd\r\n    /**\r\n     * btn - 编辑*/\r\n    handleModify() {\r\n      const rows = this.table.currentRow.length\r\n      if (rows === 0) {\r\n        commonMsgWarn('请选择要修改的行', this)\r\n        return\r\n      }\r\n      if (rows >= 2) {\r\n        commonMsgWarn('不支持多行修改，请重新选择', this)\r\n        return\r\n      }\r\n      this.dialog.oprate = 'edit'\r\n      this.dialog.componentProps.title = '编辑'\r\n      // 获取外部字典，给弹窗 系统号 赋值\r\n      this.getExternalData()\r\n      // 打开弹窗\r\n      this.changeVisible(true)\r\n      // 新增时，机构号、用户编号为不可操作\r\n      this.dialog.form.config.organ_no.componentProps.disabled = true\r\n      this.dialog.form.config.user_no.componentProps.disabled = true\r\n      this.$nextTick(() => {\r\n        // 弹出框加载完成后赋值\r\n        this.dialog.form.defaultForm = Object.assign(\r\n          {},\r\n          this.table.currentRow[0]\r\n        )\r\n        timout1 = setTimeout(() => {\r\n          const external_data_name = this.table.currentRow[0].external_data_name\r\n          for (const item of this.roleList) {\r\n            if (item.role_name === external_data_name) {\r\n              this.dialog.form.defaultForm.external_data_no = item.role_no\r\n              this.dialog.form.defaultForm.external_data_name =\r\n                external_data_name\r\n            }\r\n          }\r\n        }, 100)\r\n      })\r\n    },\r\n    /**\r\n     * btn - 删除*/\r\n    handleDelete() {\r\n      const rows = this.table.currentRow\r\n      if (rows.length === 0) {\r\n        commonMsgWarn('请选择要删除的行', this)\r\n        return\r\n      }\r\n      // 多条数据删除\r\n      let dels = []\r\n      for (let i = 0; i < rows.length; i++) {\r\n        dels = [\r\n          ...dels,\r\n          {\r\n            external_system_no: rows[i].id // 选中行的角色id\r\n          }\r\n        ]\r\n      }\r\n      commonMsgConfirm('是否确认删除当前选中信息？', this, (param) => {\r\n        this.showLoading(true)\r\n        if (param) {\r\n          const msg = {\r\n            oper_type: dictionaryGet('OPERATE_DELETE'),\r\n            operation_value: dels,\r\n            user_external_data_old: rows\r\n          }\r\n          // del\r\n          del(msg)\r\n            .then((response) => {\r\n              commonMsgSuccess(response.retMsg, this)\r\n              this.queryList(1)\r\n            })\r\n            .catch(() => {})\r\n        }\r\n        this.showLoading(false)\r\n      })\r\n    },\r\n    /**\r\n     * 弹出框 - 关闭\r\n     * @param {Boolean} param 弹出框显示隐藏配置*/\r\n    changeVisible(param) {\r\n      this.dialog.visible = param\r\n    },\r\n    /**\r\n     * 弹出框 - 确认*/\r\n    dialogSumbit() {\r\n      const param = this.dialog.oprate\r\n      if (param === 'add') {\r\n        this.dialogAddSubmit()\r\n      } else {\r\n        this.dialogEditSubmit()\r\n      }\r\n    },\r\n    /**\r\n     * 弹出框 - 确认 - 新增*/\r\n    dialogAddSubmit() {\r\n      const formData1 = Object.assign({}, this.dialog.form.defaultForm)\r\n      // 新增时，更新前的数据为空\r\n      formData1.user_external_data_old = {}\r\n      this.showLoading()\r\n      const param = {\r\n        parameterList: [],\r\n        user_external_data_new: formData1,\r\n        user_no: this.$store.getters.userNo,\r\n        oper_type: dictionaryGet('OPERATE_ADD')\r\n      }\r\n      add(param)\r\n        .then((response) => {\r\n          this.queryList(1) // 重新查询\r\n          this.showLoading()\r\n          commonMsgSuccess(response.retMsg, this)\r\n        })\r\n        .catch(() => {\r\n          this.showLoading()\r\n        })\r\n      this.changeVisible(false)\r\n    },\r\n    /**\r\n     * 弹出框 - 确认 - 编辑*/\r\n    dialogEditSubmit() {\r\n      commonMsgConfirm('是否确认提交当前数据？', this, (param) => {\r\n        if (param) {\r\n          const formData1 = Object.assign({}, this.dialog.form.defaultForm)\r\n          formData1.user_external_data_old = this.table.currentRow[0]\r\n          this.showLoading()\r\n          const msg = {\r\n            parameterList: [],\r\n            oper_type: dictionaryGet('OPERATE_MODIFY'),\r\n            user_no: this.$store.getters.userNo,\r\n            user_external_data_new: formData1\r\n          }\r\n          modify(msg)\r\n            .then((response) => {\r\n              commonMsgSuccess(response.retMsg, this)\r\n              this.queryList(1)\r\n              this.showLoading()\r\n            })\r\n            .catch(() => {\r\n              this.showLoading()\r\n            })\r\n          this.changeVisible(false) // 弹出框关闭\r\n        }\r\n      })\r\n    },\r\n    // 获取外部字典\r\n    getExternalData() {\r\n      // 处理外部字典label值\r\n      const arrayState = this.$store.getters.externalData.EXTERNAL_SYSTEM_NO\r\n      // const USER_NO = this.$store.getters.externalData.USER_NO\r\n      const SystemArray = []\r\n      arrayState.map(function(item) {\r\n        const valueS = Object.assign({}, item, {\r\n          label: item.value + '-' + item.label\r\n        })\r\n        SystemArray.push(valueS)\r\n      })\r\n      // 获取外部字典\r\n      this.dialog.form.config.external_system_no.options = SystemArray\r\n    },\r\n    /**\r\n     * 初始化用户列表\r\n     */\r\n    queryUserList() {\r\n      const msg = {\r\n        parameterList: [],\r\n        oper_type: dictionaryGet('OPERATE_QUERY'),\r\n        method: 'queryUserList'\r\n      }\r\n      queryUser(msg).then((response) => {\r\n        const { userList } = response.retMap\r\n        this.userList = userList\r\n      })\r\n    },\r\n    /**\r\n     * 加载中动画配置\r\n     * @param {Boolean}param 当前加载显示状态*/\r\n    showLoading(param) {\r\n      this.table.loading = param\r\n    }\r\n  }\r\n}\r\n</script>\r\n<style lang=\"scss\" scoped></style>\r\n"]}]}