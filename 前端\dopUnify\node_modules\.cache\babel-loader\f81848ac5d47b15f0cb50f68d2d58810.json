{"remainingRequest": "E:\\华南区项目\\中国银行\\开发区\\科技公司运营平台\\前端\\dopUnify\\node_modules\\babel-loader\\lib\\index.js!E:\\华南区项目\\中国银行\\开发区\\科技公司运营平台\\前端\\dopUnify\\node_modules\\eslint-loader\\index.js??ref--13-0!E:\\华南区项目\\中国银行\\开发区\\科技公司运营平台\\前端\\dopUnify\\src\\views\\system\\outManage\\registration\\component\\table\\info.js", "dependencies": [{"path": "E:\\华南区项目\\中国银行\\开发区\\科技公司运营平台\\前端\\dopUnify\\src\\views\\system\\outManage\\registration\\component\\table\\info.js", "mtime": 1721631954662}, {"path": "E:\\华南区项目\\中国银行\\开发区\\科技公司运营平台\\前端\\dopUnify\\babel.config.js", "mtime": 1721631954662}, {"path": "E:\\华南区项目\\中国银行\\开发区\\科技公司运营平台\\前端\\dopUnify\\.babelrc", "mtime": 1721631954662}, {"path": "E:\\华南区项目\\中国银行\\开发区\\科技公司运营平台\\前端\\dopUnify\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "E:\\华南区项目\\中国银行\\开发区\\科技公司运营平台\\前端\\dopUnify\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "E:\\华南区项目\\中国银行\\开发区\\科技公司运营平台\\前端\\dopUnify\\node_modules\\eslint-loader\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:aW1wb3J0IHsgdjEgYXMgdXVpZHYxIH0gZnJvbSAndXVpZCc7Ci8vIOihqOWktApleHBvcnQgdmFyIGNvbmZpZ1RhYmxlID0gZnVuY3Rpb24gY29uZmlnVGFibGUodGhhdCkgewogIHJldHVybiBbewogICAgbmFtZTogJ29yZGVyX25vJywKICAgIGxhYmVsOiAn5rWB5rC05Y+3JywKICAgIGlkOiB1dWlkdjEoKQogIH0sIHsKICAgIG5hbWU6ICdzeXNfaWQnLAogICAgbGFiZWw6ICfns7vnu5/moIfor4YnLAogICAgaWQ6IHV1aWR2MSgpCiAgfSwgewogICAgbmFtZTogJ3RkX25vJywKICAgIGxhYmVsOiAn5o6l5Y+j5qCH6K+GJywKICAgIGlkOiB1dWlkdjEoKQogIH0sIHsKICAgIG5hbWU6ICd0cmFuc190aW1lJywKICAgIGxhYmVsOiAn5aSE55CG5pe26ZW/77yI5q+r56eS77yJJywKICAgIHdpZHRoOiAyNDAsCiAgICBpZDogdXVpZHYxKCkKICB9LCB7CiAgICBuYW1lOiAnbGFzdF9tb2RpX2RhdGUnLAogICAgbGFiZWw6ICfmnIDlkI7kv67mlLnml6XmnJ8nLAogICAgaWQ6IHV1aWR2MSgpCiAgfV07Cn07CgovLyDor6bmg4XlvLnlh7rmoYbooajljZUKZXhwb3J0IHZhciBjb25maWcgPSBmdW5jdGlvbiBjb25maWcodGhhdCkgewogIHJldHVybiB7CiAgICBmb3JtX2RhdGExOiB7CiAgICAgIGNvbXBvbmVudDogJ3RleHRhcmVhJywKICAgICAgbGFiZWw6ICfor7fmsYLmiqXmlocnLAogICAgICBjb2xTcGFuOiAyNCwKICAgICAgbmFtZTogJ2Zvcm1fZGF0YTEnLAogICAgICBjb25maWc6IHsKICAgICAgICAvLyBmb3JtLWl0ZW0g6YWN572uCiAgICAgICAgLy8gcnVsZXM6IFt7IHJlcXVpcmVkOiB0cnVlLCBtZXNzYWdlOiAn5o6l5Y+j5qCH6K+G5Li65b+F6L6TJyB9XQogICAgICB9LAogICAgICBjb21wb25lbnRQcm9wczogewogICAgICAgIC8vIGlucHV057uE5Lu26YWN572uCiAgICAgICAgZGlzYWJsZWQ6IHRydWUsCiAgICAgICAgcm93czogNwogICAgICB9LAogICAgICBvcHRpb25zOiBbXQogICAgfSwKICAgIGZvcm1fZGF0YTI6IHsKICAgICAgY29tcG9uZW50OiAndGV4dGFyZWEnLAogICAgICBsYWJlbDogJ+i/lOWbnuaKpeaWhycsCiAgICAgIGNvbFNwYW46IDI0LAogICAgICBuYW1lOiAnZm9ybV9kYXRhMicsCiAgICAgIGNvbmZpZzogewogICAgICAgIC8vIGZvcm0taXRlbSDphY3nva4KICAgICAgICAvLyBydWxlczogW3sgcmVxdWlyZWQ6IHRydWUsIG1lc3NhZ2U6ICfmjqXlj6PliIbnsbvkuLrlv4XovpMnIH1dCiAgICAgIH0sCiAgICAgIGNvbXBvbmVudFByb3BzOiB7CiAgICAgICAgLy8gaW5wdXTnu4Tku7bphY3nva4KICAgICAgICBkaXNhYmxlZDogdHJ1ZSwKICAgICAgICByb3dzOiA3CiAgICAgIH0sCiAgICAgIG9wdGlvbnM6IFtdCiAgICB9CiAgfTsKfTs="}, {"version": 3, "names": ["v1", "uuidv1", "configTable", "that", "name", "label", "id", "width", "config", "form_data1", "component", "colSpan", "componentProps", "disabled", "rows", "options", "form_data2"], "sources": ["E:/华南区项目/中国银行/开发区/科技公司运营平台/前端/dopUnify/src/views/system/outManage/registration/component/table/info.js"], "sourcesContent": ["import { v1 as uuidv1 } from 'uuid'\r\n// 表头\r\nexport const configTable = (that) => [\r\n  {\r\n    name: 'order_no',\r\n    label: '流水号',\r\n    id: uuidv1()\r\n  },\r\n  {\r\n    name: 'sys_id',\r\n    label: '系统标识',\r\n    id: uuidv1()\r\n  },\r\n  {\r\n    name: 'td_no',\r\n    label: '接口标识',\r\n    id: uuidv1()\r\n  },\r\n  {\r\n    name: 'trans_time',\r\n    label: '处理时长（毫秒）',\r\n    width: 240,\r\n    id: uuidv1()\r\n  },\r\n  {\r\n    name: 'last_modi_date',\r\n    label: '最后修改日期',\r\n    id: uuidv1()\r\n  }\r\n]\r\n\r\n// 详情弹出框表单\r\nexport const config = (that) => ({\r\n  form_data1: {\r\n    component: 'textarea',\r\n    label: '请求报文',\r\n    colSpan: 24,\r\n    name: 'form_data1',\r\n    config: {\r\n      // form-item 配置\r\n      // rules: [{ required: true, message: '接口标识为必输' }]\r\n    },\r\n    componentProps: {\r\n      // input组件配置\r\n      disabled: true,\r\n      rows: 7\r\n    },\r\n    options: []\r\n  },\r\n  form_data2: {\r\n    component: 'textarea',\r\n    label: '返回报文',\r\n    colSpan: 24,\r\n    name: 'form_data2',\r\n    config: {\r\n      // form-item 配置\r\n      // rules: [{ required: true, message: '接口分类为必输' }]\r\n    },\r\n    componentProps: {\r\n      // input组件配置\r\n      disabled: true,\r\n      rows: 7\r\n    },\r\n    options: []\r\n  }\r\n})\r\n"], "mappings": "AAAA,SAASA,EAAE,IAAIC,MAAM,QAAQ,MAAM;AACnC;AACA,OAAO,IAAMC,WAAW,GAAG,SAAdA,WAAW,CAAIC,IAAI;EAAA,OAAK,CACnC;IACEC,IAAI,EAAE,UAAU;IAChBC,KAAK,EAAE,KAAK;IACZC,EAAE,EAAEL,MAAM;EACZ,CAAC,EACD;IACEG,IAAI,EAAE,QAAQ;IACdC,KAAK,EAAE,MAAM;IACbC,EAAE,EAAEL,MAAM;EACZ,CAAC,EACD;IACEG,IAAI,EAAE,OAAO;IACbC,KAAK,EAAE,MAAM;IACbC,EAAE,EAAEL,MAAM;EACZ,CAAC,EACD;IACEG,IAAI,EAAE,YAAY;IAClBC,KAAK,EAAE,UAAU;IACjBE,KAAK,EAAE,GAAG;IACVD,EAAE,EAAEL,MAAM;EACZ,CAAC,EACD;IACEG,IAAI,EAAE,gBAAgB;IACtBC,KAAK,EAAE,QAAQ;IACfC,EAAE,EAAEL,MAAM;EACZ,CAAC,CACF;AAAA;;AAED;AACA,OAAO,IAAMO,MAAM,GAAG,SAATA,MAAM,CAAIL,IAAI;EAAA,OAAM;IAC/BM,UAAU,EAAE;MACVC,SAAS,EAAE,UAAU;MACrBL,KAAK,EAAE,MAAM;MACbM,OAAO,EAAE,EAAE;MACXP,IAAI,EAAE,YAAY;MAClBI,MAAM,EAAE;QACN;QACA;MAAA,CACD;MACDI,cAAc,EAAE;QACd;QACAC,QAAQ,EAAE,IAAI;QACdC,IAAI,EAAE;MACR,CAAC;MACDC,OAAO,EAAE;IACX,CAAC;IACDC,UAAU,EAAE;MACVN,SAAS,EAAE,UAAU;MACrBL,KAAK,EAAE,MAAM;MACbM,OAAO,EAAE,EAAE;MACXP,IAAI,EAAE,YAAY;MAClBI,MAAM,EAAE;QACN;QACA;MAAA,CACD;MACDI,cAAc,EAAE;QACd;QACAC,QAAQ,EAAE,IAAI;QACdC,IAAI,EAAE;MACR,CAAC;MACDC,OAAO,EAAE;IACX;EACF,CAAC;AAAA,CAAC"}]}