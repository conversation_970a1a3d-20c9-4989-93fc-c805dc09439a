package com.sunyard.etl.custom.handler;

import java.io.BufferedReader;
import java.io.File;
import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.InputStreamReader;
import java.io.OutputStreamWriter;
import java.io.PrintWriter;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.List;
import com.sunyard.etl.system.common.Constants;

import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import com.sunyard.etl.system.dao.DataDateDAO;
import com.sunyard.etl.system.dao.impl.DataDateDAOImpl;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.enums.JobStartEnum;
import com.xxl.job.core.handler.IJobHandler;
import com.xxl.job.core.handler.annotation.JobHandler;
import com.xxl.job.core.log.XxlJobLogger;


@JobHandler(value = "FileFormat_DEPD2050", name = "DEPD2050文件格式化")
@Service

public class FileFormat_DEPD2050 extends IJobHandler {

	private static final long serialVersionUID = 1L;

	private static DataDateDAO dateDao = new DataDateDAOImpl();

	@Override
	public ReturnT<String> execute(String jobId, String... arg1) throws Exception {
		XxlJobLogger.log("开始DEPD2050文件格式化...");
		String jobDate = dateDao.getDataDate();
		if (null != arg1[0]) {
			String dirPath = arg1[0].toString().replace("@", jobDate);
			File dir = new File(dirPath);
			if (!dir.exists()) {
				XxlJobLogger.log("INFO: 资源不足，目录不存在：" + dir, jobId + "");
				return new ReturnT<String>(ReturnT.FAIL_CODE, JobStartEnum.TASK_STATE_NO_RESOURCE.getCode(),
						"文件目录" + dir.getPath() + "不存在");
			}
			File preFile = new File(dir, "DEPD2050_"+jobDate+".txt");
			File newFile = new File(dir, "DEPD2050_"+jobDate+"_proc.txt");
			if (newFile.exists()) {
				XxlJobLogger.log("INFO: 文件已存在，删除后再转换：" + newFile.getPath(), jobId + "");
				newFile.delete();
			}
			PrintWriter pw;
			pw = new PrintWriter(new OutputStreamWriter(new FileOutputStream(newFile), "GBK"));
			if (preFile.length()==0) {
				pw.print("|||");
				pw.flush();
				pw.close();
				XxlJobLogger.log("INFO: 源文件不存在，生成转换文件：" + preFile.getPath() + " >> " + newFile.getPath(), jobId + "");
				return ReturnT.SUCCESS;
			}
			BufferedReader br = new BufferedReader(new InputStreamReader(new FileInputStream(preFile), "GBK"));
			
			String line = "";
			String siteNo = "";
			String siteName = "";
			String currencyType="";
			String occurDate = "";
			String btLine = "";
			String kxSign = "" ;
			String prodAddr = "" ;
			String[] strArr = null;
			List list = new ArrayList();
			try {
				a:while ((line = br.readLine()) != null) {
					if(line.contains("1                 ") && line.contains("-DEPD2050")){
						siteNo = line.substring(line.indexOf("( ")+"( ".length(),line.indexOf("-DEPD2050"));
						continue;
					} 
					
					if((line.contains("公司存款业务开／销户清单") || line.contains("===============")
							|| line.contains("产品类别")|| (line.length()<90 && !line.contains("开销户标识") && !line.contains("产品属性")))){
						continue;
					}   
					
					// ADD 2021-02-20 针对报表中多出来的行跳过
					if (line.contains("1@")){
						String[] array=line.split("|");
						if (array.length>=3){
							continue;
						}
					}
					// END 2021-02-20
					if(line.contains("机构 :") && line.contains("日期 :")){
					//	siteNo = line.substring(line.indexOf("机构号 : ")+"机构号 : ".length(),line.indexOf("机构 :")).trim();
						siteName = (line.substring(line.indexOf("机构 :  ")+"机构 :  ".length(),line.indexOf("日期 :"))).trim();
						occurDate = (line.substring(line.indexOf("日期 : ")+"日期 : ".length(),line.indexOf("日期 : ")+"日期 : ".length()+10)).trim();
						occurDate = new SimpleDateFormat("yyyyMMdd").format(new SimpleDateFormat("yyyy/MM/dd").parse(occurDate));
						currencyType = (line.substring(line.indexOf("货币 : ")+"货币 : ".length(),line.indexOf("页码 :"))).trim();
						continue;
					}
					if(line.contains("开销户标识") && line.contains("产品属性")){
						//	siteNo = line.substring(line.indexOf("机构号 : ")+"机构号 : ".length(),line.indexOf("机构 :")).trim();
							kxSign = (line.substring(line.indexOf("开销户标识 :  ")+"开销户标识 :  ".length(),line.indexOf("产品属性"))).trim();
							prodAddr = (line.substring(line.indexOf("产品属性 : ")+"产品属性 : ".length())).trim();
							continue;
						}
					if(line.contains("账号") && line.contains("子账户类别") && line.contains("册号")){
						btLine = line;
						strArr = new String[]{
								btLine.substring(0,btLine.indexOf("账号                 ")),	
								btLine.substring(btLine.indexOf("账号                 "),btLine.indexOf("子账户类别    ")),	
								btLine.substring(btLine.indexOf("子账户类别    "),btLine.indexOf("册号   ")),	
								btLine.substring(btLine.indexOf("册号   "),btLine.indexOf("序号    ")),	
								btLine.substring(btLine.indexOf("序号    "),btLine.indexOf("账户名称                                                    ")),
								btLine.substring(btLine.indexOf("账户名称                                                    "),btLine.indexOf("存期  ")),	
								btLine.substring(btLine.indexOf("存期  "),btLine.indexOf("经办柜员  ")),	
								btLine.substring(btLine.indexOf("经办柜员  "),btLine.indexOf("经办柜员所属机构  ")),
								btLine.substring(btLine.indexOf("经办柜员所属机构  "),btLine.indexOf("授权柜员  ")),
								btLine.substring(btLine.indexOf("授权柜员  "),btLine.indexOf("日志号     ")),
								btLine.substring(btLine.indexOf("日志号     "),btLine.indexOf("开户日期               ")),	
								btLine.substring(btLine.indexOf("开户日期               "),btLine.indexOf("开户日终余额  ")-6),
								btLine.substring(btLine.indexOf("开户日终余额  ")-6,btLine.indexOf("对账单摘要                                           ")),
								btLine.substring(btLine.indexOf("对账单摘要                                           "))};
						int index = 0;
						list = new ArrayList();
						for(String str:strArr){
							list.add(new int[]{index, str.getBytes("GBK").length});
							index += str.getBytes("GBK").length;
						}
						continue;
					}
					//开销户标识为“开户”、“销户”，产品码为5189或5212开头，并且册号、序号栏位为空才需要。
					
					if (!"开户".equals(kxSign) && !"销户".equals(kxSign)) {
						continue ;
					}
					//if (!prodAddr.startsWith("5189") &&!prodAddr.startsWith("5212") ) {
					//	continue ;
					//}
					
					int lineLength = line.getBytes().length;
					int btLength = btLine.getBytes().length;
					if(lineLength<btLength){
						line = String.format("%-"+btLength+"s", line);
					}
					StringBuffer sb = new StringBuffer(siteNo+" | "+siteName+" | "+occurDate+" | "+currencyType+" | "+kxSign+" | "+prodAddr+" | ");
					byte[] arr = line.getBytes("GBK");
					for(int i=1;i<list.size();i++){
						if(i<list.size()-1){
							int offset = ((int[])list.get(i))[0];
							int length = ((int[])list.get(i))[1];
							String tmp = new String(arr,offset,length,"GBK").trim();
							//if (i==3 ||i==4) {//册号、序号为空才需要
							//	if (StringUtils.isNotBlank(tmp)) {
							//		continue a ;
							//	}
							//}
							
							
							sb.append(tmp).append(" | ");
						}else{
							int offset = ((int[])list.get(i))[0];
							String tmp = new String(arr,offset,7,"GBK");
							sb.append(tmp).append(" | ");
						}
						
					}
					pw.print(sb.toString().replaceAll("　","").replaceAll("/","")+System.getProperty("line.separator"));
					
				}
			} catch (IOException e) {
				e.printStackTrace();
			}finally{
				pw.flush();
				pw.close();
			}
			Thread.sleep(8000);
			XxlJobLogger.log("INFO: 文件转换成功：" + newFile.getPath()+ ",休眠完8秒钟", jobId);
			return ReturnT.SUCCESS;
		}
		return ReturnT.FAIL;
	}

	public static void main(String[] args) {
		FileFormat_DEPD2050 ff = new FileFormat_DEPD2050();
		try {
			ff.execute("9", "C:\\@");
		} catch (Exception e) {
			// TODO Auto-generated catch block
			e.printStackTrace();
		}
	}
}
