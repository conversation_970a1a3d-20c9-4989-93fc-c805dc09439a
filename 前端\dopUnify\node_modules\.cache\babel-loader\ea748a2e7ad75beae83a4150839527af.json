{"remainingRequest": "E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\node_modules\\babel-loader\\lib\\index.js!E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\node_modules\\qiankun\\es\\sandbox\\proxySandbox.js", "dependencies": [{"path": "E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\node_modules\\qiankun\\es\\sandbox\\proxySandbox.js", "mtime": 1667130453000}, {"path": "E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\babel.config.js", "mtime": 1686019818359}, {"path": "E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["_toConsumableArray", "_classCallCheck", "_createClass", "_defineProperty", "_objectSpread", "SandBoxType", "isPropertyFrozen", "nativeGlobal", "nextTask", "getCurrentRunningApp", "getTargetValue", "trustedGlobals", "setCurrentRunningApp", "uniq", "array", "filter", "element", "Object", "create", "rawObjectDefineProperty", "defineProperty", "variableWhiteListInDev", "process", "env", "NODE_ENV", "window", "__QIANKUN_DEVELOPMENT__", "globalVariableWhiteList", "concat", "unscopables", "reduce", "acc", "key", "__proto__", "useNativeWindowForBindingsProps", "Map", "createFakeWindow", "globalContext", "propertiesWithGetter", "fakeWindow", "getOwnPropertyNames", "p", "descriptor", "getOwnPropertyDescriptor", "configurable", "for<PERSON>ach", "hasGetter", "prototype", "hasOwnProperty", "call", "writable", "set", "freeze", "activeSandboxCount", "ProxySandbox", "name", "_this", "arguments", "length", "undefined", "updatedValueSet", "Set", "type", "proxy", "sandboxRunning", "latestSetProp", "globalWhitelistPrevDescriptor", "Proxy", "_createFakeWindow", "descriptorTargetMap", "target", "value", "registerRunningApp", "enumerable", "indexOf", "add", "console", "warn", "toString", "get", "Symbol", "parent", "document", "eval", "actual<PERSON>arget", "has", "boundTarget", "_descriptor", "ownKeys", "Reflect", "attributes", "from", "deleteProperty", "delete", "getPrototypeOf", "active", "inactive", "_this2", "info", "keys", "currentRunningApp", "default"], "sources": ["E:/1_Project/XYD_Project/dop-4.0/dop-4.1-qianduan-unify/dopUnify/node_modules/qiankun/es/sandbox/proxySandbox.js"], "sourcesContent": ["import _toConsumableArray from \"@babel/runtime/helpers/esm/toConsumableArray\";\nimport _classCallCheck from \"@babel/runtime/helpers/esm/classCallCheck\";\nimport _createClass from \"@babel/runtime/helpers/esm/createClass\";\nimport _defineProperty from \"@babel/runtime/helpers/esm/defineProperty\";\nimport _objectSpread from \"@babel/runtime/helpers/esm/objectSpread2\";\nimport { SandBoxType } from '../interfaces';\nimport { isPropertyFrozen, nativeGlobal, nextTask } from '../utils';\nimport { getCurrentRunningApp, getTargetValue, trustedGlobals, setCurrentRunningApp } from './common';\n/**\n * fastest(at most time) unique array method\n * @see https://jsperf.com/array-filter-unique/30\n */\nfunction uniq(array) {\n  return array.filter(function filter(element) {\n    return element in this ? false : this[element] = true;\n  }, Object.create(null));\n}\n// zone.js will overwrite Object.defineProperty\nvar rawObjectDefineProperty = Object.defineProperty;\nvar variableWhiteListInDev = process.env.NODE_ENV === 'test' || process.env.NODE_ENV === 'development' || window.__QIANKUN_DEVELOPMENT__ ? [\n// for react hot reload\n// see https://github.com/facebook/create-react-app/blob/66bf7dfc43350249e2f09d138a20840dae8a0a4a/packages/react-error-overlay/src/index.js#L180\n'__REACT_ERROR_OVERLAY_GLOBAL_HOOK__'] : [];\n// who could escape the sandbox\nvar globalVariableWhiteList = [\n// FIXME System.js used a indirect call with eval, which would make it scope escape to global\n// To make System.js works well, we write it back to global window temporary\n// see https://github.com/systemjs/systemjs/blob/457f5b7e8af6bd120a279540477552a07d5de086/src/evaluate.js#L106\n'System',\n// see https://github.com/systemjs/systemjs/blob/457f5b7e8af6bd120a279540477552a07d5de086/src/instantiate.js#L357\n'__cjsWrapper'].concat(variableWhiteListInDev);\n/*\n variables who are impossible to be overwritten need to be escaped from proxy sandbox for performance reasons\n see https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Symbol/unscopables\n */\nvar unscopables = trustedGlobals.reduce(function (acc, key) {\n  return _objectSpread(_objectSpread({}, acc), {}, _defineProperty({}, key, true));\n}, {\n  __proto__: null\n});\nvar useNativeWindowForBindingsProps = new Map([['fetch', true], ['mockDomAPIInBlackList', process.env.NODE_ENV === 'test']]);\nfunction createFakeWindow(globalContext) {\n  // map always has the fastest performance in has check scenario\n  // see https://jsperf.com/array-indexof-vs-set-has/23\n  var propertiesWithGetter = new Map();\n  var fakeWindow = {};\n  /*\n   copy the non-configurable property of global to fakeWindow\n   see https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Proxy/handler/getOwnPropertyDescriptor\n   > A property cannot be reported as non-configurable, if it does not exist as an own property of the target object or if it exists as a configurable own property of the target object.\n   */\n  Object.getOwnPropertyNames(globalContext).filter(function (p) {\n    var descriptor = Object.getOwnPropertyDescriptor(globalContext, p);\n    return !(descriptor === null || descriptor === void 0 ? void 0 : descriptor.configurable);\n  }).forEach(function (p) {\n    var descriptor = Object.getOwnPropertyDescriptor(globalContext, p);\n    if (descriptor) {\n      var hasGetter = Object.prototype.hasOwnProperty.call(descriptor, 'get');\n      /*\n       make top/self/window property configurable and writable, otherwise it will cause TypeError while get trap return.\n       see https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Proxy/handler/get\n       > The value reported for a property must be the same as the value of the corresponding target object property if the target object property is a non-writable, non-configurable data property.\n       */\n      if (p === 'top' || p === 'parent' || p === 'self' || p === 'window' || process.env.NODE_ENV === 'test' && (p === 'mockTop' || p === 'mockSafariTop')) {\n        descriptor.configurable = true;\n        /*\n         The descriptor of window.window/window.top/window.self in Safari/FF are accessor descriptors, we need to avoid adding a data descriptor while it was\n         Example:\n          Safari/FF: Object.getOwnPropertyDescriptor(window, 'top') -> {get: function, set: undefined, enumerable: true, configurable: false}\n          Chrome: Object.getOwnPropertyDescriptor(window, 'top') -> {value: Window, writable: false, enumerable: true, configurable: false}\n         */\n        if (!hasGetter) {\n          descriptor.writable = true;\n        }\n      }\n      if (hasGetter) propertiesWithGetter.set(p, true);\n      // freeze the descriptor to avoid being modified by zone.js\n      // see https://github.com/angular/zone.js/blob/a5fe09b0fac27ac5df1fa746042f96f05ccb6a00/lib/browser/define-property.ts#L71\n      rawObjectDefineProperty(fakeWindow, p, Object.freeze(descriptor));\n    }\n  });\n  return {\n    fakeWindow: fakeWindow,\n    propertiesWithGetter: propertiesWithGetter\n  };\n}\nvar activeSandboxCount = 0;\n/**\n * 基于 Proxy 实现的沙箱\n */\nvar ProxySandbox = /*#__PURE__*/function () {\n  function ProxySandbox(name) {\n    var _this = this;\n    var globalContext = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : window;\n    _classCallCheck(this, ProxySandbox);\n    this.updatedValueSet = new Set();\n    this.name = void 0;\n    this.type = void 0;\n    this.proxy = void 0;\n    this.sandboxRunning = true;\n    this.latestSetProp = null;\n    this.globalWhitelistPrevDescriptor = {};\n    this.globalContext = void 0;\n    this.name = name;\n    this.globalContext = globalContext;\n    this.type = SandBoxType.Proxy;\n    var updatedValueSet = this.updatedValueSet;\n    var _createFakeWindow = createFakeWindow(globalContext),\n      fakeWindow = _createFakeWindow.fakeWindow,\n      propertiesWithGetter = _createFakeWindow.propertiesWithGetter;\n    var descriptorTargetMap = new Map();\n    var hasOwnProperty = function hasOwnProperty(key) {\n      return fakeWindow.hasOwnProperty(key) || globalContext.hasOwnProperty(key);\n    };\n    var proxy = new Proxy(fakeWindow, {\n      set: function set(target, p, value) {\n        if (_this.sandboxRunning) {\n          _this.registerRunningApp(name, proxy);\n          // We must keep its description while the property existed in globalContext before\n          if (!target.hasOwnProperty(p) && globalContext.hasOwnProperty(p)) {\n            var descriptor = Object.getOwnPropertyDescriptor(globalContext, p);\n            var writable = descriptor.writable,\n              configurable = descriptor.configurable,\n              enumerable = descriptor.enumerable,\n              set = descriptor.set;\n            // only writable property can be overwritten\n            // here we ignored accessor descriptor of globalContext as it makes no sense to trigger its logic(which might make sandbox escaping instead)\n            // we force to set value by data descriptor\n            if (writable || set) {\n              Object.defineProperty(target, p, {\n                configurable: configurable,\n                enumerable: enumerable,\n                writable: true,\n                value: value\n              });\n            }\n          } else {\n            target[p] = value;\n          }\n          // sync the property to globalContext\n          if (typeof p === 'string' && globalVariableWhiteList.indexOf(p) !== -1) {\n            _this.globalWhitelistPrevDescriptor[p] = Object.getOwnPropertyDescriptor(globalContext, p);\n            // @ts-ignore\n            globalContext[p] = value;\n          }\n          updatedValueSet.add(p);\n          _this.latestSetProp = p;\n          return true;\n        }\n        if (process.env.NODE_ENV === 'development') {\n          console.warn(\"[qiankun] Set window.\".concat(p.toString(), \" while sandbox destroyed or inactive in \").concat(name, \"!\"));\n        }\n        // 在 strict-mode 下，Proxy 的 handler.set 返回 false 会抛出 TypeError，在沙箱卸载的情况下应该忽略错误\n        return true;\n      },\n      get: function get(target, p) {\n        _this.registerRunningApp(name, proxy);\n        if (p === Symbol.unscopables) return unscopables;\n        // avoid who using window.window or window.self to escape the sandbox environment to touch the really window\n        // see https://github.com/eligrey/FileSaver.js/blob/master/src/FileSaver.js#L13\n        if (p === 'window' || p === 'self') {\n          return proxy;\n        }\n        // hijack globalWindow accessing with globalThis keyword\n        if (p === 'globalThis') {\n          return proxy;\n        }\n        if (p === 'top' || p === 'parent' || process.env.NODE_ENV === 'test' && (p === 'mockTop' || p === 'mockSafariTop')) {\n          // if your master app in an iframe context, allow these props escape the sandbox\n          if (globalContext === globalContext.parent) {\n            return proxy;\n          }\n          return globalContext[p];\n        }\n        // proxy.hasOwnProperty would invoke getter firstly, then its value represented as globalContext.hasOwnProperty\n        if (p === 'hasOwnProperty') {\n          return hasOwnProperty;\n        }\n        if (p === 'document') {\n          return document;\n        }\n        if (p === 'eval') {\n          return eval;\n        }\n        var actualTarget = propertiesWithGetter.has(p) ? globalContext : p in target ? target : globalContext;\n        var value = actualTarget[p];\n        // frozen value should return directly, see https://github.com/umijs/qiankun/issues/2015\n        if (isPropertyFrozen(actualTarget, p)) {\n          return value;\n        }\n        /* Some dom api must be bound to native window, otherwise it would cause exception like 'TypeError: Failed to execute 'fetch' on 'Window': Illegal invocation'\n           See this code:\n             const proxy = new Proxy(window, {});\n             const proxyFetch = fetch.bind(proxy);\n             proxyFetch('https://qiankun.com');\n        */\n        var boundTarget = useNativeWindowForBindingsProps.get(p) ? nativeGlobal : globalContext;\n        return getTargetValue(boundTarget, value);\n      },\n      // trap in operator\n      // see https://github.com/styled-components/styled-components/blob/master/packages/styled-components/src/constants.js#L12\n      has: function has(target, p) {\n        return p in unscopables || p in target || p in globalContext;\n      },\n      getOwnPropertyDescriptor: function getOwnPropertyDescriptor(target, p) {\n        /*\n         as the descriptor of top/self/window/mockTop in raw window are configurable but not in proxy target, we need to get it from target to avoid TypeError\n         see https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Proxy/handler/getOwnPropertyDescriptor\n         > A property cannot be reported as non-configurable, if it does not exists as an own property of the target object or if it exists as a configurable own property of the target object.\n         */\n        if (target.hasOwnProperty(p)) {\n          var descriptor = Object.getOwnPropertyDescriptor(target, p);\n          descriptorTargetMap.set(p, 'target');\n          return descriptor;\n        }\n        if (globalContext.hasOwnProperty(p)) {\n          var _descriptor = Object.getOwnPropertyDescriptor(globalContext, p);\n          descriptorTargetMap.set(p, 'globalContext');\n          // A property cannot be reported as non-configurable, if it does not exists as an own property of the target object\n          if (_descriptor && !_descriptor.configurable) {\n            _descriptor.configurable = true;\n          }\n          return _descriptor;\n        }\n        return undefined;\n      },\n      // trap to support iterator with sandbox\n      ownKeys: function ownKeys(target) {\n        return uniq(Reflect.ownKeys(globalContext).concat(Reflect.ownKeys(target)));\n      },\n      defineProperty: function defineProperty(target, p, attributes) {\n        var from = descriptorTargetMap.get(p);\n        /*\n         Descriptor must be defined to native window while it comes from native window via Object.getOwnPropertyDescriptor(window, p),\n         otherwise it would cause a TypeError with illegal invocation.\n         */\n        switch (from) {\n          case 'globalContext':\n            return Reflect.defineProperty(globalContext, p, attributes);\n          default:\n            return Reflect.defineProperty(target, p, attributes);\n        }\n      },\n      deleteProperty: function deleteProperty(target, p) {\n        _this.registerRunningApp(name, proxy);\n        if (target.hasOwnProperty(p)) {\n          // @ts-ignore\n          delete target[p];\n          updatedValueSet.delete(p);\n          return true;\n        }\n        return true;\n      },\n      // makes sure `window instanceof Window` returns truthy in micro app\n      getPrototypeOf: function getPrototypeOf() {\n        return Reflect.getPrototypeOf(globalContext);\n      }\n    });\n    this.proxy = proxy;\n    activeSandboxCount++;\n  }\n  _createClass(ProxySandbox, [{\n    key: \"active\",\n    value: /** window 值变更记录 */\n\n    function active() {\n      if (!this.sandboxRunning) activeSandboxCount++;\n      this.sandboxRunning = true;\n    }\n  }, {\n    key: \"inactive\",\n    value: function inactive() {\n      var _this2 = this;\n      if (process.env.NODE_ENV === 'development') {\n        console.info(\"[qiankun:sandbox] \".concat(this.name, \" modified global properties restore...\"), _toConsumableArray(this.updatedValueSet.keys()));\n      }\n      if (process.env.NODE_ENV === 'test' || --activeSandboxCount === 0) {\n        // reset the global value to the prev value\n        Object.keys(this.globalWhitelistPrevDescriptor).forEach(function (p) {\n          var descriptor = _this2.globalWhitelistPrevDescriptor[p];\n          if (descriptor) {\n            Object.defineProperty(_this2.globalContext, p, descriptor);\n          } else {\n            // @ts-ignore\n            delete _this2.globalContext[p];\n          }\n        });\n      }\n      this.sandboxRunning = false;\n    }\n    // the descriptor of global variables in whitelist before it been modified\n  }, {\n    key: \"registerRunningApp\",\n    value: function registerRunningApp(name, proxy) {\n      if (this.sandboxRunning) {\n        var currentRunningApp = getCurrentRunningApp();\n        if (!currentRunningApp || currentRunningApp.name !== name) {\n          setCurrentRunningApp({\n            name: name,\n            window: proxy\n          });\n        }\n        // FIXME if you have any other good ideas\n        // remove the mark in next tick, thus we can identify whether it in micro app or not\n        // this approach is just a workaround, it could not cover all complex cases, such as the micro app runs in the same task context with master in some case\n        nextTask(function () {\n          setCurrentRunningApp(null);\n        });\n      }\n    }\n  }]);\n  return ProxySandbox;\n}();\nexport { ProxySandbox as default };"], "mappings": ";;;;;;;;;;;;;;;;;;;;;AAAA,OAAOA,kBAAkB,MAAM,8CAA8C;AAC7E,OAAOC,eAAe,MAAM,2CAA2C;AACvE,OAAOC,YAAY,MAAM,wCAAwC;AACjE,OAAOC,eAAe,MAAM,2CAA2C;AACvE,OAAOC,aAAa,MAAM,0CAA0C;AACpE,SAASC,WAAW,QAAQ,eAAe;AAC3C,SAASC,gBAAgB,EAAEC,YAAY,EAAEC,QAAQ,QAAQ,UAAU;AACnE,SAASC,oBAAoB,EAAEC,cAAc,EAAEC,cAAc,EAAEC,oBAAoB,QAAQ,UAAU;AACrG;AACA;AACA;AACA;AACA,SAASC,IAAI,CAACC,KAAK,EAAE;EACnB,OAAOA,KAAK,CAACC,MAAM,CAAC,SAASA,MAAM,CAACC,OAAO,EAAE;IAC3C,OAAOA,OAAO,IAAI,IAAI,GAAG,KAAK,GAAG,IAAI,CAACA,OAAO,CAAC,GAAG,IAAI;EACvD,CAAC,EAAEC,MAAM,CAACC,MAAM,CAAC,IAAI,CAAC,CAAC;AACzB;AACA;AACA,IAAIC,uBAAuB,GAAGF,MAAM,CAACG,cAAc;AACnD,IAAIC,sBAAsB,GAAGC,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,MAAM,IAAIF,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,aAAa,IAAIC,MAAM,CAACC,uBAAuB,GAAG;AAC3I;AACA;AACA,qCAAqC,CAAC,GAAG,EAAE;AAC3C;AACA,IAAIC,uBAAuB,GAAG;AAC9B;AACA;AACA;AACA,QAAQ;AACR;AACA,cAAc,CAAC,CAACC,MAAM,CAACP,sBAAsB,CAAC;AAC9C;AACA;AACA;AACA;AACA,IAAIQ,WAAW,GAAGlB,cAAc,CAACmB,MAAM,CAAC,UAAUC,GAAG,EAAEC,GAAG,EAAE;EAC1D,OAAO5B,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAE2B,GAAG,CAAC,EAAE,CAAC,CAAC,EAAE5B,eAAe,CAAC,CAAC,CAAC,EAAE6B,GAAG,EAAE,IAAI,CAAC,CAAC;AAClF,CAAC,EAAE;EACDC,SAAS,EAAE;AACb,CAAC,CAAC;AACF,IAAIC,+BAA+B,GAAG,IAAIC,GAAG,CAAC,CAAC,CAAC,OAAO,EAAE,IAAI,CAAC,EAAE,CAAC,uBAAuB,EAAEb,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,MAAM,CAAC,CAAC,CAAC;AAC5H,SAASY,gBAAgB,CAACC,aAAa,EAAE;EACvC;EACA;EACA,IAAIC,oBAAoB,GAAG,IAAIH,GAAG,EAAE;EACpC,IAAII,UAAU,GAAG,CAAC,CAAC;EACnB;AACF;AACA;AACA;AACA;EACEtB,MAAM,CAACuB,mBAAmB,CAACH,aAAa,CAAC,CAACtB,MAAM,CAAC,UAAU0B,CAAC,EAAE;IAC5D,IAAIC,UAAU,GAAGzB,MAAM,CAAC0B,wBAAwB,CAACN,aAAa,EAAEI,CAAC,CAAC;IAClE,OAAO,EAAEC,UAAU,KAAK,IAAI,IAAIA,UAAU,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,UAAU,CAACE,YAAY,CAAC;EAC3F,CAAC,CAAC,CAACC,OAAO,CAAC,UAAUJ,CAAC,EAAE;IACtB,IAAIC,UAAU,GAAGzB,MAAM,CAAC0B,wBAAwB,CAACN,aAAa,EAAEI,CAAC,CAAC;IAClE,IAAIC,UAAU,EAAE;MACd,IAAII,SAAS,GAAG7B,MAAM,CAAC8B,SAAS,CAACC,cAAc,CAACC,IAAI,CAACP,UAAU,EAAE,KAAK,CAAC;MACvE;AACN;AACA;AACA;AACA;MACM,IAAID,CAAC,KAAK,KAAK,IAAIA,CAAC,KAAK,QAAQ,IAAIA,CAAC,KAAK,MAAM,IAAIA,CAAC,KAAK,QAAQ,IAAInB,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,MAAM,KAAKiB,CAAC,KAAK,SAAS,IAAIA,CAAC,KAAK,eAAe,CAAC,EAAE;QACpJC,UAAU,CAACE,YAAY,GAAG,IAAI;QAC9B;AACR;AACA;AACA;AACA;AACA;QACQ,IAAI,CAACE,SAAS,EAAE;UACdJ,UAAU,CAACQ,QAAQ,GAAG,IAAI;QAC5B;MACF;MACA,IAAIJ,SAAS,EAAER,oBAAoB,CAACa,GAAG,CAACV,CAAC,EAAE,IAAI,CAAC;MAChD;MACA;MACAtB,uBAAuB,CAACoB,UAAU,EAAEE,CAAC,EAAExB,MAAM,CAACmC,MAAM,CAACV,UAAU,CAAC,CAAC;IACnE;EACF,CAAC,CAAC;EACF,OAAO;IACLH,UAAU,EAAEA,UAAU;IACtBD,oBAAoB,EAAEA;EACxB,CAAC;AACH;AACA,IAAIe,kBAAkB,GAAG,CAAC;AAC1B;AACA;AACA;AACA,IAAIC,YAAY,GAAG,aAAa,YAAY;EAC1C,SAASA,YAAY,CAACC,IAAI,EAAE;IAC1B,IAAIC,KAAK,GAAG,IAAI;IAChB,IAAInB,aAAa,GAAGoB,SAAS,CAACC,MAAM,GAAG,CAAC,IAAID,SAAS,CAAC,CAAC,CAAC,KAAKE,SAAS,GAAGF,SAAS,CAAC,CAAC,CAAC,GAAGhC,MAAM;IAC9FxB,eAAe,CAAC,IAAI,EAAEqD,YAAY,CAAC;IACnC,IAAI,CAACM,eAAe,GAAG,IAAIC,GAAG,EAAE;IAChC,IAAI,CAACN,IAAI,GAAG,KAAK,CAAC;IAClB,IAAI,CAACO,IAAI,GAAG,KAAK,CAAC;IAClB,IAAI,CAACC,KAAK,GAAG,KAAK,CAAC;IACnB,IAAI,CAACC,cAAc,GAAG,IAAI;IAC1B,IAAI,CAACC,aAAa,GAAG,IAAI;IACzB,IAAI,CAACC,6BAA6B,GAAG,CAAC,CAAC;IACvC,IAAI,CAAC7B,aAAa,GAAG,KAAK,CAAC;IAC3B,IAAI,CAACkB,IAAI,GAAGA,IAAI;IAChB,IAAI,CAAClB,aAAa,GAAGA,aAAa;IAClC,IAAI,CAACyB,IAAI,GAAGzD,WAAW,CAAC8D,KAAK;IAC7B,IAAIP,eAAe,GAAG,IAAI,CAACA,eAAe;IAC1C,IAAIQ,iBAAiB,GAAGhC,gBAAgB,CAACC,aAAa,CAAC;MACrDE,UAAU,GAAG6B,iBAAiB,CAAC7B,UAAU;MACzCD,oBAAoB,GAAG8B,iBAAiB,CAAC9B,oBAAoB;IAC/D,IAAI+B,mBAAmB,GAAG,IAAIlC,GAAG,EAAE;IACnC,IAAIa,cAAc,GAAG,SAASA,cAAc,CAAChB,GAAG,EAAE;MAChD,OAAOO,UAAU,CAACS,cAAc,CAAChB,GAAG,CAAC,IAAIK,aAAa,CAACW,cAAc,CAAChB,GAAG,CAAC;IAC5E,CAAC;IACD,IAAI+B,KAAK,GAAG,IAAII,KAAK,CAAC5B,UAAU,EAAE;MAChCY,GAAG,EAAE,SAASA,GAAG,CAACmB,MAAM,EAAE7B,CAAC,EAAE8B,KAAK,EAAE;QAClC,IAAIf,KAAK,CAACQ,cAAc,EAAE;UACxBR,KAAK,CAACgB,kBAAkB,CAACjB,IAAI,EAAEQ,KAAK,CAAC;UACrC;UACA,IAAI,CAACO,MAAM,CAACtB,cAAc,CAACP,CAAC,CAAC,IAAIJ,aAAa,CAACW,cAAc,CAACP,CAAC,CAAC,EAAE;YAChE,IAAIC,UAAU,GAAGzB,MAAM,CAAC0B,wBAAwB,CAACN,aAAa,EAAEI,CAAC,CAAC;YAClE,IAAIS,QAAQ,GAAGR,UAAU,CAACQ,QAAQ;cAChCN,YAAY,GAAGF,UAAU,CAACE,YAAY;cACtC6B,UAAU,GAAG/B,UAAU,CAAC+B,UAAU;cAClCtB,GAAG,GAAGT,UAAU,CAACS,GAAG;YACtB;YACA;YACA;YACA,IAAID,QAAQ,IAAIC,GAAG,EAAE;cACnBlC,MAAM,CAACG,cAAc,CAACkD,MAAM,EAAE7B,CAAC,EAAE;gBAC/BG,YAAY,EAAEA,YAAY;gBAC1B6B,UAAU,EAAEA,UAAU;gBACtBvB,QAAQ,EAAE,IAAI;gBACdqB,KAAK,EAAEA;cACT,CAAC,CAAC;YACJ;UACF,CAAC,MAAM;YACLD,MAAM,CAAC7B,CAAC,CAAC,GAAG8B,KAAK;UACnB;UACA;UACA,IAAI,OAAO9B,CAAC,KAAK,QAAQ,IAAId,uBAAuB,CAAC+C,OAAO,CAACjC,CAAC,CAAC,KAAK,CAAC,CAAC,EAAE;YACtEe,KAAK,CAACU,6BAA6B,CAACzB,CAAC,CAAC,GAAGxB,MAAM,CAAC0B,wBAAwB,CAACN,aAAa,EAAEI,CAAC,CAAC;YAC1F;YACAJ,aAAa,CAACI,CAAC,CAAC,GAAG8B,KAAK;UAC1B;UACAX,eAAe,CAACe,GAAG,CAAClC,CAAC,CAAC;UACtBe,KAAK,CAACS,aAAa,GAAGxB,CAAC;UACvB,OAAO,IAAI;QACb;QACA,IAAInB,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,aAAa,EAAE;UAC1CoD,OAAO,CAACC,IAAI,CAAC,uBAAuB,CAACjD,MAAM,CAACa,CAAC,CAACqC,QAAQ,EAAE,EAAE,0CAA0C,CAAC,CAAClD,MAAM,CAAC2B,IAAI,EAAE,GAAG,CAAC,CAAC;QAC1H;QACA;QACA,OAAO,IAAI;MACb,CAAC;MACDwB,GAAG,EAAE,SAASA,GAAG,CAACT,MAAM,EAAE7B,CAAC,EAAE;QAC3Be,KAAK,CAACgB,kBAAkB,CAACjB,IAAI,EAAEQ,KAAK,CAAC;QACrC,IAAItB,CAAC,KAAKuC,MAAM,CAACnD,WAAW,EAAE,OAAOA,WAAW;QAChD;QACA;QACA,IAAIY,CAAC,KAAK,QAAQ,IAAIA,CAAC,KAAK,MAAM,EAAE;UAClC,OAAOsB,KAAK;QACd;QACA;QACA,IAAItB,CAAC,KAAK,YAAY,EAAE;UACtB,OAAOsB,KAAK;QACd;QACA,IAAItB,CAAC,KAAK,KAAK,IAAIA,CAAC,KAAK,QAAQ,IAAInB,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,MAAM,KAAKiB,CAAC,KAAK,SAAS,IAAIA,CAAC,KAAK,eAAe,CAAC,EAAE;UAClH;UACA,IAAIJ,aAAa,KAAKA,aAAa,CAAC4C,MAAM,EAAE;YAC1C,OAAOlB,KAAK;UACd;UACA,OAAO1B,aAAa,CAACI,CAAC,CAAC;QACzB;QACA;QACA,IAAIA,CAAC,KAAK,gBAAgB,EAAE;UAC1B,OAAOO,cAAc;QACvB;QACA,IAAIP,CAAC,KAAK,UAAU,EAAE;UACpB,OAAOyC,QAAQ;QACjB;QACA,IAAIzC,CAAC,KAAK,MAAM,EAAE;UAChB,OAAO0C,IAAI;QACb;QACA,IAAIC,YAAY,GAAG9C,oBAAoB,CAAC+C,GAAG,CAAC5C,CAAC,CAAC,GAAGJ,aAAa,GAAGI,CAAC,IAAI6B,MAAM,GAAGA,MAAM,GAAGjC,aAAa;QACrG,IAAIkC,KAAK,GAAGa,YAAY,CAAC3C,CAAC,CAAC;QAC3B;QACA,IAAInC,gBAAgB,CAAC8E,YAAY,EAAE3C,CAAC,CAAC,EAAE;UACrC,OAAO8B,KAAK;QACd;QACA;AACR;AACA;AACA;AACA;AACA;QACQ,IAAIe,WAAW,GAAGpD,+BAA+B,CAAC6C,GAAG,CAACtC,CAAC,CAAC,GAAGlC,YAAY,GAAG8B,aAAa;QACvF,OAAO3B,cAAc,CAAC4E,WAAW,EAAEf,KAAK,CAAC;MAC3C,CAAC;MACD;MACA;MACAc,GAAG,EAAE,SAASA,GAAG,CAACf,MAAM,EAAE7B,CAAC,EAAE;QAC3B,OAAOA,CAAC,IAAIZ,WAAW,IAAIY,CAAC,IAAI6B,MAAM,IAAI7B,CAAC,IAAIJ,aAAa;MAC9D,CAAC;MACDM,wBAAwB,EAAE,SAASA,wBAAwB,CAAC2B,MAAM,EAAE7B,CAAC,EAAE;QACrE;AACR;AACA;AACA;AACA;QACQ,IAAI6B,MAAM,CAACtB,cAAc,CAACP,CAAC,CAAC,EAAE;UAC5B,IAAIC,UAAU,GAAGzB,MAAM,CAAC0B,wBAAwB,CAAC2B,MAAM,EAAE7B,CAAC,CAAC;UAC3D4B,mBAAmB,CAAClB,GAAG,CAACV,CAAC,EAAE,QAAQ,CAAC;UACpC,OAAOC,UAAU;QACnB;QACA,IAAIL,aAAa,CAACW,cAAc,CAACP,CAAC,CAAC,EAAE;UACnC,IAAI8C,WAAW,GAAGtE,MAAM,CAAC0B,wBAAwB,CAACN,aAAa,EAAEI,CAAC,CAAC;UACnE4B,mBAAmB,CAAClB,GAAG,CAACV,CAAC,EAAE,eAAe,CAAC;UAC3C;UACA,IAAI8C,WAAW,IAAI,CAACA,WAAW,CAAC3C,YAAY,EAAE;YAC5C2C,WAAW,CAAC3C,YAAY,GAAG,IAAI;UACjC;UACA,OAAO2C,WAAW;QACpB;QACA,OAAO5B,SAAS;MAClB,CAAC;MACD;MACA6B,OAAO,EAAE,SAASA,OAAO,CAAClB,MAAM,EAAE;QAChC,OAAOzD,IAAI,CAAC4E,OAAO,CAACD,OAAO,CAACnD,aAAa,CAAC,CAACT,MAAM,CAAC6D,OAAO,CAACD,OAAO,CAAClB,MAAM,CAAC,CAAC,CAAC;MAC7E,CAAC;MACDlD,cAAc,EAAE,SAASA,cAAc,CAACkD,MAAM,EAAE7B,CAAC,EAAEiD,UAAU,EAAE;QAC7D,IAAIC,IAAI,GAAGtB,mBAAmB,CAACU,GAAG,CAACtC,CAAC,CAAC;QACrC;AACR;AACA;AACA;QACQ,QAAQkD,IAAI;UACV,KAAK,eAAe;YAClB,OAAOF,OAAO,CAACrE,cAAc,CAACiB,aAAa,EAAEI,CAAC,EAAEiD,UAAU,CAAC;UAC7D;YACE,OAAOD,OAAO,CAACrE,cAAc,CAACkD,MAAM,EAAE7B,CAAC,EAAEiD,UAAU,CAAC;QAAC;MAE3D,CAAC;MACDE,cAAc,EAAE,SAASA,cAAc,CAACtB,MAAM,EAAE7B,CAAC,EAAE;QACjDe,KAAK,CAACgB,kBAAkB,CAACjB,IAAI,EAAEQ,KAAK,CAAC;QACrC,IAAIO,MAAM,CAACtB,cAAc,CAACP,CAAC,CAAC,EAAE;UAC5B;UACA,OAAO6B,MAAM,CAAC7B,CAAC,CAAC;UAChBmB,eAAe,CAACiC,MAAM,CAACpD,CAAC,CAAC;UACzB,OAAO,IAAI;QACb;QACA,OAAO,IAAI;MACb,CAAC;MACD;MACAqD,cAAc,EAAE,SAASA,cAAc,GAAG;QACxC,OAAOL,OAAO,CAACK,cAAc,CAACzD,aAAa,CAAC;MAC9C;IACF,CAAC,CAAC;IACF,IAAI,CAAC0B,KAAK,GAAGA,KAAK;IAClBV,kBAAkB,EAAE;EACtB;EACAnD,YAAY,CAACoD,YAAY,EAAE,CAAC;IAC1BtB,GAAG,EAAE,QAAQ;IACbuC,KAAK,EAAE;;IAEP,SAASwB,MAAM,GAAG;MAChB,IAAI,CAAC,IAAI,CAAC/B,cAAc,EAAEX,kBAAkB,EAAE;MAC9C,IAAI,CAACW,cAAc,GAAG,IAAI;IAC5B;EACF,CAAC,EAAE;IACDhC,GAAG,EAAE,UAAU;IACfuC,KAAK,EAAE,SAASyB,QAAQ,GAAG;MACzB,IAAIC,MAAM,GAAG,IAAI;MACjB,IAAI3E,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,aAAa,EAAE;QAC1CoD,OAAO,CAACsB,IAAI,CAAC,oBAAoB,CAACtE,MAAM,CAAC,IAAI,CAAC2B,IAAI,EAAE,wCAAwC,CAAC,EAAEvD,kBAAkB,CAAC,IAAI,CAAC4D,eAAe,CAACuC,IAAI,EAAE,CAAC,CAAC;MACjJ;MACA,IAAI7E,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,MAAM,IAAI,EAAE6B,kBAAkB,KAAK,CAAC,EAAE;QACjE;QACApC,MAAM,CAACkF,IAAI,CAAC,IAAI,CAACjC,6BAA6B,CAAC,CAACrB,OAAO,CAAC,UAAUJ,CAAC,EAAE;UACnE,IAAIC,UAAU,GAAGuD,MAAM,CAAC/B,6BAA6B,CAACzB,CAAC,CAAC;UACxD,IAAIC,UAAU,EAAE;YACdzB,MAAM,CAACG,cAAc,CAAC6E,MAAM,CAAC5D,aAAa,EAAEI,CAAC,EAAEC,UAAU,CAAC;UAC5D,CAAC,MAAM;YACL;YACA,OAAOuD,MAAM,CAAC5D,aAAa,CAACI,CAAC,CAAC;UAChC;QACF,CAAC,CAAC;MACJ;MACA,IAAI,CAACuB,cAAc,GAAG,KAAK;IAC7B;IACA;EACF,CAAC,EAAE;IACDhC,GAAG,EAAE,oBAAoB;IACzBuC,KAAK,EAAE,SAASC,kBAAkB,CAACjB,IAAI,EAAEQ,KAAK,EAAE;MAC9C,IAAI,IAAI,CAACC,cAAc,EAAE;QACvB,IAAIoC,iBAAiB,GAAG3F,oBAAoB,EAAE;QAC9C,IAAI,CAAC2F,iBAAiB,IAAIA,iBAAiB,CAAC7C,IAAI,KAAKA,IAAI,EAAE;UACzD3C,oBAAoB,CAAC;YACnB2C,IAAI,EAAEA,IAAI;YACV9B,MAAM,EAAEsC;UACV,CAAC,CAAC;QACJ;QACA;QACA;QACA;QACAvD,QAAQ,CAAC,YAAY;UACnBI,oBAAoB,CAAC,IAAI,CAAC;QAC5B,CAAC,CAAC;MACJ;IACF;EACF,CAAC,CAAC,CAAC;EACH,OAAO0C,YAAY;AACrB,CAAC,EAAE;AACH,SAASA,YAAY,IAAI+C,OAAO"}]}