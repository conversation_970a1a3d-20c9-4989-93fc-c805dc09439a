package com.sunyard.etl.nps.model;


//无纸化任务定义表
public class NpInputDef {
	private String nopaperId;
	private String source;
	private String ECMLogonParameter;
	private String ECMQueryParameter;
	private String ECMUploadParameter;
	private String FTPLogonParameter;
	private String FTPOtherParameter;
	private String businessId;
	private String nopaperDesc;
	private String TMSJobId;
	private String needUpload;
	private String startDate;
	private String psLevel;
	private String indexValueNew;
	
	public String getStartDate() {
		return startDate;
	}

	public void setStartDate(String startDate) {
		this.startDate = startDate;
	}

	public NpInputDef(){
		
	}
	
	public NpInputDef(String nopaperId, String source, String ECMLogonParameter,
			String ECMOtherParameter, String businessId, String nopaperDesc) {
		this.nopaperId = nopaperId;
		this.source = source;
		this.setECMLogonParameter(ECMLogonParameter);
		this.setECMQueryParameter(ECMOtherParameter);
		this.businessId = businessId;
		this.nopaperDesc = nopaperDesc;
	}

	public String getNopaperId() {
		return nopaperId;
	}

	public void setNopaperId(String nopaperId) {
		this.nopaperId = nopaperId;
	}

	public String getSource() {
		return source;
	}

	public void setSource(String source) {
		this.source = source;
	}

	public String getBusinessId() {
		return businessId;
	}

	public void setBusinessId(String businessId) {
		this.businessId = businessId;
	}

	public String getNopaperDesc() {
		return nopaperDesc;
	}

	public void setNopaperDesc(String nopaperDesc) {
		this.nopaperDesc = nopaperDesc;
	}

	public String getTMSJobId() {
		return TMSJobId;
	}

	public void setTMSJobId(String tMSJobId) {
		TMSJobId = tMSJobId;
	}

	public String getFTPOtherParameter() {
		return FTPOtherParameter;
	}

	public void setFTPOtherParameter(String fTPOtherParameter) {
		FTPOtherParameter = fTPOtherParameter;
	}

	public String getFTPLogonParameter() {
		return FTPLogonParameter;
	}

	public void setFTPLogonParameter(String fTPLogonParameter) {
		FTPLogonParameter = fTPLogonParameter;
	}

	public String getECMLogonParameter() {
		return ECMLogonParameter;
	}

	public void setECMLogonParameter(String eCMLogonParameter) {
		ECMLogonParameter = eCMLogonParameter;
	}

	public String getECMUploadParameter() {
		return ECMUploadParameter;
	}

	public void setECMUploadParameter(String eCMUploadParameter) {
		ECMUploadParameter = eCMUploadParameter;
	}

	

	public String getECMQueryParameter() {
		return ECMQueryParameter;
	}

	public void setECMQueryParameter(String eCMQueryParameter) {
		ECMQueryParameter = eCMQueryParameter;
	}

	public String getNeedUpload() {
		return needUpload;
	}

	public void setNeedUpload(String needUpload) {
		this.needUpload = needUpload;
	}

	public String getPsLevel() {
		return psLevel;
	}

	public void setPsLevel(String psLevel) {
		this.psLevel = psLevel;
	}

	public String getIndexValueNew() {
		return indexValueNew;
	}

	public void setIndexValueNew(String indexValueNew) {
		this.indexValueNew = indexValueNew;
	}


	

	
}
