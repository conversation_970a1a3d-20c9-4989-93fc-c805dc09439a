package com.sunyard.etl.nps.service.business;

import java.io.File;
import java.sql.Connection;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.sunyard.util.dbutil.DBHandler;
import org.sunyard.util.file.FileUtil;

import com.sunyard.etl.nps.common.Parameters;
import com.sunyard.etl.nps.dao.OutputToADMS4Dao;
import com.sunyard.etl.nps.dao.OutputToADMS4DaoImpl;
import com.sunyard.etl.nps.model.BpTmpbatchTb;
import com.sunyard.etl.nps.model.BpTmpdata1Tb;
import com.sunyard.etl.nps.service.base.FileService;
import com.sunyard.etl.system.common.Constants;
import com.xxl.job.core.log.XxlJobLogger;

public class OutputToADMS4Service {

	private OutputToADMS4Dao outputToADMS4Dao = new OutputToADMS4DaoImpl();
	protected final Logger log = LoggerFactory.getLogger(getClass());
	private FileUtil fileUtil = new FileUtil();
	private FileService fileService = new FileService();

	
	
	/**
	 * 混合无纸化业务接入到事后监督（源自江南农商VTM混合业务接入）（参考）
	 * 
	 * <AUTHOR> 2017年7月10日
	 * @param tableName
	 * @return
	 */
	public int MixedNopaper(String tableName) {
		String mixedDataDate = null;
		String vtmDataDate = null;
		BpTmpbatchTb bpTmpbatchTb = null;
		
		List<String> pendingDateList = new ArrayList<String>();
		Map<String,String> JPGPathMap = new HashMap<String,String>();
		
		// 批次表中需要处理混合批次日期
		try {
			pendingDateList = outputToADMS4Dao.getPendingDateList();
		} catch (SQLException e1) {
			e1.printStackTrace();
		}
		if (pendingDateList == null || pendingDateList.size() == 0) {
			XxlJobLogger.log("当前批次表中没有需要处理的混合业务,本次为执行插入操作", tableName);
			return 0;
		}
		
		
		XxlJobLogger.log("本次处理日期："+ pendingDateList.toString(), tableName);
		log.info("本次处理日期："+ pendingDateList.toString(), tableName);
		// 按日期来处理
		for (int dateIndex = 0; dateIndex < pendingDateList.size(); dateIndex++) {
			mixedDataDate = pendingDateList.get(dateIndex);
			try {
				vtmDataDate = outputToADMS4Dao.vtmDataDate();
			} catch (SQLException e) {
				e.printStackTrace();
			}
			String FTPBasePath = Parameters.fileRootPath + File.separator + mixedDataDate + File.separator;
			String IMGBasePath = Parameters.dsRoot;
			// 检查VTM导入是否完成（条件：日期） 
			if (mixedDataDate.compareTo(vtmDataDate) < 0) {
				XxlJobLogger.log(mixedDataDate+"的VTM导入未完成", tableName);
				continue;
			}
			// 检查FTP目录下图像标志文件是否存在（条件：日期）
			if (!fileUtil.fileIsComplete(FTPBasePath + mixedDataDate + Parameters.flagFileSuffix)) {
				XxlJobLogger.log("FTP目录下不存在" + mixedDataDate + "的文件", tableName);
				XxlJobLogger.log("路径："+FTPBasePath + mixedDataDate + Parameters.flagFileSuffix,tableName);
				continue;
			}
			// 获取每笔业务对应的JPG图像路径（条件：日期）
			try {
				JPGPathMap = outputToADMS4Dao.getJPGPathMap(mixedDataDate);
			} catch (SQLException e) {
				e.printStackTrace();
			}
			if (JPGPathMap == null || JPGPathMap.size() == 0 ) {
				XxlJobLogger.log("混合业务表中没有" + mixedDataDate + "的数据", tableName);
				continue;
			}
			// 从批次表提取待处理批次（条件：日期）
			List<BpTmpbatchTb> batchInfoList = null;
			try {
				batchInfoList = outputToADMS4Dao.getPendingBatch(mixedDataDate);
			} catch (SQLException e) {
				e.printStackTrace();
			}
			if (batchInfoList == null || batchInfoList.size() == 0) {
				XxlJobLogger.log("批次表中没有" + mixedDataDate + "需要处理的混合业务", tableName);
				continue;
			}
			// 按批次进行处理
			for (int batchIndex = 0; batchIndex < batchInfoList.size(); batchIndex++) {
				// 获取一个批次
				bpTmpbatchTb = batchInfoList.get(batchIndex);
				// 检查IMG文件是否存在
				if (!fileUtil.fileIsComplete(IMGBasePath + File.separator + mixedDataDate + File.separator + bpTmpbatchTb.getBatchId()+".IMG")) {
					XxlJobLogger.log("批次" + bpTmpbatchTb.getBatchId() + ".IMG文件不存在", tableName);
					XxlJobLogger.log(IMGBasePath + File.separator + mixedDataDate + File.separator + bpTmpbatchTb.getSiteNo(), tableName);
					continue;
				}
				// 获取图像信息（条件：已经完成勾兑的主件）
				List<BpTmpdata1Tb> bpTmpdataList = null;
				try {
					bpTmpdataList = outputToADMS4Dao.getBpTmpData(bpTmpbatchTb.getBatchId());
				} catch (SQLException e) {
					e.printStackTrace();
				}
				if (bpTmpdataList == null || bpTmpdataList.size() == 0) {
					XxlJobLogger.log("批次" + bpTmpbatchTb.getBatchId() + "对应的数据表中没有数据", tableName);
					continue;
				}
				for (int dataIndex = 0; dataIndex < bpTmpdataList.size(); dataIndex++) {
					// 获取一笔业务的主件信息
					BpTmpdata1Tb mainBpTmpdata1Tb = bpTmpdataList.get(dataIndex);
					String JPGPathArray[] = null;
					String JPGPath = "";
		
					if(!JPGPathMap.containsKey(mainBpTmpdata1Tb.getFlowId())) {
						XxlJobLogger.log("混合业务表中找不到流水号：" + mainBpTmpdata1Tb.getFlowId(), tableName);
						continue;
					}
					// 读取文件：读取JPG附件，附件可能有多张
					JPGPath = JPGPathMap.get(mainBpTmpdata1Tb.getFlowId());
					if (JPGPath.contains(";")) {
						JPGPathArray = JPGPath.split(";");
						for (int pathIndex = 0; pathIndex < JPGPathArray.length; pathIndex++) {
							File file = new File(FTPBasePath + JPGPathArray[pathIndex]);
							int insertResult = insertJPG(bpTmpbatchTb, mainBpTmpdata1Tb, file);
							if (insertResult == 0) {
								XxlJobLogger.log("批次号："+mainBpTmpdata1Tb.getBatchId()+" 中批内码："+mainBpTmpdata1Tb.getInccodeinBatch()+"的JPG附件插入失败，请检查日志：", tableName);
								continue;
							}
						}
					} else {
						File file = new File(FTPBasePath + JPGPath);
						int insertResult = insertJPG(bpTmpbatchTb, mainBpTmpdata1Tb, file);
						if (insertResult == 0) {
							XxlJobLogger.log("批次号："+mainBpTmpdata1Tb.getBatchId()+" 中批内码："+mainBpTmpdata1Tb.getInccodeinBatch()+"的JPG附件插入失败，请检查日志：", tableName);
							continue;
						}
					}
					JPGPathMap.remove(mainBpTmpdata1Tb.getFlowId());// 一笔业务处理完成，将该条流水移除
				}
				//将JPGPathMap中剩余的所有图像都插入到批次的最后
				if (JPGPathMap.size() != 0) {
					for (Iterator<String> i = JPGPathMap.keySet().iterator(); i.hasNext();) {
						String flowId = i.next();
						String JPGPath = JPGPathMap.get(flowId);
						String JPGPathArray[] = JPGPath.split(";");
						for (int pathIndex = 0; pathIndex < JPGPathArray.length; pathIndex++) {
							File file = new File(FTPBasePath + JPGPathArray[pathIndex]);
							int insertResult = insertJPGAtLast(bpTmpbatchTb, flowId, file);
							if (insertResult == 0) {
								XxlJobLogger.log("批次号："+bpTmpbatchTb.getBatchId()+" 剩余的JPG附件插入失败，请检查日志：", tableName);
								continue;
							}
						}
					}
				}
			}
		}
		return 1;
	}
	
	
	/**
	 * 
	 * @param IMGPath
	 * @param JPGPath
	 * @return 0-失败 1-成功
	 */
	public int addToFileServer(String IMGPath, String JPGPath) {
		return fileService.addToFileServer(Parameters.fsMachineIp + "",
				Parameters.fsMachinePort + "", IMGPath, JPGPath);
	}
	
	
	/**
	 * 插入一张VTM附件，该附件的主件已经完成流水勾兑，附件追加到主件后面
	 * 	//批次，待插入附件的主件，附件文件
	 * @param bpTmpbatchTb
	 * @param mainBpTmpdata1Tb
	 * @param file
	 * @return
	 * @throws SQLException 
	 */
	public int insertJPG(BpTmpbatchTb bpTmpbatchTb, BpTmpdata1Tb mainBpTmpdata1Tb, File file)  {
		//给要插入的附件生成数据表对象，同时赋值
		BpTmpdata1Tb addBpTmpdata1Tb = new BpTmpdata1Tb();
		Long offset = null;
		try {
			offset = outputToADMS4Dao.getMaxOffset(bpTmpbatchTb.getBatchId());
		} catch (SQLException e) {
			e.printStackTrace();
		}
		addBpTmpdata1Tb.setBatchId(mainBpTmpdata1Tb.getBatchId());
		addBpTmpdata1Tb.setInccodeinBatch(mainBpTmpdata1Tb.getInccodeinBatch() + 2 ); //主件的批内码+2
		addBpTmpdata1Tb.setOffsetOfImage(offset);
		addBpTmpdata1Tb.setFlowId(mainBpTmpdata1Tb.getFlowId());
		addBpTmpdata1Tb.setLengthOfImage(FileUtil.size(file));
		return dataOperation(bpTmpbatchTb, addBpTmpdata1Tb, file);
	}
	

	/**
	 * 插入一张VTM附件，该附件的主件还没有勾兑上流水，附件追加到批次的最后
	 * 	//批次，附件文件，附件所属的流水号
	 * @param bpTmpbatchTb
	 * @param file
	 * @param FlowId
	 * @return 0-失败 1-成功
	 * @throws SQLException 
	 */
	public int insertJPGAtLast(BpTmpbatchTb bpTmpbatchTb, String FlowId, File file) {
		//给要插入的附件生成数据表对象，同时赋值
		BpTmpdata1Tb addBpTmpdata1Tb = new BpTmpdata1Tb();
		Long offset = null;
		try {
			offset = outputToADMS4Dao.getMaxOffset(bpTmpbatchTb.getBatchId());
		} catch (SQLException e) {
			e.printStackTrace();
		}
		addBpTmpdata1Tb.setBatchId(bpTmpbatchTb.getBatchId());
		addBpTmpdata1Tb.setInccodeinBatch(Integer.parseInt(bpTmpbatchTb.getBatchTotalPage()) + 1); //批内码为批次总张数+1
		addBpTmpdata1Tb.setOffsetOfImage(offset);
		addBpTmpdata1Tb.setFlowId(FlowId);
		addBpTmpdata1Tb.setLengthOfImage(FileUtil.size(file));
		return dataOperation(bpTmpbatchTb, addBpTmpdata1Tb, file);
	}
	
	
	
	/**
	 * 数据操作:插入数据表、更新批次表、追加文件到.IMG
	 * @param bpTmpbatchTb
	 * @param addBpTmpdata1Tb
	 * @param file
	 * @return
	 */
	public int dataOperation(BpTmpbatchTb bpTmpbatchTb, BpTmpdata1Tb addBpTmpdata1Tb, File file) {
		DBHandler dbHandler = new DBHandler(Constants.CONN_POOL_NAME);
		int flag = 0;
		Connection conn = null;
		try {
			conn.setAutoCommit(false);//取消自动提交
			while (true) {
				flag = outputToADMS4Dao.updateInccodeinBatch(conn, bpTmpbatchTb.getBatchId(), addBpTmpdata1Tb.getInccodeinBatch()+"");// 更新批内码
				if (flag == 0)break;
				flag = outputToADMS4Dao.inertBpTmpata1Tb(conn, addBpTmpdata1Tb);// 插入附件的图像数据
				if (flag == 0)break;
				flag = outputToADMS4Dao.updateBatchInfo(conn, bpTmpbatchTb);	// 更新批次表的信息
				if (flag == 0)break;
				flag = outputToADMS4Dao.updateSmMixedNopaperTb(conn, bpTmpbatchTb.getOccurDate(), addBpTmpdata1Tb.getFlowId());// 更新混合业务表这张附件的处理状态
				if (flag == 0)break;
				// 追加图像文件到.IMG
				flag = addToFileServer(bpTmpbatchTb.getLargeFileName(),file.getAbsolutePath());
				if (flag == 0)break;
				flag = 1;break;
			}
			if (flag == 0) {
				conn.rollback();
			} else {
				conn.commit();
			}
		} catch (SQLException e) {
			e.printStackTrace();
		} finally {
		}
		return flag;
	}

}
