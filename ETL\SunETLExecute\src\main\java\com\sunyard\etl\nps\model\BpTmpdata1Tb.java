package com.sunyard.etl.nps.model;

public class BpTmpdata1Tb {

	// 共用字段
	private String batchId;
	private String checkFlag;  //数据库 默认是-1未勾兑    0强过（附件） 1 勾兑成功 
	private String flowId;
	private String formName;
	private int inccodeinBatch;
	private String processState;
	private String psLevel; // 5.x 0-附件 1-主件 、4.x 1-附件 0-主件
	private String contentId;
	
	
	// 4.x字段
	private long lengthOfImage;
	private long offsetOfImage;
	private String slaveCount;

	// 5.x 字段
	private String copyInccodein; // 默认0 
	private String copyRec;     // 默认0 
	private String isFrontPage = "1";
	private int primaryInccodein;  // 默认0  
	private String formGroup;
	private String vouhType;
	private String errorFlag;
	private String isAudit;
	private String selfDelete;
	private String memo;
	private String imageSize;  // 
	private String backImageSize; //
	private String patchFlag;
	private String fileName;
	private String backFileName;
	private String copySerialno;
	private String isSign;
	private String superviseDeal;
	private String dataSourceId;

	public BpTmpdata1Tb() {

	}

	public BpTmpdata1Tb(String batchId, String checkFlag, String flowId,
			String formName, int inccodeinBatch, long lengthOfImage,
			long offsetOfImage, String processState, String psLevel,
			String slaveCount) {
		this.batchId = batchId;
		this.checkFlag = checkFlag;
		this.flowId = flowId;
		this.formName = formName;
		this.inccodeinBatch = inccodeinBatch;
		this.lengthOfImage = lengthOfImage;
		this.offsetOfImage = offsetOfImage;
		this.processState = processState;
		this.psLevel = psLevel;
	}

	
	
	public String getBatchId() {
		return batchId;
	}

	public void setBatchId(String batchId) {
		this.batchId = batchId;
	}

	public String getCheckFlag() {
		return checkFlag;
	}

	public void setCheckFlag(String checkFlag) {
		this.checkFlag = checkFlag;
	}

	public String getFlowId() {
		return flowId;
	}

	public void setFlowId(String flowId) {
		this.flowId = flowId;
	}

	public String getFormName() {
		return formName;
	}

	public void setFormName(String formName) {
		this.formName = formName;
	}

	public int getInccodeinBatch() {
		return inccodeinBatch;
	}

	public void setInccodeinBatch(int inccodeinBatch) {
		this.inccodeinBatch = inccodeinBatch;
	}

	public long getLengthOfImage() {
		return lengthOfImage;
	}

	public void setLengthOfImage(Long lengthOfImage) {
		this.lengthOfImage = lengthOfImage;
	}

	public long getOffsetOfImage() {
		return offsetOfImage;
	}

	public void setOffsetOfImage(Long offsetOfImage) {
		this.offsetOfImage = offsetOfImage;
	}

	public String getProcessState() {
		return processState;
	}

	public void setProcessState(String processState) {
		this.processState = processState;
	}

	public String getPsLevel() {
		return psLevel;
	}

	public void setPsLevel(String psLevel) {
		this.psLevel = psLevel;
	}

	public String getSlaveCount() {
		return slaveCount;
	}

	public void setSlaveCount(String slaveCount) {
		this.slaveCount = slaveCount;
	}


	public String getCopyInccodein() {
		return copyInccodein;
	}

	public void setCopyInccodein(String copyInccodein) {
		this.copyInccodein = copyInccodein;
	}

	public String getCopyRec() {
		return copyRec;
	}

	public void setCopyRec(String copyRec) {
		this.copyRec = copyRec;
	}

	public String getIsFrontPage() {
		return isFrontPage;
	}

	public void setIsFrontPage(String isFrontPage) {
		this.isFrontPage = isFrontPage;
	}

	public int getPrimaryInccodein() {
		return primaryInccodein;
	}

	public void setPrimaryInccodein(int primaryInccodein) {
		this.primaryInccodein = primaryInccodein;
	}

	public String getFormGroup() {
		return formGroup;
	}

	public void setFormGroup(String formGroup) {
		this.formGroup = formGroup;
	}

	public String getVouhType() {
		return vouhType;
	}

	public void setVouhType(String vouhType) {
		this.vouhType = vouhType;
	}

	public String getErrorFlag() {
		return errorFlag;
	}

	public void setErrorFlag(String errorFlag) {
		this.errorFlag = errorFlag;
	}

	public String getIsAudit() {
		return isAudit;
	}

	public void setIsAudit(String isAudit) {
		this.isAudit = isAudit;
	}

	public String getSelfDelete() {
		return selfDelete;
	}

	public void setSelfDelete(String selfDelete) {
		this.selfDelete = selfDelete;
	}

	public String getMemo() {
		return memo;
	}

	public void setMemo(String memo) {
		this.memo = memo;
	}

	public String getImageSize() {
		return imageSize;
	}

	public void setImageSize(String imageSize) {
		this.imageSize = imageSize;
	}

	public String getBackImageSize() {
		return backImageSize;
	}

	public void setBackImageSize(String backImageSize) {
		this.backImageSize = backImageSize;
	}

	public String getPatchFlag() {
		return patchFlag;
	}

	public void setPatchFlag(String patchFlag) {
		this.patchFlag = patchFlag;
	}

	public String getFileName() {
		return fileName;
	}

	public void setFileName(String fileName) {
		this.fileName = fileName;
	}

	public String getBackFileName() {
		return backFileName;
	}

	public void setBackFileName(String backFileName) {
		this.backFileName = backFileName;
	}

	public String getCopySerialno() {
		return copySerialno;
	}

	public void setCopySerialno(String copySerialno) {
		this.copySerialno = copySerialno;
	}

	public String getIsSign() {
		return isSign;
	}

	public void setIsSign(String isSign) {
		this.isSign = isSign;
	}

	public String getSuperviseDeal() {
		return superviseDeal;
	}

	public void setSuperviseDeal(String superviseDeal) {
		this.superviseDeal = superviseDeal;
	}

	public void setLengthOfImage(long lengthOfImage) {
		this.lengthOfImage = lengthOfImage;
	}

	public void setOffsetOfImage(long offsetOfImage) {
		this.offsetOfImage = offsetOfImage;
	}

	public String getContentId() {
		return contentId;
	}

	public void setContentId(String contentId) {
		this.contentId = contentId;
	}

	public String getDataSourceId() {
		return dataSourceId;
	}

	public void setDataSourceId(String dataSourceId) {
		this.dataSourceId = dataSourceId;
	}
}
