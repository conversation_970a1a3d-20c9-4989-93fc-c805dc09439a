{"remainingRequest": "E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\node_modules\\babel-loader\\lib\\index.js!E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\node_modules\\eslint-loader\\index.js??ref--13-0!E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\src\\api\\views\\system\\config\\encryp\\index.js", "dependencies": [{"path": "E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\src\\api\\views\\system\\config\\encryp\\index.js", "mtime": 1686019810591}, {"path": "E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\babel.config.js", "mtime": 1686019818359}, {"path": "E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\.babelrc", "mtime": 1705285078668}, {"path": "E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\node_modules\\eslint-loader\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:aW1wb3J0IHJlcXVlc3QgZnJvbSAnQC91dGlscy9yZXF1ZXN0JzsKaW1wb3J0IGRlZmF1bHRTZXR0aW5ncyBmcm9tICdAL3NldHRpbmdzJzsKdmFyIHByZWZpeCA9IGRlZmF1bHRTZXR0aW5ncy5zZXJ2aWNlLnN5c3RlbSArICcvZW5jcnlwRmllbGQnOwoKLy8g5pWP5oSf5a2X5q615Yqg5a+G6YWN572u55u45YWz5o6l5Y+jCmV4cG9ydCB2YXIgU3lzRW5jID0gewogIHF1ZXJ5OiBmdW5jdGlvbiBxdWVyeShkYXRhKSB7CiAgICAvLyDnlKjmiLfmn6Xor6IKICAgIHJldHVybiByZXF1ZXN0KHsKICAgICAgdXJsOiBwcmVmaXggKyAnL3F1ZXJ5LmRvJywKICAgICAgbWV0aG9kOiAnZ2V0JywKICAgICAgcGFyYW1zOiB7CiAgICAgICAgbWVzc2FnZTogZGF0YQogICAgICB9CiAgICB9KTsKICB9LAogIGFkZDogZnVuY3Rpb24gYWRkKGRhdGEpIHsKICAgIC8vIOaWsOWingogICAgcmV0dXJuIHJlcXVlc3QoewogICAgICB1cmw6IHByZWZpeCArICcvYWRkLmRvJywKICAgICAgbWV0aG9kOiAncG9zdCcsCiAgICAgIGRhdGE6IGRhdGEKICAgIH0pOwogIH0sCiAgbW9kaWZ5OiBmdW5jdGlvbiBtb2RpZnkoZGF0YSkgewogICAgLy8g5L+u5pS5CiAgICByZXR1cm4gcmVxdWVzdCh7CiAgICAgIHVybDogcHJlZml4ICsgJy9tb2RpZnkuZG8nLAogICAgICBtZXRob2Q6ICdwb3N0JywKICAgICAgZGF0YTogZGF0YQogICAgfSk7CiAgfSwKICBkZWw6IGZ1bmN0aW9uIGRlbChkYXRhKSB7CiAgICAvLyDliKDpmaQKICAgIHJldHVybiByZXF1ZXN0KHsKICAgICAgdXJsOiBwcmVmaXggKyAnL2RlbGV0ZS5kbycsCiAgICAgIG1ldGhvZDogJ2RlbGV0ZScsCiAgICAgIGRhdGE6IGRhdGEKICAgIH0pOwogIH0KfTs="}, {"version": 3, "names": ["request", "defaultSettings", "prefix", "service", "system", "SysEnc", "query", "data", "url", "method", "params", "message", "add", "modify", "del"], "sources": ["E:/1_Project/XYD_Project/dop-4.0/dop-4.1-qianduan-unify/dopUnify/src/api/views/system/config/encryp/index.js"], "sourcesContent": ["import request from '@/utils/request'\r\nimport defaultSettings from '@/settings'\r\nconst prefix = defaultSettings.service.system + '/encrypField'\r\n\r\n// 敏感字段加密配置相关接口\r\nexport const SysEnc = {\r\n  query(data) {\r\n    // 用户查询\r\n    return request({\r\n      url: prefix + '/query.do',\r\n      method: 'get',\r\n      params: { message: data }\r\n    })\r\n  },\r\n  add(data) {\r\n    // 新增\r\n    return request({\r\n      url: prefix + '/add.do',\r\n      method: 'post',\r\n      data\r\n    })\r\n  },\r\n  modify(data) {\r\n    // 修改\r\n    return request({\r\n      url: prefix + '/modify.do',\r\n      method: 'post',\r\n      data\r\n    })\r\n  },\r\n  del(data) {\r\n    // 删除\r\n    return request({\r\n      url: prefix + '/delete.do',\r\n      method: 'delete',\r\n      data\r\n    })\r\n  }\r\n}\r\n"], "mappings": "AAAA,OAAOA,OAAO,MAAM,iBAAiB;AACrC,OAAOC,eAAe,MAAM,YAAY;AACxC,IAAMC,MAAM,GAAGD,eAAe,CAACE,OAAO,CAACC,MAAM,GAAG,cAAc;;AAE9D;AACA,OAAO,IAAMC,MAAM,GAAG;EACpBC,KAAK,iBAACC,IAAI,EAAE;IACV;IACA,OAAOP,OAAO,CAAC;MACbQ,GAAG,EAAEN,MAAM,GAAG,WAAW;MACzBO,MAAM,EAAE,KAAK;MACbC,MAAM,EAAE;QAAEC,OAAO,EAAEJ;MAAK;IAC1B,CAAC,CAAC;EACJ,CAAC;EACDK,GAAG,eAACL,IAAI,EAAE;IACR;IACA,OAAOP,OAAO,CAAC;MACbQ,GAAG,EAAEN,MAAM,GAAG,SAAS;MACvBO,MAAM,EAAE,MAAM;MACdF,IAAI,EAAJA;IACF,CAAC,CAAC;EACJ,CAAC;EACDM,MAAM,kBAACN,IAAI,EAAE;IACX;IACA,OAAOP,OAAO,CAAC;MACbQ,GAAG,EAAEN,MAAM,GAAG,YAAY;MAC1BO,MAAM,EAAE,MAAM;MACdF,IAAI,EAAJA;IACF,CAAC,CAAC;EACJ,CAAC;EACDO,GAAG,eAACP,IAAI,EAAE;IACR;IACA,OAAOP,OAAO,CAAC;MACbQ,GAAG,EAAEN,MAAM,GAAG,YAAY;MAC1BO,MAAM,EAAE,QAAQ;MAChBF,IAAI,EAAJA;IACF,CAAC,CAAC;EACJ;AACF,CAAC"}]}