{"remainingRequest": "E:\\华南区项目\\中国银行\\开发区\\科技公司运营平台\\前端\\dopUnify\\node_modules\\babel-loader\\lib\\index.js!E:\\华南区项目\\中国银行\\开发区\\科技公司运营平台\\前端\\dopUnify\\node_modules\\eslint-loader\\index.js??ref--13-0!E:\\华南区项目\\中国银行\\开发区\\科技公司运营平台\\前端\\dopUnify\\src\\views\\system\\externalManage\\priv\\info.js", "dependencies": [{"path": "E:\\华南区项目\\中国银行\\开发区\\科技公司运营平台\\前端\\dopUnify\\src\\views\\system\\externalManage\\priv\\info.js", "mtime": 1721631954662}, {"path": "E:\\华南区项目\\中国银行\\开发区\\科技公司运营平台\\前端\\dopUnify\\babel.config.js", "mtime": 1721631954662}, {"path": "E:\\华南区项目\\中国银行\\开发区\\科技公司运营平台\\前端\\dopUnify\\.babelrc", "mtime": 1721631954662}, {"path": "E:\\华南区项目\\中国银行\\开发区\\科技公司运营平台\\前端\\dopUnify\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "E:\\华南区项目\\中国银行\\开发区\\科技公司运营平台\\前端\\dopUnify\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "E:\\华南区项目\\中国银行\\开发区\\科技公司运营平台\\前端\\dopUnify\\node_modules\\eslint-loader\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8gaW1wb3J0IHsgZGljdGlvbmFyeUZpZWRzIH0gZnJvbSAnQC91dGlscy9kaWN0aW9uYXJ5JyAvLyDlrZflhbgKLy8g6KGo5Y2VCmV4cG9ydCB2YXIgY29uZmlnID0gZnVuY3Rpb24gY29uZmlnKHRoYXQpIHsKICByZXR1cm4gewogICAgc3lzdGVtX25vOiB7CiAgICAgIGNvbXBvbmVudDogJ3NlbGVjdCcsCiAgICAgIGxhYmVsOiAn57O757ufJywKICAgICAgY29sU3BhbjogOCwKICAgICAgbmFtZTogJ3N5c3RlbV9ubycsCiAgICAgIGNvbmZpZzogewogICAgICAgIC8vIGZvcm0taXRlbSDphY3nva4KICAgICAgfSwKICAgICAgY29tcG9uZW50UHJvcHM6IHsKICAgICAgICAvLyBpbnB1dOe7hOS7tumFjee9rgogICAgICAgIHBsYWNlaG9sZGVyOiAn6K+36Y<PERSON><PERSON><PERSON>oupJywKICAgICAgICBjbGVhcmFibGU6IHRydWUKICAgICAgfSwKICAgICAgb3B0aW9uczogW10KICAgIH0KICB9Owp9Ow=="}, {"version": 3, "names": ["config", "that", "system_no", "component", "label", "colSpan", "name", "componentProps", "placeholder", "clearable", "options"], "sources": ["E:/华南区项目/中国银行/开发区/科技公司运营平台/前端/dopUnify/src/views/system/externalManage/priv/info.js"], "sourcesContent": ["// import { dictionaryFieds } from '@/utils/dictionary' // 字典\r\n// 表单\r\nexport const config = (that) => ({\r\n  system_no: {\r\n    component: 'select',\r\n    label: '系统',\r\n    colSpan: 8,\r\n    name: 'system_no',\r\n    config: {\r\n      // form-item 配置\r\n    },\r\n    componentProps: {\r\n      // input组件配置\r\n      placeholder: '请选择',\r\n      clearable: true\r\n    },\r\n    options: []\r\n  }\r\n})\r\n"], "mappings": "AAAA;AACA;AACA,OAAO,IAAMA,MAAM,GAAG,SAATA,MAAM,CAAIC,IAAI;EAAA,OAAM;IAC/BC,SAAS,EAAE;MACTC,SAAS,EAAE,QAAQ;MACnBC,KAAK,EAAE,IAAI;MACXC,OAAO,EAAE,CAAC;MACVC,IAAI,EAAE,WAAW;MACjBN,MAAM,EAAE;QACN;MAAA,CACD;MACDO,cAAc,EAAE;QACd;QACAC,WAAW,EAAE,KAAK;QAClBC,SAAS,EAAE;MACb,CAAC;MACDC,OAAO,EAAE;IACX;EACF,CAAC;AAAA,CAAC"}]}