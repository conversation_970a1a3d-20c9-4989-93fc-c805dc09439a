{"remainingRequest": "E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-q<PERSON><PERSON>n\\数字运营平台-统一门户工程\\dopUnify\\node_modules\\babel-loader\\lib\\index.js!E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan\\数字运营平台-统一门户工程\\dopUnify\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan\\数字运营平台-统一门户工程\\dopUnify\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan\\数字运营平台-统一门户工程\\dopUnify\\src\\views\\home\\component\\todoList\\index.vue?vue&type=script&lang=js&", "dependencies": [{"path": "E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-<PERSON><PERSON><PERSON>n\\数字运营平台-统一门户工程\\dopUnify\\src\\views\\home\\component\\todoList\\index.vue", "mtime": 1686019809513}, {"path": "E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-<PERSON><PERSON><PERSON>n\\数字运营平台-统一门户工程\\dopUnify\\babel.config.js", "mtime": 1686019818359}, {"path": "E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-<PERSON><PERSON><PERSON>n\\数字运营平台-统一门户工程\\dopUnify\\.babelrc", "mtime": 1705285078668}, {"path": "E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-<PERSON><PERSON><PERSON>n\\数字运营平台-统一门户工程\\dopUnify\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-<PERSON><PERSON><PERSON>n\\数字运营平台-统一门户工程\\dopUnify\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-<PERSON><PERSON><PERSON>n\\数字运营平台-统一门户工程\\dopUnify\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-<PERSON><PERSON><PERSON>n\\数字运营平台-统一门户工程\\dopUnify\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1655715099000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAgIA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EAAAA;EAAAC;AACA;EACAC;EACAC;IACA;MACAC;MACAC;MAAA;MACAC;MACAC;MACAC;MAAA;MACAC;MAAA;MACAC;MAAA;MACAC;MAAA;MACAC,YACA;MAAA;MACA;MAAA;MACA;MAAA;MACA;MAAA,CACA;;MACAC,UACA,yBACA,yBACA,2BACA,yBACA;MACAC,eACA;MAAA;MACA;MAAA;MACA;MAAA;MACA;MAAA,CACA;;MACAC;MAAA;MACAC;IACA;EACA;EACAC;IACA;IACAC;MACA;QACA;MACA;IACA;EACA;EACAC;IACA;IACA;IACA;EAAA,CACA;EACAC;IACA;EACA;EACAC;IAAA;IACA;MACA;QACA;MACA;IACA;IACA;IACA;IACA;IACA;IACA;EACA;EACAC;IACAC;IACA;EACA;EACAC;IACA;AACA;AACA;IACAC;MACA;MACA;MACA;MACA;IACA;IACA;IACAC;MAAA;MACA;MACA;QACA;MACA;IACA;IACA;AACA;AACA;IACAC;MAAA;MACA;MACA;MACA;QACAC;QACAC;MACA;;MACA7B;QACA;QACA;UACA;YACA4B;YACAE;YACAC;YACAC;YACAC;YACAC;YACAC;YACAC,UACAC,6BACAA,8BACAA,6BACA,KACAA;YACAC;YACAC;YAAA;YACAC;UACA;UACA;UACAvC;YACA;YACA;YACA,mDACA;YACA;cACA;gBACA;gBACAwC;gBAAA;gBACAC,+BACAC,SACA,gCACAF,qBACAA,iCACAA,yBACA,GACA,mBACAA;cACA;cACA;YACA;;YACA;UACA;QACA;MACA;IACA;IACA;AACA;IACAG;MAAA;MACA;QACAhB;QACAI;QACAC;QACAC;QACAC;MACA;MACAU;QACA;UAAAC;UAAAC;QACA;QAAA,2BACAN;UACA;YACA;cACAO;YACA;UACA;QAAA;QALA;UAAA;QAMA;QACA;QACA;QACA;MACA;IACA;IACA;AACA;IACAC;MACA;IACA;IACA;AACA;AACA;IACAC;MACA;IACA;IACA;AACA;IACAC;MACA;IACA;IACA;AACA;AACA;IACAC;MACA;IACA;EACA;AACA", "names": ["selectClassify", "queryMsg", "name", "data", "User", "title", "list", "svgArr", "selectClass", "todoList", "menuName", "delay", "iconColor", "iconSvg", "iconBigColor", "listShow", "height", "computed", "varColor", "watch", "created", "mounted", "<PERSON><PERSON><PERSON><PERSON>", "clearTimeout", "methods", "getHeight", "getMore", "handleClick", "parameterList", "content_title", "currentPage", "pageSize", "user_no", "organ_no", "organ_level", "role_no", "msg_type", "val", "classify", "msg_content", "deal_state", "item", "process", "prefix", "getList", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "returnList", "msgMap", "i", "handlePrint", "openSystemMsg", "dialogSumbit", "changeVisible"], "sourceRoot": "src/views/home/<USER>/todoList", "sources": ["index.vue"], "sourcesContent": ["<!--\n* 首页-待办事项\n-->\n<template>\n  <div ref=\"box1\" class=\"homeContent\">\n    <h3 class=\"homePageTitle\">\n      {{ title }}\n      <span v-show=\"listShow\" class=\"InfoMore\" @click=\"getMore\">更多 ></span>\n    </h3>\n    <div ref=\"box2\" class=\"todoBox\">\n      <div\n        v-for=\"(item, index) in list\"\n        :key=\"item.id\"\n        class=\"boxItem\"\n        :style=\"{ background: iconColor[index % 4] }\"\n        :class=\"{\n          importantInformationColor: item.message_type === '重要信息',\n          transactionApprovalColor: item.message_type === '事务审批',\n          otherDefualtColor: item.message_type === '其他',\n          checkAdministrationColor: item.message_type === '检查管理',\n          errorManagementColor: item.message_type === '差错管理'\n        }\"\n        @click=\"handleClick(item)\"\n      >\n        <div class=\"firstLine\">\n          <span class=\"svgSpan\">\n            <!-- <sun-svg-icon icon-class=\"systemManage\" /> -->\n            <sun-svg-icon\n              v-if=\"item.message_type === '事务审批'\"\n              class=\"svgIcon\"\n              icon-class=\"transactionApproval\"\n            />\n            <sun-svg-icon\n              v-else-if=\"item.message_type === '检查管理'\"\n              class=\"svgIcon\"\n              icon-class=\"checkAdministration\"\n            />\n            <sun-svg-icon\n              v-else-if=\"item.message_type === '重要信息'\"\n              class=\"svgIcon\"\n              icon-class=\"importantInformation\"\n            />\n            <sun-svg-icon\n              v-else-if=\"item.message_type === '差错管理'\"\n              class=\"svgIcon\"\n              icon-class=\"errorManagement\"\n            />\n            <sun-svg-icon\n              v-else-if=\"item.message_type === '其他'\"\n              class=\"svgIcon\"\n              icon-class=\"otherDefualt\"\n            />\n            <sun-svg-icon\n              v-else\n              class=\"svgIcon\"\n              :icon-class=\"iconSvg[index % 4]\"\n            />\n            <!-- <svg>\n              <use xlink:href=\"#icon-transactionApproval\" />\n            </svg> -->\n          </span>\n          <span class=\"numberSpan\">{{ item.count }}</span>\n        </div>\n\n        <div class=\"secondLine\">\n          <span>{{ item.message_type }} </span>\n        </div>\n        <div class=\"thirdLine\">\n          <sun-svg-icon\n            v-if=\"item.message_type === '事务审批'\"\n            icon-class=\"transactionApprovalBig\"\n            class=\"transactionApprovalBigIcon\"\n          />\n          <sun-svg-icon\n            v-else-if=\"item.message_type === '重要信息'\"\n            icon-class=\"importantInformationBig\"\n            class=\"importantInformationBigIcon\"\n          />\n          <sun-svg-icon\n            v-else-if=\"item.message_type === '其他'\"\n            icon-class=\"otherDefualtBig\"\n            class=\"otherDefualtBigIcon\"\n          />\n          <sun-svg-icon\n            v-else-if=\"item.message_type === '检查管理'\"\n            icon-class=\"checkAdministrationBig\"\n            class=\"checkAdministrationBigIcon\"\n          />\n          <sun-svg-icon\n            v-else-if=\"item.message_type === '差错管理'\"\n            icon-class=\"errorManagementBig\"\n            class=\"errorManagementBigIcon\"\n          />\n          <sun-svg-icon\n            v-else\n            icon-class=\"otherDefualtBig\"\n            :style=\"{ color: iconBigColor[index % 4] }\"\n          />\n        </div>\n      </div>\n    </div>\n    <div class=\"todoListBox\" :style=\"[varColor, { height: height }]\">\n      <div\n        v-for=\"(item, index) in todoList\"\n        :key=\"index\"\n        class=\"text item\"\n        @click=\"openSystemMsg(item)\"\n      >\n        <!-- <div class=\"el-icon-s-promotion\" /> -->\n        <!-- <sun-svg-icon icon-class=\"bell\" class=\"el-icon-s-promotion\" /> -->\n        <!-- <sun-svg-icon icon-class=\"systemManage\" class=\"el-icon-s-promotion\" /> -->\n        <img v-if=\"item.image\" :src=\"item.image\" class=\"userIcon\">\n        <img v-else :src=\"User\" class=\"userIcon\">\n        <div class=\"msgContent\">\n          <div class=\"msgTitle\">\n            {{ item.msg_content }}\n          </div>\n          <div class=\"msgDetail\">\n            <span>{{ item.create_user }}</span>\n            <span>{{ item.create_time | dateTimeFormat }}</span>\n          </div>\n        </div>\n      </div>\n    </div>\n  </div>\n</template>\n\n<script>\nimport { Home, system } from '@/api'\nimport otherSvg from '@/assets/img/newVersion/other.svg'\nimport User from '@/assets/img/main/user.png' // 默认头像\nimport { commonBlank } from '@/utils/common'\nimport defaultSettings from '@/settings'\nconst prefix = defaultSettings.service.system // 前缀公共路由\nconst { todoQuery } = Home\nconst { selectClassify, queryMsg } = system.SysMes\nexport default {\n  name: 'TodoList',\n  data() {\n    return {\n      User: User,\n      title: '待办事项', // 组件标题\n      list: [],\n      svgArr: [otherSvg],\n      selectClass: '', // 消息类型参数\n      todoList: [], // 系统消息\n      menuName: '', // 当前点击的菜单的名字\n      delay: '', // 定时器名称\n      iconColor: [\n        'linear-gradient(312deg, #4ebcfd 0%, #7ee3fc 100%)', // 青\n        'linear-gradient(315deg, #486ff5 0%, #64b5f7 100%)', // 蓝\n        'linear-gradient(315deg, #ff824e 0%, #fac74b 100%)', // 黄\n        'linear-gradient(315deg, #6dcaa3 0%, #7add7a 100%)' // 绿\n      ],\n      iconSvg: [\n        'color-otherDefualtOne',\n        'color-otherDefualtTwo',\n        'color-otherDefualtThree',\n        'color-otherDefualtFour'\n      ],\n      iconBigColor: [\n        'rgba(119, 204, 254, 0.8)', // 青\n        'rgba(91, 146, 248, 0.8)', // 蓝\n        'rgba(254, 167, 108, 0.8)', // 黄\n        'rgba(140, 220, 174, 0.8)' // 绿\n      ],\n      listShow: true, // 是否展示更多标识\n      height: ''\n    }\n  },\n  computed: {\n    // 计算属性调用store中的主题色\n    varColor() {\n      return {\n        '--color': this.$store.state.settings.theme\n      }\n    }\n  },\n  watch: {\n    // height(value) {\n    //   this.table.componentProps.height = value\n    // }\n  },\n  created() {\n    this.getList()\n  },\n  mounted() {\n    this.$bus.$on('updateHeight', (data) => {\n      if (data) {\n        this.getHeight()\n      }\n    })\n    // this.$nextTick(() => {\n    // })\n    // setTimeout(() => {\n    //   this.getHeight()\n    // }, 2000)\n  },\n  beforeDestroy() {\n    clearTimeout(this.delay)\n    this.$bus.$off('updateHeight')\n  },\n  methods: {\n    /**\n     * 计算表格高度\n     * 动态获取角色表格的高度 */\n    getHeight() {\n      const allHeight = this.$refs.box1.clientHeight\n      const boxHeight = this.$refs.box2.clientHeight\n      // console.log(allHeight, boxHeight)\n      this.height = allHeight - boxHeight - 75 + 'px'\n    },\n    // 点击更多进行路由跳转\n    getMore() {\n      this.$router.push('/systems')\n      this.delay = setTimeout(() => {\n        this.$bus.$emit('jumpRouter', this.menuName)\n      }, 600)\n    },\n    /**\n     * 查询对应待办事项下的列表\n     * @param {Object} val 当前点击的待办事项*/\n    handleClick(val) {\n      this.menuName = val.message_type\n      // 先进行类型查询请求    请求回来的数据作为参数进行消息列表查询参数\n      const msg = {\n        parameterList: [],\n        content_title: val.message_type // 当前点击菜单名称\n      }\n      selectClassify(msg).then((res) => {\n        this.selectClass = res.retMap.classify\n        if (res.retCode === '200') {\n          const msg2 = {\n            parameterList: [{}],\n            currentPage: 1,\n            pageSize: this.$store.getters.pageSize,\n            user_no: this.$store.getters.userNo,\n            organ_no: this.$store.getters.organNo,\n            organ_level: this.$store.getters.organLevel,\n            role_no: this.$store.getters.roleNo,\n            msg_type:\n              val.message_type === '全部' ||\n              val.message_type === '待处理' ||\n              val.message_type === '已处理'\n                ? ''\n                : val.field_value,\n            classify: this.selectClass,\n            msg_content: '', // 表单消息内容\n            deal_state: '0' // menu默认选中全部\n          }\n          // 查询\n          queryMsg(msg2).then((res) => {\n            const { list } = res.retMap\n            this.todoList = list // table表格数据\n            if (list.length < 10) this.listShow = false\n            else this.listShow = true\n            this.todoList.map((item) => {\n              if (!commonBlank(item.image)) {\n                // const userImage = JSON.stringify(JSON.parse(item.image))\n                item.image = // 拿到地址上的头像图片\n                  process.env.VUE_APP_BASE_API +\n                  prefix +\n                  '/download/file.do?fileName=' +\n                  item.image.substring(\n                    item.image.lastIndexOf('/') + 1,\n                    item.image.indexOf('##')\n                  ) +\n                  '&saveFileName=' +\n                  item.image.split('##')[0]\n              }\n              // return item\n            })\n            this.getHeight()\n          })\n        }\n      })\n    },\n    /**\n     * 查询-待办列表 */\n    getList() {\n      const msg = {\n        parameterList: [],\n        user_no: this.$store.getters.userNo,\n        organ_no: this.$store.getters.organNo,\n        organ_level: this.$store.getters.organLevel,\n        role_no: this.$store.getters.roleNo\n      }\n      todoQuery(msg).then((response) => {\n        const { returnList, msgMap } = response.retMap\n        this.list = returnList\n        for (const item in msgMap) {\n          this.list.map((i) => {\n            if (i.sysmessage_id === item) {\n              i.count = msgMap[item]\n            }\n          })\n        }\n        const firstBox = this.list[0]\n        this.menuName = firstBox.message_type\n        this.handleClick(firstBox)\n      })\n    },\n    /**\n     * 打印 */\n    handlePrint() {\n      this.$print(this.$refs.printRef)\n    },\n    /**\n     * 调用系统消息弹窗\n     * @param {Object} row 当前点击的待办事项行*/\n    openSystemMsg(row) {\n      this.$bus.$emit('openSystemMsg', row)\n    },\n    /**\n     * 弹出框 - 确认*/\n    dialogSumbit() {\n      this.changeVisible(false)\n    },\n    /**\n     * 弹出框 - 关闭\n     * @param {Boolean} param 弹出框显示隐藏配置*/\n    changeVisible(param) {\n      this.dialog.visible = param\n    }\n  }\n}\n</script>\n\n<style lang=\"scss\" scoped>\n@import '~@/assets/scss/common/variable/variable/size.scss';\n.homeContent {\n  overflow: hidden;\n  .InfoMore {\n    float: right;\n    color: gray;\n    cursor: pointer;\n    font-size: 1.2rem;\n  }\n\n  .todoBox {\n    font-size: $contFont;\n    margin-bottom: 1.4rem;\n    .boxItem {\n      width: 15rem;\n      height: 6.6rem;\n      box-shadow: 0px 6px 14px rgba(62, 161, 185, 0.16);\n      border-radius: 8px;\n      display: inline-block;\n      margin-left: 1rem;\n      margin-bottom: 1rem;\n      position: relative;\n      overflow: hidden;\n      z-index: 1;\n      &:hover {\n        cursor: pointer;\n      }\n      .firstLine {\n        padding-left: 2rem;\n        padding-top: 1rem;\n        .svgSpan {\n          display: inline-block;\n          width: 2.2rem;\n          height: 2.2rem;\n          text-align: center;\n          line-height: 2.2rem;\n          background: #fff;\n          border-radius: 0.8rem;\n          margin-right: 1rem;\n          position: relative;\n          top: -0.3rem;\n          .svgIcon {\n            width: 4em;\n            height: 4em;\n            position: absolute;\n            top: -1.1rem;\n            left: -1.7rem;\n          }\n        }\n        .numberSpan {\n          letter-spacing: 0.1rem;\n          font-size: 2.6rem;\n          font-weight: bolder;\n          color: #fff;\n        }\n      }\n      .secondLine {\n        text-align: center;\n        color: #fff;\n      }\n      .thirdLine {\n        position: absolute;\n        top: -0.3rem;\n        right: -2.1rem;\n        z-index: -1;\n        .sun-svg-icon {\n          width: 7em;\n          height: 7em;\n        }\n        .transactionApprovalBigIcon {\n          color: rgba(119, 204, 254, 0.8);\n        }\n        .importantInformationBigIcon {\n          color: rgba(254, 167, 108, 0.8);\n        }\n        .otherDefualtBigIcon {\n          color: rgba(126, 115, 246, 0.8);\n        }\n        .checkAdministrationBigIcon {\n          color: rgba(91, 146, 248, 0.8);\n        }\n        .errorManagementBigIcon {\n          color: rgba(140, 220, 174, 0.8);\n        }\n      }\n    }\n    .transactionApprovalColor {\n      background: linear-gradient(312deg, #4ebcfd 0%, #7ee3fc 100%) !important;\n    }\n    .importantInformationColor {\n      background: linear-gradient(315deg, #ff824e 0%, #fac74b 100%) !important;\n    }\n    .otherDefualtColor {\n      background: linear-gradient(315deg, #502cff 0%, #b9a3ff 100%) !important;\n    }\n    .checkAdministrationColor {\n      background: linear-gradient(315deg, #486ff5 0%, #64b5f7 100%) !important;\n    }\n    .errorManagementColorColor {\n      background: linear-gradient(315deg, #6dcaa3 0%, #7add7a 100%) !important;\n    }\n  }\n  .todoListBox {\n    padding-left: 0.6rem;\n    overflow: auto;\n    height: calc(100% - 13.5rem);\n    .item {\n      cursor: pointer;\n      display: flex;\n      margin-bottom: 1.6rem;\n      &:hover {\n        .el-icon-s-promotion {\n          color: var(--color);\n        }\n        .msgTitle {\n          color: var(--color) !important;\n        }\n      }\n      .userIcon {\n        width: 3rem;\n        height: 3rem;\n        margin-right: 1.3rem;\n        border-radius: 50%;\n      }\n      .msgContent {\n        width: 100%;\n        margin-right: 1.6rem;\n        .msgTitle {\n          // margin: 10px auto;\n          margin-bottom: 8px;\n          // width: 350px;\n          white-space: nowrap;\n          overflow: hidden;\n          text-overflow: ellipsis;\n          font-size: 14px;\n          // color: var(--color);\n          .el-icon-remove-outline {\n            float: right;\n            font-size: 1.6rem;\n            font-weight: 800;\n            margin-right: 5px;\n            float: right;\n            &:hover {\n              color: var(--color);\n            }\n          }\n        }\n        .msgDetail {\n          font-size: 12px;\n          // width: 350px;\n          color: #a9a0a0;\n          // display: flex;\n          justify-content: space-between;\n          span:nth-of-type(1) {\n            display: inline-block;\n            // width: 60px;\n          }\n          span:nth-of-type(2) {\n            margin-left: 1.2rem;\n          }\n        }\n      }\n    }\n  }\n  .toMove {\n    text-align: center;\n    cursor: pointer;\n  }\n}\n</style>\n"]}]}