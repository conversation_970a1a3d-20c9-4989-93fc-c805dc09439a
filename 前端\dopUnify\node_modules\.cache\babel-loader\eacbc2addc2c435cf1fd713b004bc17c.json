{"remainingRequest": "E:\\华南区项目\\中国银行\\开发区\\科技公司运营平台\\前端\\dopUnify\\node_modules\\babel-loader\\lib\\index.js!E:\\华南区项目\\中国银行\\开发区\\科技公司运营平台\\前端\\dopUnify\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!E:\\华南区项目\\中国银行\\开发区\\科技公司运营平台\\前端\\dopUnify\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\华南区项目\\中国银行\\开发区\\科技公司运营平台\\前端\\dopUnify\\src\\views\\system\\role\\component\\table\\index.vue?vue&type=script&lang=js&", "dependencies": [{"path": "E:\\华南区项目\\中国银行\\开发区\\科技公司运营平台\\前端\\dopUnify\\src\\views\\system\\role\\component\\table\\index.vue", "mtime": 1721631954662}, {"path": "E:\\华南区项目\\中国银行\\开发区\\科技公司运营平台\\前端\\dopUnify\\babel.config.js", "mtime": 1721631954662}, {"path": "E:\\华南区项目\\中国银行\\开发区\\科技公司运营平台\\前端\\dopUnify\\.babelrc", "mtime": 1721631954662}, {"path": "E:\\华南区项目\\中国银行\\开发区\\科技公司运营平台\\前端\\dopUnify\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "E:\\华南区项目\\中国银行\\开发区\\科技公司运营平台\\前端\\dopUnify\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "E:\\华南区项目\\中国银行\\开发区\\科技公司运营平台\\前端\\dopUnify\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "E:\\华南区项目\\中国银行\\开发区\\科技公司运营平台\\前端\\dopUnify\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1655715099000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAoEA,SACAA,kBACAC,eACAC,wBACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;EAAAC;EAAAC;EAAAC;EAAAC;AACA;EACAC;EACAC;EACAC;IACAC;MACAC;MACAC;QACA;MACA;IACA;IACAC;MACAF;MACAC;QACA;MACA;IACA;EACA;EACAE;IACA;MACAC;QACA;QACAC;QAAA;QACAC;QACAC;QAAA;QACAC;QACAC;UACAN;UAAA;UACAO;UACA;QACA;;QACAC;UACAC;UACAC;UAAA;UACAC;UAAA;UACAC;UACA;QACA;;QACAC;MACA;;MACAC;QACAC;UAAAC;QAAA;QACAC;UAAAD;QAAA;QACAE;UAAAF;QAAA;MACA;MACAG;MACAC;MAAA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACAC;QACAC;QACAC;QACAjB;UACA;UACAkB;UACAC;QACA;;QACAC;UACAC;UAAA;UACAC;UACAhC;YACAiC;YACAC;YACAC;YACAC;YACAC;YACAC;YACAC;UACA;QACA;MACA;MACAC;IACA;EACA;EACAC;IACAtC;MACA;IACA,EACA;IACA;IACA;IACA;EACA;EACAuC;EACAC;EACA;EACA;EACA;EACA;EACA;EACA;EACAC;IACA;AACA;IACAC;MACA;MACA;IACA;IACA;IACAC;MACA;MACA;IACA;IACA;AACA;IACAC;MAAA;QAAAC;MACAC;IACA;IACA;AACA;IACAC;MACA;IACA;IACA;AACA;IACAC;MAAA;MACA;MACA;MACA;QACAC;QACAC;MAAA,GACA;QACAvC;QACAC;MAAA,EACA;MAEAtB,WACA6D;QACA;UAAAC;UAAA1C;UAAA2C;QACA;QACA;QACA;QACA;MACA,GACAC;QACA;MACA;IACA;IACA;AACA;IACAC;MACA;MACA;MACA;MACA;IACA;IAAA;IACA;AACA;IACAC;MAAA;MACA;QACApE;QACA;MACA;MACA;MACA;MACA;MACA;MACA;QACA;QACA;QACA;UACAqE;QACA;QACA;QACA,wEACA,GACA,EACA;QACA;MACA;IACA;IACA;AACA;IACAC;MAAA;MACA;MACA;QACAtE;QACA;MACA;MACA;QAAA6C;QAAAC;QAAAC;MACA9C;QACA;UACA;YACA4D;YACAU,kBACA;cACA3B;cACAC;cACAC;cACAC;YACA;UAEA;UACA1C,SACA0D;YACA;YACA;YACAhE;YACA;YACA;UACA,GACAmE;QACA;MACA;IACA;IACA;AACA;AACA;IACAM;MACA;IACA;IAAA;IACA;AACA;IACAC;MACA;MACA;QACA;MACA;QACA;MACA;MACA;IACA;IACA;AACA;IACAC;MAAA;MACA;MACAC;MACAA;MACAA;MACA;MACA;QACAC,kCAEAD,WAEA;QACAd;MACA;MACA1D,SACA4D;QACA;QACA;QACAhE;QACA;QACA;MACA,GACAmE;QACA;MACA;IACA;IACA;AACA;IACAW;MAAA;MACA5E;QACA;UACA;UACA;UACA0E,uBACAA,oCACAA,oDACAA;UACAA;YACA/B;YACAkC;YAAA;YACAC;YACAC;YACAC;YACAC;YACAC;YACAC;UACA;UACA;UACA;YACA;YACAR,kCAEAD,WAEA;YACAd;UACA;UACAzD,YACA2D;YACA;YACA;YACA;YACA;YACAhE;YACA;YACA;UACA,GACAmE;YACA;UACA;UACA;QACA;MACA;IACA;IACA;AACA;AACA;IACAmB;MACA;IACA;IACA;AACA;AACA;IACAC;MACAC;IACA;EACA;AACA", "names": ["commonMsgSuccess", "commonMsgWarn", "commonMsgConfirm", "query", "add", "update", "del", "name", "filters", "props", "defaultForm", "type", "default", "haveHeight", "data", "table", "columns", "ref", "indexNumber", "loading", "componentProps", "height", "pageList", "totalNum", "currentPage", "pageSize", "small", "currentRow", "btnArr", "btnAdd", "show", "btnModify", "btnDelete", "downloadLoading", "currentIndex", "dialog", "oprate", "visible", "title", "width", "form", "labelWidth", "config", "organ_level", "role_level", "role_no", "role_name", "role_mode", "is_open", "role_des", "screenHeight", "watch", "beforeCreate", "created", "methods", "resize<PERSON><PERSON>ler", "handleCurrentChange", "rowClassName", "rowIndex", "row", "getList", "queryList", "oper_type", "cur_role<PERSON>o", "then", "roles", "pageNum", "catch", "handleAdd", "handleModify", "dataF", "handleDelete", "operation_value", "changeVisible", "dialogSumbit", "dialogAddSubmit", "formData1", "parameterList", "dialogEditSubmit", "last_modi_date", "role_no_old", "is_open_old", "role_name_old", "role_des_old", "role_mode_old", "organ_level_old", "showLoading", "refreshRoleData", "sessionStorage"], "sourceRoot": "src/views/system/role/component/table", "sources": ["index.vue"], "sourcesContent": ["<!--\n* 角色管理: 左侧树\n-->\n<template>\n  <div class=\"sun-content\">\n    <!-- <div class=\"list-btn\">\n      <sun-button\n        :btn-datas=\"btnArr\"\n        @handleAdd=\"handleAdd\"\n        @handleModify=\"handleModify\"\n        @handleDelete=\"handleDelete\"\n      />\n    </div> -->\n    <sun-table\n      :table-config=\"table\"\n      @current-change=\"handleCurrentChange\"\n      @pagination=\"getList\"\n    >\n      <template slot=\"tableColumn\">\n        <el-table-column\n          v-for=\"item in table.columns\"\n          :key=\"item.id\"\n          :prop=\"item.name\"\n          :label=\"item.label\"\n          :width=\"item.width\"\n        >\n          <div slot-scope=\"{ row }\">\n            <span v-if=\"item.name === 'last_modi_date'\">{{\n              row[item.name] | dateTimeFormat\n            }}</span>\n            <span v-else-if=\"item.name === 'role_mode'\">{{\n              row[item.name] | commonFormatValue('ROLE_MODE')\n            }}</span>\n            <span v-else-if=\"item.name === 'role_level'\">{{\n              row.role_level.substring(1)\n            }}</span>\n            <span v-else-if=\"item.name === 'role_organ_level'\">{{\n              row.role_level.substring(0, 1) | commonFormatValue('ORGAN_LEVEL')\n            }}</span>\n            <span v-else-if=\"item.name === 'is_open'\">\n              <el-tag\n                :type=\"row[item.name] === '1' ? 'primary' : 'danger'\"\n                disable-transitions\n              >{{ row.is_open | commonFormatValue('IS_OPEN') }}\n              </el-tag>\n            </span>\n            <span v-else>{{ row[item.name] }}</span>\n          </div>\n        </el-table-column>\n      </template>\n      <template slot=\"customButton\">\n        <!--按钮配置-->\n        <sun-button\n          :btn-datas=\"btnArr\"\n          @handleAdd=\"handleAdd\"\n          @handleModify=\"handleModify\"\n          @handleDelete=\"handleDelete\"\n        />\n      </template>\n    </sun-table>\n    <sun-form-dialog\n      :dialog-config=\"dialog\"\n      @dialogClose=\"changeVisible\"\n      @dialogSubmit=\"dialogSumbit\"\n    /><!--新增、修改弹出框-->\n  </div>\n</template>\n<script>\nimport {\n  commonMsgSuccess,\n  commonMsgWarn,\n  commonMsgConfirm\n} from '@/utils/message.js' // 提示信息\nimport { commonBlank } from '@/utils/common' // 提示信息\nimport { dictionaryGet } from '@/utils/dictionary.js' // 字典常量\nimport { dateNowFormat } from '@/utils/date.js' // 日期格式化\n\nimport { config, configTable } from './info' // 表头、表单配置\n\nimport { system } from '@/api'\nconst { query, add, update, del } = system.SysRole\nexport default {\n  name: 'TableList',\n  filters: {},\n  props: {\n    defaultForm: {\n      type: Object,\n      default: function() {\n        return {}\n      }\n    },\n    haveHeight: {\n      type: String,\n      default: function() {\n        return ''\n      }\n    }\n  },\n  data() {\n    return {\n      table: {\n        // 表格配置\n        columns: configTable(), // 表头配置\n        ref: 'tableRef',\n        indexNumber: true, // 序号\n        loading: false,\n        componentProps: {\n          data: [], // 表格数据\n          height: '300px'\n          // formRow: 2 // 表单行数\n        },\n        pageList: {\n          totalNum: 0,\n          currentPage: 1, // 当前页\n          pageSize: this.$store.getters.pageSize, // 当前页显示条数\n          small: true\n          // pagerCount: 7 // 页码个数\n        },\n        currentRow: [] // 选中行\n      },\n      btnArr: {\n        btnAdd: { show: this.$attrs['btn-all'].btnAdd },\n        btnModify: { show: this.$attrs['btn-all'].btnModify },\n        btnDelete: { show: this.$attrs['btn-all'].btnDelete }\n      },\n      downloadLoading: false,\n      currentIndex: 0, // 当前行索引\n      // dialog: {\n      //   title: '新增',\n      //   width: '50rem',\n      //   labelWidth: '12rem',\n      //   visible: false,\n      //   oprate: 'add',\n      //   formData: {\n      //     organ_level: '',\n      //     role_level: '',\n      //     role_no: '',\n      //     role_name: '',\n      //     role_mode: '',\n      //     is_open: '',\n      //     role_des: ''\n      //   },\n      //   config: config(this)\n      // },\n      dialog: {\n        oprate: 'add',\n        visible: false,\n        componentProps: {\n          // 弹出框配置属性\n          title: '新增',\n          width: '50rem' // 当前弹出框宽度\n        },\n        form: {\n          labelWidth: '12rem', // 当前表单标签宽度配置\n          config: config(this),\n          defaultForm: {\n            organ_level: '',\n            role_level: '',\n            role_no: '',\n            role_name: '',\n            role_mode: '',\n            is_open: '',\n            role_des: ''\n          }\n        }\n      },\n      screenHeight: document.body.clientHeight\n    }\n  },\n  watch: {\n    haveHeight(value) {\n      this.table.componentProps.height = value\n    }\n    // screenHeight(val) {\n    //   this.screenHeight = val\n    //   this.table.componentProps.height = this.screenHeight - 470\n    // }\n  },\n  beforeCreate: function() {},\n  created() {},\n  // beforeMount() {\n  //   window.addEventListener('resize', this.resizeHandler)\n  // },\n  // beforeDestroy() {\n  //   window.removeEventListener('resize', this.resizeHandler)\n  // },\n  methods: {\n    /**\n     * 页面尺寸配置*/\n    resizeHandler() {\n      const screenWidth = document.body.clientHeight\n      this.screenHeight = screenWidth\n    },\n    // 表格选择行\n    handleCurrentChange(val) {\n      this.table.currentRow = val\n      this.$emit('handleCurrentChange', val)\n    },\n    /**\n     * 行的 className 的回调方法，也可以使用字符串为所有行设置一个固定的 className*/\n    rowClassName({ row, rowIndex }) {\n      row.index = rowIndex // 将索引放置到row数据中\n    },\n    /**\n     *页码更新 */\n    getList(param) {\n      this.queryList(param.currentPage)\n    },\n    /**\n     * 按钮：查询*/\n    queryList(currentPage) {\n      this.currentPage = {}\n      this.showLoading(true)\n      const msg = {\n        oper_type: dictionaryGet('OPERATE_QUERY'),\n        cur_roleNo: this.$store.getters.roleNo,\n        ...this.defaultForm,\n        currentPage: currentPage,\n        pageSize: this.table.pageList.pageSize\n      }\n\n      query(msg)\n        .then((response) => {\n          const { roles, totalNum, pageNum } = response.retMap\n          this.table.componentProps.data = roles\n          this.table.pageList.totalNum = totalNum\n          this.table.pageList.currentPage = pageNum\n          this.showLoading(false)\n        })\n        .catch(() => {\n          this.showLoading(false)\n        })\n    },\n    /**\n     * btn - 新增*/\n    handleAdd() {\n      this.dialog.oprate = 'add'\n      this.dialog.componentProps.title = '新增'\n      this.dialog.form.config.role_no.componentProps.disabled = false\n      this.changeVisible(true)\n    }, // handleAdd\n    /**\n     * btn - 修改*/\n    handleModify() {\n      if (commonBlank(this.table.currentRow)) {\n        commonMsgWarn('请选择要修改的行', this)\n        return\n      }\n      this.dialog.oprate = 'edit'\n      this.dialog.componentProps.title = '编辑'\n      this.dialog.form.config.role_no.componentProps.disabled = true // 编辑时，不可修改该属性\n      this.changeVisible(true)\n      this.$nextTick(() => {\n        // 弹出框加载完成后赋值\n        const dataF = {}\n        for (const key in this.dialog.form.defaultForm) {\n          dataF[key] = this.table.currentRow[key]\n        }\n        this.dialog.form.defaultForm = dataF\n        this.dialog.form.defaultForm.organ_level = dataF.role_level.substring(\n          0,\n          1\n        )\n        this.dialog.form.defaultForm.role_level = dataF.role_level.substring(1)\n      })\n    },\n    /**\n     * btn - 删除*/\n    handleDelete() {\n      const rows = this.table.currentRow\n      if (commonBlank(rows)) {\n        commonMsgWarn('请选择要删除的行', this)\n        return\n      }\n      const { role_no, role_name, role_mode, is_open } = rows\n      commonMsgConfirm('是否确认删除当前选中行？', this, (param) => {\n        if (param) {\n          const msg = {\n            oper_type: dictionaryGet('OPERATE_DELETE'),\n            operation_value: [\n              {\n                role_no: role_no,\n                role_name: role_name,\n                role_mode: role_mode,\n                is_open: is_open\n              }\n            ]\n          }\n          del(msg)\n            .then((response) => {\n              // this.table.componentProps.data.splice(this.table.currentRow[0].index, rows.length)\n              this.queryList(1)\n              commonMsgSuccess(response.retMsg, this)\n              this.$bus.$emit('updateRole', true)\n              this.refreshRoleData()\n            })\n            .catch(() => {})\n        }\n      })\n    },\n    /**\n     * 弹出框 - 关闭\n     * @param {Boolean} param 弹出框显示隐藏配置*/\n    changeVisible(param) {\n      this.dialog.visible = param\n    }, // handleAdd\n    /**\n     * 弹出框 - 确认*/\n    dialogSumbit() {\n      const param = this.dialog.oprate\n      if (param === 'add') {\n        this.dialogAddSubmit()\n      } else {\n        this.dialogEditSubmit()\n      }\n      this.refreshRoleData()\n    },\n    /**\n     * 弹出框 - 确认 - 新增*/\n    dialogAddSubmit() {\n      const formData1 = Object.assign({}, this.dialog.form.defaultForm)\n      formData1.role_no = formData1.role_no.toString()\n      formData1.last_modi_date = dateNowFormat()\n      formData1.role_level = formData1.organ_level + formData1.role_level\n      this.showLoading(true)\n      const msg = {\n        parameterList: [\n          {\n            ...formData1\n          }\n        ],\n        oper_type: dictionaryGet('OPERATE_ADD')\n      }\n      add(msg)\n        .then((response) => {\n          this.queryList(1) // 重新查询\n          this.showLoading(false)\n          commonMsgSuccess(response.retMsg, this)\n          this.$bus.$emit('updateRole', true)\n          this.changeVisible(false)\n        })\n        .catch(() => {\n          this.showLoading(false)\n        })\n    },\n    /**\n     * 弹出框 - 确认 - 修改*/\n    dialogEditSubmit() {\n      commonMsgConfirm('是否确认提交当前数据？', this, (param) => {\n        if (param) {\n          let formData1 = Object.assign({}, this.dialog.form.defaultForm)\n          // 格式化 角色层级\n          formData1.role_level =\n            formData1.role_level.length === 2\n              ? formData1.organ_level + '' + formData1.role_level\n              : formData1.organ_level + '0' + formData1.role_level\n          formData1 = Object.assign({}, formData1, {\n            role_no: formData1.role_no.toString(),\n            last_modi_date: dateNowFormat(), // 初始化最后修改日期\n            role_no_old: this.table.currentRow.role_no,\n            is_open_old: this.table.currentRow.is_open,\n            role_name_old: this.table.currentRow.role_name,\n            role_des_old: this.table.currentRow.role_des,\n            role_mode_old: this.table.currentRow.role_mode,\n            organ_level_old: this.table.currentRow.role_level.substring(0, 1)\n          })\n          this.showLoading(true)\n          const msg = {\n            // 请求参数\n            parameterList: [\n              {\n                ...formData1\n              }\n            ],\n            oper_type: dictionaryGet('OPERATE_MODIFY')\n          }\n          update(msg)\n            .then((response) => {\n              const index = this.table.currentRow.index\n              // formData1.role_level =\n              //   formData1.organ_level + formData1.role_level // 角色机构 + 角色等级\n              this.table.componentProps.data.splice(index, 1, formData1)\n              commonMsgSuccess(response.retMsg, this)\n              this.showLoading(false)\n              this.$bus.$emit('updateRole', true)\n            })\n            .catch(() => {\n              this.showLoading(false)\n            })\n          this.changeVisible(false) // 弹出框关闭\n        }\n      })\n    },\n    /**\n     * 加载中动画配置\n     * @param {Boolean}param 当前加载显示状态*/\n    showLoading(param) {\n      this.table.loading = param\n    },\n    /**\n     * 刷新角色数据源\n     */\n    refreshRoleData() {\n      sessionStorage.setItem('roleNoTreeRefreshFlag', 'true')\n    }\n  }\n}\n</script>\n<style lang=\"scss\" scoped>\n.sun-content {\n  padding: 0;\n}\n</style>\n"]}]}