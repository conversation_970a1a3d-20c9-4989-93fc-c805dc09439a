{"remainingRequest": "E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1\\数字运营平台-统一门户工程\\dopUnify\\node_modules\\thread-loader\\dist\\cjs.js!E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1\\数字运营平台-统一门户工程\\dopUnify\\node_modules\\babel-loader\\lib\\index.js!E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1\\数字运营平台-统一门户工程\\dopUnify\\src\\utils\\crypto.js", "dependencies": [{"path": "E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1\\数字运营平台-统一门户工程\\dopUnify\\src\\utils\\crypto.js", "mtime": 1719277996458}, {"path": "E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1\\数字运营平台-统一门户工程\\dopUnify\\babel.config.js", "mtime": 1716875191548}, {"path": "E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1\\数字运营平台-统一门户工程\\dopUnify\\.babelrc", "mtime": 1716875191502}, {"path": "E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1\\数字运营平台-统一门户工程\\dopUnify\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1\\数字运营平台-统一门户工程\\dopUnify\\node_modules\\thread-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1\\数字运营平台-统一门户工程\\dopUnify\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, null]}