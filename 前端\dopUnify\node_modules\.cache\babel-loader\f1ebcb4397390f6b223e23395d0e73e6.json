{"remainingRequest": "E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1\\数字运营平台-统一门户工程\\dopUnify\\node_modules\\babel-loader\\lib\\index.js!E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1\\数字运营平台-统一门户工程\\dopUnify\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1\\数字运营平台-统一门户工程\\dopUnify\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1\\数字运营平台-统一门户工程\\dopUnify\\node_modules\\sunui\\src\\components\\SunForm\\selectTree.vue?vue&type=script&lang=js&", "dependencies": [{"path": "E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1\\数字运营平台-统一门户工程\\dopUnify\\node_modules\\sunui\\src\\components\\SunForm\\selectTree.vue", "mtime": 499162500000}, {"path": "E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1\\数字运营平台-统一门户工程\\dopUnify\\babel.config.js", "mtime": 1716875191548}, {"path": "E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1\\数字运营平台-统一门户工程\\dopUnify\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1\\数字运营平台-统一门户工程\\dopUnify\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1\\数字运营平台-统一门户工程\\dopUnify\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1\\数字运营平台-统一门户工程\\dopUnify\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1655715099000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:aW1wb3J0IF9jcmVhdGVGb3JPZkl0ZXJhdG9ySGVscGVyIGZyb20gIkU6LzFfUHJvamVjdC9YWURfUHJvamVjdC9kb3AtNC4wL2RvcC00LjEvXHU2NTcwXHU1QjU3XHU4RkQwXHU4NDI1XHU1RTczXHU1M0YwLVx1N0VERlx1NEUwMFx1OTVFOFx1NjIzN1x1NURFNVx1N0EwQi9kb3BVbmlmeS9ub2RlX21vZHVsZXMvQGJhYmVsL3J1bnRpbWUvaGVscGVycy9lc20vY3JlYXRlRm9yT2ZJdGVyYXRvckhlbHBlci5qcyI7CmltcG9ydCBfdG9Db25zdW1hYmxlQXJyYXkgZnJvbSAiRTovMV9Qcm9qZWN0L1hZRF9Qcm9qZWN0L2RvcC00LjAvZG9wLTQuMS9cdTY1NzBcdTVCNTdcdThGRDBcdTg0MjVcdTVFNzNcdTUzRjAtXHU3RURGXHU0RTAwXHU5NUU4XHU2MjM3XHU1REU1XHU3QTBCL2RvcFVuaWZ5L25vZGVfbW9kdWxlcy9AYmFiZWwvcnVudGltZS9oZWxwZXJzL2VzbS90b0NvbnN1bWFibGVBcnJheS5qcyI7CmltcG9ydCAiY29yZS1qcy9tb2R1bGVzL2VzLm9iamVjdC50by1zdHJpbmcuanMiOwppbXBvcnQgImNvcmUtanMvbW9kdWxlcy9lcy5hcnJheS5tYXAuanMiOwppbXBvcnQgImNvcmUtanMvbW9kdWxlcy9lcy5hcnJheS5jb25jYXQuanMiOwppbXBvcnQgImNvcmUtanMvbW9kdWxlcy9lcy5mdW5jdGlvbi5uYW1lLmpzIjsKaW1wb3J0ICJjb3JlLWpzL21vZHVsZXMvZXMuYXJyYXkuZmlsdGVyLmpzIjsKaW1wb3J0ICJjb3JlLWpzL21vZHVsZXMvZXMuanNvbi5zdHJpbmdpZnkuanMiOwppbXBvcnQgImNvcmUtanMvbW9kdWxlcy9lcy5hcnJheS5qb2luLmpzIjsKLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KCmltcG9ydCB7IGRlYm91bmNlIH0gZnJvbSAnbG9kYXNoJzsKaW1wb3J0IHsgY29tbW9uQmxhbmsgfSBmcm9tICcuLi8uLi91dGlscy9jb21tb24nOwpleHBvcnQgZGVmYXVsdCB7CiAgbmFtZTogJ1NlbGVjdFRyZWUnLAogIHByb3BzOiB7CiAgICBwbGFjZWhvbGRlcjogewogICAgICB0eXBlOiBTdHJpbmcsCiAgICAgIGRlZmF1bHQ6IGZ1bmN0aW9uIF9kZWZhdWx0KCkgewogICAgICAgIHJldHVybiAn6K+36YCJ5oupJzsKICAgICAgfQogICAgfSwKICAgIHBhcmVudFZhbHVlOiB7CiAgICAgIC8vIOiOt+WPluaJgOacieeItue6p+iKgueCuQogICAgICB0eXBlOiBCb29sZWFuLAogICAgICBkZWZhdWx0OiBmYWxzZQogICAgfSwKICAgIGNoZWNrU3RyaWN0bHk6IHsKICAgICAgLy8g54i257qn5piv5ZCm6IGU5Yqo5LiL57qnCiAgICAgIHR5cGU6IEJvb2xlYW4sCiAgICAgIGRlZmF1bHQ6IGZhbHNlCiAgICB9LAogICAgY2xlYXJhYmxlOiB7CiAgICAgIHR5cGU6IEJvb2xlYW4sCiAgICAgIGRlZmF1bHQ6IHRydWUKICAgIH0sCiAgICBuZXdWYWx1ZTogewogICAgICAvLyDkuI5wYXJlbnRWYWx1ZemFjeWvueWHuueOsO+8jHBhcmVudFZhbHVlIOS4unRydWXmmK/mnInmlYjvvIxwYXJlbnRWYWx1ZTp0cnVl5pe277yM5Li65b2T5YmN6YCJ5Lit5YC8CiAgICAgIHR5cGU6IE9iamVjdCwKICAgICAgZGVmYXVsdDogZnVuY3Rpb24gX2RlZmF1bHQoKSB7CiAgICAgICAgcmV0dXJuIHt9OwogICAgICB9CiAgICB9LAogICAgLy8g6IqC54K55pWw5o2uCiAgICBvcHRpb25zOiB7CiAgICAgIHR5cGU6IEFycmF5LAogICAgICAvLyDlv4XpobvmmK/moJHlvaLnu5PmnoTnmoTlr7nosaHmlbDnu4QKICAgICAgZGVmYXVsdDogZnVuY3Rpb24gX2RlZmF1bHQoKSB7CiAgICAgICAgcmV0dXJuIFtdOwogICAgICB9CiAgICB9LAogICAgLy8g6K6+572ubGFibGUgdmFsdWXlsZ7mgKcKICAgIGRlZmF1bHRQcm9wczogewogICAgICB0eXBlOiBPYmplY3QsCiAgICAgIGRlZmF1bHQ6IGZ1bmN0aW9uIF9kZWZhdWx0KCkgewogICAgICAgIHJldHVybiB7CiAgICAgICAgICB2YWx1ZTogJ2lkJywKICAgICAgICAgIC8vIElE5a2X5q615ZCNCiAgICAgICAgICBsYWJlbDogJ2xhYmVsJywKICAgICAgICAgIC8vIOaYvuekuuWQjeensAogICAgICAgICAgY2hpbGRyZW46ICdjaGlsZHJlbicgLy8g5a2Q57qn5a2X5q615ZCNCiAgICAgICAgfTsKICAgICAgfQogICAgfSwKCiAgICAvLyDpu5jorqTli77pgInnmoToioLngrkKICAgIGRlZmF1bHRDaGVja05vZGVzOiB7CiAgICAgIHR5cGU6IEFycmF5LAogICAgICAvLyDlt7Lnu4/liIbphY3nmoTotYTmupAKICAgICAgZGVmYXVsdDogZnVuY3Rpb24gX2RlZmF1bHQoKSB7CiAgICAgICAgcmV0dXJuIFtdOwogICAgICB9CiAgICB9LAogICAgdmFsdWVNOiB7CiAgICAgIHR5cGU6IG51bGwsCiAgICAgIC8vIOW3sue7j+WIhumFjeeahOi1hOa6kAogICAgICBkZWZhdWx0OiBudWxsCiAgICB9LAogICAgZGVmYXVsdEZvcm06IHsKICAgICAgdHlwZTogT2JqZWN0LAogICAgICBkZWZhdWx0OiBmdW5jdGlvbiBfZGVmYXVsdCgpIHsKICAgICAgICByZXR1cm4ge307CiAgICAgIH0KICAgIH0sCiAgICB2YWx1ZU9wdGlvbjogewogICAgICAvLyDlvZPliY3nu4Tku7bkv6Hmga8KICAgICAgdHlwZTogT2JqZWN0LAogICAgICBkZWZhdWx0OiBmdW5jdGlvbiBfZGVmYXVsdCgpIHsKICAgICAgICByZXR1cm4ge307CiAgICAgIH0KICAgIH0KICB9LAogIGRhdGE6IGZ1bmN0aW9uIGRhdGEoKSB7CiAgICByZXR1cm4gewogICAgICB2YWx1ZTogW10sCiAgICAgIHBhcmVudExpc3Q6IFtdLAogICAgICAvLyDniLbnuqfoioLngrkKICAgICAgY2hhbmdlTjogZmFsc2UsCiAgICAgIC8vIOeUqOS6juWIpOaWreW9k+WJjeWAvOeahOaJp+ihjOasoeaVsAogICAgICBzZWFyY2hGaXJzdElkOiAnJyAvLyDnlKjkuo7orr7nva7pu5jorqTpgInkuK3nrZvpgInmlbDmja7nrKzkuIDmnaHnmoTmlbDmja4KICAgIH07CiAgfSwKCiAgd2F0Y2g6IHsKICAgIHZhbHVlTTogZnVuY3Rpb24gdmFsdWVNKHZhbHVlKSB7CiAgICAgIGlmICh2YWx1ZSA9PT0gbnVsbCB8fCB2YWx1ZSA9PT0gJycgfHwgdmFsdWUubGVuZ3RoID09PSAwKSB7CiAgICAgICAgdGhpcy52YWx1ZSA9ICcnOwogICAgICAgIGlmIChjb21tb25CbGFuayh0aGlzLnZhbHVlKSkgewogICAgICAgICAgdGhpcy5yZXNldENoZWNrZWQoKTsKICAgICAgICB9CiAgICAgICAgLy8gaWYgKHRoaXMuY2hhbmdlTikgewogICAgICAgIC8vICAgcmV0dXJuCiAgICAgICAgLy8gfQogICAgICAgIC8vIOa4heepugogICAgICB9IGVsc2UgewogICAgICAgIC8vIOWIneasoeaJp+ihjAogICAgICAgIC8vIGlmICh0aGlzLmNoYW5nZU4pIHsKICAgICAgICAvLyAgIHJldHVybgogICAgICAgIC8vIH0KICAgICAgICB0aGlzLmluaXRWYWx1ZSh0aGlzLnZhbHVlTSk7CiAgICAgIH0KICAgIH0sCiAgICBuZXdWYWx1ZTogewogICAgICBoYW5kbGVyOiBmdW5jdGlvbiBoYW5kbGVyKHZhbCkgewogICAgICAgIHZhciBfdGhpcyA9IHRoaXM7CiAgICAgICAgaWYgKHZhbC5jb21wb25lbnRQcm9wcy5wYXJlbnRWYWx1ZSkgewogICAgICAgICAgdmFyIHZhbHVlTiA9IHRoaXMubmV3VmFsdWUuY29tcG9uZW50UHJvcHMudmFsdWU7CiAgICAgICAgICBpZiAodmFsdWVOICE9PSBudWxsICYmIHZhbHVlTiAhPT0gJycpIHsKICAgICAgICAgICAgdGhpcy4kbmV4dFRpY2soZnVuY3Rpb24gKCkgewogICAgICAgICAgICAgIGlmIChfdGhpcy5jaGFuZ2VOKSB7CiAgICAgICAgICAgICAgICBfdGhpcy5jaGFuZ2VOID0gZmFsc2U7CiAgICAgICAgICAgICAgICByZXR1cm47CiAgICAgICAgICAgICAgfQogICAgICAgICAgICAgIHZhciBub2RlTiA9IF90aGlzLiRyZWZzLnRyZWVPcHRpb24uZ2V0Tm9kZSh2YWx1ZU4pOwogICAgICAgICAgICAgIF90aGlzLm5vZGVDbGljayhudWxsLCBub2RlTik7CiAgICAgICAgICAgICAgX3RoaXMuY2hlY2tOb2RlKG5vZGVOLCAnbmV3Jyk7CiAgICAgICAgICAgICAgX3RoaXMuJHJlZnMudHJlZU9wdGlvbi5zZXRDdXJyZW50S2V5KF90aGlzLm5ld1ZhbHVlLmNvbXBvbmVudFByb3BzLnZhbHVlKTsKICAgICAgICAgICAgfSk7CiAgICAgICAgICB9CiAgICAgICAgfQogICAgICB9LAogICAgICBkZWVwOiB0cnVlCiAgICB9CiAgfSwKICBtb3VudGVkOiBmdW5jdGlvbiBtb3VudGVkKCkgewogICAgdmFyIF90aGlzMiA9IHRoaXM7CiAgICB0aGlzLiRuZXh0VGljaygpLnRoZW4oZnVuY3Rpb24gKCkgewogICAgICBfdGhpczIuaW5pdCgpOwogICAgfSk7CiAgfSwKICBtZXRob2RzOiB7CiAgICAvKioKICAgICAqIOWIneWni+i1i+WAvAogICAgICovCiAgICBpbml0OiBmdW5jdGlvbiBpbml0KCkgewogICAgICB2YXIgbm93VmFsdWUgPSB0aGlzLiRhdHRycy52YWx1ZTsgLy8g6I635Y+W5b2T5YmN5YC8CiAgICAgIGlmIChub3dWYWx1ZSkgewogICAgICAgIHRoaXMuaW5pdFZhbHVlKG5vd1ZhbHVlKTsKICAgICAgfQogICAgfSwKICAgIC8qKgogICAgICog5Yid5aeL6LWL5YC8CiAgICAgKiBAcGFyYW0ge1N0cmluZ31wYXJhbSDlvZPliY3lgLwKICAgICAqLwogICAgaW5pdFZhbHVlOiBmdW5jdGlvbiBpbml0VmFsdWUocGFyYW0pIHsKICAgICAgdmFyIF90aGlzMyA9IHRoaXM7CiAgICAgIHZhciBub3dWYWx1ZSA9IHBhcmFtOwogICAgICAvLyBlc2xpbnQtZGlzYWJsZS1uZXh0LWxpbmUgdmFsaWQtdHlwZW9mCiAgICAgIC8vIOi/lOWbnuWAvOaVsOe7hOaDheWGteS4iwogICAgICBpZiAoT2JqZWN0LnByb3RvdHlwZS50b1N0cmluZy5jYWxsKG5vd1ZhbHVlKSA9PT0gJ1tvYmplY3QgQXJyYXldJykgewogICAgICAgIHRoaXMuJHJlZnMudHJlZU9wdGlvbi5zZXRDaGVja2VkS2V5cyhub3dWYWx1ZSk7IC8vIOiuvue9rum7mOiupOWAvCDljZXpgIkKICAgICAgfSBlbHNlIGlmICh0aGlzLiRhdHRycy5tdWx0aXBsZSAmJiBub3dWYWx1ZSAhPT0gbnVsbCkgewogICAgICAgIC8vIOWkjemAiQogICAgICAgIHZhciB2YWx1ZVQgPSBbXTsKICAgICAgICBpZiAobm93VmFsdWUuaW5kZXhPZignLCcpICE9PSAtMSkgewogICAgICAgICAgdmFsdWVUID0gbm93VmFsdWUuc3BsaXQoJywnKTsKICAgICAgICAgIHRoaXMuJHJlZnMudHJlZU9wdGlvbi5zZXRDaGVja2VkS2V5cyhfdG9Db25zdW1hYmxlQXJyYXkodmFsdWVUKSk7IC8vIOiuvue9rum7mOiupOWAvAogICAgICAgIH0gZWxzZSB7CiAgICAgICAgICB0aGlzLiRyZWZzLnRyZWVPcHRpb24uc2V0Q2hlY2tlZEtleXMoW25vd1ZhbHVlXSk7IC8vIOiuvue9rum7mOiupOWAvAogICAgICAgIH0KICAgICAgfSBlbHNlIHsKICAgICAgICB0aGlzLiRyZWZzLnRyZWVPcHRpb24uc2V0Q3VycmVudEtleShub3dWYWx1ZSk7IC8vIOiuvue9rum7mOiupOWAvCDljZXpgIkKICAgICAgfQogICAgICAvLyDotYvlgLwKICAgICAgdGhpcy4kbmV4dFRpY2soZnVuY3Rpb24gKCkgewogICAgICAgIGlmIChfdGhpczMuJGF0dHJzLm11bHRpcGxlICYmIG5vd1ZhbHVlICE9PSBudWxsKSB7CiAgICAgICAgICB2YXIgc2VsZWN0QWxsID0gX3RoaXMzLiRyZWZzLnRyZWVPcHRpb24uZ2V0Q2hlY2tlZE5vZGVzKCk7CiAgICAgICAgICAvLyB0aGlzLnZhbHVlID0gdGhpcy4kcmVmcy50cmVlT3B0aW9uLmdldENoZWNrZWRLZXlzKCkKICAgICAgICAgIHZhciBkYXRhc1ZhbHVlID0gW107CiAgICAgICAgICBzZWxlY3RBbGwubWFwKGZ1bmN0aW9uIChpdGVtKSB7CiAgICAgICAgICAgIGRhdGFzVmFsdWUgPSBbXS5jb25jYXQoX3RvQ29uc3VtYWJsZUFycmF5KGRhdGFzVmFsdWUpLCBbaXRlbS5sYWJlbF0pOwogICAgICAgICAgfSk7CiAgICAgICAgICBfdGhpczMudmFsdWUgPSBkYXRhc1ZhbHVlOwogICAgICAgIH0gZWxzZSB7CiAgICAgICAgICB2YXIgc2VsZWN0T25lID0gX3RoaXMzLiRyZWZzLnRyZWVPcHRpb24uZ2V0Q3VycmVudE5vZGUoKTsKICAgICAgICAgIC8vIHRoaXMudmFsdWUgPSB0aGlzLiRyZWZzLnRyZWVPcHRpb24uZ2V0Q3VycmVudEtleSgpIC8vIOiuvue9rum7mOiupOWAvAogICAgICAgICAgaWYgKG5vd1ZhbHVlID09PSAwKSB7CiAgICAgICAgICAgIF90aGlzMy52YWx1ZSA9IDA7CiAgICAgICAgICAgIHJldHVybjsKICAgICAgICAgIH0KICAgICAgICAgIGlmIChzZWxlY3RPbmUgIT09IG51bGwpIHsKICAgICAgICAgICAgX3RoaXMzLnZhbHVlID0gc2VsZWN0T25lLmxhYmVsOwogICAgICAgICAgfQogICAgICAgIH0KICAgICAgfSk7CiAgICB9LAogICAgLy8g6ZSu55uYZW50ZXLkuovku7YKICAgIGVudGVyRXZlbnQ6IGZ1bmN0aW9uIGVudGVyRXZlbnQoKSB7CiAgICAgIHZhciBub2RlID0gdGhpcy4kcmVmcy50cmVlT3B0aW9uLmdldEN1cnJlbnROb2RlKCk7IC8vIOiOt+WPluW9k+WJjeiKgueCuQogICAgICBpZiAoIW5vZGUpIHsKICAgICAgICByZXR1cm47CiAgICAgIH0KICAgICAgdmFyIHNlbGVjdFRyZWUgPSB0aGlzLiRyZWZzLnNlbGVjdFRyZWU7CiAgICAgIGlmICghdGhpcy4kYXR0cnMubXVsdGlwbGUpIHsKICAgICAgICAvLyDljZXpgIkKICAgICAgICB0aGlzLmRlZmF1bHRGb3JtW3RoaXMudmFsdWVPcHRpb24ubmFtZV0gPSBub2RlLmlkICsgJyc7CiAgICAgICAgc2VsZWN0VHJlZS5jYWNoZWRPcHRpb25zLnB1c2goewogICAgICAgICAgY3VycmVudExhYmVsOiBub2RlLmxhYmVsLAogICAgICAgICAgdmFsdWU6IG5vZGUuaWQKICAgICAgICB9KTsKICAgICAgICBzZWxlY3RUcmVlLmhhbmRsZU9wdGlvblNlbGVjdCh7CiAgICAgICAgICB2YWx1ZTogbm9kZS5pZAogICAgICAgIH0sIHRydWUpOwogICAgICB9IGVsc2UgewogICAgICAgIC8vIOWkmumAiQogICAgICAgIHNlbGVjdFRyZWUuY2FjaGVkT3B0aW9ucyA9IFtdOwogICAgICAgIHRoaXMuJHJlZnMudHJlZU9wdGlvbi5zZXRDaGVja2VkKG5vZGUuaWQsIHRydWUsIHRydWUpOyAvLyDorr7nva7lvZPliY3oioLngrnpgInkuK0KICAgICAgICB2YXIgZ2V0Q2hlY2tlZE5vZGVzID0gdGhpcy4kcmVmcy50cmVlT3B0aW9uLmdldENoZWNrZWROb2RlcygpOyAvLyDojrflj5bmiYDmnInpgInkuK3oioLngrkKICAgICAgICB2YXIgZ2V0Q2hlY2tlZEtleXMgPSB0aGlzLiRyZWZzLnRyZWVPcHRpb24uZ2V0Q2hlY2tlZEtleXMoKTsgLy8g6I635Y+W5omA5pyJ6YCJ5Lit6IqC54K55YC8CiAgICAgICAgdmFyIF9pdGVyYXRvciA9IF9jcmVhdGVGb3JPZkl0ZXJhdG9ySGVscGVyKGdldENoZWNrZWROb2RlcyksCiAgICAgICAgICBfc3RlcDsKICAgICAgICB0cnkgewogICAgICAgICAgZm9yIChfaXRlcmF0b3IucygpOyAhKF9zdGVwID0gX2l0ZXJhdG9yLm4oKSkuZG9uZTspIHsKICAgICAgICAgICAgdmFyIGl0ZW0gPSBfc3RlcC52YWx1ZTsKICAgICAgICAgICAgaXRlbS5jdXJyZW50TGFiZWwgPSBpdGVtLmxhYmVsOwogICAgICAgICAgICBpdGVtLnZhbHVlID0gaXRlbS5pZDsKICAgICAgICAgICAgc2VsZWN0VHJlZS5jYWNoZWRPcHRpb25zLnB1c2goaXRlbSk7CiAgICAgICAgICAgIGlmIChpdGVtID09PSBub2RlLmlkKSB7CiAgICAgICAgICAgICAgc2VsZWN0VHJlZS5oYW5kbGVPcHRpb25TZWxlY3Qobm9kZSwgZmFsc2UpOwogICAgICAgICAgICB9IGVsc2UgewogICAgICAgICAgICAgIHNlbGVjdFRyZWUuaGFuZGxlT3B0aW9uU2VsZWN0KG5vZGUsIHRydWUpOwogICAgICAgICAgICB9CiAgICAgICAgICB9CiAgICAgICAgICAvLyDorr7nva7pgInkuK3oioLngrnlgLwKICAgICAgICB9IGNhdGNoIChlcnIpIHsKICAgICAgICAgIF9pdGVyYXRvci5lKGVycik7CiAgICAgICAgfSBmaW5hbGx5IHsKICAgICAgICAgIF9pdGVyYXRvci5mKCk7CiAgICAgICAgfQogICAgICAgIHRoaXMudmFsdWUgPSBnZXRDaGVja2VkS2V5czsKICAgICAgICB0aGlzLmRlZmF1bHRGb3JtW3RoaXMudmFsdWVPcHRpb24ubmFtZV0gPSBnZXRDaGVja2VkS2V5czsKICAgICAgfQogICAgfSwKICAgIC8vIHNlbGVjdCDmkJzntKIKICAgIHNlbGVjdEZpbHRlcjogZGVib3VuY2UoZnVuY3Rpb24gKHZhbCkgewogICAgICB0aGlzLnNlYXJjaEZpcnN0SWQgPSAnJzsKICAgICAgaWYgKHRoaXMuJHJlZnMudHJlZU9wdGlvbikgewogICAgICAgIHRoaXMuJHJlZnMudHJlZU9wdGlvbi5maWx0ZXIodmFsKTsKICAgICAgICAvLyDpu5jorqTpgInkuK3ov4fmu6Tlh7rlkI7mlbDmja7nmoTnrKzkuIDmnaHmlbDmja4KICAgICAgICB0aGlzLiRyZWZzLnRyZWVPcHRpb24uc2V0Q3VycmVudEtleSh0aGlzLnNlYXJjaEZpcnN0SWQpOwogICAgICB9CiAgICB9LCA2MDApLAogICAgLy8g5qCR6IqC54K56L+H5rukCiAgICBmaWx0ZXJOb2RlOiBmdW5jdGlvbiBmaWx0ZXJOb2RlKHZhbHVlLCBkYXRhKSB7CiAgICAgIGlmICghdmFsdWUpIHsKICAgICAgICB0aGlzLnNlYXJjaEZpcnN0SWQgPSBudWxsOwogICAgICAgIHJldHVybiB0cnVlOwogICAgICB9CiAgICAgIGlmIChkYXRhLmxhYmVsLmluZGV4T2YodmFsdWUpICE9PSAtMSkgewogICAgICAgIGlmICghdGhpcy5zZWFyY2hGaXJzdElkKSB7CiAgICAgICAgICB0aGlzLnNlYXJjaEZpcnN0SWQgPSBkYXRhLmlkOwogICAgICAgIH0KICAgICAgICByZXR1cm4gdHJ1ZTsKICAgICAgfSBlbHNlIHsKICAgICAgICByZXR1cm4gZmFsc2U7CiAgICAgIH0KICAgIH0sCiAgICAvKioKICAgICAqIOiKgueCueiiq+eCueWHu+aXtueahOWbnuiwgwogICAgICogQHBhcmFtIHtPYmplY3R9IGUg5Lyg6YCS57uZIGRhdGEg5bGe5oCn55qE5pWw57uE5Lit6K+l6IqC54K55omA5a+55bqU55qE5a+56LGhCiAgICAgKiBAcGFyYW0ge09iamVjdH0gTm9kZSDoioLngrnlr7nlupTnmoQgTm9kZQogICAgICogQHBhcmFtIHtPYmplY3R9IFZ1ZUNvbXBvbmVudCDoioLngrnnu4Tku7bmnKzouqsKICAgICAqLwogICAgbm9kZUNsaWNrOiBmdW5jdGlvbiBub2RlQ2xpY2soZSwgbm9kZSwgZGF0YSkgewogICAgICBpZiAoIWNvbW1vbkJsYW5rKGUuY2hpbGRyZW4pICYmIHRoaXMudmFsdWVPcHRpb24uZGlzYWJsZU5vZGUpIHsKICAgICAgICByZXR1cm47CiAgICAgIH0KICAgICAgLy8g56aB55So5pyJ5a2Q6IqC54K555qE54i26IqC54K5CiAgICAgIC8vIHRoaXMuJGVtaXQoJ2dldE5vZGUnLCBlKQogICAgICBpZiAoIXRoaXMuJGF0dHJzLm11bHRpcGxlKSB7CiAgICAgICAgLy8g5b2T6YCJ5Lit55qE5YC85ZKM5LiK5LiA5qyh6YCJ5Lit5YC85LiN5ZCM5pe25omN6LWw5LiL6Z2i55qE5Luj56CBCiAgICAgICAgaWYgKHRoaXMuZGVmYXVsdEZvcm1bdGhpcy52YWx1ZU9wdGlvbi5uYW1lXSAhPT0gbm9kZS5kYXRhLmlkICsgJycpIHsKICAgICAgICAgIHRoaXMuZGVmYXVsdEZvcm1bdGhpcy52YWx1ZU9wdGlvbi5uYW1lXSA9IG5vZGUuZGF0YS5pZCArICcnOwogICAgICAgICAgdmFyIHNlbGVjdFRyZWUgPSB0aGlzLiRyZWZzLnNlbGVjdFRyZWU7CiAgICAgICAgICBzZWxlY3RUcmVlLmNhY2hlZE9wdGlvbnMgPSBbXTsKICAgICAgICAgIHNlbGVjdFRyZWUuY2FjaGVkT3B0aW9ucy5wdXNoKG5vZGUuZGF0YSk7CiAgICAgICAgICBzZWxlY3RUcmVlLmhhbmRsZU9wdGlvblNlbGVjdChub2RlLmRhdGEsIHRydWUpOwogICAgICAgICAgLy8g5Lul5LiK5Luj56CB5bqU6K+l5piv5YWz6Zet5LiL5ouJ5qCR5qGG77yM5rKh5pyJ5YW25LuW55So5aSECiAgICAgICAgfQogICAgICB9CiAgICAgIC8vIOe7mXNlbGVjdFRyZWXnmoRjYWNoZWRPcHRpb25z6LWL5YC8IOiuvue9rm5vZGUubGFiZWzvvIzkvb/nlKjpobXpnaLmmL7npLpsYWJlbOWAvAogICAgICAvLyB0aGlzLnBhcmVudExpc3QgPSBbXQogICAgICAvLyB0aGlzLmdldFRyZWVOb2RlKG5vZGUpCiAgICAgIC8vIGlmICh0aGlzLnBhcmVudFZhbHVlID09PSB0cnVlKSB7CiAgICAgIC8vICAgdGhpcy4kZW1pdCgnc2VsZWN0UGFyZW50JywgeyBpdGVtOiB0aGlzLm5ld1ZhbHVlLCB2YWx1ZTogbm9kZS5kYXRhLmlkIH0pCiAgICAgIC8vICAgdGhpcy52YWx1ZSA9IFsuLi50aGlzLnBhcmVudExpc3QsIG5vZGUuZGF0YS5sYWJlbF0uam9pbignLycpCiAgICAgIC8vIH0KICAgIH0sCiAgICAvLyDpgInkuK3oioLngrnnmoTmiYDmnInivZfnuqdpZOWSjGNvZGVJdGVt5L+h5oGvCiAgICBnZXRUcmVlTm9kZTogZnVuY3Rpb24gZ2V0VHJlZU5vZGUobm9kZSkgewogICAgICBpZiAobm9kZSkgewogICAgICAgIC8vIHRoaXMucGFyZW50TGlzdOaJgOacieK9l+e6p+iKgueCueeahGNvZGVJdGVt5L+h5oGvCiAgICAgICAgaWYgKG5vZGUucGFyZW50KSB7CiAgICAgICAgICBpZiAobm9kZS5wYXJlbnQubGFiZWwpIHsKICAgICAgICAgICAgdGhpcy5wYXJlbnRMaXN0ID0gW25vZGUucGFyZW50LmxhYmVsXS5jb25jYXQoX3RvQ29uc3VtYWJsZUFycmF5KHRoaXMucGFyZW50TGlzdCkpOwogICAgICAgICAgICB2YXIgbm9kZXMgPSB0aGlzLiRyZWZzLnRyZWVPcHRpb24uZ2V0Tm9kZShub2RlLnBhcmVudCk7CiAgICAgICAgICAgIHRoaXMuZ2V0VHJlZU5vZGUobm9kZXMpOwogICAgICAgICAgfQogICAgICAgIH0KICAgICAgfQogICAgfSwKICAgIC8vIOWIoOmZpHRhZ+aXtu+8jAogICAgcmVtb3ZlVGFnOiBmdW5jdGlvbiByZW1vdmVUYWcodmFsKSB7CiAgICAgIC8vIOWPlua2iOWLvumAiQogICAgICBpZiAodmFsLmluZGV4T2YoJy0nKSAhPT0gLTEpIHsKICAgICAgICB2YXIgdHJlZU9wdGlvbiA9IHRoaXMuJHJlZnMudHJlZU9wdGlvbjsKICAgICAgICB0aGlzLiRuZXh0VGljayhmdW5jdGlvbiAoKSB7CiAgICAgICAgICB0cmVlT3B0aW9uLnNldENoZWNrZWQodmFsLnNwbGl0KCctJylbMF0sIGZhbHNlLCBmYWxzZSk7CiAgICAgICAgfSk7CiAgICAgICAgdmFyIHZhbHVlQXJyID0gW107CiAgICAgICAgdmFyIF9pdGVyYXRvcjIgPSBfY3JlYXRlRm9yT2ZJdGVyYXRvckhlbHBlcih0aGlzLnZhbHVlKSwKICAgICAgICAgIF9zdGVwMjsKICAgICAgICB0cnkgewogICAgICAgICAgZm9yIChfaXRlcmF0b3IyLnMoKTsgIShfc3RlcDIgPSBfaXRlcmF0b3IyLm4oKSkuZG9uZTspIHsKICAgICAgICAgICAgdmFyIGl0ZW0gPSBfc3RlcDIudmFsdWU7CiAgICAgICAgICAgIHZhbHVlQXJyID0gW10uY29uY2F0KF90b0NvbnN1bWFibGVBcnJheSh2YWx1ZUFyciksIFtpdGVtLnNwbGl0KCctJylbMF1dKTsKICAgICAgICAgIH0KICAgICAgICB9IGNhdGNoIChlcnIpIHsKICAgICAgICAgIF9pdGVyYXRvcjIuZShlcnIpOwogICAgICAgIH0gZmluYWxseSB7CiAgICAgICAgICBfaXRlcmF0b3IyLmYoKTsKICAgICAgICB9CiAgICAgICAgdGhpcy5kZWZhdWx0Rm9ybVt0aGlzLnZhbHVlT3B0aW9uLm5hbWVdID0gdmFsdWVBcnI7IC8vIOiuvue9rum7mOiupOWAvAogICAgICB9IGVsc2UgewogICAgICAgIHZhciBhcnIxID0gSlNPTi5wYXJzZShKU09OLnN0cmluZ2lmeSh0aGlzLmRlZmF1bHRGb3JtW3RoaXMudmFsdWVPcHRpb24ubmFtZV0pKTsKICAgICAgICB2YXIgYXJyMiA9IHRoaXMudmFsdWUubWFwKGZ1bmN0aW9uIChpdGVtKSB7CiAgICAgICAgICByZXR1cm4gaXRlbS5zcGxpdCgnLScpWzBdOwogICAgICAgIH0pOwogICAgICAgIHZhciBmaWx0ZXJFbGUgPSBhcnIxLmNvbmNhdChhcnIyKS5maWx0ZXIoZnVuY3Rpb24gKHYsIGksIGFycikgewogICAgICAgICAgcmV0dXJuIGFyci5pbmRleE9mKHYpID09PSBhcnIubGFzdEluZGV4T2Yodik7CiAgICAgICAgfSk7CiAgICAgICAgdmFyIF90cmVlT3B0aW9uID0gdGhpcy4kcmVmcy50cmVlT3B0aW9uOwogICAgICAgIHRoaXMuJG5leHRUaWNrKGZ1bmN0aW9uICgpIHsKICAgICAgICAgIF90cmVlT3B0aW9uLnNldENoZWNrZWQoZmlsdGVyRWxlWzBdLCBmYWxzZSwgZmFsc2UpOwogICAgICAgIH0pOwogICAgICAgIHRoaXMuZGVmYXVsdEZvcm1bdGhpcy52YWx1ZU9wdGlvbi5uYW1lXSA9IHRoaXMuZGVmYXVsdEZvcm1bdGhpcy52YWx1ZU9wdGlvbi5uYW1lXS5maWx0ZXIoZnVuY3Rpb24gKGkpIHsKICAgICAgICAgIHJldHVybiBpICE9PSBmaWx0ZXJFbGVbMF07CiAgICAgICAgfSk7CiAgICAgIH0KICAgICAgLy8gY29uc3QgdHJlZU9wdGlvbiA9IHRoaXMuJHJlZnMudHJlZU9wdGlvbgogICAgICAvLyB0cmVlT3B0aW9uLnNldENoZWNrZWQodmFsLCBmYWxzZSwgZmFsc2UpCiAgICAgIC8vIOmHjeaWsOe7meaOp+S7tui1i+WAvAogICAgICAvLyB0aGlzLiRlbWl0KCdpbnB1dCcsIHRoaXMudmFsdWUpCiAgICB9LAogICAgLyoqCiAgICAgKiDoioLngrnpgInkuK3nirbmgIHlj5HnlJ/lj5jljJbml7bnmoTlm57osIMKICAgICAqLwogICAgY2hlY2tOb2RlOiBmdW5jdGlvbiBjaGVja05vZGUobm9kZSwgZGF0YSwgdHJlZVN0YXR1cywgY2hpbGRyZW4pIHsKICAgICAgaWYgKCFub2RlIHx8ICF0aGlzLiRhdHRycy5tdWx0aXBsZSkgewogICAgICAgIHJldHVybjsKICAgICAgfQogICAgICBub2RlLnZhbHVlID0gbm9kZS5pZDsgLy8g6L+U5pi+5Lit5paHCiAgICAgIG5vZGUuY3VycmVudExhYmVsID0gbm9kZS5sYWJlbDsgLy8g6L+U5pi+5Lit5paHCiAgICAgIC8vIOe7mXNlbGVjdFRyZWXnmoRjYWNoZWRPcHRpb25z6LWL5YC8IOiuvue9rm5vZGUubGFiZWzvvIzkvb/nlKjpobXpnaLmmL7npLpsYWJlbOWAvAogICAgICB2YXIgc2VsZWN0VHJlZSA9IHRoaXMuJHJlZnMuc2VsZWN0VHJlZTsKICAgICAgc2VsZWN0VHJlZS5jYWNoZWRPcHRpb25zID0gW107CiAgICAgIHZhciBfaXRlcmF0b3IzID0gX2NyZWF0ZUZvck9mSXRlcmF0b3JIZWxwZXIoZGF0YS5jaGVja2VkTm9kZXMpLAogICAgICAgIF9zdGVwMzsKICAgICAgdHJ5IHsKICAgICAgICBmb3IgKF9pdGVyYXRvcjMucygpOyAhKF9zdGVwMyA9IF9pdGVyYXRvcjMubigpKS5kb25lOykgewogICAgICAgICAgdmFyIGl0ZW0gPSBfc3RlcDMudmFsdWU7CiAgICAgICAgICBpdGVtLmN1cnJlbnRMYWJlbCA9IGl0ZW0ubGFiZWw7CiAgICAgICAgICBpdGVtLnZhbHVlID0gaXRlbS5pZDsKICAgICAgICAgIHNlbGVjdFRyZWUuY2FjaGVkT3B0aW9ucy5wdXNoKGl0ZW0pOwogICAgICAgICAgaWYgKGl0ZW0gPT09IG5vZGUuaWQpIHsKICAgICAgICAgICAgc2VsZWN0VHJlZS5oYW5kbGVPcHRpb25TZWxlY3Qobm9kZSwgZmFsc2UpOwogICAgICAgICAgfSBlbHNlIHsKICAgICAgICAgICAgc2VsZWN0VHJlZS5oYW5kbGVPcHRpb25TZWxlY3Qobm9kZSwgdHJ1ZSk7CiAgICAgICAgICB9CiAgICAgICAgfQogICAgICB9IGNhdGNoIChlcnIpIHsKICAgICAgICBfaXRlcmF0b3IzLmUoZXJyKTsKICAgICAgfSBmaW5hbGx5IHsKICAgICAgICBfaXRlcmF0b3IzLmYoKTsKICAgICAgfQogICAgICBpZiAodGhpcy5wYXJlbnRWYWx1ZSA9PT0gdHJ1ZSkgewogICAgICAgIGlmICh0cmVlU3RhdHVzID09PSAnbmV3JykgewogICAgICAgICAgdGhpcy5jaGFuZ2VOID0gZmFsc2U7CiAgICAgICAgfSBlbHNlIHsKICAgICAgICAgIHRoaXMuY2hhbmdlTiA9IHRydWU7CiAgICAgICAgfQogICAgICAgIHRoaXMuJGVtaXQoJ3NlbGVjdFBhcmVudCcsIHsKICAgICAgICAgIGl0ZW06IHRoaXMubmV3VmFsdWUsCiAgICAgICAgICB2YWx1ZTogbm9kZS5pZAogICAgICAgIH0pOwogICAgICAgIHRoaXMudmFsdWUgPSBbXS5jb25jYXQoX3RvQ29uc3VtYWJsZUFycmF5KHRoaXMucGFyZW50TGlzdCksIFtub2RlLmxhYmVsXSkuam9pbignLycpOwogICAgICB9IGVsc2UgewogICAgICAgIHRoaXMuY2hhbmdlTiA9IHRydWU7CiAgICAgICAgdGhpcy52YWx1ZSA9IGRhdGEuY2hlY2tlZEtleXM7CiAgICAgICAgdGhpcy5kZWZhdWx0Rm9ybVt0aGlzLnZhbHVlT3B0aW9uLm5hbWVdID0gZGF0YS5jaGVja2VkS2V5czsKICAgICAgfQogICAgfSwKICAgIC8vIOa4heepugogICAgcmVzZXRDaGVja2VkOiBmdW5jdGlvbiByZXNldENoZWNrZWQoKSB7CiAgICAgIHRoaXMuJHJlZnMudHJlZU9wdGlvbi5zZXRDaGVja2VkS2V5cyhbXSk7CiAgICAgIHRoaXMuJHJlZnMudHJlZU9wdGlvbi5zZXRDdXJyZW50S2V5KCcnKTsKICAgICAgaWYgKHRoaXMuJGF0dHJzLm11bHRpcGxlKSB7CiAgICAgICAgdGhpcy5kZWZhdWx0Rm9ybVt0aGlzLnZhbHVlT3B0aW9uLm5hbWVdID0gdGhpcy52YWx1ZTsKICAgICAgfSBlbHNlIHsKICAgICAgICBpZiAodGhpcy4kcmVmcy50cmVlT3B0aW9uLnJvb3Quc3RvcmUuY3VycmVudE5vZGUpIHsKICAgICAgICAgIHRoaXMuJHJlZnMudHJlZU9wdGlvbi5yb290LnN0b3JlLmN1cnJlbnROb2RlLmlzQ3VycmVudCA9IGZhbHNlOyAvLyDmuIXnqbrml7blj5bmtojpq5jkuq7moLflvI8KICAgICAgICB9CgogICAgICAgIHRoaXMuZGVmYXVsdEZvcm1bdGhpcy52YWx1ZU9wdGlvbi5uYW1lXSA9ICcnOwogICAgICB9CiAgICAgIC8vIOmHjeaWsOaQnOe0oiDnlKjnqbrlgLwKICAgICAgdGhpcy5zZWxlY3RGaWx0ZXIoJycpOwogICAgICAvLyDorr7nva7miYDmnInnmoToioLngrnkuLrpl63lkIjnirbmgIEKICAgICAgdGhpcy5jaGFuZ2VUcmVlTm9kZVN0YXR1cyh0aGlzLiRyZWZzLnRyZWVPcHRpb24uc3RvcmUucm9vdCk7CiAgICB9LAogICAgLy8g6Zet5ZCI5pS55Y+Y6IqC54K555qE54q25oCBCiAgICBjaGFuZ2VUcmVlTm9kZVN0YXR1czogZnVuY3Rpb24gY2hhbmdlVHJlZU5vZGVTdGF0dXMobm9kZSkgewogICAgICBub2RlLmV4cGFuZGVkID0gZmFsc2U7CiAgICAgIGZvciAodmFyIGkgPSAwOyBpIDwgbm9kZS5jaGlsZE5vZGVzLmxlbmd0aDsgaSsrKSB7CiAgICAgICAgLy8g5pS55Y+Y6IqC54K555qE6Ieq6LqrZXhwYW5kZWTnirbmgIEKICAgICAgICBub2RlLmNoaWxkTm9kZXNbaV0uZXhwYW5kZWQgPSBmYWxzZTsKICAgICAgICAvLyDpgY3ljoblrZDoioLngrkKICAgICAgICBpZiAobm9kZS5jaGlsZE5vZGVzW2ldLmNoaWxkTm9kZXMubGVuZ3RoID4gMCkgewogICAgICAgICAgdGhpcy5jaGFuZ2VUcmVlTm9kZVN0YXR1cyhub2RlLmNoaWxkTm9kZXNbaV0pOwogICAgICAgIH0KICAgICAgfQogICAgfSwKICAgIGNoYW5nZTogZnVuY3Rpb24gY2hhbmdlKHZhbCkgewogICAgICAvLyB0aGlzLiRlbWl0KCdjaGFuZ2UnLCB2YWwpCiAgICB9LAogICAgLyoqCiAgICAgKiDoioLngrnmoJHlhoXlrrnmuLLmn5MKICAgICAqIEBwYXJhbSB7ZnVuY3Rpb259IGgKICAgICAqIEBwYXJhbSB7T2JqZWN0fSBub2RlIOW9k+WJjeiKgueCueaJgOacieaVsOaNrgogICAgICogQHBhcmFtIHtPYmplY3R9IGRhdGEg5b2T5YmN6IqC54K555qEZGF0YeaVsOaNrgogICAgICogQHBhcmFtIHtPYmplY3R9IHN0b3JlIOW9k+WJjeiKgueCueaJgOacieWxnuaApwogICAgICovCiAgICByZW5kZXJDb250ZW50OiBmdW5jdGlvbiByZW5kZXJDb250ZW50KGgsIF9yZWYpIHsKICAgICAgdmFyIG5vZGUgPSBfcmVmLm5vZGUsCiAgICAgICAgZGF0YSA9IF9yZWYuZGF0YSwKICAgICAgICBzdG9yZSA9IF9yZWYuc3RvcmU7CiAgICAgIHJldHVybiB0aGlzLnZhbHVlT3B0aW9uLmRpc2FibGVOb2RlID8gaCgiZGl2IiwgewogICAgICAgICJjbGFzcyI6ICdjdXN0b20tdHJlZS1ub2RlJywKICAgICAgICAic3R5bGUiOiB7CiAgICAgICAgICBjdXJzb3I6ICFjb21tb25CbGFuayhkYXRhLmNoaWxkcmVuKSA/ICdub3QtYWxsb3dlZCcgOiAncG9pbnRlcicKICAgICAgICB9CiAgICAgIH0sIFtoKCJzcGFuIiwgW25vZGUubGFiZWxdKV0pIDogaCgiZGl2IiwgW25vZGUubGFiZWxdKTsKICAgIH0sCiAgICBibHVyRXZlbnQ6IGZ1bmN0aW9uIGJsdXJFdmVudCgpIHsKICAgICAgaWYgKGNvbW1vbkJsYW5rKHRoaXMudmFsdWUpKSB7CiAgICAgICAgLy8g6YeN5paw5pCc57SiIOeUqOepuuWAvAogICAgICAgIHRoaXMuc2VsZWN0RmlsdGVyKCcnKTsKICAgICAgfQogICAgfQogIH0KfTs="}, {"version": 3, "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA8CA;AACA;AACA;EACAA;EACAC;IACAC;MACAC;MACAC;QACA;MACA;IACA;IACAC;MACA;MACAF;MACAC;IACA;IACAE;MACA;MACAH;MACAC;IACA;IACAG;MACAJ;MACAC;IACA;IACAI;MACA;MACAL;MACAC;QACA;MACA;IACA;IACA;IACAK;MACAN;MAAA;MACAC;QACA;MACA;IACA;IACA;IACAM;MACAP;MACAC;QACA;UACAO;UAAA;UACAC;UAAA;UACAC;QACA;MACA;IACA;;IACA;IACAC;MACAX;MAAA;MACAC;QACA;MACA;IACA;IACAW;MACAZ;MAAA;MACAC;IACA;IACAY;MACAb;MACAC;QACA;MACA;IACA;IACAa;MACA;MACAd;MACAC;QACA;MACA;IACA;EACA;EACAc;IACA;MACAP;MACAQ;MAAA;MACAC;MAAA;MACAC;IACA;EACA;;EACAC;IACAP;MACA;QACA;QACA;UACA;QACA;QACA;QACA;QACA;QACA;MACA;QACA;QACA;QACA;QACA;QACA;MACA;IACA;IACAP;MACAe;QAAA;QACA;UACA;UACA;YACA;cACA;gBACA;gBACA;cACA;cACA;cACA;cACA;cACA,qCACA,oCACA;YACA;UACA;QACA;MACA;MACAC;IACA;EACA;EACAC;IAAA;IACA;MACA;IACA;EACA;EACAC;IACA;AACA;AACA;IACAC;MACA;MACA;QACA;MACA;IACA;IACA;AACA;AACA;AACA;IACAC;MAAA;MACA;MACA;MACA;MACA;QACA;MACA;QACA;QACA;QACA;UACAC;UACA;QACA;UACA;QACA;MACA;QACA;MACA;MACA;MACA;QACA;UACA;UACA;UACA;UACAC;YACAC;UACA;UACA;QACA;UACA;UACA;UACA;YACA;YACA;UACA;UACA;YACA;UACA;QACA;MACA;IACA;IACA;IACAC;MACA;MACA;QACA;MACA;MACA;MACA;QACA;QACA;QACAC;UACAC;UACAvB;QACA;QACAsB;UAAAtB;QAAA;MACA;QACA;QACAsB;QACA;QACA;QACA;QAAA,2CACAE;UAAA;QAAA;UAAA;YAAA;YACAC;YACAA;YACAH;YACA;cACAA;YACA;cACAA;YACA;UACA;UACA;QAAA;UAAA;QAAA;UAAA;QAAA;QACA;QACA;MACA;IACA;IACA;IACAI;MACA;MACA;QACA;QACA;QACA;MACA;IACA;IACA;IACAC;MACA;QACA;QACA;MACA;MACA;QACA;UACA;QACA;QACA;MACA;QACA;MACA;IACA;IACA;AACA;AACA;AACA;AACA;AACA;IACAC;MACA;QACA;MACA;MACA;MACA;MACA;QACA;QACA;UACA;UACA;UACAN;UACAA;UACAA;UACA;QACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;IACA;IACA;IACAO;MACA;QACA;QACA;UACA;YACA;YACA;YACA;UACA;QACA;MACA;IACA;IACA;IACAC;MACA;MACA;QACA;QACA;UACAC;QACA;QACA;QAAA,4CACA;UAAA;QAAA;UAAA;YAAA;YACAC;UACA;QAAA;UAAA;QAAA;UAAA;QAAA;QACA;MACA;QACA,sBACAC,wDACA;QACA;UACA;QACA;QACA;UACA;QACA;QACA;QACA;UACAF;QACA;QACA,2DACA,sBACA;UAAA;QAAA;MACA;MACA;MACA;MACA;MACA;IACA;IACA;AACA;AACA;IACAG;MACA;QACA;MACA;MACAC;MACAA;MACA;MACA;MACAb;MAAA,4CACAf;QAAA;MAAA;QAAA;UAAA;UACAkB;UACAA;UACAH;UACA;YACAA;UACA;YACAA;UACA;QACA;MAAA;QAAA;MAAA;QAAA;MAAA;MAEA;QACA;UACA;QACA;UACA;QACA;QACA;UAAAG;UAAAzB;QAAA;QACA;MACA;QACA;QACA;QACA;MACA;IACA;IACA;IACAoC;MACA;MACA;MACA;QACA;MACA;QACA;UACA;QACA;;QACA;MACA;MACA;MACA;MACA;MACA;IACA;IACA;IACAC;MACAF;MACA;QACA;QACAA;QACA;QACA;UACA;QACA;MACA;IACA;IACAG;MACA;IAAA,CACA;IACA;AACA;AACA;AACA;AACA;AACA;AACA;IACAC;MAAA;QAAAhC;QAAAiC;MACA;QAAA,SAEA;QAAA,SACA;UACAC;QACA;MAAA,eAEAN,2BAGAA,YACA;IACA;IACAO;MACA;QACA;QACA;MACA;IACA;EACA;AACA", "names": ["name", "props", "placeholder", "type", "default", "parentValue", "checkStrictly", "clearable", "newValue", "options", "defaultProps", "value", "label", "children", "defaultCheckNodes", "valueM", "defaultForm", "valueOption", "data", "parentList", "changeN", "searchFirstId", "watch", "handler", "deep", "mounted", "methods", "init", "initValue", "valueT", "selectAll", "datasValue", "enterEvent", "selectTree", "current<PERSON><PERSON><PERSON>", "getCheckedNodes", "item", "selectFilter", "filterNode", "nodeClick", "getTreeNode", "removeTag", "treeOption", "valueArr", "JSON", "checkNode", "node", "resetChecked", "changeTreeNodeStatus", "change", "renderContent", "store", "cursor", "blurEvent"], "sourceRoot": "node_modules/sunui/src/components/SunForm", "sources": ["selectTree.vue"], "sourcesContent": ["<template>\n  <el-select\n    ref=\"selectTree\"\n    v-model=\"value\"\n    style=\"width: 100%\"\n    :placeholder=\"placeholder\"\n    v-bind=\"$attrs\"\n    filterable\n    :clearable=\"clearable\"\n    :title=\"value\"\n    :popper-append-to-body=\"false\"\n    :filter-method=\"selectFilter\"\n    @keydown.enter.native=\"enterEvent\"\n    @clear=\"resetChecked\"\n    @change=\"change\"\n    @remove-tag=\"removeTag\"\n    @blur=\"blurEvent\"\n  >\n    <el-option value=\"\" />\n    <template v-if=\"valueOption.slotObj\">\n      <el-checkbox\n        v-model=\"valueOption.slotObj.checked\"\n        style=\"margin-left: 0.5rem\"\n        @change=\"valueOption.slotObj.click(valueOption)\"\n      >机构是否联动</el-checkbox>\n    </template>\n    <el-tree\n      ref=\"treeOption\"\n      :show-checkbox=\"$attrs.multiple\"\n      :check-strictly=\"checkStrictly\"\n      highlight-current\n      accordion\n      node-key=\"id\"\n      check-on-click-node\n      :data=\"options\"\n      :props=\"defaultProps\"\n      :render-content=\"renderContent\"\n      auto-expand-parent\n      :filter-node-method=\"filterNode\"\n      @check=\"checkNode\"\n      @node-click=\"nodeClick\"\n    />\n  </el-select>\n</template>\n\n<script>\nimport { debounce } from 'lodash'\nimport { commonBlank } from '../../utils/common'\nexport default {\n  name: 'SelectTree',\n  props: {\n    placeholder: {\n      type: String,\n      default: () => {\n        return '请选择'\n      }\n    },\n    parentValue: {\n      // 获取所有父级节点\n      type: Boolean,\n      default: false\n    },\n    checkStrictly: {\n      // 父级是否联动下级\n      type: Boolean,\n      default: false\n    },\n    clearable: {\n      type: Boolean,\n      default: true\n    },\n    newValue: {\n      // 与parentValue配对出现，parentValue 为true是有效，parentValue:true时，为当前选中值\n      type: Object,\n      default: () => {\n        return {}\n      }\n    },\n    // 节点数据\n    options: {\n      type: Array, // 必须是树形结构的对象数组\n      default: () => {\n        return []\n      }\n    },\n    // 设置lable value属性\n    defaultProps: {\n      type: Object,\n      default: () => {\n        return {\n          value: 'id', // ID字段名\n          label: 'label', // 显示名称\n          children: 'children' // 子级字段名\n        }\n      }\n    },\n    // 默认勾选的节点\n    defaultCheckNodes: {\n      type: Array, // 已经分配的资源\n      default: () => {\n        return []\n      }\n    },\n    valueM: {\n      type: null, // 已经分配的资源\n      default: null\n    },\n    defaultForm: {\n      type: Object,\n      default: () => {\n        return {}\n      }\n    },\n    valueOption: {\n      // 当前组件信息\n      type: Object,\n      default: () => {\n        return {}\n      }\n    }\n  },\n  data() {\n    return {\n      value: [],\n      parentList: [], // 父级节点\n      changeN: false, // 用于判断当前值的执行次数\n      searchFirstId: '' // 用于设置默认选中筛选数据第一条的数据\n    }\n  },\n  watch: {\n    valueM(value) {\n      if (value === null || value === '' || value.length === 0) {\n        this.value = ''\n        if (commonBlank(this.value)) {\n          this.resetChecked()\n        }\n        // if (this.changeN) {\n        //   return\n        // }\n        // 清空\n      } else {\n        // 初次执行\n        // if (this.changeN) {\n        //   return\n        // }\n        this.initValue(this.valueM)\n      }\n    },\n    newValue: {\n      handler(val) {\n        if (val.componentProps.parentValue) {\n          const valueN = this.newValue.componentProps.value\n          if (valueN !== null && valueN !== '') {\n            this.$nextTick(() => {\n              if (this.changeN) {\n                this.changeN = false\n                return\n              }\n              const nodeN = this.$refs.treeOption.getNode(valueN)\n              this.nodeClick(null, nodeN)\n              this.checkNode(nodeN, 'new')\n              this.$refs.treeOption.setCurrentKey(\n                this.newValue.componentProps.value\n              )\n            })\n          }\n        }\n      },\n      deep: true\n    }\n  },\n  mounted() {\n    this.$nextTick().then(() => {\n      this.init()\n    })\n  },\n  methods: {\n    /**\n     * 初始赋值\n     */\n    init() {\n      const nowValue = this.$attrs.value // 获取当前值\n      if (nowValue) {\n        this.initValue(nowValue)\n      }\n    },\n    /**\n     * 初始赋值\n     * @param {String}param 当前值\n     */\n    initValue(param) {\n      const nowValue = param\n      // eslint-disable-next-line valid-typeof\n      // 返回值数组情况下\n      if (Object.prototype.toString.call(nowValue) === '[object Array]') {\n        this.$refs.treeOption.setCheckedKeys(nowValue) // 设置默认值 单选\n      } else if (this.$attrs.multiple && nowValue !== null) {\n        // 复选\n        let valueT = []\n        if (nowValue.indexOf(',') !== -1) {\n          valueT = nowValue.split(',')\n          this.$refs.treeOption.setCheckedKeys([...valueT]) // 设置默认值\n        } else {\n          this.$refs.treeOption.setCheckedKeys([nowValue]) // 设置默认值\n        }\n      } else {\n        this.$refs.treeOption.setCurrentKey(nowValue) // 设置默认值 单选\n      }\n      // 赋值\n      this.$nextTick(() => {\n        if (this.$attrs.multiple && nowValue !== null) {\n          const selectAll = this.$refs.treeOption.getCheckedNodes()\n          // this.value = this.$refs.treeOption.getCheckedKeys()\n          let datasValue = []\n          selectAll.map((item) => {\n            datasValue = [...datasValue, item.label]\n          })\n          this.value = datasValue\n        } else {\n          const selectOne = this.$refs.treeOption.getCurrentNode()\n          // this.value = this.$refs.treeOption.getCurrentKey() // 设置默认值\n          if (nowValue === 0) {\n            this.value = 0\n            return\n          }\n          if (selectOne !== null) {\n            this.value = selectOne.label\n          }\n        }\n      })\n    },\n    // 键盘enter事件\n    enterEvent() {\n      const node = this.$refs.treeOption.getCurrentNode() // 获取当前节点\n      if (!node) {\n        return\n      }\n      const selectTree = this.$refs.selectTree\n      if (!this.$attrs.multiple) {\n        // 单选\n        this.defaultForm[this.valueOption.name] = node.id + ''\n        selectTree.cachedOptions.push({\n          currentLabel: node.label,\n          value: node.id\n        })\n        selectTree.handleOptionSelect({ value: node.id }, true)\n      } else {\n        // 多选\n        selectTree.cachedOptions = []\n        this.$refs.treeOption.setChecked(node.id, true, true) // 设置当前节点选中\n        const getCheckedNodes = this.$refs.treeOption.getCheckedNodes() // 获取所有选中节点\n        const getCheckedKeys = this.$refs.treeOption.getCheckedKeys() // 获取所有选中节点值\n        for (const item of getCheckedNodes) {\n          item.currentLabel = item.label\n          item.value = item.id\n          selectTree.cachedOptions.push(item)\n          if (item === node.id) {\n            selectTree.handleOptionSelect(node, false)\n          } else {\n            selectTree.handleOptionSelect(node, true)\n          }\n        }\n        // 设置选中节点值\n        this.value = getCheckedKeys\n        this.defaultForm[this.valueOption.name] = getCheckedKeys\n      }\n    },\n    // select 搜索\n    selectFilter: debounce(function(val) {\n      this.searchFirstId = ''\n      if (this.$refs.treeOption) {\n        this.$refs.treeOption.filter(val)\n        // 默认选中过滤出后数据的第一条数据\n        this.$refs.treeOption.setCurrentKey(this.searchFirstId)\n      }\n    }, 600),\n    // 树节点过滤\n    filterNode(value, data) {\n      if (!value) {\n        this.searchFirstId = null\n        return true\n      }\n      if (data.label.indexOf(value) !== -1) {\n        if (!this.searchFirstId) {\n          this.searchFirstId = data.id\n        }\n        return true\n      } else {\n        return false\n      }\n    },\n    /**\n     * 节点被点击时的回调\n     * @param {Object} e 传递给 data 属性的数组中该节点所对应的对象\n     * @param {Object} Node 节点对应的 Node\n     * @param {Object} VueComponent 节点组件本身\n     */\n    nodeClick(e, node, data) {\n      if (!commonBlank(e.children) && this.valueOption.disableNode) {\n        return\n      }\n      // 禁用有子节点的父节点\n      // this.$emit('getNode', e)\n      if (!this.$attrs.multiple) {\n        // 当选中的值和上一次选中值不同时才走下面的代码\n        if (this.defaultForm[this.valueOption.name] !== node.data.id + '') {\n          this.defaultForm[this.valueOption.name] = node.data.id + ''\n          const selectTree = this.$refs.selectTree\n          selectTree.cachedOptions = []\n          selectTree.cachedOptions.push(node.data)\n          selectTree.handleOptionSelect(node.data, true)\n          // 以上代码应该是关闭下拉树框，没有其他用处\n        }\n      }\n      // 给selectTree的cachedOptions赋值 设置node.label，使用页面显示label值\n      // this.parentList = []\n      // this.getTreeNode(node)\n      // if (this.parentValue === true) {\n      //   this.$emit('selectParent', { item: this.newValue, value: node.data.id })\n      //   this.value = [...this.parentList, node.data.label].join('/')\n      // }\n    },\n    // 选中节点的所有⽗级id和codeItem信息\n    getTreeNode(node) {\n      if (node) {\n        // this.parentList所有⽗级节点的codeItem信息\n        if (node.parent) {\n          if (node.parent.label) {\n            this.parentList = [node.parent.label, ...this.parentList]\n            const nodes = this.$refs.treeOption.getNode(node.parent)\n            this.getTreeNode(nodes)\n          }\n        }\n      }\n    },\n    // 删除tag时，\n    removeTag(val) {\n      // 取消勾选\n      if (val.indexOf('-') !== -1) {\n        const treeOption = this.$refs.treeOption\n        this.$nextTick(() => {\n          treeOption.setChecked(val.split('-')[0], false, false)\n        })\n        let valueArr = []\n        for (const item of this.value) {\n          valueArr = [...valueArr, item.split('-')[0]]\n        }\n        this.defaultForm[this.valueOption.name] = valueArr // 设置默认值\n      } else {\n        const arr1 = JSON.parse(\n          JSON.stringify(this.defaultForm[this.valueOption.name])\n        )\n        const arr2 = this.value.map((item) => {\n          return item.split('-')[0]\n        })\n        const filterEle = arr1.concat(arr2).filter(function(v, i, arr) {\n          return arr.indexOf(v) === arr.lastIndexOf(v)\n        })\n        const treeOption = this.$refs.treeOption\n        this.$nextTick(() => {\n          treeOption.setChecked(filterEle[0], false, false)\n        })\n        this.defaultForm[this.valueOption.name] = this.defaultForm[\n          this.valueOption.name\n        ].filter((i) => i !== filterEle[0])\n      }\n      // const treeOption = this.$refs.treeOption\n      // treeOption.setChecked(val, false, false)\n      // 重新给控件赋值\n      // this.$emit('input', this.value)\n    },\n    /**\n     * 节点选中状态发生变化时的回调\n     */\n    checkNode(node, data, treeStatus, children) {\n      if (!node || !this.$attrs.multiple) {\n        return\n      }\n      node.value = node.id // 返显中文\n      node.currentLabel = node.label // 返显中文\n      // 给selectTree的cachedOptions赋值 设置node.label，使用页面显示label值\n      const selectTree = this.$refs.selectTree\n      selectTree.cachedOptions = []\n      for (const item of data.checkedNodes) {\n        item.currentLabel = item.label\n        item.value = item.id\n        selectTree.cachedOptions.push(item)\n        if (item === node.id) {\n          selectTree.handleOptionSelect(node, false)\n        } else {\n          selectTree.handleOptionSelect(node, true)\n        }\n      }\n\n      if (this.parentValue === true) {\n        if (treeStatus === 'new') {\n          this.changeN = false\n        } else {\n          this.changeN = true\n        }\n        this.$emit('selectParent', { item: this.newValue, value: node.id })\n        this.value = [...this.parentList, node.label].join('/')\n      } else {\n        this.changeN = true\n        this.value = data.checkedKeys\n        this.defaultForm[this.valueOption.name] = data.checkedKeys\n      }\n    },\n    // 清空\n    resetChecked() {\n      this.$refs.treeOption.setCheckedKeys([])\n      this.$refs.treeOption.setCurrentKey('')\n      if (this.$attrs.multiple) {\n        this.defaultForm[this.valueOption.name] = this.value\n      } else {\n        if (this.$refs.treeOption.root.store.currentNode) {\n          this.$refs.treeOption.root.store.currentNode.isCurrent = false // 清空时取消高亮样式\n        }\n        this.defaultForm[this.valueOption.name] = ''\n      }\n      // 重新搜索 用空值\n      this.selectFilter('')\n      // 设置所有的节点为闭合状态\n      this.changeTreeNodeStatus(this.$refs.treeOption.store.root)\n    },\n    // 闭合改变节点的状态\n    changeTreeNodeStatus(node) {\n      node.expanded = false\n      for (let i = 0; i < node.childNodes.length; i++) {\n        // 改变节点的自身expanded状态\n        node.childNodes[i].expanded = false\n        // 遍历子节点\n        if (node.childNodes[i].childNodes.length > 0) {\n          this.changeTreeNodeStatus(node.childNodes[i])\n        }\n      }\n    },\n    change(val) {\n      // this.$emit('change', val)\n    },\n    /**\n     * 节点树内容渲染\n     * @param {function} h\n     * @param {Object} node 当前节点所有数据\n     * @param {Object} data 当前节点的data数据\n     * @param {Object} store 当前节点所有属性\n     */\n    renderContent(h, { node, data, store }) {\n      return this.valueOption.disableNode ? (\n        <div\n          class='custom-tree-node'\n          style={{\n            cursor: !commonBlank(data.children) ? 'not-allowed' : 'pointer'\n          }}\n        >\n          <span>{node.label}</span>\n        </div>\n      ) : (\n        <div>{node.label}</div>\n      )\n    },\n    blurEvent() {\n      if (commonBlank(this.value)) {\n        // 重新搜索 用空值\n        this.selectFilter('')\n      }\n    }\n  }\n}\n</script>\n<style scoped>\n/* 去除tree上面的一行高度 */\n.el-scrollbar .el-scrollbar__view .el-select-dropdown__item {\n  height: auto;\n  padding: 0;\n}\n</style>\n"]}]}