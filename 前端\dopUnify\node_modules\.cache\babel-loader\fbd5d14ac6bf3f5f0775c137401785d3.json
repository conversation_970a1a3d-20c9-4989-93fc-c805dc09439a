{"remainingRequest": "E:\\华南区项目\\中国银行\\开发区\\科技公司运营平台\\前端\\dopUnify\\node_modules\\babel-loader\\lib\\index.js!E:\\华南区项目\\中国银行\\开发区\\科技公司运营平台\\前端\\dopUnify\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!E:\\华南区项目\\中国银行\\开发区\\科技公司运营平台\\前端\\dopUnify\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\华南区项目\\中国银行\\开发区\\科技公司运营平台\\前端\\dopUnify\\src\\layout\\components\\Sublicense\\index.vue?vue&type=script&lang=js&", "dependencies": [{"path": "E:\\华南区项目\\中国银行\\开发区\\科技公司运营平台\\前端\\dopUnify\\src\\layout\\components\\Sublicense\\index.vue", "mtime": 1721631954662}, {"path": "E:\\华南区项目\\中国银行\\开发区\\科技公司运营平台\\前端\\dopUnify\\babel.config.js", "mtime": 1721631954662}, {"path": "E:\\华南区项目\\中国银行\\开发区\\科技公司运营平台\\前端\\dopUnify\\.babelrc", "mtime": 1721631954662}, {"path": "E:\\华南区项目\\中国银行\\开发区\\科技公司运营平台\\前端\\dopUnify\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "E:\\华南区项目\\中国银行\\开发区\\科技公司运营平台\\前端\\dopUnify\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "E:\\华南区项目\\中国银行\\开发区\\科技公司运营平台\\前端\\dopUnify\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "E:\\华南区项目\\中国银行\\开发区\\科技公司运营平台\\前端\\dopUnify\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1655715099000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAoFA;AACA;;AAEA,SACAA,eACAC,kBACAC,wBACA;AACA;AACA;AACA;AACA;EAAAC;EAAAC;EAAAC;AACA;EACAC;EACAC;IACA;MACAC;QACA;MAAA,CACA;MACAC;QACA;QACAC;UACAC;QACA;QACAC;UACAD;QACA;MACA;MACAE;QACAC;UACA;UACAC;UACAC;QACA;QACAC;QACAC;UACAC;UACAC;UAAA;UACAC;YACAC;YAAA;YACAC;YAAA;YACAC;YAAA;YACAC;YAAA;YACAC;YAAA;YACAC;YAAA;YACAC;UACA;QACA;MACA;;MACAC;MAAA;MACAC;MAAA;MACAC;QACAC;QACAC;MACA;MACAC;MAAA;MACAC;MACAC;MAAA;MACAC;MAAA;MACAC;MAAA;MACAC;QACA;QACAzB;UACA;UACAC;UACAC;QACA;QACAC;QACAC;UACAC;UACAC;UAAA;UACAC;YACAmB;UACA;QACA;MACA;;MACAC;MAAA;MACAC;IACA;EACA;;EACAC;EACAC;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACAN;MACA;QACAO;MACA;IACA;EACA;EACAC;EACAC;EACAC;IACA;AACA;IACAC;MAAA;MACA;MACA;QACA,0CACAC;QACA,+DACA,yBACA;MACA;IACA;IACA;AACA;IACAC;MAAA;MACA;MACA;MACA;QACA;QACA;MACA;IACA;IACA;AACA;IACAC;MACA;IACA;IACA;AACA;IACAC;MAAA;MACA;MACA;QACAC;QACAC;UACAC;UACAC;UACAC;UACAC;QACA;MACA;MACAC;QACA;QACA;QACAC;UACA;YACAC;YACA7B;UACA;QACA;MACA;IACA;IACA;AACA;AACA;AACA;IACA8B;MAAA;MACA;MACA;QACAT;QACAC;UACAC;UACAC;UACAC;UACAC;QACA;MACA;MACAC;QACA;QACA;QACAC;UACA;YACAC;YACA7B;UACA;QACA;MACA;IACA;IACA;AACA;AACA;AACA;IACA+B;MACA;QACA;MACA;IACA;IAEA;AACA;IACAC;MACA;MACA;IACA;IACA;AACA;IACAC;MACA;IACA;IACA;AACA;IACAC;MAAA;MACA;QAAA3C;QAAAF;MACA;QACAtB;QACA;MACA;MACA;MACA;QACAsD;QACAc;QACAV;QACAW,wBACA,qDACA;MAEA;MACAlE;QACA;UACA;UACA;YACAmD;YACAI;YACAY;UACA;UACAlE;YACA;YACA;YACA;cACAH,iBACA,qBACA,QACA;gBACA;kBACA;gBACA;cACA,EACA;YACA;cACA;cACA;YACA;UACA;QACA;MACA;IACA;IACA;IACAsE;MACA;MACA;QACA;MACA;QACA;QACA;MACA;IACA;IACA;AACA;IACAC;MAAA;MACA;QACAlB;QACAI;QACAW,wBACA,qDACA;MAEA;MACA;MACAlE;QACA;UACA;UACA;QACA;MACA;IACA;IACA;IACAsE;MAAA;MACA;MACA;MACA;MACA,6BACA;QADAnD;QAAAE;QAAAC;QAAAC;QAAAC;QAAAC;MAEA,UACA,mDACA;QACA0B;QACAI;QACAgB;QACAC;QACAC;QACAC;QACAnD;QACAE;MACA,IACA;QACA0B;QACAwB;QAAA;QACAC;QAAA;QACArB;QACAgB;QACAC;QACAC;QACAC;QACAnD;QACAE;MACA;MACAvB;QACA;UACA;UACA;UACA;UACA;UACA;YACA;YACA;cACA2E;cACA;YACA;UACA;QACA;UACA;UACA;UACA;UACA9E;QACA;MACA;IACA;IACA;AACA;IACA+E;MAAA;MAAA;QAAA;UAAA;YAAA;cAAA;gBAAA;gBAAA,OACA;cAAA;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IACA;IACA;AACA;IACAC;MACA;IACA;IACA;AACA;IACAC;MACA;IACA;EACA;AACA", "names": ["commonMsgWarn", "commonMsgConfirm", "commonMsgSuccess", "psdValid", "noapproved", "subLicense", "name", "data", "btnAll", "btnDatas", "btnSave", "show", "btnCancle", "dialog", "componentProps", "width", "title", "visible", "form", "config", "labelWidth", "defaultForm", "user", "psd", "target", "reason", "author_type", "date", "remark", "dialogVisible", "treeData", "defaultProps", "children", "label", "defaultId", "titles", "searchVal", "roleData", "roleValue", "dialogPsd", "password", "Successdialog", "Sencond", "computed", "watch", "val", "created", "mounted", "methods", "dialogShow", "dictionaryFieds", "elDialogShow", "organTreeGet", "handleSearch", "parameterList", "sysMap", "role_id", "org_id", "user_no", "org_tp", "getUserInfo", "users", "key", "getCurrentNode", "leftC", "handleSave", "handleCancle", "dialogSumbit", "oper_type", "passWord", "organ_no", "approved", "dialogSumbitPsd", "empower", "target_user_no", "start_date", "end_date", "sublicense_reason", "module_id", "inst_name", "window", "logout", "dialogClosePsd", "dialogClose"], "sourceRoot": "src/layout/components/Sublicense", "sources": ["index.vue"], "sourcesContent": ["/** 转授权 */\r\n<template>\r\n  <div ref=\"sublice\">\r\n    <sun-form-dialog\r\n      :dialog-config=\"dialog\"\r\n      @dialogClose=\"dialogClose\"\r\n      @dialogSubmit=\"dialogSumbit\"\r\n    /><!--转授权弹出框-->\r\n    <el-dialog\r\n      v-if=\"dialogVisible\"\r\n      ref=\"refDialog\"\r\n      width=\"95rem\"\r\n      title=\"转授权目标人员\"\r\n      :visible.sync=\"dialogVisible\"\r\n      @close=\"handleCancle()\"\r\n    >\r\n      <div class=\"targetDialog\">\r\n        <el-tree\r\n          ref=\"tree\"\r\n          :data=\"treeData\"\r\n          :props=\"defaultProps\"\r\n          node-key=\"id\"\r\n          :expand-on-click-node=\"false\"\r\n          highlight-current\r\n          :current-node-key=\"defaultId\"\r\n          :default-expanded-keys=\"[defaultId]\"\r\n          @current-change=\"getCurrentNode\"\r\n        />\r\n        <el-transfer\r\n          ref=\"transfer\"\r\n          v-model=\"roleValue\"\r\n          filterable\r\n          :titles=\"titles\"\r\n          :data=\"roleData\"\r\n          @left-check-change=\"leftC\"\r\n        >\r\n          <span\r\n            slot-scope=\"{ option }\"\r\n          >{{ option.key }} - {{ option.label }}\r\n          </span>\r\n\r\n          <el-input\r\n            slot=\"left-footer\"\r\n            v-model=\"searchVal\"\r\n            placeholder=\"支持用户号和用户名模糊查询\"\r\n          >\r\n            <i\r\n              slot=\"suffix\"\r\n              class=\"el-input__icon el-icon-search\"\r\n              @click=\"handleSearch\"\r\n            /> </el-input></el-transfer>\r\n      </div>\r\n      <sun-button\r\n        class=\"sunButton\"\r\n        :btn-datas=\"btnDatas\"\r\n        @handleSave=\"handleSave\"\r\n        @handleCancle=\"handleCancle\"\r\n      /></el-dialog><!-- 选择目标用户弹出框 -->\r\n    <sun-form-dialog\r\n      :dialog-config=\"dialogPsd\"\r\n      @dialogClose=\"dialogClosePsd\"\r\n      @dialogSubmit=\"dialogSumbitPsd\"\r\n    /><!--目标用密码弹框-->\r\n\r\n    <!-- 转授权成功弹窗 -->\r\n    <el-dialog\r\n      title=\"重新登陆\"\r\n      top=\"15%\"\r\n      :visible.sync=\"Successdialog\"\r\n      :show-close=\"false\"\r\n      :close-on-press-escape=\"false\"\r\n      :close-on-click-modal=\"false\"\r\n      width=\"30%\"\r\n    >\r\n      <span style=\"font-size: 14px\">\r\n        <el-button type=\"success\" icon=\"el-icon-check\" circle /><span\r\n          style=\"color: teal\"\r\n        >\r\n          转授权成功 </span><span>请等待<span style=\"font-size: 20px; color: red\"> {{ Sencond }} </span>秒，将自动跳转到登录页面</span></span>\r\n    </el-dialog>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport { dictionaryFieds } from '@/utils/dictionary.js' // 字典常量\r\nimport { config, configPsd } from './info' // 表头、表单配置\r\n\r\nimport {\r\n  commonMsgWarn,\r\n  commonMsgConfirm,\r\n  commonMsgSuccess\r\n} from '@/utils/message.js' // 提示信息\r\nimport { encryptResult } from '@/utils/crypto' // 加密\r\nimport { formatTime } from '@/utils/date.js'\r\nimport { Common } from '@/api'\r\nconst { getUserInfo, psdValid, noapproved, subLicense } = Common\r\nexport default {\r\n  name: 'Sublicense',\r\n  data() {\r\n    return {\r\n      btnAll: {\r\n        // 当前页需要配置权限的按钮  权限获取\r\n      },\r\n      btnDatas: {\r\n        // 按钮配置\r\n        btnSave: {\r\n          show: true\r\n        },\r\n        btnCancle: {\r\n          show: true\r\n        }\r\n      },\r\n      dialog: {\r\n        componentProps: {\r\n          // 弹出框配置属性\r\n          width: '50rem',\r\n          title: '转授权'\r\n        },\r\n        visible: false,\r\n        form: {\r\n          config: config(this),\r\n          labelWidth: '10rem', // 当前表单标签宽度配置\r\n          defaultForm: {\r\n            user: this.$store.getters.userNo, // 授权用户\r\n            psd: '', // 用户密码\r\n            target: '', // 目标用户\r\n            reason: '', // 授权原因\r\n            author_type: '', // 授权方式\r\n            date: '', // 日期区间\r\n            remark: '' // 备注\r\n          }\r\n        }\r\n      },\r\n      dialogVisible: false, // 目标人员选择弹框\r\n      treeData: [], // 左侧树\r\n      defaultProps: {\r\n        children: 'children',\r\n        label: 'label'\r\n      },\r\n      defaultId: this.$store.getters.organNo, // 默认选中节点\r\n      titles: ['可选人员', '选中人员'],\r\n      searchVal: '', // 搜索框的值\r\n      roleData: [], // 穿梭框左边的值\r\n      roleValue: [], // 穿梭框右边的值\r\n      dialogPsd: {\r\n        // 目标用户密码弹框\r\n        componentProps: {\r\n          // 弹出框配置属性\r\n          width: '50rem',\r\n          title: '密码校验'\r\n        },\r\n        visible: false,\r\n        form: {\r\n          config: configPsd(this),\r\n          labelWidth: '14rem', // 当前表单标签宽度配置\r\n          defaultForm: {\r\n            password: this.$store.getters.emptyPswd // 目标用户密码\r\n          }\r\n        }\r\n      },\r\n      Successdialog: false, // 转授权成功提示弹窗\r\n      Sencond: 5 // 设置初始倒计时\r\n    }\r\n  },\r\n  computed: {},\r\n  watch: {\r\n    // dialogVisible(val) {\r\n    //   // 弹框显示,清空转授权弹框右侧和输入框的值\r\n    //   if (val) {\r\n    //     this.searchVal = ''\r\n    //     this.roleValue = []\r\n    //   }\r\n    // },\r\n    roleValue(val) {\r\n      if (val.length > 1) {\r\n        val.shift()\r\n      }\r\n    }\r\n  },\r\n  created() {},\r\n  mounted() {},\r\n  methods: {\r\n    /**\r\n     * 转授权弹框显示*/\r\n    dialogShow() {\r\n      this.dialog.visible = true\r\n      this.$nextTick(() => {\r\n        this.dialog.form.config.reason.options =\r\n          dictionaryFieds('SUBLICENSE_REASON') // 授权原因\r\n        this.dialog.form.config.author_type.options = dictionaryFieds(\r\n          'SUBLICENSE_AUTHOR_TYPE'\r\n        ) // 授权方式\r\n      })\r\n    },\r\n    /**\r\n     * 转授权选择弹框显示*/\r\n    elDialogShow() {\r\n      this.dialogVisible = true\r\n      this.organTreeGet() // 加载机构树\r\n      this.$nextTick(() => {\r\n        this.$refs.tree.setCurrentKey(this.$store.getters.organNo) // 默认选中节点\r\n        this.getCurrentNode(this.$store.getters.organNo) // 默认展示当前用户机构号匹配到的用户\r\n      })\r\n    },\r\n    /**\r\n     * 配置机构树*/\r\n    organTreeGet() {\r\n      this.treeData = this.$store.getters.organTree\r\n    },\r\n    /**\r\n     * 搜索框*/\r\n    handleSearch() {\r\n      // 根据用户号或用户名查询用户信息\r\n      const msg = {\r\n        parameterList: [{}],\r\n        sysMap: {\r\n          role_id: '',\r\n          org_id: '',\r\n          user_no: this.searchVal,\r\n          org_tp: ''\r\n        }\r\n      }\r\n      getUserInfo(msg).then((res) => {\r\n        const { users } = res.retMap\r\n        this.roleData = []\r\n        users.forEach((item) => {\r\n          this.roleData.push({\r\n            key: item.user_no,\r\n            label: item.user_name\r\n          })\r\n        })\r\n      })\r\n    },\r\n    /**\r\n     * 选中节点\r\n     * @param {Object}data 选中行数据\r\n     */\r\n    getCurrentNode(data) {\r\n      // 根据机构号查询用户信息\r\n      const msg = {\r\n        parameterList: [{}],\r\n        sysMap: {\r\n          role_id: '',\r\n          org_id: data.id,\r\n          user_no: '',\r\n          org_tp: ''\r\n        }\r\n      }\r\n      getUserInfo(msg).then((res) => {\r\n        const { users } = res.retMap\r\n        this.roleData = []\r\n        users.forEach((item) => {\r\n          this.roleData.push({\r\n            key: item.user_no,\r\n            label: item.user_name\r\n          })\r\n        })\r\n      })\r\n    },\r\n    /**\r\n     * 穿梭框左侧选中事件\r\n     * @param {Array} value 选中值\r\n     */\r\n    leftC(value) {\r\n      if (value.length > 1) {\r\n        this.$refs.transfer.$children[0].checked.shift()\r\n      }\r\n    },\r\n\r\n    /**\r\n     *(目标人员弹框)-按钮-保存*/\r\n    handleSave() {\r\n      this.dialog.form.defaultForm.target = this.roleValue.join()\r\n      this.dialogVisible = false // 目标人员弹框关闭\r\n    },\r\n    /**\r\n     *(目标人员弹框)-按钮-取消*/\r\n    handleCancle() {\r\n      this.dialogVisible = false // 目标人员弹框关闭\r\n    },\r\n    /**\r\n     * 转授权弹框-按钮--确定*/\r\n    dialogSumbit() {\r\n      const { target, user } = this.dialog.form.defaultForm\r\n      if (user === target) {\r\n        commonMsgWarn('授权用户和授权对象用户相同，不能转授权', this)\r\n        return\r\n      }\r\n      // 先进性密码检验\r\n      const msg = {\r\n        parameterList: [],\r\n        oper_type: 'queryUser',\r\n        user_no: this.dialog.form.defaultForm.user,\r\n        passWord: encryptResult(\r\n          this.$store.getters.initParams.enSecMap.encryptType,\r\n          this.dialog.form.defaultForm.psd\r\n        )\r\n      }\r\n      psdValid(msg).then((res) => {\r\n        if (res.retCode === '200') {\r\n          // 查询当前用户是否有待审批任务\r\n          const msg2 = {\r\n            parameterList: [],\r\n            user_no: this.$store.getters.userNo,\r\n            organ_no: this.$store.getters.organNo\r\n          }\r\n          noapproved(msg2).then((res2) => {\r\n            // haveTask 为1时代表存在待审核任务，为0则表示没有待审核任务\r\n            // 有审批任务  提示当前用户有待审批任务是否进行转授权   授权请求  1.本地审核（本地输入授权用户密码） 2.远程审核（发起流 程）\r\n            if (res2.retMap.haveTask === '1') {\r\n              commonMsgConfirm(\r\n                '当前用户有待审批任务是否进行转授权',\r\n                this,\r\n                (param) => {\r\n                  if (param) {\r\n                    this.approved()\r\n                  }\r\n                }\r\n              )\r\n            } else {\r\n              // 无审批任务   授权请求  1.本地审核（本地输入授权用户密码） 2.远程审核（发起流程）\r\n              this.approved()\r\n            }\r\n          })\r\n        }\r\n      })\r\n    },\r\n    // 转授权 本地审核和远程审核方法   1.本地审核（本地输入授权用户密码） 2.远程审核（发起流程）\r\n    approved() {\r\n      // 本地审核   弹密授权用户码输入框\r\n      if (this.dialog.form.defaultForm.author_type === '1') {\r\n        this.dialogPsd.visible = true\r\n      } else {\r\n        // 远程授权\r\n        this.empower()\r\n      }\r\n    },\r\n    /**\r\n     * 目标用户密码校验弹出框 - 确定*/\r\n    dialogSumbitPsd() {\r\n      const msg = {\r\n        parameterList: [],\r\n        user_no: this.dialog.form.defaultForm.target,\r\n        passWord: encryptResult(\r\n          this.$store.getters.initParams.enSecMap.encryptType,\r\n          this.dialogPsd.form.defaultForm.password\r\n        )\r\n      }\r\n      // 密码校验   再进行授权请求\r\n      psdValid(msg).then((res) => {\r\n        if (res.retCode === '200') {\r\n          // 发起授权请求\r\n          this.empower()\r\n        }\r\n      })\r\n    },\r\n    // 转授权请求\r\n    empower() {\r\n      // 标题 （模板名称（时间））\r\n      const time = formatTime(new Date(), 'yyyy-MM-dd hh:mm:ss')\r\n      const inst_name = `转授权模板(${this.$store.getters.userName} ${time})`\r\n      const { user, target, reason, author_type, date, remark } =\r\n        this.dialog.form.defaultForm\r\n      const msg =\r\n        this.dialog.form.defaultForm.author_type === '1'\r\n          ? {\r\n            parameterList: [],\r\n            user_no: user,\r\n            target_user_no: target,\r\n            start_date: date[0],\r\n            end_date: date[1],\r\n            sublicense_reason: reason,\r\n            author_type: author_type,\r\n            remark: remark\r\n          }\r\n          : {\r\n            parameterList: [],\r\n            module_id: '20201221134046308029', // 模板id 远程审批有参数  本地审核无\r\n            inst_name: inst_name, // 标题  远程审批有参数  本地审核无\r\n            user_no: user,\r\n            target_user_no: target,\r\n            start_date: date[0],\r\n            end_date: date[1],\r\n            sublicense_reason: reason,\r\n            author_type: author_type,\r\n            remark: remark\r\n          }\r\n      subLicense(msg).then((res) => {\r\n        if (this.dialog.form.defaultForm.author_type === '1') {\r\n          // 本地授权  弹框跳转登录页\r\n          this.dialog.visible = false\r\n          this.dialogPsd.visible = false\r\n          this.Successdialog = true\r\n          const interval = setInterval(() => {\r\n            this.Sencond--\r\n            if (this.Sencond === 0) {\r\n              window.clearInterval(interval)\r\n              this.logout() // 倒计时结束时跳转登录页面\r\n            }\r\n          }, 1000)\r\n        } else {\r\n          // 远程审核\r\n          this.dialog.visible = false\r\n          this.dialogPsd.visible = false\r\n          commonMsgSuccess('流程发起成功', this)\r\n        }\r\n      })\r\n    },\r\n    /**\r\n     * 跳转登录页面方法*/\r\n    async logout() {\r\n      await this.$store.dispatch('user/logout')\r\n      this.$router.push(`/login?redirect=${this.$route.fullPath}`)\r\n    },\r\n    /**\r\n     * 目标用户密码校验弹出框 - 关闭*/\r\n    dialogClosePsd() {\r\n      this.dialogPsd.visible = false\r\n    },\r\n    /**\r\n     * 转授权弹出框 - 关闭*/\r\n    dialogClose() {\r\n      this.dialog.visible = false\r\n    }\r\n  }\r\n}\r\n</script>\r\n<style lang=\"scss\" scoped>\r\n.targetDialog {\r\n  display: flex;\r\n  justify-content: space-around;\r\n}\r\n::v-deep .el-tree {\r\n  height: 400px;\r\n  width: 260px;\r\n  overflow: auto;\r\n}\r\n::v-deep .el-transfer-panel {\r\n  width: 230px !important;\r\n  .el-transfer-panel__header {\r\n    .el-checkbox {\r\n      .el-checkbox__input {\r\n        .el-checkbox__inner {\r\n          display: none;\r\n        }\r\n      }\r\n      .el-checkbox__label {\r\n        span {\r\n          display: none;\r\n        }\r\n      }\r\n    }\r\n  }\r\n  .el-transfer-panel__body {\r\n    height: 345px;\r\n  }\r\n  .el-transfer-panel__footer {\r\n    width: 92%;\r\n    position: absolute;\r\n    top: 50px;\r\n    left: 6px;\r\n    .el-input__inner {\r\n      font-size: 13px;\r\n      padding: 0px 30px 0 8px;\r\n    }\r\n    .el-icon-search {\r\n      font-size: 12px;\r\n    }\r\n  }\r\n}\r\n::v-deep .is-filterable {\r\n  height: 300px !important;\r\n}\r\n::v-deep\r\n  .el-transfer\r\n  .el-transfer-panel:nth-child(3)\r\n  .el-transfer-panel__body\r\n  .el-transfer-panel__filter {\r\n  display: none !important;\r\n}\r\n.sunButton {\r\n  margin-top: 10px;\r\n  text-align: center;\r\n}\r\n.el-button.is-circle {\r\n  padding: 10px !important;\r\n}\r\n</style>\r\n"]}]}