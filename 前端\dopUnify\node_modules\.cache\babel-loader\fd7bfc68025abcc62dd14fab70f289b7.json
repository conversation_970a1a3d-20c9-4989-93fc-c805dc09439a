{"remainingRequest": "E:\\华南区项目\\中国银行\\开发区\\科技公司运营平台\\前端\\dopUnify\\node_modules\\babel-loader\\lib\\index.js!E:\\华南区项目\\中国银行\\开发区\\科技公司运营平台\\前端\\dopUnify\\node_modules\\eslint-loader\\index.js??ref--13-0!E:\\华南区项目\\中国银行\\开发区\\科技公司运营平台\\前端\\dopUnify\\src\\router\\routers\\system\\outManage.js", "dependencies": [{"path": "E:\\华南区项目\\中国银行\\开发区\\科技公司运营平台\\前端\\dopUnify\\src\\router\\routers\\system\\outManage.js", "mtime": 1721631954662}, {"path": "E:\\华南区项目\\中国银行\\开发区\\科技公司运营平台\\前端\\dopUnify\\babel.config.js", "mtime": 1721631954662}, {"path": "E:\\华南区项目\\中国银行\\开发区\\科技公司运营平台\\前端\\dopUnify\\.babelrc", "mtime": 1721631954662}, {"path": "E:\\华南区项目\\中国银行\\开发区\\科技公司运营平台\\前端\\dopUnify\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "E:\\华南区项目\\中国银行\\开发区\\科技公司运营平台\\前端\\dopUnify\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "E:\\华南区项目\\中国银行\\开发区\\科技公司运营平台\\前端\\dopUnify\\node_modules\\eslint-loader\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:LyoqDQogKiDns7vnu5/nrqHnkIY6IOWvueWkluaOpeWPo+euoeeQhg0KICovCmV4cG9ydCB2YXIgb3V0TWFuYWdlID0gewogIG91dE1hbmFnZTogewogICAgdGl0bGU6ICflr7nlpJbmjqXlj6PnrqHnkIYnLAogICAgbmFtZTogJ091dE1hbmFnZScsCiAgICBjb21wb25lbnQ6ICdzeXN0ZW0vb3V0TWFuYWdlJwogIH0sCiAgJ2RlZmluaXRpb24nOiB7CiAgICB0aXRsZTogJ+WvueWkluaOpeWPo+WumuS5iScsCiAgICBuYW1lOiAnRGVmaW5pdGlvbicsCiAgICBjb21wb25lbnQ6ICdzeXN0ZW0vb3V0TWFuYWdlL2RlZmluaXRpb24nCiAgfSwKICAnc3lzdGVtJzogewogICAgdGl0bGU6ICflr7nlpJbmjqXlj6Pns7vnu5/lrprkuYknLAogICAgbmFtZTogJ1N5c3RlbScsCiAgICBjb21wb25lbnQ6ICdzeXN0ZW0vb3V0TWFuYWdlL3N5c3RlbScKICB9LAogICdwZXJtaXNzaW9ucyc6IHsKICAgIHRpdGxlOiAn5a+55aSW5o6l5Y+j57O757uf5p2D6ZmQ6YWN572uJywKICAgIG5hbWU6ICdQZXJtaXNzaW9ucycsCiAgICBjb21wb25lbnQ6ICdzeXN0ZW0vb3V0TWFuYWdlL3Blcm1pc3Npb25zJwogIH0sCiAgJ3JlZ2lzdHJhdGlvbic6IHsKICAgIHRpdGxlOiAn5a+55aSW5o6l5Y+j6K+35rGC55m76K6w57C/JywKICAgIG5hbWU6ICdSZWdpc3RyYXRpb24nLAogICAgY29tcG9uZW50OiAnc3lzdGVtL291dE1hbmFnZS9yZWdpc3RyYXRpb24nCiAgfQp9Ow=="}, {"version": 3, "names": ["outManage", "title", "name", "component"], "sources": ["E:/华南区项目/中国银行/开发区/科技公司运营平台/前端/dopUnify/src/router/routers/system/outManage.js"], "sourcesContent": ["/**\r\n * 系统管理: 对外接口管理\r\n */\r\nexport const outManage = {\r\n  outManage: {\r\n    title: '对外接口管理',\r\n    name: 'OutManage',\r\n    component: 'system/outManage'\r\n  },\r\n  'definition': {\r\n    title: '对外接口定义',\r\n    name: 'Definition',\r\n    component: 'system/outManage/definition'\r\n  },\r\n  'system': {\r\n    title: '对外接口系统定义',\r\n    name: 'System',\r\n    component: 'system/outManage/system'\r\n  },\r\n  'permissions': {\r\n    title: '对外接口系统权限配置',\r\n    name: 'Permissions',\r\n    component: 'system/outManage/permissions'\r\n  },\r\n  'registration': {\r\n    title: '对外接口请求登记簿',\r\n    name: 'Registration',\r\n    component: 'system/outManage/registration'\r\n  }\r\n}\r\n"], "mappings": "AAAA;AACA;AACA;AACA,OAAO,IAAMA,SAAS,GAAG;EACvBA,SAAS,EAAE;IACTC,KAAK,EAAE,QAAQ;IACfC,IAAI,EAAE,WAAW;IACjBC,SAAS,EAAE;EACb,CAAC;EACD,YAAY,EAAE;IACZF,KAAK,EAAE,QAAQ;IACfC,IAAI,EAAE,YAAY;IAClBC,SAAS,EAAE;EACb,CAAC;EACD,QAAQ,EAAE;IACRF,KAAK,EAAE,UAAU;IACjBC,IAAI,EAAE,QAAQ;IACdC,SAAS,EAAE;EACb,CAAC;EACD,aAAa,EAAE;IACbF,KAAK,EAAE,YAAY;IACnBC,IAAI,EAAE,aAAa;IACnBC,SAAS,EAAE;EACb,CAAC;EACD,cAAc,EAAE;IACdF,KAAK,EAAE,WAAW;IAClBC,IAAI,EAAE,cAAc;IACpBC,SAAS,EAAE;EACb;AACF,CAAC"}]}