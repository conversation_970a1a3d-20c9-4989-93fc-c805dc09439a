{"remainingRequest": "D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\node_modules\\babel-loader\\lib\\index.js!D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\node_modules\\qiankun\\es\\addons\\index.js", "dependencies": [{"path": "D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\node_modules\\qiankun\\es\\addons\\index.js", "mtime": 1667130453000}, {"path": "D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\babel.config.js", "mtime": 1686019818359}, {"path": "D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan-unify\\dopUnify\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:aW1wb3J0IF9jb25jYXQgZnJvbSAibG9kYXNoL2NvbmNhdCI7CmltcG9ydCBfbWVyZ2VXaXRoIGZyb20gImxvZGFzaC9tZXJnZVdpdGgiOwppbXBvcnQgZ2V0RW5naW5lRmxhZ0FkZG9uIGZyb20gJy4vZW5naW5lRmxhZyc7CmltcG9ydCBnZXRSdW50aW1lUHVibGljUGF0aEFkZE9uIGZyb20gJy4vcnVudGltZVB1YmxpY1BhdGgnOwpleHBvcnQgZGVmYXVsdCBmdW5jdGlvbiBnZXRBZGRPbnMoZ2xvYmFsLCBwdWJsaWNQYXRoKSB7CiAgcmV0dXJuIF9tZXJnZVdpdGgoe30sIGdldEVuZ2luZUZsYWdBZGRvbihnbG9iYWwpLCBnZXRSdW50aW1lUHVibGljUGF0aEFkZE9uKGdsb2JhbCwgcHVibGljUGF0aCksIGZ1bmN0aW9uICh2MSwgdjIpIHsKICAgIHJldHVybiBfY29uY2F0KHYxICE9PSBudWxsICYmIHYxICE9PSB2b2lkIDAgPyB2MSA6IFtdLCB2MiAhPT0gbnVsbCAmJiB2MiAhPT0gdm9pZCAwID8gdjIgOiBbXSk7CiAgfSk7Cn0="}, {"version": 3, "names": ["_concat", "_mergeWith", "getEngineFlagAddon", "getRuntimePublicPathAddOn", "getAddOns", "global", "publicPath", "v1", "v2"], "sources": ["D:/1_Project/XYD_Project/dop-4.0/dop-4.1-qianduan-unify/dopUnify/node_modules/qiankun/es/addons/index.js"], "sourcesContent": ["import _concat from \"lodash/concat\";\nimport _mergeWith from \"lodash/mergeWith\";\nimport getEngineFlagAddon from './engineFlag';\nimport getRuntimePublicPathAddOn from './runtimePublicPath';\nexport default function getAddOns(global, publicPath) {\n  return _mergeWith({}, getEngineFlagAddon(global), getRuntimePublicPathAddOn(global, publicPath), function (v1, v2) {\n    return _concat(v1 !== null && v1 !== void 0 ? v1 : [], v2 !== null && v2 !== void 0 ? v2 : []);\n  });\n}"], "mappings": "AAAA,OAAOA,OAAO,MAAM,eAAe;AACnC,OAAOC,UAAU,MAAM,kBAAkB;AACzC,OAAOC,kBAAkB,MAAM,cAAc;AAC7C,OAAOC,yBAAyB,MAAM,qBAAqB;AAC3D,eAAe,SAASC,SAAS,CAACC,MAAM,EAAEC,UAAU,EAAE;EACpD,OAAOL,UAAU,CAAC,CAAC,CAAC,EAAEC,kBAAkB,CAACG,MAAM,CAAC,EAAEF,yBAAyB,CAACE,MAAM,EAAEC,UAAU,CAAC,EAAE,UAAUC,EAAE,EAAEC,EAAE,EAAE;IACjH,OAAOR,OAAO,CAACO,EAAE,KAAK,IAAI,IAAIA,EAAE,KAAK,KAAK,CAAC,GAAGA,EAAE,GAAG,EAAE,EAAEC,EAAE,KAAK,IAAI,IAAIA,EAAE,KAAK,KAAK,CAAC,GAAGA,EAAE,GAAG,EAAE,CAAC;EAChG,CAAC,CAAC;AACJ"}]}