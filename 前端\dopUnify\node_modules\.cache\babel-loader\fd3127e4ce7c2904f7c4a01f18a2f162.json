{"remainingRequest": "E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-q<PERSON>duan\\数字运营平台-统一门户工程\\dopUnify\\node_modules\\thread-loader\\dist\\cjs.js!E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan\\数字运营平台-统一门户工程\\dopUnify\\node_modules\\babel-loader\\lib\\index.js!E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan\\数字运营平台-统一门户工程\\dopUnify\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-qianduan\\数字运营平台-统一门户工程\\dopUnify\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-q<PERSON>duan\\数字运营平台-统一门户工程\\dopUnify\\src\\views\\system\\outManage\\registration\\index.vue?vue&type=script&lang=js&", "dependencies": [{"path": "E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-<PERSON><PERSON><PERSON>n\\数字运营平台-统一门户工程\\dopUnify\\src\\views\\system\\outManage\\registration\\index.vue", "mtime": 1686019809310}, {"path": "E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-<PERSON><PERSON><PERSON>n\\数字运营平台-统一门户工程\\dopUnify\\babel.config.js", "mtime": 1686019818359}, {"path": "E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-<PERSON><PERSON><PERSON>n\\数字运营平台-统一门户工程\\dopUnify\\.babelrc", "mtime": 1705285078668}, {"path": "E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-<PERSON><PERSON><PERSON>n\\数字运营平台-统一门户工程\\dopUnify\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-<PERSON><PERSON><PERSON>n\\数字运营平台-统一门户工程\\dopUnify\\node_modules\\thread-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-<PERSON><PERSON><PERSON>n\\数字运营平台-统一门户工程\\dopUnify\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-<PERSON><PERSON><PERSON>n\\数字运营平台-统一门户工程\\dopUnify\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "E:\\1_Project\\XYD_Project\\dop-4.0\\dop-4.1-<PERSON><PERSON><PERSON>n\\数字运营平台-统一门户工程\\dopUnify\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1655715099000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, null]}